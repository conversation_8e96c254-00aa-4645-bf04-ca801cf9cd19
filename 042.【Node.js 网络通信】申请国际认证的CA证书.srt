1
00:00:00,000 --> 00:00:02,120
那我们在本地模拟的CA机构

2
00:00:02,120 --> 00:00:03,960
自己给自己颁发的证书

3
00:00:03,960 --> 00:00:04,800
浏览器不认

4
00:00:04,800 --> 00:00:05,700
那怎么办呢

5
00:00:05,700 --> 00:00:06,520
我们只能去干嘛

6
00:00:06,520 --> 00:00:09,300
找一个国际的CA机构进行认证

7
00:00:09,300 --> 00:00:11,900
并且让他给我们的服务器颁发一个证书

8
00:00:11,900 --> 00:00:12,660
这么多国际机构

9
00:00:12,660 --> 00:00:13,480
我怎么联系他呢

10
00:00:13,480 --> 00:00:14,500
其实不用你去联系

11
00:00:14,500 --> 00:00:16,300
有很多国内的代理商

12
00:00:16,300 --> 00:00:19,060
这些代理商一般都是一些云服务器的提供商

13
00:00:19,060 --> 00:00:20,980
比如大家比较熟悉的像阿里云

14
00:00:20,980 --> 00:00:21,800
像腾讯云

15
00:00:21,800 --> 00:00:24,700
他们都有和这些公司进行合作代理

16
00:00:24,700 --> 00:00:26,140
来给我们这些普通用户

17
00:00:26,140 --> 00:00:27,940
来去提供一些认证的服务

18
00:00:27,940 --> 00:00:29,540
给我们的服务器颁发证书

19
00:00:29,540 --> 00:00:30,180
好那么这里呢

20
00:00:30,180 --> 00:00:31,380
我打开了两个啊

21
00:00:31,380 --> 00:00:33,020
一个呢是这个腾讯的

22
00:00:33,020 --> 00:00:34,600
腾讯里面你直接找到这个产品

23
00:00:34,600 --> 00:00:35,860
这里有一个企业应用

24
00:00:35,860 --> 00:00:37,440
然后这里有个SSL证书点开它

25
00:00:37,440 --> 00:00:38,640
这就是我们可以在这里啊

26
00:00:38,640 --> 00:00:39,160
你看这里是吧

27
00:00:39,160 --> 00:00:39,760
具有什么什么啊

28
00:00:39,760 --> 00:00:41,440
为您提供一站式的ATPS解决方案

29
00:00:41,440 --> 00:00:43,220
那这就是SSL证书的提供方

30
00:00:43,220 --> 00:00:44,040
那除了它呢

31
00:00:44,040 --> 00:00:45,340
阿里呢也是一样的啊

32
00:00:45,340 --> 00:00:47,180
你呢直接可以在这个产品服务里呢

33
00:00:47,180 --> 00:00:48,640
去搜索一下SSL

34
00:00:48,640 --> 00:00:50,180
然后呢就会出了一个SSL证书

35
00:00:50,180 --> 00:00:52,700
你点击就跳到了SSL证书的一个申请的页面

36
00:00:52,700 --> 00:00:54,100
你可以去购买就可以了

37
00:00:54,100 --> 00:00:55,640
注意这些代理商

38
00:00:55,640 --> 00:00:57,160
这些国际的认证机构

39
00:00:57,160 --> 00:00:58,320
不是白给你认证的

40
00:00:58,320 --> 00:00:59,300
一定要注意

41
00:00:59,300 --> 00:01:00,220
不是白给你认证的

42
00:01:00,220 --> 00:01:01,440
我们随便点一个人家购买证书

43
00:01:01,440 --> 00:01:02,480
好在购买证书里

44
00:01:02,480 --> 00:01:03,500
我们随便点一个

45
00:01:03,500 --> 00:01:04,440
就选择默认的这个

46
00:01:04,440 --> 00:01:06,540
然后这是开通一年就买一个

47
00:01:06,540 --> 00:01:08,340
一共是5676块钱

48
00:01:08,340 --> 00:01:10,080
我这个人太穷了

49
00:01:10,080 --> 00:01:11,520
穷的就是快接快过了

50
00:01:11,520 --> 00:01:12,940
我哪有钱买SSL证书去

51
00:01:12,940 --> 00:01:14,260
而且说实话

52
00:01:14,260 --> 00:01:15,080
我一个个人的网站

53
00:01:15,080 --> 00:01:16,880
我用不着这个非常专业的

54
00:01:20,180 --> 00:01:21,620
那么这个时候啊

55
00:01:21,620 --> 00:01:22,120
怎么办呢

56
00:01:22,120 --> 00:01:23,360
我们找找一下单域名的

57
00:01:23,360 --> 00:01:24,260
能不能便宜点

58
00:01:24,260 --> 00:01:25,340
那我其实也没有钱

59
00:01:25,340 --> 00:01:26,240
然后别再看看呢

60
00:01:26,240 --> 00:01:26,860
要贵一点的

61
00:01:26,860 --> 00:01:28,060
你看这都要钱是吧

62
00:01:28,060 --> 00:01:28,740
别着急

63
00:01:28,740 --> 00:01:30,560
阿里上目前都是要钱的

64
00:01:30,560 --> 00:01:31,020
以前呢

65
00:01:31,020 --> 00:01:32,140
他会给提供一个免费的

66
00:01:32,140 --> 00:01:32,680
现在没有了

67
00:01:32,680 --> 00:01:33,640
那么怎么办呢

68
00:01:33,640 --> 00:01:34,020
别着急

69
00:01:34,020 --> 00:01:35,140
我费尽千辛万苦

70
00:01:35,140 --> 00:01:37,040
在通讯的SL证书里呢

71
00:01:37,040 --> 00:01:38,460
找到了有免费的证书

72
00:01:38,460 --> 00:01:38,780
所以说呢

73
00:01:38,780 --> 00:01:39,800
我们就抛下里不用了

74
00:01:39,800 --> 00:01:40,660
这里有个什么

75
00:01:40,660 --> 00:01:41,780
叫域名型免费版

76
00:01:41,780 --> 00:01:42,900
那么一点击

77
00:01:42,900 --> 00:01:44,240
这个呢是不花钱的

78
00:01:44,240 --> 00:01:45,440
然后呢直接去申请就可以了

79
00:01:45,440 --> 00:01:46,340
但是这个地方呢

80
00:01:46,340 --> 00:01:47,740
还是需要强调一下啊

81
00:01:47,740 --> 00:01:49,020
证书的申请

82
00:01:49,020 --> 00:01:50,380
不管是免费的还是收费的

83
00:01:50,380 --> 00:01:53,280
你必须要有真实的域名和真实的服务器

84
00:01:53,280 --> 00:01:55,200
因为人家机构给你的福气

85
00:01:55,200 --> 00:01:55,840
颁发这个证书

86
00:01:55,840 --> 00:01:57,400
不能凭空的就给你颁一个完事了

87
00:01:57,400 --> 00:01:57,700
对吧

88
00:01:57,700 --> 00:01:58,440
你得让人家去访问

89
00:01:58,440 --> 00:01:59,980
来人家看看是不是符合我们的条件

90
00:01:59,980 --> 00:02:01,260
你必须得有一个真实的域名

91
00:02:01,260 --> 00:02:01,820
真实的服务器

92
00:02:01,820 --> 00:02:03,900
让人家这个CE机构来到你的服务器里

93
00:02:03,900 --> 00:02:04,620
能够进行认证

94
00:02:04,620 --> 00:02:05,080
然后呢

95
00:02:05,080 --> 00:02:05,720
这个证书呢

96
00:02:05,720 --> 00:02:07,040
才能够颁发给你

97
00:02:07,040 --> 00:02:07,260
OK

98
00:02:07,260 --> 00:02:08,700
那么我们在腾讯里面啊

99
00:02:08,700 --> 00:02:08,800
来

100
00:02:08,800 --> 00:02:10,860
其实我的域名已经在这里准备好了

101
00:02:10,860 --> 00:02:12,340
有一些这都是我个人的一些域名

102
00:02:12,340 --> 00:02:16,200
好 那么这里我们就用这个叫西敏老师.com这个域名来去做这个认证

103
00:02:16,200 --> 00:02:18,200
那么证书我们现在还没有申请对不对

104
00:02:18,200 --> 00:02:20,380
好 我们用这个域名来去申请这个证书

105
00:02:20,380 --> 00:02:21,620
那么选择一个免费的

106
00:02:21,620 --> 00:02:23,500
然后这里就是免费快速申请一点击

107
00:02:23,500 --> 00:02:26,640
好 点击过来之后来看里说通用域名

108
00:02:26,640 --> 00:02:28,620
好 那么此时这里注意我们告诉了

109
00:02:28,620 --> 00:02:32,860
我要借助于我现在已经有的西敏老师.com这个域名来去做SSL证书的申请

110
00:02:32,860 --> 00:02:35,680
那么因此这个域名这里我就需要先去写一个二句域名

111
00:02:35,680 --> 00:02:36,960
注意我们只能申请二居民

112
00:02:36,960 --> 00:02:38,920
一居民是不提供免费的

113
00:02:38,920 --> 00:02:40,240
C机构的证书的

114
00:02:40,240 --> 00:02:41,480
所以我们给他来一个二居民

115
00:02:41,480 --> 00:02:42,120
那这个二居民呢

116
00:02:42,120 --> 00:02:45,520
比方说我们就XL.70老师.com

117
00:02:45,520 --> 00:02:46,720
然后申请的邮箱呢

118
00:02:46,720 --> 00:02:47,320
我也来写一个

119
00:02:47,320 --> 00:02:48,120
西林老师

120
00:02:48,120 --> 00:02:51,060
at163.com

121
00:02:51,060 --> 00:02:51,700
那这什么意思呢

122
00:02:51,700 --> 00:02:52,460
就是申请通过之后呢

123
00:02:52,460 --> 00:02:53,440
他会给你发一个邮件

124
00:02:53,440 --> 00:02:53,960
那么一会呢

125
00:02:53,960 --> 00:02:55,220
我就这个地方应该会有个弹窗

126
00:02:55,220 --> 00:02:55,760
如果申请成功的话

127
00:02:55,760 --> 00:02:56,040
好

128
00:02:56,040 --> 00:02:57,600
那么证书的这个备注名

129
00:02:57,600 --> 00:02:58,660
那我们随便写一个

130
00:02:58,660 --> 00:03:00,000
我们写2020测试

131
00:03:00,000 --> 00:03:01,940
测试啊

132
00:03:01,940 --> 00:03:03,020
证书申请

133
00:03:03,020 --> 00:03:04,160
随便写这个无所谓

134
00:03:04,160 --> 00:03:05,080
然后密码的选权

135
00:03:05,080 --> 00:03:05,800
你填也行不填也行

136
00:03:05,800 --> 00:03:06,180
这个无所谓

137
00:03:06,180 --> 00:03:06,800
然后项目呢

138
00:03:06,800 --> 00:03:07,520
你也可以选择一个模式

139
00:03:07,520 --> 00:03:08,080
或者你自己想

140
00:03:08,080 --> 00:03:08,760
如果有那就写

141
00:03:08,760 --> 00:03:09,220
没有呢

142
00:03:09,220 --> 00:03:09,900
你可以不写

143
00:03:09,900 --> 00:03:10,480
无所谓的

144
00:03:10,480 --> 00:03:12,660
而且说前面这两个是必选的

145
00:03:12,660 --> 00:03:13,720
你邮箱也必须得正式的

146
00:03:13,720 --> 00:03:14,380
因为他要给你发通知

147
00:03:14,380 --> 00:03:16,080
那这个域名也必须是你真实的

148
00:03:16,080 --> 00:03:16,900
已经有的申请好的

149
00:03:16,900 --> 00:03:17,380
注册好的

150
00:03:17,380 --> 00:03:17,900
然后呢

151
00:03:17,900 --> 00:03:19,480
经过备案的这个域名才是可以的

152
00:03:19,480 --> 00:03:19,660
好

153
00:03:19,660 --> 00:03:20,720
那我们去点下一步

154
00:03:20,720 --> 00:03:21,440
OK

155
00:03:21,440 --> 00:03:22,140
点完下一步之后呢

156
00:03:22,140 --> 00:03:22,440
注意啊

157
00:03:22,440 --> 00:03:23,480
他给了我们两个选项

158
00:03:23,480 --> 00:03:24,440
就是你的玉名身份验证

159
00:03:24,440 --> 00:03:24,840
什么意思呢

160
00:03:24,840 --> 00:03:25,540
你提供了一个玉名

161
00:03:25,540 --> 00:03:25,740
好了

162
00:03:25,740 --> 00:03:26,840
人家C证书要通过这个玉名

163
00:03:26,840 --> 00:03:27,400
访问你的福气

164
00:03:27,400 --> 00:03:28,260
要进行进一步的验证了

165
00:03:28,260 --> 00:03:28,540
那注意

166
00:03:28,540 --> 00:03:30,820
你必须得对这个玉名进行解析

167
00:03:30,820 --> 00:03:31,240
解析什么呢

168
00:03:31,240 --> 00:03:32,260
解析到一个福气那里

169
00:03:32,260 --> 00:03:33,660
人家得通过你这个真实的玉名

170
00:03:33,660 --> 00:03:35,260
访问到你真实的福气

171
00:03:35,260 --> 00:03:36,200
然后经过说

172
00:03:36,200 --> 00:03:36,820
这个福气可以

173
00:03:36,820 --> 00:03:37,120
没有问题

174
00:03:37,120 --> 00:03:37,280
好了

175
00:03:37,280 --> 00:03:38,140
我给你办法证书

176
00:03:38,140 --> 00:03:39,020
那么验证的时候

177
00:03:39,020 --> 00:03:39,960
有两种方式

178
00:03:39,960 --> 00:03:41,300
一个是手动DNS验证

179
00:03:41,300 --> 00:03:42,000
一个是文件验证

180
00:03:42,000 --> 00:03:44,400
那我们就选择手动的DNS验证就可以了

181
00:03:44,400 --> 00:03:46,840
如果你还不是太懂说怎么去配置域名

182
00:03:46,840 --> 00:03:48,160
怎么去添加记录怎么的

183
00:03:48,160 --> 00:03:49,540
那么这里有一个详细说明

184
00:03:49,540 --> 00:03:50,460
你可以点击去看一看

185
00:03:50,460 --> 00:03:51,560
那这里我就不再去看了

186
00:03:51,560 --> 00:03:53,420
我直接点击确认

187
00:03:53,420 --> 00:03:54,260
好点完全之后

188
00:03:54,260 --> 00:03:55,200
他就会给你一个提示

189
00:03:55,200 --> 00:03:57,800
说你这个DNS

190
00:03:57,800 --> 00:04:00,820
要加这么一条记录在你的服务器那里

191
00:04:00,820 --> 00:04:01,800
主题的记录是这个

192
00:04:01,800 --> 00:04:02,720
然后域名是这个

193
00:04:02,720 --> 00:04:04,120
那么记录的值是这么一长串

194
00:04:04,120 --> 00:04:04,720
好那么注意

195
00:04:04,720 --> 00:04:05,900
证书的类型是谁谁

196
00:04:05,900 --> 00:04:06,980
然后注意这里一样的

197
00:04:06,980 --> 00:04:07,900
有效期为一年

198
00:04:07,900 --> 00:04:09,760
域名必须是XL点

199
00:04:09,760 --> 00:04:10,440
新老师点

200
00:04:10,440 --> 00:04:10,980
看不过这个语名

201
00:04:10,980 --> 00:04:12,620
有时候这个证书只办法给这个语名

202
00:04:12,620 --> 00:04:13,940
其他的语名都是不合适的

203
00:04:13,940 --> 00:04:14,680
也都是不行的

204
00:04:14,680 --> 00:04:15,080
OK

205
00:04:15,080 --> 00:04:16,580
那么根据他的这个提示和信息

206
00:04:16,580 --> 00:04:17,820
那么我们必须要在我们的

207
00:04:17,820 --> 00:04:18,780
现在这个语名这里呢

208
00:04:18,780 --> 00:04:20,280
添加一条什么解析记录

209
00:04:20,280 --> 00:04:20,540
好

210
00:04:20,540 --> 00:04:21,040
找到这个天这里

211
00:04:21,040 --> 00:04:22,140
还是A解析记录值

212
00:04:22,140 --> 00:04:23,040
注意主机记录值

213
00:04:23,040 --> 00:04:23,920
那人这里已经告诉你了

214
00:04:23,920 --> 00:04:24,720
主机记录是这个

215
00:04:24,720 --> 00:04:25,240
那我们呢

216
00:04:25,240 --> 00:04:26,220
直接把它复制过来

217
00:04:26,220 --> 00:04:27,260
到我们的这个地方

218
00:04:27,260 --> 00:04:28,100
OK

219
00:04:28,100 --> 00:04:28,800
那么记录值呢

220
00:04:28,800 --> 00:04:29,460
他也给你提供好了

221
00:04:29,460 --> 00:04:30,900
拿过来

222
00:04:30,900 --> 00:04:32,140
直接复制到我们的语名里

223
00:04:32,140 --> 00:04:32,440
记录值

224
00:04:32,440 --> 00:04:33,840
记录值

225
00:04:33,840 --> 00:04:34,080
好

226
00:04:34,080 --> 00:04:36,260
当然了那个记录类型里的人给你提供了是什么是TIT的

227
00:04:36,260 --> 00:04:37,180
那么你这里呢就选择TIT

228
00:04:37,180 --> 00:04:39,100
OK那么它呢就会十分钟之内就给你检测完

229
00:04:39,100 --> 00:04:41,060
那我们呢一点击添加OK操作成功

230
00:04:41,060 --> 00:04:42,040
好那么操作成功之后呢

231
00:04:42,040 --> 00:04:43,180
一会儿我应该会收到一个邮件说

232
00:04:43,180 --> 00:04:44,040
我的新老师点

233
00:04:44,040 --> 00:04:46,240
COME这个域名呢添加了一个这样的记录值

234
00:04:46,240 --> 00:04:47,340
你只要添加好之后

235
00:04:47,340 --> 00:04:50,140
注意啊只要你的域名里添加好之后就算完事了

236
00:04:50,140 --> 00:04:50,640
就算完事了

237
00:04:50,640 --> 00:04:51,420
那么一旦添加好之后呢

238
00:04:51,420 --> 00:04:52,940
我们可以试询一下自己查询一下

239
00:04:52,940 --> 00:04:53,980
我们先点一下查询一下

240
00:04:53,980 --> 00:04:55,080
来看这预名状态正好

241
00:04:55,080 --> 00:04:56,320
看这一成功解析

242
00:04:56,320 --> 00:04:57,960
耐心等待CA机构扫描

243
00:04:57,960 --> 00:04:59,160
那这个扫描注意是什么呢

244
00:04:59,160 --> 00:05:00,960
是你这个TIT的预名解析

245
00:05:00,960 --> 00:05:04,960
已经由你这个预名解析到你正确的那个服务器预名那里了

246
00:05:04,960 --> 00:05:05,960
但是呢CA机构呢

247
00:05:05,960 --> 00:05:07,720
还没有通过你的这个解析呢

248
00:05:07,720 --> 00:05:09,100
来去对你的服务器进行人证

249
00:05:09,100 --> 00:05:09,800
好那么此时呢

250
00:05:09,800 --> 00:05:10,480
不用急啊

251
00:05:10,480 --> 00:05:10,720
不用急

252
00:05:10,720 --> 00:05:12,760
我们呢也就等个大概十分钟之内啊

253
00:05:12,760 --> 00:05:13,840
一定就麻烦了

254
00:05:13,840 --> 00:05:14,960
好我们可以刷新一下

255
00:05:14,960 --> 00:05:15,460
哎这里啊

256
00:05:15,460 --> 00:05:15,660
看里

257
00:05:15,660 --> 00:05:17,340
好我刚才在刷新的过分钟呢

258
00:05:17,340 --> 00:05:18,020
哎已经啊

259
00:05:18,020 --> 00:05:19,040
给我发了一个语字了

260
00:05:19,040 --> 00:05:19,580
是腾训报的

261
00:05:19,580 --> 00:05:19,940
说什么呢

262
00:05:19,940 --> 00:05:21,160
说这个证书审核已经通过了

263
00:05:21,160 --> 00:05:22,240
说最近的腾训用户你好

264
00:05:22,240 --> 00:05:24,440
域名XL点新老师点看不到

265
00:05:24,440 --> 00:05:25,900
这个域名的证书已经审核通过了

266
00:05:25,900 --> 00:05:26,820
那么审核通过之后

267
00:05:26,820 --> 00:05:27,360
来我们这里呢

268
00:05:27,360 --> 00:05:27,900
看这页注意注意

269
00:05:27,900 --> 00:05:29,340
说已经颁发了这个证书了

270
00:05:29,340 --> 00:05:30,260
那么此时我们就可以刚刚

271
00:05:30,260 --> 00:05:31,500
哎把这个证书下子下来

272
00:05:31,500 --> 00:05:33,140
好那么找到这里啊

273
00:05:33,140 --> 00:05:33,800
我们点击保存

274
00:05:33,800 --> 00:05:35,500
好我们稍作等待

275
00:05:35,500 --> 00:05:36,940
OK那么下载完之后呢

276
00:05:36,940 --> 00:05:37,920
打开我们这个文件啊

277
00:05:37,920 --> 00:05:38,340
就在这里啊

278
00:05:38,340 --> 00:05:38,900
好那么此时呢

279
00:05:38,900 --> 00:05:39,380
我把它呀

280
00:05:39,380 --> 00:05:40,860
先拖到我们的桌面上

281
00:05:40,860 --> 00:05:41,900
OK然后呢

282
00:05:41,900 --> 00:06:11,900
我们打开来看一看,是不是和我们自己模拟的那个C差不多呢,哎,发现不太一样,对吧,哪不太一样呢,你会发现,它呢,实际啊,给我们提供了阿华西服务器的,RS服务器的,NGX服务器的,TOMCAT服务器的,哎,各种不同的,但是都是常用的一些服务器,啊,为他们准备了一些证书,就没有一个为我们nodeJS准备的证书吗,哎,注意啊,不是这样的,其实呢,你可以直接使用什么,使用这个NGX啊,给提供的这个证书,看到没有,你发现NGX里面呢,它也是啊,就是一个叫DRCRT的这样的一个文件,和一个DRK的这样的

283
00:06:11,900 --> 00:06:12,840
真实的证书

284
00:06:12,840 --> 00:06:13,620
我们就拿到了

285
00:06:13,620 --> 00:06:14,560
找到我们的服务器

286
00:06:14,560 --> 00:06:15,740
我们刚刚自己是不是生成了一个

287
00:06:15,740 --> 00:06:16,640
CRT.CRT

288
00:06:16,640 --> 00:06:17,020
还有一个什么

289
00:06:17,020 --> 00:06:18,020
RSA

290
00:06:18,020 --> 00:06:21,180
这两个是我们自己模拟C生成的

291
00:06:21,180 --> 00:06:21,720
那么现在呢

292
00:06:21,720 --> 00:06:22,580
我们通过啊

293
00:06:22,580 --> 00:06:22,860
这个

294
00:06:22,860 --> 00:06:24,720
通讯云服务器

295
00:06:24,720 --> 00:06:25,320
这个代理商

296
00:06:25,320 --> 00:06:26,840
申请了一个真实的

297
00:06:26,840 --> 00:06:27,540
能够啊

298
00:06:27,540 --> 00:06:28,980
国际认证的一个证书

299
00:06:28,980 --> 00:06:29,580
并且呢

300
00:06:29,580 --> 00:06:30,280
把它也下下来了

301
00:06:30,280 --> 00:06:31,100
好下下来之后呢

302
00:06:31,100 --> 00:06:31,980
我们把这两个证书

303
00:06:31,980 --> 00:06:33,820
传到我们的服务器上

304
00:06:33,820 --> 00:06:34,440
传上去之后呢

305
00:06:34,440 --> 00:06:35,780
你就像刚才那样

306
00:06:35,780 --> 00:06:35,920
啊

307
00:06:35,920 --> 00:06:36,640
就是该怎么使用

308
00:06:36,640 --> 00:06:37,440
就怎么使用

309
00:06:37,440 --> 00:06:37,840
就OK了

310
00:06:37,840 --> 00:06:38,360
好

311
00:06:38,360 --> 00:06:44,460
那么这就是我们如何通过这个代理商去申请国际认证的CE证书一样的

312
00:06:44,460 --> 00:06:45,280
注意这里强调一下

313
00:06:45,280 --> 00:06:48,980
这个阿里云他给你提供的这个CE证书的申请

314
00:06:48,980 --> 00:06:50,920
目前来说都是收费的

315
00:06:50,920 --> 00:06:52,440
而在腾讯里面有一个是免费的

316
00:06:52,440 --> 00:06:54,320
但是他只能免费一年

317
00:06:54,320 --> 00:06:56,780
同时只支持二级域名的证书申请

318
00:06:56,780 --> 00:06:57,860
别的事不行了

319
00:06:57,860 --> 00:06:58,500
这一点需要注意

320
00:06:58,500 --> 00:06:59,900
那么申请的过程也是非常简单

321
00:06:59,900 --> 00:07:02,940
有一个前提就是你必须有一个自己真实申请过的

322
00:07:02,940 --> 00:07:04,880
已经被押过的一个真实的域名就可以了

323
00:07:04,880 --> 00:07:07,920
OK那这就是我们国际CE机构的认证证书的申请

324
00:07:07,920 --> 00:07:08,960
感谢观看

