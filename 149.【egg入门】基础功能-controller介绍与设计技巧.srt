1
00:00:00,000 --> 00:00:01,860
好 这节课我们来看下控制器

2
00:00:01,860 --> 00:00:03,980
那么控制器我们之前是不是已经写过很多

3
00:00:03,980 --> 00:00:04,940
那么简单来讲

4
00:00:04,940 --> 00:00:07,020
控制器它就是负责了解析用户的输入

5
00:00:07,020 --> 00:00:08,400
然后来返回相应的结果

6
00:00:08,400 --> 00:00:09,880
那么它的作用者有三个

7
00:00:09,880 --> 00:00:11,000
前两个我们是非常

8
00:00:11,000 --> 00:00:12,860
是不是经常使用

9
00:00:12,860 --> 00:00:16,400
你比如说我们通过RESF接口去返回一些节省数据

10
00:00:16,400 --> 00:00:18,100
那么包括我们是不是还会有一些

11
00:00:18,100 --> 00:00:20,000
通过loads服务器渲染页面的需求

12
00:00:20,000 --> 00:00:21,600
也就是咱们通过context.render

13
00:00:21,600 --> 00:00:22,420
是不是直接返回模板

14
00:00:22,420 --> 00:00:23,860
一个返回模板一个返回节省

15
00:00:23,860 --> 00:00:25,080
这是咱们最常见的两个需求

16
00:00:25,080 --> 00:00:26,340
那么第三个是什么呢

17
00:00:26,340 --> 00:00:27,600
在代理服务器中

18
00:00:27,600 --> 00:00:29,860
Ctrl 将用户的请求转发到其他服务器上

19
00:00:29,860 --> 00:00:31,580
并将其他服务器的处理结果

20
00:00:31,580 --> 00:00:32,100
访问一个用户

21
00:00:32,100 --> 00:00:32,840
什么意思

22
00:00:32,840 --> 00:00:34,360
其实这里呢

23
00:00:34,360 --> 00:00:35,340
我给大家举个例子

24
00:00:35,340 --> 00:00:37,000
你比如说我们之前是不是做过一些

25
00:00:37,000 --> 00:00:38,060
访淘宝 访天猫

26
00:00:38,060 --> 00:00:39,780
或者说访某某某的一些页面

27
00:00:39,780 --> 00:00:41,660
包括咱们一些Github上面一些开源的项目

28
00:00:41,660 --> 00:00:43,080
比如说咱们去做一个访Edmore的APP

29
00:00:43,080 --> 00:00:44,840
那么我们去访在一些页面的时候

30
00:00:44,840 --> 00:00:45,480
接口从哪里来

31
00:00:45,480 --> 00:00:47,680
我们是不是直接调用他们第三方所提供的接口

32
00:00:47,680 --> 00:00:49,420
那么我这里呢举个例子

33
00:00:49,420 --> 00:00:50,680
假如说我们要去做一个

34
00:00:50,680 --> 00:00:52,340
天气预报的一个系统

35
00:00:52,340 --> 00:00:54,000
那么天气一报这样一些数据是怎么来的

36
00:00:54,000 --> 00:00:55,220
我们肯定不可能自己写接口吧

37
00:00:55,220 --> 00:00:55,580
对吧

38
00:00:55,580 --> 00:00:57,760
因为你能预测天气是多少吗不可能的

39
00:00:57,760 --> 00:01:00,360
所以我们需要去调用第三方的一些接口

40
00:01:00,360 --> 00:01:01,700
你比如说像聚合数据

41
00:01:01,700 --> 00:01:03,000
他就提供一些免费的接口

42
00:01:03,000 --> 00:01:03,880
我们可以通过

43
00:01:03,880 --> 00:01:05,840
比如说我们去聚合去调一个verter

44
00:01:05,840 --> 00:01:07,300
在那个接口我们是不是就可以获取到

45
00:01:07,300 --> 00:01:10,560
我们是不是就可以获取到我们的一个天气的信息

46
00:01:10,560 --> 00:01:11,940
然后再通过咱们nodejs

47
00:01:11,940 --> 00:01:13,460
我们去把它给数据处理一下

48
00:01:13,460 --> 00:01:14,920
再转发给我们的前端

49
00:01:14,920 --> 00:01:15,960
是不是就能够去使用

50
00:01:15,960 --> 00:01:18,260
是不是减少了我们的一个开发成本

51
00:01:18,260 --> 00:01:19,500
那么当然了

52
00:01:19,500 --> 00:01:21,600
这样一些API调用肯定是需要收费的

53
00:01:21,600 --> 00:01:22,700
那么500次不要钱

54
00:01:22,700 --> 00:01:24,840
1000次可能就开始需要收费了

55
00:01:24,840 --> 00:01:26,700
那么同样的你不仅可以去做天区预报

56
00:01:26,700 --> 00:01:28,280
你还可以去做一些其他的API

57
00:01:28,280 --> 00:01:30,640
你比如说我们除了天区预报

58
00:01:30,640 --> 00:01:31,920
然后还有一些

59
00:01:31,920 --> 00:01:33,460
比如说你想获取一些日期

60
00:01:33,460 --> 00:01:35,400
或者说一些计算的接口

61
00:01:35,400 --> 00:01:37,060
我们都是可以去查的

62
00:01:37,060 --> 00:01:39,100
那么这里就是它的一个作用

63
00:01:39,100 --> 00:01:40,840
我们来看一下推荐用法

64
00:01:40,840 --> 00:01:42,620
那么什么是推荐用法

65
00:01:42,620 --> 00:01:44,180
其实就是咱们哪些逻辑

66
00:01:44,180 --> 00:01:45,380
你适合在Ctrl的东西

67
00:01:45,380 --> 00:01:47,280
我们不可能说把所有的要逻辑都丢到里面

68
00:01:47,280 --> 00:01:48,260
你比如说首先

69
00:01:48,260 --> 00:01:51,200
我们通过router去获取一个用户请求的时候

70
00:01:51,200 --> 00:01:52,100
我们是不是可以获取到

71
00:01:52,100 --> 00:01:53,340
咱们传递过来的一些参数

72
00:01:53,340 --> 00:01:54,260
你比如说query

73
00:01:54,260 --> 00:01:55,040
比如说params

74
00:01:55,040 --> 00:01:57,060
query是不是从咱们一个地址来获取的

75
00:01:57,060 --> 00:01:59,120
那么params就是从我们的router里面去获取的

76
00:01:59,120 --> 00:02:01,500
我们获取到这些参数之后

77
00:02:01,500 --> 00:02:03,400
在控制器里面获取到参数

78
00:02:03,400 --> 00:02:06,060
是不是可以去对它进行一些组装或者教验

79
00:02:06,060 --> 00:02:07,360
那么组装和教验的一个过程

80
00:02:07,360 --> 00:02:08,960
是适合在咱们controller里面去做的

81
00:02:08,960 --> 00:02:10,840
那么我们获取到一些传递的参数之后

82
00:02:10,840 --> 00:02:13,140
是不是接下来要去咱们的数据库里面去读取数据了

83
00:02:13,140 --> 00:02:15,140
那么这里呢就不是很适合在controller里面

84
00:02:15,140 --> 00:02:16,840
咱们可以去单独去建一个service层

85
00:02:16,840 --> 00:02:18,860
那么什么是service我们后面的课程会去介绍

86
00:02:18,860 --> 00:02:21,100
所以呢我们把整个数据查询的一个过程

87
00:02:21,100 --> 00:02:23,860
包括数据的一个组装都放到了我们的一个service里面

88
00:02:23,860 --> 00:02:26,600
这样呢我们的代码结构就很好了去分层

89
00:02:26,600 --> 00:02:28,700
可以呢让我们项目更加清晰更好维护

90
00:02:28,700 --> 00:02:31,400
好那么我们的service获取到数据之后

91
00:02:31,400 --> 00:02:34,200
我们是不是在通过http将结果返回给我们的用户

92
00:02:34,200 --> 00:02:36,420
比如说我们之前的控制器里面是不是可以去

93
00:02:36,420 --> 00:02:38,460
比如说去render或者 render一个模板

94
00:02:38,460 --> 00:02:41,380
或者说呢我们去通过contact body去返回一个json

95
00:02:41,380 --> 00:02:42,380
这样呢都是可以的

96
00:02:42,380 --> 00:02:43,540
那么如何编写Ctrl呢

97
00:02:43,540 --> 00:02:44,580
我们之前是不是写过很多

98
00:02:44,580 --> 00:02:46,480
这里呢就我们不再去介绍了

99
00:02:46,480 --> 00:02:47,440
包括Rooter的调用

100
00:02:47,440 --> 00:02:50,280
通过咱们Rooter的Gate或者Post

101
00:02:50,280 --> 00:02:50,900
到我们的路由

102
00:02:50,900 --> 00:02:51,940
然后呢再去选定

103
00:02:51,940 --> 00:02:53,280
咱们相应的一个控制器

104
00:02:53,280 --> 00:02:54,400
这里呢之前也写过

105
00:02:54,400 --> 00:02:55,980
那么Ctrl呢它还支持多级目录

106
00:02:55,980 --> 00:02:56,740
你比如说

107
00:02:56,740 --> 00:02:58,180
我们来看一下

108
00:02:58,180 --> 00:03:00,800
Ctrl呢它是如何支持多级目录呢

109
00:03:00,800 --> 00:03:01,700
其实我们可以看到

110
00:03:01,700 --> 00:03:03,780
咱们的Home和Post

111
00:03:03,780 --> 00:03:04,940
是不是都是在咱们的Ctrl

112
00:03:04,940 --> 00:03:05,660
这样一个目录下面

113
00:03:05,660 --> 00:03:08,300
那么如果说我们建一个多级目录

114
00:03:08,300 --> 00:03:09,700
它是怎么样去支持的呢

115
00:03:09,700 --> 00:03:11,540
比如说我们在Sup里面

116
00:03:11,540 --> 00:03:12,040
Sup是不是

117
00:03:12,040 --> 00:03:12,940
自己的意思

118
00:03:12,940 --> 00:03:14,260
我们再来建一个

119
00:03:14,260 --> 00:03:16,180
比如说我们建一个

120
00:03:16,180 --> 00:03:17,780
user.js

121
00:03:17,780 --> 00:03:18,200
好

122
00:03:18,200 --> 00:03:18,840
那么我们来

123
00:03:18,840 --> 00:03:19,760
再写一个

124
00:03:19,760 --> 00:03:21,220
关于user的一个控制器

125
00:03:21,220 --> 00:03:23,000
user.com

126
00:03:23,000 --> 00:03:25,680
修了

127
00:03:25,680 --> 00:03:28,860
好

128
00:03:28,860 --> 00:03:30,220
我们把它导出一下

129
00:03:30,220 --> 00:03:35,840
咱们呢定义了一个

130
00:03:35,840 --> 00:03:38,680
是不是index的方法

131
00:03:38,680 --> 00:03:40,200
然后咱们在context的body里面

132
00:03:40,200 --> 00:03:41,700
去把它给返回

133
00:03:41,700 --> 00:03:47,580
我是sub,因为我们主要是去示意我们的一个controller的一个嵌套

134
00:03:47,580 --> 00:03:50,400
好,在root里面我们是要新增一个路由啊

135
00:03:50,400 --> 00:03:54,040
比如说我们去新增一个user

136
00:03:54,040 --> 00:03:55,400
那么我们的controller怎么写

137
00:03:55,400 --> 00:03:57,540
首先controller下面是不是有一个sub文件夹

138
00:03:57,540 --> 00:03:59,060
所以呢这里是sub

139
00:03:59,060 --> 00:04:00,940
然后sub下面是不是有个user连接也是

140
00:04:00,940 --> 00:04:01,960
这里就是user

141
00:04:01,960 --> 00:04:04,200
那么user后面呢还有一个index方法

142
00:04:04,200 --> 00:04:06,960
所以我们这里呢把它给加上index

143
00:04:06,960 --> 00:04:09,720
那么我们来看一下咱们的路由和控制器有没有生效

144
00:04:11,700 --> 00:04:20,920
好 大家可以看到是不是我们的路由和控制器就已经成功了呀

145
00:04:20,920 --> 00:04:23,820
那么这里呢咱们的多几步路呢使用呢也是非常的简单

146
00:04:23,820 --> 00:04:26,500
接下来我们来看一下控制器里面它有哪些属性

147
00:04:26,500 --> 00:04:29,600
那么其实controller里面咱们的controller是不是个类

148
00:04:29,600 --> 00:04:32,220
那么当我们访问它的this的时候它其实有四个方法

149
00:04:32,220 --> 00:04:35,080
一个是context上下文然后呢appservice

150
00:04:35,080 --> 00:04:37,960
那么context和app我们是不是都已经学习过了

151
00:04:37,960 --> 00:04:40,540
service呢后面会去讲那么config配置我们也学习过

152
00:04:40,540 --> 00:04:41,700
那么还一个对象是Norg

153
00:04:41,700 --> 00:04:44,840
是对咱们一个项目去进行一些日子记录的

154
00:04:44,840 --> 00:04:45,680
后面呢我们也会去讲

155
00:04:45,680 --> 00:04:47,240
那么Ctrl里面到底有哪些属性

156
00:04:47,240 --> 00:04:48,820
我们是不是把它打印出来看一下才安心呢

157
00:04:48,820 --> 00:04:49,240
对吧

158
00:04:49,240 --> 00:04:51,060
好

159
00:04:51,060 --> 00:04:53,480
那么我们就把咱们一个控制器给打印出来看一下

160
00:04:53,480 --> 00:04:54,980
看它里面的this里面有哪些属性

161
00:04:54,980 --> 00:05:00,800
好

162
00:05:00,800 --> 00:05:03,660
假如说呢我们就在咱们的一个index里面

163
00:05:03,660 --> 00:05:05,440
咱们来把this给打印出来

164
00:05:05,440 --> 00:05:07,880
this

165
00:05:07,880 --> 00:05:09,440
好

166
00:05:09,440 --> 00:05:10,280
打一个标记

167
00:05:10,280 --> 00:05:13,280
index 是不是对应的咱们的 这个目录啊

168
00:05:13,280 --> 00:05:17,280
好 我们来看一下控制台

169
00:05:17,280 --> 00:05:20,280
大家可以看到 this 是不是非常大一个对象

170
00:05:20,280 --> 00:05:22,280
里面有logger

171
00:05:22,280 --> 00:05:24,280
然后有

172
00:05:24,280 --> 00:05:26,280
其实很难看 是吧 同学们

173
00:05:26,280 --> 00:05:28,280
那么这里呢 就教大家一个小技巧

174
00:05:28,280 --> 00:05:30,280
因为我们的目的只是看 this 下面它有哪些属性

175
00:05:30,280 --> 00:05:32,280
那么这里呢 教大家一个方法

176
00:05:32,280 --> 00:05:34,280
叫做object.case

177
00:05:34,280 --> 00:05:36,280
其实呢 这里是咱们s6里面的一个语法

178
00:05:36,280 --> 00:05:39,360
其实这也是126里面的一个语法

179
00:05:39,360 --> 00:05:42,940
如果说不知道这个API的同学们可以去查阅一下文档

180
00:05:42,940 --> 00:05:44,980
好那么我们再来看一下

181
00:05:44,980 --> 00:05:48,320
咱们把它可列一下npm run bv

182
00:05:48,320 --> 00:05:52,160
好

183
00:05:52,160 --> 00:05:53,680
大家可以看到

184
00:05:53,680 --> 00:05:55,740
那么咱们在this下面是不是有这样几个

185
00:05:55,740 --> 00:05:59,580
属性呢contest app config和service

186
00:05:59,580 --> 00:06:01,120
那么我们刚才

187
00:06:01,120 --> 00:06:04,180
那么我们刚才文档里面是不是写

188
00:06:04,180 --> 00:06:05,980
下面还有一个log对象进行认识记录

189
00:06:06,280 --> 00:06:07,180
那么为什么这里没有呢

190
00:06:07,180 --> 00:06:09,620
其实因为我们是没有用上日制的这样一个插件

191
00:06:09,620 --> 00:06:10,780
我们后面会去使用

192
00:06:10,780 --> 00:06:12,100
那么如果说我们使用了插件之后

193
00:06:12,100 --> 00:06:14,180
在Ctrl上面是有这样的一个对象的

194
00:06:14,180 --> 00:06:14,360
好

195
00:06:14,360 --> 00:06:16,280
这里呢就是Ctrl属性的一个介绍

196
00:06:16,280 --> 00:06:18,880
那么接下来我们再来看一下

197
00:06:18,880 --> 00:06:20,760
去制定一个Ctrl的机内

198
00:06:20,760 --> 00:06:21,500
什么意思

199
00:06:21,500 --> 00:06:24,080
那么我们项目之前

200
00:06:24,080 --> 00:06:26,600
是不是通过require咱们一机机里面一个Ctrl的对象

201
00:06:26,600 --> 00:06:28,240
然后我们去编写咱们控制器的时候

202
00:06:28,240 --> 00:06:29,940
是不是直接去继承它呀

203
00:06:29,940 --> 00:06:33,060
那么其实呢我们还可以去定义一些自己的Ctrl的机内

204
00:06:33,060 --> 00:06:34,180
然后去继承它这样呢

205
00:06:34,180 --> 00:06:37,280
他可以去对我们一些通用的邏輯器进行一些封装和抽象

206
00:06:37,280 --> 00:06:38,080
那么这样

207
00:06:38,080 --> 00:06:40,280
光说可能大家不正常理解

208
00:06:40,280 --> 00:06:41,800
那么我们来尝试一下

209
00:06:41,800 --> 00:06:42,520
给大家演示一下

210
00:06:42,520 --> 00:06:44,500
我们怎么样通过Ctrl去写一个机内

211
00:06:44,500 --> 00:06:45,460
你比如说

212
00:06:45,460 --> 00:06:48,600
我们在Ctrl下面有一个base在那里目录

213
00:06:48,600 --> 00:06:49,060
这里呢

214
00:06:49,060 --> 00:06:49,980
我已经提前给建好了

215
00:06:49,980 --> 00:06:50,500
那么里面呢

216
00:06:50,500 --> 00:06:51,780
写了有一个http介绍

217
00:06:51,780 --> 00:06:52,240
什么意思呢

218
00:06:52,240 --> 00:06:54,100
我可能对一些http相关的方法

219
00:06:54,100 --> 00:06:55,380
我是不是需要对它进行一些

220
00:06:55,380 --> 00:06:55,760
怎么样

221
00:06:55,760 --> 00:06:56,460
是不是进行一些封装

222
00:06:56,460 --> 00:06:57,260
你比如说

223
00:06:57,260 --> 00:06:59,120
比如说我们这里呢

224
00:06:59,120 --> 00:07:02,040
比如说我们这里有一个控制器

225
00:07:02,040 --> 00:07:03,080
是不是继承于咱们的1GG

226
00:07:03,080 --> 00:07:05,560
也就是咱们最基础的一个控制器

227
00:07:05,560 --> 00:07:06,880
我们继承它之后

228
00:07:06,880 --> 00:07:08,680
是不是产生了一个http control

229
00:07:08,680 --> 00:07:11,380
那么http control上面

230
00:07:11,380 --> 00:07:13,100
是不是有这样一个方法

231
00:07:13,100 --> 00:07:13,860
叫做success

232
00:07:13,860 --> 00:07:18,080
那么我们为什么要去定义这样的一个方法

233
00:07:18,080 --> 00:07:19,600
因为在咱们实际的项目中

234
00:07:19,600 --> 00:07:22,100
你比如说我们返回了节省的数据

235
00:07:22,100 --> 00:07:23,980
一般来讲我们都需要有个message

236
00:07:23,980 --> 00:07:25,200
或者code这样的两个字段

237
00:07:25,200 --> 00:07:27,180
但是我们又不想每次都写

238
00:07:27,180 --> 00:07:29,400
所以我们可以通过这样一种方式

239
00:07:29,400 --> 00:07:31,540
去进行咱们这样的一个积累

240
00:07:31,540 --> 00:07:33,620
那么我们就来看一下

241
00:07:33,620 --> 00:07:34,220
它有哪些好处

242
00:07:34,220 --> 00:07:35,420
你比如说message和code

243
00:07:35,420 --> 00:07:36,400
是不是我们每一个请求

244
00:07:36,400 --> 00:07:37,380
你不管是成功还是失败

245
00:07:37,380 --> 00:07:38,680
我们都必须要有code和message

246
00:07:38,680 --> 00:07:39,260
这样的两个字段

247
00:07:39,260 --> 00:07:40,720
那么我们来看一下

248
00:07:40,720 --> 00:07:41,560
怎么样去使用

249
00:07:41,560 --> 00:07:43,080
你比如说我们在

250
00:07:43,080 --> 00:07:46,160
你比如说我们在home

251
00:07:46,160 --> 00:07:47,540
home这样的一个控制器中

252
00:07:47,540 --> 00:07:48,540
我们是不是可以去继承

253
00:07:48,540 --> 00:07:50,260
我们的http这样的controller

254
00:07:50,260 --> 00:07:52,020
那么我们把它给引入

255
00:07:52,020 --> 00:07:53,300
我们把它给引入

256
00:07:53,300 --> 00:07:54,540
这样一个给注释掉

257
00:07:54,540 --> 00:07:55,360
那么我们这里呢

258
00:07:55,360 --> 00:07:56,720
是不是可以去通过继承

259
00:07:56,720 --> 00:07:58,180
我们的httpcontroller

260
00:07:58,180 --> 00:07:59,860
那么我们此时

261
00:07:59,860 --> 00:08:00,920
我们目前的控制器

262
00:08:00,920 --> 00:08:03,060
它是不是也就有了咱们bass上面的success方法

263
00:08:03,060 --> 00:08:05,340
你比如说我们去访问根部录的时候

264
00:08:05,340 --> 00:08:06,620
咱们呢希望

265
00:08:06,620 --> 00:08:08,200
希望去返回一个什么呢

266
00:08:08,200 --> 00:08:09,000
第四点success

267
00:08:09,000 --> 00:08:10,740
你比如说我们做了一些一系列操作之后

268
00:08:10,740 --> 00:08:12,700
我们希望告诉用户你访问成功了

269
00:08:12,700 --> 00:08:15,080
然后我们还需要去额外的给他一个data

270
00:08:15,080 --> 00:08:15,400
对吧

271
00:08:15,400 --> 00:08:17,080
比如说咱们的userid

272
00:08:17,080 --> 00:08:20,060
191919

273
00:08:20,060 --> 00:08:21,880
好

274
00:08:21,880 --> 00:08:23,920
那么我们来看一下我们的页面到底会返回什么

275
00:08:23,920 --> 00:08:28,880
好

276
00:08:28,880 --> 00:08:30,240
大家可以看到我们报错了

277
00:08:30,240 --> 00:08:31,640
那么我们来看下到什么错

278
00:08:31,640 --> 00:08:36,760
httpcontrollerfrom.basehttp

279
00:08:36,760 --> 00:08:39,460
这里呢大家可以看到我们是不是去读取我们的

280
00:08:39,460 --> 00:08:42,280
读取我们的一个依赖事单

281
00:08:42,280 --> 00:08:44,840
好其实问题出在这里

282
00:08:44,840 --> 00:08:46,620
问题出在哪里呢

283
00:08:46,620 --> 00:08:48,420
我们这里是不是用了ES6的语法

284
00:08:48,420 --> 00:08:50,460
import一个什么from什么

285
00:08:50,460 --> 00:08:52,520
其实在NodeGIS里面是不支持这样的语法的

286
00:08:52,520 --> 00:08:53,800
如果说你想去使用import

287
00:08:53,800 --> 00:08:54,820
你必须使用什么呀

288
00:08:54,820 --> 00:08:56,600
是不是利用babel和webpack

289
00:08:56,600 --> 00:08:59,420
所以我们这里呢需要去通过require

290
00:08:59,680 --> 00:09:00,560
去引入咱们的一个依赖

291
00:09:00,560 --> 00:09:04,640
http

292
00:09:04,640 --> 00:09:05,840
base

293
00:09:05,840 --> 00:09:14,800
http.js

294
00:09:14,800 --> 00:09:17,200
好 这两应该就可以了 我们来重启一下项目

295
00:09:17,200 --> 00:09:22,160
好 我们再来访问下个目

296
00:09:22,160 --> 00:09:26,800
this.success

297
00:09:28,240 --> 00:09:30,240
好 其实我们问题是不是出在这里啊

298
00:09:30,240 --> 00:09:32,520
因为我们在http的controller里面

299
00:09:32,520 --> 00:09:35,440
咱们在机内里面是不是在success方法里面是不是已经有了

300
00:09:35,440 --> 00:09:37,320
this.context.body等于一个对象

301
00:09:37,320 --> 00:09:39,020
那么我们在外面是不是又定义了一次

302
00:09:39,020 --> 00:09:41,800
对吧 所以说呢 我们这里

303
00:09:41,800 --> 00:09:45,740
是不是可以直接去调用awaitthis.success

304
00:09:45,740 --> 00:09:47,800
然后呢 传入我们需要给用户返回的数据

305
00:09:47,800 --> 00:09:49,880
比如说我们 假如说我们去添加了一个用户id

306
00:09:49,880 --> 00:09:53,520
添加之后 添加成功之后我们是不是要返回咱们用户的id

307
00:09:53,520 --> 00:09:55,500
对吧 好 那么我们此时来返回页面来看一下

308
00:09:55,500 --> 00:09:58,220
大家可以看到message success code 0 data

309
00:09:58,220 --> 00:10:01,420
其实message和code它呢属于咱们的一个

310
00:10:01,420 --> 00:10:03,320
咱们一个API的规范

311
00:10:03,320 --> 00:10:07,220
那么如果说我们需要在所有的数据里面都去添加message和code在两个字段

312
00:10:07,220 --> 00:10:10,100
如果说我们每一个请求里面都去手中去写是不是很麻烦

313
00:10:10,100 --> 00:10:11,940
所以说呢你可以去建立这样的一个积累

314
00:10:11,940 --> 00:10:14,220
那么我们这里呢来做这样的一个笔记

315
00:10:14,220 --> 00:10:24,800
message和code这样的两个字段在所有的请求里面它都需要

316
00:10:24,800 --> 00:10:26,840
是不是都需要返回

317
00:10:26,840 --> 00:10:29,820
那么如果说你每次都去写是不是很麻烦

318
00:10:29,820 --> 00:10:31,560
所以我们这里就可以去通过建议

319
00:10:31,560 --> 00:10:32,800
建议咱们这样的一个机内

320
00:10:32,800 --> 00:10:34,480
然后在咱们去使用的时候去继承它

321
00:10:34,480 --> 00:10:35,700
当然你还可以去做一些

322
00:10:35,700 --> 00:10:37,380
其他咱们业务上面的一些抽象或者封装

323
00:10:37,380 --> 00:10:39,280
大家一定要好好地去理解OOP

324
00:10:39,280 --> 00:10:41,660
也就是面向对象的一个思想

325
00:10:41,660 --> 00:10:45,480
那么大家不要觉得这样的代码

326
00:10:45,480 --> 00:10:46,600
或者说比较难

327
00:10:46,600 --> 00:10:47,620
或者说比较不是很好理解

328
00:10:47,620 --> 00:10:48,740
实际上在咱们一线的鞋中

329
00:10:48,740 --> 00:10:50,280
这样的一些设计模式

330
00:10:50,280 --> 00:10:51,580
包括方法是经常会使用到的

331
00:10:51,580 --> 00:10:52,440
好

332
00:10:52,440 --> 00:10:53,860
那么我们这里就来总结一下

333
00:10:53,860 --> 00:10:55,100
我们这一节课的内容

334
00:10:55,100 --> 00:10:57,940
我们这节课是不是

335
00:10:57,940 --> 00:11:03,360
这节课是不是主要讲到咱们的一个控制器啊

336
00:11:03,360 --> 00:11:04,220
也就是Ctrl

337
00:11:04,220 --> 00:11:06,040
那么它的作用是什么呀

338
00:11:06,040 --> 00:11:06,960
它的作用是哪些

339
00:11:06,960 --> 00:11:08,840
是不是解析http

340
00:11:08,840 --> 00:11:10,820
然后返回结果

341
00:11:10,820 --> 00:11:12,140
那么最重要的是什么

342
00:11:12,140 --> 00:11:13,960
是不是需要把

343
00:11:13,960 --> 00:11:17,020
数据库的一些操作

344
00:11:17,020 --> 00:11:18,500
是不是要加入到我们的service里面去

345
00:11:18,500 --> 00:11:23,600
那么呢

346
00:11:23,600 --> 00:11:25,060
你就不要去把一些数据和操作

347
00:11:25,060 --> 00:11:26,560
写进咱们的一个Ctrl

348
00:11:26,560 --> 00:11:30,440
这样是不是不方便我们的一个项目的一个捷偶

349
00:11:30,440 --> 00:11:34,320
那么咱们是不是还可以去编写什么呀

350
00:11:34,320 --> 00:11:34,840
多层

351
00:11:34,840 --> 00:11:37,100
是不是咱们多层嵌套的一个Ctrl

352
00:11:37,100 --> 00:11:41,380
那么此时的你只需要去建一些物件夹就可以了

353
00:11:41,380 --> 00:11:45,100
我们是不是还讲到了一个

354
00:11:45,100 --> 00:11:46,580
它的一个使用方法呀

355
00:11:46,580 --> 00:11:48,200
也就是设计技巧

356
00:11:48,200 --> 00:11:49,680
可以去

357
00:11:49,680 --> 00:11:52,280
是不是可以使用咱们的OP思想

358
00:11:53,600 --> 00:11:56,620
抽象一些机内

359
00:11:56,620 --> 00:11:58,920
好这里呢就是我们这节课的内容

