1
00:00:00,000 --> 00:00:04,540
前边我们主要熟悉了

2
00:00:04,540 --> 00:00:05,720
resover函数的第一个参数

3
00:00:05,720 --> 00:00:07,200
也就是parent的具体的用法

4
00:00:07,200 --> 00:00:08,460
那接下来我们继续来探讨

5
00:00:08,460 --> 00:00:09,680
resover函数的第三个参数

6
00:00:09,680 --> 00:00:12,180
也就是context它的基本的用法

7
00:00:12,180 --> 00:00:13,280
那这个context参数

8
00:00:13,280 --> 00:00:14,600
其实可以让我们更加方便的

9
00:00:14,600 --> 00:00:15,700
去对接这个数据源

10
00:00:15,700 --> 00:00:16,720
这里的数据源

11
00:00:16,720 --> 00:00:18,180
主要就包括如下的一些情况

12
00:00:18,180 --> 00:00:19,300
比如说这个数据库

13
00:00:19,300 --> 00:00:20,660
这是我们最常用的

14
00:00:20,660 --> 00:00:21,200
那除此之外

15
00:00:21,200 --> 00:00:23,160
也可以从文件中对数据进行操作

16
00:00:23,160 --> 00:00:25,880
那还有就是第三方的一些接口

17
00:00:25,880 --> 00:00:27,280
也可以作为我们的数据源

18
00:00:27,280 --> 00:00:28,880
那这些数据源的操作

19
00:00:28,880 --> 00:00:29,900
绝大多数场景

20
00:00:29,900 --> 00:00:31,260
现在都是义故的

21
00:00:31,260 --> 00:00:33,040
所以说我们后边也会用到promise

22
00:00:33,040 --> 00:00:35,020
首先我们来明确一下

23
00:00:35,020 --> 00:00:36,520
就是context的参数

24
00:00:36,520 --> 00:00:37,960
它如何来使用

25
00:00:37,960 --> 00:00:39,180
它的用法稍微复杂一点

26
00:00:39,180 --> 00:00:40,920
因为我们需要先定义一个函数

27
00:00:40,920 --> 00:00:42,440
然后在函数当中

28
00:00:42,440 --> 00:00:43,140
通过written的方式

29
00:00:43,140 --> 00:00:45,140
返回一个操作数据员的对象

30
00:00:45,140 --> 00:00:46,620
但是这里边

31
00:00:46,620 --> 00:00:48,460
我们先临时的给它提供了一个字符算

32
00:00:48,460 --> 00:00:49,720
只要我们能得到这个字符算

33
00:00:49,720 --> 00:00:50,720
后期我们把它换成对象

34
00:00:50,720 --> 00:00:51,840
就可以了

35
00:00:51,840 --> 00:00:52,740
然后的话

36
00:00:52,740 --> 00:00:54,220
我们需要把context的函数

37
00:00:54,220 --> 00:00:55,500
来作为参数传递给

38
00:00:55,500 --> 00:00:57,360
ApolloServer这个构造函数中

39
00:00:57,360 --> 00:00:58,780
这样的话

40
00:00:58,780 --> 00:01:00,340
我们就可以在resolver函数中

41
00:01:00,340 --> 00:01:01,340
通过第三个参数

42
00:01:01,340 --> 00:01:03,200
来得到在上面的db

43
00:01:03,200 --> 00:01:05,260
那接下来我们就具体延续一下

44
00:01:05,260 --> 00:01:07,180
那首先的话

45
00:01:07,180 --> 00:01:08,340
我们需要提供一个函数

46
00:01:08,340 --> 00:01:10,200
就是context

47
00:01:10,200 --> 00:01:12,720
然后在这里边

48
00:01:12,720 --> 00:01:13,500
我们去resolver一个对象

49
00:01:13,500 --> 00:01:14,480
这起个名字

50
00:01:14,480 --> 00:01:15,620
这个名字是自定义的

51
00:01:15,620 --> 00:01:16,600
后边的话应该是

52
00:01:16,600 --> 00:01:18,280
操作数据源的具体的一个对象

53
00:01:18,280 --> 00:01:19,520
这我们就临时的

54
00:01:19,520 --> 00:01:20,380
先填充一个图片

55
00:01:20,380 --> 00:01:21,660
比如说我们就填充db

56
00:01:21,660 --> 00:01:24,440
然后把这个函数

57
00:01:24,440 --> 00:01:25,220
作为参数

58
00:01:25,220 --> 00:01:26,040
看到这个后边

59
00:01:26,040 --> 00:01:27,980
好

60
00:01:27,980 --> 00:01:28,500
那这样的话

61
00:01:28,500 --> 00:01:29,220
我们就可以在

62
00:01:29,220 --> 00:01:30,700
这个 resolver函数中

63
00:01:30,700 --> 00:01:31,640
来获取到这个DB了

64
00:01:31,640 --> 00:01:32,520
怎么获取呢

65
00:01:32,520 --> 00:01:33,300
我们就直接把这

66
00:01:33,300 --> 00:01:34,220
改造一下

67
00:01:34,220 --> 00:01:35,380
改成parent

68
00:01:35,380 --> 00:01:36,600
然后ax

69
00:01:36,600 --> 00:01:38,000
在后边加上context

70
00:01:38,000 --> 00:01:39,800
那么这个街头函数的后边

71
00:01:39,800 --> 00:01:40,840
我们换成函数体

72
00:01:40,840 --> 00:01:43,380
这里边我们直接去written

73
00:01:43,380 --> 00:01:44,820
通过context

74
00:01:44,820 --> 00:01:46,200
获取到这个DB

75
00:01:46,200 --> 00:01:47,560
好 然后的话

76
00:01:47,560 --> 00:01:48,540
我们启动这个bo

77
00:01:48,540 --> 00:01:51,740
执行这个node mode

78
00:01:51,740 --> 00:01:53,260
然后是09

79
00:01:53,260 --> 00:01:53,980
回车

80
00:01:53,980 --> 00:01:55,780
好 启动成功之后

81
00:01:55,780 --> 00:01:56,600
我们通过浏览器

82
00:01:56,600 --> 00:01:57,540
来做一个测试

83
00:01:57,540 --> 00:01:59,160
在这呢我们要查询的

84
00:01:59,160 --> 00:01:59,820
就是hello

85
00:01:59,820 --> 00:02:01,060
然后呢

86
00:02:01,060 --> 00:02:01,260
顶记

87
00:02:01,260 --> 00:02:02,440
这里我们就拿到了

88
00:02:02,440 --> 00:02:03,700
这个DB OBJ

89
00:02:03,700 --> 00:02:05,020
只要我们后期

90
00:02:05,020 --> 00:02:06,200
把这个自破事

91
00:02:06,200 --> 00:02:07,540
换成操作数据员的对象

92
00:02:07,540 --> 00:02:09,200
那么我们在所有的resolver当中

93
00:02:09,200 --> 00:02:10,420
就可以得到这个对象了

94
00:02:10,420 --> 00:02:11,540
那得到这个对象之后呢

95
00:02:11,540 --> 00:02:12,240
其实我们就可以在这

96
00:02:12,240 --> 00:02:13,660
做这个数据的对接

97
00:02:13,660 --> 00:02:14,980
这里

98
00:02:14,980 --> 00:02:16,860
获取到

99
00:02:16,860 --> 00:02:18,740
这个操作

100
00:02:18,740 --> 00:02:19,780
数据员

101
00:02:19,780 --> 00:02:21,200
对象之后

102
00:02:21,200 --> 00:02:23,320
就可以

103
00:02:23,320 --> 00:02:25,360
非常方便的

104
00:02:25,360 --> 00:02:27,360
怎么样啊

105
00:02:27,360 --> 00:02:29,180
获取需要的数据

106
00:02:29,180 --> 00:02:31,120
同时怎么样

107
00:02:31,120 --> 00:02:32,060
返回到客户端

108
00:02:32,060 --> 00:02:36,000
这是我们在resolver函数中

109
00:02:36,000 --> 00:02:36,740
一般要做的事情

110
00:02:36,740 --> 00:02:37,980
对于查评操作来说

111
00:02:37,980 --> 00:02:40,700
就是把数据返回到客户端

112
00:02:40,700 --> 00:02:43,400
但是我们不会直接返回数据对象的

113
00:02:43,400 --> 00:02:45,340
因为后边我们会在数据对象当中

114
00:02:45,340 --> 00:02:47,360
封装一些获取数据的方法

115
00:02:47,360 --> 00:02:48,020
这个我们要注意

116
00:02:48,020 --> 00:02:51,140
这是关于context的参数的基本用法

117
00:02:51,140 --> 00:02:52,280
我们就先熟悉到这里

