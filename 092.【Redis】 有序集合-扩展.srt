1
00:00:00,000 --> 00:00:03,840
好这节课我们就来看一下有序集合他那些命令的补充

2
00:00:03,840 --> 00:00:06,140
首先第1个获取元素中元素的数量

3
00:00:06,140 --> 00:00:07,940
大家还记得我们的

4
00:00:07,940 --> 00:00:10,500
我们的lums里面他有几个元素吗

5
00:00:10,500 --> 00:00:11,780
比如说咱们去

6
00:00:11,780 --> 00:00:12,800
看一下

7
00:00:12,800 --> 00:00:14,340
Jerange咱们的lums

8
00:00:14,340 --> 00:00:15,880
他有几个元素

9
00:00:15,880 --> 00:00:16,900
实际上三个啊

10
00:00:16,900 --> 00:00:17,920
<PERSON>和David

11
00:00:17,920 --> 00:00:20,740
咱们通过Jcard就可以直接得到他那个元素的数量

12
00:00:20,740 --> 00:00:22,020
比如说Jcard

13
00:00:22,020 --> 00:00:23,560
三个

14
00:00:23,560 --> 00:00:27,140
第二个呢获取指定分数放入来的元素个数

15
00:00:27,140 --> 00:00:28,420
比如说我们的Peter是不是80分

16
00:00:28,420 --> 00:00:29,440
Toms80多分

17
00:00:29,700 --> 00:00:34,220
david是100分 那我们来获取一下我们比如说获取81分到100分 他有几个

18
00:00:34,220 --> 00:00:35,200
 是不是有两个tom和david

19
00:00:35,200 --> 00:00:40,320
那么为了方便点呢 我们来把他一个 怎么来分数给打印出来看一下 0-1

20
00:00:40,320 --> 00:00:42,420
 怎么样看分数啊 是不是位置

21
00:00:42,420 --> 00:00:44,040
scores啊

22
00:00:44,040 --> 00:00:45,320
位置score

23
00:00:45,320 --> 00:00:51,320
难道是要大写吗 好 我们来大写一下位置

24
00:00:51,320 --> 00:00:53,560
scores

25
00:00:53,560 --> 00:00:55,300
empty

26
00:00:55,300 --> 00:00:57,200
咱们加少了一个s

27
00:00:58,360 --> 00:01:01,680
好大概看到我们的一个三个元素他那个分数啊我来获取一下

28
00:01:01,680 --> 00:01:06,040
81分到100分他这个元素个数z count在一个命令z count

29
00:01:06,040 --> 00:01:09,360
比如说81到100分

30
00:01:09,360 --> 00:01:10,400
什么两个呀

31
00:01:10,400 --> 00:01:11,420
汤和达密

32
00:01:11,420 --> 00:01:14,240
好那我们来看一下怎么样去双出一个元素

33
00:01:14,240 --> 00:01:17,560
比如说我们要把peter给双掉他只有80分分数最低要淘汰

34
00:01:17,560 --> 00:01:19,100
z 21m

35
00:01:19,100 --> 00:01:24,980
peter好返回1说明了已经双掉我们再来看一下

36
00:01:28,360 --> 00:01:30,540
好大家可以看到我们现在只有

37
00:01:30,540 --> 00:01:32,560
POM和DAV的两个人了

38
00:01:32,560 --> 00:01:34,620
好再来看一下第四个命令

39
00:01:34,620 --> 00:01:36,840
按照排名范围双处元素

40
00:01:36,840 --> 00:01:38,760
什么意思啊

41
00:01:38,760 --> 00:01:39,820
我们现在是不是有两个人

42
00:01:39,820 --> 00:01:41,440
POM和DAV的按照排名范围

43
00:01:41,440 --> 00:01:43,420
你比如说如果说你需要去实现一个需求

44
00:01:43,420 --> 00:01:44,220
什么需求呢

45
00:01:44,220 --> 00:01:45,400
你对一堆人进行的排序

46
00:01:45,400 --> 00:01:47,460
你想排除最末尾的十个人

47
00:01:47,460 --> 00:01:49,560
或者说最前面的十个人

48
00:01:49,560 --> 00:01:50,760
你可以用这样一个命令

49
00:01:50,760 --> 00:01:51,960
叫什么呢

50
00:01:51,960 --> 00:01:53,780
Z21M

51
00:01:53,780 --> 00:01:56,080
Range by Rank

52
00:01:56,080 --> 00:01:57,540
什么意思啊

53
00:01:57,540 --> 00:01:59,340
你要去双除REM双除

54
00:01:59,340 --> 00:02:00,580
双除一个范围

55
00:02:00,580 --> 00:02:01,220
通过什么呢

56
00:02:01,220 --> 00:02:02,060
通过它们的分数

57
00:02:02,060 --> 00:02:02,980
好

58
00:02:02,980 --> 00:02:03,540
这里呢

59
00:02:03,540 --> 00:02:04,560
我们比如说啊

60
00:02:04,560 --> 00:02:05,320
比如说我们去

61
00:02:05,320 --> 00:02:07,400
比如说我们去Zadd这样一个

62
00:02:07,400 --> 00:02:08,720
testIM这样一个字段

63
00:02:08,720 --> 00:02:11,040
我们添加了ABCDEF6个值

64
00:02:11,040 --> 00:02:13,040
它们分数分别是123456

65
00:02:13,040 --> 00:02:14,520
那么如果说我们想双除

66
00:02:14,520 --> 00:02:16,380
如果说我们想双除前三名

67
00:02:16,380 --> 00:02:17,220
双除前三名

68
00:02:17,220 --> 00:02:18,000
我们来看一下

69
00:02:18,000 --> 00:02:18,860
这个命令

70
00:02:18,860 --> 00:02:20,260
ZREM

71
00:02:20,260 --> 00:02:21,500
RUNGE BY RUNK

72
00:02:21,500 --> 00:02:22,620
TEST IAM0到2

73
00:02:22,620 --> 00:02:23,440
是不是双除前三名

74
00:02:23,440 --> 00:02:23,920
好

75
00:02:23,920 --> 00:02:24,700
那我们再来看一下

76
00:02:24,700 --> 00:02:25,400
它还剩下多少

77
00:02:25,400 --> 00:02:26,160
ZRUNGE

78
00:02:26,160 --> 00:02:31,060
TESLEM0-1大家可以看到它只剩DEF了

79
00:02:31,060 --> 00:02:33,820
也就是说咱们把前面分数最低的三个给酸掉

80
00:02:33,820 --> 00:02:38,080
好 这里是按它的一个排名范围去酸除元素

81
00:02:38,080 --> 00:02:41,220
那么我们来看一下怎么样去按照分数范围去酸除元素

82
00:02:41,220 --> 00:02:43,180
其实是不是一样的呀

83
00:02:43,180 --> 00:02:46,620
之前咱们是byrank 现在我们只需要改为byscore

84
00:02:46,620 --> 00:02:52,660
比如说我们去改为ZRM rangebyscore

85
00:02:52,660 --> 00:02:54,240
酸谁呢 咱们还是酸TESLEM

86
00:02:54,240 --> 00:02:55,700
它现在是不是只剩下第一F

87
00:02:55,700 --> 00:02:57,300
分别是四五六分

88
00:02:57,300 --> 00:02:58,480
那我们双除

89
00:02:58,480 --> 00:03:00,800
我们就双除分数为六的

90
00:03:00,800 --> 00:03:01,380
是不是F

91
00:03:01,380 --> 00:03:02,080
那我们来看一下

92
00:03:02,080 --> 00:03:02,520
双的一个

93
00:03:02,520 --> 00:03:05,200
咱们来看一下TESAIM还剩多少

94
00:03:05,200 --> 00:03:05,980
是不是还剩两个

95
00:03:05,980 --> 00:03:06,480
第一啊

96
00:03:06,480 --> 00:03:07,620
因为刚才咱们是不是双了

97
00:03:07,620 --> 00:03:09,200
双的第六名也就是F

98
00:03:09,200 --> 00:03:10,040
不是第六名

99
00:03:10,040 --> 00:03:10,980
是分数为六的F

100
00:03:10,980 --> 00:03:11,240
对吧

101
00:03:11,240 --> 00:03:11,740
我们来看一下

102
00:03:11,740 --> 00:03:13,240
是不是现在只剩下第一呢

103
00:03:13,240 --> 00:03:13,400
好

104
00:03:13,400 --> 00:03:16,300
我们再来看一下第六个命令

105
00:03:16,300 --> 00:03:17,580
获得元素的排名

106
00:03:17,580 --> 00:03:19,200
获得元素的排名

107
00:03:19,200 --> 00:03:21,600
现在咱们的TESAIM

108
00:03:21,600 --> 00:03:22,180
这样一个字段

109
00:03:22,180 --> 00:03:23,340
是不是只剩下D和E

110
00:03:23,340 --> 00:03:24,220
那么我们来看一下

111
00:03:24,220 --> 00:03:27,680
d 他排第几名 是不是第一名了 我们来看下 z rank

112
00:03:27,680 --> 00:03:31,840
testimd 大家可以看到返回0 说明他是第一名

113
00:03:31,840 --> 00:03:34,560
我们来看下 e 呢 1 说明他是第二名

114
00:03:34,560 --> 00:03:39,180
同样的呢 还有一个 ziv rank 是不是一个是升序 一个是降序

115
00:03:39,180 --> 00:03:42,060
这里我就不过多的去介绍了 我们来看下第七个命令

116
00:03:42,060 --> 00:03:45,120
计算有序集合 他的一个交集 什么意思

117
00:03:45,120 --> 00:03:46,860
什么意思

118
00:03:46,860 --> 00:03:49,040
他的命令是

119
00:03:49,040 --> 00:03:52,560
z inter store

120
00:03:52,560 --> 00:03:56,900
咱们见到Store这样一个单词的时候

121
00:03:56,900 --> 00:03:57,660
在Reddit里面

122
00:03:57,660 --> 00:03:59,780
它是不是会存储到一个新的制度里面去

123
00:03:59,780 --> 00:04:01,100
对吧

124
00:04:01,100 --> 00:04:04,160
好 我们来看一下

125
00:04:04,160 --> 00:04:06,860
ZintelStore这样一个命令

126
00:04:06,860 --> 00:04:08,000
它虽然看起来长

127
00:04:08,000 --> 00:04:08,900
但大家不要胖

128
00:04:08,900 --> 00:04:10,560
很简单其实这样一个命令

129
00:04:10,560 --> 00:04:12,220
比如说我们先去Zadd一个

130
00:04:12,220 --> 00:04:14,040
Zadd一个谁呢

131
00:04:14,040 --> 00:04:16,040
比如说我们先Zadd一个

132
00:04:16,040 --> 00:04:18,420
测试数据吧

133
00:04:18,420 --> 00:04:18,780
TTE

134
00:04:18,780 --> 00:04:20,760
add一个分数1A

135
00:04:20,760 --> 00:04:22,020
分数2B

136
00:04:22,020 --> 00:04:23,980
咱们再去再add一个

137
00:04:23,980 --> 00:04:27,240
测试数据2pt2

138
00:04:27,240 --> 00:04:28,640
比如说咱们的A是10分

139
00:04:28,640 --> 00:04:29,860
B拿20分

140
00:04:29,860 --> 00:04:31,200
这里有两个有序集合

141
00:04:31,200 --> 00:04:32,260
分别存储了A和B

142
00:04:32,260 --> 00:04:33,740
它们的分数相差10倍

143
00:04:33,740 --> 00:04:36,700
那么假如说我们想求T1和T2

144
00:04:36,700 --> 00:04:38,140
它们两个集合的一个交集

145
00:04:38,140 --> 00:04:38,960
怎么去做

146
00:04:38,960 --> 00:04:40,060
它们的交集是不是A和B

147
00:04:40,060 --> 00:04:41,540
那么求交集的时候

148
00:04:41,540 --> 00:04:42,100
它的分数

149
00:04:42,100 --> 00:04:43,400
其实我们也要去给它们的分数

150
00:04:43,400 --> 00:04:44,220
去求一个交集

151
00:04:44,220 --> 00:04:46,080
这里它们的分数有三种情况

152
00:04:46,080 --> 00:04:48,180
sum,min和max

153
00:04:48,180 --> 00:04:48,620
什么意思

154
00:04:48,620 --> 00:04:49,560
你比如说

155
00:04:49,560 --> 00:04:50,840
我们的T1和T2

156
00:04:50,840 --> 00:04:51,960
它的交集肯定是A和B

157
00:04:51,960 --> 00:04:53,380
但是它们的分数不一样怎么办

158
00:04:53,380 --> 00:04:54,440
可以通过这样一个算术

159
00:04:54,440 --> 00:04:54,960
比如说SUM

160
00:04:54,960 --> 00:04:56,800
SUM就会对它们的权重进行相加

161
00:04:56,800 --> 00:04:57,740
比如说1和10

162
00:04:57,740 --> 00:04:58,300
相加是11

163
00:04:58,300 --> 00:04:58,860
这就是SUM

164
00:04:58,860 --> 00:05:00,140
命运就是取最小的

165
00:05:00,140 --> 00:05:01,220
比如说T1和T2

166
00:05:01,220 --> 00:05:02,120
它们的交集是A和B

167
00:05:02,120 --> 00:05:03,440
但是它们的分数取最小的

168
00:05:03,440 --> 00:05:05,260
所以得色的结果就是A1和B2

169
00:05:05,260 --> 00:05:06,400
如果是max呢

170
00:05:06,400 --> 00:05:07,620
就是A10和B20

171
00:05:07,620 --> 00:05:09,120
那么我们就来看一下

172
00:05:09,120 --> 00:05:11,000
Zinter

173
00:05:11,000 --> 00:05:12,300
Store

174
00:05:12,300 --> 00:05:15,340
Store后面是不是跟上我们要存储的

175
00:05:15,340 --> 00:05:15,940
这段叫什么

176
00:05:15,940 --> 00:05:16,920
比如说我们叫做

177
00:05:16,920 --> 00:05:18,880
存储吧

178
00:05:18,880 --> 00:05:19,360
就叫存储

179
00:05:19,360 --> 00:05:20,100
存储

180
00:05:20,100 --> 00:05:20,860
然后呢

181
00:05:20,860 --> 00:05:23,020
跟上我们求交集的一个数量

182
00:05:23,020 --> 00:05:23,740
我们是不是要求

183
00:05:23,740 --> 00:05:25,680
TT1和TT2它的一个交集

184
00:05:25,680 --> 00:05:27,440
这里有个参数叫做LarmCase

185
00:05:27,440 --> 00:05:29,160
也就是说你要求几个集合的交集

186
00:05:29,160 --> 00:05:30,500
咱们要求两个TT1和TT2

187
00:05:30,500 --> 00:05:31,240
所以你呢给一个2

188
00:05:31,240 --> 00:05:33,520
然后呢传络你需要去求的交集

189
00:05:33,520 --> 00:05:34,880
TT1和TT2

190
00:05:34,880 --> 00:05:37,800
然后是不是还需要接受一个参数啊

191
00:05:37,800 --> 00:05:39,200
也就是你的一个权重子们去计算

192
00:05:39,200 --> 00:05:40,500
Sum,Min和Max

193
00:05:40,500 --> 00:05:41,400
你也可以不传

194
00:05:41,400 --> 00:05:42,460
它的默认值是Sum

195
00:05:42,460 --> 00:05:44,200
好我们先不传看一下

196
00:05:44,200 --> 00:05:45,780
这里得到一个返回值2

197
00:05:45,780 --> 00:05:47,400
说明了他们求出的交集数量为2

198
00:05:47,400 --> 00:05:48,780
我们来看一下结果

199
00:05:48,780 --> 00:05:49,940
ZRange

200
00:05:49,940 --> 00:05:51,220
谁呢

201
00:05:51,220 --> 00:05:51,660
存储

202
00:05:51,660 --> 00:05:53,240
0-1

203
00:05:53,240 --> 00:05:54,180
大家可以看到a和b

204
00:05:54,180 --> 00:05:55,400
我们是不是看到分数啊

205
00:05:55,400 --> 00:05:56,720
所以我们可以通过一个min

206
00:05:56,720 --> 00:05:57,840
with scores

207
00:05:57,840 --> 00:05:59,740
可以看到a11b22

208
00:05:59,740 --> 00:06:02,040
是不是他们的分数进行了相加呀

209
00:06:02,040 --> 00:06:03,220
那么如果说我们不想相加

210
00:06:03,220 --> 00:06:04,240
假如说我们不想相加

211
00:06:04,240 --> 00:06:05,940
我们存储

212
00:06:05,940 --> 00:06:06,800
是不是要改一下存储1

213
00:06:06,800 --> 00:06:08,480
我们传入一个参数叫做什么呢

214
00:06:08,480 --> 00:06:08,740
min

215
00:06:08,740 --> 00:06:10,880
也就是他们的分数取最小的那一个

216
00:06:10,880 --> 00:06:13,360
s syntax error

217
00:06:13,360 --> 00:06:16,000
这里来说明我们的权重

218
00:06:16,000 --> 00:06:16,740
只能是

219
00:06:16,740 --> 00:06:17,900
比如说我们去取min

220
00:06:17,900 --> 00:06:20,900
好 少一个参数叫做

221
00:06:20,900 --> 00:06:25,900
aggb

222
00:06:25,900 --> 00:06:27,900
好 我们来看一下

223
00:06:27,900 --> 00:06:29,900
比如说我们取为min

224
00:06:29,900 --> 00:06:30,900
好 大家可以看到

225
00:06:30,900 --> 00:06:32,900
我们需要传入

226
00:06:32,900 --> 00:06:34,900
aggregate这样一个

227
00:06:34,900 --> 00:06:35,900
这样一个参数

228
00:06:35,900 --> 00:06:37,900
后面来跟上你的一个规则

229
00:06:37,900 --> 00:06:38,900
上min和max

230
00:06:38,900 --> 00:06:39,900
好 我们再来看一下

231
00:06:39,900 --> 00:06:40,900
咱们的存储1

232
00:06:40,900 --> 00:06:42,900
它是一个什么样的结果

233
00:06:42,900 --> 00:06:46,900
这 run 存储1

234
00:06:46,900 --> 00:06:50,680
好大家可以看到我们现在只是变成了A1和B2同样的

235
00:06:50,680 --> 00:06:52,080
同样的我们如果说改为max

236
00:06:52,080 --> 00:06:53,480
改为max

237
00:06:53,480 --> 00:06:57,000
那我们的存储1是不是就变成了A10和B20

238
00:06:57,000 --> 00:06:59,820
存储1

239
00:06:59,820 --> 00:07:03,020
好大家可以看到已经改为了A10和B20

240
00:07:03,020 --> 00:07:05,280
这里呢就是咱们计算有序集合交集上一个命令

241
00:07:05,280 --> 00:07:06,440
看起来很长其实很简单

242
00:07:06,440 --> 00:07:10,240
那么我们就来总结一下这节课的内容

243
00:07:10,240 --> 00:07:15,740
这里呢我们是不是学习了一些咱们命令的补充

244
00:07:15,740 --> 00:07:16,780
学习了哪些呢

245
00:07:16,780 --> 00:07:18,680
第一个是不是获取我们元素的数量

246
00:07:18,680 --> 00:07:20,800
获取集合数量

247
00:07:20,800 --> 00:07:21,720
什么呢

248
00:07:21,720 --> 00:07:22,920
贼card

249
00:07:22,920 --> 00:07:23,760
第二个呢

250
00:07:23,760 --> 00:07:24,520
获取数量之后

251
00:07:24,520 --> 00:07:30,600
我们是不是还可以去获取指定分数范围的元素个数

252
00:07:30,600 --> 00:07:32,020
通过什么命令

253
00:07:32,020 --> 00:07:33,100
贼count

254
00:07:33,100 --> 00:07:33,780
好

255
00:07:33,780 --> 00:07:36,340
那么我们获取分数范围的元素个数

256
00:07:36,340 --> 00:07:37,400
我们是不是还可以双处它

257
00:07:37,400 --> 00:07:38,200
怎么双处

258
00:07:38,200 --> 00:07:39,560
是不是贼

259
00:07:39,560 --> 00:07:41,400
那么双处之后

260
00:07:41,400 --> 00:07:44,000
我们还可以去按照我们的范围去双处

261
00:07:44,000 --> 00:07:45,380
按照范围

262
00:07:45,380 --> 00:07:48,780
删除 怎么就按照范围删除啊 其实很简单 Z-I-E-M

263
00:07:48,780 --> 00:07:53,580
by rank rank是什么 分数排名

264
00:07:53,580 --> 00:07:58,180
不知道同学们有没有打过DOTA 里面以前是不是有个天梯分数 他呢 又叫做rank

265
00:07:58,180 --> 00:08:05,340
我们不仅可以通过排名去删除 是不是还可以去按照咱们的分数删呢 按照分数删

266
00:08:05,340 --> 00:08:08,160
是什么 Z-I-E-M

267
00:08:08,160 --> 00:08:10,980
by什么

268
00:08:10,980 --> 00:08:14,560
by score

269
00:08:15,080 --> 00:08:19,680
好刚才呢这里我们是不是也少了一个zimrun几百rank呀好

270
00:08:19,680 --> 00:08:26,080
所以说redis他的命令设计是不是非常的有规则有规律很好记对吧好我们来看一下

271
00:08:26,080 --> 00:08:30,440
你可以按范围可以按分数双处那么我们是不是也可以获取元素的排名呢

272
00:08:30,440 --> 00:08:33,000
怎么样去获取

273
00:08:33,000 --> 00:08:35,820
其实就是zrank

274
00:08:35,820 --> 00:08:41,960
或者呢你可能是升序和降序获取的排名他不一样所以还有一个对应的也就是zivrank

275
00:08:43,760 --> 00:08:47,340
按我们或许排名之后是不是还讲那个非常复杂的命令很长很恶心

276
00:08:47,340 --> 00:08:48,880
但是有非常简单的一个命名叫什么

277
00:08:48,880 --> 00:08:49,900
jinter

278
00:08:49,900 --> 00:08:53,240
jinter store代表什么他可以去存储

279
00:08:53,240 --> 00:08:55,540
他是做什么呢

280
00:08:55,540 --> 00:08:58,600
咱们说简单一点是不是就是求

281
00:08:58,600 --> 00:09:00,140
求有序

282
00:09:00,140 --> 00:09:01,680
集合的交奖

283
00:09:01,680 --> 00:09:03,980
咱们是不是要注意他的三个参数一个sum

284
00:09:03,980 --> 00:09:05,520
一个min一个max

285
00:09:05,520 --> 00:09:09,100
好这里呢就是咱们这几个的内容那么讲到这里呢我们redis

286
00:09:09,100 --> 00:09:09,880
第1章

287
00:09:09,880 --> 00:09:11,920
第1章的内容就已经全部结束了我们讲

288
00:09:12,180 --> 00:09:15,760
咱们讲的哪些 咱们是不是讲的radis的一个发展 包括他的一些特点

289
00:09:15,760 --> 00:09:18,060
他的特点呢 是不是

290
00:09:18,060 --> 00:09:18,580
他

291
00:09:18,580 --> 00:09:20,120
是一个字典结构

292
00:09:20,120 --> 00:09:22,680
19化功能的很丰富 包括他的API很简单

293
00:09:22,680 --> 00:09:24,220
以及我们讲的radis的一些

294
00:09:24,220 --> 00:09:24,720
安装

295
00:09:24,720 --> 00:09:27,280
然后还讲解了radis他的一些简单的操作

296
00:09:27,280 --> 00:09:31,120
对于重要的 我们第一章最重要的内容 是不是我们总共讲了radis的几种数据类型

297
00:09:31,120 --> 00:09:32,400
首先制作串

298
00:09:32,400 --> 00:09:33,680
闪链

299
00:09:33,680 --> 00:09:35,980
闪链是不是就是哈希

300
00:09:35,980 --> 00:09:37,020
包括列表

301
00:09:37,020 --> 00:09:38,800
有点类似于咱们JS的数组吧

302
00:09:38,800 --> 00:09:39,820
然后还讲了

303
00:09:39,820 --> 00:09:40,600
集合

304
00:09:41,100 --> 00:09:43,660
集合讲了之后还讲了有序集合 这里就是redis

305
00:09:43,660 --> 00:09:45,460
比较核心的几种数据类型

306
00:09:45,460 --> 00:09:46,480
那么后面内容

307
00:09:46,480 --> 00:09:51,340
我们就会去讲解一些redis比较高级的东西 包括咱们怎么去结合 去使用

308
00:09:51,340 --> 00:09:52,880
这里就是咱们这节课的内容

