1
00:00:00,000 --> 00:00:03,080
各位同学,大家好,欢迎继续学习NodeGX网络通信

2
00:00:03,080 --> 00:00:07,240
那么前面我们学过TCP服务,UDP服务,HTTP服务

3
00:00:07,240 --> 00:00:10,340
那么接下来我们一起来去搭建HTTPs服务

4
00:00:10,340 --> 00:00:13,360
本章节的内容主要包含了以下五个方面的内容

5
00:00:13,360 --> 00:00:18,640
首先第一个,我们需要给大家先去回顾一下关于HTTPs的一个原理部分

6
00:00:18,640 --> 00:00:22,720
那么再接下来我们需要去模拟一个CA机构,然后去安装本地证书

7
00:00:22,720 --> 00:00:26,200
那么此时我们得到证书之后就可以搭建一个本地的HTTPs服务

8
00:00:26,200 --> 00:00:28,340
当然这个服务器会出现一定的问题

9
00:00:28,340 --> 00:00:29,640
那么同时我们就会引出

10
00:00:29,640 --> 00:00:33,120
关于国际CA机构的认证证书的一个申请方式

11
00:00:33,120 --> 00:00:33,840
那么在接下来呢

12
00:00:33,840 --> 00:00:34,980
我们就拿到这个证书之后

13
00:00:34,980 --> 00:00:36,620
来去做一个真实上线的

14
00:00:36,620 --> 00:00:38,700
ATB-S服务器的搭建

