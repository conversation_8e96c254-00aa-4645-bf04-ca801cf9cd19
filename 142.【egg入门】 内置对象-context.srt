1
00:00:00,000 --> 00:00:02,760
好,这节课我们就来看一下类似对象里面的contest

2
00:00:02,760 --> 00:00:04,780
那么contest呢,我们先来看一下它的介绍

3
00:00:04,780 --> 00:00:06,520
它呢,是一个请求级别的对象

4
00:00:06,520 --> 00:00:08,180
继承至call里面的contest

5
00:00:08,180 --> 00:00:10,260
在每一次收到用户请求时

6
00:00:10,260 --> 00:00:12,140
框架会实力化一个contest的对象

7
00:00:12,140 --> 00:00:14,440
那么它和APP有什么区别啊

8
00:00:14,440 --> 00:00:16,440
我们的APP它是不是全局只实力化一次

9
00:00:16,440 --> 00:00:18,140
但是呢,我们的contest

10
00:00:18,140 --> 00:00:19,880
你每一次收到用户请求时

11
00:00:19,880 --> 00:00:22,140
框架呢,它都会去实力化一个contest

12
00:00:22,140 --> 00:00:24,180
那么在一个对象呢,它封装了

13
00:00:24,180 --> 00:00:25,940
这个,这一次用户请求的

14
00:00:25,940 --> 00:00:28,000
信息,而且呢,提供了一些便捷的方法

15
00:00:29,000 --> 00:00:31,720
来获取咱们请求的参数或者去设置响应信息

16
00:00:31,720 --> 00:00:33,560
说白了contest是做什么用的

17
00:00:33,560 --> 00:00:36,400
是不是就是专门去处理咱们的请求的一个对象

18
00:00:36,400 --> 00:00:36,820
好

19
00:00:36,820 --> 00:00:38,400
那么我们就来看一下怎么样可以获取它

20
00:00:38,400 --> 00:00:41,580
其实获取contest是不是咱们到处都可以获取啊

21
00:00:41,580 --> 00:00:42,940
你比如说中间键里面controller

22
00:00:42,940 --> 00:00:44,100
对吧

23
00:00:44,100 --> 00:00:44,480
service

24
00:00:44,480 --> 00:00:45,260
好

25
00:00:45,260 --> 00:00:46,900
那么我们就来看一下在哪里可以去获取

26
00:00:46,900 --> 00:00:49,220
我们简单来回归一下

27
00:00:49,220 --> 00:00:50,000
你比如说

28
00:00:50,000 --> 00:00:51,520
我们的controller里面

29
00:00:51,520 --> 00:00:52,460
是不是可以获取

30
00:00:52,460 --> 00:00:54,920
在this上面有一个contest这样的一个对象吧

31
00:00:54,920 --> 00:00:56,200
那么除了controller呢

32
00:00:56,200 --> 00:00:58,080
同样的咱们之前是不是写了一个关于log的

33
00:00:58,080 --> 00:00:58,880
中间键

34
00:00:58,880 --> 00:01:00,680
那么我们的中间键里面

35
00:01:00,680 --> 00:01:02,380
是不是也会获取到一个contest

36
00:01:02,380 --> 00:01:03,500
说说的contest

37
00:01:03,500 --> 00:01:07,340
它可以说是无处不在的一个对象

38
00:01:07,340 --> 00:01:07,680
好

39
00:01:07,680 --> 00:01:08,580
那么我们来看一下

40
00:01:08,580 --> 00:01:10,140
这种情况

41
00:01:10,140 --> 00:01:11,400
你比如说

42
00:01:11,400 --> 00:01:12,500
你比如说

43
00:01:12,500 --> 00:01:13,200
虽然说contest

44
00:01:13,200 --> 00:01:14,780
它是跟随我们的什么

45
00:01:14,780 --> 00:01:15,620
是跟随我们的请求

46
00:01:15,620 --> 00:01:16,680
那么有一些

47
00:01:16,680 --> 00:01:18,060
你非用户请求的场景下

48
00:01:18,060 --> 00:01:19,340
如果说我们需要去访问

49
00:01:19,340 --> 00:01:20,680
咱们的一个contest

50
00:01:20,680 --> 00:01:21,560
怎么办

51
00:01:21,560 --> 00:01:23,340
你比如说我们举个例子

52
00:01:23,340 --> 00:01:25,100
咱们现在举个例子

53
00:01:25,100 --> 00:01:26,920
你比如说咱们在app实力化

54
00:01:26,920 --> 00:01:28,600
也就是说在咱们的服务没有启动之前

55
00:01:28,600 --> 00:01:31,180
你呢需要去访问context上面的某一个属性

56
00:01:31,180 --> 00:01:31,600
怎么办

57
00:01:31,600 --> 00:01:33,520
其实这里在APP里面提供了一个方法

58
00:01:33,520 --> 00:01:34,160
叫做什么呢

59
00:01:34,160 --> 00:01:36,500
Create Alloymos Context

60
00:01:36,500 --> 00:01:37,040
这样一个方法

61
00:01:37,040 --> 00:01:39,160
它就可以去创建一个临时的context

62
00:01:39,160 --> 00:01:41,940
你一样呢可以去拿到咱们context上面的一些属性或者方法

63
00:01:41,940 --> 00:01:43,500
那我们来讲一下它有什么用场景

64
00:01:43,500 --> 00:01:44,980
你比如说我这里呢举个例子

65
00:01:44,980 --> 00:01:47,580
你假如说你去启动一个服务器的时候

66
00:01:47,580 --> 00:01:50,360
你有一个方法专门去计算你的端口在哪里

67
00:01:50,360 --> 00:01:50,600
对吧

68
00:01:50,600 --> 00:01:51,380
但是呢这样一个方法

69
00:01:51,380 --> 00:01:53,520
它是不是挂载在你的context对象上面

70
00:01:53,520 --> 00:01:55,140
但是此时你的服务是不是还没启动

71
00:01:55,140 --> 00:01:55,680
你拿不到

72
00:01:55,680 --> 00:01:56,800
所以你可以通过APP

73
00:01:56,800 --> 00:01:58,160
这样一个方法

74
00:01:58,160 --> 00:02:00,460
可以去临时的去创建contest

75
00:02:00,460 --> 00:02:01,200
此时你就可以拿到

76
00:02:01,200 --> 00:02:02,440
它上面一些属性或者方法去做

77
00:02:02,440 --> 00:02:04,420
你想要做的一个事情

78
00:02:04,420 --> 00:02:08,340
而且呢contest是不是在咱们的一个定时任务里面

79
00:02:08,340 --> 00:02:09,040
也可以去用啊

80
00:02:09,040 --> 00:02:09,700
那么定时任务呢

81
00:02:09,700 --> 00:02:10,920
我们后面会去讲

82
00:02:10,920 --> 00:02:12,380
好我们来回顾一下contest

83
00:02:12,380 --> 00:02:15,140
刚才讲了一些内容

84
00:02:15,140 --> 00:02:20,520
好我们

85
00:02:20,520 --> 00:02:23,140
我们刚才是不是讲到了contest

86
00:02:23,140 --> 00:02:25,440
那么contest他是做什么呀

87
00:02:25,440 --> 00:02:26,460
是不是伴随

88
00:02:26,460 --> 00:02:28,000
是不是伴随

89
00:02:28,000 --> 00:02:29,800
每一次

90
00:02:29,800 --> 00:02:31,340
请求啊

91
00:02:31,340 --> 00:02:33,380
一次请求实力化

92
00:02:33,380 --> 00:02:35,940
一次好那么呢他有什么作用啊

93
00:02:35,940 --> 00:02:37,220
刚才是有什么作用

94
00:02:37,220 --> 00:02:37,980
就是

95
00:02:37,980 --> 00:02:39,520
你每次请求实力化一次

96
00:02:39,520 --> 00:02:42,340
就是解析咱们的解析咱们什么呀

97
00:02:42,340 --> 00:02:43,620
是解析我们的

98
00:02:43,620 --> 00:02:45,160
请求参数

99
00:02:45,160 --> 00:02:46,940
或者一些是不是咱们的一些

100
00:02:46,940 --> 00:02:47,460
响应

101
00:02:47,460 --> 00:02:48,480
对吧

102
00:02:48,480 --> 00:02:49,000
好

103
00:02:49,000 --> 00:02:49,760
那么呢

104
00:02:49,760 --> 00:02:51,300
在哪里可以获取到

105
00:02:51,300 --> 00:02:52,840
获取方式

106
00:02:53,140 --> 00:02:55,380
是不是在我们的一个

107
00:02:55,380 --> 00:02:57,340
mead world里面可以获取

108
00:02:57,340 --> 00:02:57,880
还有啦

109
00:02:57,880 --> 00:02:59,240
controller吧

110
00:02:59,240 --> 00:03:00,020
包括啦

111
00:03:00,020 --> 00:03:02,040
service也是可以去获取的

112
00:03:02,040 --> 00:03:03,360
那么如果说我们

113
00:03:03,360 --> 00:03:05,140
你需要去临时获取怎么办

114
00:03:05,140 --> 00:03:06,000
此时还没有请求

115
00:03:06,000 --> 00:03:08,220
是不是可以通过APP的一个方法呀

116
00:03:08,220 --> 00:03:08,740
叫做什么

117
00:03:08,740 --> 00:03:09,580
是不是这个

118
00:03:09,580 --> 00:03:11,280
create alloymos contest

119
00:03:11,280 --> 00:03:12,020
对吧

120
00:03:12,020 --> 00:03:12,540
好

121
00:03:12,540 --> 00:03:15,340
create alloymos contest

122
00:03:15,340 --> 00:03:16,100
好

123
00:03:16,100 --> 00:03:17,560
这里呢就是我们这几课的内容

