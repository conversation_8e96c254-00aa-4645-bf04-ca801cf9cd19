1
00:00:00,520 --> 00:00:03,840
好 这里我们就来看一下 我们怎么样去进行项目的一些配置

2
00:00:03,840 --> 00:00:06,400
那么框架提供了强大且可扩展的配置功能

3
00:00:06,400 --> 00:00:11,520
可以了自动合并应用插件框架的配置按顺序去覆盖 而且能可以根据环境去维护不同的配置

4
00:00:11,520 --> 00:00:15,100
和平后的配置可以直接从app的config在一个对象里面去获取

5
00:00:15,100 --> 00:00:19,460
那么在config目录下面是不是有很多 比如说config.default.js

6
00:00:19,460 --> 00:00:21,240
config.proud.js

7
00:00:21,240 --> 00:00:24,060
那么我们的项目中目前有哪一个

8
00:00:24,060 --> 00:00:28,680
我们的项目目前是不是有一个config.default.js 说明什么 他是不是一个默认的配置

9
00:00:28,920 --> 00:00:32,000
那么下面的比如说proud local和unit test 他呢

10
00:00:32,000 --> 00:00:34,800
他们你是不是当前在什么环境下

11
00:00:34,800 --> 00:00:36,340
他就会读取哪个配章

12
00:00:36,340 --> 00:00:43,000
你比如说我们我们去执行npm run dv 的时候你只是是不是在local在那个环境下面那么他就会去加载config.local.js

13
00:00:43,000 --> 00:00:50,160
那么如果说你是proud的环境那就会去加载config.proud.js 而且是不是会与config的default.js

14
00:00:50,160 --> 00:00:54,260
也就是说你当前的环境配置是不是和会和你的一个默认配置去合并

15
00:00:54,260 --> 00:00:57,080
而且你的环境配置会覆盖默认的配置

16
00:00:57,340 --> 00:00:59,480
那么我们来看一下咱们配置的一个写法

17
00:00:59,480 --> 00:01:00,700
我们之前是不是写过配置

18
00:01:00,700 --> 00:01:01,180
你比如说

19
00:01:01,180 --> 00:01:02,980
你比如说我们要去

20
00:01:02,980 --> 00:01:06,980
比如说我们来看一下吧

21
00:01:06,980 --> 00:01:08,940
我们直接来看一下我们的config长什么样

22
00:01:08,940 --> 00:01:10,620
比如说我们的config

23
00:01:10,620 --> 00:01:11,720
它是不是会返回一个

24
00:01:11,720 --> 00:01:12,840
对象

25
00:01:12,840 --> 00:01:14,680
里面呢有一个config这样的一个属性

26
00:01:14,680 --> 00:01:16,720
比如说我们之前是不是配置了一个什么呀

27
00:01:16,720 --> 00:01:17,500
是不是配置了我们一个模板

28
00:01:17,500 --> 00:01:19,500
那么我们需要在config里面去加一个view属性

29
00:01:19,500 --> 00:01:22,180
而且呢我们是不是还配置了一个中间键

30
00:01:22,180 --> 00:01:23,620
我们去记录一些日志的中间键

31
00:01:23,620 --> 00:01:24,500
那么呢我们会

32
00:01:24,500 --> 00:01:26,400
是不是加入一个middlewall这样的一个属性

33
00:01:26,400 --> 00:01:27,180
然后呢里面是一个宿主

34
00:01:27,180 --> 00:01:30,080
然后里面会传入你所需要去配置的一些中间的名称

35
00:01:30,080 --> 00:01:32,720
这里是config它的一个用法

36
00:01:32,720 --> 00:01:34,140
那么我们刚才是不是讲到了

37
00:01:34,140 --> 00:01:36,900
是不是咱们的环境配置会覆盖我们的默认配置

38
00:01:36,900 --> 00:01:39,480
那么我们在默认的配置里面是不是引用了一个middlewall叫做

39
00:01:39,480 --> 00:01:41,000
叫做log

40
00:01:41,000 --> 00:01:42,440
那么我们就来尝试一下

41
00:01:42,440 --> 00:01:43,700
看我们能不能够去覆盖它

42
00:01:43,700 --> 00:01:48,860
比如说我们去定一个config.local.js

43
00:01:48,860 --> 00:01:52,140
那么我们现在去做一个什么事情呢

44
00:01:52,140 --> 00:01:55,720
我们现在就把config的middlewall

45
00:01:55,720 --> 00:01:56,660
把它的速度改为空

46
00:01:56,660 --> 00:01:57,660
那么大家觉得

47
00:01:57,660 --> 00:01:59,680
此时我们去执行NPM run dv的时候

48
00:01:59,680 --> 00:02:01,680
我们的log在那一个中间键

49
00:02:01,680 --> 00:02:03,380
它还会不会去执行

50
00:02:03,380 --> 00:02:04,440
那么我们就来试一下

51
00:02:04,440 --> 00:02:07,960
好 首先我先把它注视

52
00:02:07,960 --> 00:02:09,580
我们来看一下我们的中间键能不能执行

53
00:02:09,580 --> 00:02:11,800
NPM run dv

54
00:02:11,800 --> 00:02:13,580
好 打开我们的middlewall

55
00:02:13,580 --> 00:02:15,260
刚才我们前面内容是不是奇怪

56
00:02:15,260 --> 00:02:19,080
middlewall

57
00:02:19,080 --> 00:02:23,040
好 我们是不是每次进行请求的时候

58
00:02:23,040 --> 00:02:23,800
都会打印这样一段

59
00:02:23,800 --> 00:02:25,460
我是日志什么什么中间键 对吧

60
00:02:25,460 --> 00:02:27,160
好现在呢我们的配置是不是打开了

61
00:02:27,160 --> 00:02:29,000
因为我们把这行代码给注释了

62
00:02:29,000 --> 00:02:29,780
那么我们来看一下

63
00:02:29,780 --> 00:02:30,540
是不是这么回事

64
00:02:30,540 --> 00:02:32,320
好

65
00:02:32,320 --> 00:02:34,580
好像没有生效

66
00:02:34,580 --> 00:02:37,360
再来起一遍

67
00:02:37,360 --> 00:02:41,880
好像还是没有

68
00:02:41,880 --> 00:02:42,960
好像还是没有

69
00:02:42,960 --> 00:02:44,260
好我先把这样一个文件给刷掉

70
00:02:55,460 --> 00:02:58,380
其实原因是什么呀

71
00:02:58,380 --> 00:03:00,720
是不是刚才我们在测试NPM start的时候

72
00:03:00,720 --> 00:03:01,620
是不是起了一个守护进程

73
00:03:01,620 --> 00:03:02,340
忘了给退出啊

74
00:03:02,340 --> 00:03:03,920
那么我们来把它给关一下

75
00:03:03,920 --> 00:03:06,340
7037我们把它给退出一下

76
00:03:06,340 --> 00:03:07,920
好

77
00:03:07,920 --> 00:03:09,840
那么我们此时来重启一下咱们的dv环境

78
00:03:09,840 --> 00:03:12,840
好大家可以看到

79
00:03:12,840 --> 00:03:14,760
此时我们的日子中间键是不是生效了

80
00:03:14,760 --> 00:03:15,660
你每次的请求

81
00:03:15,660 --> 00:03:18,040
我们是不是都会打印出咱们一个日子信息

82
00:03:18,040 --> 00:03:19,480
那么这里呢

83
00:03:19,480 --> 00:03:20,260
我们来创建一个

84
00:03:20,260 --> 00:03:22,500
我们来创建一个config点

85
00:03:22,500 --> 00:03:23,800
local点键

86
00:03:23,800 --> 00:03:24,740
因为我们现在的一个

87
00:03:24,740 --> 00:03:25,520
环境

88
00:03:25,520 --> 00:03:26,240
是不是local环境

89
00:03:26,240 --> 00:03:26,780
也就是咱们那个

90
00:03:26,780 --> 00:03:27,720
本地的开放环境

91
00:03:27,720 --> 00:03:28,960
那么我们来复制看一下

92
00:03:28,960 --> 00:03:30,260
比如说我们把local

93
00:03:30,260 --> 00:03:31,200
把它给去掉

94
00:03:31,200 --> 00:03:31,960
但是呢

95
00:03:31,960 --> 00:03:32,720
我们的default里面

96
00:03:32,720 --> 00:03:33,320
是不是有

97
00:03:33,320 --> 00:03:34,160
那么我们就来看一下

98
00:03:34,160 --> 00:03:35,740
local它是否会把它给覆盖掉

99
00:03:35,740 --> 00:03:37,360
好

100
00:03:37,360 --> 00:03:37,940
我们再来访问

101
00:03:37,940 --> 00:03:38,420
走里

102
00:03:38,420 --> 00:03:39,680
大家可以看到

103
00:03:39,680 --> 00:03:41,140
我们的中间键是不是已经失效了

104
00:03:41,140 --> 00:03:41,820
说明一个什么问题

105
00:03:41,820 --> 00:03:43,060
是不是说明我们的

106
00:03:43,060 --> 00:03:45,100
local会覆盖我们的一个

107
00:03:45,100 --> 00:03:45,860
default

108
00:03:45,860 --> 00:03:46,780
这样的一个配置

109
00:03:46,780 --> 00:03:51,080
好

110
00:03:51,080 --> 00:03:51,840
那么呢

111
00:03:51,840 --> 00:03:54,380
那么我们的配置文件

112
00:03:54,380 --> 00:03:56,760
他呢也可以呢去返回一个

113
00:03:56,760 --> 00:03:58,520
比如说你也可以去返回一个方形

114
00:03:58,520 --> 00:04:00,580
刚才我们是不是就是通过方形的写法

115
00:04:00,580 --> 00:04:02,180
我们来config打给看一下

116
00:04:02,180 --> 00:04:05,580
我们config是不是通过方形的方式

117
00:04:05,580 --> 00:04:06,940
去return了一个新的对象出来

118
00:04:06,940 --> 00:04:07,920
就是咱们的一个配置文件

119
00:04:07,920 --> 00:04:10,280
那么呢他还会传入一个appinfo这样一个参数

120
00:04:10,280 --> 00:04:11,820
那么appinfo是干什么的呢

121
00:04:11,820 --> 00:04:12,280
我们来看一下

122
00:04:12,280 --> 00:04:13,840
那么类似的appinfo

123
00:04:13,840 --> 00:04:15,700
它下面呢会有一些的属性

124
00:04:15,700 --> 00:04:17,800
比如说pkg这样的属性是什么

125
00:04:17,800 --> 00:04:20,860
是他呢可以把我们的一个pkg连接生内容给打印出来

126
00:04:20,860 --> 00:04:22,460
name就是咱们的一个应用名

127
00:04:22,460 --> 00:04:23,940
包括呢baseDR

128
00:04:23,940 --> 00:04:25,220
咱们应用代码的一个目录

129
00:04:25,220 --> 00:04:26,620
home 我们的用户目录

130
00:04:26,620 --> 00:04:28,300
root 我们的一个用户的一个

131
00:04:28,300 --> 00:04:29,060
根目录

132
00:04:29,060 --> 00:04:30,820
那么这里可能有同学们会有疑问的

133
00:04:30,820 --> 00:04:34,460
那么你这个baseDR和home它有什么区别

134
00:04:34,460 --> 00:04:35,860
大家可以看到下面一段解释

135
00:04:35,860 --> 00:04:38,140
appinfo.root它是一个优雅的配置

136
00:04:38,140 --> 00:04:39,900
比如说在服务器环境

137
00:04:39,900 --> 00:04:41,780
我们会使用你比如说你肯定

138
00:04:41,780 --> 00:04:43,060
会使用你的比如说你在home

139
00:04:43,060 --> 00:04:44,700
amintlogs去做一个日子目录

140
00:04:44,700 --> 00:04:46,820
而本地又不想去污染用户目录

141
00:04:46,820 --> 00:04:48,940
这样的配置就可以去解决这个问题

142
00:04:48,940 --> 00:04:50,460
什么意思说的有点迷

143
00:04:50,460 --> 00:04:51,500
其实大家可以看到

144
00:04:51,500 --> 00:04:53,860
我们在应用的一个运行过程中

145
00:04:53,860 --> 00:04:55,200
是不是会生成一些日资文件呢

146
00:04:55,200 --> 00:04:56,040
logs

147
00:04:56,040 --> 00:04:56,720
这里呢

148
00:04:56,720 --> 00:04:57,520
我来给大家看一下

149
00:04:57,520 --> 00:05:00,880
这里呢

150
00:05:00,880 --> 00:05:01,820
他是1GG呢

151
00:05:01,820 --> 00:05:03,420
他自己去帮我们去记录了一些日资

152
00:05:03,420 --> 00:05:04,400
大家可以看到

153
00:05:04,400 --> 00:05:05,580
logs在那一个日资文件

154
00:05:05,580 --> 00:05:06,480
你看有日期

155
00:05:06,480 --> 00:05:07,080
info

156
00:05:07,080 --> 00:05:07,820
包括呢

157
00:05:07,820 --> 00:05:08,580
有一些信息

158
00:05:08,580 --> 00:05:09,640
我们去做了哪些操作

159
00:05:09,640 --> 00:05:10,900
包括呢

160
00:05:10,900 --> 00:05:11,560
还会有一些error

161
00:05:11,560 --> 00:05:12,260
比如说我们之前去

162
00:05:12,260 --> 00:05:13,100
请求的时候报了一些错

163
00:05:13,100 --> 00:05:13,620
都会呢

164
00:05:13,620 --> 00:05:14,680
存在咱们的日资文件里面

165
00:05:14,680 --> 00:05:15,480
大家可以看到我们呢

166
00:05:15,480 --> 00:05:16,580
我们的日资文件在哪里

167
00:05:16,580 --> 00:05:18,520
我们的日资文件在哪里

168
00:05:18,520 --> 00:05:20,060
是不是在咱们的项目根目录下面

169
00:05:20,060 --> 00:05:21,020
对吧

170
00:05:21,020 --> 00:05:22,460
是不是在咱们的项目根目录下面

171
00:05:22,460 --> 00:05:23,680
那么这里呢

172
00:05:23,680 --> 00:05:24,720
在我们本地开发的时候

173
00:05:24,720 --> 00:05:25,440
是不是没有什么问题

174
00:05:25,440 --> 00:05:26,980
但是往往在我们的生产环境中

175
00:05:26,980 --> 00:05:27,720
我们的日志文件

176
00:05:27,720 --> 00:05:28,820
是不是有可能

177
00:05:28,820 --> 00:05:30,360
你不会存在你的本地啊

178
00:05:30,360 --> 00:05:31,080
你可能希望

179
00:05:31,080 --> 00:05:32,700
去用一块单独的硬盘去存储它

180
00:05:32,700 --> 00:05:34,460
因为我们的日志文件可能会非常大

181
00:05:34,460 --> 00:05:34,740
你

182
00:05:34,740 --> 00:05:37,340
我们是不是会把它存储在其他的地方

183
00:05:37,340 --> 00:05:38,640
那么这里呢

184
00:05:38,640 --> 00:05:40,020
就可以使用这样的一个办法

185
00:05:40,020 --> 00:05:40,780
去解决这个问题

186
00:05:40,780 --> 00:05:42,500
你呢就可以使用

187
00:05:42,500 --> 00:05:44,460
appinfo的root和basedr

188
00:05:44,460 --> 00:05:45,660
去区分这样一个环境

189
00:05:45,660 --> 00:05:46,660
好

190
00:05:46,660 --> 00:05:47,680
那么我们接下来来看一下

191
00:05:47,680 --> 00:05:49,020
我们配置的一个加载顺序是什么

192
00:05:49,020 --> 00:05:50,220
刚才我们是不是讲到了

193
00:05:50,220 --> 00:05:51,100
我们的一个

194
00:05:51,100 --> 00:05:51,820
比如说我们的

195
00:05:51,820 --> 00:05:53,680
比如说我们config local.js

196
00:05:53,680 --> 00:05:55,560
是不是会覆盖我们的默认的一个配置

197
00:05:55,560 --> 00:05:57,940
但是在我们的一GG的框架里面

198
00:05:57,940 --> 00:06:00,020
我们是不是会写一些插件

199
00:06:00,020 --> 00:06:01,320
包括呢会写一些应用

200
00:06:01,320 --> 00:06:02,340
也比如说什么是插件

201
00:06:02,340 --> 00:06:03,440
是不是medwall

202
00:06:03,440 --> 00:06:04,020
咱们的中间件

203
00:06:04,020 --> 00:06:05,040
它是属于插件的

204
00:06:05,040 --> 00:06:06,380
那么应用是不是就是我们的

205
00:06:06,380 --> 00:06:06,940
我们的什么

206
00:06:06,940 --> 00:06:08,000
是不是app目录下面的

207
00:06:08,000 --> 00:06:08,520
对吧

208
00:06:08,520 --> 00:06:10,280
这个里面是不是属于我们的应用

209
00:06:10,280 --> 00:06:11,980
那么config里面

210
00:06:11,980 --> 00:06:13,320
比如说我们是不是会引入一些

211
00:06:13,320 --> 00:06:14,840
比如说我们是不是会引入一些

212
00:06:14,840 --> 00:06:17,020
medwall对吧

213
00:06:17,020 --> 00:06:18,220
那么同时呢

214
00:06:18,220 --> 00:06:18,780
APP呢

215
00:06:18,780 --> 00:06:19,980
同时呢我们的一GG

216
00:06:19,980 --> 00:06:21,020
是不是还会有一些

217
00:06:21,020 --> 00:06:21,920
内置的一些配置

218
00:06:21,920 --> 00:06:23,380
那么我们来看一下覆盖顺序是什么

219
00:06:23,380 --> 00:06:24,260
覆盖顺序呢

220
00:06:24,260 --> 00:06:26,580
实际上是根据优先级而存在的

221
00:06:26,580 --> 00:06:27,540
那么优先级是什么呢

222
00:06:27,540 --> 00:06:29,140
应用的优先级是最高的

223
00:06:29,140 --> 00:06:29,540
说明呢

224
00:06:29,540 --> 00:06:30,620
你在应用里面去做一些配置

225
00:06:30,620 --> 00:06:31,540
它呢会覆盖框架

226
00:06:31,540 --> 00:06:33,280
那么框架呢就会去覆盖插件

227
00:06:33,280 --> 00:06:34,920
那么框架是1GG

228
00:06:34,920 --> 00:06:35,540
那么插件呢

229
00:06:35,540 --> 00:06:36,780
我们呢

230
00:06:36,780 --> 00:06:38,280
暂时还没有设计到去写插件

231
00:06:38,280 --> 00:06:39,240
后面内容会去介绍

232
00:06:39,240 --> 00:06:39,980
大家可以了解一下

233
00:06:39,980 --> 00:06:41,420
配置的一个加载顺序

234
00:06:41,420 --> 00:06:43,040
也可以方便我们后面去进行一些debug

235
00:06:43,040 --> 00:06:43,780
好

236
00:06:43,780 --> 00:06:45,480
那么接下来我们来看一下我们的一个配置

237
00:06:45,480 --> 00:06:45,960
我们的一个合并

238
00:06:45,960 --> 00:06:47,440
我们刚才是不是其实已经

239
00:06:47,440 --> 00:06:50,140
我们刚才是不是已经演示了我们的一个合并了

240
00:06:50,140 --> 00:06:52,580
你比如说我们的配置A和B去合并

241
00:06:52,580 --> 00:06:54,400
那么B里面有一个数组

242
00:06:54,400 --> 00:06:54,820
里面是3

243
00:06:54,820 --> 00:06:55,800
那么A那是12

244
00:06:55,800 --> 00:06:56,960
B呢是不是会去

245
00:06:56,960 --> 00:06:58,800
B呢是不是会去覆盖A

246
00:06:58,800 --> 00:07:01,300
那么B覆盖A之后的结果是什么

247
00:07:01,300 --> 00:07:02,100
其实结果

248
00:07:02,100 --> 00:07:03,140
结果是什么

249
00:07:03,140 --> 00:07:04,020
其实结果就是

250
00:07:04,020 --> 00:07:07,440
其实他们覆盖之后的结果

251
00:07:07,440 --> 00:07:08,640
还是3

252
00:07:08,640 --> 00:07:09,400
不会是123

253
00:07:09,400 --> 00:07:11,000
就和咱们刚才举的例子是不是一样的呀

254
00:07:11,000 --> 00:07:12,400
我们的default

255
00:07:12,400 --> 00:07:13,520
middlewall里面有个log

256
00:07:13,520 --> 00:07:15,260
那么我们的local里面传的是一个空

257
00:07:15,260 --> 00:07:16,580
直接会用空的宿主

258
00:07:16,580 --> 00:07:18,840
把咱们旧的宿主直接给替换掉

259
00:07:18,840 --> 00:07:20,640
这里是不是一个浅合并

260
00:07:20,640 --> 00:07:21,100
对吧

261
00:07:21,100 --> 00:07:23,400
好

262
00:07:23,400 --> 00:07:25,000
那么这里我们再来看一下

263
00:07:25,000 --> 00:07:26,720
其实我们在配置的过程中

264
00:07:26,720 --> 00:07:29,120
在run在一个目录下面

265
00:07:29,120 --> 00:07:30,840
还会有一些配置相关的信息

266
00:07:30,840 --> 00:07:32,300
那么我们来看一下它到底是什么

267
00:07:32,300 --> 00:07:36,460
你比如说我们现在

268
00:07:36,460 --> 00:07:38,740
egg是不是帮我们自动生成了run在一个文件目录

269
00:07:38,740 --> 00:07:40,420
那么我们来看一下什么呢

270
00:07:40,420 --> 00:07:41,740
applicationconfig.json

271
00:07:41,740 --> 00:07:43,740
大家可以看到

272
00:07:43,740 --> 00:07:44,060
是不是有

273
00:07:44,060 --> 00:07:45,340
Config下面是不是有非常多的属性

274
00:07:45,340 --> 00:07:46,220
你比如说像

275
00:07:46,220 --> 00:07:47,540
Session

276
00:07:47,540 --> 00:07:48,500
Security

277
00:07:48,500 --> 00:07:49,800
安全相关的一些配置

278
00:07:49,800 --> 00:07:50,620
包括了HEP

279
00:07:50,620 --> 00:07:51,660
我们的HEP

280
00:07:51,660 --> 00:07:53,300
包括了JsonP

281
00:07:53,300 --> 00:07:54,040
On Arrow

282
00:07:54,040 --> 00:07:54,580
I18N

283
00:07:54,580 --> 00:07:54,980
其实呢

284
00:07:54,980 --> 00:07:56,240
他们的都是在一机记里面

285
00:07:56,240 --> 00:07:57,520
内置的一些配置信息

286
00:07:57,520 --> 00:07:58,660
那么有的同学可能会问的

287
00:07:58,660 --> 00:07:59,660
那么我们配置的

288
00:07:59,660 --> 00:08:00,660
比如说我们自己写的Config

289
00:08:00,660 --> 00:08:01,200
在哪里了

290
00:08:01,200 --> 00:08:02,000
其实可以找

291
00:08:02,000 --> 00:08:03,080
大家可以看到

292
00:08:03,080 --> 00:08:03,960
我们之前是不是配了一个

293
00:08:03,960 --> 00:08:04,800
LongJunk式的模板引擎

294
00:08:04,800 --> 00:08:06,160
那么我们来搜索一下View

295
00:08:06,160 --> 00:08:07,200
它里面有没有View

296
00:08:07,200 --> 00:08:08,340
大家是不是可以看到

297
00:08:08,340 --> 00:08:09,120
大家是不是可以看到

298
00:08:09,120 --> 00:08:11,040
Config里面有View这样一个属性

299
00:08:11,040 --> 00:08:12,080
那么这里的Mapping

300
00:08:12,080 --> 00:08:13,700
Mapping和DefaultView

301
00:08:13,700 --> 00:08:15,480
应接是不是我们配置里面给传进去了

302
00:08:15,480 --> 00:08:16,920
包括什么CatchForce

303
00:08:16,920 --> 00:08:19,060
包括这里的DefaultExtension

304
00:08:19,060 --> 00:08:19,980
是不是View里面

305
00:08:19,980 --> 00:08:21,400
比如说Longer是在一个插件

306
00:08:21,400 --> 00:08:22,360
它默认的一些配置

307
00:08:22,360 --> 00:08:24,420
但是呢我们我们去写了一些Config之后

308
00:08:24,420 --> 00:08:25,580
是不是会把它给覆盖掉

309
00:08:25,580 --> 00:08:27,020
这里呢就是咱们

310
00:08:27,020 --> 00:08:29,280
所有的Config的信息

311
00:08:29,280 --> 00:08:31,500
都会在Run在那个文件夹里面去呈现

312
00:08:31,500 --> 00:08:32,300
你比如说不仅呢

313
00:08:32,300 --> 00:08:33,060
它可以展示APP的

314
00:08:33,060 --> 00:08:34,280
包括我们的Adients

315
00:08:34,280 --> 00:08:37,160
包括我们的一些其他的一些配置

316
00:08:37,160 --> 00:08:38,560
都会在Run里面可以去看到

317
00:08:38,560 --> 00:08:39,660
这样是不是很方便呢

318
00:08:39,660 --> 00:08:40,600
比如说我们应用有哪些

319
00:08:40,600 --> 00:08:41,520
有哪些配置生效

320
00:08:41,520 --> 00:08:42,320
哪些配置没有生效

321
00:08:42,320 --> 00:08:43,720
我们可以直接在一个目录里面

322
00:08:43,720 --> 00:08:45,480
可以找到所有的配置信息

323
00:08:45,480 --> 00:08:47,460
那么这里还给同学们介绍一个东西

324
00:08:47,460 --> 00:08:47,960
叫做什么呢

325
00:08:47,960 --> 00:08:48,980
叫做meta.json

326
00:08:48,980 --> 00:08:51,200
我们刚才是不是看的是applicationconfig.json

327
00:08:51,200 --> 00:08:53,200
我们来看一下meta.json和它有什么区别

328
00:08:53,200 --> 00:08:54,900
其实也非常的好理解

329
00:08:54,900 --> 00:08:56,420
你比如说我们的view

330
00:08:56,420 --> 00:08:57,520
在一个属性

331
00:08:57,520 --> 00:08:59,500
我们刚才是不是通过config去改写了mapping

332
00:08:59,500 --> 00:08:59,900
对吧

333
00:08:59,900 --> 00:09:01,640
那么其实在meta的js里面

334
00:09:01,640 --> 00:09:02,680
它其实是一个映射关系

335
00:09:02,680 --> 00:09:03,180
映射什么呢

336
00:09:03,180 --> 00:09:04,740
我们在哪里去修改了它

337
00:09:04,740 --> 00:09:05,640
你比如说

338
00:09:05,640 --> 00:09:07,860
我们的mapping在哪里修改了

339
00:09:07,860 --> 00:09:08,900
是不是在config的

340
00:09:08,900 --> 00:09:11,140
config.defaultgs里面去修改

341
00:09:11,140 --> 00:09:13,000
那么我们就可以在meta.js里面可以看到

342
00:09:13,000 --> 00:09:15,540
我们的nj这样一个mapping这样一个属性

343
00:09:15,540 --> 00:09:16,840
是哪里修改的呢

344
00:09:16,840 --> 00:09:17,300
大家可以看到

345
00:09:17,300 --> 00:09:21,160
是不是在我们一个根目录里面的config.local.js里面去修改

346
00:09:21,160 --> 00:09:23,100
那么同时的我们再随便看一下其他的属性

347
00:09:23,100 --> 00:09:24,000
你比如说

348
00:09:24,000 --> 00:09:26,040
你比如说我们什么defaultextension

349
00:09:26,040 --> 00:09:28,400
它是不是咱们的一个view自带的一个

350
00:09:28,400 --> 00:09:29,940
自带的一个配置

351
00:09:29,940 --> 00:09:31,120
它来自于哪里了

352
00:09:31,120 --> 00:09:31,980
是不是来自于loadmodules

353
00:09:31,980 --> 00:09:33,560
说明了它是默认的配置

354
00:09:33,560 --> 00:09:34,000
好

355
00:09:34,000 --> 00:09:35,220
这里呢就是我们

356
00:09:35,220 --> 00:09:37,620
这里呢就是我们

357
00:09:37,620 --> 00:09:39,820
config这节的内容

358
00:09:39,820 --> 00:09:41,640
我们一起来回顾一下

359
00:09:41,640 --> 00:09:46,640
config

360
00:09:46,640 --> 00:09:50,140
其实config的内容

361
00:09:50,140 --> 00:09:50,700
我们刚才是不是

362
00:09:50,700 --> 00:09:52,680
它内容是不是还比较多

363
00:09:52,680 --> 00:09:53,980
因为它也是相当重要的

364
00:09:53,980 --> 00:09:54,460
在我们的项目中

365
00:09:54,460 --> 00:09:55,860
我们刚才是不是讲解了

366
00:09:55,860 --> 00:09:57,580
我们的config的一些配置

367
00:09:57,580 --> 00:10:01,220
比如说你可以去写一些config.before

368
00:10:01,220 --> 00:10:01,720
对吧

369
00:10:01,720 --> 00:10:02,760
还有呢

370
00:10:02,760 --> 00:10:04,220
你是不是可以去通过环境去

371
00:10:04,220 --> 00:10:07,060
可以通过环境去覆盖

372
00:10:07,060 --> 00:10:11,620
呀比如说我们的什么卡费一个点比如说你在一个本地就是费个local.js

373
00:10:11,620 --> 00:10:16,180
但是呢你如果说是生产环境你是不是就是看费给连泡的点见识可以去覆盖他的一些默认的配置

374
00:10:16,180 --> 00:10:19,000
那么我们配置的写法支持哪些呢

375
00:10:19,000 --> 00:10:21,520
你是不是可以直接去

376
00:10:21,520 --> 00:10:27,580
直接去export是一个对象啊当然了你是不是还可以去暴露一个方形呢对吧

377
00:10:27,580 --> 00:10:34,600
那么你当你export是一个方形的时候他是不是会接受一个参数叫做什么是不是app

378
00:10:34,600 --> 00:10:36,120
info呀

379
00:10:37,000 --> 00:10:41,040
对吧他呢会帮我们去注入一个参数appinfo那么appinfo他里面

380
00:10:41,040 --> 00:10:42,280
这是哪些属性呢

381
00:10:42,280 --> 00:10:49,210
就是有一个pk机做什么呢是不是可以去访问到我们的park几点接受包括了内我们用名称包括我们的bass

382
00:10:49,210 --> 00:10:49,940
 dr是不是我们的

383
00:10:49,940 --> 00:10:52,560
用根目录它是什么路径对吧包括了一个

384
00:10:52,560 --> 00:10:59,700
痛也是我们的根目录还有一个是root那么root他可以解决什么问题是不是可以解决我们日子文件存储的

385
00:10:59,700 --> 00:11:00,700
一字星呢

386
00:11:00,700 --> 00:11:06,880
你比如说你在本地你是不是可以存在你的项目录但是你们但是在服务器呢你不能存在根目录你可能需要单独的地方去存储我们的一个日子文件

387
00:11:07,000 --> 00:11:09,040
那么我们是不是还讲到了

388
00:11:09,040 --> 00:11:11,300
我们一个加载顺序

389
00:11:11,300 --> 00:11:13,280
对吧

390
00:11:13,280 --> 00:11:15,980
那么加载顺序是什么样的呢

391
00:11:15,980 --> 00:11:19,840
是不是应用大于我们的一个框架

392
00:11:19,840 --> 00:11:20,240
对吧

393
00:11:20,240 --> 00:11:21,000
那么框架呢

394
00:11:21,000 --> 00:11:22,260
是不是大于我们的插件

395
00:11:22,260 --> 00:11:24,640
那么我们是不是还讲了一个合并规则

396
00:11:24,640 --> 00:11:25,580
合并规则是什么

397
00:11:25,580 --> 00:11:26,160
潜合并

398
00:11:26,160 --> 00:11:32,140
那么我们潜合并之后

399
00:11:32,140 --> 00:11:33,840
是不是还讲到了

400
00:11:33,840 --> 00:11:35,100
我们最终是不是在乱里面

401
00:11:35,100 --> 00:11:36,300
乱里面

402
00:11:36,300 --> 00:11:38,300
乱在一个文件夹里面会生成一个什么呀

403
00:11:38,300 --> 00:11:40,300
生成一个最终的配置表吧

404
00:11:40,300 --> 00:11:44,300
首先呢我们是不是有一个

405
00:11:44,300 --> 00:11:46,300
有一个什么呀是不是有一个接生文件

406
00:11:46,300 --> 00:11:48,300
一个接生文件就是最终的配置表吧

407
00:11:48,300 --> 00:11:51,300
而且呢还会生成一个什么是不是meta点接生呢

408
00:11:51,300 --> 00:11:53,300
那么meta点接生

409
00:11:53,300 --> 00:11:56,300
它有什么作用我们是不是可以看到

410
00:11:56,300 --> 00:11:59,300
从哪里修改了咱们的配置

411
00:11:59,300 --> 00:12:01,940
好这里呢就是我们最近看到的内容

