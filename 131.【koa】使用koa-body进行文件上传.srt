1
00:00:00,000 --> 00:00:02,040
好 这节课我们就来看一下

2
00:00:02,040 --> 00:00:05,120
我们怎么样去使用Core玻璃模块去处理咱们的文件上传

3
00:00:05,120 --> 00:00:09,220
那么呢 这里呢 我们就直接把代码给Core到咱们的一个

4
00:00:09,220 --> 00:00:13,560
代码里面去 我们去通过断点带着同学们去一行一行的去学习 怎么去进行文件上传

5
00:00:13,560 --> 00:00:16,900
好 这里呢 我呢其实已经把代码提前给写好了 也Core过来了

6
00:00:16,900 --> 00:00:21,230
这里呢 我就不去写了 因为写了 比较浪费时间 我直接通过断点一行一行的给同学们去分析

7
00:00:21,230 --> 00:00:22,020
 这样可能会更好

8
00:00:22,020 --> 00:00:24,580
好 首先呢 我们来

9
00:00:24,580 --> 00:00:27,140
运行咱们的一个服务

10
00:00:28,420 --> 00:00:29,700
我们先来分析一下前面代码

11
00:00:29,700 --> 00:00:30,980
比如说我们会有些依赖

12
00:00:30,980 --> 00:00:31,740
这里先不用去管

13
00:00:31,740 --> 00:00:32,260
好

14
00:00:32,260 --> 00:00:35,340
这里App.use是不是会使用咱们的cora body的一个

15
00:00:35,340 --> 00:00:35,840
中间键

16
00:00:35,840 --> 00:00:37,120
然后他会去传入一些参数

17
00:00:37,120 --> 00:00:38,140
比如说multipart2

18
00:00:38,140 --> 00:00:38,660
什么意思

19
00:00:38,660 --> 00:00:40,200
是不是说明了我们上传的是文件

20
00:00:40,200 --> 00:00:41,980
而且你是不是还可以去上传多个文件呢

21
00:00:41,980 --> 00:00:44,040
可以上传

22
00:00:44,040 --> 00:00:45,580
多个文件

23
00:00:45,580 --> 00:00:47,620
好

24
00:00:47,620 --> 00:00:50,440
我们这里定义了upload这样一个路由

25
00:00:50,440 --> 00:00:51,460
然后如果说

26
00:00:51,460 --> 00:00:54,020
咱们post发到了upload这里就会执行咱们的命方法

27
00:00:54,020 --> 00:00:54,540
好

28
00:00:54,540 --> 00:00:56,840
那么这里呢我首先在咱们的fails这里打一个断点

29
00:00:56,840 --> 00:00:57,860
我们先来分析前面的代码

30
00:00:58,120 --> 00:01:05,160
首先 我们第一行调用的os.tmpdr这个方法 os是什么 是不是咱们nodg是原生提供的一些一个模块

31
00:01:05,160 --> 00:01:10,780
它可以去操作咱们操作系统的一些方法 这里tmpdr它是做什么的呢 其实它是创建一个系统的临时目录

32
00:01:10,780 --> 00:01:16,880
其实我们文件上传 这里我们会随机申请目录 然后让咱们的文件去存储在一个地方

33
00:01:16,880 --> 00:01:20,600
当然了你也可以去指定一个目录 这里我们相对是随机申请目录

34
00:01:20,600 --> 00:01:25,960
第二行 bear pass 我们这里去用一个数组去定义 它是咱们

35
00:01:25,960 --> 00:01:27,400
最终

36
00:01:28,120 --> 00:01:32,460
最终生成的文件

37
00:01:32,460 --> 00:01:35,160
那么有的同学可能会问了

38
00:01:35,160 --> 00:01:36,420
你的一个地址明明是一个字符创

39
00:01:36,420 --> 00:01:37,760
比如说我们去

40
00:01:37,760 --> 00:01:38,840
比如说你在一个user

41
00:01:38,840 --> 00:01:40,680
user这样一个目录下面的一个

42
00:01:40,680 --> 00:01:42,260
什么work目录下面

43
00:01:42,260 --> 00:01:43,800
或者说你一个什么fares

44
00:01:43,800 --> 00:01:45,220
你明明可以用一个字符创去表示

45
00:01:45,220 --> 00:01:45,960
你为什么要用一个数字

46
00:01:45,960 --> 00:01:47,280
这个问题问得非常好

47
00:01:47,280 --> 00:01:48,560
不知道同学们还记不记得

48
00:01:48,560 --> 00:01:49,520
我们刚才是不是讲解了

49
00:01:49,520 --> 00:01:50,080
咱们的call body

50
00:01:50,080 --> 00:01:51,500
是不是有一个multipart这样一个属性

51
00:01:51,500 --> 00:01:52,080
说明什么问题

52
00:01:52,080 --> 00:01:54,140
我们是不是可以同时上传多个文件了

53
00:01:54,140 --> 00:01:55,840
所以我们用数字去存储

54
00:01:55,840 --> 00:01:56,600
它的一个地址

55
00:01:56,600 --> 00:01:58,180
可以支持我们去上传多个文件

56
00:01:58,180 --> 00:02:00,140
好 接下来是Fails

57
00:02:00,140 --> 00:02:01,600
那么Fails呢它就是

58
00:02:01,600 --> 00:02:03,000
CoreBody解析

59
00:02:03,000 --> 00:02:04,900
比如说你去Post上传了一段文件

60
00:02:04,900 --> 00:02:06,960
那么呢咱们通过Core.CoreBody

61
00:02:06,960 --> 00:02:08,460
这样一个中间键它会自动的

62
00:02:08,460 --> 00:02:10,640
把咱们的一个文件对象给挂载到

63
00:02:10,640 --> 00:02:12,680
context.request.fails这样一个属性下面

64
00:02:12,680 --> 00:02:14,020
这是咱们中间键去处理的

65
00:02:14,020 --> 00:02:16,320
那么呢我们就来真正的上传一个文件来看一下

66
00:02:16,320 --> 00:02:20,840
其实呢在Postman里面是支持去上传文件的

67
00:02:20,840 --> 00:02:22,240
我们这里已经呢上传成功了

68
00:02:22,240 --> 00:02:24,340
好 这里呢来给同学们去演示一下

69
00:02:24,340 --> 00:02:54,340
首先我们来选择到咱们的玻璃栅栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏栏�

70
00:02:54,340 --> 00:02:54,860
pass是什么

71
00:02:54,860 --> 00:02:56,640
pass其实就是在咱们

72
00:02:56,640 --> 00:02:59,720
是不是咱们 nodejs首先你上传是不是经过咱们的库尔根玻璃

73
00:02:59,720 --> 00:03:02,780
那么呢这里的一个路径就是咱们库尔根玻璃它随机生成的一个

74
00:03:02,780 --> 00:03:08,680
目录但是呢他是不是我们所指定的目录不是的这是库尔他随机生成的目录等一下我们是不是需要把这样的一个

75
00:03:08,680 --> 00:03:10,980
文件给转移到咱们指定的一个目录里面去

76
00:03:10,980 --> 00:03:11,740
对吧

77
00:03:11,740 --> 00:03:14,300
包括了他一些大小包括我们的文件一些内型

78
00:03:14,300 --> 00:03:15,340
inmagic jpg

79
00:03:15,340 --> 00:03:17,120
好这里呢我们需要去

80
00:03:17,120 --> 00:03:19,940
关注一下首先呢库尔根玻璃它会自动去处理并挂载到

81
00:03:20,960 --> 00:03:23,260
他自己定义的一个目录

82
00:03:23,260 --> 00:03:25,060
什么意思是不是咱们的coa

83
00:03:25,060 --> 00:03:28,380
我里在一个中间间会把我们上传文件给存储到一个他所指定的地方啊

84
00:03:28,380 --> 00:03:31,960
但是我们希望把文件存到哪里

85
00:03:31,960 --> 00:03:33,500
是希望存到我们自己

86
00:03:33,500 --> 00:03:34,780
所指定的目录

87
00:03:34,780 --> 00:03:38,360
当然

88
00:03:38,360 --> 00:03:39,640
这个目录你是可以改的

89
00:03:39,640 --> 00:03:41,180
因为这里就是我们的目标目录

90
00:03:41,180 --> 00:03:44,520
你可以是一个临时目录当然你也可以指定到你自己的一些文件夹里面去

91
00:03:44,520 --> 00:03:48,100
你们可以去修改的这里是可以好那么我们继续来往下面走

92
00:03:48,360 --> 00:03:51,240
这里是不是走到一个负循环 首先fails它是不是一个对象

93
00:03:51,240 --> 00:03:56,280
然后呢刚才我们看到fails里面是不是有很是不是有一个空字不串 它的value是一个fail对象

94
00:03:56,280 --> 00:03:57,780
因为你有可能上传多个文件

95
00:03:57,780 --> 00:04:00,060
所以呢这里会有很多的k然后呢对应不同的文件

96
00:04:00,060 --> 00:04:04,400
所以我们通过一个负循环来把咱们的一个是不是通过for-in循环把咱们的fails给便利出来

97
00:04:04,400 --> 00:04:08,740
首先便利是不是首先咱们是不是可以第一次便利是不是可以得到咱们的一个

98
00:04:08,740 --> 00:04:12,060
咱们真正的文件对象fail对吧好 然后这里呢

99
00:04:12,060 --> 00:04:16,640
我们就来首先生成一个fail pass 生成我们一个所需要的

100
00:04:18,360 --> 00:04:24,680
生成我们自己指定的目录

101
00:04:24,680 --> 00:04:27,200
首先tmpdir是不是一个文件夹

102
00:04:27,200 --> 00:04:28,240
我们指定的文件夹

103
00:04:28,240 --> 00:04:29,580
那么我们要存储文件

104
00:04:29,580 --> 00:04:30,340
是不是还要指定文件名

105
00:04:30,340 --> 00:04:32,560
那么文件名是不是就是我们所上传的文件名了

106
00:04:32,560 --> 00:04:32,940
对吧

107
00:04:32,940 --> 00:04:33,340
好

108
00:04:33,340 --> 00:04:34,580
那么我们继续下一段

109
00:04:34,580 --> 00:04:35,840
不好意思

110
00:04:35,840 --> 00:04:36,780
我们继续下一段

111
00:04:36,780 --> 00:04:37,000
好

112
00:04:37,000 --> 00:04:39,080
下一行我们是不是走到一个reader

113
00:04:39,080 --> 00:04:39,800
reader这样一个变量

114
00:04:39,800 --> 00:04:40,540
它是什么呢

115
00:04:40,540 --> 00:04:42,420
fis.createreadstream

116
00:04:42,420 --> 00:04:44,340
我们刚才是不是看过stream它的文档

117
00:04:44,340 --> 00:04:45,480
其实呢

118
00:04:45,480 --> 00:04:48,340
createreadstream和咱们的createreadstream

119
00:04:48,340 --> 00:04:50,340
Dream它其实是一对

120
00:04:50,340 --> 00:04:54,340
它们分别是代表咱们nodejs文件里面的一个可读流和一个可写流

121
00:04:54,340 --> 00:04:55,340
咱们前面内容是不是讲过

122
00:04:55,340 --> 00:04:57,340
如果说忘记或者说

123
00:04:57,340 --> 00:05:00,340
遗忘的同学可以去翻阅一下文档或者前面的一些课程

124
00:05:00,340 --> 00:05:01,340
其实是非常简单的

125
00:05:01,340 --> 00:05:03,340
其实它做了一件什么事情呢

126
00:05:03,340 --> 00:05:04,340
Red是Dream

127
00:05:04,340 --> 00:05:05,340
其实很简单是不是

128
00:05:05,340 --> 00:05:06,340
读取文件

129
00:05:06,340 --> 00:05:12,340
并且把它存到一个变量中

130
00:05:12,340 --> 00:05:14,340
说白了是不是就把咱们所上传的文件

131
00:05:14,340 --> 00:05:15,340
读取出来然后存到内存中

132
00:05:15,340 --> 00:05:17,340
是不是存到Red这样一个

133
00:05:17,340 --> 00:05:20,660
是不是变量里面 然后呢 我们存储过之后

134
00:05:20,660 --> 00:05:26,390
我们是不是要去write他 此时呢 我们会创建一个可写流 可写流是不是需要去指定一个目录啊

135
00:05:26,390 --> 00:05:31,340
 我们要把它写到哪里去 对吧 我们写到哪里去了 是不是写到我们刚才所生成的这样一个fairpass里面去

136
00:05:31,340 --> 00:05:42,540
写入到定义 它其实是定义写入函数 写入到我们指定的目录 好 那么我们继续下一行

137
00:05:42,540 --> 00:05:47,100
read.piperread.piperwriter 其实呢 他呢 就是真正的去执行

138
00:05:47,100 --> 00:05:49,240
真正

139
00:05:49,240 --> 00:05:52,480
真正的去执行

140
00:05:52,480 --> 00:05:58,880
读写的过程 其实pipe它也是咱们nodejs可读流和可写流他们所结合使用的一个方法

141
00:05:58,880 --> 00:06:00,920
他们三个是不是可以称为什么呀

142
00:06:00,920 --> 00:06:03,480
咱们nodejs可读可写的三件课呀 谁也不开谁

143
00:06:03,480 --> 00:06:04,760
那么呢

144
00:06:04,760 --> 00:06:06,820
当咱们真正的去

145
00:06:06,820 --> 00:06:10,660
进行了一个读写 也就是说把咱们首先 首先把咱们是不是

146
00:06:10,660 --> 00:06:15,520
把咱们fails里面的一个目录把它给读取出来 然后呢去write 去写入到咱们指定的目录里面去

147
00:06:15,780 --> 00:06:17,060
那我们读写完成之后

148
00:06:17,060 --> 00:06:18,860
我们是不是要生成一个真正的路径呢

149
00:06:18,860 --> 00:06:20,640
咱们在fairpass里面

150
00:06:20,640 --> 00:06:22,180
就是咱们刚开始所定义的这样一个速度

151
00:06:22,180 --> 00:06:22,700
咱们把它给

152
00:06:22,700 --> 00:06:23,460
push进去

153
00:06:23,460 --> 00:06:24,740
然后呢通过context的点玻璃

154
00:06:24,740 --> 00:06:25,500
把它给返回

155
00:06:25,500 --> 00:06:27,040
是不是返回给用户的是什么呀

156
00:06:27,040 --> 00:06:28,840
是不是返回给咱们客户端的就是告诉他

157
00:06:28,840 --> 00:06:29,860
你这样一个文件

158
00:06:29,860 --> 00:06:30,380
在

159
00:06:30,380 --> 00:06:30,880
什么

160
00:06:30,880 --> 00:06:32,680
地址里面呢你就可以去访问他

161
00:06:32,680 --> 00:06:34,720
好那么我们就来看一下到底是不是怎么回事

162
00:06:34,720 --> 00:06:36,260
我们直接把所有的代码走完好

163
00:06:36,260 --> 00:06:38,300
那么我们来看一下postman里面的一个结果

164
00:06:38,300 --> 00:06:40,620
大家可以看到我们的response里面是不是返回一个数字

165
00:06:40,620 --> 00:06:41,640
里面有个字不串

166
00:06:41,640 --> 00:06:44,460
其实字幕串是不是就是咱们返回的一个文件存储的地址是什么呀

167
00:06:44,460 --> 00:06:45,740
那么我们就来看一下

168
00:06:45,740 --> 00:06:48,300
他到底在咱们系统中有没有这样的一个

169
00:06:48,300 --> 00:06:49,320
文件

170
00:06:49,320 --> 00:06:50,860
好我们来

171
00:06:50,860 --> 00:06:53,160
返回到这样一个目录把字幕串给拴掉

172
00:06:53,160 --> 00:06:55,720
好走

173
00:06:55,720 --> 00:06:56,740
大家可以看到

174
00:06:56,740 --> 00:07:00,840
是不是我们在系统的临时目录里面是不是有一个帅起来自己多怕这样一个图片

175
00:07:00,840 --> 00:07:01,600
而且呢

176
00:07:01,600 --> 00:07:02,880
我们桌面上面是不是也有

177
00:07:02,880 --> 00:07:03,660
对吧

178
00:07:03,660 --> 00:07:06,220
这里呢其实就已经完成了我们文件上传的一个逻辑

179
00:07:06,220 --> 00:07:08,780
其实也是比较简单的只是看起来比较复杂

180
00:07:08,780 --> 00:07:10,820
那我们就来总结一下文件上传的一个过程

181
00:07:11,080 --> 00:07:12,080
首先我们是不是会使用

182
00:07:12,080 --> 00:07:13,680
跨杠 卧底站一个中间键

183
00:07:13,680 --> 00:07:15,880
那么第一步呢 是不是创建一个临时目录

184
00:07:15,880 --> 00:07:16,980
然后

185
00:07:16,980 --> 00:07:20,080
然后是不是通过post 通过form data

186
00:07:20,080 --> 00:07:22,580
通过咱们form data的形式去上传咱们的一个文件

187
00:07:22,580 --> 00:07:26,380
然后去写入一个pipe流进行读写到咱们的一个指定目录就可以了

188
00:07:26,380 --> 00:07:28,420
好 这里呢就是我们这节课的内容

