1
00:00:00,000 --> 00:00:01,540
接下来我们来编写

2
00:00:01,540 --> 00:00:04,100
注册的接口

3
00:00:04,100 --> 00:00:06,660
那么注册的流程是什么样的

4
00:00:06,660 --> 00:00:11,000
注册核心是什么呀 是不是咱们需要把数据给存储到mongodp里面

5
00:00:11,000 --> 00:00:13,560
好 我们来看一下我们如何来编写注册这样一个

6
00:00:13,560 --> 00:00:14,600
接口

7
00:00:14,600 --> 00:00:21,760
好 这里首先我去定一个路由对吧

8
00:00:21,760 --> 00:00:23,040
注册的

9
00:00:23,040 --> 00:00:24,320
API

10
00:00:24,320 --> 00:00:26,880
接口 我们来定一个

11
00:00:26,880 --> 00:00:27,900
post

12
00:00:29,180 --> 00:00:30,980
api/sig

13
00:00:30,980 --> 00:00:35,320
好 那么呢 后面是不是要跟上controller 那么controller呢 咱们同样

14
00:00:35,320 --> 00:00:37,880
也在login这个模块里面 咱们的定义一个方法

15
00:00:37,880 --> 00:00:39,940
sync/sig

16
00:00:39,940 --> 00:00:42,500
好 那么我们的

17
00:00:42,500 --> 00:00:45,060
注册就需要去走到我们controller里面的一个

18
00:00:45,060 --> 00:00:45,560
zine

19
00:00:45,560 --> 00:00:48,640
好 接下来我们来看一下zine这样一个逻辑如何去编写

20
00:00:48,640 --> 00:00:51,960
注册逻辑呢

21
00:00:51,960 --> 00:00:54,260
主要分为两步 首先第一步

22
00:00:54,260 --> 00:00:55,540
我们要获取

23
00:00:55,540 --> 00:00:57,080
request的

24
00:00:57,860 --> 00:01:01,180
玻璃对吧 因为首先咱们要获取扣端所传递过来的一个用户名和密码

25
00:01:01,180 --> 00:01:01,960
第二步呢

26
00:01:01,960 --> 00:01:02,980
存储到

27
00:01:02,980 --> 00:01:04,260
mongo里面

28
00:01:04,260 --> 00:01:06,820
好 那么我们就来开始 首先呢 完成第一步

29
00:01:06,820 --> 00:01:10,140
首先呢 咱们要获取到咱们的一个

30
00:01:10,140 --> 00:01:11,420
context

31
00:01:11,420 --> 00:01:19,100
const 这里写错了 const

32
00:01:19,100 --> 00:01:22,700
好 那么我们获取了context之后 我们如何获取到咱们的一个玻璃呢

33
00:01:22,700 --> 00:01:24,480
其实呢 咱们可以通过context的点

34
00:01:24,480 --> 00:01:26,020
request的点

35
00:01:26,280 --> 00:01:29,720
那么这里呢其实就可以获取了我们那个password和咱们那个user name

36
00:01:29,720 --> 00:01:32,380
那么如果说对这块呢有所遗忘的话呢可以去看一下1GG的一个文档

37
00:01:32,380 --> 00:01:33,900
我们呢定一个变量把它给存起来

38
00:01:33,900 --> 00:01:36,180
比如说我们叫做request body等于它

39
00:01:36,180 --> 00:01:38,220
好那么到这里呢其实已经

40
00:01:38,220 --> 00:01:41,980
其实到这里呢已经获取到了咱们一个request的一个body

41
00:01:41,980 --> 00:01:43,180
好那么我们获取完成之后

42
00:01:43,180 --> 00:01:45,720
接下来是不是要开始去进行咱们一个存储工作呀

43
00:01:45,720 --> 00:01:47,480
好那么存储工作呢其实也非常简单

44
00:01:47,480 --> 00:01:55,120
我们waitcontext.model.user.insetmoney

45
00:01:55,120 --> 00:01:57,620
这里呢其实就是蒙口里面去插入数据的一个方法

46
00:01:57,620 --> 00:01:58,760
那么插入什么数据呢

47
00:01:58,760 --> 00:02:00,420
我们呢给数据库定义两个字段

48
00:02:00,420 --> 00:02:01,180
一个叫做user name

49
00:02:01,180 --> 00:02:03,300
一个呢叫做password

50
00:02:03,300 --> 00:02:06,100
它们分别呢都是通过body所传递过来的

51
00:02:06,100 --> 00:02:08,300
所以呢我们直接调用request.body.user name

52
00:02:08,300 --> 00:02:10,340
password呢也直接去调用它就可以了

53
00:02:10,340 --> 00:02:12,480
好那么这里呢其实就是

54
00:02:12,480 --> 00:02:14,280
那么其实这里呢就完成了我们的一个

55
00:02:14,280 --> 00:02:15,860
存储工作

56
00:02:15,860 --> 00:02:18,820
好那么到这里呢其实我们还

57
00:02:18,820 --> 00:02:20,020
缺少一步是什么呢

58
00:02:20,020 --> 00:02:20,980
我们的model其实还没有定义

59
00:02:20,980 --> 00:02:22,900
那么这里呢我们需要去定义一下user

60
00:02:22,900 --> 00:02:24,740
这样一个类它的一个

61
00:02:24,740 --> 00:02:28,240
model 方法 好 我们呢 此时的来定义一下

62
00:02:28,240 --> 00:02:29,540
他的model 在哪里呢

63
00:02:29,540 --> 00:02:32,940
我们呢 创建一个model这样的一个文件夹

64
00:02:32,940 --> 00:02:34,940
咱们呢 里面创建一个user

65
00:02:34,940 --> 00:02:37,540
那么model呢 其实这里呢 它是mongodb 里面的一个schema

66
00:02:37,540 --> 00:02:39,440
所以呢 这里的一个schema呢 我直接给copy过来

67
00:02:39,440 --> 00:02:40,440
好

68
00:02:40,440 --> 00:02:42,840
首先呢 我们定义一个schema 然后去new它

69
00:02:42,840 --> 00:02:44,540
咱们在数据库里面定义两个字段

70
00:02:44,540 --> 00:02:45,940
一个呢 是username 一个呢 是password

71
00:02:45,940 --> 00:02:48,240
然后咱们把它给定义为user

72
00:02:48,240 --> 00:02:51,440
好 那么这里呢 其实就完成了我们的一个schema的一个定义

73
00:02:51,440 --> 00:02:53,240
好 那么这块内容 如果说有不熟悉的同学呢

74
00:02:53,240 --> 00:02:54,620
我觉得可以去看一下mongodb的一个文档

75
00:02:54,620 --> 00:02:56,600
或者说看一下咱们前面学习mongodb的一个视频

76
00:02:56,600 --> 00:02:57,740
好 这里呢其实就

77
00:02:57,740 --> 00:03:01,120
彻底完成了咱们存储的一个过程

78
00:03:01,120 --> 00:03:03,020
好 我们来看一下

79
00:03:03,020 --> 00:03:07,060
接下来我们来验证一下咱们的一个代码能不能够走通

80
00:03:07,060 --> 00:03:11,420
我们来给它一个标识

81
00:03:11,420 --> 00:03:12,760
如果说注册成功的话 对吧

82
00:03:12,760 --> 00:03:15,420
h1注册成功

83
00:03:15,420 --> 00:03:19,100
而且目前的话我们代码里面是没有做一些融错措施的

84
00:03:19,100 --> 00:03:20,580
比如说前面这些代码发生错误怎么办

85
00:03:20,580 --> 00:03:22,720
其实这里呢我们在项目里面

86
00:03:22,720 --> 00:03:23,940
咱们暂时没有考虑这样一个问题

87
00:03:23,940 --> 00:03:26,120
我们来看一下效果

88
00:03:26,120 --> 00:03:26,800
注册

89
00:03:26,800 --> 00:03:31,540
我们来注册一个用户名

90
00:03:31,540 --> 00:03:34,060
比如说我这里取一个新的名称叫做

91
00:03:34,060 --> 00:03:36,400
传字F1

92
00:03:36,400 --> 00:03:37,340
传字前端

93
00:03:37,340 --> 00:03:38,560
密码123456

94
00:03:38,560 --> 00:03:39,320
点击注册

95
00:03:39,320 --> 00:03:40,700
大家可以发现已经注册成功了

96
00:03:40,700 --> 00:03:41,740
那么到底有没注册成功呢

97
00:03:41,740 --> 00:03:43,180
咱们去数据库里面去看一下就可以了

98
00:03:43,180 --> 00:03:44,460
我们来刷新一下

99
00:03:44,460 --> 00:03:45,520
大家可以看到

100
00:03:45,520 --> 00:03:47,500
我们的数据库里面是不是已经存储了

101
00:03:47,500 --> 00:03:48,660
传字F1

102
00:03:48,660 --> 00:03:49,740
密码123456

103
00:03:49,740 --> 00:03:51,560
那么这样一个过程其实也是非常的简单

104
00:03:51,560 --> 00:03:56,420
好 那么到这里呢 其实我们已经完成了注册接口的编写 那么注册其实主要分为两部

105
00:03:56,420 --> 00:03:56,800
 获取玻璃

106
00:03:56,800 --> 00:04:00,960
然后第二个呢 存储到咱们那个门口里面 好 接下来我们来看一下如何去进行一个登录

107
00:04:00,960 --> 00:04:08,400
登录接口 好 那么我们的登录接口呢 同样的 我们的也是Post 请求

108
00:04:08,400 --> 00:04:10,880
请求名称呢

109
00:04:10,880 --> 00:04:12,920
API

110
00:04:12,920 --> 00:04:14,120
login

111
00:04:14,120 --> 00:04:19,880
好 那么login它的逻辑在哪里去编写呢 咱们同样的 在login.js里面咱们去定义一个

112
00:04:20,360 --> 00:04:21,860
sync login这样一个方法

113
00:04:21,860 --> 00:04:22,780
代表了我们去完成

114
00:04:22,780 --> 00:04:23,480
我们的一个登录逻辑

115
00:04:23,480 --> 00:04:28,480
好 那么登录

116
00:04:28,480 --> 00:04:30,600
主要经历哪些步骤呢

117
00:04:30,600 --> 00:04:31,360
大家思考一下

118
00:04:31,360 --> 00:04:33,580
登录需要经历哪些步骤

119
00:04:33,580 --> 00:04:36,960
我们还是来看一张流程图

120
00:04:36,960 --> 00:04:37,660
首先呢

121
00:04:37,660 --> 00:04:38,320
我们用户房登录

122
00:04:38,320 --> 00:04:38,960
这里呢

123
00:04:38,960 --> 00:04:39,760
是不是需要去验证

124
00:04:39,760 --> 00:04:40,880
我们的用户名和密码进行校验

125
00:04:40,880 --> 00:04:41,320
对吧

126
00:04:41,320 --> 00:04:41,840
所以说呢

127
00:04:41,840 --> 00:04:43,060
登录一定有一个校验的过程

128
00:04:43,060 --> 00:04:44,240
那么教验完成之后呢

129
00:04:44,240 --> 00:04:45,680
需要在redis里面去存储一个token

130
00:04:45,680 --> 00:04:46,760
好 那么我们就来看一下

131
00:04:46,760 --> 00:04:47,900
如何去完成这个过程

132
00:04:47,900 --> 00:04:49,640
好

133
00:04:49,640 --> 00:04:51,640
首先呢 其实我们第一步和上面是一样的

134
00:04:51,640 --> 00:04:53,640
我们是不是要获取request一个body啊

135
00:04:53,640 --> 00:04:56,640
也就是你登录的时候是不是要获取用户传递过来的一个用户信息

136
00:04:56,640 --> 00:04:57,640
那么第二步呢

137
00:04:57,640 --> 00:04:59,640
是不是需要去校验呢

138
00:04:59,640 --> 00:05:03,640
校验

139
00:05:03,640 --> 00:05:05,640
那么第三步

140
00:05:05,640 --> 00:05:09,640
也就是对咱们的一个redis进行存储 对吧

141
00:05:09,640 --> 00:05:11,640
存储

142
00:05:11,640 --> 00:05:17,640
这么描述吧 将session和redis进行同步

143
00:05:17,640 --> 00:05:18,640
好 我们就来看一下这样一个过程

144
00:05:18,640 --> 00:05:22,680
那么获取玻璃的过程和咱们的一个注册是一样的

145
00:05:22,680 --> 00:05:25,020
所以说我们直接把代码给copy过来就可以了

146
00:05:25,020 --> 00:05:27,780
好 这里我们已经把代码给copy过来了

147
00:05:27,780 --> 00:05:28,820
我们获取到了玻璃

148
00:05:28,820 --> 00:05:30,620
那么获取到玻璃之后我们需要去教验

149
00:05:30,620 --> 00:05:33,000
那么教育之前你是不是首先需要去数据库里面去

150
00:05:33,000 --> 00:05:35,280
需要去数据库里面去查询了对吧

151
00:05:35,280 --> 00:05:37,520
因为你要在数据库去查

152
00:05:37,520 --> 00:05:39,020
用户名和密码是不是能够匹配

153
00:05:39,020 --> 00:05:41,580
所以说这里呢涉及到一个查询数据库的一个过程

154
00:05:41,580 --> 00:05:43,920
比如说我们去conc的一个

155
00:05:43,920 --> 00:05:46,640
犯的user等于什么呢

156
00:05:46,640 --> 00:05:53,100
awaitcontest.model.user.find

157
00:05:53,100 --> 00:05:55,840
这里呢其实也是

158
00:05:55,840 --> 00:05:58,000
也是MongoDB它的一个

159
00:05:58,000 --> 00:05:59,040
淘汰了一篇

160
00:05:59,040 --> 00:06:01,120
咱们只需要去使用find就可以去查询到咱们这一条数据

161
00:06:01,120 --> 00:06:01,860
那么我们find的什么呢

162
00:06:01,860 --> 00:06:04,260
我们直接去find的数据库里面的字段

163
00:06:04,260 --> 00:06:07,880
为user name它的值和我们request的body

164
00:06:07,880 --> 00:06:11,020
的user name相同的那条数据

165
00:06:11,020 --> 00:06:13,680
好

166
00:06:13,680 --> 00:06:16,380
这里呢咱们就把数据给查询出来了

167
00:06:16,380 --> 00:06:17,020
什么意思呢

168
00:06:17,020 --> 00:06:18,700
这里给同学们稍微的简单的解释一下

169
00:06:18,700 --> 00:06:22,200
比如说我们输入的一个用户名秘密码

170
00:06:22,200 --> 00:06:23,020
那么用户名是什么呢

171
00:06:23,020 --> 00:06:24,120
用户名是传字F1

172
00:06:24,120 --> 00:06:25,520
那么我们刚才通过犯的user

173
00:06:25,520 --> 00:06:26,340
犯的是谁

174
00:06:26,340 --> 00:06:27,820
是不是犯的user name等于传字F1

175
00:06:27,820 --> 00:06:28,420
传字F1

176
00:06:28,420 --> 00:06:31,200
那么查询出来的数据是不是会把这样一个整个对象给我们返回

177
00:06:31,200 --> 00:06:32,760
那么我们是不是就可以拿到password

178
00:06:32,760 --> 00:06:35,440
然后和咱们浏览器传递过来一个密码进行对比

179
00:06:35,440 --> 00:06:37,160
如果说一样说明了它登录成功了

180
00:06:37,160 --> 00:06:37,720
好

181
00:06:37,720 --> 00:06:41,160
我们来继续完成咱们下面的逻辑

182
00:06:41,160 --> 00:06:42,780
我们查询到用户之后

183
00:06:42,780 --> 00:06:45,020
我们这里是不是涉及到一个教验的过程了

184
00:06:45,020 --> 00:06:45,240
对吧

185
00:06:45,240 --> 00:06:47,720
这里呢是数据库查询

186
00:06:47,720 --> 00:06:50,360
那么我们来看一下教验怎么写

187
00:06:50,360 --> 00:06:51,460
其实教验呢也非常的简单

188
00:06:51,460 --> 00:06:53,180
比如说呢因为FindUser呢

189
00:06:53,180 --> 00:06:54,420
它呢会返回一个速度

190
00:06:54,420 --> 00:06:56,120
所以说呢我们如何去写呢

191
00:06:56,120 --> 00:06:56,380
if

192
00:06:56,380 --> 00:07:00,460
FindUser.nense

193
00:07:00,460 --> 00:07:03,540
如果说你能查到数据

194
00:07:03,540 --> 00:07:04,180
那肯定有长度

195
00:07:04,180 --> 00:07:05,200
那么如果说数据都没查到

196
00:07:05,200 --> 00:07:05,700
肯定没有长度

197
00:07:05,700 --> 00:07:06,540
所以说呢这样就不通过了

198
00:07:06,540 --> 00:07:07,160
对吧

199
00:07:07,160 --> 00:07:07,980
好

200
00:07:07,980 --> 00:07:09,580
那么我们查到数据

201
00:07:09,580 --> 00:07:11,440
那么你的密码是不是不一定

202
00:07:11,440 --> 00:07:12,300
是正确的

203
00:07:12,300 --> 00:07:14,700
所以说咱们还需要去加上再一段逻辑

204
00:07:14,700 --> 00:07:16,900
犯了user0也就是获取到咱们这样一个对象

205
00:07:16,900 --> 00:07:17,900
找到他的password

206
00:07:17,900 --> 00:07:20,200
如果说能够和我们的一个谁呢

207
00:07:20,200 --> 00:07:23,800
和我们request body.password

208
00:07:23,800 --> 00:07:25,300
如果说能够相同的话说明什么问题

209
00:07:25,300 --> 00:07:27,400
说明咱们用户名和密码是不是已经输入正确

210
00:07:27,400 --> 00:07:28,100
对吧

211
00:07:28,100 --> 00:07:29,700
好 那么接下来在这一段逻辑里面

212
00:07:29,700 --> 00:07:33,300
我们就要进行session和redis的一个同步

213
00:07:33,300 --> 00:07:34,000
对吧

214
00:07:34,000 --> 00:07:35,600
那么如果说你没有输入正确

215
00:07:35,600 --> 00:07:37,900
此时我们去调用一个redirect

216
00:07:37,900 --> 00:07:38,900
也就是重定向

217
00:07:38,900 --> 00:07:40,400
咱们把你定向到登陆界面

218
00:07:40,400 --> 00:07:41,300
你需要去重新登陆一下

219
00:07:41,300 --> 00:07:42,200
因为你输入的是错误的

220
00:07:42,200 --> 00:07:45,460
那么包括如果说你一个完善的网站是不是还需要给用户一些信息啊对吧

221
00:07:45,460 --> 00:07:47,440
比如说用户名错误或者密码错误咱们这里呢

222
00:07:47,440 --> 00:07:49,680
项目很粗糙就不去搞了些细节了

223
00:07:49,680 --> 00:07:52,920
我们来看一下如何将session和radis进行一个同步

224
00:07:52,920 --> 00:07:54,720
首先呢我们来看一下同步的一个目标是什么

225
00:07:54,720 --> 00:07:58,460
同步的目标呢咱们其实就是需要把session存在咱们的radis里面存成这样一种数据结构

226
00:07:58,460 --> 00:08:00,060
好我们来看一下如何去做

227
00:08:00,060 --> 00:08:02,680
其实呢非常的神奇

228
00:08:02,680 --> 00:08:06,220
大家可以看到我们将session和radis进行同步呢只需要一行代码就够了

229
00:08:06,220 --> 00:08:10,060
只需要一行代码就够了

230
00:08:10,060 --> 00:08:12,000
为什么只需要一行代码就够的呢

231
00:08:12,000 --> 00:08:15,200
其实就是因为咱们最开始所介绍的这样一个插件

232
00:08:15,200 --> 00:08:16,440
叫做1gg-session

233
00:08:16,440 --> 00:08:18,800
它是专门进行咱们session和redis一个同步

234
00:08:18,800 --> 00:08:19,340
好

235
00:08:19,340 --> 00:08:20,720
那么这里呢

236
00:08:20,720 --> 00:08:22,760
我们就把它给引入到咱们的一个项目中

237
00:08:22,760 --> 00:08:24,000
因为我们刚才已经安装了依赖

238
00:08:24,000 --> 00:08:25,780
但是还没有对app.js进行一个配置

239
00:08:25,780 --> 00:08:27,320
我们只要把它给copy过来

240
00:08:27,320 --> 00:08:32,400
好

241
00:08:32,400 --> 00:08:35,680
我去创建一个app.js

242
00:08:35,680 --> 00:08:36,240
好

243
00:08:36,240 --> 00:08:38,360
我们把它把这段代码直接给copy过来

244
00:08:38,360 --> 00:08:40,080
那么其实这样一段代码的作用是什么呢

245
00:08:40,080 --> 00:08:41,640
我们在最开始的时候

246
00:08:41,640 --> 00:08:42,920
是不是已经给同学们去解释了呀

247
00:08:42,920 --> 00:08:43,440
对吧

248
00:08:43,440 --> 00:08:45,480
好了到这里了我继续给同学们呢

249
00:08:45,480 --> 00:08:46,500
再简单的解释一遍

250
00:08:46,500 --> 00:08:47,780
我们一行代码都不用敢

251
00:08:47,780 --> 00:08:49,320
直接把官方的它的一个例子给扣过来

252
00:08:49,320 --> 00:08:50,340
咱们就完全可以使用

253
00:08:50,340 --> 00:08:52,140
因为这样的代码已经非常具有代表性

254
00:08:52,140 --> 00:08:53,160
也非常具有通用性

255
00:08:53,160 --> 00:08:54,700
首先呢我们去调用

256
00:08:54,700 --> 00:08:56,240
调咱们去读取session的时候

257
00:08:56,240 --> 00:08:57,520
因为中间是一个单一的过程

258
00:08:57,520 --> 00:08:58,540
我们去读取session的时候

259
00:08:58,540 --> 00:08:59,040
其实呢

260
00:08:59,040 --> 00:09:00,320
就是去同radis面去读取

261
00:09:00,320 --> 00:09:01,100
如果说后取到信息了

262
00:09:01,100 --> 00:09:03,140
咱们就会去return一个序列化之后的一个结果

263
00:09:03,140 --> 00:09:05,200
那么如果说你对session进行了一个set

264
00:09:05,200 --> 00:09:09,000
同样的我们也会对redis进行一个set 也就是对咱们redis数据库进行一个存储

265
00:09:09,000 --> 00:09:12,800
那么如果说你把session给拴掉 那么redis这样一条数据也会给拴掉

266
00:09:12,800 --> 00:09:15,800
好 接下来我们来看一下在那一行代码如何去写

267
00:09:15,800 --> 00:09:20,300
其实非常的简单 我们只需要去context.session

268
00:09:20,300 --> 00:09:25,700
我们呢context.session其实它是一机记里面原生给我们提供的一个方法

269
00:09:25,700 --> 00:09:28,700
我们只需要去修改它就可以去修改我们的session 比如说呢 我们去给它订一个lobbing

270
00:09:28,700 --> 00:09:30,200
 订一个chew

271
00:09:30,200 --> 00:09:32,500
好 这样其实就完成了咱们redis和session的一个同步

272
00:09:33,300 --> 00:09:38,500
非常的神奇 我们我们来分析一下他发生了一件什么事情 比如说我们去给session

273
00:09:38,500 --> 00:09:42,740
定义了一个locking的属性 并且给他负责了为q 那么此时会调用sat的方法

274
00:09:42,740 --> 00:09:45,000
 对吧 那么调用sat的方法之后 大家可以看到我们的k是什么

275
00:09:45,000 --> 00:09:53,500
那么这里呢 给大家去注视一下k是这个插件默认注入的token 所以说这样一个k我们不需要去管

276
00:09:53,500 --> 00:09:54,800
 你比如说我们去登录之后

277
00:09:54,800 --> 00:09:59,100
这样一个插件会自动的 自动的根据你当前的一个用户信息去生成一个在你的token

278
00:09:59,100 --> 00:10:00,800
 那么所以说k呢是自动生成的

279
00:10:03,300 --> 00:10:05,100
那么我们生成K之后

280
00:10:05,100 --> 00:10:08,680
这里就是一个Value,Value是什么呢?Value其实就是你刚才所传递过来的

281
00:10:08,680 --> 00:10:10,980
Login v2这样一个对象

282
00:10:10,980 --> 00:10:12,260
那么传递过来之后呢

283
00:10:12,260 --> 00:10:19,180
此时就会App.redis.set,也就是在redis里面会set一条数据,也就是会存储的数据,K就是咱们的一个Token,Value就是你的

284
00:10:19,180 --> 00:10:24,040
Login v2这样一个对象,然后他还会带里面去给一些过期时间等等一些额外的他

285
00:10:24,040 --> 00:10:25,580
插件所定制的一些信息

286
00:10:25,580 --> 00:10:28,640
那么这里就是我们这行代码所发生的一些

287
00:10:28,640 --> 00:10:29,160
事情

288
00:10:30,700 --> 00:10:33,520
那么完成了这样一步之后 我们只需要给咱们的一个

289
00:10:33,520 --> 00:10:37,620
咱们只需要再重新呢 再重定向到我们的一个

290
00:10:37,620 --> 00:10:43,490
哪里呢 重定向到我们的一个首页 因为我们一般登陆完成之后 都会重定向到咱们的一个首页

291
00:10:43,490 --> 00:10:43,500
 对吧

292
00:10:43,500 --> 00:10:45,300
好 我们此时来看一下效果

293
00:10:45,300 --> 00:10:52,460
好 首先呢 我们刚才是不是已经注册了一个船字f1 对吧 此时呢 咱们对它进行登陆来看一下效果

294
00:10:52,460 --> 00:10:54,000
OK

295
00:10:54,000 --> 00:10:57,320
好 为了方便也是呢 我们看一下 application 里面其实是没有

296
00:10:57,580 --> 00:10:58,780
说明了咱们之前没有登录过

297
00:10:58,780 --> 00:11:00,480
好 我们来访问一下传置F1

298
00:11:00,480 --> 00:11:01,980
密码给他一个123456

299
00:11:01,980 --> 00:11:02,680
我们点击登录

300
00:11:02,680 --> 00:11:03,680
好 大家可以看到

301
00:11:03,680 --> 00:11:06,080
其实我们已经进入了咱们的一个登录的页面

302
00:11:06,080 --> 00:11:07,480
我们来看一下我们的application

303
00:11:07,480 --> 00:11:08,480
咱们刷新一下

304
00:11:08,480 --> 00:11:09,980
大家可以看到我们是不是有个session

305
00:11:09,980 --> 00:11:11,880
对吧 咱们是不是在页面中写了一个session

306
00:11:11,880 --> 00:11:14,580
eggsesss这里其实是插件所定义的一个内容

307
00:11:14,580 --> 00:11:15,780
value其实就是我们的token

308
00:11:15,780 --> 00:11:17,180
代表我们的用户信息

309
00:11:17,180 --> 00:11:19,380
也就是这个value代表你是谁

310
00:11:19,380 --> 00:11:23,080
那么它开头的是74d5a1

311
00:11:23,080 --> 00:11:25,380
好 我们此时来redis数据库里面去看一下

312
00:11:25,380 --> 00:11:26,180
有没有这样一条数据

313
00:11:27,580 --> 00:11:30,480
好 我们来刷新一下

314
00:11:30,480 --> 00:11:30,920
大家可以看到

315
00:11:30,920 --> 00:11:32,160
这样一条数据

316
00:11:32,160 --> 00:11:33,000
其实D5AE

317
00:11:33,000 --> 00:11:34,460
其实是不是已经给写入进来了

318
00:11:34,460 --> 00:11:35,980
说明了咱们Redis和Sessy

319
00:11:35,980 --> 00:11:36,880
其实已经同步成功了

320
00:11:36,880 --> 00:11:37,600
这里呢

321
00:11:37,600 --> 00:11:38,360
其实我们已经完成了

322
00:11:38,360 --> 00:11:39,740
登录和注册的这样的两个接口

323
00:11:39,740 --> 00:11:40,520
那么接下来呢

324
00:11:40,520 --> 00:11:42,060
我们就来完成剩下的一个逻辑

325
00:11:42,060 --> 00:11:43,760
好 我们先把视频给停一下

