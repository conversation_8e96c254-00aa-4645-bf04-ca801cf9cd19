1
00:00:00,000 --> 00:00:01,620
好,刚才我们是不是讲到了

2
00:00:01,620 --> 00:00:03,300
我们插件的引入有两种方式

3
00:00:03,300 --> 00:00:04,760
一种是npm,一种是在我们本地

4
00:00:04,760 --> 00:00:06,140
那么我们在本地来写一个插件

5
00:00:06,140 --> 00:00:07,820
看一下到底怎么去写

6
00:00:07,820 --> 00:00:09,480
首先这里呢有一行命令

7
00:00:09,480 --> 00:00:11,320
我们通过npm init 1gg

8
00:00:11,320 --> 00:00:13,240
然后我们把type指定为一个plugin

9
00:00:13,240 --> 00:00:15,840
那么那自动就会帮我们去生成一个写插件的游入

10
00:00:15,840 --> 00:00:16,880
那么首先

11
00:00:16,880 --> 00:00:20,740
我们在app下面去创建一个文件夹叫做lib

12
00:00:20,740 --> 00:00:22,880
然后呢,lib下面呢

13
00:00:22,880 --> 00:00:25,620
有一个plugin的文件夹

14
00:00:25,620 --> 00:00:27,380
然后我们再来创建文件夹

15
00:00:27,380 --> 00:00:28,200
1gg-a

16
00:00:28,200 --> 00:00:29,800
你比如说我们现在有一个插件

17
00:00:29,800 --> 00:00:31,020
就叫做一GG-A

18
00:00:31,020 --> 00:00:32,200
那么这里我为什么-A

19
00:00:32,200 --> 00:00:33,880
因为接下来要给同学们去演示一下

20
00:00:33,880 --> 00:00:35,500
我们插件之间的一个依赖关系

21
00:00:35,500 --> 00:00:36,820
所以我们再去命名

22
00:00:36,820 --> 00:00:38,320
好 我们先进入这样一个目录

23
00:00:38,320 --> 00:00:45,140
我们去cd app leave plugin app一GG-A

24
00:00:45,140 --> 00:00:48,540
好 我们来执行一下npm init一GG-type等于plugin

25
00:00:48,540 --> 00:00:50,200
我们稍等一下

26
00:00:50,200 --> 00:00:52,600
好 这里呢 其实

27
00:00:52,600 --> 00:00:54,400
我们执行完再一条命令之后呢

28
00:00:54,400 --> 00:00:55,420
就会生成一个这样的目录

29
00:00:55,420 --> 00:00:58,480
其实大家可以看到我们刚才是不是已经讲过了

30
00:00:58,480 --> 00:01:02,160
其实它生成的目录和我们的一GG应用是非常类似的

31
00:01:02,160 --> 00:01:02,760
除了没有什么呀

32
00:01:02,760 --> 00:01:04,340
除了没有我们的一个Rooter和Service

33
00:01:04,340 --> 00:01:06,020
其实其他的东西都是有的

34
00:01:06,020 --> 00:01:07,060
那么还有一个东西也没有

35
00:01:07,060 --> 00:01:07,480
是什么呢

36
00:01:07,480 --> 00:01:08,260
Plugin.js

37
00:01:08,260 --> 00:01:08,780
为什么呀

38
00:01:08,780 --> 00:01:10,060
因为它自己本身就是一个插件

39
00:01:10,060 --> 00:01:11,720
你还去引入Plugin.js

40
00:01:11,720 --> 00:01:12,740
那你定于谁是插件

41
00:01:12,740 --> 00:01:13,580
那么你定于谁呢

42
00:01:13,580 --> 00:01:13,800
对吧

43
00:01:13,800 --> 00:01:14,780
好

44
00:01:14,780 --> 00:01:16,040
那么我们来看一下装好了没有

45
00:01:16,040 --> 00:01:16,660
好

46
00:01:16,660 --> 00:01:17,580
这里呢稍微有点慢

47
00:01:17,580 --> 00:01:18,660
没关系

48
00:01:18,660 --> 00:01:19,740
那么我们接下来继续看一下

49
00:01:19,740 --> 00:01:20,920
那么我们的插件

50
00:01:20,920 --> 00:01:22,600
为什么没有独立的Rooter和Country了

51
00:01:22,600 --> 00:01:23,100
这里呢

52
00:01:23,100 --> 00:01:24,740
主要去基于几点去考虑

53
00:01:24,740 --> 00:01:25,700
首先第一个呢

54
00:01:25,700 --> 00:01:27,560
路由一般和应用强绑力不具备通用性

55
00:01:27,560 --> 00:01:28,140
第二个

56
00:01:28,140 --> 00:01:29,660
那么一个应用可能依赖多个插件

57
00:01:29,660 --> 00:01:31,000
那么如果插件支持路由

58
00:01:31,000 --> 00:01:32,740
可能会导致路由的一个冲突

59
00:01:32,740 --> 00:01:33,080
对吧

60
00:01:33,080 --> 00:01:33,880
而且

61
00:01:33,880 --> 00:01:37,860
而且如果说你确实有统一路由的一个需求

62
00:01:37,860 --> 00:01:41,460
你也可以考虑在插件去通过中间键来实现

63
00:01:41,460 --> 00:01:41,920
你并没有

64
00:01:41,920 --> 00:01:43,200
你并没有必要

65
00:01:43,200 --> 00:01:44,700
一定要去写Root和Ctrl

66
00:01:44,700 --> 00:01:45,580
好

67
00:01:45,580 --> 00:01:47,000
我们再来看一下有没有安装好

68
00:01:47,000 --> 00:01:47,180
好

69
00:01:47,180 --> 00:01:48,840
现在其实已经装好了

70
00:01:48,840 --> 00:01:49,780
好

71
00:01:49,780 --> 00:01:50,620
我们其实这里

72
00:01:50,620 --> 00:01:51,640
我们其实可以这里看到

73
00:01:51,640 --> 00:01:52,660
我们生成了这样一个目录

74
00:01:52,660 --> 00:01:54,740
那么我们做一个什么事情呢

75
00:01:54,740 --> 00:01:56,280
我们做一个什么事情

76
00:01:56,280 --> 00:01:57,940
我们首先创建一个APP这样的文件夹

77
00:01:57,940 --> 00:01:59,540
然后创建一个extend

78
00:01:59,540 --> 00:02:00,360
extend是做什么的

79
00:02:00,360 --> 00:02:02,200
extend是不是可以给我们的实力

80
00:02:02,200 --> 00:02:03,340
比如说我们的APP实力

81
00:02:03,340 --> 00:02:05,480
或者说context去添加一些属性或者方法

82
00:02:05,480 --> 00:02:07,680
这里呢我们去给context

83
00:02:07,680 --> 00:02:10,600
我们去给context来扩展一些方法

84
00:02:10,600 --> 00:02:11,240
我们来试一下

85
00:02:11,240 --> 00:02:13,720
比如说我们之前在APP里面是不是也扩展过呀

86
00:02:13,720 --> 00:02:15,680
我们是不是扩展了一个iOS这样的方法

87
00:02:15,680 --> 00:02:18,060
那么我们在插件里面同样的也去扩展一个方法

88
00:02:18,060 --> 00:02:18,980
我们呢叫做

89
00:02:18,980 --> 00:02:20,200
叫做什么呢

90
00:02:20,200 --> 00:02:20,860
我们就叫做

91
00:02:20,860 --> 00:02:24,040
我是插件A

92
00:02:24,040 --> 00:02:29,160
好 这里呢 我们就在context里面是不是去扩展了一个plugin a这样的一个属性

93
00:02:29,160 --> 00:02:31,720
好 那么我们呢 我们来看一下package.accent

94
00:02:31,720 --> 00:02:33,760
其实当我们去enit的时候 帮我们已经

95
00:02:33,760 --> 00:02:37,600
是不是帮我们去自动的去写入了一些name 包括了egg plugin

96
00:02:37,600 --> 00:02:39,140
这里呢 希望同学们去注意一下

97
00:02:39,140 --> 00:02:43,760
我们在写一个插件的时候 你呢 需要在package.accent里面

98
00:02:43,760 --> 00:02:46,820
去加入一些当前插件的一些信息

99
00:02:46,820 --> 00:02:50,400
你比如说我们是不是要添加一个egg plugin这样一个字段

100
00:02:50,400 --> 00:02:54,000
name呢 就是你插件那个名称 比如说我们刚才是不是创建了一个name叫做egg-a

101
00:02:54,000 --> 00:02:54,680
那么你的内容呢

102
00:02:54,680 --> 00:02:55,540
其实就是A

103
00:02:55,540 --> 00:02:57,980
那么dependence和optionaldependence是什么

104
00:02:57,980 --> 00:02:59,780
也就是说我们的插件依赖于什么

105
00:02:59,780 --> 00:03:02,020
那么dependence和optionaldependence

106
00:03:02,020 --> 00:03:02,660
它们有什么区别

107
00:03:02,660 --> 00:03:03,860
我们来看一下解释

108
00:03:03,860 --> 00:03:04,600
这里呢

109
00:03:04,600 --> 00:03:05,580
它是插件的一个强依赖

110
00:03:05,580 --> 00:03:07,000
如果说你的依赖没有找到

111
00:03:07,000 --> 00:03:07,680
这形容失败

112
00:03:07,680 --> 00:03:09,480
这样一个optionaldependence呢

113
00:03:09,480 --> 00:03:10,680
说明了它是一个可选的

114
00:03:10,680 --> 00:03:12,040
你如果说依赖的插件没有开启

115
00:03:12,040 --> 00:03:12,640
或者说没有找到

116
00:03:12,640 --> 00:03:13,320
它呢不会报错

117
00:03:13,320 --> 00:03:14,780
只会给你一个警告

118
00:03:14,780 --> 00:03:16,320
你的插件没有找到

119
00:03:16,320 --> 00:03:17,880
这里呢就是他们的一个区别

120
00:03:17,880 --> 00:03:18,660
那么env呢

121
00:03:18,660 --> 00:03:19,620
env就是环境

122
00:03:19,620 --> 00:03:21,240
你比如说你去引入一个插件的时候

123
00:03:21,240 --> 00:03:22,620
它会自动的帮你去选择环境

124
00:03:22,620 --> 00:03:23,840
你比如说我们需要在DV

125
00:03:23,840 --> 00:03:25,220
或者说咱们的生产环境

126
00:03:25,220 --> 00:03:27,820
其实这里一般都不需要去配置

127
00:03:27,820 --> 00:03:29,540
好 我们来

128
00:03:29,540 --> 00:03:31,280
那么我们就来看一下我们的package

129
00:03:31,280 --> 00:03:35,740
好 这里呢

130
00:03:35,740 --> 00:03:37,040
其实我们是不是指定了一个字

131
00:03:37,040 --> 00:03:38,480
EGG plugin name A

132
00:03:38,480 --> 00:03:39,140
就可以了

133
00:03:39,140 --> 00:03:41,280
因为它暂时是不是不需要依赖

134
00:03:41,280 --> 00:03:42,460
任何的一个插件

135
00:03:42,460 --> 00:03:42,800
对吧

136
00:03:42,800 --> 00:03:43,620
好 这里呢

137
00:03:43,620 --> 00:03:44,060
我们来

138
00:03:44,060 --> 00:03:44,960
这里首先呢

139
00:03:44,960 --> 00:03:46,780
我们在APP里面去引入

140
00:03:46,780 --> 00:03:47,220
看一下

141
00:03:47,220 --> 00:03:50,240
我们的plugin A到底能不能够去生效

142
00:03:50,240 --> 00:03:53,360
首先我们在APP里面

143
00:03:53,360 --> 00:03:54,720
是不是要找到我们的一个

144
00:03:54,720 --> 00:03:56,020
找到一个什么呀

145
00:03:56,020 --> 00:03:58,780
是不是找到我们的config里面有个plugin.js

146
00:03:58,780 --> 00:03:59,720
此时呢

147
00:03:59,720 --> 00:04:01,420
我们是不是需要去引入a

148
00:04:01,420 --> 00:04:02,660
enable to package

149
00:04:02,660 --> 00:04:03,640
是什么1gg-a

150
00:04:03,640 --> 00:04:03,960
对吧

151
00:04:03,960 --> 00:04:04,680
为什么呀

152
00:04:04,680 --> 00:04:05,940
因为我们的neb里面是不是有一个

153
00:04:05,940 --> 00:04:06,960
有一个什么

154
00:04:06,960 --> 00:04:08,440
是不是有一个1gg-a

155
00:04:08,440 --> 00:04:09,820
所以我们这里package叫它

156
00:04:09,820 --> 00:04:10,400
那么这里呢

157
00:04:10,400 --> 00:04:11,480
因为我们是在本地写的一个插件

158
00:04:11,480 --> 00:04:12,560
你不是在npm里面

159
00:04:12,560 --> 00:04:14,080
所以我们需要去指定pass

160
00:04:14,080 --> 00:04:14,840
那么pass呢

161
00:04:14,840 --> 00:04:16,840
它就是在我们的nebplugin里面的1gg-a

162
00:04:16,840 --> 00:04:17,480
好

163
00:04:17,480 --> 00:04:19,340
那么为了给方便同学们演示呢

164
00:04:19,340 --> 00:04:21,380
我们在插件里面去定义一个APP.js

165
00:04:21,380 --> 00:04:23,300
他可以做什么呀 我们之前是不是讲过

166
00:04:23,300 --> 00:04:28,300
App.js是不是可以在我们项目启动的时候去监听一些消息啊 对吧

167
00:04:28,300 --> 00:04:30,340
我们来看一下我们刚才的例子

168
00:04:30,340 --> 00:04:33,160
我们在APP里面 我们的应用里面是不是监听了一些事件

169
00:04:33,160 --> 00:04:34,440
然后去打印了一些东西

170
00:04:34,440 --> 00:04:37,520
那么我们在插件里面也去也去写一下 然后把它给打印出来

171
00:04:37,520 --> 00:04:40,080
我们就在

172
00:04:40,080 --> 00:04:42,120
上个里面我们去打印一下

173
00:04:42,120 --> 00:04:45,460
A服务启动完毕 也就是咱们的插件 对吧 比如说我们的

174
00:04:45,460 --> 00:04:46,980
A插件

175
00:04:46,980 --> 00:04:48,520
加载

176
00:04:49,340 --> 00:04:54,200
好 那么我们来run一下npm run dv

177
00:04:54,200 --> 00:04:57,280
好 我要退出到课目录 好 我们

178
00:04:57,280 --> 00:05:01,620
好 我们再来run一下

179
00:05:01,620 --> 00:05:09,820
好 大家可以看到我们的pocket.egg plugin is missing in

180
00:05:09,820 --> 00:05:15,460
大家可以看到这里呢 报了一个错 说明了我们的插件呢

181
00:05:15,460 --> 00:05:16,740
没有找到

182
00:05:17,240 --> 00:05:19,680
那么为什么没有找到 我们一起来找一下原因 大不要着急

183
00:05:19,680 --> 00:05:25,800
好 其实这里呢是我们的pass写错了 我们这里的少一个app 那么大家可以看到

184
00:05:25,800 --> 00:05:27,400
大家可以看到我们重启一下

185
00:05:27,400 --> 00:05:33,320
大家可以看到这里是不是a插件已经加载了 说明我们的插件是不是已经加载成功了

186
00:05:33,320 --> 00:05:37,840
那么我们来 那么我们在应用里面来访问一下plugin a在这个属性它到底有没有

187
00:05:37,840 --> 00:05:42,320
我们来进入咱们的一个控制器 那么呢还是进入我们的一个home

188
00:05:42,320 --> 00:05:46,740
那么在这里呢 我们直接把它给打印出来看一下 比如说我们去访问一下context的点什么呀

189
00:05:46,740 --> 00:05:46,880
 是不是

190
00:05:47,040 --> 00:05:49,520
点pluginA 我们来看一下它到底是什么

191
00:05:49,520 --> 00:05:57,060
好 大家可以看到 undefined

192
00:05:57,060 --> 00:06:02,140
好 我们来重新访问一下 看一下 还是 undefined

193
00:06:02,140 --> 00:06:06,240
好 其实呢 我们这里原因是什么呀

194
00:06:06,240 --> 00:06:08,280
是不是我们的plugin拼错了呀

195
00:06:08,280 --> 00:06:09,560
说实话很尴尬

196
00:06:09,560 --> 00:06:11,620
好 那么我们再来看一下

197
00:06:11,620 --> 00:06:14,680
大家可以看到是不是打印出来了呀

198
00:06:14,940 --> 00:06:18,400
我们的一个插件是不是在我们context上面扩展的一个属性

199
00:06:18,400 --> 00:06:19,880
然后呢打印出来了

200
00:06:19,880 --> 00:06:22,780
那么接下来呢我再来给大家演示一个例子演示一个什么呢

201
00:06:22,780 --> 00:06:26,520
因为我们现在是不是自定的自定义了一个插件的一样

202
00:06:26,520 --> 00:06:28,940
那么如果说我们插件插件之间有依赖关系怎么办

203
00:06:28,940 --> 00:06:30,860
所以这里呢我再来创建一个新的插件

204
00:06:30,860 --> 00:06:32,680
比如说我们去

205
00:06:32,680 --> 00:06:36,180
比如说我这里呢去修改了一个package

206
00:06:36,180 --> 00:06:40,180
那么呢我们是不是直接刚才把17G-A复制了一遍了

207
00:06:40,180 --> 00:06:40,860
所以了

208
00:06:40,860 --> 00:06:42,060
所以了

209
00:06:42,060 --> 00:06:43,780
这里我需要去改一下

210
00:06:44,540 --> 00:06:47,200
改下他一个b里面他一个package.json

211
00:06:47,200 --> 00:06:50,400
首先这里我需要把内容改为b

212
00:06:50,400 --> 00:06:54,140
然后这里内容也改为e-b

213
00:06:54,140 --> 00:06:55,100
其实这样就可以了

214
00:06:55,100 --> 00:06:57,880
然后还有一个地方在app.js里面

215
00:06:57,880 --> 00:06:59,600
我是不是需要去打印b插件加载来

216
00:06:59,600 --> 00:07:03,300
然后在app的context里面咱们去打印50插件b

217
00:07:03,300 --> 00:07:05,240
好这里我们就完成了一个插件b的兵器

218
00:07:05,240 --> 00:07:08,240
我们来看一下他们之间的一个依赖关系是什么样的

219
00:07:08,240 --> 00:07:11,540
你比如说我们刚才

220
00:07:14,540 --> 00:07:19,460
好 我们这里呢就在plugin.js里面去添加一个B

221
00:07:19,460 --> 00:07:24,680
然后路径的话 这里呢同样的也少了一个APP package呢1GG-B

222
00:07:24,680 --> 00:07:26,880
好 那么我们这里就来看一下

223
00:07:26,880 --> 00:07:29,680
首先这里呢我们来重启一下

224
00:07:29,680 --> 00:07:33,180
大家可以看到 如果说我们不添加任何依赖的话

225
00:07:33,180 --> 00:07:34,860
那么我们的一个加载顺序是什么样的

226
00:07:34,860 --> 00:07:38,780
是不是我们的A先加载然后B再加载

227
00:07:38,780 --> 00:07:39,960
那么它上了什么顺序

228
00:07:39,960 --> 00:07:43,540
如果说我们不去配置一个依赖关系

229
00:07:43,540 --> 00:07:44,300
是不是A先加载

230
00:07:44,300 --> 00:07:45,960
然后B再加载

231
00:07:45,960 --> 00:07:46,360
对吧

232
00:07:46,360 --> 00:07:48,400
所以这里我们得出一个结论

233
00:07:48,400 --> 00:07:50,140
如果不配置

234
00:07:50,140 --> 00:07:51,440
依赖关系

235
00:07:51,440 --> 00:07:57,780
按照引入顺序

236
00:07:57,780 --> 00:07:58,160
好

237
00:07:58,160 --> 00:07:58,840
那么这里呢

238
00:07:58,840 --> 00:07:59,480
我们就来试一下

239
00:07:59,480 --> 00:08:01,300
假如说我们在咱们一个plugA里面

240
00:08:01,300 --> 00:08:02,360
去添加了一个dependence

241
00:08:02,360 --> 00:08:02,760
这样一个这段

242
00:08:02,760 --> 00:08:04,020
把B把它给加进去

243
00:08:04,020 --> 00:08:05,000
那么我们再来看一下

244
00:08:05,000 --> 00:08:06,820
现在的执行顺序是什么样的

245
00:08:06,820 --> 00:08:08,800
大家是不是可以看到

246
00:08:08,800 --> 00:08:09,800
大家是不是可以看到

247
00:08:09,800 --> 00:08:10,720
B先加载

248
00:08:10,720 --> 00:08:11,280
然后再加到A

249
00:08:11,280 --> 00:08:13,040
所以说我们的一个依赖关系

250
00:08:13,040 --> 00:08:14,920
在插件里面是支持的

251
00:08:14,920 --> 00:08:15,600
那么这里呢

252
00:08:15,600 --> 00:08:16,320
也就是中间键

253
00:08:16,320 --> 00:08:18,020
没有插件好使的一个原因

254
00:08:18,020 --> 00:08:19,620
也就是一GG为什么要扩展它的一个原因

255
00:08:19,620 --> 00:08:20,220
好

256
00:08:20,220 --> 00:08:21,300
这里呢我们同样了

257
00:08:21,300 --> 00:08:22,060
再来

258
00:08:22,060 --> 00:08:23,980
记录一下

259
00:08:23,980 --> 00:08:27,060
如果确认了依赖关系

260
00:08:27,060 --> 00:08:31,540
会先加载依赖项

261
00:08:31,540 --> 00:08:33,140
好

262
00:08:33,140 --> 00:08:34,440
这里呢就是咱们插件里面

263
00:08:34,440 --> 00:08:35,960
它的一个依赖关系

264
00:08:35,960 --> 00:08:37,560
好

265
00:08:37,560 --> 00:08:39,260
那么插件它到底能做什么呢

266
00:08:39,260 --> 00:08:41,460
其实我们之前都已经演示过了吧

267
00:08:41,460 --> 00:08:41,820
对吧

268
00:08:41,820 --> 00:08:43,020
其实都已经演示过一些了

269
00:08:43,020 --> 00:08:44,900
首先它呢是不是可以扩展我们类似对象的接口

270
00:08:44,900 --> 00:08:45,720
刚才我们扩展的是什么

271
00:08:45,720 --> 00:08:46,300
是不是context

272
00:08:46,300 --> 00:08:47,660
同样的也可以去扩展什么

273
00:08:47,660 --> 00:08:50,520
比如说heap,app,agent,requests

274
00:08:50,520 --> 00:08:52,200
那么agent的话后面我们也会去讲

275
00:08:52,200 --> 00:08:54,200
包括呢你去咱们在插件里面

276
00:08:54,200 --> 00:08:55,240
可以去扩展对象

277
00:08:55,240 --> 00:08:57,200
同样呢我们在插件里面是不是也可以去写medalware

278
00:08:57,200 --> 00:08:58,140
也可以去写中间键

279
00:08:58,140 --> 00:08:58,660
为什么呀

280
00:08:58,660 --> 00:08:59,560
因为我们刚才讲到了

281
00:08:59,560 --> 00:09:02,420
在插件里面是不是除了没有root和controller其他东西是不是都有

282
00:09:02,420 --> 00:09:04,640
包括呢可以在应用启动时做一些粗塑化的工作

283
00:09:04,640 --> 00:09:08,100
我们刚才是不是也在app.js里面去打印了一些咱们启动的信息

284
00:09:08,100 --> 00:09:09,660
包括呢去设置一些定时任务

285
00:09:09,660 --> 00:09:10,480
那么定时任务呢

286
00:09:10,480 --> 00:09:11,860
我们后面的课程也会去讲

287
00:09:11,860 --> 00:09:12,500
好 这里呢

288
00:09:12,500 --> 00:09:13,960
我们就来总结一下这些课

289
00:09:13,960 --> 00:09:14,720
我们所讲解的内容

290
00:09:14,720 --> 00:09:18,880
好 那么我们怎么去编写插件呢

291
00:09:18,880 --> 00:09:20,360
首先我们是通过一个命令

292
00:09:20,360 --> 00:09:21,780
npm init 1GT

293
00:09:21,780 --> 00:09:22,680
然后呢给它个参数

294
00:09:22,680 --> 00:09:23,300
type等于什么

295
00:09:23,300 --> 00:09:24,320
pluggy 对吧

296
00:09:24,320 --> 00:09:26,460
这里呢我们就可以自动的去创建一个目录

297
00:09:26,460 --> 00:09:28,620
然后就可以愉快的去编写我们的一个插件

298
00:09:28,620 --> 00:09:29,880
那么我们编写插件的时候

299
00:09:29,880 --> 00:09:31,260
是不是需要注意一个问题是什么呀

300
00:09:31,260 --> 00:09:32,960
它是不是除了控制器和router没有

301
00:09:32,960 --> 00:09:34,880
提供了和我们的1GT的应用是一样的呀

302
00:09:34,880 --> 00:09:36,800
他可以去扩党我们的一些对象吧

303
00:09:36,800 --> 00:09:37,280
对吧

304
00:09:37,280 --> 00:09:37,640
好

305
00:09:37,640 --> 00:09:38,200
那么呢

306
00:09:38,200 --> 00:09:40,520
我们是不是需要在parkage里面去做一些配置

307
00:09:40,520 --> 00:09:43,300
配置什么

308
00:09:43,300 --> 00:09:44,680
是不是配置一个字段

309
00:09:44,680 --> 00:09:45,380
叫做一GG

310
00:09:45,380 --> 00:09:46,620
一GG什么呀

311
00:09:46,620 --> 00:09:47,160
是不是一GG

312
00:09:47,160 --> 00:09:49,020
然后一GG plugin里面

313
00:09:49,020 --> 00:09:50,380
他是不是又支持几个字段

314
00:09:50,380 --> 00:09:50,800
你比如说内

315
00:09:50,800 --> 00:09:52,840
我们去定义我们插件的一个名称

316
00:09:52,840 --> 00:09:53,380
然后呢

317
00:09:53,380 --> 00:09:55,520
还会有一些dependence

318
00:09:55,520 --> 00:09:55,900
对吧

319
00:09:55,900 --> 00:09:58,260
然后还有可选的

320
00:09:58,260 --> 00:09:58,560
对吧

321
00:09:58,560 --> 00:10:02,300
那么可选的依赖之后

322
00:10:02,300 --> 00:10:03,960
还会有一些环境因素吧

323
00:10:03,960 --> 00:10:06,180
比如说你需要去配置我们在哪些环境下面加载这些插件

324
00:10:06,180 --> 00:10:07,740
但是一般情况下是不需要的

325
00:10:07,740 --> 00:10:10,260
最后呢我们是不是还去演示了一个依赖关系

326
00:10:10,260 --> 00:10:10,680
对吧

327
00:10:10,680 --> 00:10:13,380
那么如果说有的话会先加载

328
00:10:13,380 --> 00:10:17,100
是不是先加载被依赖的插件

329
00:10:17,100 --> 00:10:19,580
那如果没有是不是就按我们的一个引入顺序去加载

330
00:10:19,580 --> 00:10:21,440
好这里呢就是我们这节课的内容

