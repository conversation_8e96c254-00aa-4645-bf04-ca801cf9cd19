1
00:00:00,000 --> 00:00:02,700
好,这节课我们就来看一下Core中间间里面

2
00:00:02,700 --> 00:00:05,180
它的一个洋葱模型,包括它的一个执行顺序是什么

3
00:00:05,180 --> 00:00:07,300
我们先来看一下Core里面的一个洋葱模型

4
00:00:07,300 --> 00:00:13,720
这里呢,是我呢从Core它的一个官网来获得的一个图片

5
00:00:13,720 --> 00:00:16,960
Request和Redbox其实看起来是不是像一颗洋葱

6
00:00:16,960 --> 00:00:19,520
先到里面去,然后呢再出来

7
00:00:19,520 --> 00:00:22,280
也就是说呢,请求先进去,响应呢再出来

8
00:00:22,280 --> 00:00:24,820
同学们,有没有感觉一脸懵逼

9
00:00:24,820 --> 00:00:27,020
图片看起来很美好,但是想去理解它

10
00:00:27,020 --> 00:00:28,460
什么玩意啊,对吧

11
00:00:28,860 --> 00:00:29,600
所以说呢

12
00:00:29,600 --> 00:00:31,420
同学们没有关系

13
00:00:31,420 --> 00:00:32,560
我们来看一下

14
00:00:32,560 --> 00:00:33,520
到底什么是杨树模型

15
00:00:33,520 --> 00:00:35,860
所以我们直接通过代码来看

16
00:00:35,860 --> 00:00:37,320
这样是最直观的

17
00:00:37,320 --> 00:00:39,460
好

18
00:00:39,460 --> 00:00:40,560
其实代码我已经写好了

19
00:00:40,560 --> 00:00:41,320
因为我没有必要

20
00:00:41,320 --> 00:00:42,260
在这里去浪费时间

21
00:00:42,260 --> 00:00:42,880
比如说

22
00:00:42,880 --> 00:00:44,100
我们先去引入一个

23
00:00:44,100 --> 00:00:44,820
Core

24
00:00:44,820 --> 00:00:45,740
然后去利用一个实力

25
00:00:45,740 --> 00:00:46,860
去监听一个3000档口

26
00:00:46,860 --> 00:00:47,660
这里呢

27
00:00:47,660 --> 00:00:48,720
我们去诱使了三个中间键

28
00:00:48,720 --> 00:00:49,400
万秋税

29
00:00:49,400 --> 00:00:49,840
其实呢

30
00:00:49,840 --> 00:00:51,660
他们三个都是中间键

31
00:00:51,660 --> 00:00:55,420
好

32
00:00:55,420 --> 00:00:56,480
我们刚才讲到的中间键

33
00:00:56,480 --> 00:00:57,360
它一个核心是什么呀

34
00:00:57,360 --> 00:00:58,300
是不是Next

35
00:00:58,300 --> 00:01:00,340
所以说我们每一个插件里面

36
00:01:00,340 --> 00:01:01,360
也就是每一个中间键里面

37
00:01:01,360 --> 00:01:03,120
是不是都调用了一个next

38
00:01:03,120 --> 00:01:04,980
那么我们调用next的时候

39
00:01:04,980 --> 00:01:07,120
是不是在next的前面和后面

40
00:01:07,120 --> 00:01:08,280
都分别去打印出来了

41
00:01:08,280 --> 00:01:10,260
你比如说我们这样一个大语符号

42
00:01:10,260 --> 00:01:12,560
就是咱们在next之前执行的

43
00:01:12,560 --> 00:01:13,260
如果说是小语符号

44
00:01:13,260 --> 00:01:14,460
就是在next之后执行的

45
00:01:14,460 --> 00:01:15,380
那么我们来看一下

46
00:01:15,380 --> 00:01:16,260
one two three

47
00:01:16,260 --> 00:01:17,960
它到底是一个什么样的一个执行顺序

48
00:01:17,960 --> 00:01:19,720
这里我们就可以去理解咱们quava

49
00:01:19,720 --> 00:01:20,820
中间键它的一个执行顺序

50
00:01:20,820 --> 00:01:23,340
我们刚才在一个恶性的图片

51
00:01:23,340 --> 00:01:24,700
其实就是为了向我们表达

52
00:01:24,700 --> 00:01:26,280
当我们去调用next的时候

53
00:01:26,280 --> 00:01:28,180
大家可以注意

54
00:01:28,180 --> 00:01:31,740
其实就是想向我们表达

55
00:01:31,740 --> 00:01:34,160
要用next的时候

56
00:01:34,160 --> 00:01:38,140
中间键的代码

57
00:01:38,140 --> 00:01:40,120
执行顺序是什么

58
00:01:40,120 --> 00:01:41,180
其实它就是这个作用

59
00:01:41,180 --> 00:01:41,940
没别的

60
00:01:41,940 --> 00:01:43,020
不要以为大家看到一个洋葱

61
00:01:43,020 --> 00:01:45,140
它可能想象的传达什么

62
00:01:45,140 --> 00:01:46,340
其实它就是想象的传达

63
00:01:46,340 --> 00:01:47,540
next要用的时候

64
00:01:47,540 --> 00:01:48,320
我们的执行顺序是什么

65
00:01:48,320 --> 00:01:49,180
那么我们就来看一下

66
00:01:49,180 --> 00:01:53,360
loadmodel.js

67
00:01:53,360 --> 00:01:54,120
走理

68
00:01:54,120 --> 00:01:56,220
好我们来访问一下

69
00:01:56,220 --> 00:01:57,840
好

70
00:01:57,840 --> 00:01:58,560
都放的

71
00:01:58,560 --> 00:01:59,140
其实没关系

72
00:01:59,140 --> 00:02:00,720
我们来看一下执行顺序

73
00:02:00,720 --> 00:02:01,740
One to say

74
00:02:01,740 --> 00:02:02,380
3 to one

75
00:02:02,380 --> 00:02:03,380
大家可以看到

76
00:02:03,380 --> 00:02:04,580
首先是大于符号

77
00:02:04,580 --> 00:02:05,780
是不是咱们的

78
00:02:05,780 --> 00:02:07,700
首先咱们Nex之前的代码

79
00:02:07,700 --> 00:02:08,840
Nex之前的代码

80
00:02:08,840 --> 00:02:10,780
大家可以注意到

81
00:02:10,780 --> 00:02:12,080
我们所有

82
00:02:12,080 --> 00:02:14,760
所有Nex前面的代码

83
00:02:14,760 --> 00:02:15,920
是不是按照我们

84
00:02:15,920 --> 00:02:17,880
App use的顺序去执行的

85
00:02:17,880 --> 00:02:18,300
对吧

86
00:02:18,300 --> 00:02:20,560
App use的顺序去执行的

87
00:02:20,560 --> 00:02:21,500
然后呢

88
00:02:21,500 --> 00:02:25,160
然后我们最后三个执行的是什么

89
00:02:25,160 --> 00:02:26,220
是不是3 to one

90
00:02:26,220 --> 00:02:27,420
也就分别是

91
00:02:27,420 --> 00:02:28,420
他

92
00:02:28,420 --> 00:02:30,420
他

93
00:02:30,420 --> 00:02:31,420
什么意思

94
00:02:31,420 --> 00:02:32,420
什么意思同学们

95
00:02:32,420 --> 00:02:33,420
我们是不是先

96
00:02:33,420 --> 00:02:34,420
先怎么样执行

97
00:02:34,420 --> 00:02:35,420
是不是先执行next上面的代码

98
00:02:35,420 --> 00:02:36,420
执行走走走

99
00:02:36,420 --> 00:02:38,420
然后再去执行咱们的

100
00:02:38,420 --> 00:02:40,420
咱们再去

101
00:02:40,420 --> 00:02:41,420
执行next后面的代码

102
00:02:41,420 --> 00:02:42,420
是不是倒过

103
00:02:42,420 --> 00:02:43,420
是不是倒着来

104
00:02:43,420 --> 00:02:44,420
比如说咱们next

105
00:02:44,420 --> 00:02:45,420
next前面的代码

106
00:02:45,420 --> 00:02:47,420
按use的顺序去执行

107
00:02:47,420 --> 00:02:48,420
next后面的代码

108
00:02:48,420 --> 00:02:50,420
是不是倒着去执行了

109
00:02:50,420 --> 00:02:51,420
那么我们这里是不是就可以得出一个结论

110
00:02:51,420 --> 00:02:56,420
next前面的代码

111
00:02:56,420 --> 00:02:58,420
前面的代码,按照

112
00:02:58,420 --> 00:03:01,480
use的顺序执行

113
00:03:01,480 --> 00:03:04,360
next后面的代码

114
00:03:04,360 --> 00:03:05,360
是否反的执行

115
00:03:05,360 --> 00:03:07,360
那么这里同学们就很奇怪了

116
00:03:07,360 --> 00:03:09,360
我到底怎样去理解这一块呢

117
00:03:09,360 --> 00:03:10,360
我们next为什么会这样

118
00:03:10,360 --> 00:03:14,080
其实这里呢,可能有些同学学了两三点的诺语

119
00:03:14,080 --> 00:03:16,080
今天是包括实行了很长时间的一段

120
00:03:16,080 --> 00:03:19,080
cowa在一个框架,他们都不能够去很好的理解

121
00:03:19,080 --> 00:03:22,080
next,他的执行顺序为什么是这样的

122
00:03:22,080 --> 00:03:25,080
这里我呢有一套,可以说是独家秘方

123
00:03:25,080 --> 00:03:54,640
 groove 结论

124
00:03:55,080 --> 00:03:57,000
next的方法

125
00:03:57,000 --> 00:04:00,560
其实就是值的

126
00:04:00,560 --> 00:04:10,540
当前执行函数的下一个use里面的方法

127
00:04:10,540 --> 00:04:12,660
什么意思啊

128
00:04:12,660 --> 00:04:13,820
什么意思

129
00:04:13,820 --> 00:04:17,540
比如说我们app是不是use了one two three

130
00:04:17,540 --> 00:04:17,940
对吧

131
00:04:17,940 --> 00:04:19,600
是不是use了one two three

132
00:04:19,600 --> 00:04:22,620
那么我们在one在一个中间键里面去执行next的时候

133
00:04:22,620 --> 00:04:24,020
其实这个next是谁啊

134
00:04:24,020 --> 00:04:24,700
同学们

135
00:04:24,700 --> 00:04:25,540
next 其实指谁啊

136
00:04:25,540 --> 00:04:30,700
其实 next 指的是 q嘛 对吧 我们刚才是不是解释了 next 的方法其实

137
00:04:30,700 --> 00:04:35,020
就是指了当前执行函数的下一个又是里面的方法 我们当前执行的是不是

138
00:04:35,020 --> 00:04:35,060
 one

139
00:04:35,060 --> 00:04:36,780
那么 next 指谁

140
00:04:36,780 --> 00:04:40,420
是不是指 q啊 因为咱们 atp 又是 one 之后是不是又是 q

141
00:04:40,420 --> 00:04:43,140
好 同样的 我们 q里面 next 指谁

142
00:04:43,140 --> 00:04:44,340
是不是 three呀

143
00:04:44,340 --> 00:04:47,620
所以呢 我们来注释一下 在 one 里面其实指的是 two

144
00:04:47,620 --> 00:04:49,980
to 里面 next 指的是 three

145
00:04:49,980 --> 00:04:51,900
那么 three 里面 next 指的是谁

146
00:04:52,460 --> 00:04:53,860
虽然3里面的ness是谁

147
00:04:53,860 --> 00:04:57,300
咱们app是不是use3之后就没有了

148
00:04:57,300 --> 00:04:58,080
虽然是空

149
00:04:58,080 --> 00:05:00,060
那么这里呢

150
00:05:00,060 --> 00:05:00,920
我们就可以很

151
00:05:00,920 --> 00:05:01,400
这里呢

152
00:05:01,400 --> 00:05:02,300
我们就可以很好的理解

153
00:05:02,300 --> 00:05:04,220
中间它的一个执行顺序

154
00:05:04,220 --> 00:05:04,900
我们来画一幅图

155
00:05:04,900 --> 00:05:06,080
来帮助同学们来理解一下

156
00:05:06,080 --> 00:05:09,980
好

157
00:05:09,980 --> 00:05:11,540
重新画吧

158
00:05:11,540 --> 00:05:13,140
好

159
00:05:13,140 --> 00:05:13,700
假如说

160
00:05:13,700 --> 00:05:15,000
假如说这是我们的执行站

161
00:05:15,000 --> 00:05:15,800
同学们

162
00:05:15,800 --> 00:05:16,780
假如说这里呢

163
00:05:16,780 --> 00:05:17,280
是我们的执行站

164
00:05:17,280 --> 00:05:19,480
我们首先app use1的时候

165
00:05:19,480 --> 00:05:20,660
我们是不是要执行1在一个方法

166
00:05:20,660 --> 00:05:21,200
好

167
00:05:21,200 --> 00:05:22,660
首先我们要执行万站一个方法

168
00:05:22,660 --> 00:05:24,960
好

169
00:05:24,960 --> 00:05:25,920
这里我把代砍过来

170
00:05:25,920 --> 00:05:27,740
可能好理解一点

171
00:05:27,740 --> 00:05:39,400
这里调大一点

172
00:05:39,400 --> 00:05:40,420
好

173
00:05:40,420 --> 00:05:41,020
这应该看得见

174
00:05:41,020 --> 00:05:41,400
好

175
00:05:41,400 --> 00:05:43,640
首先我们APP是不是又死万

176
00:05:43,640 --> 00:05:44,800
然后呢

177
00:05:44,800 --> 00:05:45,400
又死之后

178
00:05:45,400 --> 00:05:46,420
输掉执行万站一个方法

179
00:05:46,420 --> 00:05:46,860
对吧

180
00:05:46,860 --> 00:05:47,160
好

181
00:05:47,160 --> 00:05:47,920
我们这里来

182
00:05:47,920 --> 00:05:49,200
首先来来

183
00:05:49,200 --> 00:05:50,920
画一个万的执行站

184
00:05:50,920 --> 00:05:52,620
咱们执行站里面

185
00:05:52,620 --> 00:05:53,400
是不是执行了一个

186
00:05:53,400 --> 00:05:55,940
万站一个方法

187
00:05:55,940 --> 00:05:57,240
好

188
00:05:57,240 --> 00:05:58,280
字体调大一点

189
00:05:58,280 --> 00:06:01,620
好

190
00:06:01,620 --> 00:06:03,340
首先我们此时是不是执行万

191
00:06:03,340 --> 00:06:04,840
那么万执行到

192
00:06:04,840 --> 00:06:06,240
首先是不是执行咱们第一行

193
00:06:06,240 --> 00:06:06,680
对吧

194
00:06:06,680 --> 00:06:07,420
执行第一行之后

195
00:06:07,420 --> 00:06:08,640
再执行第二行

196
00:06:08,640 --> 00:06:08,960
Next

197
00:06:08,960 --> 00:06:09,620
Next指谁

198
00:06:09,620 --> 00:06:10,440
是不是指2

199
00:06:10,440 --> 00:06:11,100
对吧

200
00:06:11,100 --> 00:06:12,200
好

201
00:06:12,200 --> 00:06:12,800
Next指2

202
00:06:12,800 --> 00:06:13,400
那么此时呢

203
00:06:13,400 --> 00:06:14,800
我们的方形2是不是要入站

204
00:06:14,800 --> 00:06:16,220
咱们的万是不是还没执行完

205
00:06:16,220 --> 00:06:16,540
对吧

206
00:06:16,540 --> 00:06:17,340
好

207
00:06:17,340 --> 00:06:18,340
那么我们此时呢

208
00:06:18,340 --> 00:06:20,420
2这样一个方法

209
00:06:20,420 --> 00:06:25,920
入账,好,来啦,把

210
00:06:25,920 --> 00:06:35,020
自己调大一点,好,那我们来看一下tue,首先呢tue会执行一行,是不是大于符号tue,然后呢,这些nex,那么这个nex是谁,是不是指我们的3呀

211
00:06:35,020 --> 00:06:42,700
好,指,指3,那么我们的3执行,是不是也要把咱们的方法3,是不是进入咱们的执行站了,好,进来

212
00:06:42,700 --> 00:06:47,560
好

213
00:06:47,560 --> 00:06:50,160
此时是不是one two say

214
00:06:50,160 --> 00:06:50,680
是不是到齐了

215
00:06:50,680 --> 00:06:51,540
好

216
00:06:51,540 --> 00:06:53,500
那么我们的three执行的时候

217
00:06:53,500 --> 00:06:55,140
到next

218
00:06:55,140 --> 00:06:56,500
是不是首先打印了一个大于符号

219
00:06:56,500 --> 00:06:56,820
three

220
00:06:56,820 --> 00:06:57,440
然后到next

221
00:06:57,440 --> 00:06:58,060
next是什么

222
00:06:58,060 --> 00:06:59,780
咱们app use three之后

223
00:06:59,780 --> 00:07:00,420
后面是不是没有了

224
00:07:00,420 --> 00:07:01,640
所以他的next的指控

225
00:07:01,640 --> 00:07:02,740
对吧

226
00:07:02,740 --> 00:07:03,940
好

227
00:07:03,940 --> 00:07:05,400
所以接下来就没有

228
00:07:05,400 --> 00:07:06,480
是不是没有方法入战了

229
00:07:06,480 --> 00:07:07,500
然后再去执行什么

230
00:07:07,500 --> 00:07:09,660
是不是执行console.log

231
00:07:09,660 --> 00:07:11,640
是不是小于符号

232
00:07:11,640 --> 00:07:12,480
所以那么我们的three

233
00:07:12,480 --> 00:07:14,540
我们的three

234
00:07:14,540 --> 00:07:16,000
不好意思

235
00:07:16,000 --> 00:07:16,800
我们的

236
00:07:17,560 --> 00:07:22,180
我们的3是不是要出战 代表什么 是不是我们的3站一个方法已经执行完毕了呀

237
00:07:22,180 --> 00:07:24,740
好 那么我们的3出战之后 此时是不是

238
00:07:24,740 --> 00:07:28,760
Q跟着就要出战了 因为刚才去里面的next 是不是指什么 是不是指对呀

239
00:07:28,760 --> 00:07:30,120
 我们的睡执行完了

240
00:07:30,120 --> 00:07:35,450
对吧 那么的接下来是不是要我们的去继续去执行什么council.log 是不是小一符号去执行完成之后

241
00:07:35,450 --> 00:07:36,520
 我们的Q是不是

242
00:07:36,520 --> 00:07:37,800
也跟着

243
00:07:37,800 --> 00:07:40,600
是不是也跟着出战了呀 同学们

244
00:07:46,100 --> 00:07:47,220
好 那么我们的

245
00:07:47,220 --> 00:07:48,500
q

246
00:07:48,500 --> 00:07:50,860
执行完成之后

247
00:07:50,860 --> 00:07:52,860
执行完成之后是不是就轮到我们的

248
00:07:52,860 --> 00:07:54,300
万出战了 为什么呀

249
00:07:54,300 --> 00:07:54,980
因为

250
00:07:54,980 --> 00:07:56,780
在万的 next 是不是就是指q啊

251
00:07:56,780 --> 00:07:57,940
我们的q执行完成之后

252
00:07:57,940 --> 00:07:59,900
是不是就执行康泽点那个小一幅号万了

253
00:07:59,900 --> 00:08:01,580
所以说我们的洋葱模型

254
00:08:01,580 --> 00:08:03,500
所以说我们的洋葱模型

255
00:08:03,500 --> 00:08:04,940
才是这样一个执行顺序

256
00:08:04,940 --> 00:08:05,740
首先呢

257
00:08:05,740 --> 00:08:06,740
是不是万

258
00:08:06,740 --> 00:08:08,620
然后呢

259
00:08:08,620 --> 00:08:11,620
q吧

260
00:08:11,620 --> 00:08:13,060
然后执行

261
00:08:13,060 --> 00:08:13,740
3

262
00:08:13,740 --> 00:08:15,420
最后呢

263
00:08:15,420 --> 00:08:15,980
3

264
00:08:16,100 --> 00:08:17,340
Q1

265
00:08:17,340 --> 00:08:22,180
这样同学们是不是就可以理解了我们在Core里面他的一个中间间的一个概念

266
00:08:22,180 --> 00:08:25,100
其实并没有想象那么复杂

267
00:08:25,100 --> 00:08:27,320
在同学们去理解了next他到底是什么

268
00:08:27,320 --> 00:08:28,880
我们就完全可以理解

269
00:08:28,880 --> 00:08:31,320
咱们露了几天使中他的一个中间间的概念到底是什么

270
00:08:31,320 --> 00:08:31,700
好

271
00:08:31,700 --> 00:08:32,300
那么我们呢

272
00:08:32,300 --> 00:08:34,400
接下来就来总结一下

273
00:08:34,400 --> 00:08:38,260
我们刚才是不是讲到了洋葱模型

274
00:08:38,260 --> 00:08:40,340
而且他得出了一个很重要的结论

275
00:08:40,340 --> 00:08:44,980
是不是在next里面

276
00:08:46,100 --> 00:08:49,020
其实next代表的是什么

277
00:08:49,020 --> 00:08:52,720
是不是next是不是就是代表了

278
00:08:52,720 --> 00:08:58,560
当前执行函数的下一个app.use

279
00:08:58,560 --> 00:09:01,940
这谁指的

280
00:09:01,940 --> 00:09:04,760
就是谁呀

281
00:09:04,760 --> 00:09:06,480
那么这里呢

282
00:09:06,480 --> 00:09:07,860
语言描述上面可能有一点

283
00:09:07,860 --> 00:09:08,640
有一点绕

284
00:09:08,640 --> 00:09:09,600
没关系

285
00:09:09,600 --> 00:09:11,020
同学只要能够理解

286
00:09:11,020 --> 00:09:11,980
就可以啊

287
00:09:11,980 --> 00:09:12,460
比如说我们的one

288
00:09:12,460 --> 00:09:13,480
他的next是不是只to

289
00:09:13,480 --> 00:09:15,960
那么我们的to里面

290
00:09:15,960 --> 00:09:17,460
3的next是不是指3啊

291
00:09:17,460 --> 00:09:18,620
那么3的next指空

292
00:09:18,620 --> 00:09:19,900
其实很好去理解

293
00:09:19,900 --> 00:09:22,660
只是说语言上面可能并不是很好表达

294
00:09:22,660 --> 00:09:27,000
这里呢就是咱们中间界里面一个洋出模型

295
00:09:27,000 --> 00:09:27,420
它那个概念

296
00:09:27,420 --> 00:09:29,000
其实这一块呢是比较复杂的

297
00:09:29,000 --> 00:09:31,000
但是只要说我们理解了coa它的一个执行原理

298
00:09:31,000 --> 00:09:32,360
其实并没有想象那么难

299
00:09:32,360 --> 00:09:34,800
好 这里呢就是我们这几个内容

