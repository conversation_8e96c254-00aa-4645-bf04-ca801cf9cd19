1
00:00:00,000 --> 00:00:02,300
好 我们来看一下agent它的用法

2
00:00:02,300 --> 00:00:04,360
那么其实它的用法也非常简单

3
00:00:04,360 --> 00:00:06,920
我们只需要在根部录下面去创点个agent.js就可以了

4
00:00:06,920 --> 00:00:09,220
它其实和我们的app.js使用是非常类似的

5
00:00:09,220 --> 00:00:11,260
我们去module.export一个function

6
00:00:11,260 --> 00:00:13,320
那么里面会帮我们去注入一个agent这样一个对象

7
00:00:13,320 --> 00:00:15,360
那么它里面有一个message

8
00:00:15,360 --> 00:00:17,160
message是做什么 是不是可以监听一些事件

9
00:00:17,160 --> 00:00:18,940
那么我们就可以去监听到

10
00:00:18,940 --> 00:00:20,480
1gg ready 这样一个事件

11
00:00:20,480 --> 00:00:23,300
那么其实我们在agent里面

12
00:00:23,300 --> 00:00:25,600
它还可以去发送一些

13
00:00:25,600 --> 00:00:27,400
通过message去发送一些消息

14
00:00:27,400 --> 00:00:29,960
因为我们进程中是不是需要去通信了

15
00:00:30,000 --> 00:00:33,000
然后呢我们APP里面就可以去收到

16
00:00:33,000 --> 00:00:35,880
那么进程这件通信呢我们后面会去介绍

17
00:00:35,880 --> 00:00:39,780
现在刚才只是给大家去简单的介绍一下我们APP的agent它的用法

18
00:00:39,780 --> 00:00:43,200
好那么这里呢我来给同学们去简单的演示一下我们的agent

19
00:00:43,200 --> 00:00:48,000
首先呢我们在根目录下面呢去创建一个什么呀是不是agent.js

20
00:00:48,000 --> 00:00:52,200
好那么我们呢好直接粘贴过来解决同学们的时间

21
00:00:52,200 --> 00:00:56,100
其实非常简单我们直接去导出一个方形对吧

22
00:00:56,100 --> 00:00:59,500
然后我们去监听一下咱们的egg ready这样的一个事件

23
00:00:59,500 --> 00:01:01,500
这里呢 我们什么都不做 直接去打印

24
00:01:01,500 --> 00:01:05,500
咱们的agent开始运行

25
00:01:05,500 --> 00:01:07,500
好 我们来重新来run一下

26
00:01:07,500 --> 00:01:11,500
好 大家可以看到

27
00:01:11,500 --> 00:01:13,500
大家可以看到 我们的agent是不是已经

28
00:01:13,500 --> 00:01:15,500
开始运行了 对吧

29
00:01:15,500 --> 00:01:17,500
好 那么其实agent它的启动是非常简单的

30
00:01:17,500 --> 00:01:19,500
那么我们继续往下看

31
00:01:19,500 --> 00:01:21,500
首先呢 我们来看一下

32
00:01:21,500 --> 00:01:23,500
master,agent,work,他们三者之间的关系是什么

33
00:01:23,500 --> 00:01:25,500
他们到底怎么去用

34
00:01:25,500 --> 00:01:27,500
首先呢 我们来看一下这张表

35
00:01:27,500 --> 00:01:31,080
同时呢 同时会启动这样的三类进程 我们刚才是不是画了一个图 对吧

36
00:01:31,080 --> 00:01:33,380
比如说我们cpu有8核 agent是不是有一个

37
00:01:33,380 --> 00:01:34,920
walker是不是有7个 对吧

38
00:01:34,920 --> 00:01:40,040
好 那么我们的master呢 它的稳定性实际上是非常高的 为什么呀 因为master它不负责具体的一个业务逻辑

39
00:01:40,040 --> 00:01:44,140
它什么业务都不行 所以说它的稳定性非常高 它只负责去管理我们的agent和walker

40
00:01:44,140 --> 00:01:46,440
说白了 为什么 比如说我们举个例子

41
00:01:46,440 --> 00:01:49,260
我们的领导是不是一般不干活呀

42
00:01:49,260 --> 00:01:54,970
只是指挥我们的下属去干活 你去把这个搞一下 你去把他搞一下 所以说犯错了往往是干活的

43
00:01:54,970 --> 00:01:55,140
 对吧

44
00:01:55,140 --> 00:01:57,400
好 那么呢 他呢 是不运行业务代码

45
00:01:57,400 --> 00:01:59,680
那么 尔征特呢 他的稳定性呢 也是比较高的 为什么呀

46
00:01:59,680 --> 00:02:01,240
其实尔征特 因为他是一个秘书

47
00:02:01,240 --> 00:02:02,940
他呢 其实也不干活

48
00:02:02,940 --> 00:02:04,940
对吧 那么我们工人实际上是搬砖的

49
00:02:04,940 --> 00:02:07,640
那么 秘书呢 实际上是辅助master去做一些琐事

50
00:02:07,640 --> 00:02:09,940
好 那么呢 他的稳定性呢 也是

51
00:02:09,940 --> 00:02:12,140
比较高的 而且呢 他呢 会写

52
00:02:12,140 --> 00:02:13,340
少量的业务代码

53
00:02:13,340 --> 00:02:14,740
为什么尔征特是少量啊

54
00:02:14,740 --> 00:02:16,040
那么我们的领导不干活

55
00:02:16,040 --> 00:02:17,140
那么 秘书要不要干活

56
00:02:17,140 --> 00:02:17,980
秘书肯定要干活

57
00:02:17,980 --> 00:02:19,140
但是呢肯定比工人干的少一点

58
00:02:19,140 --> 00:02:19,380
对吧

59
00:02:19,380 --> 00:02:19,580
好

60
00:02:19,580 --> 00:02:20,460
那么walker呢

61
00:02:20,460 --> 00:02:21,100
他的稳定性呢

62
00:02:21,100 --> 00:02:21,780
就比较一般

63
00:02:21,780 --> 00:02:22,240
为什么呀

64
00:02:22,240 --> 00:02:23,120
因为我天天搬砖

65
00:02:23,120 --> 00:02:23,540
天天搬砖

66
00:02:23,540 --> 00:02:23,900
对吧

67
00:02:23,900 --> 00:02:24,820
哪天搬的不爽呢

68
00:02:24,820 --> 00:02:25,180
我就不干了

69
00:02:25,180 --> 00:02:25,680
所以我一般

70
00:02:25,680 --> 00:02:26,140
好

71
00:02:26,140 --> 00:02:27,560
是否运行业务代码

72
00:02:27,560 --> 00:02:29,080
肯定呢

73
00:02:29,080 --> 00:02:30,100
搬砖的就是我们的walker

74
00:02:30,100 --> 00:02:30,840
对吧

75
00:02:30,840 --> 00:02:31,120
所以呢

76
00:02:31,120 --> 00:02:32,060
专班的越多

77
00:02:32,060 --> 00:02:32,620
越容易犯错

78
00:02:32,620 --> 00:02:34,280
那么我们来看一下

79
00:02:34,280 --> 00:02:35,140
具体的介绍他们

80
00:02:35,140 --> 00:02:35,940
那么master

81
00:02:35,940 --> 00:02:37,000
我们是不是刚才已经讲到了

82
00:02:37,000 --> 00:02:38,600
他负责进程管理的工作

83
00:02:38,600 --> 00:02:39,400
而且呢

84
00:02:39,400 --> 00:02:40,880
不去运行任何的业务代码

85
00:02:40,880 --> 00:02:42,620
而且Master它的稳定性非常高的

86
00:02:42,620 --> 00:02:44,620
那么我们再来看一下JINT

87
00:02:44,620 --> 00:02:46,180
那么大部分情况下

88
00:02:46,180 --> 00:02:47,020
我们在写业务代码的时候

89
00:02:47,020 --> 00:02:49,500
完全不用考虑JINT进程的一个存在

90
00:02:49,500 --> 00:02:51,440
但是当我们遇到了一些场景

91
00:02:51,440 --> 00:02:52,060
好 同学们注意

92
00:02:52,060 --> 00:02:54,740
这里我们就开始去简单的去讲解一下

93
00:02:54,740 --> 00:02:56,300
我们JINT它的应用场景是什么

94
00:02:56,300 --> 00:02:58,440
我们什么情况下需要去创建JINT这样一个建议室

95
00:02:58,440 --> 00:02:59,480
我们来看一下

96
00:02:59,480 --> 00:03:01,160
由于JINT只有一个

97
00:03:01,160 --> 00:03:04,100
而且会负责许多持续连接的脏火内火

98
00:03:04,100 --> 00:03:05,800
因此它不能轻易挂掉和重启

99
00:03:05,800 --> 00:03:08,320
所以JINT进程在监听到Web或异常时

100
00:03:08,320 --> 00:03:09,640
不会退出

101
00:03:09,640 --> 00:03:10,840
但是会打印出错误日志

102
00:03:10,840 --> 00:03:13,460
我们需要对日志中的微博异常提高请提

103
00:03:13,460 --> 00:03:15,420
其实这里我们是不是可以得出两个信息

104
00:03:15,420 --> 00:03:16,620
什么信息

105
00:03:16,620 --> 00:03:18,260
什么信息

106
00:03:18,260 --> 00:03:21,840
Gent它会负责许多维持连接的张火烈火

107
00:03:21,840 --> 00:03:22,400
什么意思

108
00:03:22,400 --> 00:03:24,540
说明它的应用场景是什么

109
00:03:24,540 --> 00:03:26,540
应用场景

110
00:03:26,540 --> 00:03:29,100
它的应用场景之一

111
00:03:29,100 --> 00:03:30,400
其实就是我们一个长连接

112
00:03:30,400 --> 00:03:33,440
可能有了什么就不能理解

113
00:03:33,440 --> 00:03:34,580
我们稍微举个例子

114
00:03:34,580 --> 00:03:35,980
我们的Worker是不是会有

115
00:03:35,980 --> 00:03:37,020
比如说你的CPU有8核

116
00:03:37,020 --> 00:03:38,280
我们的Worker是不是可能就会有

117
00:03:38,280 --> 00:03:39,700
如果说没有achint是不是有八个

118
00:03:39,700 --> 00:03:42,240
那么我们如果一个worker去进行一个长链接

119
00:03:42,240 --> 00:03:43,560
比如说我们都去连接到一个服务

120
00:03:43,560 --> 00:03:45,320
那么八个worker是不是要有八个链接

121
00:03:45,320 --> 00:03:46,960
是不是会增加福气的负担

122
00:03:46,960 --> 00:03:49,260
但是如果说我们让achint去连接

123
00:03:49,260 --> 00:03:49,760
是不是只有一个

124
00:03:49,760 --> 00:03:51,920
所以这就是achint他的一个

125
00:03:51,920 --> 00:03:53,320
是不是他的一个作用

126
00:03:53,320 --> 00:03:55,540
是不是就类似于咱们开发中的一个产品经历

127
00:03:55,540 --> 00:03:56,740
那么我们产品经历

128
00:03:56,740 --> 00:03:58,400
一个人是不是可以对很多开发

129
00:03:58,400 --> 00:03:59,680
比如说我们去对接后台

130
00:03:59,680 --> 00:04:00,860
对接前端对接我们的UI

131
00:04:00,860 --> 00:04:01,360
对吧

132
00:04:01,360 --> 00:04:04,100
也不可能说前端和咱们的运营直接去对接

133
00:04:04,100 --> 00:04:05,360
后端直接和运营去对接

134
00:04:05,360 --> 00:04:06,560
所以这样咱们沟通成本是不是很高

135
00:04:06,560 --> 00:04:08,200
而且呢运营运营也会很累

136
00:04:08,200 --> 00:04:08,760
你自己也很累

137
00:04:08,760 --> 00:04:10,300
但是如果说我们中间有个产品去对接

138
00:04:10,300 --> 00:04:11,760
就类似于咱们agint的一个职责

139
00:04:11,760 --> 00:04:13,200
这样呢就会提高我们的效率

140
00:04:13,200 --> 00:04:16,220
那么第二个信息是什么呀

141
00:04:16,220 --> 00:04:17,720
就是说agint他呢

142
00:04:17,720 --> 00:04:19,520
没有一个优雅退出的机制

143
00:04:19,520 --> 00:04:21,980
也就是说agint挂了master不管

144
00:04:21,980 --> 00:04:24,300
所以呢我们需要自己通过日志去维护他

145
00:04:24,300 --> 00:04:25,380
比如说我们去看起里面

146
00:04:25,380 --> 00:04:27,360
是不是我们通过log.r去把他写入日志

147
00:04:27,360 --> 00:04:28,740
我们需要时常关注我们的agint

148
00:04:28,740 --> 00:04:29,380
他有没有问题

149
00:04:29,380 --> 00:04:31,180
我们再来看一下walker

150
00:04:31,180 --> 00:04:32,900
那么walker他的职责是什么呢

151
00:04:32,900 --> 00:04:34,780
是不是真正的去进行我们的用户请求

152
00:04:34,780 --> 00:04:35,560
说白了就是搬砖的

153
00:04:35,560 --> 00:04:38,720
这里呢我们就不去过多的去介绍了

154
00:04:38,720 --> 00:04:41,960
好 这里呢就是我们进行他的一个简单的使用

155
00:04:41,960 --> 00:04:43,280
以及我们去介绍了一下

156
00:04:43,280 --> 00:04:45,020
他们的一个应用场景是什么

157
00:04:45,020 --> 00:04:47,820
这里呢来简单的去总结一下

158
00:04:47,820 --> 00:04:49,780
好 那么我们来看一下

159
00:04:49,780 --> 00:04:51,460
进行他的一个应用场景是什么

160
00:04:51,460 --> 00:04:52,640
我们刚才是不是讲到了

161
00:04:52,640 --> 00:04:55,320
应用场景是什么

162
00:04:55,320 --> 00:04:56,240
是不是可以进行

163
00:04:56,240 --> 00:04:58,200
比如说我们有长连接的场景可以去使用

164
00:04:58,200 --> 00:04:59,920
那么他一个特点是什么呢

165
00:04:59,920 --> 00:05:01,300
我们需要注意的特点是什么

166
00:05:01,300 --> 00:05:02,980
出错

167
00:05:02,980 --> 00:05:03,600
是不是master

168
00:05:03,600 --> 00:05:05,480
不会重启

169
00:05:05,480 --> 00:05:08,100
所以我们需要时刻关注日资

170
00:05:08,100 --> 00:05:13,980
这里就是我们这些内容

