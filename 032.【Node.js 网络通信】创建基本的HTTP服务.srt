1
00:00:00,000 --> 00:00:02,980
接下来我们来学习我们本课程的第四章

2
00:00:02,980 --> 00:00:03,980
构建http服务

3
00:00:03,980 --> 00:00:06,000
那么我们在本章节当中

4
00:00:06,000 --> 00:00:08,000
是这样一个内容安排

5
00:00:08,000 --> 00:00:09,000
首先我们会给大家来介绍一下

6
00:00:09,000 --> 00:00:12,000
在Node中所提供的htt模块

7
00:00:12,000 --> 00:00:13,000
然后了解了这个模块之后

8
00:00:13,000 --> 00:00:15,000
接下来我们就可以使用Node

9
00:00:15,000 --> 00:00:16,000
来基于这个模块

10
00:00:16,000 --> 00:00:18,000
来构建我们的http服务

11
00:00:18,000 --> 00:00:19,000
那么然后接下来

12
00:00:19,000 --> 00:00:21,000
我们了解了基本API使用之后

13
00:00:21,000 --> 00:00:24,000
我们就可以来实现一个静态文件服务器

14
00:00:24,000 --> 00:00:25,000
我们可以实现

15
00:00:25,000 --> 00:00:27,000
让它类似于我们常见的阿塔奇

16
00:00:27,000 --> 00:00:28,000
IIS

17
00:00:28,000 --> 00:00:30,000
Tomcat等服务器所拥有的一些简单

18
00:00:30,000 --> 00:00:33,000
那么处理好这个静态文件服务器之后

19
00:00:33,000 --> 00:00:38,000
然后接下来我们会学习到如何在Node中使用模板引擎来处理我们的动态网页

20
00:00:38,000 --> 00:00:40,000
了解了如何处理动态网页之后

21
00:00:40,000 --> 00:00:45,000
然后接下来我们来看一下在Node中如何结合数据户来渲染我们的动态页面

22
00:00:45,000 --> 00:00:48,000
那么在课程的最后我们会来做一个简单的综合案例

23
00:00:48,000 --> 00:00:50,000
也就是一个留言本

24
00:00:50,000 --> 00:00:53,000
当然我们这里呢还是使用我们的原生的HTTP核心模块

25
00:00:53,000 --> 00:00:56,000
最后我们来给大家说一下在Node.js中

26
00:00:56,000 --> 00:00:59,000
它相关的一些第三方的HTTP服务框架

27
00:00:59,000 --> 00:00:59,860
ATTP服务框架

28
00:00:59,860 --> 00:01:00,960
LU Express

29
00:01:00,960 --> 00:01:02,320
Core等其他的

30
00:01:02,320 --> 00:01:03,320
我们就是都来

31
00:01:03,320 --> 00:01:04,460
就是进行一个简单的了解

32
00:01:04,460 --> 00:01:05,460
好

33
00:01:05,460 --> 00:01:07,140
那么首先我们来看一下

34
00:01:07,140 --> 00:01:09,380
在Note中为我们提供的ATTP模块

35
00:01:09,380 --> 00:01:11,440
那么在这里大家就可以看到

36
00:01:11,440 --> 00:01:12,780
TCP和UDP呢

37
00:01:12,780 --> 00:01:15,140
都属于网络传输层的一个协议

38
00:01:15,140 --> 00:01:16,680
那么如果说你要构建这种

39
00:01:16,680 --> 00:01:18,420
极致高效或者说高性能的

40
00:01:18,420 --> 00:01:19,040
这种网络应用

41
00:01:19,040 --> 00:01:20,320
那么我们大家呢

42
00:01:20,320 --> 00:01:21,300
可以通过传输层

43
00:01:21,300 --> 00:01:22,720
去进行这个编程开发

44
00:01:22,720 --> 00:01:23,680
这种方式呢

45
00:01:23,680 --> 00:01:24,100
就有一个高效

46
00:01:24,100 --> 00:01:26,460
但是这种传输层的协议

47
00:01:26,460 --> 00:01:28,180
对于我们这种常见的

48
00:01:28,180 --> 00:01:28,920
或者说经典的这种

49
00:01:28,920 --> 00:01:30,920
浏览器和服务器通信场景

50
00:01:30,920 --> 00:01:31,800
那么这个时候

51
00:01:31,800 --> 00:01:33,920
TCP和UDP就不太合适了

52
00:01:33,920 --> 00:01:34,760
并不是说不合适

53
00:01:34,760 --> 00:01:35,540
而是他们两者的

54
00:01:35,540 --> 00:01:37,300
这个时候就会变得非常的麻烦

55
00:01:37,300 --> 00:01:38,840
例如我们在浏览器和服务器

56
00:01:38,840 --> 00:01:39,800
进行通信的过程当中

57
00:01:39,800 --> 00:01:41,540
我们有常见的这种网页

58
00:01:41,540 --> 00:01:42,540
页面的普通字符

59
00:01:42,540 --> 00:01:43,160
图片

60
00:01:43,160 --> 00:01:43,940
音视频

61
00:01:43,940 --> 00:01:45,960
等更多的其他一些超媒体资源

62
00:01:45,960 --> 00:01:47,780
那么TCP和UDP呢

63
00:01:47,780 --> 00:01:49,240
只是用于最简单的

64
00:01:49,240 --> 00:01:50,720
这种数据传输的一种协议

65
00:01:50,720 --> 00:01:51,380
他们本身呢

66
00:01:51,380 --> 00:01:52,400
不关心这种数据的格式

67
00:01:52,400 --> 00:01:53,280
所以说

68
00:01:53,280 --> 00:01:55,180
如果说你要在浏览器和服务器之间

69
00:01:55,180 --> 00:01:56,740
传递这种就是非常复杂的

70
00:01:56,740 --> 00:01:58,380
就是有格式的这种数据

71
00:01:58,380 --> 00:01:59,020
那么此时的话

72
00:01:59,020 --> 00:01:59,740
我们就可以看到

73
00:01:59,740 --> 00:02:01,380
对于这种经典的DS通信

74
00:02:01,380 --> 00:02:03,600
那么就基于这个传输层之上

75
00:02:03,600 --> 00:02:05,760
专门制定了更上一层的一种通信协议

76
00:02:05,760 --> 00:02:06,240
HTTP

77
00:02:06,240 --> 00:02:08,440
那么它就专门用于

78
00:02:08,440 --> 00:02:09,640
注意在设计之初

79
00:02:09,640 --> 00:02:12,180
就专门用于这种浏览器和服务器来进行通信

80
00:02:12,180 --> 00:02:13,620
那么同样的

81
00:02:13,620 --> 00:02:15,920
由于我们这个HTTP协议本身呢

82
00:02:15,920 --> 00:02:18,680
它并不关心如何传输

83
00:02:18,680 --> 00:02:20,680
这种数据相关的细节的这种协议问题

84
00:02:20,680 --> 00:02:23,860
所以说它属于是应用层协议

85
00:02:23,860 --> 00:02:25,180
那么关于这个应用层协议呢

86
00:02:25,180 --> 00:02:26,360
它的理解也非常简单

87
00:02:26,360 --> 00:02:27,720
它就好比例如

88
00:02:27,720 --> 00:02:31,100
我们平常打电话

89
00:02:31,100 --> 00:02:32,820
那么你打电话的时候

90
00:02:32,820 --> 00:02:33,740
两个人

91
00:02:33,740 --> 00:02:34,640
那么这两个人呢

92
00:02:34,640 --> 00:02:35,900
他们都通过这个电话

93
00:02:35,900 --> 00:02:37,500
这个物理设备来进行通信

94
00:02:37,500 --> 00:02:39,740
也就是说我们电话两端的

95
00:02:39,740 --> 00:02:40,960
这两个人是不关心

96
00:02:40,960 --> 00:02:43,560
我们所说的话是如何进行传输的

97
00:02:43,560 --> 00:02:44,660
那我们在这里

98
00:02:44,660 --> 00:02:46,280
两端的人要关心的是什么呢

99
00:02:46,280 --> 00:02:47,460
也就是两个人所说的话

100
00:02:47,460 --> 00:02:48,820
那么这两个人所说的话

101
00:02:48,820 --> 00:02:50,280
例如我们中国人用的是汉语

102
00:02:50,280 --> 00:02:52,320
美国人用的是美式英语

103
00:02:52,320 --> 00:02:53,320
或者说英语

104
00:02:53,320 --> 00:02:54,860
那么这个语言本身

105
00:02:54,860 --> 00:02:55,680
或者说汉语

106
00:02:55,680 --> 00:02:56,300
或者说是英语

107
00:02:56,300 --> 00:02:57,340
那么他们就是什么呢

108
00:02:57,340 --> 00:02:57,820
他们就是

109
00:02:57,820 --> 00:02:59,860
我们把它称之为是应用层协议

110
00:02:59,860 --> 00:03:00,940
或者说是一种规范

111
00:03:00,940 --> 00:03:03,260
那么HTTP就制定了

112
00:03:03,260 --> 00:03:04,780
我们这种就是浏览器和服务器

113
00:03:04,780 --> 00:03:06,440
去进行通信的一种协议规范

114
00:03:06,440 --> 00:03:09,320
或者说一种应用层的一种数据格式

115
00:03:09,320 --> 00:03:10,640
那么在Node中

116
00:03:10,640 --> 00:03:12,180
他就为我们提供了

117
00:03:12,180 --> 00:03:15,560
提供了这个就是相关的HTTP模块和ATPS

118
00:03:15,560 --> 00:03:18,720
当然ATPS主要是用于这个就是数据加密

119
00:03:18,720 --> 00:03:20,840
就是加密的ATTP协议

120
00:03:20,840 --> 00:03:22,060
当然本质的还是ATTP

121
00:03:22,060 --> 00:03:24,040
这个我们在下一章节当中会了解

122
00:03:24,040 --> 00:03:25,640
那么无论如何

123
00:03:25,640 --> 00:03:26,360
我们大家就可以看到

124
00:03:26,360 --> 00:03:27,800
如果说你要在Node中

125
00:03:27,800 --> 00:03:29,000
来构建UDP

126
00:03:29,000 --> 00:03:30,260
构建ATTP服务

127
00:03:30,260 --> 00:03:32,500
那你只需要来使用这个核心模块

128
00:03:32,500 --> 00:03:33,400
就可以了

129
00:03:33,400 --> 00:03:34,240
所以说你可以看到

130
00:03:34,240 --> 00:03:34,960
使用它的第一步

131
00:03:34,960 --> 00:03:37,420
就是去加载这个ATTP核心模块

132
00:03:37,420 --> 00:03:38,460
有了它以后

133
00:03:38,460 --> 00:03:39,880
我们就会调用它的CritServ

134
00:03:39,880 --> 00:03:41,740
这个方法来创建一个Serv实例

135
00:03:41,740 --> 00:03:44,400
那么下面的就是Serv实例

136
00:03:44,400 --> 00:03:45,800
相关的一些API

137
00:03:45,800 --> 00:03:46,820
例如它相关的一些事件

138
00:03:46,820 --> 00:03:47,980
包括它相关的一些方法

139
00:03:47,980 --> 00:03:50,140
那么除了这个Saurus实力本身

140
00:03:50,140 --> 00:03:51,080
我们最常用的

141
00:03:51,080 --> 00:03:52,960
还有这个就是在HTTP

142
00:03:52,960 --> 00:03:54,200
请求处理过程当中的

143
00:03:54,200 --> 00:03:56,220
请求对象和响应对象

144
00:03:56,220 --> 00:03:57,840
那么有了这个请求对象和响应对象

145
00:03:57,840 --> 00:03:58,760
所提供的这些API

146
00:03:58,760 --> 00:04:00,480
我们就能通过请求对象

147
00:04:00,480 --> 00:04:03,000
拿到客户端请求相关的一些信息

148
00:04:03,000 --> 00:04:03,960
然后通过这个响应对象

149
00:04:03,960 --> 00:04:06,320
我们就能给客户端去发送一些响应消息

150
00:04:06,320 --> 00:04:08,340
那么这个就是我们接下来

151
00:04:08,340 --> 00:04:10,420
在这些案例当中

152
00:04:10,420 --> 00:04:12,340
要使用到的这些最常用的API

153
00:04:12,340 --> 00:04:14,160
那么接下来我们在这里

154
00:04:14,160 --> 00:04:15,540
就可以利用这个API

155
00:04:15,540 --> 00:04:17,680
我们来构建一个非常简单的

156
00:04:17,680 --> 00:04:18,960
Hello World这样一个服务

157
00:04:18,960 --> 00:04:20,440
我们快速的来体验一下

158
00:04:20,440 --> 00:04:22,020
这个模块的一个基本用法

159
00:04:22,020 --> 00:04:23,000
那么我们在这里给出的

160
00:04:23,000 --> 00:04:23,580
实力代码当中

161
00:04:23,580 --> 00:04:26,400
它呢就是在创建了一个HTTP服务

162
00:04:26,400 --> 00:04:27,680
然后当客户端请就过来的时候

163
00:04:27,680 --> 00:04:30,140
那么这里呢就去发送了一个Hello World

164
00:04:30,140 --> 00:04:32,960
那么接下来回到我们的编辑器当中

165
00:04:32,960 --> 00:04:34,280
我们首先先把这个代码

166
00:04:34,280 --> 00:04:35,280
我们在这里就不再去手写

167
00:04:35,280 --> 00:04:36,660
我们先把它打过来

168
00:04:36,660 --> 00:04:38,160
然后回到我们的编辑器当中

169
00:04:38,160 --> 00:04:38,920
好我们在这里呢

170
00:04:38,920 --> 00:04:40,400
我们来新建一个文件

171
00:04:40,400 --> 00:04:42,820
那么这个就是我们的零号文件

172
00:04:42,820 --> 00:04:45,080
那这就是Hello World.js

173
00:04:45,080 --> 00:04:46,400
好了我们把刚才的代码

174
00:04:46,400 --> 00:04:47,480
直接担接过来

175
00:04:47,480 --> 00:04:48,400
好简单解释一下

176
00:04:48,400 --> 00:04:50,040
这个这一段代码的含义

177
00:04:50,040 --> 00:04:51,300
首先加载ADD模块

178
00:04:51,300 --> 00:04:53,400
好然后定义了两个常量

179
00:04:53,400 --> 00:04:54,600
一个是我们的

180
00:04:54,600 --> 00:04:56,040
我们接下来要使用的地址

181
00:04:56,040 --> 00:04:57,500
一个是我们接下来要使用的端口号

182
00:04:57,500 --> 00:04:58,800
好然后接下来

183
00:04:58,800 --> 00:05:00,260
我们调用这个模块的

184
00:05:00,260 --> 00:05:01,540
一个Curt Server这个方法

185
00:05:01,540 --> 00:05:02,940
好我们创建了一个Server实例

186
00:05:02,940 --> 00:05:04,780
然后创建Server的同时

187
00:05:04,780 --> 00:05:05,920
我们为它设置了一个

188
00:05:05,920 --> 00:05:07,680
这个被叫做请求处理函数

189
00:05:07,680 --> 00:05:08,540
也就是说

190
00:05:08,540 --> 00:05:09,680
当客户端请求过来的时候

191
00:05:09,680 --> 00:05:11,040
这个函数会被执行

192
00:05:11,040 --> 00:05:12,500
好那么在这个函数当中

193
00:05:12,500 --> 00:05:15,140
首先他设置了一个200的状态码

194
00:05:15,140 --> 00:05:16,200
响应200的状态码

195
00:05:16,200 --> 00:05:17,820
然后接下来他设置了一个响应头

196
00:05:17,820 --> 00:05:19,360
在响应头当中告诉客户端

197
00:05:19,360 --> 00:05:20,720
我本次给你想要的消息

198
00:05:20,720 --> 00:05:22,960
内容类型是文本数据

199
00:05:22,960 --> 00:05:24,600
plane就表示是普通的文本

200
00:05:24,600 --> 00:05:25,520
没有其他格式

201
00:05:25,520 --> 00:05:27,240
好那么在最后呢

202
00:05:27,240 --> 00:05:28,780
去response.end去结束响应

203
00:05:28,780 --> 00:05:29,840
结束响应的同时呢

204
00:05:29,840 --> 00:05:30,580
去发一个消息

205
00:05:30,580 --> 00:05:31,160
就是hello world

206
00:05:31,160 --> 00:05:33,200
那么在最后

207
00:05:33,200 --> 00:05:34,200
这个sell.listen

208
00:05:34,200 --> 00:05:35,040
调用listen方法

209
00:05:35,040 --> 00:05:36,200
然后就绑定一个段伙号

210
00:05:36,200 --> 00:05:38,520
和这个我们指定的IP地址

211
00:05:38,520 --> 00:05:39,300
绑定成功以后

212
00:05:39,300 --> 00:05:40,240
然后再回到函数当中

213
00:05:40,240 --> 00:05:41,720
去输出了我们这个服务

214
00:05:41,720 --> 00:05:44,160
监听启动成功的这个日志消息

215
00:05:44,160 --> 00:05:46,360
表示我们的服务端运行在哪里

216
00:05:46,360 --> 00:05:46,560
好了

217
00:05:46,560 --> 00:05:47,660
了解了这段代码以后

218
00:05:47,660 --> 00:05:49,320
然后接下来我们就可以来到命令行当中

219
00:05:49,320 --> 00:05:51,100
把这段脚本去给它启动起来

220
00:05:51,100 --> 00:05:54,080
然后我们在这里输入node00

221
00:05:54,080 --> 00:05:57,180
我们进入我们的http这个目录当中

222
00:05:57,180 --> 00:05:59,220
然后输入node00hello.js

223
00:05:59,220 --> 00:06:00,160
好

224
00:06:00,160 --> 00:06:00,740
执行完毕以后

225
00:06:00,740 --> 00:06:01,800
我们可以看到在控制档当中

226
00:06:01,800 --> 00:06:03,020
我们就收到了这个日志消息

227
00:06:03,020 --> 00:06:04,980
那么接下来我们测试的话

228
00:06:04,980 --> 00:06:07,240
我们就可以直接来到浏览器当中

229
00:06:07,240 --> 00:06:08,640
在浏览器当中

230
00:06:08,640 --> 00:06:10,980
我们接下来就可以输入我们的这个服务班地址

231
00:06:10,980 --> 00:06:13,140
那么这个就是3000

232
00:06:13,140 --> 00:06:13,980
好了

233
00:06:13,980 --> 00:06:15,240
我们输入以后敲回车

234
00:06:15,240 --> 00:06:17,100
然后我们就可以看到在当前网页当中

235
00:06:17,100 --> 00:06:19,940
我们就收到了来自于服务班所响应的这个消息

236
00:06:19,940 --> 00:06:21,440
好

237
00:06:21,440 --> 00:06:25,180
那么这个就是关于我们在nodejs中使用http模块

238
00:06:25,180 --> 00:06:27,540
一个最简单的一个实例

