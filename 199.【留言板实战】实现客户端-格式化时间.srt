1
00:00:00,000 --> 00:00:03,260
前面我们已经完成了

2
00:00:03,260 --> 00:00:04,300
这个留言板的主体功能

3
00:00:04,300 --> 00:00:05,560
但是还剩一个细节

4
00:00:05,560 --> 00:00:07,000
就是这个时间的显示

5
00:00:07,000 --> 00:00:08,280
对这个时间来说

6
00:00:08,280 --> 00:00:09,860
我们之前存储的实际上是毫秒数

7
00:00:09,860 --> 00:00:11,140
但是我们显示的时候

8
00:00:11,140 --> 00:00:12,220
应该有特定的格式

9
00:00:12,220 --> 00:00:13,060
所以说接下来

10
00:00:13,060 --> 00:00:14,020
我们要处理一下这个时间

11
00:00:14,020 --> 00:00:14,880
怎么来做

12
00:00:14,880 --> 00:00:16,620
我们可以借助VoE所提供的

13
00:00:16,620 --> 00:00:17,280
自定义过滤器

14
00:00:17,280 --> 00:00:19,240
来对这个时间进行格式化

15
00:00:19,240 --> 00:00:20,160
所以说我们在这

16
00:00:20,160 --> 00:00:21,460
找到这个入口文件

17
00:00:21,460 --> 00:00:22,960
我们首先要做一个

18
00:00:22,960 --> 00:00:24,900
日期的格式化的过滤器

19
00:00:24,900 --> 00:00:25,820
在这

20
00:00:25,820 --> 00:00:27,200
我们叫自定义

21
00:00:27,200 --> 00:00:28,820
这个VoE的过滤器

22
00:00:28,820 --> 00:00:31,020
要做什么事呢

23
00:00:31,020 --> 00:00:31,720
格式化时间

24
00:00:31,720 --> 00:00:34,600
通过Vue.future来做

25
00:00:34,600 --> 00:00:36,860
第一个参数就是过滤器的名字

26
00:00:36,860 --> 00:00:38,580
我们就叫Fmart

27
00:00:38,580 --> 00:00:43,960
然后第二个参数是一个函数

28
00:00:43,960 --> 00:00:45,460
这里边我们需要两个参数

29
00:00:45,460 --> 00:00:46,480
一个是Value

30
00:00:46,480 --> 00:00:47,920
就是你要格式化的数据

31
00:00:47,920 --> 00:00:49,000
第二个就是格式

32
00:00:49,000 --> 00:00:49,760
Fmart

33
00:00:49,760 --> 00:00:53,500
那这函数里边用来完成格式化的业务逻辑

34
00:00:53,500 --> 00:00:54,980
格式化时间

35
00:00:54,980 --> 00:00:57,740
那怎么来做这个工作呢

36
00:00:57,740 --> 00:00:58,900
如果我们自己要做的话

37
00:00:58,900 --> 00:01:00,180
实际上还是稍微麻烦一些

38
00:01:00,180 --> 00:01:03,180
但是我们可以借助第三方的包来实现

39
00:01:03,180 --> 00:01:06,200
比如说这里边有一个包叫moment.ds

40
00:01:06,200 --> 00:01:09,460
第二包可以对时间进行格式化处理

41
00:01:09,460 --> 00:01:13,480
比如说用这种格式可以产生后边对应的展示效果

42
00:01:13,480 --> 00:01:14,620
下面有好多的例子

43
00:01:14,620 --> 00:01:16,120
这还有对应的文档

44
00:01:16,120 --> 00:01:17,420
我们要想使用它的话

45
00:01:17,420 --> 00:01:18,800
首先要把这个包给它装一下

46
00:01:18,800 --> 00:01:21,540
然后导入就可以使用它提供的API了

47
00:01:21,540 --> 00:01:23,400
联系的话这个moment这个方法

48
00:01:23,400 --> 00:01:24,360
这也可以接受参数

49
00:01:24,360 --> 00:01:26,100
用来处理你要格式化的时间

50
00:01:26,100 --> 00:01:27,480
比如说在解析这一块

51
00:01:27,480 --> 00:01:28,360
有对应的说明

52
00:01:28,360 --> 00:01:29,440
它可以接受参数

53
00:01:29,440 --> 00:01:31,480
那接下来我们就利用这个特性

54
00:01:31,480 --> 00:01:32,920
来完成格式化的动作

55
00:01:32,920 --> 00:01:35,060
在这我们要做的事情

56
00:01:35,060 --> 00:01:37,380
首先我们要产生一个日期对象

57
00:01:37,380 --> 00:01:39,620
它等于什么

58
00:01:39,620 --> 00:01:40,680
NeoDate

59
00:01:40,680 --> 00:01:43,160
这应该传递value值

60
00:01:43,160 --> 00:01:45,220
因为value的话就是时间对象

61
00:01:45,220 --> 00:01:46,880
但是我们这这个是好秒数

62
00:01:46,880 --> 00:01:47,420
并且是字不算

63
00:01:47,420 --> 00:01:48,700
比较方便的做法

64
00:01:48,700 --> 00:01:50,280
我们这把它转成数值

65
00:01:50,280 --> 00:01:51,280
就是pass int

66
00:01:51,280 --> 00:01:53,820
然后再传递给NeoDate

67
00:01:53,820 --> 00:01:54,480
好

68
00:01:54,480 --> 00:01:55,840
然后的话我们就可以调用

69
00:01:55,840 --> 00:01:57,320
moment来做格式化了

70
00:01:57,320 --> 00:01:58,680
应该先返回

71
00:01:58,680 --> 00:02:00,400
把moment处理的结果

72
00:02:00,400 --> 00:02:03,000
要返回这里边把d传进去

73
00:02:03,000 --> 00:02:04,380
然后调用一个方法叫format

74
00:02:04,380 --> 00:02:07,420
然后把参数format传进来

75
00:02:07,420 --> 00:02:08,740
这就可以了

76
00:02:08,740 --> 00:02:10,360
这个参数就是我们在使用过滤器的时候

77
00:02:10,360 --> 00:02:11,320
给它传递的

78
00:02:11,320 --> 00:02:14,880
这个形式大体上和我们刚才在这里看到的是类似的

79
00:02:14,880 --> 00:02:15,720
就这种格式

80
00:02:15,720 --> 00:02:18,920
这些格式我们自己可以按照规则定义

81
00:02:18,920 --> 00:02:22,320
这是关于过滤器的处理逻辑

82
00:02:22,320 --> 00:02:24,460
接下来我们就使用过滤器

83
00:02:24,460 --> 00:02:25,460
把它拿过来

84
00:02:25,460 --> 00:02:26,220
然后找到

85
00:02:26,220 --> 00:02:27,700
这个时间

86
00:02:27,700 --> 00:02:29,140
这再加个输线

87
00:02:29,140 --> 00:02:30,180
把规律性放这

88
00:02:30,180 --> 00:02:31,420
然后这我们传递

89
00:02:31,420 --> 00:02:32,540
对应的格式就可以了

90
00:02:32,540 --> 00:02:33,700
这个格式会传递给

91
00:02:33,700 --> 00:02:34,420
第二个坛数

92
00:02:34,420 --> 00:02:35,140
就是那formart

93
00:02:35,140 --> 00:02:36,500
这里边我们就写

94
00:02:36,500 --> 00:02:37,140
年

95
00:02:37,140 --> 00:02:37,940
恒天

96
00:02:37,940 --> 00:02:38,540
月

97
00:02:38,540 --> 00:02:39,340
日

98
00:02:39,340 --> 00:02:40,140
还有时

99
00:02:40,140 --> 00:02:40,940
分

100
00:02:40,940 --> 00:02:41,540
秒

101
00:02:41,540 --> 00:02:42,860
好了这是它的使用

102
00:02:42,860 --> 00:02:43,900
然后保存

103
00:02:43,900 --> 00:02:45,020
我们看个页面

104
00:02:45,020 --> 00:02:46,260
这样的话这个时间

105
00:02:46,260 --> 00:02:47,060
它就正常了

106
00:02:47,060 --> 00:02:48,220
刷新一下

107
00:02:48,220 --> 00:02:49,220
有没有问题

108
00:02:49,220 --> 00:02:50,340
这就是这个

109
00:02:50,340 --> 00:02:51,460
时间的格式化处理

110
00:02:51,460 --> 00:02:52,340
就这么多

111
00:02:52,340 --> 00:02:54,340
其实这属于BOC的支持点

112
00:02:54,340 --> 00:02:55,140
与这个graphq2

113
00:02:55,140 --> 00:02:56,060
其实没什么关系

114
00:02:56,060 --> 00:02:57,760
好 这是关于功能层面

115
00:02:57,760 --> 00:02:58,420
一个细节

116
00:02:58,420 --> 00:02:59,140
我们就说到这里

117
00:02:59,140 --> 00:03:00,720
好 到此为止

118
00:03:00,720 --> 00:03:01,680
关于整个的

119
00:03:01,680 --> 00:03:02,920
领域版的案例

120
00:03:02,920 --> 00:03:03,920
我们就全部做完了

121
00:03:03,920 --> 00:03:04,620
就这么多

