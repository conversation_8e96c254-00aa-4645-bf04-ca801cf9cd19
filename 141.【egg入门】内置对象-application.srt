1
00:00:00,000 --> 00:00:03,080
好 这节课我们就来看一下1G它的类似对象有哪些内容

2
00:00:03,080 --> 00:00:04,100
那么呢

3
00:00:04,100 --> 00:00:06,660
比如说在core里面它有哪些类似对象啊

4
00:00:06,660 --> 00:00:08,320
咱们常用了是不是一个app

5
00:00:08,320 --> 00:00:09,220
一个context

6
00:00:09,220 --> 00:00:12,040
那么呢 我们的1GG其实它也是基于core的

7
00:00:12,040 --> 00:00:14,080
包括了从core继承来的四个对象

8
00:00:14,080 --> 00:00:14,840
application

9
00:00:14,840 --> 00:00:16,120
context

10
00:00:16,120 --> 00:00:18,940
application是不是就是app啊 包括了request和response

11
00:00:18,940 --> 00:00:22,280
然后呢1GG呢 它自己还扩展了一些对象

12
00:00:22,280 --> 00:00:23,300
像controller

13
00:00:23,300 --> 00:00:24,060
service

14
00:00:24,060 --> 00:00:24,680
help

15
00:00:24,680 --> 00:00:25,600
config和log

16
00:00:25,600 --> 00:00:27,900
log是什么 咱们去进行日制的一些工具

17
00:00:27,900 --> 00:00:29,700
那么我们为什么要学习类似对象啊

18
00:00:30,000 --> 00:00:31,220
它就和我们盖房子一样

19
00:00:31,220 --> 00:00:32,940
那么内置对象就是我们的砖

20
00:00:32,940 --> 00:00:33,560
就是我们的瓦

21
00:00:33,560 --> 00:00:34,840
我们想把一个房子建好

22
00:00:34,840 --> 00:00:35,440
我们一定要把

23
00:00:35,440 --> 00:00:36,600
它那些内置对象搞清楚

24
00:00:36,600 --> 00:00:37,260
它有哪些功能

25
00:00:37,260 --> 00:00:38,080
怎么样去使用的

26
00:00:38,080 --> 00:00:40,220
这样就可以去方便我们的学习

27
00:00:40,220 --> 00:00:41,520
和我们的一个使用

28
00:00:41,520 --> 00:00:41,960
好

29
00:00:41,960 --> 00:00:43,320
那么这节目我们主要来看一下

30
00:00:43,320 --> 00:00:46,020
application也就是1G里面的

31
00:00:46,020 --> 00:00:48,000
它的app是怎么样的一回事

32
00:00:48,000 --> 00:00:51,520
application它是全局应用对象

33
00:00:51,520 --> 00:00:52,840
说明它是一个全局对象

34
00:00:52,840 --> 00:00:54,460
在一个应用中只会实力化一个

35
00:00:54,460 --> 00:00:55,660
它继承自

36
00:00:55,660 --> 00:00:56,960
call application

37
00:00:56,960 --> 00:00:57,740
好

38
00:00:57,740 --> 00:00:59,020
那么它只会实力化一个

39
00:00:59,020 --> 00:00:59,640
怎么样去理解

40
00:01:00,160 --> 00:01:04,170
你比如说我们的controller 是不是一个路由对一个controller 对吧 那么我们的controller

41
00:01:04,170 --> 00:01:05,780
 它是不是会实力化多个呀

42
00:01:05,780 --> 00:01:08,080
那么app 它不一样 全局只有一个

43
00:01:08,080 --> 00:01:13,200
而且在上面我们可以去挂扎一些全局的方法和对象 我们可以轻松的去扩展它

44
00:01:13,200 --> 00:01:17,300
怎么样扩展了 刚才我们是不是扩展过一次 是不是在extend在一个路里面

45
00:01:17,300 --> 00:01:23,460
去创建一个什么application.js 因为我们刚才是不是扩展了contest呀 我们在extend里面去创建一个contest.js

46
00:01:23,460 --> 00:01:26,780
 其实同样的 我们也可以通过这种方式去扩展它

47
00:01:28,060 --> 00:01:30,360
好 我们先来看一下他支持哪些事件

48
00:01:30,360 --> 00:01:33,700
在框架运行时会在application上触发一些事件

49
00:01:33,700 --> 00:01:37,540
那么应用开发者或插件开发者可以监听这事件去做一些操作

50
00:01:37,540 --> 00:01:38,560
你比如说有哪些事件呢

51
00:01:38,560 --> 00:01:40,600
server error request和response

52
00:01:40,600 --> 00:01:41,620
那么什么是server事件

53
00:01:41,620 --> 00:01:44,440
那么该事件一个Worker进程只会触发一次

54
00:01:44,440 --> 00:01:46,240
在http服务完成启动之后

55
00:01:46,240 --> 00:01:48,540
会将http的server通过这个事件报道出来给开发者

56
00:01:48,540 --> 00:01:50,580
什么意思 是不是说明你只要起了一个服务

57
00:01:50,580 --> 00:01:52,120
当你的http服务完成之后

58
00:01:52,120 --> 00:01:53,280
那么就会触发在一个

59
00:01:53,280 --> 00:01:54,940
事件 那么error是不是就是

60
00:01:54,940 --> 00:01:56,740
遇到错误的时候 前面咱们也讲过

61
00:01:56,740 --> 00:01:58,020
request or response呢

62
00:01:58,020 --> 00:02:00,840
在你的应用收到请求或者响应请求时

63
00:02:00,840 --> 00:02:01,600
分别会出发

64
00:02:01,600 --> 00:02:02,980
request or response时间

65
00:02:02,980 --> 00:02:04,420
好那么呢我们呢

66
00:02:04,420 --> 00:02:06,980
就来具体的写一个app.js

67
00:02:06,980 --> 00:02:08,400
我们来看一下它的效果是什么

68
00:02:08,400 --> 00:02:10,860
好首先呢我们在

69
00:02:10,860 --> 00:02:14,440
在一个根目录下面去创建一个app.js

70
00:02:14,440 --> 00:02:18,140
我们在sifer里面

71
00:02:18,140 --> 00:02:19,860
比如说我们去打印一下

72
00:02:19,860 --> 00:02:23,820
http服务启动完毕

73
00:02:23,820 --> 00:02:24,920
对吧因为我们的service

74
00:02:24,920 --> 00:02:26,720
是不是专门去监听我们的HTTP服务的

75
00:02:26,720 --> 00:02:28,300
Ever呢我们先不看把它给刷掉

76
00:02:28,300 --> 00:02:32,240
Request是不是咱们只要客户端发起了请求就会触发呀

77
00:02:32,240 --> 00:02:37,540
客户端发起请求

78
00:02:37,540 --> 00:02:45,120
那么RizBounce呢

79
00:02:45,120 --> 00:02:47,180
好我们简单一点这个RizBounce吧

80
00:02:47,180 --> 00:02:50,260
好那么我们来重启一下

81
00:02:50,260 --> 00:02:51,200
我们来访问看一下

82
00:02:51,200 --> 00:02:53,580
好大家可以看到我们现在是不是已经打印出来了

83
00:02:53,580 --> 00:02:54,800
HTTP服务启动完毕

84
00:02:54,800 --> 00:02:56,940
也就是说咱们触发了我们的一个server这样一个事件

85
00:02:56,940 --> 00:02:58,320
那么我们来访问一下页面

86
00:02:58,320 --> 00:02:59,780
看能不能触发request和response

87
00:02:59,780 --> 00:03:06,520
好 我们刷新了一下

88
00:03:06,520 --> 00:03:08,180
好 大家可以看到我们是不是触发了

89
00:03:08,180 --> 00:03:09,000
首先触发了request

90
00:03:09,000 --> 00:03:10,160
然后呢是response

91
00:03:10,160 --> 00:03:12,180
后来呢又触发一次request和response

92
00:03:12,180 --> 00:03:14,120
这里是不是很奇怪呀

93
00:03:14,120 --> 00:03:15,020
我们明明刷了一次

94
00:03:15,020 --> 00:03:17,860
为什么会产生了两次往返的一个请求

95
00:03:17,860 --> 00:03:18,880
那么我们再刷一次看一下

96
00:03:18,880 --> 00:03:20,080
刷 走理

97
00:03:20,080 --> 00:03:22,060
好 大家可以看到我们是不是又走的事实上

98
00:03:22,060 --> 00:03:22,560
什么原因

99
00:03:22,560 --> 00:03:23,900
我们明明只刷新了一下页面

100
00:03:23,900 --> 00:03:24,920
为什么request response

101
00:03:24,920 --> 00:03:25,560
它走了两次

102
00:03:25,560 --> 00:03:27,440
那么其实没关系

103
00:03:27,440 --> 00:03:28,660
我们来看一下

104
00:03:28,660 --> 00:03:33,740
我们来看一下我们的网络请求是怎么回事

105
00:03:33,740 --> 00:03:34,460
其实大家可以看到

106
00:03:34,460 --> 00:03:35,780
我们的服务是不是发起了两个请求

107
00:03:35,780 --> 00:03:36,680
一个是localhost

108
00:03:36,680 --> 00:03:37,180
我是首页

109
00:03:37,180 --> 00:03:38,260
是不是咱们刚才发起了

110
00:03:38,260 --> 00:03:39,580
跟目录这样一个get请求

111
00:03:39,580 --> 00:03:41,540
那么这里还有一个请求是什么呢

112
00:03:41,540 --> 00:03:43,080
其实它是浏览器

113
00:03:43,080 --> 00:03:44,440
默认了一个请求

114
00:03:44,440 --> 00:03:45,820
它会请求咱们的一个图标

115
00:03:45,820 --> 00:03:47,220
所以真的会发起两次请求

116
00:03:47,220 --> 00:03:50,820
好

117
00:03:50,820 --> 00:03:51,560
那么呢

118
00:03:51,560 --> 00:03:55,240
刚才是不是讲到了app里面他的一些事件

119
00:03:55,240 --> 00:03:57,060
这里我们就来看一下

120
00:03:57,060 --> 00:03:59,160
我们在哪里可以获取到app这样一个对象

121
00:03:59,160 --> 00:04:00,240
好

122
00:04:00,240 --> 00:04:00,720
首先

123
00:04:00,720 --> 00:04:02,100
第一种获取方式

124
00:04:02,100 --> 00:04:04,440
我们是不是直接在app.js里面

125
00:04:04,440 --> 00:04:06,180
你比如说你去export的一个app

126
00:04:06,180 --> 00:04:07,040
咱们看这里

127
00:04:07,040 --> 00:04:09,940
在这里是不是可以获取咱们app的一个对象

128
00:04:09,940 --> 00:04:11,520
比如说app.on.wans

129
00:04:11,520 --> 00:04:11,960
对吧

130
00:04:11,960 --> 00:04:12,600
好

131
00:04:12,600 --> 00:04:14,520
那么我们除了在app.js里面

132
00:04:14,520 --> 00:04:15,780
可以获取了app这样一个对象

133
00:04:15,780 --> 00:04:16,860
那么我们还在哪里可以呢

134
00:04:16,860 --> 00:04:19,180
其实在controller里面也是可以了

135
00:04:19,180 --> 00:04:21,160
你比如说我们来测试一下

136
00:04:21,160 --> 00:04:22,160
我们现在

137
00:04:22,160 --> 00:04:25,300
我们现在是不是在app.js里面对吧

138
00:04:25,300 --> 00:04:27,700
那么呢我们在这里呢给app添加一个属性

139
00:04:27,700 --> 00:04:31,020
比如说我们app.testapp等于

140
00:04:31,020 --> 00:04:35,120
添加了一个app的属性

141
00:04:35,120 --> 00:04:36,740
那么我们来测试一下

142
00:04:36,740 --> 00:04:37,740
我们在controller里面

143
00:04:37,740 --> 00:04:39,600
controller里面能不能够拿到

144
00:04:39,600 --> 00:04:41,060
怎么样拿呢

145
00:04:41,060 --> 00:04:42,800
我们直接通过this.app去取

146
00:04:42,800 --> 00:04:47,000
好那么呢我们还是在home在一个controller里面

147
00:04:47,000 --> 00:04:48,060
咱们去拿了试一下

148
00:04:48,060 --> 00:04:48,820
好

149
00:04:48,820 --> 00:04:50,480
比如说我们

150
00:04:50,480 --> 00:04:52,780
直接把它给打到我们的玻璃上面去

151
00:04:52,780 --> 00:04:54,580
this.app点什么呀

152
00:04:54,580 --> 00:04:58,680
是不是testapp我们来看一下在咱们的controller里面能不能拿到app上面的属性

153
00:04:58,680 --> 00:05:01,480
如果说能拿到是不是说明咱们的controller上面能够去挂载我们的

154
00:05:01,480 --> 00:05:03,540
能够去取到我们app这样一个对象

155
00:05:03,540 --> 00:05:03,780
好

156
00:05:03,780 --> 00:05:05,580
那么我们来刷新一下咱们的

157
00:05:05,580 --> 00:05:07,880
页面手里是不是大家可以看到

158
00:05:07,880 --> 00:05:13,780
添加了一个app的属性说明了是不是在咱们的controller里面是不是可以拿到app上一个对象

159
00:05:13,780 --> 00:05:17,100
那么呢其实呢和括号一样在contest的对象也可以通过

160
00:05:17,100 --> 00:05:21,460
contest.app访问到咱们一个application对象什么意思

161
00:05:21,460 --> 00:05:22,740
什么意思

162
00:05:22,780 --> 00:05:27,140
我们刚才是不是直接使用的 在controller里面是不直接使用的第4.app.testapp

163
00:05:27,140 --> 00:05:30,200
 那么如果说我们通过context.app看一下能不能防荡

164
00:05:30,200 --> 00:05:33,020
好 这里其实也可以防荡

165
00:05:33,020 --> 00:05:36,360
所以说呢 我们不仅在controller的他的一个

166
00:05:36,360 --> 00:05:42,500
第4上面挂载了app 而且他在context上面也挂载了app的一个实力 好 那么我们就来总结一下这节课的一个内容

167
00:05:42,500 --> 00:05:47,100
好 我们刚才是不是讲到了

168
00:05:47,100 --> 00:05:50,180
一个咱们类置对象啊 什么是不是

169
00:05:51,460 --> 00:05:58,100
casing 它是做什么的呀 是不是一个全局的对象 而且只能实力化

170
00:05:58,100 --> 00:06:01,060
一次 好 那么它是不是可以监听一些

171
00:06:01,060 --> 00:06:03,880
事件 可以监听哪些事件

172
00:06:03,880 --> 00:06:11,580
是不是第一个 server 呀 它是监听了什么事件 是不是 htp 服务启动 完毕会触发

173
00:06:11,580 --> 00:06:11,980
 对吧

174
00:06:11,980 --> 00:06:13,940
还有呢 error

175
00:06:13,940 --> 00:06:16,580
是不是还可以去监听咱们的一个

176
00:06:16,580 --> 00:06:20,960
request和response 好 那么除了监听事件

177
00:06:20,960 --> 00:06:23,020
我们可在哪里可以去获取了

178
00:06:23,020 --> 00:06:25,380
那么获取APP这样一个对象

179
00:06:25,380 --> 00:06:27,320
首先在APP.js里面是不是可以

180
00:06:27,320 --> 00:06:29,980
包括了context里面不对

181
00:06:29,980 --> 00:06:30,920
controller对吧

182
00:06:30,920 --> 00:06:33,340
在controller里面是可以去拿到了

183
00:06:33,340 --> 00:06:34,040
而且呢

184
00:06:34,040 --> 00:06:35,600
我们的controller里面哪里可以拿到

185
00:06:35,600 --> 00:06:36,860
是不是this.app

186
00:06:36,860 --> 00:06:37,580
还有呢

187
00:06:37,580 --> 00:06:40,420
this.context.app

188
00:06:40,420 --> 00:06:40,860
好

189
00:06:40,860 --> 00:06:41,300
这里呢

190
00:06:41,300 --> 00:06:42,920
就是我们这节课的内容

