1
00:00:00,000 --> 00:00:02,820
好,接下来我们来看第2个代码执行片段

2
00:00:02,820 --> 00:00:04,220
它是怎么样一回事

3
00:00:04,220 --> 00:00:05,740
我们直接来看代码

4
00:00:05,740 --> 00:00:09,480
刚才我们的demo1

5
00:00:09,480 --> 00:00:11,960
setTimeout它花了多久

6
00:00:11,960 --> 00:00:13,120
是不是花了10毫秒

7
00:00:13,120 --> 00:00:14,520
那么这里老师把它改为了5毫秒

8
00:00:14,520 --> 00:00:18,540
然后demo1文件读取花了多长时间

9
00:00:18,540 --> 00:00:20,880
是不是花了2毫秒

10
00:00:20,880 --> 00:00:23,280
那么这里我们假设它花了9毫秒

11
00:00:23,280 --> 00:00:24,700
为什么要假设花了9毫秒

12
00:00:24,700 --> 00:00:27,480
因为在NodeGIS里面去读取文件的速度是非常快的

13
00:00:27,480 --> 00:00:32,340
这里了 我很难去找到一个读取一个文件需要9号面的例子 其实我们可以看一下

14
00:00:32,340 --> 00:00:35,160
可以给同学们演示一下 我们去读取一个文件速度有多快

15
00:00:35,160 --> 00:00:44,640
比如说readfail.js 我们来现场演示一下读文件有多快 首先挖fs.require

16
00:00:44,640 --> 00:00:51,280
fsfs.readfail

17
00:00:52,320 --> 00:00:55,640
咱们首先来挖一个pass 因为咱们需要使用绝对路径

18
00:00:55,640 --> 00:00:57,440
requell

19
00:00:57,440 --> 00:00:59,480
pass

20
00:00:59,480 --> 00:01:01,020
好

21
00:01:01,020 --> 00:01:05,380
readfail路径是什么呀 pass.resolve

22
00:01:05,380 --> 00:01:08,200
什么

23
00:01:08,200 --> 00:01:14,600
dir 咱们的文件夹路径 下面呢 接下来下滑线

24
00:01:14,600 --> 00:01:18,440
read点 咱们来读一下 咱们来读一下 txt

25
00:01:18,680 --> 00:01:23,800
tst是不是就是helloworld呀对吧好就下来了他的编码utf

26
00:01:23,800 --> 00:01:26,100
callback

27
00:01:26,100 --> 00:01:29,680
好我们怎么看他的读取时间这里呢教同学们一个小技巧

28
00:01:29,680 --> 00:01:33,520
可能同学们之前在学习的时候学过这样一个东西console.time

29
00:01:33,520 --> 00:01:35,580
给他一个

30
00:01:35,580 --> 00:01:37,120
名字fair

31
00:01:37,120 --> 00:01:40,180
console.time

32
00:01:40,180 --> 00:01:44,020
这样就可以计算出他所执行的时间这是一个小技巧

33
00:01:46,320 --> 00:02:11,580
那

34
00:02:16,320 --> 00:02:24,540
实际上也只花了1.64毫秒

35
00:02:24,540 --> 00:02:25,460
为什么呢

36
00:02:25,460 --> 00:02:27,500
因为咱们的cpu运行速度是非常快的

37
00:02:27,500 --> 00:02:29,120
同学们你可能会说

38
00:02:29,120 --> 00:02:31,300
我把这个text文件无限的去加大

39
00:02:31,300 --> 00:02:32,140
这样不就可以了吗

40
00:02:32,140 --> 00:02:32,620
其实不行

41
00:02:32,620 --> 00:02:33,080
为什么呀

42
00:02:33,080 --> 00:02:36,540
因为cpu它的运行速度非常快

43
00:02:36,540 --> 00:02:38,920
但是当老师把这个tst文件写的非常大的时候

44
00:02:38,920 --> 00:02:39,920
老师做个测试

45
00:02:39,920 --> 00:02:41,920
我可以把tst写一兆两兆

46
00:02:41,920 --> 00:02:42,640
但是会有一个问题

47
00:02:42,640 --> 00:02:44,260
咱们的编辑器会卡死

48
00:02:44,260 --> 00:02:45,900
但是咱们的readfail还是非常快

49
00:02:45,900 --> 00:02:46,880
它远远小于9毫秒

50
00:02:46,880 --> 00:02:47,620
所以说呢

51
00:02:47,620 --> 00:02:50,240
所以说这里呢

52
00:02:50,240 --> 00:02:51,620
我很难去举出一个例子

53
00:02:51,620 --> 00:02:52,900
读取文件需要9毫秒

54
00:02:52,900 --> 00:02:54,200
但是呢我们可以讲述在一个场景

55
00:02:54,200 --> 00:02:55,860
这里呢读取文件需要9毫秒

56
00:02:55,860 --> 00:02:56,880
这样一个事情

57
00:02:56,880 --> 00:02:58,240
我们讲述它需要9毫秒

58
00:02:58,240 --> 00:02:59,760
我们怎么样去分析这样一个过程

59
00:02:59,760 --> 00:03:01,540
好

60
00:03:01,540 --> 00:03:02,980
那么呢我们同样的

61
00:03:02,980 --> 00:03:03,480
直接看图

62
00:03:03,480 --> 00:03:04,480
看图说话

63
00:03:04,480 --> 00:03:07,300
这里呢

64
00:03:07,300 --> 00:03:08,620
我们先把代码贴过来

65
00:03:15,900 --> 00:03:24,440
好 挺好的 是吧

66
00:03:24,440 --> 00:03:31,440
好 把它往下拖一点 往下稍微拖一点 可能有点不够

67
00:03:31,440 --> 00:03:34,260
代码怎么这么长啊

68
00:03:34,260 --> 00:03:37,960
好 注释刷一下

69
00:03:37,960 --> 00:03:43,700
好 差不多了

70
00:03:43,700 --> 00:03:45,300
那我们就来分析下

71
00:03:45,300 --> 00:03:45,840
在一个过程

72
00:03:45,840 --> 00:03:46,600
首先

73
00:03:46,600 --> 00:03:49,380
fs pass模块的引入

74
00:03:49,380 --> 00:03:49,940
我们不用去关注

75
00:03:49,940 --> 00:03:50,600
我们关注什么呢

76
00:03:50,600 --> 00:03:51,840
我们去读取文件的时候

77
00:03:51,840 --> 00:03:52,460
它花了9毫秒

78
00:03:52,460 --> 00:03:54,040
但是呢定时器只花了5毫秒

79
00:03:54,040 --> 00:03:55,380
此时我们怎么去分析这样一件事情

80
00:03:55,380 --> 00:03:55,940
首先

81
00:03:55,940 --> 00:03:57,240
我们是不是还是

82
00:03:57,240 --> 00:03:59,100
当它第一次执行的时候

83
00:03:59,100 --> 00:03:59,800
当前时间是几

84
00:03:59,800 --> 00:04:01,000
当前时间是不是零了

85
00:04:01,000 --> 00:04:01,480
0毫秒

86
00:04:01,480 --> 00:04:02,540
0毫秒的时候

87
00:04:02,540 --> 00:04:03,880
set timeout它会执行

88
00:04:03,880 --> 00:04:05,000
咱们是不是才把它丢进咱们的IO

89
00:04:05,000 --> 00:04:06,240
丢进IO

90
00:04:06,240 --> 00:04:07,420
它的毁掉需要5毫秒

91
00:04:07,420 --> 00:04:08,440
那么呢

92
00:04:08,440 --> 00:04:09,020
第二个

93
00:04:09,020 --> 00:04:10,380
some async option执行的时候

94
00:04:10,380 --> 00:04:11,780
是不是把咱们的fs.readfail

95
00:04:11,780 --> 00:04:13,300
也把它丢到咱们的IO里面去啊

96
00:04:13,300 --> 00:04:13,860
它需要9毫秒

97
00:04:13,860 --> 00:04:15,540
好那咱们来分析一下

98
00:04:15,540 --> 00:04:16,920
当前时间是0

99
00:04:16,920 --> 00:04:17,740
那我们来看一下

100
00:04:17,740 --> 00:04:18,600
先把时间改过5

101
00:04:18,600 --> 00:04:20,360
到第5毫秒的时候会发生什么事情

102
00:04:20,360 --> 00:04:23,420
刚才我们0毫秒的时候执行到POR

103
00:04:23,420 --> 00:04:24,000
是不是会主射

104
00:04:24,000 --> 00:04:25,140
为什么呀

105
00:04:25,140 --> 00:04:25,600
因为

106
00:04:25,600 --> 00:04:28,440
Event loop将主射

107
00:04:28,440 --> 00:04:30,540
在该阶段等待Corebox加入POR Queen

108
00:04:30,540 --> 00:04:31,580
一旦到达立即执行

109
00:04:31,580 --> 00:04:31,860
好

110
00:04:31,860 --> 00:04:34,120
那么呢我们先到5毫秒

111
00:04:34,120 --> 00:04:36,100
它是不是还主射在这里啊

112
00:04:36,100 --> 00:04:37,620
咱们的Event loop是不是还在POR这里

113
00:04:37,620 --> 00:04:39,040
那我们来看一下

114
00:04:39,040 --> 00:04:40,600
当第5毫秒的时候

115
00:04:40,600 --> 00:04:41,720
咱们的setTimeout的回调

116
00:04:41,720 --> 00:04:42,300
是不是已经过来了

117
00:04:42,300 --> 00:04:43,900
他还找一个地方插进去

118
00:04:43,900 --> 00:04:44,680
插到哪里去呢

119
00:04:44,680 --> 00:04:45,960
是被卡住了

120
00:04:45,960 --> 00:04:47,720
还是插到某一个阶段

121
00:04:47,720 --> 00:04:49,460
有没有地方插入

122
00:04:49,460 --> 00:04:51,280
那么我们来看一下下面这句话

123
00:04:51,280 --> 00:04:53,060
如果PoreQuain进入空状态

124
00:04:53,060 --> 00:04:54,340
此时我们的Pore是不是空状态

125
00:04:54,340 --> 00:04:55,520
对吧

126
00:04:55,520 --> 00:04:57,540
EventNope将检查Timer

127
00:04:57,540 --> 00:05:00,340
如果有一个或多个Timer时间已经到达

128
00:05:00,340 --> 00:05:01,400
那么5毫秒的时候

129
00:05:01,400 --> 00:05:02,260
Timer到达了没有

130
00:05:02,260 --> 00:05:03,420
是不是已经到达了

131
00:05:03,420 --> 00:05:04,400
此时Pore是不是空状态

132
00:05:04,400 --> 00:05:06,260
所以在5毫秒的时候

133
00:05:06,260 --> 00:05:08,100
setTimeout

134
00:05:08,100 --> 00:05:09,220
它会到哪里去

135
00:05:09,220 --> 00:05:09,680
到Pore吗

136
00:05:09,680 --> 00:05:10,380
咱们看后面

137
00:05:10,380 --> 00:05:12,680
如果有一个或多个timers时间已经到达

138
00:05:12,680 --> 00:05:14,620
EVNLOVE将按顺序进入timers阶段

139
00:05:14,620 --> 00:05:16,140
也就是说咱们的第一轮事件循环结束

140
00:05:16,140 --> 00:05:18,220
在第二轮事件循环的timers阶段

141
00:05:18,220 --> 00:05:20,180
它将会插入settimeout的毁定

142
00:05:20,180 --> 00:05:22,040
此时咱们的定时期会执行

143
00:05:22,040 --> 00:05:23,980
那么在第五毫秒的时候

144
00:05:23,980 --> 00:05:26,140
咱们执行定时期完成之后

145
00:05:26,140 --> 00:05:27,880
又是咱们的执行块

146
00:05:27,880 --> 00:05:28,780
它执行完成了

147
00:05:28,780 --> 00:05:29,000
好

148
00:05:29,000 --> 00:05:31,600
五毫秒的时候settimeout执行完了

149
00:05:31,600 --> 00:05:32,120
咱们来看一下

150
00:05:32,120 --> 00:05:33,800
当时间为九毫秒的时候

151
00:05:33,800 --> 00:05:35,460
会发生什么事情

152
00:05:35,460 --> 00:05:37,780
此时在第五毫秒的时候

153
00:05:37,780 --> 00:05:39,820
timers已经执行完了

154
00:05:39,820 --> 00:05:41,200
是不是到IO 此时没有IO 对吧

155
00:05:41,200 --> 00:05:42,980
Pol呢 是不是又会继续阻射呀

156
00:05:42,980 --> 00:05:44,820
Pol它会一直持续的阻射

157
00:05:44,820 --> 00:05:46,560
好 阻射到底9毫秒

158
00:05:46,560 --> 00:05:48,660
到底9毫秒的时候

159
00:05:48,660 --> 00:05:49,720
咱们的

160
00:05:49,720 --> 00:05:51,400
如果Pol空为空

161
00:05:51,400 --> 00:05:52,380
它一直会阻射

162
00:05:52,380 --> 00:05:53,520
等待Callback加入Pol

163
00:05:53,520 --> 00:05:54,220
咱们9毫秒的时候

164
00:05:54,220 --> 00:05:55,880
FS.Radfile是不是会加入Pol

165
00:05:55,880 --> 00:05:57,320
这是第二轮事件循环的Pol阶段

166
00:05:57,320 --> 00:05:58,820
此时呢

167
00:05:58,820 --> 00:05:59,900
是不是FSRadfile

168
00:05:59,900 --> 00:06:01,000
它的Callback就可以去执行

169
00:06:01,000 --> 00:06:02,300
执行这样一个方法

170
00:06:02,300 --> 00:06:03,180
执行完成之后

171
00:06:03,180 --> 00:06:03,660
丢进去

172
00:06:03,660 --> 00:06:04,300
它会执行多久

173
00:06:04,300 --> 00:06:05,340
是不是也会卡20毫秒

174
00:06:05,340 --> 00:06:06,200
所以呢

175
00:06:06,200 --> 00:06:09,800
它就会执行9至29毫秒

176
00:06:09,800 --> 00:06:12,620
那么stimeout第五毫秒的时候就已经执行了

177
00:06:12,620 --> 00:06:14,560
这里就是这一段代码它那个执行过程

178
00:06:14,560 --> 00:06:22,700
那么我们和前面demo依赖对比一下它们的区别是什么

179
00:06:22,700 --> 00:06:27,960
首先咱们这里咱们的定时器是不是它比咱们的文件

180
00:06:27,960 --> 00:06:29,280
real file它时间长

181
00:06:29,280 --> 00:06:31,720
咱们的stimeout会先执行

182
00:06:31,720 --> 00:06:34,700
但是如果咱们的fsreal file

183
00:06:34,700 --> 00:06:38,320
callback先执行的情况下咱们fs的real file是不是会阻塞

184
00:06:38,320 --> 00:06:40,580
会阻射咱们色的timeout的执行呢

185
00:06:40,580 --> 00:06:41,100
同学们

186
00:06:41,100 --> 00:06:41,380
对吧

187
00:06:41,380 --> 00:06:43,140
这里呢就是demo1和demo2的一个区别

188
00:06:43,140 --> 00:06:44,620
那我们来回顾一下咱们刚才

189
00:06:44,620 --> 00:06:46,100
demo2是怎么去执行的

190
00:06:46,100 --> 00:06:47,220
首先

191
00:06:47,220 --> 00:06:48,420
我们的时间

192
00:06:48,420 --> 00:06:50,040
为0

193
00:06:50,040 --> 00:06:52,220
那么色的timeout和fsreal file

194
00:06:52,220 --> 00:06:53,160
是不是同时加入咱们的io

195
00:06:53,160 --> 00:06:53,640
对吧

196
00:06:53,640 --> 00:06:54,360
同时加入io

197
00:06:54,360 --> 00:06:55,940
那么当时间为5的时候

198
00:06:55,940 --> 00:06:57,060
时间为5的时候

199
00:06:57,060 --> 00:06:58,620
是不是pro

200
00:06:58,620 --> 00:06:59,540
pro它阻射

201
00:06:59,540 --> 00:07:00,560
首先时间为0阻射

202
00:07:00,560 --> 00:07:01,440
等到第5毫秒

203
00:07:01,440 --> 00:07:02,140
色的timeout

204
00:07:02,140 --> 00:07:02,740
加入

205
00:07:02,740 --> 00:07:03,500
加入哪里

206
00:07:03,500 --> 00:07:04,700
加入第二轮世界熊的timer

207
00:07:04,700 --> 00:07:05,700
咱们看这段话

208
00:07:05,700 --> 00:07:06,660
看这段话

209
00:07:06,660 --> 00:07:07,700
好

210
00:07:07,700 --> 00:07:10,200
那么当settimeout执行完成之后

211
00:07:10,200 --> 00:07:11,200
咱们丢进去

212
00:07:11,200 --> 00:07:12,400
i5s realfail

213
00:07:12,400 --> 00:07:17,900
咱们呢settimeout执行完成之后是不是继续往下走

214
00:07:17,900 --> 00:07:20,900
第二个事情继续完到io 没有到pro 注射对吧

215
00:07:20,900 --> 00:07:23,000
一直注射了第几毫秒第9毫秒

216
00:07:23,000 --> 00:07:24,200
好 注射完成之后

217
00:07:24,200 --> 00:07:25,900
i5s realfail callback

218
00:07:25,900 --> 00:07:27,000
丢进去

219
00:07:27,000 --> 00:07:27,700
执行

220
00:07:27,700 --> 00:07:28,700
好 执行完

221
00:07:28,700 --> 00:07:29,400
走

222
00:07:29,400 --> 00:07:30,700
滚蛋对吧

223
00:07:30,700 --> 00:07:33,100
那么这里就是咱们diamout里面

224
00:07:33,100 --> 00:07:34,900
他这样一种情况整个的一个执行过程

225
00:07:34,900 --> 00:07:36,200
这几个内容就到这里

