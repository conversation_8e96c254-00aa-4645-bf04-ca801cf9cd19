1
00:00:00,000 --> 00:00:04,760
熟悉了留言本案例的基本的业务之后

2
00:00:04,760 --> 00:00:06,440
我们接下来来继续实现这个功能

3
00:00:06,440 --> 00:00:07,980
主要我们要做的是两部分

4
00:00:07,980 --> 00:00:09,560
一个是服务端的接口的开发

5
00:00:09,560 --> 00:00:10,820
另外一个就是客户端

6
00:00:10,820 --> 00:00:12,120
页面效果以及接口的对接

7
00:00:12,120 --> 00:00:13,980
首先我们要做的就是服务端的实现

8
00:00:13,980 --> 00:00:15,280
对于服务端来说

9
00:00:15,280 --> 00:00:17,220
其实核心的业务功能主要包含两部分

10
00:00:17,220 --> 00:00:18,920
一个就是类型的定义

11
00:00:18,920 --> 00:00:20,180
另外一个就是数据的解析

12
00:00:20,180 --> 00:00:21,420
具体来说的话

13
00:00:21,420 --> 00:00:22,500
我们要做这么几个步骤

14
00:00:22,500 --> 00:00:24,000
首先我们要出数化项目

15
00:00:24,000 --> 00:00:25,000
然后定义类型

16
00:00:25,000 --> 00:00:25,860
然后做数据的解析

17
00:00:25,860 --> 00:00:27,340
对于数据解析来说

18
00:00:27,340 --> 00:00:29,220
主要包括如下的这四个方面

19
00:00:29,220 --> 00:00:30,440
前三个的话

20
00:00:30,440 --> 00:00:32,480
就是不同的数据的查询的工作

21
00:00:32,480 --> 00:00:34,700
分别是留言板的列表信息

22
00:00:34,700 --> 00:00:35,740
还有有情电机的信息

23
00:00:35,740 --> 00:00:37,220
还有就是天气的信息

24
00:00:37,220 --> 00:00:38,160
除此之外还有一个

25
00:00:38,160 --> 00:00:39,000
就是对留言板来说

26
00:00:39,000 --> 00:00:41,120
也支持添加功能

27
00:00:41,120 --> 00:00:42,140
这涉及到了数据的并更

28
00:00:42,140 --> 00:00:43,740
所以说我们主要就涉及到

29
00:00:43,740 --> 00:00:44,400
这四个方面

30
00:00:44,400 --> 00:00:46,420
核心的数据类型

31
00:00:46,420 --> 00:00:47,420
在这我们就提供好了

32
00:00:47,420 --> 00:00:49,180
主要就这里边有查询有并更

33
00:00:49,180 --> 00:00:50,360
这个数据的格式

34
00:00:50,360 --> 00:00:51,420
主要有三个

35
00:00:51,420 --> 00:00:52,760
子字段

36
00:00:52,760 --> 00:00:54,560
这待会的话我们会进行完善

37
00:00:54,560 --> 00:00:56,360
然后的话就是数据的对接业务

38
00:00:56,360 --> 00:00:57,840
对接的话就上面只要定义好类型

39
00:00:57,840 --> 00:00:59,880
下面就按照这个格式提供数据就可以了

40
00:00:59,880 --> 00:01:01,860
最终我们提供的reserve函数中

41
00:01:01,860 --> 00:01:03,240
就是返回这种格式的数据

42
00:01:03,240 --> 00:01:03,900
这就可以

43
00:01:03,900 --> 00:01:05,520
那接下来我们就着重的

44
00:01:05,520 --> 00:01:07,280
把这两方面的工作来实现一下

45
00:01:07,280 --> 00:01:08,600
那关于这个初始化项目

46
00:01:08,600 --> 00:01:10,140
这个我们已经帮大家做好了

47
00:01:10,140 --> 00:01:11,320
因为之前我们在读案例的时候

48
00:01:11,320 --> 00:01:12,960
已经把这个工作做过很多遍了

49
00:01:12,960 --> 00:01:13,960
所以说这我们就直接用

50
00:01:13,960 --> 00:01:15,380
那这个项目的话

51
00:01:15,380 --> 00:01:18,040
就是我帮大家初始化好的一个后台项目

52
00:01:18,040 --> 00:01:18,520
那就是这个

53
00:01:18,520 --> 00:01:20,640
那在这个项目当中

54
00:01:20,640 --> 00:01:21,600
我们看一下

55
00:01:21,600 --> 00:01:23,600
我们的入口文件是index.js

56
00:01:23,600 --> 00:01:26,400
这里边我们关于类型的定义

57
00:01:26,400 --> 00:01:28,040
和resolver函数都是空着的

58
00:01:28,040 --> 00:01:30,200
但是我们需要的数据都帮大家提供好了

59
00:01:30,200 --> 00:01:32,140
我们需要的数据有这三部分数据

60
00:01:32,140 --> 00:01:34,120
首先是留言板的数据

61
00:01:34,120 --> 00:01:35,260
然后还有是清晰的数据

62
00:01:35,260 --> 00:01:36,520
还有有行连接的数据

63
00:01:36,520 --> 00:01:38,460
都通过文件的方式提供好了

64
00:01:38,460 --> 00:01:40,840
这里边我们就先直观的来看一看

65
00:01:40,840 --> 00:01:42,760
这几个文件的分别是如何提供数据的

66
00:01:42,760 --> 00:01:43,940
这个我们就不带着大家写了

67
00:01:43,940 --> 00:01:45,680
因为之前我们已经写过了

68
00:01:45,680 --> 00:01:49,040
首先我们看留言板的数据

69
00:01:49,040 --> 00:01:51,020
就是data.file这个文件

70
00:01:51,020 --> 00:01:52,520
把它打开

71
00:01:52,520 --> 00:01:53,320
这里边实际上

72
00:01:53,320 --> 00:01:54,680
就是从data.danson中

73
00:01:54,680 --> 00:01:56,680
来读取列表数据

74
00:01:56,680 --> 00:01:58,780
这是留言板的数据

75
00:01:58,780 --> 00:01:59,900
当然这里边是没有格式的

76
00:01:59,900 --> 00:02:00,520
你格式化一下

77
00:02:00,520 --> 00:02:01,340
可以更直观的看一下

78
00:02:01,340 --> 00:02:03,240
实际上是一个对象数组

79
00:02:03,240 --> 00:02:04,180
这是我们的数据

80
00:02:04,180 --> 00:02:07,580
然后关键就是这里边

81
00:02:07,580 --> 00:02:09,020
data.danson里边

82
00:02:09,020 --> 00:02:09,580
做的是什么事情

83
00:02:09,580 --> 00:02:10,540
实际上一方面

84
00:02:10,540 --> 00:02:11,600
要读取文件的数据

85
00:02:11,600 --> 00:02:12,220
另一方面

86
00:02:12,220 --> 00:02:13,720
要写入文件的数据

87
00:02:13,720 --> 00:02:14,280
因为我们在

88
00:02:14,280 --> 00:02:15,460
添加留言板信息的时候

89
00:02:15,460 --> 00:02:16,400
要重新的

90
00:02:16,400 --> 00:02:17,400
把新加的这条记录

91
00:02:17,400 --> 00:02:18,460
写入原始的

92
00:02:18,460 --> 00:02:19,700
data.danson文件中

93
00:02:19,700 --> 00:02:20,500
所以说这里边

94
00:02:20,500 --> 00:02:21,100
主要有两个方法

95
00:02:21,100 --> 00:02:21,900
一个是读文件

96
00:02:21,900 --> 00:02:22,520
一个是写文件

97
00:02:22,520 --> 00:02:24,820
好这就是Node.js中的文件读写

98
00:02:24,820 --> 00:02:26,240
这个我们就不带着写了

99
00:02:26,240 --> 00:02:27,640
这是关于留言板的数据

100
00:02:27,640 --> 00:02:31,340
然后我们看天气的数据

101
00:02:31,340 --> 00:02:32,280
对于天气数据来说

102
00:02:32,280 --> 00:02:34,040
我们这里边调用了一个第三方的接口

103
00:02:34,040 --> 00:02:34,760
我们看一下

104
00:02:34,760 --> 00:02:37,300
天气数据的话是-API结尾的

105
00:02:37,300 --> 00:02:37,800
我们看一下是谁

106
00:02:37,800 --> 00:02:40,100
这里边我们使用了actress

107
00:02:40,100 --> 00:02:42,880
来调用一个中央气象局的天气接口

108
00:02:42,880 --> 00:02:44,140
注意一下后边这个是北京

109
00:02:44,140 --> 00:02:44,960
我们这是固定写死的

110
00:02:44,960 --> 00:02:45,960
这是北京的城市编号

111
00:02:45,960 --> 00:02:47,660
根据的编号我们可以查询出

112
00:02:47,660 --> 00:02:48,620
对应的天气的数据

113
00:02:48,620 --> 00:02:50,500
然后把这个数据返回两项数据

114
00:02:50,500 --> 00:02:51,600
分别是天气和温度

115
00:02:51,600 --> 00:02:53,620
这是我们的天气数据的对接

116
00:02:53,620 --> 00:02:54,920
这个也比较简单

117
00:02:54,920 --> 00:02:57,040
然后最后一点就是有情链接

118
00:02:57,040 --> 00:02:58,520
这个数据我们是写死的

119
00:02:58,520 --> 00:02:59,640
在这个-db当中

120
00:02:59,640 --> 00:03:01,060
就是这一个列表

121
00:03:01,060 --> 00:03:02,540
实际上你要多复杂一点的话

122
00:03:02,540 --> 00:03:03,860
可以把这个数据放到数据库当中

123
00:03:03,860 --> 00:03:05,800
在这把它取出来也是可以的

124
00:03:05,800 --> 00:03:08,140
这个流程和读文件其实非常类似

125
00:03:08,140 --> 00:03:09,080
只是一部操博

126
00:03:09,080 --> 00:03:10,800
这是我们需要的数据

127
00:03:10,800 --> 00:03:11,780
主要就是三部分

128
00:03:11,780 --> 00:03:13,260
然后我们把这三部分数据

129
00:03:13,260 --> 00:03:15,000
提供给contents的

130
00:03:15,000 --> 00:03:15,680
在这提供一下

131
00:03:15,680 --> 00:03:17,420
然后把它整合到这个参数中

132
00:03:17,420 --> 00:03:18,480
就是Apollo Server当中

133
00:03:18,480 --> 00:03:19,100
第三个参数

134
00:03:19,100 --> 00:03:20,960
然后我们就可以在resolver函数中

135
00:03:20,960 --> 00:03:23,580
得到这些数据源的对象了

136
00:03:23,580 --> 00:03:24,060
就这个意思

137
00:03:24,060 --> 00:03:25,980
在这我们可以稍微备注一下

138
00:03:25,980 --> 00:03:27,880
这里边实际上是提供了什么

139
00:03:27,880 --> 00:03:32,100
提供数据源操作的对象

140
00:03:32,100 --> 00:03:33,840
有了对象之后

141
00:03:33,840 --> 00:03:36,020
我们再操作数据就比较方便了

142
00:03:36,020 --> 00:03:37,580
这是关于数据

143
00:03:37,580 --> 00:03:39,920
然后的话我们需要定义相关的类型

144
00:03:39,920 --> 00:03:41,400
我们看一下这里边都需要哪些类型

145
00:03:41,400 --> 00:03:43,680
首先我们需要的是查询类型

146
00:03:43,680 --> 00:03:44,540
这个是必须的

147
00:03:44,540 --> 00:03:46,320
查询类型的话我们要查什么

148
00:03:46,320 --> 00:03:48,540
实际上我们在这里边需要

149
00:03:48,540 --> 00:03:50,220
几个字段呢

150
00:03:50,220 --> 00:03:52,080
实际上我们只需要一个

151
00:03:52,080 --> 00:03:52,840
就是infer

152
00:03:52,840 --> 00:03:54,540
那它的类型是什么类型呢

153
00:03:54,540 --> 00:03:55,480
我们就给它定义一个类型

154
00:03:55,480 --> 00:03:55,880
就叫data

155
00:03:55,880 --> 00:03:57,440
那这data类型

156
00:03:57,440 --> 00:03:58,400
我们在上面再提供一下

157
00:03:58,400 --> 00:03:59,240
那就是tether

158
00:03:59,240 --> 00:04:00,340
这就是data类型

159
00:04:00,340 --> 00:04:01,780
这里边有什么呢

160
00:04:01,780 --> 00:04:03,020
有三个字段

161
00:04:03,020 --> 00:04:03,860
分别是

162
00:04:03,860 --> 00:04:04,740
一个是list

163
00:04:04,740 --> 00:04:07,000
list的类型是谁呢

164
00:04:07,000 --> 00:04:08,900
这里边有留言板的数据

165
00:04:08,900 --> 00:04:09,820
留言板我们就

166
00:04:09,820 --> 00:04:10,660
这样给它请个名字

167
00:04:10,660 --> 00:04:11,940
comment

168
00:04:11,940 --> 00:04:14,120
这是留言板的数据

169
00:04:14,120 --> 00:04:14,680
是一个类别

170
00:04:14,680 --> 00:04:16,440
然后还有就是有情连接

171
00:04:16,440 --> 00:04:17,780
这个叫link

172
00:04:17,780 --> 00:04:18,980
它也是一个类型

173
00:04:18,980 --> 00:04:20,820
待会的话我们需要定义一个叫link

174
00:04:20,820 --> 00:04:22,660
然后还有谁呢

175
00:04:22,660 --> 00:04:23,300
还有就是天气

176
00:04:23,300 --> 00:04:24,460
weather

177
00:04:24,460 --> 00:04:25,840
这个数据的话

178
00:04:25,840 --> 00:04:27,160
我们也要提供一个类型

179
00:04:27,160 --> 00:04:28,220
就叫weather

180
00:04:28,220 --> 00:04:31,440
这是我们查询用到的相关类型

181
00:04:31,440 --> 00:04:33,300
然后我们定义一下这里边的这个子类型

182
00:04:33,300 --> 00:04:35,760
分别是comment

183
00:04:35,760 --> 00:04:38,680
这里边都有什么信息呢

184
00:04:38,680 --> 00:04:39,320
有这个euter name

185
00:04:39,320 --> 00:04:42,200
这个是子病类型

186
00:04:42,200 --> 00:04:44,680
然后还有什么类型呢

187
00:04:44,680 --> 00:04:45,960
content的内容

188
00:04:45,960 --> 00:04:47,740
也是子病

189
00:04:47,740 --> 00:04:49,200
还有一个日期

190
00:04:49,200 --> 00:04:49,880
data

191
00:04:49,880 --> 00:04:51,800
这个我们也用spin

192
00:04:51,800 --> 00:04:53,100
因为我们存的是毫秒数

193
00:04:53,100 --> 00:04:54,460
直接给它存成字乎上

194
00:04:54,460 --> 00:04:56,820
然后这是comment

195
00:04:56,820 --> 00:04:57,800
然后呢

196
00:04:57,800 --> 00:04:58,280
也还有

197
00:04:58,280 --> 00:04:59,300
type

198
00:04:59,300 --> 00:05:00,140
还有一个link

199
00:05:00,140 --> 00:05:02,240
连接的话呢

200
00:05:02,240 --> 00:05:03,160
分别有这个name

201
00:05:03,160 --> 00:05:04,660
还有一个ur地址

202
00:05:04,660 --> 00:05:06,440
ur2

203
00:05:06,440 --> 00:05:07,360
这个也是spin

204
00:05:07,360 --> 00:05:08,540
最后还有一个

205
00:05:08,540 --> 00:05:09,200
就是天气

206
00:05:09,200 --> 00:05:11,260
weather

207
00:05:11,260 --> 00:05:12,900
天气的话

208
00:05:12,900 --> 00:05:13,340
我们这儿呢

209
00:05:13,340 --> 00:05:14,100
只提供两项数据

210
00:05:14,100 --> 00:05:14,640
一个呢

211
00:05:14,640 --> 00:05:15,640
就是天气的具体信息

212
00:05:15,640 --> 00:05:16,600
就是wea

213
00:05:16,600 --> 00:05:17,520
这个表示天气

214
00:05:17,520 --> 00:05:19,140
然后还有一个温度

215
00:05:19,140 --> 00:05:20,860
Time也是同样

216
00:05:20,860 --> 00:05:22,320
这是我们需要的

217
00:05:22,320 --> 00:05:23,720
查询相关的所有的类型

218
00:05:23,720 --> 00:05:26,200
然后我们要对接一下

219
00:05:26,200 --> 00:05:27,220
这里边的数据了

220
00:05:27,220 --> 00:05:27,920
怎么对接呢

221
00:05:27,920 --> 00:05:28,820
我们在这个query当中

222
00:05:28,820 --> 00:05:30,320
提供一个resol函数

223
00:05:30,320 --> 00:05:32,300
要解析是哪个字段呢

224
00:05:32,300 --> 00:05:33,700
要解析的就是这个infer

225
00:05:33,700 --> 00:05:35,680
所以说在这我们提供一个infer函数

226
00:05:35,680 --> 00:05:39,700
这里边我们分别要获取一下

227
00:05:39,700 --> 00:05:41,120
上面的三部分数据

228
00:05:41,120 --> 00:05:42,900
由于这里边有些数据是一部的

229
00:05:42,900 --> 00:05:43,580
所以说我们这个函数

230
00:05:43,580 --> 00:05:44,400
应该是aping函数

231
00:05:44,400 --> 00:05:47,340
这里边我们通过wait来得到数据

232
00:05:47,340 --> 00:05:50,260
首先第一份数据就是这边数据

233
00:05:50,260 --> 00:05:51,500
就是留言板的这边数据

234
00:05:51,500 --> 00:05:54,020
它通过wait来获取

235
00:05:54,020 --> 00:05:58,800
然后上边通过context拿到数据源

236
00:05:58,800 --> 00:06:00,800
所以说我们前边两个探数给它填充一下

237
00:06:00,800 --> 00:06:01,920
parent还有ax

238
00:06:01,920 --> 00:06:03,700
还有就是context

239
00:06:03,700 --> 00:06:07,020
在这通过context来访问数据源

240
00:06:07,020 --> 00:06:11,440
在这里边要访问到一个对象

241
00:06:11,440 --> 00:06:12,920
那这个对象的名字是谁呢

242
00:06:12,920 --> 00:06:14,020
主意就是comment

243
00:06:14,020 --> 00:06:15,480
所以说把这个名字拿过来

244
00:06:15,480 --> 00:06:17,320
然后调用它的有方法叫geteditor

245
00:06:17,320 --> 00:06:18,400
这样就得到了我们需要的数据

246
00:06:18,400 --> 00:06:20,940
但是用这种方式得到的数据

247
00:06:20,940 --> 00:06:22,060
实际上它是字符串

248
00:06:22,060 --> 00:06:24,240
但是我们最终希望它是什么呢

249
00:06:24,240 --> 00:06:27,040
最终希望它是一个列表形式的数据

250
00:06:27,040 --> 00:06:28,940
所以说要把字符串转成一个对象

251
00:06:28,940 --> 00:06:29,780
怎么转呢

252
00:06:29,780 --> 00:06:30,720
我们可以这样做

253
00:06:30,720 --> 00:06:31,960
在这儿可以叫RET

254
00:06:31,960 --> 00:06:33,880
然后我们再改个名字

255
00:06:33,880 --> 00:06:34,700
这个叫list

256
00:06:34,700 --> 00:06:35,740
然后调用一个方法叫

257
00:06:35,740 --> 00:06:37,000
詹分点Path

258
00:06:37,000 --> 00:06:37,720
做一个转换

259
00:06:37,720 --> 00:06:39,880
把RET转成这个对象

260
00:06:39,880 --> 00:06:40,980
因为它本身是字符串

261
00:06:40,980 --> 00:06:44,260
这是留言版数据

262
00:06:44,260 --> 00:06:46,600
这个是列表数据

263
00:06:46,600 --> 00:06:48,680
另外的话我们还需要什么数据呢

264
00:06:48,680 --> 00:06:50,560
还需要的就是有情链接

265
00:06:50,560 --> 00:06:52,960
数据

266
00:06:52,960 --> 00:06:54,280
这个数据的话我们在这

267
00:06:54,280 --> 00:06:54,640
link

268
00:06:54,640 --> 00:06:58,160
还是同样的我们加上await

269
00:06:58,160 --> 00:06:59,500
然后后边context

270
00:06:59,500 --> 00:07:01,520
然后这里边有个link

271
00:07:01,520 --> 00:07:03,540
同样的也要调用get data

272
00:07:03,540 --> 00:07:05,900
这样拿到了有情链接的数据

273
00:07:05,900 --> 00:07:07,320
然后还有谁啊

274
00:07:07,320 --> 00:07:08,560
还有就是听起的数据

275
00:07:08,560 --> 00:07:11,440
这里边有weather

276
00:07:11,440 --> 00:07:13,220
await

277
00:07:13,220 --> 00:07:15,740
这里边还是通过context

278
00:07:15,740 --> 00:07:17,560
拿到这个天气

279
00:07:17,560 --> 00:07:18,320
weather

280
00:07:18,320 --> 00:07:18,880
然后呢

281
00:07:18,880 --> 00:07:20,660
这样的话

282
00:07:20,660 --> 00:07:21,620
我们把三部分数据呢

283
00:07:21,620 --> 00:07:22,180
都拿到了

284
00:07:22,180 --> 00:07:22,920
拿到之后呢

285
00:07:22,920 --> 00:07:23,700
最后我们把它返回

286
00:07:23,700 --> 00:07:24,220
这就可以

287
00:07:24,220 --> 00:07:24,980
在这呢

288
00:07:24,980 --> 00:07:25,520
直接去written

289
00:07:25,520 --> 00:07:29,360
written的一个是list

290
00:07:29,360 --> 00:07:31,580
后边这个名字呢

291
00:07:31,580 --> 00:07:32,520
我们也给它写上

292
00:07:32,520 --> 00:07:33,360
对应的

293
00:07:33,360 --> 00:07:33,820
然后呢

294
00:07:33,820 --> 00:07:34,460
还有这个link

295
00:07:34,460 --> 00:07:35,960
也是link

296
00:07:35,960 --> 00:07:37,440
还有一个就是weather

297
00:07:37,440 --> 00:07:39,060
这对接上weather

298
00:07:39,060 --> 00:07:40,520
好

299
00:07:40,520 --> 00:07:41,140
那到此为止呢

300
00:07:41,140 --> 00:07:41,660
这个类型

301
00:07:41,660 --> 00:07:42,780
所对应的这个readover函数

302
00:07:42,780 --> 00:07:43,900
我们也提供好了

303
00:07:43,900 --> 00:07:44,960
然后的话

304
00:07:44,960 --> 00:07:45,460
我们去启动

305
00:07:45,460 --> 00:07:46,480
看效果

306
00:07:46,480 --> 00:07:48,200
把原来的这个给它关掉

307
00:07:48,200 --> 00:07:50,140
然后我们重新打开这个引领行

308
00:07:50,140 --> 00:07:52,120
在这去运行

309
00:07:52,120 --> 00:07:53,060
node

310
00:07:53,060 --> 00:07:53,800
index

311
00:07:53,800 --> 00:07:54,340
然后回车

312
00:07:54,340 --> 00:07:57,100
这样就启动成功了

313
00:07:57,100 --> 00:07:57,940
然后我们测试一下

314
00:07:57,940 --> 00:07:59,060
看一下这三部分数据

315
00:07:59,060 --> 00:08:00,220
能不能查询到

316
00:08:00,220 --> 00:08:01,220
所以说我们在浏览器中

317
00:08:01,220 --> 00:08:02,760
再打开一个新的标签

318
00:08:02,760 --> 00:08:03,700
在这我们做一个查询

319
00:08:03,700 --> 00:08:05,840
首先我们要查询的是

320
00:08:05,840 --> 00:08:08,660
infer中的列表数据

321
00:08:08,660 --> 00:08:11,640
注意infer后面是没有冒号的

322
00:08:11,640 --> 00:08:12,680
直接加这个滑过号

323
00:08:12,680 --> 00:08:14,520
然后要查询的是list

324
00:08:14,520 --> 00:08:16,980
List的里边有什么呢

325
00:08:16,980 --> 00:08:17,400
UderName

326
00:08:17,400 --> 00:08:18,620
Content

327
00:08:18,620 --> 00:08:20,240
还有一个Thate

328
00:08:20,240 --> 00:08:21,380
然后我们点查询

329
00:08:21,380 --> 00:08:23,160
好 这样的话就得到了

330
00:08:23,160 --> 00:08:24,040
我们刚才所提供的数据

331
00:08:24,040 --> 00:08:24,960
然后还有两部分

332
00:08:24,960 --> 00:08:25,780
一块再查一下

333
00:08:25,780 --> 00:08:26,780
还有一个是Link

334
00:08:26,780 --> 00:08:29,900
这呢是LName

335
00:08:29,900 --> 00:08:31,620
还有呢是LUIL

336
00:08:31,620 --> 00:08:33,700
然后呢我们再点查询

337
00:08:33,700 --> 00:08:34,780
这是第二部分数据

338
00:08:34,780 --> 00:08:35,980
然后最后一项呢就是天体

339
00:08:35,980 --> 00:08:37,380
Whether

340
00:08:37,380 --> 00:08:38,940
这里边呢有这个WAA

341
00:08:38,940 --> 00:08:39,820
还有呢是Temp

342
00:08:39,820 --> 00:08:41,180
然后再点查询

343
00:08:41,180 --> 00:08:42,860
好 第三部分数据也出来了

344
00:08:42,860 --> 00:08:44,020
好 那到此为止的话呢

345
00:08:44,020 --> 00:08:45,420
这就证明我们刚才服务端的

346
00:08:45,420 --> 00:08:47,100
查询相关的逻辑就完成了

347
00:08:47,100 --> 00:08:48,140
主要就包括两部分

348
00:08:48,140 --> 00:08:49,640
一个就是类型的定义

349
00:08:49,640 --> 00:08:51,180
另外一个就是对应的

350
00:08:51,180 --> 00:08:52,340
锐头版函数的数据解析

351
00:08:52,340 --> 00:08:55,020
这是关于服务端的

352
00:08:55,020 --> 00:08:57,100
关于查询相关的具体的实现

353
00:08:57,100 --> 00:08:59,520
主要就完成了前三部分

354
00:08:59,520 --> 00:09:00,540
这个数据的提供

355
00:09:00,540 --> 00:09:02,160
这个工作我们就先做到这里

