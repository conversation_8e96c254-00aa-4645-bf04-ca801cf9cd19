1
00:00:00,000 --> 00:00:05,380
好 那么我们接下来就看一下路由怎么去设计然后呢怎么样去给咱们的数据库去添加数据

2
00:00:05,380 --> 00:00:11,000
那么首先我们来看一下我们项目初始化的一个路由这是我们模模板所提供的一个路由

3
00:00:11,000 --> 00:00:18,180
那么按照我的一个开玩习惯的首先我会把它乱起来看一下咱们项目是不是能够跑通这里呢也避免我们代码编写完成之后不知道啊出现些诱来错误

4
00:00:18,180 --> 00:00:22,280
好 那么我们项目启动之后我们来访问一下我们的根目录

5
00:00:22,280 --> 00:00:29,440
我们来访问一下localhost 7001大家可以看到嗨 1GG说明我们的路由现在是跑通的

6
00:00:29,700 --> 00:00:33,280
好 那么接下来我们就来设计一下我们的一个路由

7
00:00:33,280 --> 00:00:34,060
首先呢

8
00:00:34,060 --> 00:00:36,860
我们现在是不是要实现增删改查对吧

9
00:00:36,860 --> 00:00:38,920
增删改查

10
00:00:38,920 --> 00:00:40,460
那么四个API够吗

11
00:00:40,460 --> 00:00:41,740
大家有四个够吗

12
00:00:41,740 --> 00:00:44,040
增一个 删一个 改一个 查一个

13
00:00:44,040 --> 00:00:45,320
够不够

14
00:00:45,320 --> 00:00:47,360
比如说增 我去添加一条数据

15
00:00:47,360 --> 00:00:49,660
删呢 对吧 传一个ID 然后把它给删掉

16
00:00:49,660 --> 00:00:51,980
改呢 也是传一个ID 然后把它给改掉

17
00:00:51,980 --> 00:00:52,740
查呢

18
00:00:52,740 --> 00:00:56,060
那么我们查是不是封面两种情况

19
00:00:56,060 --> 00:00:57,600
查是不是封面两种情况

20
00:00:57,600 --> 00:00:59,400
那么第一种呢 是不是查一条

21
00:00:59,700 --> 00:01:02,340
查一条

22
00:01:02,340 --> 00:01:05,000
还有一种情况呢

23
00:01:05,000 --> 00:01:06,980
是不是咱们只要一get就可以查全部

24
00:01:06,980 --> 00:01:08,380
好

25
00:01:08,380 --> 00:01:09,760
那么其实查的分为两种情况

26
00:01:09,760 --> 00:01:11,580
所以说呢我们接口需要去写五个

27
00:01:11,580 --> 00:01:12,980
那么同学们来思考一下

28
00:01:12,980 --> 00:01:14,660
比如说我们通过resif去设计的话

29
00:01:14,660 --> 00:01:16,560
我们是不是要写五次啊

30
00:01:16,560 --> 00:01:17,900
这样好不好

31
00:01:17,900 --> 00:01:19,220
这样好不好

32
00:01:19,220 --> 00:01:20,920
比如说我们post的put delete

33
00:01:20,920 --> 00:01:22,800
这样其实不好

34
00:01:22,800 --> 00:01:24,020
那么我们之前是不是介绍过

35
00:01:24,020 --> 00:01:26,180
有一种比较好的方式是什么呀

36
00:01:26,180 --> 00:01:27,480
是不是咱们一机记里面

37
00:01:27,480 --> 00:01:28,860
它提供了一个resource这样一个方法

38
00:01:28,860 --> 00:01:29,680
那我们来看一下它一个

39
00:01:29,680 --> 00:01:31,520
我们来看一下1GG它的一个官网

40
00:01:31,520 --> 00:01:33,260
在router里面

41
00:01:33,260 --> 00:01:34,680
我们来看一下中文

42
00:01:34,680 --> 00:01:36,600
resource

43
00:01:36,600 --> 00:01:37,840
大家可以看到

44
00:01:37,840 --> 00:01:39,320
我们通过router.resource

45
00:01:39,320 --> 00:01:40,600
是不是可以帮我们自动的去生成

46
00:01:40,600 --> 00:01:41,180
我们一个API

47
00:01:41,180 --> 00:01:43,680
咱们一个resif风格的一个API

48
00:01:43,680 --> 00:01:43,920
对吧

49
00:01:43,920 --> 00:01:45,600
所以我们就来使用它

50
00:01:45,600 --> 00:01:47,980
那么这一张表带还记得吗

51
00:01:47,980 --> 00:01:49,360
我们的增上改查分别对于哪几个

52
00:01:49,360 --> 00:01:50,180
首先

53
00:01:50,180 --> 00:01:51,640
改put

54
00:01:51,640 --> 00:01:51,960
对吧

55
00:01:51,960 --> 00:01:52,440
update

56
00:01:52,440 --> 00:01:54,280
对于咱们控制器里面update的方法

57
00:01:54,280 --> 00:01:55,820
那么delete也很好

58
00:01:55,820 --> 00:01:56,280
很好理解

59
00:01:56,280 --> 00:01:56,880
对于我们的

60
00:01:56,880 --> 00:01:58,440
destroy

61
00:01:58,440 --> 00:01:58,780
对吧

62
00:01:58,780 --> 00:01:59,140
销毁

63
00:01:59,140 --> 00:01:59,740
post呢

64
00:01:59,740 --> 00:02:00,780
post是不是对应我们的create

65
00:02:00,780 --> 00:02:01,360
也就是新增

66
00:02:01,360 --> 00:02:02,500
增

67
00:02:02,500 --> 00:02:02,980
感

68
00:02:02,980 --> 00:02:03,500
酸

69
00:02:03,500 --> 00:02:04,440
那么还有查呢

70
00:02:04,440 --> 00:02:06,000
我们还要查

71
00:02:06,000 --> 00:02:06,540
其实这里呢

72
00:02:06,540 --> 00:02:06,840
get

73
00:02:06,840 --> 00:02:07,540
咱们的get

74
00:02:07,540 --> 00:02:07,960
index

75
00:02:07,960 --> 00:02:08,980
大家看到传入参数没有

76
00:02:08,980 --> 00:02:09,880
有没有传入参数

77
00:02:09,880 --> 00:02:10,640
其实重点看pass

78
00:02:10,640 --> 00:02:11,780
是不是没有传入参数

79
00:02:11,780 --> 00:02:12,600
没有传入参数

80
00:02:12,600 --> 00:02:13,140
是不是说明

81
00:02:13,140 --> 00:02:14,280
查清所有的数据

82
00:02:14,280 --> 00:02:15,440
因为你没有指定查哪一条

83
00:02:15,440 --> 00:02:16,260
所以说只能查

84
00:02:16,260 --> 00:02:16,920
所以了

85
00:02:16,920 --> 00:02:18,560
那么除了查所有的

86
00:02:18,560 --> 00:02:19,060
是不是含有一种

87
00:02:19,060 --> 00:02:20,080
是需要去查什么呀

88
00:02:20,080 --> 00:02:20,920
是不是查其中某一条

89
00:02:20,920 --> 00:02:22,140
查其中某一条

90
00:02:22,140 --> 00:02:23,060
我们怎么样去看呢

91
00:02:23,060 --> 00:02:24,580
其实我们看咱们的一个路由就可以了吧

92
00:02:24,580 --> 00:02:25,400
post id

93
00:02:28,780 --> 00:02:30,580
接下来我们就来正式写咱们这样一个路由

94
00:02:30,580 --> 00:02:34,420
首先我们是不是调用router.resources

95
00:02:34,420 --> 00:02:38,000
第一个参数来传入我们路由的一个名称 比如说我们给它定义叫做host

96
00:02:38,000 --> 00:02:41,320
重点是咱们第二个参数 也就是我们访问那个路径apihost

97
00:02:41,320 --> 00:02:43,380
好 那么接下来就是我们一个控制器

98
00:02:43,380 --> 00:02:44,400
con

99
00:02:44,400 --> 00:02:47,720
出了 点点什么呢

100
00:02:47,720 --> 00:02:51,560
点home嘛 那么我们这里有一个home.js 所以我们就直接用它就

101
00:02:51,560 --> 00:02:54,900
就用concreen了点home 我们就不给它改名字了

102
00:02:54,900 --> 00:02:56,940
好 那么我们resource之后

103
00:02:57,200 --> 00:02:59,120
当然其实已经定义好了我们的一个路由

104
00:02:59,120 --> 00:03:01,660
那么到这里我们接下来应该干什么

105
00:03:01,660 --> 00:03:03,680
我们是不是要去写我们控制器的一个逻辑

106
00:03:03,680 --> 00:03:05,020
也就是我们的一个

107
00:03:05,020 --> 00:03:08,320
那么刚才讲到了我们首先第一步

108
00:03:08,320 --> 00:03:09,060
需要去完成什么

109
00:03:09,060 --> 00:03:11,600
首先我们是不是要创建一条数据

110
00:03:11,600 --> 00:03:14,160
那么创建数据应该用什么呢

111
00:03:14,160 --> 00:03:16,900
我们来首先来把一个对应关系给张过来

112
00:03:16,900 --> 00:03:19,160
那么这样方便我们去写咱们一个代码

113
00:03:19,160 --> 00:03:25,200
好我们来把刚才我们resource的一个对应关系

114
00:03:25,200 --> 00:03:26,780
咱们把它给张过来

115
00:03:26,780 --> 00:03:28,040
首先我们来确定一下

116
00:03:28,040 --> 00:03:29,480
我们通过哪一个API

117
00:03:29,480 --> 00:03:30,220
咱们可以去

118
00:03:30,220 --> 00:03:32,080
咱们可以去添加数据

119
00:03:32,080 --> 00:03:32,680
大家可以看到

120
00:03:32,680 --> 00:03:33,440
这里是不是可以添加数据

121
00:03:33,440 --> 00:03:33,900
POST

122
00:03:33,900 --> 00:03:34,340
对吧

123
00:03:34,340 --> 00:03:35,340
我们对应的方法是什么

124
00:03:35,340 --> 00:03:35,740
Create

125
00:03:35,740 --> 00:03:38,600
所以我们通过这里去添加数据

126
00:03:38,600 --> 00:03:40,320
所以我们这里需要定义一个什么方法

127
00:03:40,320 --> 00:03:41,180
是不是

128
00:03:41,180 --> 00:03:42,160
ASYNC

129
00:03:42,160 --> 00:03:43,160
Create

130
00:03:43,160 --> 00:03:44,760
好

131
00:03:44,760 --> 00:03:46,640
那么我们Create应该怎么样去写呢

132
00:03:46,640 --> 00:03:48,460
首先我们来分析一下

133
00:03:48,460 --> 00:03:50,380
首先我们第一步是不是要获取

134
00:03:50,380 --> 00:03:52,960
获取咱们POST的Body

135
00:03:52,960 --> 00:03:55,520
也就是从咱们扣端传过来的参数吧

136
00:03:55,520 --> 00:03:56,360
因为我们创建

137
00:03:56,360 --> 00:03:58,240
那么你用户要创建我们的一篇文章

138
00:03:58,240 --> 00:04:00,200
你是不是肯定要传一个content

139
00:04:00,200 --> 00:04:00,700
传一个title

140
00:04:00,700 --> 00:04:01,700
不然我怎么知道你怎么

141
00:04:01,700 --> 00:04:02,700
你怎么创建文章

142
00:04:02,700 --> 00:04:02,940
对吧

143
00:04:02,940 --> 00:04:03,980
你要去传一个什么样的文章

144
00:04:03,980 --> 00:04:04,620
所以说呢

145
00:04:04,620 --> 00:04:05,680
我们首先是不是要获取

146
00:04:05,680 --> 00:04:06,920
我们post它的一个参数

147
00:04:06,920 --> 00:04:08,780
那么我们获取完参数之后

148
00:04:08,780 --> 00:04:09,760
第二步接下来干嘛

149
00:04:09,760 --> 00:04:11,800
是不是要把它去写入数据库

150
00:04:11,800 --> 00:04:12,320
这样呢

151
00:04:12,320 --> 00:04:12,940
分为两步

152
00:04:12,940 --> 00:04:14,220
是不是就完成了我们的一个

153
00:04:14,220 --> 00:04:15,500
咱们数据库的一个添加

154
00:04:15,500 --> 00:04:15,960
好

155
00:04:15,960 --> 00:04:16,780
那么我们首先来看一下

156
00:04:16,780 --> 00:04:17,360
一步

157
00:04:17,360 --> 00:04:18,440
咱们去conce一个

158
00:04:18,440 --> 00:04:20,700
首先我们获取一个context

159
00:04:20,700 --> 00:04:22,440
那么我们要去获取参数

160
00:04:22,440 --> 00:04:23,840
咱们是不是通过

161
00:04:23,840 --> 00:04:25,040
咱们request一个body

162
00:04:25,040 --> 00:04:25,700
比如说我们去

163
00:04:25,700 --> 00:04:27,640
咱们去定一个requestbody

164
00:04:27,640 --> 00:04:28,040
等于什么呢

165
00:04:28,040 --> 00:04:28,480
它在哪里呢

166
00:04:28,480 --> 00:04:29,280
就是contest的点

167
00:04:29,280 --> 00:04:30,360
request的点

168
00:04:30,360 --> 00:04:31,100
body

169
00:04:31,100 --> 00:04:32,140
那么它这里呢

170
00:04:32,140 --> 00:04:33,860
就可以获取到我们的一个body

171
00:04:33,860 --> 00:04:34,180
好

172
00:04:34,180 --> 00:04:34,880
那么这里呢

173
00:04:34,880 --> 00:04:36,260
我们先不着急写下面的代码

174
00:04:36,260 --> 00:04:37,800
我们先把这条代码

175
00:04:37,800 --> 00:04:38,400
调试一下

176
00:04:38,400 --> 00:04:38,780
我们来看一下

177
00:04:38,780 --> 00:04:40,060
到底咱们能不能获取到

178
00:04:40,060 --> 00:04:40,980
我们的body

179
00:04:40,980 --> 00:04:42,440
因为你如果连body都获取不到的话

180
00:04:42,440 --> 00:04:43,720
那么我们后面的代码是不是白写了

181
00:04:43,720 --> 00:04:44,940
所以说我们先来检测一下

182
00:04:44,940 --> 00:04:46,800
咱们的一个效果

183
00:04:46,800 --> 00:04:47,560
好

184
00:04:47,560 --> 00:04:48,160
这里也报了一个错

185
00:04:48,160 --> 00:04:49,000
我们来看一下报什么错

186
00:04:49,000 --> 00:04:56,420
大家可以看到这里报一个错

187
00:04:56,420 --> 00:04:58,100
controller not exists

188
00:04:58,100 --> 00:05:00,340
好 我们来看一下到底为什么会报这样一个错

189
00:05:00,340 --> 00:05:03,660
其实这样一个错误呢

190
00:05:03,660 --> 00:05:05,520
是我们这里的一个controller它给拼错了

191
00:05:05,520 --> 00:05:06,500
我这里呢已经给调整过来了

192
00:05:06,500 --> 00:05:08,020
所以说这里呢我们是没有问题了

193
00:05:08,020 --> 00:05:12,940
好 我们呢首先来看一下咱们post的数据能不能够发送过来

194
00:05:12,940 --> 00:05:14,620
那我们怎么去模拟我们的post呢

195
00:05:14,620 --> 00:05:16,460
是不是通过我们的postman呢

196
00:05:16,460 --> 00:05:17,920
所以说我们来访问一下咱们这样的接口

197
00:05:17,920 --> 00:05:18,780
咱们通过postman

198
00:05:18,780 --> 00:05:20,200
首先我们打开postman

199
00:05:20,200 --> 00:05:22,560
那么我们访问我们的7001

200
00:05:22,560 --> 00:05:23,560
app

201
00:05:23,560 --> 00:05:26,240
这里是不是我们所定的一个路由

202
00:05:26,240 --> 00:05:27,980
我们把方法的方法改为post

203
00:05:27,980 --> 00:05:30,580
然后咱们在board里面去添加一条数据

204
00:05:30,580 --> 00:05:31,900
比如说我们去添加一个content

205
00:05:31,900 --> 00:05:32,820
title

206
00:05:32,820 --> 00:05:35,760
咱们的内容是帅起来你也怕

207
00:05:35,760 --> 00:05:38,340
那么我们title就改为帅起来

208
00:05:38,340 --> 00:05:39,300
我也怕

209
00:05:39,300 --> 00:05:41,520
那么我们来

210
00:05:41,520 --> 00:05:42,700
我们来渗得一下看一下

211
00:05:42,700 --> 00:05:45,900
咱们看一下咱们的服务

212
00:05:45,900 --> 00:05:46,860
有没有收到我们的一个数据

213
00:05:46,860 --> 00:05:47,360
大家可以看到

214
00:05:47,360 --> 00:05:48,720
是不是可以收到我们的content

215
00:05:48,720 --> 00:05:49,900
对吧

216
00:05:49,900 --> 00:05:50,380
title

217
00:05:50,380 --> 00:05:51,880
所以说说明什么问题

218
00:05:51,880 --> 00:05:52,520
是不是说明我们

219
00:05:52,520 --> 00:05:53,660
咱们客户章的一个请求

220
00:05:53,660 --> 00:05:54,320
可以发送过来

221
00:05:54,320 --> 00:05:55,080
好

222
00:05:55,080 --> 00:05:55,620
那么说明了

223
00:05:55,620 --> 00:05:56,860
我们在这里的代码已经走通了

224
00:05:56,860 --> 00:05:57,780
不过这里需要同学们

225
00:05:57,780 --> 00:05:58,740
去注意一点是什么呢

226
00:05:58,740 --> 00:05:59,600
咱们在1G1里面

227
00:05:59,600 --> 00:06:00,440
去发送post的请求

228
00:06:00,440 --> 00:06:01,820
需要带上一个什么头啊

229
00:06:01,820 --> 00:06:02,700
是不是需要带上一个

230
00:06:02,700 --> 00:06:04,740
CSRF的一个token啊

231
00:06:04,740 --> 00:06:05,880
咱们前面的内容是不是讲过

232
00:06:05,880 --> 00:06:06,960
那么假如说我们把它给去掉

233
00:06:06,960 --> 00:06:07,720
会发生一下什么事情

234
00:06:07,720 --> 00:06:08,440
大家可以看到

235
00:06:08,440 --> 00:06:09,240
是不是返回了一个403

236
00:06:09,240 --> 00:06:10,640
403错误

237
00:06:10,640 --> 00:06:12,060
403是不是说明你们有权限

238
00:06:12,060 --> 00:06:13,060
所以它这里提示

239
00:06:13,060 --> 00:06:14,340
就是inbody的csrftoken

240
00:06:14,340 --> 00:06:15,560
所以说的同学们一定要去注意这一点

241
00:06:15,560 --> 00:06:16,840
好大家可以看到了

242
00:06:16,840 --> 00:06:18,060
其实我们现在的数据已经收到了

243
00:06:18,060 --> 00:06:19,740
那么我们数据收到之后

244
00:06:19,740 --> 00:06:21,340
接下来是不是就可以把咱们的一个数据

245
00:06:21,340 --> 00:06:23,140
写入到我们的一个数据库里面了

246
00:06:23,140 --> 00:06:23,860
好那么接下来呢

247
00:06:23,860 --> 00:06:25,960
我们就来进行咱们数据库的一个写入

248
00:06:25,960 --> 00:06:28,900
好咱们写入数据库

249
00:06:28,900 --> 00:06:32,960
那么我们写入数据库怎么去写呢

250
00:06:32,960 --> 00:06:38,620
那么在写之前大家思考一下

251
00:06:38,620 --> 00:06:40,420
我们要通过谁去写啊

252
00:06:40,420 --> 00:06:42,340
是不是肯定是model啊

253
00:06:42,340 --> 00:06:44,340
那么我们来看一下model是什么

254
00:06:44,340 --> 00:06:45,340
我们来看一下model是什么

255
00:06:45,340 --> 00:06:48,340
我们model是不是return了咱们的一个posts

256
00:06:48,340 --> 00:06:50,340
这样的一个model layer

257
00:06:50,340 --> 00:06:51,340
那么我们要存到数据库里面

258
00:06:51,340 --> 00:06:52,340
咱们前面是不是讲过

259
00:06:52,340 --> 00:06:54,340
咱们是不是要首先要把它lue一下

260
00:06:54,340 --> 00:06:56,340
咱们要把这样一个model实力化

261
00:06:56,340 --> 00:06:57,340
然后调用它的什么方法

262
00:06:57,340 --> 00:06:58,340
调用它的save方法

263
00:06:58,340 --> 00:06:59,340
咱们就可以去存入数据库了

264
00:06:59,340 --> 00:07:00,340
那么这里呢

265
00:07:00,340 --> 00:07:03,340
首先咱们来去lue一下咱们的model

266
00:07:03,340 --> 00:07:04,340
那么我们的model在哪里呢

267
00:07:04,340 --> 00:07:05,340
其实呢

268
00:07:05,340 --> 00:07:06,340
我刚才是不是讲过了

269
00:07:06,340 --> 00:07:07,340
是不是挂载在咱们的context

270
00:07:07,340 --> 00:07:08,340
这样一个对象下面呢

271
00:07:08,340 --> 00:07:09,340
所以我们来const一个

272
00:07:09,340 --> 00:07:11,340
比如说我们punch一个posts

273
00:07:12,100 --> 00:07:15,700
instance 对吧 因为我们要去new model 所以咱们现在叫instance 等于什么呢

274
00:07:15,700 --> 00:07:17,220
就等于new this 点

275
00:07:17,220 --> 00:07:21,840
这里不用this contest 那么contest下面是不是有一个model属性 那么model下面呢

276
00:07:21,840 --> 00:07:25,420
有一个什么呀 咱们刚才是不是进行一个post 咱们去new它

277
00:07:25,420 --> 00:07:27,460
对吧 然后呢去传入

278
00:07:27,460 --> 00:07:30,800
我们所定义的一个title和content 就可以了

279
00:07:30,800 --> 00:07:33,100
比如说我们的title是什么呀

280
00:07:33,100 --> 00:07:38,220
我们title是不是rebounceboard.title 咱们的content呢

281
00:07:38,220 --> 00:07:40,020
是不是rebounceboard.content

282
00:07:41,340 --> 00:07:45,220
那么这里呢其实我们就已经把它给漏出来了

283
00:07:45,220 --> 00:07:46,200
那么漏完之后

284
00:07:46,200 --> 00:07:48,100
我们是不是调用它的什么方法呀

285
00:07:48,100 --> 00:07:50,920
是不是调用它的save方法

286
00:07:50,920 --> 00:07:52,300
然后这里大家需要去注意

287
00:07:52,300 --> 00:07:53,160
我们之前是不是讲过

288
00:07:53,160 --> 00:07:54,740
蒙古斯它是不是一个异步的一个库

289
00:07:54,740 --> 00:07:55,220
对吧

290
00:07:55,220 --> 00:07:57,400
大家还记得我们访问他gethub的官网的时候

291
00:07:57,400 --> 00:07:58,340
他有说过一句话吗

292
00:07:58,340 --> 00:08:00,660
大家看到

293
00:08:00,660 --> 00:08:04,460
这里是1GG港蒙古斯

294
00:08:04,460 --> 00:08:06,940
好大家可以看到

295
00:08:06,940 --> 00:08:08,320
它是不是只在异步的环境中工作

296
00:08:08,320 --> 00:08:08,720
所以说呢

297
00:08:08,720 --> 00:08:09,960
它save这样一个方法

298
00:08:09,960 --> 00:08:10,560
其实是异步的

299
00:08:10,560 --> 00:08:13,200
所以说我们需要把它给wait一下

300
00:08:13,200 --> 00:08:15,960
好 那么我们来运行一下

301
00:08:15,960 --> 00:08:17,020
试一下 看到底能不能成功

302
00:08:17,020 --> 00:08:19,740
好 我们post sent

303
00:08:19,740 --> 00:08:20,820
好 大家可以看到

304
00:08:20,820 --> 00:08:22,140
放了一个500错误 这里报了一个错

305
00:08:22,140 --> 00:08:24,600
context.model post is not a construct

306
00:08:24,600 --> 00:08:26,040
其实这里呢 大家需要去注意

307
00:08:26,040 --> 00:08:27,360
注意个什么问题呢

308
00:08:27,360 --> 00:08:28,920
虽然说我们model里面是不是定义了

309
00:08:28,920 --> 00:08:30,100
咱们这样的一个post这样的model

310
00:08:30,100 --> 00:08:31,100
但是我们在调用的时候

311
00:08:31,100 --> 00:08:32,840
其实1GG里面会自动的把它改为

312
00:08:32,840 --> 00:08:35,080
1G会自动把它改为驼峰式

313
00:08:35,080 --> 00:08:39,080
那么具体呢

314
00:08:39,080 --> 00:08:40,960
为什么可能是他内部的一些机制

315
00:08:40,960 --> 00:08:42,160
我们不用去过度纠结

316
00:08:42,160 --> 00:08:43,440
咱们把它改为大写就可以了

317
00:08:43,440 --> 00:08:44,460
那么这里我们来看一下

318
00:08:44,460 --> 00:08:46,760
咱们现在有没有运营成功

319
00:08:46,760 --> 00:08:47,020
正的

320
00:08:47,020 --> 00:08:51,360
context and model post is not constructed

321
00:08:51,360 --> 00:08:52,140
好 大家不要着急

322
00:08:52,140 --> 00:08:53,160
我们来看一下是什么

323
00:08:53,160 --> 00:08:55,460
其实这个问题是我们在编写代码的时候

324
00:08:55,460 --> 00:08:56,920
没有进行咱们common加s

325
00:08:56,920 --> 00:08:57,520
也就是没有保存

326
00:08:57,520 --> 00:08:59,040
所以说一机它没有重新编译

327
00:08:59,040 --> 00:09:00,080
所以说一个小问题

328
00:09:00,080 --> 00:09:00,840
好 我们再来

329
00:09:00,840 --> 00:09:02,120
发送一下

330
00:09:02,120 --> 00:09:03,660
大家可以看到

331
00:09:03,660 --> 00:09:05,200
我们是不是返回了一个Nordfund

332
00:09:05,200 --> 00:09:06,220
那么其实说明什么呢

333
00:09:06,220 --> 00:09:08,260
其实说明我们数据有可能已经存储成功了

334
00:09:08,260 --> 00:09:13,260
我们来看一下数据库 它到底有没有存储进去 首先我们来找一下有没有blog这样一张表

335
00:09:13,260 --> 00:09:22,300
其实这里大家可以看到 我们是不是已经多了blog这样一个库吧

336
00:09:22,300 --> 00:09:27,240
然后在blog里面是不是有一张叫post式的表 我们现在是不是已经有两条数据了

337
00:09:27,240 --> 00:09:28,940
 对吧 帅起来我也怕 帅起来你也怕

338
00:09:28,940 --> 00:09:33,600
有两个字吧 title和content 那么它其实还多余 多余了两个字吧 一个是id

339
00:09:33,600 --> 00:09:34,300
 一个是-v

340
00:09:34,300 --> 00:09:35,560
那么ID呢其实是Mongos

341
00:09:35,560 --> 00:09:37,460
它自动帮我们生成了咱们的一个所以

342
00:09:37,460 --> 00:09:39,500
那么-V呢其实大家暂时不用去关心

343
00:09:39,500 --> 00:09:40,940
所以说这里呢说明什么问题

344
00:09:40,940 --> 00:09:42,480
是不是我们已经创建数据成功了

345
00:09:42,480 --> 00:09:44,920
那么这里呢其实还有一个小问题

346
00:09:44,920 --> 00:09:45,920
不知道大家有没有发现

347
00:09:45,920 --> 00:09:47,620
我们去存储数据的时候

348
00:09:47,620 --> 00:09:49,060
比如说我们去发表文章

349
00:09:49,060 --> 00:09:51,100
那么文章的标题是不是一般不能够去重复啊

350
00:09:51,100 --> 00:09:52,300
也就是不能相同

351
00:09:52,300 --> 00:09:53,280
因为如果说这样的话

352
00:09:53,280 --> 00:09:54,440
大家去搜索会有些困难

353
00:09:54,440 --> 00:09:56,280
所以说我们需要去做一些事情

354
00:09:56,280 --> 00:09:59,200
我们是不是要需要让我们的文章不能够重复

355
00:09:59,200 --> 00:09:59,900
那怎么办

356
00:09:59,900 --> 00:10:02,300
是不是在我们model schema里面去做文章

357
00:10:02,300 --> 00:10:04,040
那么其实在schema里面

358
00:10:04,040 --> 00:10:05,480
我们可以去给他做一些配置

359
00:10:05,480 --> 00:10:06,140
比如说呢

360
00:10:06,140 --> 00:10:07,440
它的type是什么呀

361
00:10:07,440 --> 00:10:07,640
是不是

362
00:10:07,640 --> 00:10:08,440
子俊

363
00:10:08,440 --> 00:10:09,580
对吧

364
00:10:09,580 --> 00:10:10,840
那么我们其实可以给他一个

365
00:10:10,840 --> 00:10:12,480
可以给他一个属性

366
00:10:12,480 --> 00:10:13,760
什么属性呢

367
00:10:13,760 --> 00:10:14,400
其实就是

368
00:10:14,400 --> 00:10:16,980
uniquireV2

369
00:10:16,980 --> 00:10:19,040
那么uniquireV2说明一个什么问题

370
00:10:19,040 --> 00:10:20,620
说明一个什么问题

371
00:10:20,620 --> 00:10:21,220
也就是说呢

372
00:10:21,220 --> 00:10:22,460
说明我们的这个字段

373
00:10:22,460 --> 00:10:23,420
它是不能够去重复的

374
00:10:23,420 --> 00:10:24,440
那么我们就来测试一下

375
00:10:24,440 --> 00:10:25,280
看到底是不是怎么回事

376
00:10:25,280 --> 00:10:27,360
好 我们去渗载

377
00:10:27,360 --> 00:10:30,340
刷新

378
00:10:30,340 --> 00:10:31,980
大家可以看到我们现在是不是有三条数据

379
00:10:31,980 --> 00:10:33,100
那么原因是为什么

380
00:10:33,100 --> 00:10:33,700
有的同学会说

381
00:10:33,700 --> 00:10:34,780
你刚才不是已经配置了吗

382
00:10:34,780 --> 00:10:36,020
为什么还会发生这种情况

383
00:10:36,020 --> 00:10:38,580
其实呢我们在改完咱们schema之后

384
00:10:38,580 --> 00:10:39,980
需要把我们的数据先给拴掉

385
00:10:39,980 --> 00:10:41,600
这里呢也是它的

386
00:10:41,600 --> 00:10:43,320
咱们mongodb它的一个限制

387
00:10:43,320 --> 00:10:44,880
好那么现在我们再来测试一下

388
00:10:44,880 --> 00:10:45,860
看有没有出现这种情况

389
00:10:45,860 --> 00:10:46,780
比如说我们发送一次

390
00:10:46,780 --> 00:10:48,920
那么我们的数据肯定是只有一条的

391
00:10:48,920 --> 00:10:50,020
好有一条对吧

392
00:10:50,020 --> 00:10:51,120
好那么我们再发送一次

393
00:10:51,120 --> 00:10:54,320
好很尴尬

394
00:10:54,320 --> 00:11:00,080
其实呢我们需要把这整个collection都给拴掉

395
00:11:00,080 --> 00:11:01,680
我们光光双除数据是不够的

396
00:11:01,680 --> 00:11:03,020
好那么现在我们再来看一下

397
00:11:03,020 --> 00:11:04,440
比如说我们再去剩的

398
00:11:04,440 --> 00:11:05,740
其实这里大家可以看到报了一个错

399
00:11:05,740 --> 00:11:07,700
error 什么什么什么

400
00:11:07,700 --> 00:11:08,820
说明一个什么问题呢

401
00:11:08,820 --> 00:11:10,680
蒙狗error 其实它呢意思就是

402
00:11:10,680 --> 00:11:12,420
咱们核心呢 其实看这样一个单词

403
00:11:12,420 --> 00:11:14,220
duplicate 实际上就是说明呢

404
00:11:14,220 --> 00:11:16,040
你有一个咱们这样一个可以有重复的数据

405
00:11:16,040 --> 00:11:18,360
所以说说明我们的unicart生效了

406
00:11:18,360 --> 00:11:20,360
好 那么这里呢就是我们这一节课的内容

407
00:11:20,360 --> 00:11:21,840
我们呢 先来总结一下

408
00:11:21,840 --> 00:11:24,000
好 那么首先呢

409
00:11:24,000 --> 00:11:25,640
我们是不是利用咱们root的一个resources

410
00:11:25,640 --> 00:11:27,880
去创造咱们一个resof API

411
00:11:27,880 --> 00:11:29,680
那么呢 最后呢定义到咱们home

412
00:11:29,680 --> 00:11:31,080
这样一个controller里面

413
00:11:31,080 --> 00:11:32,920
那么create 咱们首先呢去新增数据

414
00:11:32,920 --> 00:11:35,400
那么新增数据是不是对应咱们POST这样的一个方法

415
00:11:35,400 --> 00:11:38,060
那么POST是不是对应咱们Ctrl里面一个Create

416
00:11:38,060 --> 00:11:39,920
那么我们Create是不是也分为两步

417
00:11:39,920 --> 00:11:42,480
首先你要获取我们Request传入来了一个玻璃

418
00:11:42,480 --> 00:11:43,820
那么你取了参数之后

419
00:11:43,820 --> 00:11:44,520
通过这样一个参数

420
00:11:44,520 --> 00:11:45,900
是不是写入我们的数据库

421
00:11:45,900 --> 00:11:46,920
那么写入我们数据库

422
00:11:46,920 --> 00:11:49,380
首先我们是不是通过new了一个model这样一个对象

423
00:11:49,380 --> 00:11:50,060
new出来之后

424
00:11:50,060 --> 00:11:51,760
调用实力的一个save方法

425
00:11:51,760 --> 00:11:54,300
这样就完成了我们数据库的一个添加操作

426
00:11:54,300 --> 00:11:57,280
那么我们还和刚才一样

427
00:11:57,280 --> 00:11:58,840
我们讲完了路由设计

428
00:11:58,840 --> 00:12:00,720
同学们是不是会思考我们下一步该做什么

429
00:12:00,720 --> 00:12:02,640
那么我们实际上下不该做什么呢

430
00:12:02,640 --> 00:12:03,320
其实大家可以看到

431
00:12:03,320 --> 00:12:06,480
咱们现在的create是不是很粗糙啊

432
00:12:06,480 --> 00:12:07,080
比如说我们

433
00:12:07,080 --> 00:12:10,440
比如说我们通过post去发送一个错误之后

434
00:12:10,440 --> 00:12:11,740
是不是咱们还没有进行一个错误处理

435
00:12:11,740 --> 00:12:13,040
因为你不可能把这样的东西给用户

436
00:12:13,040 --> 00:12:13,660
这样是不是很丑

437
00:12:13,660 --> 00:12:16,360
包括如果说我们去添加数据成功了之后

438
00:12:16,360 --> 00:12:17,440
大家可以看到啊

439
00:12:17,440 --> 00:12:18,900
比如说我们去随便改一下

440
00:12:18,900 --> 00:12:19,980
大家可以看到我们返回的是什么

441
00:12:19,980 --> 00:12:23,860
大家可以看到我们返回的是不是多的放的

442
00:12:23,860 --> 00:12:26,140
我们是不是还需要给咱们用户一个成功的提示

443
00:12:26,140 --> 00:12:27,100
因为我们现在设计是API

444
00:12:27,100 --> 00:12:28,740
我们需要去返回一个success成功

445
00:12:28,740 --> 00:12:30,000
你不能给一个多的放的

446
00:12:30,000 --> 00:12:31,920
这里呢也就是我们需要去补充

447
00:12:31,920 --> 00:12:33,360
我们是不是需要去

448
00:12:33,360 --> 00:12:34,620
咱们需要去补充

449
00:12:34,620 --> 00:12:36,560
凑处理啊对吧

450
00:12:36,560 --> 00:12:37,840
首先有一个凑处理

451
00:12:37,840 --> 00:12:39,300
然后还会需要一个

452
00:12:39,300 --> 00:12:40,560
防御值吧

453
00:12:40,560 --> 00:12:43,120
最后呢其实我们还需要去教验一下参数

454
00:12:43,120 --> 00:12:45,780
为什么呀

455
00:12:45,780 --> 00:12:47,140
因为我们

456
00:12:47,140 --> 00:12:49,480
咱们schema里面规定的是什么类型

457
00:12:49,480 --> 00:12:50,060
子菌吧

458
00:12:50,060 --> 00:12:52,000
但是如果说你的玻璃传的数字怎么办

459
00:12:52,000 --> 00:12:54,100
其实这里是不是也会报错呀

460
00:12:54,100 --> 00:12:55,940
那么实际上呢我们可以去加入咱们的一个

461
00:12:55,940 --> 00:12:57,520
数据的一个教验对吧

462
00:12:57,520 --> 00:12:59,560
好那么具体的补充逻辑呢

463
00:12:59,560 --> 00:13:03,720
我们就放在咱们下一个内容去讲 好 那么这里呢 这就是我们这几个内容

