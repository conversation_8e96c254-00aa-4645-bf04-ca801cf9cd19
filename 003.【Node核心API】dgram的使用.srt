1
00:00:00,000 --> 00:00:02,700
好 这节课我们来看一下 dgram数据报

2
00:00:02,700 --> 00:00:08,420
那么 dgram数据报是什么 我们来看一下它的简介 非常简单 dgram模块呢 提供了

3
00:00:08,420 --> 00:00:09,880
 udp数据报 Socket的实现

4
00:00:09,880 --> 00:00:12,160
那么 udp和 Socket

5
00:00:12,160 --> 00:00:19,760
你不要告诉我 我不知道 对吧 那么 udp呢 它和 tcp 类似 那么 tcp呢 是我们的一个咱们http

6
00:00:19,760 --> 00:00:24,920
它的协议的一个基石 那么 udp呢 其实它也是咱们业内的非常有名的一种数据传输的协议

7
00:00:25,240 --> 00:00:30,240
那么UDP它的应用场景呢 最典型的就是邮件 它和TCP的区别是什么呢 那么有没有同学能够知道

8
00:00:30,240 --> 00:00:36,850
如果说不知道的同学呢 你可能需要去补习一下你的计算机基础啊 这里呢有推荐大家去看一下啊

9
00:00:36,850 --> 00:00:38,080
 计算机网络基础相关的一些书籍

10
00:00:38,080 --> 00:00:41,730
看距离看什么书呢 大家可以自己去百度一下 你可能这样一些基础呢

11
00:00:41,730 --> 00:00:43,240
 对你今后的发展非常的重要

12
00:00:43,240 --> 00:00:45,760
那么UDP呢 它和TCP的区别就是呢

13
00:00:45,760 --> 00:00:46,940
TCP协议

14
00:00:48,240 --> 00:00:52,680
大家应该听说过 三次握手 四次揮手 它其实核心就是保证我的数据一定会送达

15
00:00:52,680 --> 00:00:54,120
 那么UDP它是一种什么协议呢

16
00:00:54,120 --> 00:00:59,000
我发送的数据 收没收到了 就不是我的事情了 收没收到我不管 反正我发了

17
00:00:59,000 --> 00:01:02,060
好 那么就跟了产品和开发去沟通一样

18
00:01:02,060 --> 00:01:09,740
这个东西你能不能实现 那我不管 反正我下个星期要 这也是开发和产品产生对斗的矛盾

19
00:01:09,740 --> 00:01:17,420
那么这里呢 地管模块也提供了UDP数据包 也Socket的一个实现 那么也就是说Socket我们的服务端和客户端之间进行通信

20
00:01:17,420 --> 00:01:20,620
它是基于咱们udp的 也就是这样一个模块 我们来看一下

21
00:01:20,620 --> 00:01:24,640
首先去引入了我们dgram 包括去创建的一个server

22
00:01:24,640 --> 00:01:27,240
通过create socket 协议就是udp

23
00:01:27,240 --> 00:01:31,760
这里server监听了一个error 这样一个事件 去处理一场 包括去监听了一个message

24
00:01:31,760 --> 00:01:32,760
 去进行消息的接收

25
00:01:32,760 --> 00:01:37,560
我们lessing就是去监听端口 然后绑定了41234这样一个端口

26
00:01:37,560 --> 00:01:40,600
好 我们把这样的一段代码给复制一下

27
00:01:40,600 --> 00:01:45,920
我们这里创点一个service.js

28
00:01:46,720 --> 00:01:47,480
好 我们把这张列过来

29
00:01:47,480 --> 00:01:54,140
好 然后我们通过load咱们来启动我们这样的一个service

30
00:01:54,140 --> 00:01:55,680
我们执行load

31
00:01:55,680 --> 00:01:58,760
service.js

32
00:01:58,760 --> 00:02:02,600
好 大家可以看到我们的服务是不是已经开始监听了 而且处于一个挂起的状态

33
00:02:02,600 --> 00:02:03,620
挂起

34
00:02:03,620 --> 00:02:05,400
是不是说明我们程序在前台运行

35
00:02:05,400 --> 00:02:07,460
那么前台和后台的区别是什么呢

36
00:02:07,460 --> 00:02:10,280
咱们前面讲解我们load.js守护进程的时候也说过呀

37
00:02:10,280 --> 00:02:12,320
好 我们开始监听了

38
00:02:12,320 --> 00:02:14,360
那么咱们服务启动监听这样一个帮口

39
00:02:14,360 --> 00:02:16,160
我们的socket是做什么用的呀

40
00:02:16,160 --> 00:02:19,440
Socket是不是进行咱们建立连接之后进行消息的一个通信

41
00:02:19,440 --> 00:02:22,860
所以说呢Message这里呢它就会去接受一些数据

42
00:02:22,860 --> 00:02:25,100
所以说我们接下来需要思考一个问题就是呢

43
00:02:25,100 --> 00:02:29,160
如何给我们的Socket的服务器也就是我们的service是不是和它去发送消息啊

44
00:02:29,160 --> 00:02:32,460
那么如何给我们的41234这个端口去发送消息呢

45
00:02:32,460 --> 00:02:35,400
我们呢就接着我们的文档呢咱们往下看

46
00:02:35,400 --> 00:02:38,500
好那么它的一些具体的介绍呢

47
00:02:38,500 --> 00:02:41,320
我们就不去介绍它的一些细节我们只看核心的内容

48
00:02:41,320 --> 00:02:43,420
好这里呢包括这里呢使用了一个Band

49
00:02:43,420 --> 00:02:46,080
Band的这个方法呢其实就是绑定我们的一个端口对吧

50
00:02:46,080 --> 00:02:48,220
好 我们继续往下翻

51
00:02:48,220 --> 00:02:50,560
好 大家注意这样一个方法

52
00:02:50,560 --> 00:02:51,860
Socket.son的

53
00:02:51,860 --> 00:02:53,280
大家是不是看它的名称就好像

54
00:02:53,280 --> 00:02:55,320
我是不是给某一个东西去发送一条消息

55
00:02:55,320 --> 00:02:56,660
我们来看一下它的demo

56
00:02:56,660 --> 00:02:57,940
首先去引入一个dgram

57
00:02:57,940 --> 00:02:59,300
然后创建一个message

58
00:02:59,300 --> 00:03:00,180
大家可以看到

59
00:03:00,180 --> 00:03:01,600
我们是不是立马就使用到了buffer

60
00:03:01,600 --> 00:03:03,600
我们通过buffer去创建一个什么

61
00:03:03,600 --> 00:03:06,140
是不是创建一个二进制的一个数据

62
00:03:06,140 --> 00:03:10,080
然后通过Socket去发送给我们的41234

63
00:03:10,080 --> 00:03:10,760
这样一个端口

64
00:03:10,760 --> 00:03:12,400
那么我们就测试一下

65
00:03:12,400 --> 00:03:13,820
看一下我们刚才创建的service

66
00:03:13,820 --> 00:03:14,960
能不能收到这一条消息

67
00:03:14,960 --> 00:03:19,020
那么我们发消息呢

68
00:03:19,020 --> 00:03:21,920
我们就把它叫做客户端client.js

69
00:03:21,920 --> 00:03:27,860
我们的端口是41234

70
00:03:27,860 --> 00:03:30,120
发生的信息是一些字节

71
00:03:30,120 --> 00:03:30,820
没问题

72
00:03:30,820 --> 00:03:34,980
那么我们就通过咱们的一个窗口

73
00:03:34,980 --> 00:03:36,020
我们来给他发送一条消息

74
00:03:36,020 --> 00:03:37,900
我们来执行load

75
00:03:37,900 --> 00:03:41,940
dgramclient.js走你

76
00:03:41,940 --> 00:03:43,580
好大家可以看到

77
00:03:43,580 --> 00:03:50,420
好 我们这里是不是已经出现了服务端接收到来自我们52614啊 这样的一个端口发送来的消息

78
00:03:50,420 --> 00:03:55,580
 那么它的消息是什么呢 一些字节 对吧 说明了我们此时呢 消息是发送成功的

79
00:03:55,580 --> 00:04:01,580
好 而且呢 你呢还可以去发送包含多个buff的UTB包 那么呢 如何发送多个buff呢

80
00:04:01,580 --> 00:04:05,580
 其实就是咱们圣德的第一个参数来传入一个什么呀 是不是传入一个数组啊

81
00:04:05,580 --> 00:04:10,580
好 那么这里呢 其实就是它发送消息的过程 那么这里呢 我们来做一下测试

82
00:04:10,580 --> 00:04:14,990
什么测试呢 我们刚才传入了一个message是什么样 是不是二进制的一个bubble呀

83
00:04:14,990 --> 00:04:15,960
 那么如果说我们把它改为一个字图创

84
00:04:15,960 --> 00:04:19,540
大家觉得这一条消息能不能够发送成功 大家猜一下

85
00:04:19,540 --> 00:04:23,900
既然猜不到结果 我们就来看一下到底能不能够传输成功

86
00:04:23,900 --> 00:04:31,320
我们需要在我们的一个命令行窗口里面去重新执行 走理

87
00:04:31,320 --> 00:04:36,350
大家可以看到了 其实我们去剩的一个字图创和剩的我们的一个二进制呢

88
00:04:36,350 --> 00:04:40,020
 其实咱们都可以去收到消息 对吧 我们来重新发送一遍

89
00:04:40,580 --> 00:04:44,790
确实是可以的 说明了我们圣的字不串也可以 圣的我们的二进制也就是一个buffer

90
00:04:44,790 --> 00:04:45,700
 也是可以的

91
00:04:45,700 --> 00:04:53,640
好 那么到这里呢 我们就成功的使用dgram呢 发送了咱们的二进制以及了我们字不串的一些消息

92
00:04:53,640 --> 00:04:57,740
那么这里呢 我们再来做一个测试 我们在client里面呢 大家可以看到这里是不是有一个回答函数

93
00:04:57,740 --> 00:05:01,820
然后里面呢 执行一个方法叫做client.close 那么我们不去看文档都可以去

94
00:05:01,820 --> 00:05:04,640
啊 猜测出来 此时是不是我们发送成功之后就关闭我们的扣端呢

95
00:05:04,640 --> 00:05:08,220
好 如果说我们把这一行注释之后会发生什么事情呢 我们来测试一下

96
00:05:09,000 --> 00:05:14,640
好 大家可以看到我们此时消息是不是已经发送成功了呀 但是我们此时进程是不是挂起了呀

97
00:05:14,640 --> 00:05:15,920
那么挂起

98
00:05:15,920 --> 00:05:23,080
是不是就意味着我们lodejs一直在运行咱们的client.js 他一直在监听一些消息

99
00:05:23,080 --> 00:05:27,440
 也就是说呢 他会占用我们lodejs的一个进程 对吧 所以说呢会造成资源的浪费

100
00:05:27,440 --> 00:05:30,240
所以说这里大家需要注意一下 那么如果说我们平时去

101
00:05:30,240 --> 00:05:34,600
给我们Socket服务器去发送消息的时候 我们发送完成之后 大家一定不要忘记了

102
00:05:34,600 --> 00:05:37,920
 发送完成之后呢 要把它给关掉 不然那会一直持续的去消耗

103
00:05:38,700 --> 00:05:43,420
对吧 记得关掉 那就跟你出门一样 出门是不是记得关灯 对吧 好

104
00:05:43,420 --> 00:05:47,120
那么这里呢就是咱们Dgram它的一个内容

105
00:05:47,120 --> 00:05:51,540
那么剩下的也是它的一些使用的一些细节 我们就不去看了 那重点呢

106
00:05:51,540 --> 00:05:55,060
 我们就是去理解它的一个核心思想 那么Dgram呢其实使用非常的简单

107
00:05:55,060 --> 00:06:00,880
对吧 我们首先呢去创建一个server 然后呢通过咱们的一个生的方法呢 给它去发送数据

108
00:06:00,880 --> 00:06:02,040
 就完成了他们的一个通信

109
00:06:02,340 --> 00:06:05,340
我们当然了 我们第一个我们具体如何去搭建一个说给的服务

110
00:06:05,340 --> 00:06:07,720
包括了还会涉及到一些健全等等在那些问题

111
00:06:07,720 --> 00:06:12,320
这里呢可能就属于另外的一些业务范畴了 那么有兴趣同学的可以也可以去自己研究一下

112
00:06:12,320 --> 00:06:17,180
比如说我们微信im 它的一个实现原理是什么 我们说可以的一些应用场景是什么

113
00:06:17,180 --> 00:06:17,440
 对吧

114
00:06:17,440 --> 00:06:22,560
我们如何去进行一个 去写一个 咱们需要去健全的说可以的 对吧 因为我们的说给的你

115
00:06:22,560 --> 00:06:25,900
肯定不能跟任何人都能连接 比如说我在公网去开放一个端口

116
00:06:25,900 --> 00:06:29,480
让所有人都给来发送数据的话 我们的服务器是不是会很快就会崩掉

117
00:06:29,740 --> 00:06:31,740
一定要去做一些咱们的用户教宴

118
00:06:31,740 --> 00:06:33,660
你只有指定的人才能够去发送消息

119
00:06:33,660 --> 00:06:34,780
这里呢都是需要去

120
00:06:34,780 --> 00:06:38,020
大家下去之后去进行深入的一个学习的内容

121
00:06:38,020 --> 00:06:39,420
好那么这里呢就是咱们

122
00:06:39,420 --> 00:06:41,440
Dgram这一节的一个内容

