1
00:00:00,000 --> 00:00:01,740
接下来我们来了解一下

2
00:00:01,740 --> 00:00:03,240
UDP的三种传播方式

3
00:00:03,240 --> 00:00:05,480
那么第一种就是所谓的UDP单波

4
00:00:05,480 --> 00:00:07,000
那么这个单波呢说完了

5
00:00:07,000 --> 00:00:08,340
也就是点对点

6
00:00:08,340 --> 00:00:11,120
例如我们现在所看到的这个红点和这个绿点

7
00:00:11,120 --> 00:00:12,880
它们是一对一去进行通信

8
00:00:12,880 --> 00:00:14,760
那么单波的目的地址呢

9
00:00:14,760 --> 00:00:16,800
是单一目标的一种传播方式

10
00:00:16,800 --> 00:00:17,760
那么它的地址范围呢

11
00:00:17,760 --> 00:00:19,960
就是0.0.0.0到223点

12
00:00:19,960 --> 00:00:20,660
然后三个25

13
00:00:20,660 --> 00:00:21,800
这是我们的单波

14
00:00:21,800 --> 00:00:22,880
也就是一对一通信

15
00:00:22,880 --> 00:00:27,380
那么UDP同时还支持这种就是多波的方式

16
00:00:27,380 --> 00:00:29,140
当然这个应该叫做广播

17
00:00:29,140 --> 00:00:30,660
udp广播

18
00:00:30,660 --> 00:00:32,180
那么什么是广播呢

19
00:00:32,180 --> 00:00:34,740
广播就是好比我们有个大喇叭

20
00:00:34,740 --> 00:00:35,820
我只要在这里喊一句话

21
00:00:35,820 --> 00:00:36,720
然后所有人都能听见

22
00:00:36,720 --> 00:00:38,060
所以说大家可以看到

23
00:00:38,060 --> 00:00:39,180
我们udp广播就是

24
00:00:39,180 --> 00:00:40,480
我只要广播一条消息

25
00:00:40,480 --> 00:00:41,960
那么在同一个局域网当中

26
00:00:41,960 --> 00:00:43,880
所有的客户班就都收到了这条消息

27
00:00:43,880 --> 00:00:44,840
这就是广播

28
00:00:44,840 --> 00:00:47,420
它的目的地址就是网络中所有的设备

29
00:00:47,420 --> 00:00:49,240
当然这里对于服务团来讲

30
00:00:49,240 --> 00:00:50,840
其实只需要发送一次就可以了

31
00:00:50,840 --> 00:00:52,340
然后这个数据会被路由器

32
00:00:52,340 --> 00:00:54,120
交换机等广播转发到

33
00:00:54,120 --> 00:00:56,540
当前局域网当中的所有的其他设备

34
00:00:56,540 --> 00:00:58,500
那么广播的地址分为两种

35
00:00:59,140 --> 00:00:59,760
受限广播

36
00:00:59,760 --> 00:01:00,940
第二种是直接广播

37
00:01:00,940 --> 00:01:02,240
受限广播

38
00:01:02,240 --> 00:01:03,100
它是

39
00:01:03,100 --> 00:01:04,720
首先它不会被路由器转发

40
00:01:04,720 --> 00:01:05,680
也就是说

41
00:01:05,680 --> 00:01:08,060
它只在你当前路由设备当中

42
00:01:08,060 --> 00:01:09,120
就是进行广播

43
00:01:09,120 --> 00:01:09,920
它不会说

44
00:01:09,920 --> 00:01:11,460
把这个数据去给它转发到

45
00:01:11,460 --> 00:01:12,120
另一个路由当中

46
00:01:12,120 --> 00:01:12,440
它不会

47
00:01:12,440 --> 00:01:14,140
那么广播的地址

48
00:01:14,140 --> 00:01:16,100
就是网络自断权威1的这种地址

49
00:01:16,100 --> 00:01:16,760
也就是4.25

50
00:01:16,760 --> 00:01:17,440
权威1

51
00:01:17,440 --> 00:01:19,580
而直接广播

52
00:01:19,580 --> 00:01:21,120
首先它是可以被路由转发

53
00:01:21,120 --> 00:01:23,160
那直接广播的地址呢

54
00:01:23,160 --> 00:01:24,480
就是主机自断权威1

55
00:01:24,480 --> 00:01:25,740
一般情况下都是这样

56
00:01:25,740 --> 00:01:26,700
例如什么呢

57
00:01:26,700 --> 00:01:29,200
例如我们这个就是192.16810.25

58
00:01:29,200 --> 00:01:30,800
最关键就在于这个25

59
00:01:30,800 --> 00:01:32,300
那么关于这个直接广播

60
00:01:32,300 --> 00:01:33,240
这种广播地址

61
00:01:33,240 --> 00:01:34,880
它也有一种所谓的计算方式

62
00:01:34,880 --> 00:01:36,120
当然我们常见的

63
00:01:36,120 --> 00:01:39,620
一般的就是在这个地址的最后一位

64
00:01:39,620 --> 00:01:40,460
就是25

65
00:01:40,460 --> 00:01:42,340
这就是所谓的直接广播地址

66
00:01:42,340 --> 00:01:43,580
受限广播就是纯

67
00:01:43,580 --> 00:01:44,520
全部为一

68
00:01:44,520 --> 00:01:45,240
也就是四个25

69
00:01:45,240 --> 00:01:47,360
这是直接广播地址

70
00:01:47,360 --> 00:01:47,740
好

71
00:01:47,740 --> 00:01:48,700
了解了广播之后

72
00:01:48,700 --> 00:01:50,420
然后它三种方式的最后一个

73
00:01:50,420 --> 00:01:52,340
就是关于这个UDP的主播

74
00:01:52,340 --> 00:01:54,020
什么是主播呢

75
00:01:54,020 --> 00:01:54,940
主播我们可以看到

76
00:01:54,940 --> 00:01:56,680
就是把同样的信息

77
00:01:56,680 --> 00:01:58,460
传递给就是一组目的地

78
00:01:58,460 --> 00:01:59,880
也就是还是一对多

79
00:01:59,880 --> 00:02:01,800
但是这个一对多和广播有些区别

80
00:02:01,800 --> 00:02:02,800
广播是一对多

81
00:02:02,800 --> 00:02:03,460
这个多是所有

82
00:02:03,460 --> 00:02:04,740
而组播是什么呢

83
00:02:04,740 --> 00:02:05,180
也是一对多

84
00:02:05,180 --> 00:02:06,720
只不过这个多是指的

85
00:02:06,720 --> 00:02:08,100
我们组成员

86
00:02:08,100 --> 00:02:08,820
组成员

87
00:02:08,820 --> 00:02:10,160
例如我把它这一份数据

88
00:02:10,160 --> 00:02:11,940
发给这三个绿点相关的成员

89
00:02:11,940 --> 00:02:13,320
其他这三个黄点也不需要

90
00:02:13,320 --> 00:02:14,920
那么你就没有必要来广播

91
00:02:14,920 --> 00:02:16,340
组播就可以了

92
00:02:16,340 --> 00:02:17,660
那么对于组播

93
00:02:17,660 --> 00:02:18,740
他的这个地址范围呢

94
00:02:18,740 --> 00:02:21,120
就是从224.0.0到239

95
00:02:21,120 --> 00:02:21,720
然后3.25

96
00:02:21,720 --> 00:02:23,520
这是他的地址范围

97
00:02:23,520 --> 00:02:25,760
然后下面呢就是它的一些详细的

98
00:02:25,760 --> 00:02:27,040
一些这种地址的分类

99
00:02:27,040 --> 00:02:28,480
那么这个大家下来之后呢

100
00:02:28,480 --> 00:02:29,480
可以自行来了解一下

101
00:02:29,480 --> 00:02:31,260
了解了这三种

102
00:02:31,260 --> 00:02:34,760
就是三种的这个数据的传播方式之后

103
00:02:34,760 --> 00:02:36,480
然后接下来我们分别来看一下

104
00:02:36,480 --> 00:02:39,440
就是UDP这个所谓的就是三种通信方式

105
00:02:39,440 --> 00:02:42,220
在进行一对多的这种场景

106
00:02:42,220 --> 00:02:44,940
就是说单波一对多是如何处理

107
00:02:44,940 --> 00:02:46,560
广播一对多如何处理

108
00:02:46,560 --> 00:02:47,900
组播一对多如何来处理

109
00:02:47,900 --> 00:02:49,580
那么首先我们来看一下

110
00:02:49,580 --> 00:02:50,580
对于单波一对多

111
00:02:50,580 --> 00:02:52,540
那么单波在一对多的场景下

112
00:02:52,540 --> 00:02:53,100
我们可以看到

113
00:02:53,100 --> 00:02:55,180
就是说假如说同样的数据

114
00:02:55,180 --> 00:02:56,400
你要发送给十个客户端

115
00:02:56,400 --> 00:02:57,960
那么单薄的话是必须

116
00:02:57,960 --> 00:02:59,080
我们可以看到这个Sever

117
00:02:59,080 --> 00:03:00,580
假如说我们这里有三台PC1

118
00:03:00,580 --> 00:03:01,020
PC2

119
00:03:01,020 --> 00:03:01,460
PC3

120
00:03:01,460 --> 00:03:01,940
三台机器

121
00:03:01,940 --> 00:03:02,880
那么你这个服务端

122
00:03:02,880 --> 00:03:05,060
你最起码你要发送三次这个消息

123
00:03:05,060 --> 00:03:06,040
然后这三个消息

124
00:03:06,040 --> 00:03:07,500
然后通过层层流转发

125
00:03:07,500 --> 00:03:08,960
最终到达这个目标机器

126
00:03:08,960 --> 00:03:09,780
这是单薄

127
00:03:09,780 --> 00:03:11,320
所以说我们在这可以看到

128
00:03:11,320 --> 00:03:12,600
对于同一份数据

129
00:03:12,600 --> 00:03:13,960
如果存在多个接收者

130
00:03:13,960 --> 00:03:15,080
那我们这个服务端

131
00:03:15,080 --> 00:03:16,120
就需要发送与

132
00:03:16,120 --> 00:03:18,920
发送与这个接收端相同的单薄数据

133
00:03:18,920 --> 00:03:21,500
所以说在这种情况下

134
00:03:21,500 --> 00:03:23,160
假如说你的接收者成百上千

135
00:03:23,160 --> 00:03:24,080
或者更多的时候

136
00:03:24,080 --> 00:03:25,560
那这会极大的加重

137
00:03:25,560 --> 00:03:27,220
我们这个服务端的一个压力

138
00:03:27,220 --> 00:03:29,120
所以说单波在这种一对多的场景下

139
00:03:29,120 --> 00:03:30,500
其实就并不太合适了

140
00:03:30,500 --> 00:03:31,680
那么第二点

141
00:03:31,680 --> 00:03:32,960
我们再来看这个广播

142
00:03:32,960 --> 00:03:33,760
如何面对一对多

143
00:03:33,760 --> 00:03:35,800
那么广播面对一对多

144
00:03:35,800 --> 00:03:36,420
我们可以看到

145
00:03:36,420 --> 00:03:37,220
首先这个

146
00:03:37,220 --> 00:03:38,740
我们只需要在这里

147
00:03:38,740 --> 00:03:39,300
我们可以看到

148
00:03:39,300 --> 00:03:41,400
对于PCPCR这样一个数据

149
00:03:41,400 --> 00:03:43,000
同一个局域网当中

150
00:03:43,000 --> 00:03:45,080
我们Serv只需要发一次就可以了

151
00:03:45,080 --> 00:03:47,640
然后PCPCR就都收到了这条消息

152
00:03:47,640 --> 00:03:48,780
当然这里一定要就是

153
00:03:48,780 --> 00:03:49,660
了解一点的就是

154
00:03:49,660 --> 00:03:50,700
广播数据班

155
00:03:50,700 --> 00:03:53,500
被限制在我们这个就是局网当中

156
00:03:53,500 --> 00:03:55,020
也就是说广播数据和不数据包

157
00:03:55,020 --> 00:03:56,780
并不能穿越我们的局网

158
00:03:56,780 --> 00:03:58,020
也不能进行这个路由转发

159
00:03:58,020 --> 00:04:00,780
只是在当前这个局网当中

160
00:04:00,780 --> 00:04:02,640
所以说我们待会就可以看到

161
00:04:02,640 --> 00:04:04,580
一旦有设备发送广播数据

162
00:04:04,580 --> 00:04:05,640
那么广播域内

163
00:04:05,640 --> 00:04:07,340
所有设备就都会收到这个数据包

164
00:04:07,340 --> 00:04:09,180
那这里就涉及到一个问题

165
00:04:09,180 --> 00:04:11,180
就是说假说还有其他更多的机器

166
00:04:11,180 --> 00:04:12,420
那我并不是希望

167
00:04:12,420 --> 00:04:13,880
就是说把这个数据发给所有机器

168
00:04:13,880 --> 00:04:14,680
那这个时候

169
00:04:14,680 --> 00:04:16,420
你其他不需要接收这个数据的机器

170
00:04:16,420 --> 00:04:18,140
那你就得不得不去消耗资源去处理

171
00:04:18,140 --> 00:04:19,900
所以大量的广播数据包

172
00:04:19,900 --> 00:04:22,340
将消耗网络贷款和收费资源

173
00:04:22,340 --> 00:04:22,920
这是广播

174
00:04:22,920 --> 00:04:24,340
所以说大家可以看到

175
00:04:24,340 --> 00:04:25,680
在这个IPv6当中

176
00:04:25,680 --> 00:04:26,980
广播的这种传输方式

177
00:04:26,980 --> 00:04:28,340
已经被取消了

178
00:04:28,340 --> 00:04:30,020
但是我们现在这个IPv4当中

179
00:04:30,020 --> 00:04:32,020
广播还是依然存在的

180
00:04:32,020 --> 00:04:33,320
那么最后我们再来看一下

181
00:04:33,320 --> 00:04:33,880
关于这个就是

182
00:04:33,880 --> 00:04:35,460
主播面对一对多的场景

183
00:04:35,460 --> 00:04:37,680
那么对于这个主播面对一对多

184
00:04:37,680 --> 00:04:39,120
那首先我们来看一下

185
00:04:39,120 --> 00:04:40,180
例如这个主播

186
00:04:40,180 --> 00:04:42,380
要分别发送给PCPC2和PC3

187
00:04:42,380 --> 00:04:44,240
那么首先我们这个服务端

188
00:04:44,240 --> 00:04:46,620
我们只需要发一份数据就可以了

189
00:04:46,620 --> 00:04:47,620
一份数据过来以后

190
00:04:47,620 --> 00:04:48,800
然后经过路由转发

191
00:04:48,800 --> 00:04:51,120
最终到达了这个目标机器

192
00:04:51,120 --> 00:04:52,280
最终到达目标机器

193
00:04:52,280 --> 00:04:52,640
好

194
00:04:52,640 --> 00:04:53,520
那么主播呢

195
00:04:53,520 --> 00:04:55,080
就非常适合这种一对多的模型

196
00:04:55,080 --> 00:04:55,780
因为什么呢

197
00:04:55,780 --> 00:04:57,780
因为它是有选择性的进行发送

198
00:04:57,780 --> 00:04:59,560
只有加入特定主播组的成员

199
00:04:59,560 --> 00:05:02,060
才会接收到我们这个所谓的主播数据

200
00:05:02,060 --> 00:05:04,300
另外当我们存在多个主播组成员的时候

201
00:05:04,300 --> 00:05:05,100
这个源

202
00:05:05,100 --> 00:05:07,080
也就是我们的发送源

203
00:05:07,080 --> 00:05:08,560
或者说主播源

204
00:05:08,560 --> 00:05:10,320
它不需要发送多份数据

205
00:05:10,320 --> 00:05:11,640
它只要发一份就可以了

206
00:05:11,640 --> 00:05:14,300
然后这个我们整个主播网络当中

207
00:05:14,300 --> 00:05:15,600
这些主播网络设备

208
00:05:15,600 --> 00:05:16,680
会根据你的实际需要

209
00:05:16,680 --> 00:05:17,520
就进行转发

210
00:05:17,520 --> 00:05:20,060
那么数据流

211
00:05:20,060 --> 00:05:20,900
我们这个数据流

212
00:05:20,900 --> 00:05:21,620
只发送给就是

213
00:05:21,620 --> 00:05:23,420
加入这个祖博组的接收纸

214
00:05:23,420 --> 00:05:25,540
那不需要这个数据的设备

215
00:05:25,540 --> 00:05:27,480
是不会收到这个祖博流量的

216
00:05:27,480 --> 00:05:29,320
所以说相同的数据报纹

217
00:05:29,320 --> 00:05:30,160
在一段链路上

218
00:05:30,160 --> 00:05:30,880
仅有一份数据

219
00:05:30,880 --> 00:05:31,760
那么这样的话呢

220
00:05:31,760 --> 00:05:32,720
就大大提高了

221
00:05:32,720 --> 00:05:34,560
我们这个网络资源的利用率

222
00:05:34,560 --> 00:05:35,980
所以说我们这样就了解到

223
00:05:35,980 --> 00:05:37,540
在这种就是

224
00:05:37,540 --> 00:05:39,860
UDP一对多的通信场景下

225
00:05:39,860 --> 00:05:41,520
如果是单播要一对多

226
00:05:41,520 --> 00:05:42,640
那么它需要就是

227
00:05:42,640 --> 00:05:44,040
多个客户端就发多次

228
00:05:44,040 --> 00:05:45,220
而广播一对多

229
00:05:45,220 --> 00:05:46,620
就是哪怕你这客户端不需要

230
00:05:46,620 --> 00:05:47,560
就是挂播一次

231
00:05:47,560 --> 00:05:49,260
然后所有客户端就都收到了

232
00:05:49,260 --> 00:05:49,920
那么

233
00:05:49,920 --> 00:05:52,120
组播才是什么呢

234
00:05:52,120 --> 00:05:53,460
它是更合适用来这种

235
00:05:53,460 --> 00:05:55,460
就是进行为多个客户端

236
00:05:55,460 --> 00:05:56,540
传播数据的一种方式

237
00:05:56,540 --> 00:05:57,440
这就是组播

