1
00:00:00,760 --> 00:00:02,300
好,那么这节课我们就来看一下

2
00:00:02,300 --> 00:00:04,100
中间键的一个合成

3
00:00:04,100 --> 00:00:06,140
在Core版里面

4
00:00:06,140 --> 00:00:07,940
它有一个叫做CoreCompos的模块

5
00:00:07,940 --> 00:00:09,480
它可以将多个中间键给合为一个

6
00:00:09,480 --> 00:00:11,000
那么它是做什么用的呢

7
00:00:11,000 --> 00:00:12,280
比如说我们现在有一个

8
00:00:12,280 --> 00:00:13,060
Log模块

9
00:00:13,060 --> 00:00:15,360
是不是刚才我们看过了可以打印一些日志

10
00:00:15,360 --> 00:00:16,120
包括呢

11
00:00:16,120 --> 00:00:18,680
有个命方法可以给我们返回一个好的Word

12
00:00:18,680 --> 00:00:20,220
因为我们有一些中间键

13
00:00:20,220 --> 00:00:22,020
可能我们想把两个和三个

14
00:00:22,020 --> 00:00:22,780
把它给合为一个

15
00:00:22,780 --> 00:00:24,840
怎么去做就可以使用Compos这个模块

16
00:00:24,840 --> 00:00:26,360
那么Compos它到底是什么呢

17
00:00:26,360 --> 00:00:28,160
其实同学们可能需要去了解一下

18
00:00:28,160 --> 00:00:29,440
我们前段的函数是编程

19
00:00:29,700 --> 00:00:31,520
如果说熟悉react的同学们可能学习过

20
00:00:31,520 --> 00:00:33,800
比如说像readbox里面

21
00:00:33,800 --> 00:00:35,340
就有很多使用compos的方法

22
00:00:35,340 --> 00:00:37,120
然后呢包括像react这样一个酷

23
00:00:37,120 --> 00:00:39,680
它整个酷的设计也是具有函数式的

24
00:00:39,680 --> 00:00:40,460
所以说呢

25
00:00:40,460 --> 00:00:42,240
compos它是属于函数式里面的内容

26
00:00:42,240 --> 00:00:43,020
那么到底是什么

27
00:00:43,020 --> 00:00:44,040
那么到底什么是compos呢

28
00:00:44,040 --> 00:00:45,820
这里我来给大家简单的去介绍一下

29
00:00:45,820 --> 00:00:48,140
那么compos比如说

30
00:00:48,140 --> 00:00:50,180
比如说我们现在有这样一个场景

31
00:00:50,180 --> 00:00:52,740
我们有一个grating这样一个方法

32
00:00:52,740 --> 00:00:54,780
我们闯入一个firstname和一个lastname

33
00:00:54,780 --> 00:00:56,320
它会返回一个什么呢

34
00:00:56,320 --> 00:00:58,620
返回一个比如说hello

35
00:00:58,880 --> 00:01:03,740
first name是什么,nast name是什么,比如说我们的first name是勒布朗,nast name是詹姆斯

36
00:01:03,740 --> 00:01:07,840
我们调用grating传入两个参数就可以返回一个,hello勒布朗詹姆斯

37
00:01:07,840 --> 00:01:11,420
包括还有个tubeupper,什么意思是不是把我们的小写改为大写

38
00:01:11,420 --> 00:01:17,560
他的方法就是str.传入一个参数,然后把它给,调用了tubeupcase把它改为大写

39
00:01:17,560 --> 00:01:22,180
然后呢执行一个campus,campus是函数式里面的一个方法,那么比如说我们去

40
00:01:22,180 --> 00:01:25,760
通过campus传入在两个方形,一个tubeupper一个grating

41
00:01:26,280 --> 00:01:27,880
他能返回一个新的方法叫做FN

42
00:01:27,880 --> 00:01:31,340
当我们调用FN的这样一个方法传入什么呢

43
00:01:31,340 --> 00:01:33,080
传入参数一个Jack一个什么意思

44
00:01:33,080 --> 00:01:34,280
是不是

45
00:01:34,280 --> 00:01:38,280
是不是传入是FestName和LastName

46
00:01:38,280 --> 00:01:39,440
那么他会得到一个什么结果

47
00:01:39,440 --> 00:01:42,080
其实呢他是可以得到一个结果叫做

48
00:01:42,080 --> 00:01:46,460
HelloJackSmith

49
00:01:46,460 --> 00:01:48,920
其实可以得到这样一个结果

50
00:01:48,920 --> 00:01:50,560
那么Compost它到底是什么意思

51
00:01:50,560 --> 00:01:51,060
我们来看一下

52
00:01:51,060 --> 00:01:53,600
首先Compost它的参数是函数

53
00:01:53,600 --> 00:01:54,780
返回的也是一个函数

54
00:01:54,780 --> 00:01:56,260
那么我们刚才是不是执行了

55
00:01:56,260 --> 00:01:57,960
我们刚才是不是

56
00:01:57,960 --> 00:02:00,860
首先我们刚才是不是执行了个Compos方法

57
00:02:00,860 --> 00:02:02,220
他接受什么参数

58
00:02:02,220 --> 00:02:03,260
是不是接受两个方法

59
00:02:03,260 --> 00:02:04,400
一个to upper一个grading

60
00:02:04,400 --> 00:02:05,280
他们是不是都是函数

61
00:02:05,280 --> 00:02:07,280
然后呢返回了一个fn这样一个函数

62
00:02:07,280 --> 00:02:07,680
对吧

63
00:02:07,680 --> 00:02:08,000
好

64
00:02:08,000 --> 00:02:09,260
那么大家注意看第二句话

65
00:02:09,260 --> 00:02:13,000
因为除了第一个函数的接受参数

66
00:02:13,000 --> 00:02:14,400
其他的函数接受参数

67
00:02:14,400 --> 00:02:15,680
都是上一个函数的返回值

68
00:02:15,680 --> 00:02:18,760
那么初始函数的参数是多元的

69
00:02:18,760 --> 00:02:21,100
而其他函数接受的只是1元的

70
00:02:21,100 --> 00:02:22,060
什么意思

71
00:02:22,060 --> 00:02:24,440
我们Compos是不是执行了

72
00:02:24,440 --> 00:02:26,460
接收的是to upper和grading

73
00:02:26,460 --> 00:02:28,500
那么他接收的参数是谁的

74
00:02:28,500 --> 00:02:29,960
是不是接收的参数是咱们grading

75
00:02:29,960 --> 00:02:30,560
接收的两个参数

76
00:02:30,560 --> 00:02:32,640
first name和last name

77
00:02:32,640 --> 00:02:35,400
compose函数可以接收任意的参数

78
00:02:35,400 --> 00:02:37,160
那么所有的参数都是函数

79
00:02:37,160 --> 00:02:39,160
而且执行方向是自右向左的

80
00:02:39,160 --> 00:02:41,620
初始的函数一定要放到参数的最右面

81
00:02:41,620 --> 00:02:42,620
什么意思

82
00:02:42,620 --> 00:02:43,700
是不是说明咱们compose函数

83
00:02:43,700 --> 00:02:45,020
是不是自右向左去执行了

84
00:02:45,020 --> 00:02:47,540
而且他刚才说了

85
00:02:47,540 --> 00:02:48,400
除了第一个函数

86
00:02:48,400 --> 00:02:50,060
他所有的函数接收的参数

87
00:02:50,060 --> 00:02:50,900
必须是一元的

88
00:02:50,900 --> 00:02:52,340
也就是说只有grading这样一个方法

89
00:02:52,340 --> 00:02:53,180
它可以是多元的

90
00:02:53,180 --> 00:02:54,720
包括像to upper

91
00:02:54,720 --> 00:02:56,000
你如果说再去添加一个

92
00:02:56,000 --> 00:02:57,280
什么to小写

93
00:02:57,280 --> 00:02:58,560
那你也只能加入一个参数

94
00:02:58,560 --> 00:03:00,600
比如说咱们的grating是不是返回了一个字不串

95
00:03:00,600 --> 00:03:03,940
然后在一个字不串是不是会传入咱们的to upper这一个方法

96
00:03:03,940 --> 00:03:05,220
他是不是接受一个参数啊

97
00:03:05,220 --> 00:03:06,240
会把它改为大写

98
00:03:06,240 --> 00:03:08,540
这里呢其实就是compose他一个简单介绍

99
00:03:08,540 --> 00:03:10,340
那么如果说对这块感兴趣的同学呢

100
00:03:10,340 --> 00:03:11,620
可以去看一下这一个链接

101
00:03:11,620 --> 00:03:13,140
老师呢把链接你贴在这里

102
00:03:13,140 --> 00:03:16,480
好那么这里呢就是compose他的一个概念

103
00:03:16,480 --> 00:03:17,760
那么我们core

104
00:03:17,760 --> 00:03:21,340
我们core的compose模块其实和他是一样的

105
00:03:21,340 --> 00:03:23,140
我们可以把两个中间件合为一个

106
00:03:23,140 --> 00:03:24,420
那么我们的

107
00:03:24,420 --> 00:03:25,700
比如说我们的

108
00:03:25,700 --> 00:03:27,480
中间件他是自由向左执行的

109
00:03:27,480 --> 00:03:28,260
比如说我们的命

110
00:03:28,260 --> 00:03:31,060
比如说我们的我们是不是先执行命再执行log然后把它

111
00:03:31,060 --> 00:03:32,340
合为一个

112
00:03:32,340 --> 00:03:35,160
好这里就是我们这几个内容我们来总结一下

113
00:03:35,160 --> 00:03:42,340
我们刚才是不是讲到了

114
00:03:42,340 --> 00:03:44,120
中间见的

115
00:03:44,120 --> 00:03:46,180
合成了那么

116
00:03:46,180 --> 00:03:47,960
需要用什么是不是call

117
00:03:47,960 --> 00:03:48,980
compose

118
00:03:48,980 --> 00:03:51,040
那么compose他是一个什么概念呢

119
00:03:51,340 --> 00:03:56,460
首先它是不是自诱

120
00:03:56,460 --> 00:04:01,960
向主执行

121
00:04:01,960 --> 00:04:03,360
然后呢

122
00:04:03,360 --> 00:04:08,620
第一个函数可以接收

123
00:04:08,620 --> 00:04:11,060
多个参数吧

124
00:04:11,060 --> 00:04:16,120
并返回一个值

125
00:04:16,120 --> 00:04:16,520
对吧

126
00:04:16,520 --> 00:04:18,160
那么后面的函数

127
00:04:21,340 --> 00:04:27,340
只能接收一个参数并返回一个值

128
00:04:27,340 --> 00:04:29,340
这里就是我们的一个Compost它的概念

129
00:04:29,340 --> 00:04:31,340
那么Core Compost它的一个应用场景

130
00:04:31,340 --> 00:04:33,340
应用场景是什么呢

131
00:04:33,340 --> 00:04:37,340
其实它的应用场景是不是就是合并我们的一些合并中间间呢

132
00:04:37,340 --> 00:04:40,340
这里呢我就不给大家去演示代码了

133
00:04:40,340 --> 00:04:42,340
其实呢使用的场景是比较少的

134
00:04:42,340 --> 00:04:46,340
同学们可以到时候有这样的需求可以再去看一下到底怎么样去使用

135
00:04:46,340 --> 00:04:48,200
好这里呢就是我们这几个的内容

