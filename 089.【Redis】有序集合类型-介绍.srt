1
00:00:00,360 --> 00:00:02,220
好 这节课我们就来看一下有序集合类型

2
00:00:02,220 --> 00:00:04,500
我们首先这节课我们来看一下它的一个介绍

3
00:00:04,500 --> 00:00:07,000
有序集合类型我们看它的名字

4
00:00:07,000 --> 00:00:08,740
集合刚才讲过了

5
00:00:08,740 --> 00:00:10,520
集合有没有顺序 没有吧

6
00:00:10,520 --> 00:00:12,200
那么有序集合就是有顺序的集合

7
00:00:12,200 --> 00:00:15,820
那么有顺序的集合它什么意思呢

8
00:00:15,820 --> 00:00:19,040
其实它是为集合中的每一个元素都关联的一个分数

9
00:00:19,040 --> 00:00:22,420
也就是说它为集合中的每一个元素关联的一个数字

10
00:00:22,420 --> 00:00:25,220
通过这个数字你可以对它进行一些排序

11
00:00:25,220 --> 00:00:27,860
其实这就是有序的一个根源

12
00:00:27,860 --> 00:00:29,520
你通过数字的大小来判断

13
00:00:29,520 --> 00:00:31,260
它们之间的顺序是什么样的

14
00:00:31,260 --> 00:00:32,560
那么有序集合

15
00:00:32,560 --> 00:00:35,380
它和列表又有哪些相似之处呢

16
00:00:35,380 --> 00:00:36,180
或者哪些区别呢

17
00:00:36,180 --> 00:00:37,800
因为我们的列表它也是有序的吧

18
00:00:37,800 --> 00:00:38,000
为什么

19
00:00:38,000 --> 00:00:39,400
列表可以L push或者R push

20
00:00:39,400 --> 00:00:40,720
你是有顺序去插入

21
00:00:40,720 --> 00:00:42,340
那么它们的相同点就是说

22
00:00:42,340 --> 00:00:43,940
你二者都是有序的

23
00:00:43,940 --> 00:00:44,440
而且呢

24
00:00:44,440 --> 00:00:45,720
有序集合和咱们的列表

25
00:00:45,720 --> 00:00:47,140
都可以去获取某一范围的元素

26
00:00:47,140 --> 00:00:47,720
但是呢

27
00:00:47,720 --> 00:00:49,080
它们两个也有很大的区别

28
00:00:49,080 --> 00:00:50,880
也使它们的应用场景是不同的

29
00:00:50,880 --> 00:00:51,160
为什么

30
00:00:51,160 --> 00:00:53,800
因为它们的原理是有巨大的不同

31
00:00:53,800 --> 00:00:54,820
怎么样理解呢

32
00:00:54,820 --> 00:00:56,900
列表是通过列表去实现的

33
00:00:56,900 --> 00:00:57,780
靠近两端的数据

34
00:00:57,780 --> 00:00:59,720
或许是非常快的

35
00:00:59,720 --> 00:01:00,820
中间的是速度比较慢

36
00:01:00,820 --> 00:01:01,820
这个前面讲过吧

37
00:01:01,820 --> 00:01:03,360
那么它的应用场景就是

38
00:01:03,360 --> 00:01:04,800
比如说我们去看一些新闻

39
00:01:04,800 --> 00:01:07,360
新闻我们总是去读取咱们最新的数据吧

40
00:01:07,360 --> 00:01:08,820
因为我们总会去读取咱们

41
00:01:08,820 --> 00:01:12,280
最前面的某几十条或者几百条数据

42
00:01:12,280 --> 00:01:13,420
比如说像日志也是

43
00:01:13,420 --> 00:01:14,600
我们去读取头尾的日志

44
00:01:14,600 --> 00:01:15,760
很少去读取中间吧

45
00:01:15,760 --> 00:01:16,220
包括新闻

46
00:01:16,220 --> 00:01:17,540
我们也很少去看中间的新闻吧

47
00:01:17,540 --> 00:01:18,600
一般都是最新的

48
00:01:18,600 --> 00:01:20,480
那么他们实现原理其实也不一样

49
00:01:20,480 --> 00:01:21,360
像有序集合

50
00:01:21,360 --> 00:01:23,600
它是使用闪列表和跳跃表去实现的

51
00:01:23,600 --> 00:01:25,260
所以你读取中间的部分数据

52
00:01:25,260 --> 00:01:26,940
速度它也是很快的

53
00:01:26,940 --> 00:01:28,460
时间复杂度它是O-N

54
00:01:28,460 --> 00:01:29,200
什么意思

55
00:01:29,200 --> 00:01:33,260
它的复杂度属于O-N和N的平方之间

56
00:01:33,260 --> 00:01:36,040
那么列表它不能简单的去调整某个元素的位置

57
00:01:36,040 --> 00:01:37,360
但是有序集合是可以的

58
00:01:37,360 --> 00:01:40,040
你可以通过去更改这个元素的一些分数

59
00:01:40,040 --> 00:01:41,740
去解决这样一个问题

60
00:01:41,740 --> 00:01:43,200
怎么样去理解

61
00:01:43,200 --> 00:01:45,600
我们列表中为什么不能简单的去调整某个元素的位置

62
00:01:45,600 --> 00:01:46,400
同学们

63
00:01:46,400 --> 00:01:50,000
这里我们来给大家画一幅图来简单的去讲解一下

64
00:01:50,000 --> 00:01:51,080
比如说我们来看一下

65
00:01:51,080 --> 00:01:53,680
看一下我们的

66
00:01:53,680 --> 00:01:54,520
列表

67
00:01:54,520 --> 00:01:56,860
为什么它换位置比较麻烦

68
00:01:56,860 --> 00:01:58,060
假如说

69
00:01:58,060 --> 00:01:58,880
假如说我们的列表

70
00:01:58,880 --> 00:02:00,240
是这样的

71
00:02:00,240 --> 00:02:04,480
我们有一个长度为6的一个列表

72
00:02:04,480 --> 00:02:05,300
大家注意看

73
00:02:05,300 --> 00:02:06,160
然后呢

74
00:02:06,160 --> 00:02:08,000
列表之间是不是会通过

75
00:02:08,000 --> 00:02:11,920
列表之间是不是会通过一些

76
00:02:11,920 --> 00:02:13,400
它就像一根绳子一样

77
00:02:13,400 --> 00:02:15,380
会有一些节点去连接啊

78
00:02:15,380 --> 00:02:16,100
之前我们是不是讲过

79
00:02:16,100 --> 00:02:17,200
但每一个节点上面

80
00:02:17,200 --> 00:02:18,200
它都有两个属性吧

81
00:02:18,200 --> 00:02:20,320
每一个节点上面都有两个属性

82
00:02:20,320 --> 00:02:20,740
一个是什么

83
00:02:20,740 --> 00:02:21,720
所以一个是next

84
00:02:21,720 --> 00:02:23,300
也就是它下一个节点的值是什么

85
00:02:23,300 --> 00:02:24,100
然后呢

86
00:02:24,100 --> 00:02:25,800
还有一个before

87
00:02:25,800 --> 00:02:26,940
也就是它前面

88
00:02:26,940 --> 00:02:28,020
一个节点的值是什么

89
00:02:28,020 --> 00:02:29,740
那么假如说我们需要去修改

90
00:02:29,740 --> 00:02:32,080
假如说我们需要去修改

91
00:02:32,080 --> 00:02:34,320
我们需要去修改

92
00:02:34,320 --> 00:02:35,940
咱们的二号位置和五号位置

93
00:02:35,940 --> 00:02:38,920
咱们要把它们进行一个调换

94
00:02:38,920 --> 00:02:41,100
我们需要做多少次操作

95
00:02:41,100 --> 00:02:41,580
同学们

96
00:02:41,580 --> 00:02:43,420
我们首先二号位置

97
00:02:43,420 --> 00:02:45,520
它的二号位置

98
00:02:45,520 --> 00:02:47,580
是不是需要换到我们的六号位置

99
00:02:47,580 --> 00:02:48,340
五号位置

100
00:02:48,340 --> 00:02:48,580
对吧

101
00:02:48,580 --> 00:02:50,460
我们的二号

102
00:02:50,460 --> 00:02:52,560
和5号是不是要兑换了

103
00:02:52,560 --> 00:02:54,440
那么2号和5号兑换的过程

104
00:02:54,440 --> 00:02:55,360
比如说我们的5

105
00:02:55,360 --> 00:02:56,520
比如说此时我们把2

106
00:02:56,520 --> 00:02:57,980
我们把2改成了5

107
00:02:57,980 --> 00:02:58,420
把2

108
00:02:58,420 --> 00:03:01,440
比如说他们调换的位置

109
00:03:01,440 --> 00:03:02,600
这样会造成一个什么问题啊

110
00:03:02,600 --> 00:03:03,320
同学们

111
00:03:03,320 --> 00:03:04,080
我们的1号

112
00:03:04,080 --> 00:03:04,800
我们的1号

113
00:03:04,800 --> 00:03:05,880
它的next的属性要不要改

114
00:03:05,880 --> 00:03:07,780
我们的1号以前的next的属性

115
00:03:07,780 --> 00:03:08,280
它是2吧

116
00:03:08,280 --> 00:03:09,160
此时我们还改成5

117
00:03:09,160 --> 00:03:10,180
包括呢

118
00:03:10,180 --> 00:03:10,940
我们的5

119
00:03:10,940 --> 00:03:12,060
我们的5号节点

120
00:03:12,060 --> 00:03:12,960
我们的5号节点

121
00:03:12,960 --> 00:03:13,700
以前的next是几

122
00:03:13,700 --> 00:03:14,500
是6吧

123
00:03:14,500 --> 00:03:15,440
那么现在改成几

124
00:03:15,440 --> 00:03:16,000
是不是要改成3

125
00:03:16,000 --> 00:03:16,700
对吧

126
00:03:16,700 --> 00:03:18,600
比如说这里是1

127
00:03:18,600 --> 00:03:27,520
我们的1号以前next是2

128
00:03:27,520 --> 00:03:28,100
现在改为5

129
00:03:28,100 --> 00:03:29,320
那么我们的5

130
00:03:29,320 --> 00:03:30,180
以前next是6

131
00:03:30,180 --> 00:03:30,900
现在改为3吧

132
00:03:30,900 --> 00:03:31,820
包括我们的3

133
00:03:31,820 --> 00:03:33,900
before以前是多少

134
00:03:33,900 --> 00:03:34,380
是2吧

135
00:03:34,380 --> 00:03:34,880
现在改为5

136
00:03:34,880 --> 00:03:36,520
是不是我们仅仅去调换列表中

137
00:03:36,520 --> 00:03:37,380
两个节点的位置

138
00:03:37,380 --> 00:03:38,300
都非常的复杂

139
00:03:38,300 --> 00:03:39,040
非常的麻烦

140
00:03:39,040 --> 00:03:40,380
所以我们的列表

141
00:03:40,380 --> 00:03:41,560
不适合去做一些

142
00:03:41,560 --> 00:03:43,780
比如说我们两个index之间的互换

143
00:03:43,780 --> 00:03:45,040
这样是很复杂的

144
00:03:45,040 --> 00:03:45,880
但是为什么

145
00:03:45,880 --> 00:03:47,840
为什么

146
00:03:47,840 --> 00:03:49,360
为什么那么有序集合

147
00:03:49,360 --> 00:03:50,500
却可以去调整呢

148
00:03:50,500 --> 00:03:51,040
因为它

149
00:03:51,040 --> 00:03:53,560
它是使用散列表和跳跃表去实现的

150
00:03:53,560 --> 00:03:54,040
什么是散列呢

151
00:03:54,040 --> 00:03:54,660
是不是就是我们的hash

152
00:03:54,660 --> 00:03:55,700
hash就是一个对象

153
00:03:55,700 --> 00:03:56,980
我们对象想去调换

154
00:03:56,980 --> 00:03:58,720
你两个index之间的位置

155
00:03:58,720 --> 00:03:59,220
是不是很简单

156
00:03:59,220 --> 00:04:00,880
你只需要去重新给它复制就可以了

157
00:04:00,880 --> 00:04:01,780
比如说我们的数字

158
00:04:01,780 --> 00:04:04,200
它的index0和10

159
00:04:04,200 --> 00:04:05,360
两个数字你要互换

160
00:04:05,360 --> 00:04:06,140
你直接去

161
00:04:06,140 --> 00:04:07,360
比如说index0

162
00:04:07,360 --> 00:04:08,560
你给它改为index10的值

163
00:04:08,560 --> 00:04:10,420
你只需要做一个数字的兑换就可以了

164
00:04:10,420 --> 00:04:10,960
直接去复制

165
00:04:10,960 --> 00:04:12,080
但是我们的列表不可以

166
00:04:12,080 --> 00:04:13,680
你需要去调整

167
00:04:13,680 --> 00:04:14,120
二

168
00:04:14,120 --> 00:04:15,220
你需要去调整

169
00:04:15,220 --> 00:04:17,180
咱们列表中所有数据之间的关系

170
00:04:17,180 --> 00:04:17,500
对吧

171
00:04:17,500 --> 00:04:18,360
非常的复杂

172
00:04:18,360 --> 00:04:21,740
但是你速度快会造成一些什么问题呢

173
00:04:21,740 --> 00:04:23,700
造成你的有序集合

174
00:04:23,700 --> 00:04:25,260
要比列表类型更耗费内存

175
00:04:25,260 --> 00:04:26,900
也就是说在我们呢

176
00:04:26,900 --> 00:04:28,520
因为我们在面试中经常会去考一些算法

177
00:04:28,520 --> 00:04:29,880
其实算法的核心是什么

178
00:04:29,880 --> 00:04:31,280
你要么快要么占内存

179
00:04:31,280 --> 00:04:32,300
你不可能说兼得了

180
00:04:32,300 --> 00:04:33,200
鱼与熊长不可兼得

181
00:04:33,200 --> 00:04:36,600
因为你速度快一定是去消耗内存去换取了

182
00:04:36,600 --> 00:04:37,060
那么这里呢

183
00:04:37,060 --> 00:04:38,620
同学们去后面去做一些真实的项目

184
00:04:38,620 --> 00:04:39,720
或者说去研究算法的时候

185
00:04:39,720 --> 00:04:41,440
是可以去感受到的

186
00:04:41,440 --> 00:04:42,260
不存在说

187
00:04:42,260 --> 00:04:43,720
又这个世界上不存在

188
00:04:43,720 --> 00:04:46,320
又快又不占内存的东西

189
00:04:46,320 --> 00:04:47,760
你要么快要么占内存

190
00:04:47,760 --> 00:04:48,840
两个只能取一样

191
00:04:48,840 --> 00:04:50,500
所以呢我们的有序集合

192
00:04:50,500 --> 00:04:50,980
它的速度快

193
00:04:50,980 --> 00:04:51,680
但是它占内存

194
00:04:51,680 --> 00:04:52,400
我们的列表

195
00:04:52,400 --> 00:04:53,740
虽然说读取中间和换位置慢

196
00:04:53,740 --> 00:04:55,460
但是呢占了内存小

197
00:04:55,460 --> 00:04:58,440
好这里呢就是我们这节课的内容

