1
00:00:00,520 --> 00:00:02,300
好 接下来我们来看一下列表类型

2
00:00:02,300 --> 00:00:04,100
那么列表类型和前面内容一样

3
00:00:04,100 --> 00:00:08,440
我们会介绍一下它的概念 然后讲一些命令 包括他们的一些使用场景

4
00:00:08,440 --> 00:00:09,980
然后呢会有一些补充的命令

5
00:00:09,980 --> 00:00:11,260
好 我们先来看一下

6
00:00:11,260 --> 00:00:14,080
对列表类型它的介绍

7
00:00:14,080 --> 00:00:17,160
首先呢 列表类型是可以存储一个有序的自不穿列表

8
00:00:17,160 --> 00:00:21,000
那么场面的操作是像列表两端添加元素 或者获得列表的某一个片段

9
00:00:21,000 --> 00:00:22,280
那么列表

10
00:00:22,280 --> 00:00:24,840
不知道大家有没有想到一个很熟悉的东西 叫做宿主

11
00:00:24,840 --> 00:00:27,400
那么列表它和宿主有什么区别 它是宿主吗

12
00:00:28,420 --> 00:00:43,440
在列表类型

13
00:00:43,440 --> 00:00:44,460
有问题说明你的

14
00:00:44,460 --> 00:00:48,040
列表它不管有多长你比如说你有一个几千万个元素的列表

15
00:00:48,040 --> 00:00:50,600
你去获取头部或者尾部的十条技术也是非常快的

16
00:00:50,600 --> 00:00:53,420
和获取20个元素列表的头尾十条技术速度是一样的

17
00:00:53,420 --> 00:00:56,240
也就说明列表它去操作头尾的元素速度非常快

18
00:00:56,240 --> 00:00:59,320
但是呢使用列表的代价是通过索引访问元素比较慢

19
00:00:59,320 --> 00:01:02,900
那么这里呢刚才我们提到一个问题就是说什么呢

20
00:01:02,900 --> 00:01:04,680
和

21
00:01:04,680 --> 00:01:06,480
和js的

22
00:01:06,480 --> 00:01:07,760
数组有

23
00:01:07,760 --> 00:01:08,780
什么区别

24
00:01:08,780 --> 00:01:12,620
这里老师的话一幅图来给大家看一下

25
00:01:13,440 --> 00:01:18,220
假如说我们

26
00:01:18,220 --> 00:01:20,840
我们js里面的宿主是怎么样去操作的呀

27
00:01:20,840 --> 00:01:23,160
大家记了吗

28
00:01:23,160 --> 00:01:25,680
是不是咱们去挖了一个array等于

29
00:01:25,680 --> 00:01:27,680
01234

30
00:01:27,680 --> 00:01:29,480
但是他在一个宿主打完出来

31
00:01:29,480 --> 00:01:30,820
是不是实际上是一个对象啊同学们

32
00:01:30,820 --> 00:01:32,160
如果说不信的话

33
00:01:32,160 --> 00:01:32,980
我们可以来看一下

34
00:01:32,980 --> 00:01:35,500
我们通过浏览器来看一下

35
00:01:35,500 --> 00:01:40,000
应该是开放者工具

36
00:01:40,000 --> 00:01:44,640
首先比如说我们去定义一个变量

37
00:01:44,640 --> 00:01:46,320
等于一个数组

38
00:01:46,320 --> 00:01:46,960
012

39
00:01:46,960 --> 00:01:47,380
好

40
00:01:47,380 --> 00:01:49,920
我们把AR在一个对象打印出来看一下

41
00:01:49,920 --> 00:01:51,780
大家看到没有

42
00:01:51,780 --> 00:01:52,600
它是一个数组

43
00:01:52,600 --> 00:01:53,220
但是呢

44
00:01:53,220 --> 00:01:54,320
它会有一些属性

45
00:01:54,320 --> 00:01:54,900
比如说

46
00:01:54,900 --> 00:01:56,280
它有一个属性为0

47
00:01:56,280 --> 00:01:56,860
值为0

48
00:01:56,860 --> 00:01:58,240
说明什么问题

49
00:01:58,240 --> 00:01:59,840
同学们说明什么问题

50
00:01:59,840 --> 00:02:00,840
是不是我们的数组

51
00:02:00,840 --> 00:02:01,740
其实也是一种对象啊

52
00:02:01,740 --> 00:02:02,540
在js里面

53
00:02:02,540 --> 00:02:03,780
可能这样不明显

54
00:02:03,780 --> 00:02:07,860
我们来搞一个自创

55
00:02:07,860 --> 00:02:08,940
好了

56
00:02:08,940 --> 00:02:11,200
Word

57
00:02:11,200 --> 00:02:16,040
大家看到没有

58
00:02:16,040 --> 00:02:17,520
其实我们js里面的宿主

59
00:02:17,520 --> 00:02:18,240
它也是一个对象

60
00:02:18,240 --> 00:02:19,660
它有一个K为0的

61
00:02:19,660 --> 00:02:20,300
值为Hello

62
00:02:20,300 --> 00:02:21,100
K为1呢

63
00:02:21,100 --> 00:02:21,540
值为Word

64
00:02:21,540 --> 00:02:22,300
说明一个什么问题

65
00:02:22,300 --> 00:02:24,900
说明我们在js里面

66
00:02:24,900 --> 00:02:29,560
在js里面

67
00:02:29,560 --> 00:02:32,000
宿主其实也是对象

68
00:02:32,000 --> 00:02:33,020
那么对象它是

69
00:02:33,020 --> 00:02:33,680
怎么样

70
00:02:33,680 --> 00:02:34,360
是字典吗

71
00:02:34,360 --> 00:02:35,840
字典

72
00:02:35,840 --> 00:02:37,220
说白了

73
00:02:37,220 --> 00:02:38,240
在js里面

74
00:02:38,240 --> 00:02:40,680
它的宿主其实不是链表结构吧

75
00:02:40,680 --> 00:02:41,380
它是一个对象

76
00:02:41,380 --> 00:02:41,900
一个字顶

77
00:02:41,900 --> 00:02:43,380
那么呢

78
00:02:43,380 --> 00:02:44,540
在Redis里面

79
00:02:44,540 --> 00:02:46,640
是什么呢

80
00:02:46,640 --> 00:02:46,820
去

81
00:02:46,820 --> 00:02:48,740
是链表

82
00:02:48,740 --> 00:02:49,460
而且呢

83
00:02:49,460 --> 00:02:49,960
是双向

84
00:02:49,960 --> 00:02:51,560
怎么样理解双向链表

85
00:02:51,560 --> 00:02:52,760
这里呢

86
00:02:52,760 --> 00:02:53,980
我来画一幅图来给大家看一下

87
00:02:53,980 --> 00:02:56,480
比如说这是我们的

88
00:02:56,480 --> 00:02:57,220
在Redis里面

89
00:02:57,220 --> 00:02:57,840
第一个元素

90
00:02:57,840 --> 00:03:00,400
Redis里面第一个元素

91
00:03:00,400 --> 00:03:02,340
那么它和第二个元素

92
00:03:02,340 --> 00:03:03,160
怎么去连接呢

93
00:03:03,160 --> 00:03:04,800
这是第二个

94
00:03:04,800 --> 00:03:06,020
它们之间其实呢

95
00:03:06,020 --> 00:03:07,720
是有一个双向的箭头去连接的

96
00:03:07,720 --> 00:03:13,800
比如说我们一号二号

97
00:03:13,800 --> 00:03:17,960
三号这里呢其实是双向的这里图

98
00:03:17,960 --> 00:03:20,000
这里呢我就只画一个单向的箭头

99
00:03:20,000 --> 00:03:20,880
比如说四号

100
00:03:20,880 --> 00:03:24,700
那么如果说那么如果说我们有这样一个需求

101
00:03:24,700 --> 00:03:28,180
我们要去读取咱们这样一个列表

102
00:03:28,180 --> 00:03:29,100
咱们这样一个列表

103
00:03:29,100 --> 00:03:30,960
这在redis里面我们把它想象成为一个列表

104
00:03:30,960 --> 00:03:33,300
在12345他们之间会有个联系

105
00:03:33,300 --> 00:03:35,360
1235他们之间会有联系

106
00:03:35,360 --> 00:03:37,120
他们呢相当于

107
00:03:37,880 --> 00:03:40,200
打了一个一个的绳子上面打一个个结把他们给连起来

108
00:03:40,200 --> 00:03:43,000
你比如说我在第四个元素上面我想找

109
00:03:43,000 --> 00:03:45,320
第五个元素怎么去找

110
00:03:45,320 --> 00:03:47,620
实际上在他上面有一个属性

111
00:03:47,620 --> 00:03:48,900
叫做lext

112
00:03:48,900 --> 00:03:50,180
有个lext的属性

113
00:03:50,180 --> 00:03:51,960
他其实就是指的是5

114
00:03:51,960 --> 00:03:52,740
同样的

115
00:03:52,740 --> 00:03:53,500
在

116
00:03:53,500 --> 00:03:56,060
3这样一个元素上面他有个lext的属性

117
00:03:56,060 --> 00:03:56,840
指的是4

118
00:03:56,840 --> 00:03:58,880
所以说如果说我们在

119
00:03:58,880 --> 00:03:59,640
练表里面

120
00:03:59,640 --> 00:04:02,980
练表里面要要找到最后两个元素其实是很简单的

121
00:04:02,980 --> 00:04:04,260
你比如说你找到了

122
00:04:04,260 --> 00:04:07,080
4你是不是就能找到lext的属性是5

123
00:04:07,120 --> 00:04:09,520
他就直接给指引过来

124
00:04:09,520 --> 00:04:10,320
他的一个复杂度

125
00:04:10,320 --> 00:04:11,320
复杂度是O1

126
00:04:11,320 --> 00:04:12,640
那么在js里面

127
00:04:12,640 --> 00:04:14,400
他的宿主是怎么一回事呢

128
00:04:14,400 --> 00:04:16,340
在js里面

129
00:04:16,340 --> 00:04:21,360
在js里面

130
00:04:21,360 --> 00:04:23,540
比如说我们0

131
00:04:23,540 --> 00:04:24,780
他有一个属性为0

132
00:04:24,780 --> 00:04:25,440
值为1

133
00:04:25,440 --> 00:04:27,180
然后呢

134
00:04:27,180 --> 00:04:28,360
有一个属性

135
00:04:28,360 --> 00:04:29,400
为1

136
00:04:29,400 --> 00:04:30,160
值为2

137
00:04:30,160 --> 00:04:30,680
这里呢

138
00:04:30,680 --> 00:04:31,760
这是js里面的宿主

139
00:04:31,760 --> 00:04:32,860
那么0和1之间

140
00:04:32,860 --> 00:04:34,640
是不是其实没有什么关系啊

141
00:04:34,640 --> 00:04:35,040
同学们

142
00:04:35,040 --> 00:04:36,880
他们其实没什么关系吧

143
00:04:36,880 --> 00:04:39,960
你比如说我找到了0,想去找1,他们之间是没有一个

144
00:04:39,960 --> 00:04:43,800
没有一个连接的点,所以说在数族里面

145
00:04:43,800 --> 00:04:47,640
你去寻找索引是非常快的,那么字典的特点是不是就是寻找索引快啊

146
00:04:47,640 --> 00:04:48,660
但是像列表

147
00:04:48,660 --> 00:04:50,200
比如说咱们

148
00:04:50,200 --> 00:04:51,980
Reddit里面列表,他们

149
00:04:51,980 --> 00:04:55,820
每一个节点上面,不仅有next的属性,还有before,因为是双向的嘛

150
00:04:55,820 --> 00:04:58,900
比如说before等于3,讲说我们在4上面,你呢

151
00:04:58,900 --> 00:05:02,480
向前可以找到5,向后可以找到3,咱们逐渐一个一个挨个的去找

152
00:05:02,480 --> 00:05:06,060
你找最后的10个元素,或者前面的10个元素,是不是只需要去寻找10次

153
00:05:06,320 --> 00:05:09,520
但是在我们宿主里面是很难找的 你除非你要变你所有的可以然后给他排序

154
00:05:09,520 --> 00:05:12,560
这里呢就是js他和redis里面列表的一个区别

155
00:05:12,560 --> 00:05:15,580
好

156
00:05:15,580 --> 00:05:22,220
那么到这里呢 我们其实呢也可以去总结一下列表和宿主他的一个区别

157
00:05:22,220 --> 00:05:31,080
好 我们来看一下

158
00:05:31,080 --> 00:05:35,000
列表

159
00:05:36,320 --> 00:05:39,160
那么

160
00:05:39,160 --> 00:05:41,520
redis列表它有什么特点呢

161
00:05:41,520 --> 00:05:42,020
双向

162
00:05:42,020 --> 00:05:44,860
链表和js有什么区别

163
00:05:44,860 --> 00:05:51,500
那么js它的宿主是不是基于哈希

164
00:05:51,500 --> 00:05:52,580
同学们

165
00:05:52,580 --> 00:05:55,040
基于哈希

166
00:05:55,040 --> 00:05:57,320
那么redis里面的列表呢

167
00:05:57,320 --> 00:05:58,640
基于什么

168
00:05:58,640 --> 00:05:59,640
基于双向链表

169
00:05:59,640 --> 00:06:03,920
那么它有什么特点

170
00:06:06,320 --> 00:06:07,280
特点是什么

171
00:06:07,280 --> 00:06:08,720
咱们在redis里面双向链表

172
00:06:08,720 --> 00:06:10,080
它的列表的特点是什么

173
00:06:10,080 --> 00:06:10,960
头尾

174
00:06:10,960 --> 00:06:13,560
寻找很快

175
00:06:13,560 --> 00:06:15,080
所以慢

176
00:06:15,080 --> 00:06:15,880
为什么呀

177
00:06:15,880 --> 00:06:17,160
因为在redis里面

178
00:06:17,160 --> 00:06:18,120
它是不是每一个节点

179
00:06:18,120 --> 00:06:20,120
它都会有一个类似于像next

180
00:06:20,120 --> 00:06:22,560
或者before这样的属性去连接头尾

181
00:06:22,560 --> 00:06:23,760
在JS的数族其实是没有的

182
00:06:23,760 --> 00:06:25,160
因为它是基于它吸的

183
00:06:25,160 --> 00:06:26,680
那么这一块如果说有些疑问的同学

184
00:06:26,680 --> 00:06:27,560
可以去看一下

185
00:06:27,560 --> 00:06:28,960
老师可以推特选同学们看一下

186
00:06:28,960 --> 00:06:30,160
JS有本书叫做

187
00:06:30,160 --> 00:06:31,880
数据结构与算法非常好的一本书

188
00:06:31,880 --> 00:06:34,680
好 这里就是这一节课的内容

