1
00:00:00,260 --> 00:00:03,080
好 这节课我们就来看一下Redis中的字幕创类型

2
00:00:03,080 --> 00:00:05,880
那么字幕创类型呢 它是Redis里面最基础的一个类型

3
00:00:05,880 --> 00:00:09,220
这节课呢 我们会讲解Redis的三个内容 字幕创类型

4
00:00:09,220 --> 00:00:11,000
介绍

5
00:00:11,000 --> 00:00:11,780
命令

6
00:00:11,780 --> 00:00:14,340
和我们会举一个小的demo

7
00:00:14,340 --> 00:00:15,880
好 首先来看一下

8
00:00:15,880 --> 00:00:18,680
咱们字幕创类型 它的

9
00:00:18,680 --> 00:00:19,200
介绍

10
00:00:19,200 --> 00:00:21,500
字幕创类型是Redis中最基本的数据类型

11
00:00:21,500 --> 00:00:24,060
它能存储任何形式字幕创 包括二进制数据

12
00:00:24,060 --> 00:00:27,140
你可以用其存储用户的邮箱 阶层化的对象 甚至是一张图片

13
00:00:27,400 --> 00:00:30,680
那么一个自图创类型允许存储的数量最大容量是512兆

14
00:00:30,680 --> 00:00:31,620
什么意思

15
00:00:31,620 --> 00:00:36,620
其实自图创类型就和我们的js里面的自图创是一样的

16
00:00:36,620 --> 00:00:39,540
那么呢我们的js的自图创是不是也可以存储一张图片呢

17
00:00:39,540 --> 00:00:40,860
以什么形式都被死64

18
00:00:40,860 --> 00:00:43,200
包括了死均坏之后的对象

19
00:00:43,200 --> 00:00:44,900
那么Redis里面其实是一样的

20
00:00:44,900 --> 00:00:45,780
也可以去存储自图创

21
00:00:45,780 --> 00:00:49,100
那么一个自图创类型存储的数据的最大容量是512兆

22
00:00:49,100 --> 00:00:51,160
我们来看一下怎么样去操作它

23
00:00:51,160 --> 00:00:52,260
首先来看第一个命令

24
00:00:52,260 --> 00:00:53,500
取值与负值

25
00:00:53,500 --> 00:00:55,840
我们来试一下set

26
00:00:57,400 --> 00:00:59,400
set

27
00:00:59,400 --> 00:01:01,400
data

28
00:01:01,400 --> 00:01:02,400
好了

29
00:01:02,400 --> 00:01:03,400
ok

30
00:01:03,400 --> 00:01:05,400
那么我们set之后

31
00:01:05,400 --> 00:01:07,400
去看一下他到底有没有复制成功

32
00:01:07,400 --> 00:01:09,400
其实有ok已经代表成功了

33
00:01:09,400 --> 00:01:11,400
为了保险起来我们来get

34
00:01:11,400 --> 00:01:12,400
setget他是一对

35
00:01:12,400 --> 00:01:13,400
get data

36
00:01:13,400 --> 00:01:14,400
我们来看一下

37
00:01:14,400 --> 00:01:15,400
说明了我们已经复制成功了

38
00:01:15,400 --> 00:01:16,400
其实很简单

39
00:01:16,400 --> 00:01:17,400
他的命名

40
00:01:17,400 --> 00:01:18,400
存储呢

41
00:01:18,400 --> 00:01:19,400
hello他是一个自创

42
00:01:19,400 --> 00:01:20,400
那么我们来看一下

43
00:01:20,400 --> 00:01:21,400
递增数字

44
00:01:21,400 --> 00:01:23,400
incr之前是不是讲过

45
00:01:23,400 --> 00:01:25,400
比如说我们去incr

46
00:01:25,400 --> 00:01:28,040
那么这里呢会报错为什么呀

47
00:01:28,040 --> 00:01:29,900
因为我们的data它不是一个数字

48
00:01:29,900 --> 00:01:31,040
你不能够去寄生它

49
00:01:31,040 --> 00:01:32,980
那么假如说我们去incr

50
00:01:32,980 --> 00:01:34,160
data1

51
00:01:34,160 --> 00:01:34,880
大家看到没有

52
00:01:34,880 --> 00:01:36,800
我们的data1它就从0

53
00:01:36,800 --> 00:01:38,320
加了1防御值为1

54
00:01:38,320 --> 00:01:39,840
我们再来执行次

55
00:01:39,840 --> 00:01:40,780
它就变为了2

56
00:01:40,780 --> 00:01:42,480
这里呢是incr

57
00:01:42,480 --> 00:01:44,120
那么有的同学可能会想到

58
00:01:44,120 --> 00:01:45,800
我们可以借助getset两个命令

59
00:01:45,800 --> 00:01:47,320
去实现incr的函数

60
00:01:47,320 --> 00:01:48,480
伟大马如下

61
00:01:48,480 --> 00:01:49,540
这是一段js的代码

62
00:01:49,540 --> 00:01:51,280
其实很简单

63
00:01:51,280 --> 00:01:52,860
比如说我们去写了一个方形incr

64
00:01:52,860 --> 00:01:54,500
好

65
00:01:54,500 --> 00:01:56,300
这里呢 我直接通过

66
00:01:56,300 --> 00:01:58,080
编器带大家写一遍吧

67
00:01:58,080 --> 00:02:00,140
incr.gis

68
00:02:00,140 --> 00:02:06,020
好 假如说我们有一个方形incr

69
00:02:06,020 --> 00:02:08,580
我的 我们的目的是什么呢

70
00:02:08,580 --> 00:02:09,340
通过

71
00:02:09,340 --> 00:02:11,660
get和set

72
00:02:11,660 --> 00:02:14,220
去实现incr

73
00:02:14,220 --> 00:02:15,240
方法

74
00:02:15,240 --> 00:02:18,300
好 他能接受一个参数 叫做$k

75
00:02:18,300 --> 00:02:20,360
首先第一步

76
00:02:20,360 --> 00:02:21,640
第一步

77
00:02:21,640 --> 00:02:22,660
是不是咱们的

78
00:02:22,920 --> 00:02:26,000
多了value等于,我们是不是最开始要去获取

79
00:02:26,000 --> 00:02:29,060
获取value啊,从咱们的radis数据库里面去取值

80
00:02:29,060 --> 00:02:33,160
比如说我们去通过,假如我们现在有radis的方法,radis.

81
00:02:33,160 --> 00:02:35,980
radis.get

82
00:02:35,980 --> 00:02:37,760
多了K

83
00:02:37,760 --> 00:02:41,100
这里呢radis这样一个方法是咱们假设的

84
00:02:41,100 --> 00:02:43,140
其实在nodejs中

85
00:02:43,140 --> 00:02:45,440
是有可以去操作radis的库

86
00:02:45,440 --> 00:02:46,980
咱们先不用去关注

87
00:02:46,980 --> 00:02:48,780
假设radis

88
00:02:48,780 --> 00:02:50,820
是一个对象

89
00:02:51,080 --> 00:02:52,840
可以去操作数据

90
00:02:52,840 --> 00:02:53,080
好

91
00:02:53,080 --> 00:02:54,680
那么呢我们的value就已经得到了

92
00:02:54,680 --> 00:02:56,200
那么如果

93
00:02:56,200 --> 00:03:02,160
如果我们的value不存在

94
00:03:02,160 --> 00:03:04,460
如果我们的value它不存在

95
00:03:04,460 --> 00:03:05,300
说明什么问题

96
00:03:05,300 --> 00:03:06,880
我们是不是要把它给复制为0

97
00:03:06,880 --> 00:03:09,340
那么如果存在呢

98
00:03:09,340 --> 00:03:12,140
是不是咱们需要去

99
00:03:12,140 --> 00:03:13,720
多了value

100
00:03:13,720 --> 00:03:15,660
是不是要加加啊

101
00:03:15,660 --> 00:03:16,000
同学们

102
00:03:16,000 --> 00:03:17,480
然后呢把它给 return出去

103
00:03:17,480 --> 00:03:20,300
那么大家觉得

104
00:03:20,300 --> 00:03:22,700
如果说我们再去实现一个NCL函数可以吗

105
00:03:22,700 --> 00:03:24,120
行不行

106
00:03:24,120 --> 00:03:26,560
其实这样是不可以的

107
00:03:26,560 --> 00:03:27,400
为什么呀

108
00:03:27,400 --> 00:03:29,000
为什么呀

109
00:03:29,000 --> 00:03:30,480
因为我们的Redis

110
00:03:30,480 --> 00:03:32,240
如果说你只同时连接了一个扣端

111
00:03:32,240 --> 00:03:34,900
那么上面的代码去实现我们的NCL是完全没有问题的

112
00:03:34,900 --> 00:03:35,820
实现我们的递增函数

113
00:03:35,820 --> 00:03:38,740
但是你如果说同时有多个扣端连接到Redis

114
00:03:38,740 --> 00:03:40,420
那么可能会出现静态

115
00:03:40,420 --> 00:03:43,440
静态就是说明我们同时有多扣端去连接Redis

116
00:03:43,440 --> 00:03:44,200
会产生一个竞争

117
00:03:44,200 --> 00:03:46,000
那么假如说这个时候有扣端A和B

118
00:03:46,000 --> 00:03:48,800
都要去执行我们刚才这样一个NCL这样一个方法

119
00:03:49,320 --> 00:03:51,360
那么准备将同一个件的值进行递增

120
00:03:51,360 --> 00:03:52,120
但是呢他们

121
00:03:52,120 --> 00:03:53,400
都读到了这一行代码

122
00:03:53,400 --> 00:03:55,200
比如说他们当时读到的value都是5

123
00:03:55,200 --> 00:03:57,500
那么他们各自将该值递增到6

124
00:03:57,500 --> 00:03:59,300
并且使用set命令将其

125
00:03:59,300 --> 00:04:00,320
复给元件什么意思

126
00:04:00,320 --> 00:04:02,620
也就是说a和b他们都同时执行到了

127
00:04:02,620 --> 00:04:03,140
这里

128
00:04:03,140 --> 00:04:04,680
其实他们这里

129
00:04:04,680 --> 00:04:06,720
value

130
00:04:06,720 --> 00:04:07,740
是等于5的

131
00:04:07,740 --> 00:04:09,280
那么如果说他们两个线程

132
00:04:09,280 --> 00:04:10,560
两个后端去连接

133
00:04:10,560 --> 00:04:11,320
他们只

134
00:04:11,320 --> 00:04:13,120
都给return了一个6

135
00:04:13,120 --> 00:04:16,700
虽然说他们对咱们的

136
00:04:16,700 --> 00:04:18,500
value进行了两次递增操作

137
00:04:18,760 --> 00:04:19,780
A和B各自递真一次

138
00:04:19,780 --> 00:04:21,700
但是他们的结果只做了加1

139
00:04:21,700 --> 00:04:22,200
也就是6

140
00:04:22,200 --> 00:04:23,080
所以说呢

141
00:04:23,080 --> 00:04:24,540
所以说这里

142
00:04:24,540 --> 00:04:26,580
我们要的6不是我们预期的结果

143
00:04:26,580 --> 00:04:27,800
这里其实谈到

144
00:04:27,800 --> 00:04:28,880
提到的一个问题是什么呢

145
00:04:28,880 --> 00:04:30,420
其实是我们的一个操作的原子化

146
00:04:30,420 --> 00:04:31,200
原子化呢

147
00:04:31,200 --> 00:04:31,820
在Reddit里面

148
00:04:31,820 --> 00:04:33,100
它的NCR方法是实现的

149
00:04:33,100 --> 00:04:34,560
比如说我们A和B同时去连接

150
00:04:34,560 --> 00:04:36,140
你即使同时走到这一行

151
00:04:36,140 --> 00:04:36,860
它依然

152
00:04:36,860 --> 00:04:38,880
它依然返回的结果应该是7

153
00:04:38,880 --> 00:04:40,220
它解决了这样一个问题

154
00:04:40,220 --> 00:04:40,800
在Reddit里面

155
00:04:40,800 --> 00:04:42,400
所以说我们NCR不能够这样去封装

156
00:04:42,400 --> 00:04:43,340
而且这里呢

157
00:04:43,340 --> 00:04:44,500
跟咱们数据库的事物

158
00:04:44,500 --> 00:04:45,600
也稍微有一点类似

159
00:04:45,600 --> 00:04:46,840
比如说什么事事物

160
00:04:46,840 --> 00:04:48,140
这里呢我给大家举一个例子

161
00:04:48,140 --> 00:04:50,820
比如说我们生活中的扫码收款

162
00:04:50,820 --> 00:04:52,040
假如说我给你扫码

163
00:04:52,040 --> 00:04:53,260
那么我的钱扣了

164
00:04:53,260 --> 00:04:54,740
在扣钱的这个环节出了错

165
00:04:54,740 --> 00:04:56,240
比如说我的钱已经被扣了十块

166
00:04:56,240 --> 00:04:57,280
但是呢你没有收到

167
00:04:57,280 --> 00:04:59,000
那么这个时候我们这个错误能在

168
00:04:59,000 --> 00:05:00,960
能在扣款这里中断吗

169
00:05:00,960 --> 00:05:01,380
不能吧

170
00:05:01,380 --> 00:05:02,980
因为他还需要去把状态给回滚

171
00:05:02,980 --> 00:05:03,980
不然我的钱就飞了

172
00:05:03,980 --> 00:05:05,380
好这里呢就是

173
00:05:05,380 --> 00:05:07,360
INCR这里的一个介绍

174
00:05:07,360 --> 00:05:09,820
我们来通过咱们字幕串类型来看一个例子

175
00:05:09,820 --> 00:05:11,400
比如说博客

176
00:05:11,400 --> 00:05:14,220
博客它是我们生活中经常常见的例子吧

177
00:05:14,220 --> 00:05:16,140
我们来实现一个统计文章反问量

178
00:05:16,140 --> 00:05:19,920
而且呢我们来每一篇文章会使用一个post

179
00:05:19,920 --> 00:05:22,660
以post和文章id以及page

180
00:05:22,660 --> 00:05:24,860
点view的键来记录文章的访问量

181
00:05:24,860 --> 00:05:26,340
那么每次访问文章的时候呢

182
00:05:26,340 --> 00:05:28,580
使用incr命令时期相应的键子对增什么意思

183
00:05:28,580 --> 00:05:29,780
我们要去实现一个博客

184
00:05:29,780 --> 00:05:33,200
博客呢他有标题正文访问量吧

185
00:05:33,200 --> 00:05:35,220
而且呢我们还需要

186
00:05:35,220 --> 00:05:38,280
而且呢我们还需要通过

187
00:05:38,280 --> 00:05:40,060
我们还需要去通过

188
00:05:40,060 --> 00:05:43,180
以post文章id配给来储存文章的信息

189
00:05:43,180 --> 00:05:46,140
那么以它去存储咱们的一个访问量

190
00:05:46,140 --> 00:05:47,200
那么行

191
00:05:47,200 --> 00:05:48,080
那么我们接下来看一下

192
00:05:48,080 --> 00:05:51,060
怎么样去实现我们这样一个文章的需求

193
00:05:51,060 --> 00:05:52,880
这里呢

194
00:05:52,880 --> 00:05:54,000
我来建一个js

195
00:05:54,000 --> 00:05:57,260
叫做死君代码稍微

196
00:05:57,260 --> 00:05:59,580
删除一下

197
00:05:59,580 --> 00:06:00,120
我们来看一下

198
00:06:00,120 --> 00:06:01,660
假如说我们现在

199
00:06:01,660 --> 00:06:03,840
因为我们现在上的课是nodejs

200
00:06:03,840 --> 00:06:04,540
所以说呢

201
00:06:04,540 --> 00:06:06,320
我们需要去引入nodejs一些库

202
00:06:06,320 --> 00:06:06,920
所以说

203
00:06:06,920 --> 00:06:08,460
但是老师后面会去讲

204
00:06:08,460 --> 00:06:09,700
我们nodejs怎么样去操作redis

205
00:06:09,700 --> 00:06:10,040
这里呢

206
00:06:10,040 --> 00:06:10,280
只是

207
00:06:10,280 --> 00:06:11,180
这里

208
00:06:11,180 --> 00:06:12,400
只是我们去示意

209
00:06:12,400 --> 00:06:13,640
因为API是非常简单的

210
00:06:13,640 --> 00:06:15,200
比如说我们去引入一个

211
00:06:15,200 --> 00:06:16,320
引入一个Redis

212
00:06:16,320 --> 00:06:18,420
其实在load.js里面

213
00:06:18,420 --> 00:06:19,700
是存在这样一个cool的

214
00:06:19,700 --> 00:06:20,180
后面呢

215
00:06:20,180 --> 00:06:20,980
我们会详细的去讲

216
00:06:20,980 --> 00:06:22,520
通过load.js怎么去操作

217
00:06:22,520 --> 00:06:24,640
现在我们是让大家去熟悉

218
00:06:24,640 --> 00:06:26,340
熟悉Redis的一些操作

219
00:06:26,340 --> 00:06:28,460
怎么样去完成我们的业务

220
00:06:28,460 --> 00:06:30,160
比如说我们require一个Redis

221
00:06:30,160 --> 00:06:30,940
然后呢

222
00:06:30,940 --> 00:06:32,200
去给他留一个扣端出来

223
00:06:32,200 --> 00:06:33,740
中间的对象是一些配置

224
00:06:33,740 --> 00:06:34,080
这里呢

225
00:06:34,080 --> 00:06:34,940
只是咱们假想的代码

226
00:06:34,940 --> 00:06:35,640
这里先不用去管

227
00:06:35,640 --> 00:06:37,100
首先我们是不是要去

228
00:06:37,100 --> 00:06:38,160
比如说我们的文章

229
00:06:38,160 --> 00:06:40,280
文章是不是都会有一个ID

230
00:06:40,280 --> 00:06:41,360
而且呢

231
00:06:41,360 --> 00:06:42,260
这样一个ID是递争的

232
00:06:42,260 --> 00:06:47,160
所以说我们来挖一个post id等于

233
00:06:47,160 --> 00:06:48,380
什么呢

234
00:06:48,380 --> 00:06:49,520
我们随时操作扣端

235
00:06:49,520 --> 00:06:50,040
对吧

236
00:06:50,040 --> 00:06:51,780
咱们redis去留了一个redis的实例

237
00:06:51,780 --> 00:06:52,360
叫做client

238
00:06:52,360 --> 00:06:54,620
client.incr

239
00:06:54,620 --> 00:06:56,660
比如说我们要去incr什么

240
00:06:56,660 --> 00:06:59,920
我们以post和count去命令

241
00:06:59,920 --> 00:07:00,700
也就是咱们的id

242
00:07:00,700 --> 00:07:02,180
那么咱们命名之后

243
00:07:02,180 --> 00:07:04,120
是不是需要去存储我们的文章

244
00:07:04,120 --> 00:07:05,900
是不是需要去存储我们的文章

245
00:07:05,900 --> 00:07:07,340
对吧

246
00:07:07,340 --> 00:07:08,540
好

247
00:07:08,540 --> 00:07:11,980
那么存储我们的文章

248
00:07:11,980 --> 00:07:14,920
我们存储文章的时候应该以什么

249
00:07:14,920 --> 00:07:16,740
以什么去存储

250
00:07:16,740 --> 00:07:18,600
咱们刚才讲的是不是字幕串类型呢

251
00:07:18,600 --> 00:07:26,580
好 那我们的字幕串类型

252
00:07:26,580 --> 00:07:30,180
比如说我们怎么样去生成一个文章的字幕串类型呢

253
00:07:30,180 --> 00:07:31,740
我们文章有哪些属性呢

254
00:07:31,740 --> 00:07:38,660
是不是有标题 内容和什么

255
00:07:38,660 --> 00:07:39,940
是不是我们的阅读量

256
00:07:39,940 --> 00:07:45,380
那么我们怎么样去得到标题内容阅读量我们怎么样把它转成周创呢

257
00:07:45,380 --> 00:07:50,980
不知道大家还记不记得这个API 叫做 jason.sqin.fi

258
00:07:50,980 --> 00:07:53,820
对吧 我们可以把一个对象把它给解析成周创

259
00:07:53,820 --> 00:07:56,380
这样呢就解决了我们的问题 我们通过周创的形式去存储它

260
00:07:56,380 --> 00:08:03,020
所以呢 这里比如说我们去定义一个article 一篇文章等于 jason.sqin.fi

261
00:08:03,020 --> 00:08:07,460
title 我们是不是需要有

262
00:08:07,460 --> 00:08:10,280
需要有标题啊比如说hello

263
00:08:10,280 --> 00:08:11,900
内容呢

264
00:08:11,900 --> 00:08:13,100
内容随意吧

265
00:08:13,100 --> 00:08:14,940
我们只是教大家怎么样去写

266
00:08:14,940 --> 00:08:15,560
这样一段逻辑

267
00:08:15,560 --> 00:08:17,420
这样是不是得了一个字幕创

268
00:08:17,420 --> 00:08:18,600
那么我们怎么样去存储它

269
00:08:18,600 --> 00:08:19,640
是不是通过set呀

270
00:08:19,640 --> 00:08:22,580
比如说client.set

271
00:08:22,580 --> 00:08:23,580
set

272
00:08:23,580 --> 00:08:25,380
咱们set的时候是不是需要去

273
00:08:25,380 --> 00:08:27,180
set第一个参数是什么呀

274
00:08:27,180 --> 00:08:28,880
第一个参数是不是我们的类名呢

275
00:08:28,880 --> 00:08:29,840
比如说post

276
00:08:29,840 --> 00:08:31,500
我们是不是以post

277
00:08:31,500 --> 00:08:32,840
然后加上什么id

278
00:08:32,840 --> 00:08:33,660
id是什么

279
00:08:33,660 --> 00:08:35,280
多了post id

280
00:08:35,280 --> 00:08:37,840
然后data

281
00:08:37,840 --> 00:08:40,500
接下来就是我们的文章

282
00:08:40,500 --> 00:08:41,320
这一段受创

283
00:08:41,320 --> 00:08:43,080
多了

284
00:08:43,080 --> 00:08:43,720
artic

285
00:08:43,720 --> 00:08:45,980
到这里我们是不是就已经实现了文章

286
00:08:45,980 --> 00:08:49,060
文章数据的存储

287
00:08:49,060 --> 00:08:49,660
好

288
00:08:49,660 --> 00:08:50,640
接下来我们来看一下

289
00:08:50,640 --> 00:08:51,920
我们来看一下

290
00:08:51,920 --> 00:08:53,120
读取过程

291
00:08:53,120 --> 00:08:54,780
读取

292
00:08:54,780 --> 00:08:56,240
文章

293
00:08:56,240 --> 00:08:57,080
好

294
00:08:57,080 --> 00:08:58,840
怎么样去读取呢

295
00:08:58,840 --> 00:09:01,440
怎么样去读取

296
00:09:01,440 --> 00:09:03,320
比如说我们去挖了一个artic

297
00:09:03,320 --> 00:09:06,380
怎么读取

298
00:09:06,380 --> 00:09:07,580
是不是client店

299
00:09:07,580 --> 00:09:08,420
get

300
00:09:08,420 --> 00:09:08,880
get什么

301
00:09:08,880 --> 00:09:10,020
是不是get他呀

302
00:09:10,020 --> 00:09:11,720
get他呀

303
00:09:11,720 --> 00:09:13,280
那么假如说我们去读取id为

304
00:09:13,280 --> 00:09:14,480
id为1的文章

305
00:09:14,480 --> 00:09:16,280
此时我们是不是已经给读到了

306
00:09:16,280 --> 00:09:18,520
那么读到了之后

307
00:09:18,520 --> 00:09:19,940
读到这个article1是什么

308
00:09:19,940 --> 00:09:21,980
article1是什么

309
00:09:21,980 --> 00:09:23,800
article1是不是自不穿了

310
00:09:23,800 --> 00:09:25,620
那么我们如果说要读取他的标题

311
00:09:25,620 --> 00:09:26,380
或者内容

312
00:09:26,380 --> 00:09:27,160
怎么样去读取

313
00:09:27,160 --> 00:09:29,960
是不是首先我们需要用一个object去存储

314
00:09:29,960 --> 00:09:31,000
为什么呀

315
00:09:31,000 --> 00:09:32,580
因为他是被史军发以之过

316
00:09:32,580 --> 00:09:34,880
被我们的json序列画之后的内容

317
00:09:34,880 --> 00:09:35,940
我们需要去返讯的话吧

318
00:09:35,940 --> 00:09:38,340
json.pass

319
00:09:38,340 --> 00:09:42,560
咱们是不是把它返讯的话之后

320
00:09:42,560 --> 00:09:43,760
就变成那个json的对象

321
00:09:43,760 --> 00:09:44,820
那么我们需要去

322
00:09:44,820 --> 00:09:46,600
比如说你要去读取他的一个

323
00:09:46,600 --> 00:09:48,120
比如说你要去读取

324
00:09:48,120 --> 00:09:51,360
比如说你要去读取

325
00:09:51,360 --> 00:09:52,940
他咱们文章的标题

326
00:09:52,940 --> 00:09:54,800
是不是直接通过data.title

327
00:09:54,800 --> 00:09:56,660
去获取啊

328
00:09:56,660 --> 00:09:56,840
好

329
00:09:56,840 --> 00:09:57,720
那么这里呢

330
00:09:57,720 --> 00:09:58,900
就是我们文章

331
00:09:58,900 --> 00:09:59,880
他的一个存储

332
00:09:59,880 --> 00:10:00,260
咱们呢

333
00:10:00,260 --> 00:10:01,580
去存储文章的一个

334
00:10:01,580 --> 00:10:05,680
序列画之后的字幕创那么读取的时候呢咱们再通过接手点pass去反序列画

335
00:10:05,680 --> 00:10:07,980
核心是不是就是咱们的子军fy和pass

336
00:10:07,980 --> 00:10:10,800
那么呢我们每次访问文章的时候

337
00:10:10,800 --> 00:10:14,120
每次访问文章的时候是不是还需要做一个操作呀

338
00:10:14,120 --> 00:10:15,400
比如说我们post

339
00:10:15,400 --> 00:10:16,680
id

340
00:10:16,680 --> 00:10:18,740
是不是每次还需要去给他进行

341
00:10:18,740 --> 00:10:21,800
+1呀因为咱们的id是不是给是递增的

342
00:10:21,800 --> 00:10:23,080
好这里呢就是

343
00:10:23,080 --> 00:10:26,420
好这里呢就是咱们文章存取的一个逻辑

344
00:10:26,420 --> 00:10:29,220
好那咱们来总结一下这节课的内容

345
00:10:31,580 --> 00:10:34,480
我们刚才是不是讲到了

346
00:10:34,480 --> 00:10:35,420
制度创类型

347
00:10:35,420 --> 00:10:37,240
那么制度创类型js里面也有

348
00:10:37,240 --> 00:10:40,460
在Redis里面它最大能存取多大的容量

349
00:10:40,460 --> 00:10:44,140
多大是不是512兆啊

350
00:10:44,140 --> 00:10:45,880
那么刚才学了哪些命令

351
00:10:45,880 --> 00:10:47,700
是不是set get

352
00:10:47,700 --> 00:10:48,600
然后还有呢

353
00:10:48,600 --> 00:10:49,420
IOCR

354
00:10:49,420 --> 00:10:53,220
那么我们能不能够去通过get和set去实现一个INCR命令

355
00:10:53,220 --> 00:10:53,820
不可以吧

356
00:10:53,820 --> 00:10:54,320
为什么

357
00:10:54,320 --> 00:10:56,380
因为Redis它是原子化的

358
00:10:56,380 --> 00:10:57,200
但是呢

359
00:10:57,200 --> 00:10:59,520
我们通过咱们的get set去实现的时候

360
00:10:59,520 --> 00:11:00,800
是没有实现它的原子化

361
00:11:00,800 --> 00:11:02,940
还记得我们举的那一个付款的例子

362
00:11:02,940 --> 00:11:05,400
那么除了讲到我们的命令

363
00:11:05,400 --> 00:11:06,440
是不是还举了一个例子

364
00:11:06,440 --> 00:11:11,200
文章存取的例子

365
00:11:11,200 --> 00:11:13,120
那么文章存取它的核心是什么

366
00:11:13,120 --> 00:11:13,900
它的核心

367
00:11:13,900 --> 00:11:15,920
是不是存储

368
00:11:15,920 --> 00:11:17,740
Jason

369
00:11:17,740 --> 00:11:20,920
序列化之后的字不串

370
00:11:20,920 --> 00:11:24,700
那么读取呢

371
00:11:24,700 --> 00:11:25,360
读取什么

372
00:11:25,360 --> 00:11:26,240
是不是读取

373
00:11:26,240 --> 00:11:27,600
咱们读取

374
00:11:27,600 --> 00:11:30,140
读取是不是将

375
00:11:30,140 --> 00:11:31,380
制服创

376
00:11:31,380 --> 00:11:33,380
转为对象

377
00:11:33,380 --> 00:11:34,580
再去读起吧

378
00:11:34,580 --> 00:11:34,860
好

379
00:11:34,860 --> 00:11:37,680
这里呢就是咱们这节课的内容

