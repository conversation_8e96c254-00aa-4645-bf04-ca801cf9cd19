1
00:00:00,000 --> 00:00:03,840
前面我们主要完成了

2
00:00:03,840 --> 00:00:05,200
留言板的页面的查询功能

3
00:00:05,200 --> 00:00:06,620
但是还有一个细节我们要注意

4
00:00:06,620 --> 00:00:07,880
如果我们看这concert的话

5
00:00:07,880 --> 00:00:08,460
会有一个报复

6
00:00:08,460 --> 00:00:10,780
那是因为我们在没有获取数据之前

7
00:00:10,780 --> 00:00:12,980
那个weather天气的数据是空的

8
00:00:12,980 --> 00:00:14,620
所以说得不到天气的信息

9
00:00:14,620 --> 00:00:15,880
我们具体看这里

10
00:00:15,880 --> 00:00:17,200
在这个模板中

11
00:00:17,200 --> 00:00:19,120
你要通过weather这个属性来取值

12
00:00:19,120 --> 00:00:21,120
但是在没有调用接口之前

13
00:00:21,120 --> 00:00:22,800
也是没有这个对象的

14
00:00:22,800 --> 00:00:23,980
所以说解决办法就是

15
00:00:23,980 --> 00:00:25,480
在初始的数据当中

16
00:00:25,480 --> 00:00:26,780
我们就提供一个空的对象

17
00:00:26,780 --> 00:00:29,920
也包括link我们也给它一个空数组

18
00:00:29,920 --> 00:00:30,960
包括weather

19
00:00:30,960 --> 00:00:33,320
这都给它出示一个空的数据

20
00:00:33,320 --> 00:00:35,020
然后这样的话你再刷新

21
00:00:35,020 --> 00:00:36,520
就不会有问题了

22
00:00:36,520 --> 00:00:37,620
这是一个小的细节

23
00:00:37,620 --> 00:00:39,620
等你获取到接口数据之后

24
00:00:39,620 --> 00:00:40,420
把这个值覆盖

25
00:00:40,420 --> 00:00:41,320
那就有数据了

26
00:00:41,320 --> 00:00:42,520
这是一个细节

27
00:00:42,520 --> 00:00:43,320
好的到此为止

28
00:00:43,320 --> 00:00:44,620
我们关于留言板的案例

29
00:00:44,620 --> 00:00:45,520
还剩最后一个功能点

30
00:00:45,520 --> 00:00:47,120
就是添加留言的功能

31
00:00:47,120 --> 00:00:48,420
要完成这个功能

32
00:00:48,420 --> 00:00:49,620
我们要做如下的一些布置

33
00:00:49,620 --> 00:00:51,820
首先我们要做表单的数据绑定

34
00:00:51,820 --> 00:00:53,520
通过vgmodel做双向绑定

35
00:00:53,520 --> 00:00:56,620
然后就是给发送按钮绑定实践

36
00:00:56,620 --> 00:00:58,520
最后一步也是最复杂的一步

37
00:00:58,520 --> 00:01:00,880
就是实现变更的操作

38
00:01:00,880 --> 00:01:02,340
这里边我们会用到

39
00:01:02,340 --> 00:01:03,820
Apollo提供的一个API

40
00:01:03,820 --> 00:01:04,460
叫Mutate

41
00:01:04,460 --> 00:01:06,840
这里边会传递几个参数

42
00:01:06,840 --> 00:01:08,740
这个我们待会再详细的分析

43
00:01:08,740 --> 00:01:10,140
首先我们先把前两步

44
00:01:10,140 --> 00:01:11,140
这个工作来做一下

45
00:01:11,140 --> 00:01:13,220
我们要给它绑定数据

46
00:01:13,220 --> 00:01:15,100
首先第一个就是内容

47
00:01:15,100 --> 00:01:16,120
通过VeganModel

48
00:01:16,120 --> 00:01:17,540
这做一个绑定

49
00:01:17,540 --> 00:01:19,480
这是Content

50
00:01:19,480 --> 00:01:22,000
然后就是姓名

51
00:01:22,000 --> 00:01:23,280
也是通过VeganModel

52
00:01:23,280 --> 00:01:24,780
这做一个绑定

53
00:01:24,780 --> 00:01:26,100
有点令

54
00:01:26,100 --> 00:01:28,040
然后的话给发布

55
00:01:28,040 --> 00:01:28,960
这个要绑事件了

56
00:01:28,960 --> 00:01:30,560
但是这个数据绑定完之后

57
00:01:30,560 --> 00:01:31,860
我们还应该再添加两个属性

58
00:01:31,860 --> 00:01:33,000
然后再去绑事件

59
00:01:33,000 --> 00:01:34,700
在这个位置我们添加

60
00:01:34,700 --> 00:01:35,600
UserName

61
00:01:35,600 --> 00:01:38,140
然后是content

62
00:01:38,140 --> 00:01:41,040
好这是数据绑定

63
00:01:41,040 --> 00:01:42,660
然后的话我们再做这个事件的绑定

64
00:01:42,660 --> 00:01:44,440
在这个位置通过

65
00:01:44,440 --> 00:01:45,460
add符号click

66
00:01:45,460 --> 00:01:48,460
这我们提供一个事件函数

67
00:01:48,460 --> 00:01:49,660
比如说我们去叫send

68
00:01:49,660 --> 00:01:51,540
由于我们这使用了表单

69
00:01:51,540 --> 00:01:53,360
所以说为了防止它默认提交

70
00:01:53,360 --> 00:01:55,140
我们可以阻止它的默认行为

71
00:01:55,140 --> 00:01:56,680
通过事件修设符

72
00:01:56,680 --> 00:01:57,700
prevent

73
00:01:57,700 --> 00:01:58,960
阻止表单的提交

74
00:01:58,960 --> 00:02:00,560
好 这是关于时间的绑定

75
00:02:00,560 --> 00:02:01,720
然后的话我们需要提供一下

76
00:02:01,720 --> 00:02:02,480
这个时间函数

77
00:02:02,480 --> 00:02:03,580
所以说在最后

78
00:02:03,580 --> 00:02:05,060
我们再添加一个method

79
00:02:05,060 --> 00:02:08,160
这里提供一下send

80
00:02:08,160 --> 00:02:09,340
这是一个函数

81
00:02:09,340 --> 00:02:11,620
那在这里我们首先呢

82
00:02:11,620 --> 00:02:13,140
把对应的这两项数据呢

83
00:02:13,140 --> 00:02:13,780
先打印出来

84
00:02:13,780 --> 00:02:15,000
一个是eutername

85
00:02:15,000 --> 00:02:17,000
还有一个呢是entent

86
00:02:17,000 --> 00:02:19,080
好 我们看一下这个时间呢

87
00:02:19,080 --> 00:02:19,580
有没有绑成功

88
00:02:19,580 --> 00:02:20,980
我们看控制台

89
00:02:20,980 --> 00:02:21,960
这里边输入内容

90
00:02:21,960 --> 00:02:22,480
随便输入点

91
00:02:22,480 --> 00:02:23,160
然后点发布

92
00:02:23,160 --> 00:02:24,120
有值了

93
00:02:24,120 --> 00:02:24,960
那这就证明呢

94
00:02:24,960 --> 00:02:26,840
我们这个前两步已经完成了

95
00:02:26,840 --> 00:02:27,580
那这个关键点呢

96
00:02:27,580 --> 00:02:28,980
就是最后一步

97
00:02:28,980 --> 00:02:30,460
那如何去

98
00:02:30,460 --> 00:02:31,740
发动一个mutation的

99
00:02:31,740 --> 00:02:32,620
这么一个请求呢

100
00:02:32,620 --> 00:02:33,660
这里边就通过

101
00:02:33,660 --> 00:02:34,740
Z4.Dollar Apollo

102
00:02:34,740 --> 00:02:35,480
然后呢

103
00:02:35,480 --> 00:02:36,280
调用mutate这个方法

104
00:02:36,280 --> 00:02:37,040
来继续来做

105
00:02:37,040 --> 00:02:37,840
所以说呢

106
00:02:37,840 --> 00:02:38,880
我们在这先备注一下

107
00:02:38,880 --> 00:02:40,600
这要做的事情呢

108
00:02:40,600 --> 00:02:44,220
就是把这个表单的数据

109
00:02:44,220 --> 00:02:46,720
通过接口提交

110
00:02:46,720 --> 00:02:48,500
倒掉福气

111
00:02:48,500 --> 00:02:50,820
那具体提交的动作呢

112
00:02:50,820 --> 00:02:51,640
我们要通过谁呢

113
00:02:51,640 --> 00:02:54,860
通过Z4.Dollar Apollo

114
00:02:54,860 --> 00:02:56,500
里边有个方法叫mutate

115
00:02:56,500 --> 00:02:58,540
这里边是一个对象

116
00:02:58,540 --> 00:03:00,040
对象的话

117
00:03:00,040 --> 00:03:01,920
它是有多个属性

118
00:03:01,920 --> 00:03:02,520
我们看一下

119
00:03:02,520 --> 00:03:03,640
这里边关键型的属性

120
00:03:03,640 --> 00:03:04,340
我们先用前两个

121
00:03:04,340 --> 00:03:06,000
一个的话是Mutation

122
00:03:06,000 --> 00:03:08,940
后边的这个值是什么

123
00:03:08,940 --> 00:03:11,700
这个值和查询的工作是类似的

124
00:03:11,700 --> 00:03:12,260
GroundKR

125
00:03:12,260 --> 00:03:13,620
后边是标签模板

126
00:03:13,620 --> 00:03:16,640
这里边实现查询的逻辑

127
00:03:16,640 --> 00:03:17,760
这个查询的逻辑

128
00:03:17,760 --> 00:03:19,180
就是我们之前在构端开发当中

129
00:03:19,180 --> 00:03:21,060
所学的语法规则

130
00:03:21,060 --> 00:03:22,040
这个就是Mutation

131
00:03:22,040 --> 00:03:24,120
这是关键字

132
00:03:24,120 --> 00:03:25,420
然后提个名字

133
00:03:25,420 --> 00:03:26,060
我们就叫Create

134
00:03:26,060 --> 00:03:27,300
comment

135
00:03:27,300 --> 00:03:28,340
好

136
00:03:28,340 --> 00:03:28,920
那后边呢

137
00:03:28,920 --> 00:03:29,420
就传参的时候

138
00:03:29,420 --> 00:03:30,680
我们使用这个输入类型

139
00:03:30,680 --> 00:03:31,720
所以说我们要定义变量

140
00:03:31,720 --> 00:03:33,660
这是comment

141
00:03:33,660 --> 00:03:34,920
input

142
00:03:34,920 --> 00:03:35,600
好

143
00:03:35,600 --> 00:03:36,440
那这个输入类型的话呢

144
00:03:36,440 --> 00:03:37,540
我们就叫comment

145
00:03:37,540 --> 00:03:38,420
input

146
00:03:38,420 --> 00:03:39,880
好

147
00:03:39,880 --> 00:03:40,700
那然后的话

148
00:03:40,700 --> 00:03:41,720
这里边这个字单的名称

149
00:03:41,720 --> 00:03:42,960
是一样的

150
00:03:42,960 --> 00:03:43,320
这儿呢

151
00:03:43,320 --> 00:03:44,020
我们要传参了

152
00:03:44,020 --> 00:03:44,940
comment

153
00:03:44,940 --> 00:03:46,420
input

154
00:03:46,420 --> 00:03:47,300
然后这儿呢

155
00:03:47,300 --> 00:03:47,720
是多则符

156
00:03:47,720 --> 00:03:49,120
comment input

157
00:03:49,120 --> 00:03:49,800
好

158
00:03:49,800 --> 00:03:51,180
那这个添加成功之后

159
00:03:51,180 --> 00:03:52,080
我们要查询一下

160
00:03:52,080 --> 00:03:53,060
添加的这个用户名

161
00:03:53,060 --> 00:03:53,800
和的内容

162
00:03:53,800 --> 00:03:54,300
是什么

163
00:03:54,300 --> 00:03:55,400
可以验证一下

164
00:03:55,400 --> 00:03:56,280
有没有天下成功

165
00:03:56,280 --> 00:03:58,240
这是查询的逻辑

166
00:03:58,240 --> 00:04:00,140
但是有一点需要注意

167
00:04:00,140 --> 00:04:01,280
这个参数应该如何传递

168
00:04:01,280 --> 00:04:02,960
我们之前在浏览器中

169
00:04:02,960 --> 00:04:03,580
做测试的时候

170
00:04:03,580 --> 00:04:05,000
是这样传参的

171
00:04:05,000 --> 00:04:06,500
换上程序的方式

172
00:04:06,500 --> 00:04:06,940
我们怎么做

173
00:04:06,940 --> 00:04:07,880
看这里边

174
00:04:07,880 --> 00:04:08,960
有另外一个属性

175
00:04:08,960 --> 00:04:09,940
就是变量

176
00:04:09,940 --> 00:04:11,920
把这属性拿过来

177
00:04:11,920 --> 00:04:13,160
然后在mutation

178
00:04:13,160 --> 00:04:14,820
相同的位置

179
00:04:14,820 --> 00:04:15,660
再加一个属性

180
00:04:15,660 --> 00:04:17,820
它的值要注意

181
00:04:17,820 --> 00:04:19,720
还是输入类型

182
00:04:19,720 --> 00:04:20,620
comment input

183
00:04:20,620 --> 00:04:21,920
后面传递具体的值

184
00:04:21,920 --> 00:04:23,100
一个是有点内谱

185
00:04:23,100 --> 00:04:24,320
这个值从哪里得到

186
00:04:24,320 --> 00:04:25,660
这应该从表单中

187
00:04:25,660 --> 00:04:27,140
绑近的数据获取到

188
00:04:27,140 --> 00:04:28,420
还有一个就是content

189
00:04:28,420 --> 00:04:30,240
这是同样的处理方式

190
00:04:30,240 --> 00:04:31,480
这是content

191
00:04:31,480 --> 00:04:34,200
这样的话

192
00:04:34,200 --> 00:04:37,120
我们提交的动作就完成了

193
00:04:37,120 --> 00:04:39,340
最后其实还有一个属性

194
00:04:39,340 --> 00:04:40,400
它的作用我们待会再说

195
00:04:40,400 --> 00:04:41,320
我们先看前两个

196
00:04:41,320 --> 00:04:43,720
这个动作如果完成了

197
00:04:43,720 --> 00:04:44,620
我们这就可以做一个测试

198
00:04:44,620 --> 00:04:46,500
怎么测呢

199
00:04:46,500 --> 00:04:48,240
这里边我们就添加一条新的数据

200
00:04:48,240 --> 00:04:50,220
看一下Network这块

201
00:04:50,220 --> 00:04:52,560
这里边我们输入点内容

202
00:04:52,560 --> 00:04:52,940
Hello

203
00:04:52,940 --> 00:04:54,180
然后这儿是Tom

204
00:04:54,180 --> 00:04:55,420
然后点发布

205
00:04:55,420 --> 00:04:56,800
这儿发了一个请求

206
00:04:56,800 --> 00:04:57,860
然后我们看

207
00:04:57,860 --> 00:04:58,640
反回来的数据

208
00:04:58,640 --> 00:04:59,720
反回来的有

209
00:04:59,720 --> 00:05:00,540
Hello Tom

210
00:05:00,540 --> 00:05:02,460
这就证明我们添加的动作

211
00:05:02,460 --> 00:05:03,520
其实已经完成了

212
00:05:03,520 --> 00:05:04,720
但是页面没有刷新

213
00:05:04,720 --> 00:05:06,460
我们看一下服务端的数据

214
00:05:06,460 --> 00:05:07,460
有没有多数一条

215
00:05:07,460 --> 00:05:08,780
看谁呢

216
00:05:08,780 --> 00:05:09,340
看这个

217
00:05:09,340 --> 00:05:10,820
Data.json中的数据

218
00:05:10,820 --> 00:05:11,460
就是这个文件

219
00:05:11,460 --> 00:05:12,660
这儿我们格式化一下

220
00:05:12,660 --> 00:05:14,200
刚才我们加了一条

221
00:05:14,200 --> 00:05:14,700
就是Tom

222
00:05:14,700 --> 00:05:15,160
Hello

223
00:05:15,160 --> 00:05:16,520
这就证明我们的数据

224
00:05:16,520 --> 00:05:17,160
已经添加成功

225
00:05:17,160 --> 00:05:18,460
但是还有一个细节

226
00:05:18,460 --> 00:05:19,760
就是页面没有刷新

227
00:05:19,760 --> 00:05:20,940
其实我们希望

228
00:05:20,940 --> 00:05:21,740
它自动刷新的

229
00:05:21,740 --> 00:05:23,020
比如说我们这手动刷新

230
00:05:23,020 --> 00:05:25,140
其实那条数据是可以刷出来的

231
00:05:25,140 --> 00:05:26,780
那怎么让它自动刷新呢

232
00:05:26,780 --> 00:05:28,280
这里边就用到了我们最后一个属性

233
00:05:28,280 --> 00:05:29,920
就是revenge queries

234
00:05:29,920 --> 00:05:31,960
这个我们给它添加一下

235
00:05:31,960 --> 00:05:34,020
添加到这个位置

236
00:05:34,020 --> 00:05:35,240
它的值是一个数组

237
00:05:35,240 --> 00:05:36,080
这里边放对象

238
00:05:36,080 --> 00:05:37,800
然后这有一个属性就是carry

239
00:05:37,800 --> 00:05:39,740
后边是告诉它

240
00:05:39,740 --> 00:05:41,080
你要做什么样的查询

241
00:05:41,080 --> 00:05:42,980
实际上这的代码和上面这个

242
00:05:42,980 --> 00:05:44,400
是一样的

243
00:05:44,400 --> 00:05:45,600
就是把这份代码拷一份

244
00:05:45,600 --> 00:05:46,580
拿过来就可以了

245
00:05:46,580 --> 00:05:47,340
但是这样的话

246
00:05:47,340 --> 00:05:48,560
代码的冗余度就比较高

247
00:05:48,560 --> 00:05:49,860
那应该怎么做呢

248
00:05:49,860 --> 00:05:51,620
我们可以把这个查询的逻辑

249
00:05:51,620 --> 00:05:52,580
封装到一个变量里

250
00:05:52,580 --> 00:05:53,820
抽取到一个变量里

251
00:05:53,820 --> 00:05:54,600
我们可以这样来做

252
00:05:54,600 --> 00:05:56,040
在这我们申请一个

253
00:05:56,040 --> 00:05:57,680
就叫query list

254
00:05:57,680 --> 00:05:58,840
然后加一个tag

255
00:05:58,840 --> 00:06:00,060
因为这是标签模板

256
00:06:00,060 --> 00:06:02,560
然后把这部分给它剪掉

257
00:06:02,560 --> 00:06:04,240
然后放到这

258
00:06:04,240 --> 00:06:08,200
好 这是抽取出来了

259
00:06:08,200 --> 00:06:10,640
然后把这个名字放到这个位置

260
00:06:10,640 --> 00:06:11,720
效果还是一样的

261
00:06:11,720 --> 00:06:12,820
但是我们可以把这个名字

262
00:06:12,820 --> 00:06:14,060
同时再放到这个位置

263
00:06:14,060 --> 00:06:15,560
实际上还是做同样的查询

264
00:06:15,560 --> 00:06:17,460
就是添加成功之后再去查一次

265
00:06:17,460 --> 00:06:18,980
就是这个意思

266
00:06:18,980 --> 00:06:21,760
然后的话我们再去添加一条数据

267
00:06:21,760 --> 00:06:23,360
这里边再写一条

268
00:06:23,360 --> 00:06:24,620
你好

269
00:06:24,620 --> 00:06:25,780
然后是Jerry

270
00:06:25,780 --> 00:06:28,280
然后我们再点发布

271
00:06:28,280 --> 00:06:30,460
我们会发现这条数据自动刷出来了

272
00:06:30,460 --> 00:06:33,700
这就证明我们最后的属性就发挥了作用

273
00:06:33,700 --> 00:06:38,100
这是关于添加留言的功能点

274
00:06:38,100 --> 00:06:39,560
到此为止的话

275
00:06:39,560 --> 00:06:42,120
关于整个的留言版的案例

276
00:06:42,120 --> 00:06:43,780
所有的业务功能我们就完成了

277
00:06:43,780 --> 00:06:45,900
最后我们再来看一下这里的布置

278
00:06:45,900 --> 00:06:48,020
关键点就是最后的一步

279
00:06:48,020 --> 00:06:49,780
要调用Polo提供的API

280
00:06:49,780 --> 00:06:51,860
来进行变更操作

281
00:06:51,860 --> 00:06:53,020
这个变更的时候

282
00:06:53,020 --> 00:06:53,960
我们用到的逻辑

283
00:06:53,960 --> 00:06:55,180
还是我们之前所学的语法

284
00:06:55,180 --> 00:06:56,140
传参的时候

285
00:06:56,140 --> 00:06:57,280
这个有点特殊

286
00:06:57,280 --> 00:06:57,780
要注意

287
00:06:57,780 --> 00:06:58,960
要通过这个属性的方式

288
00:06:58,960 --> 00:06:59,960
来传递实际的数据

289
00:06:59,960 --> 00:07:01,380
最后还有强调的

290
00:07:01,380 --> 00:07:02,600
就是这个地方

291
00:07:02,600 --> 00:07:03,900
要想刷新的话

292
00:07:03,900 --> 00:07:04,960
我们要显示的指定一下

293
00:07:04,960 --> 00:07:05,260
这个属性

294
00:07:05,260 --> 00:07:06,820
通过query去绑定

295
00:07:06,820 --> 00:07:07,460
一个查询的动作

296
00:07:07,460 --> 00:07:08,600
就是添加成功之后

297
00:07:08,600 --> 00:07:10,240
来触发一个新的查询

298
00:07:10,240 --> 00:07:11,160
再去查询一次

299
00:07:11,160 --> 00:07:12,960
好 这是关于

300
00:07:12,960 --> 00:07:13,960
这个变更操作

301
00:07:13,960 --> 00:07:14,740
我们就说到这里

302
00:07:14,740 --> 00:07:18,080
感谢观看

