1
00:00:00,000 --> 00:00:05,680
好 我们来进入第1章Redis的一个学习 那我在讲解Redis呢 老师呢会分为几个部分去讲

2
00:00:05,680 --> 00:00:06,540
 首先呢 第一个部分

3
00:00:06,540 --> 00:00:10,400
他的一些特点 第二个呢 会讲解一些他的API 第三个呢 结合

4
00:00:10,400 --> 00:00:11,840
no.js

5
00:00:11,840 --> 00:00:14,700
应用

6
00:00:14,700 --> 00:00:18,660
好 首先呢 我们这节课呢 会去讲解两部分内容

7
00:00:18,660 --> 00:00:24,600
第一个 Redis 他的一个诞生 也就是历史和发展 第二个呢 这里呢 会去向同学们去介绍一下Redis

8
00:00:24,600 --> 00:00:25,620
 他的一些特点

9
00:00:25,620 --> 00:00:26,700
好 我们先来看一下

10
00:00:28,620 --> 00:00:30,980
Redis他的一个历史和发展是什么样的

11
00:00:30,980 --> 00:00:32,020
也就是他怎么样去诞生的

12
00:00:32,020 --> 00:00:33,840
首先在2008年

13
00:00:33,840 --> 00:00:35,000
意大利的一家创业公司

14
00:00:35,000 --> 00:00:36,040
叫做MESA

15
00:00:36,040 --> 00:00:38,280
推出了一款基于MySocle的网站实时统计系统

16
00:00:38,280 --> 00:00:39,580
LOGG

17
00:00:39,580 --> 00:00:40,740
这个名字很有意思

18
00:00:40,740 --> 00:00:41,920
但是没过多久

19
00:00:41,920 --> 00:00:43,300
他们公司的创始人也就是老板

20
00:00:43,300 --> 00:00:44,280
觉得MySocle的性能不好

21
00:00:44,280 --> 00:00:47,320
于是决定亲自为他的网站去做一个数据库

22
00:00:47,320 --> 00:00:49,060
并于2009年开发完成

23
00:00:49,060 --> 00:00:49,920
这个数据库就是Redis

24
00:00:49,920 --> 00:00:50,380
说明个什么

25
00:00:50,380 --> 00:00:51,200
什么问题

26
00:00:51,200 --> 00:00:52,840
Redis2009年诞生

27
00:00:52,840 --> 00:00:54,860
就是为了解决MySocle它的性能不好的问题

28
00:00:54,860 --> 00:00:57,800
但是他希望更多的人使用它

29
00:00:57,800 --> 00:00:59,700
所以在同年将RADIS开源

30
00:00:59,700 --> 00:01:02,820
并且开始和RADIS的另一名代码贡献者Peter

31
00:01:02,820 --> 00:01:04,620
去共同的去维护和开发指导今天

32
00:01:04,620 --> 00:01:05,260
说了一个什么问题

33
00:01:05,260 --> 00:01:06,680
那么RADIS它的作者有两个人

34
00:01:06,680 --> 00:01:07,140
一个是Peter

35
00:01:07,140 --> 00:01:09,580
一个是Snowit

36
00:01:09,580 --> 00:01:13,440
好 我们来看一下RADIS它当初的网站长什么样

37
00:01:13,440 --> 00:01:19,360
好 大家稍等一下

38
00:01:19,360 --> 00:01:23,140
其实很遗憾的 同学们

39
00:01:23,140 --> 00:01:25,680
RADIS它当初诞生的网站已经倒闭了

40
00:01:25,680 --> 00:01:27,360
现在已经开始进行域名的一个贩卖

41
00:01:27,360 --> 00:01:28,620
很遗憾

42
00:01:28,620 --> 00:01:30,180
但是Redis一直留存到了

43
00:01:30,180 --> 00:01:31,540
今天

44
00:01:31,540 --> 00:01:33,300
那么我们来看一下

45
00:01:33,300 --> 00:01:35,900
Redis它到底有什么魅力

46
00:01:35,900 --> 00:01:37,200
它的网站都倒闭了

47
00:01:37,200 --> 00:01:38,460
但是Redis它能发展到今天

48
00:01:38,460 --> 00:01:39,820
并且吸引了如此多的用户

49
00:01:39,820 --> 00:01:41,720
那么刚才我们是不是看到了

50
00:01:41,720 --> 00:01:43,320
Redis它有多少颗Star在Github上面

51
00:01:43,320 --> 00:01:44,920
是不是有36,900颗

52
00:01:44,920 --> 00:01:47,260
其实它的影响力有多大

53
00:01:47,260 --> 00:01:48,940
我可以给大家举一个例子

54
00:01:48,940 --> 00:01:50,300
比如说我们现在一个主流框架

55
00:01:50,300 --> 00:01:50,780
Angela

56
00:01:50,780 --> 00:01:52,920
它的一个Star目前是4万多颗星

57
00:01:52,920 --> 00:01:54,280
可以说说明什么问题

58
00:01:54,280 --> 00:01:55,220
咱们的Redis和Angela

59
00:01:55,220 --> 00:01:58,240
它的一个关注度是差不多的

60
00:01:58,240 --> 00:01:58,840
那么我们来看一下

61
00:01:58,840 --> 00:01:59,800
Redis它有哪些特点

62
00:01:59,800 --> 00:02:02,140
首先它的存储结构很特别

63
00:02:02,140 --> 00:02:03,680
它是字典类型的

64
00:02:03,680 --> 00:02:06,500
也就是list和咱们的js里面的对象是类似

65
00:02:06,500 --> 00:02:08,760
第二个内存存储与词句话

66
00:02:08,760 --> 00:02:09,460
什么意思

67
00:02:09,460 --> 00:02:11,060
说白了Redis可以用来做缓存

68
00:02:11,060 --> 00:02:12,200
第三个功能丰富

69
00:02:12,200 --> 00:02:14,240
说明它的API很丰富

70
00:02:14,240 --> 00:02:15,440
第四个简单稳定

71
00:02:15,440 --> 00:02:17,860
也就是咱们代码里面的一个原则

72
00:02:17,860 --> 00:02:19,140
简单可以了

73
00:02:19,140 --> 00:02:20,920
好我们来看一下它的第一个特点

74
00:02:20,920 --> 00:02:21,500
存储结构

75
00:02:21,500 --> 00:02:24,380
首先Redis它缩写是

76
00:02:24,380 --> 00:02:26,040
Remote

77
00:02:26,040 --> 00:02:27,740
Dictionary Server

78
00:02:27,740 --> 00:02:29,660
中文名远程字典服务器

79
00:02:29,660 --> 00:02:31,160
它以字典结构存储数据

80
00:02:31,160 --> 00:02:32,840
字典呢就是js中的对象

81
00:02:32,840 --> 00:02:34,640
那么同大多数语言中的字典一样

82
00:02:34,640 --> 00:02:36,440
Redis的字典的见词除了可以是字母创

83
00:02:36,440 --> 00:02:38,300
也可以是其他的数据类型

84
00:02:38,300 --> 00:02:40,000
它能支持字母创

85
00:02:40,000 --> 00:02:41,920
闪链 列表 集合和有序集合

86
00:02:41,920 --> 00:02:42,640
这东西是什么

87
00:02:42,640 --> 00:02:44,100
咱们后面的课程会去介绍

88
00:02:44,100 --> 00:02:45,980
这里让老师问大家一个问题

89
00:02:45,980 --> 00:02:49,740
GS它的见词除了可以是字母创

90
00:02:49,740 --> 00:02:51,960
还可以是其他的一些数据类型吗

91
00:02:51,960 --> 00:02:52,740
同学们可不可以

92
00:02:52,740 --> 00:02:54,380
其实我们来看一下

93
00:02:54,380 --> 00:02:55,080
就知道了

94
00:02:55,080 --> 00:02:59,280
好

95
00:02:59,280 --> 00:03:00,640
我们用开发者工具

96
00:03:00,640 --> 00:03:01,800
Google的开发工具

97
00:03:01,800 --> 00:03:02,200
来看一下

98
00:03:02,200 --> 00:03:02,820
做个试验

99
00:03:02,820 --> 00:03:05,360
首先我们来定一个对象

100
00:03:05,360 --> 00:03:06,640
data等于一个通对象

101
00:03:06,640 --> 00:03:08,180
给它一个见值

102
00:03:08,180 --> 00:03:09,860
比如说我们再来定一个对象

103
00:03:09,860 --> 00:03:12,160
k等于通对象

104
00:03:12,160 --> 00:03:12,940
好

105
00:03:12,940 --> 00:03:13,860
那我们来看一下

106
00:03:13,860 --> 00:03:15,400
首先data

107
00:03:15,400 --> 00:03:16,480
我们给它一个字不串属性

108
00:03:16,480 --> 00:03:17,780
等于

109
00:03:17,780 --> 00:03:19,440
我们再来给它一个

110
00:03:19,440 --> 00:03:21,260
以k为属性

111
00:03:21,260 --> 00:03:21,780
也就是呢

112
00:03:21,780 --> 00:03:22,660
以一个对象

113
00:03:22,660 --> 00:03:24,660
空对向为k 看它是什么

114
00:03:24,660 --> 00:03:26,760
咱们把data打印出来看一下

115
00:03:26,760 --> 00:03:31,060
第一个属性a是1 第二个object object是1

116
00:03:31,060 --> 00:03:33,060
那么这样一个object object

117
00:03:33,060 --> 00:03:34,960
它到底是什么类型呢

118
00:03:34,960 --> 00:03:36,460
好像现在看不出来

119
00:03:36,460 --> 00:03:37,560
它可能是对象 可能是字不创

120
00:03:37,560 --> 00:03:40,060
那么其实这里我能教大家一个方法

121
00:03:40,060 --> 00:03:41,160
我们通过object

122
00:03:41,160 --> 00:03:44,060
case去便利一下data上面的k

123
00:03:44,060 --> 00:03:44,760
看它到底是什么

124
00:03:44,760 --> 00:03:46,060
大家可以看到

125
00:03:46,060 --> 00:03:48,060
这样一个数组它有两个值

126
00:03:48,060 --> 00:03:49,060
一个是a 一个是字不创

127
00:03:49,060 --> 00:03:49,860
object object

128
00:03:49,860 --> 00:03:51,260
是不是把咱们的一个空对向

129
00:03:51,260 --> 00:03:53,140
抽死菌之后就变成了object object

130
00:03:53,140 --> 00:03:53,680
好

131
00:03:53,680 --> 00:03:55,180
那么可能有些同学不信

132
00:03:55,180 --> 00:03:56,960
难道js的对象

133
00:03:56,960 --> 00:03:59,180
他只支持以自估为界铭吗

134
00:03:59,180 --> 00:04:00,720
那么我们再来试一下

135
00:04:00,720 --> 00:04:03,760
比如说哇一个k1等于一个方形

136
00:04:03,760 --> 00:04:04,800
咱们来看一下

137
00:04:04,800 --> 00:04:06,160
能不能以方形为界值

138
00:04:06,160 --> 00:04:11,220
我们来给datak1等于1

139
00:04:11,220 --> 00:04:11,620
好

140
00:04:11,620 --> 00:04:14,800
这里我们再来打印一下data的属性是什么

141
00:04:14,800 --> 00:04:15,720
大家可以看到

142
00:04:15,720 --> 00:04:18,460
我们的方形是不是也已经被转成了一个自估

143
00:04:18,460 --> 00:04:19,200
说明什么问题

144
00:04:19,200 --> 00:04:20,440
在咱们对象创建的时候

145
00:04:20,440 --> 00:04:23,000
我会自动的去把K去做一个QSGIN的操作

146
00:04:23,000 --> 00:04:23,940
说明一个什么问题

147
00:04:23,940 --> 00:04:25,640
说明一个什么问题

148
00:04:25,640 --> 00:04:30,060
说明在js中

149
00:04:30,060 --> 00:04:32,760
K只能是字符创

150
00:04:32,760 --> 00:04:35,380
但是呢我们的Redis它可以是其他的类型

151
00:04:35,380 --> 00:04:36,920
这里呢也是Redis的一个特点

152
00:04:36,920 --> 00:04:38,700
好这里呢我们来看一下

153
00:04:38,700 --> 00:04:40,480
Redis它这一个特点在实际

154
00:04:40,480 --> 00:04:42,780
咱们项目中的一个小的应用是什么

155
00:04:42,780 --> 00:04:43,620
比如说啊

156
00:04:43,620 --> 00:04:45,160
我们现在有这样一个需求

157
00:04:45,160 --> 00:04:47,580
我们在Post的变量中去存储了一篇文章的数据

158
00:04:47,580 --> 00:04:49,760
比如说有标题有正文有阅读链

159
00:04:50,440 --> 00:04:51,340
这里呢

160
00:04:51,340 --> 00:04:53,400
我写的一些围代码

161
00:04:53,400 --> 00:04:55,620
比如说post点title.hello.contentviews.tags

162
00:04:55,620 --> 00:04:56,600
分别给了它一些属性

163
00:04:56,600 --> 00:04:57,820
然后呢tags

164
00:04:57,820 --> 00:04:59,280
什么是tags啊

165
00:04:59,280 --> 00:05:00,800
tags是不是就是我们在写文章的时候

166
00:05:00,800 --> 00:05:01,400
会给它一些标签

167
00:05:01,400 --> 00:05:03,100
比如说你这篇文章是属于前端领域

168
00:05:03,100 --> 00:05:03,660
还是php

169
00:05:03,660 --> 00:05:04,280
还是夹吧

170
00:05:04,280 --> 00:05:04,940
还是nodejs

171
00:05:04,940 --> 00:05:05,860
这里呢就是tags

172
00:05:05,860 --> 00:05:07,580
那么tags呢可以是多种类型的

173
00:05:07,580 --> 00:05:09,060
views呢就是我们的阅读量

174
00:05:09,060 --> 00:05:10,820
假设我们现在有这样一个需求

175
00:05:10,820 --> 00:05:12,760
需要通过tags去检索文章

176
00:05:12,760 --> 00:05:14,000
那么我们来看一下

177
00:05:14,000 --> 00:05:16,140
如果说我们通过关系型数据库

178
00:05:16,140 --> 00:05:17,620
怎么去设计它

179
00:05:17,620 --> 00:05:18,700
比如说mysurkle

180
00:05:20,440 --> 00:05:22,440
结构怎么样去鉴表 我们来看一下

181
00:05:22,440 --> 00:05:24,440
这里呢 我们通过一个图

182
00:05:24,440 --> 00:05:26,440
比如说 我们在post辨论中

183
00:05:26,440 --> 00:05:28,440
去存储一篇文章的数据 标题正文阅读量

184
00:05:28,440 --> 00:05:30,440
也就是说 我们需要去

185
00:05:30,440 --> 00:05:32,440
存储一篇文章的数据

186
00:05:32,440 --> 00:05:34,440
首先咱们在鉴表的时候

187
00:05:34,440 --> 00:05:36,440
是不是第一步要做了 给一个id 然后标题title

188
00:05:36,440 --> 00:05:38,440
接下来正文content

189
00:05:38,440 --> 00:05:40,440
然后呢 阅读量

190
00:05:40,440 --> 00:05:42,440
views

191
00:05:42,440 --> 00:05:44,440
我来把字体调大点

192
00:05:44,440 --> 00:05:48,440
这里就是会给他一些id

193
00:05:48,440 --> 00:05:50,300
好,title呢

194
00:05:50,300 --> 00:05:53,200
咱们随便给叉叉叉,好,这里呢我就不写了

195
00:05:53,200 --> 00:05:55,200
这里我就不写了

196
00:05:55,200 --> 00:05:57,080
好,那我们再来看一下

197
00:05:57,080 --> 00:05:59,880
我们这样去建一张表,假如说我们现在有个需求

198
00:05:59,880 --> 00:06:02,340
需要通过tags去查询文章

199
00:06:02,340 --> 00:06:05,060
也就是我们刚才说提到了一个需求

200
00:06:05,060 --> 00:06:07,680
假如需要通过tags去检索文章

201
00:06:07,680 --> 00:06:09,280
怎么样去做

202
00:06:09,280 --> 00:06:15,140
咱们假如说需要通过tags去检索文章

203
00:06:15,140 --> 00:06:18,600
那么我们是不是在关系型数据库里面

204
00:06:18,600 --> 00:06:20,500
我们还需要去另外去外念一张表

205
00:06:20,500 --> 00:06:21,360
是什么呢

206
00:06:21,360 --> 00:06:23,540
id和tag

207
00:06:23,540 --> 00:06:25,540
id

208
00:06:25,540 --> 00:06:27,580
他们两个需要关联起来

209
00:06:27,580 --> 00:06:31,600
比如说id刚才咱们是不是012

210
00:06:31,600 --> 00:06:32,200
那么id呢

211
00:06:32,200 --> 00:06:33,460
是012

212
00:06:33,460 --> 00:06:34,780
好

213
00:06:34,780 --> 00:06:36,140
那么这里tagid是什么呢

214
00:06:36,140 --> 00:06:37,580
其实我们这里还需要另外一张表

215
00:06:37,580 --> 00:06:40,040
去维护我们的tagid

216
00:06:40,040 --> 00:06:40,700
比如说啊

217
00:06:40,700 --> 00:06:41,640
比如说我们的tagid

218
00:06:41,640 --> 00:06:45,700
这里呢有什么呢

219
00:06:45,700 --> 00:06:46,700
比如说有tagad

220
00:06:46,700 --> 00:06:48,420
这里呢还会有个type

221
00:06:48,420 --> 00:06:50,220
id0代表什么

222
00:06:50,220 --> 00:06:50,920
nodejs

223
00:06:50,920 --> 00:06:52,840
id1呢代表加

224
00:06:52,840 --> 00:06:55,700
id2呢代表php

225
00:06:55,700 --> 00:06:58,000
好这里调代

226
00:06:58,000 --> 00:07:02,540
tagad呢也就对应的

227
00:07:02,540 --> 00:07:03,820
什么什么什么什么

228
00:07:03,820 --> 00:07:04,280
这里呢

229
00:07:04,280 --> 00:07:07,200
其实如果说有过后端经验的同学

230
00:07:07,200 --> 00:07:08,640
可能会有所领悟

231
00:07:08,640 --> 00:07:11,020
就比如说

232
00:07:11,020 --> 00:07:12,560
我们需要实现这样一个简单的需求

233
00:07:12,560 --> 00:07:15,380
我们仅仅是需要存储一篇文章

234
00:07:15,380 --> 00:07:15,980
一些文章

235
00:07:15,980 --> 00:07:16,480
而且呢

236
00:07:16,480 --> 00:07:17,460
需求呢

237
00:07:17,460 --> 00:07:20,700
通过Tax去检索文章

238
00:07:20,700 --> 00:07:22,620
我们就需要去建三张表

239
00:07:22,620 --> 00:07:23,960
也就是老师刚才所建的一些表

240
00:07:23,960 --> 00:07:26,260
而且他的查询非常的复杂

241
00:07:26,260 --> 00:07:28,300
这呢就是买搜口去完成这样一个需求

242
00:07:28,300 --> 00:07:30,060
他所需要去做的事情

243
00:07:30,060 --> 00:07:33,060
那么我们来看一下Redis

244
00:07:33,060 --> 00:07:34,440
是怎么样解决的

245
00:07:34,440 --> 00:07:37,080
其实我们用Redis可以对Tax进行

246
00:07:37,080 --> 00:07:38,840
并且交集这样的集合运算操作

247
00:07:38,840 --> 00:07:40,100
可以很轻易地实现

248
00:07:40,100 --> 00:07:41,600
对Tax的各种查询要求

249
00:07:41,600 --> 00:07:42,700
怎么意思

250
00:07:42,700 --> 00:07:43,520
也就是说

251
00:07:43,520 --> 00:07:44,540
如果我们用Redis

252
00:07:44,540 --> 00:07:45,720
去实现同样的功能

253
00:07:45,720 --> 00:07:47,980
它会比关系数据库简单很多

254
00:07:47,980 --> 00:07:50,160
因为它有这一些集合运算操作

255
00:07:50,160 --> 00:07:51,180
什么是集合运算操作

256
00:07:51,180 --> 00:07:52,880
老师在后面的课程里面会讲

257
00:07:52,880 --> 00:07:53,800
大家只需要记住

258
00:07:53,800 --> 00:07:55,120
Redis去做这样的需求

259
00:07:55,120 --> 00:07:55,900
它很简单

260
00:07:55,900 --> 00:07:56,060
好

261
00:07:56,060 --> 00:07:56,740
我们来看一下

262
00:07:56,740 --> 00:07:58,740
它的另外第二个特点

263
00:07:58,740 --> 00:08:00,360
内存存储与持久化

264
00:08:00,360 --> 00:08:02,580
首先Redis

265
00:08:02,580 --> 00:08:03,660
我们之前是不是讲过了

266
00:08:03,660 --> 00:08:04,300
Redis数据库中

267
00:08:04,300 --> 00:08:05,900
它所有的数据都存在内存中

268
00:08:05,900 --> 00:08:07,000
那么一台普通的笔记本

269
00:08:07,000 --> 00:08:08,120
Redis一秒刻读写

270
00:08:08,120 --> 00:08:09,140
超过10万个箭子队

271
00:08:09,140 --> 00:08:10,360
但是呢

272
00:08:10,360 --> 00:08:12,100
数据存在内存中是不是有一个问题

273
00:08:12,100 --> 00:08:13,940
程序退出后会导致数据丢失

274
00:08:13,940 --> 00:08:14,620
不过呢

275
00:08:14,620 --> 00:08:16,540
Redis也提供了对数据持久化的支持

276
00:08:16,540 --> 00:08:17,600
怎么是数据持久化

277
00:08:17,600 --> 00:08:18,800
这里呢

278
00:08:18,800 --> 00:08:20,480
我来举一个浏览器里面的例子

279
00:08:20,480 --> 00:08:23,140
在浏览器里面

280
00:08:23,140 --> 00:08:25,360
数据持久化

281
00:08:25,360 --> 00:08:27,580
可以理解为

282
00:08:27,580 --> 00:08:28,900
可以理解为什么呢

283
00:08:28,900 --> 00:08:29,340
可以理解为

284
00:08:29,340 --> 00:08:30,780
比如说我们的

285
00:08:30,780 --> 00:08:33,180
local storage

286
00:08:33,180 --> 00:08:35,380
还有我们的cookie等等

287
00:08:35,380 --> 00:08:36,420
包括session storage

288
00:08:36,420 --> 00:08:39,620
这里呢就是它的一个特点

289
00:08:39,620 --> 00:08:41,180
数据持久化

290
00:08:41,180 --> 00:08:42,220
第三个特点呢

291
00:08:42,220 --> 00:08:43,500
功能丰富

292
00:08:43,500 --> 00:08:44,240
什么意思

293
00:08:44,240 --> 00:08:46,120
它的应用场景非常的丰富

294
00:08:46,120 --> 00:08:48,000
Redis呢也是名副其实的多面首

295
00:08:48,000 --> 00:08:50,120
它主要用于两个应用场景

296
00:08:50,120 --> 00:08:50,840
第一个缓存

297
00:08:50,840 --> 00:08:51,940
第二个对立系统

298
00:08:51,940 --> 00:08:52,600
什么是对立

299
00:08:52,600 --> 00:08:53,620
比如说我们有高并发

300
00:08:53,620 --> 00:08:54,760
服器处理不过来的时候

301
00:08:54,760 --> 00:08:55,900
它可以以一个对立的形式

302
00:08:55,900 --> 00:08:56,640
先存起来

303
00:08:56,640 --> 00:08:57,500
然后呢慢慢的去处理

304
00:08:57,500 --> 00:08:58,720
这也就是对立存在的一个意义

305
00:08:58,720 --> 00:09:00,660
而且呢Redis可以为每个K

306
00:09:00,660 --> 00:09:01,600
设置生存时间

307
00:09:01,600 --> 00:09:03,160
那么到期会自动删除

308
00:09:03,160 --> 00:09:04,940
这功能呢配合它的

309
00:09:04,940 --> 00:09:06,620
缓存系统来使用

310
00:09:06,620 --> 00:09:07,900
可以说是非常好

311
00:09:07,900 --> 00:09:10,400
那么由于Redis它支持

312
00:09:10,400 --> 00:09:12,280
持久化和非常丰富的数据类型

313
00:09:12,280 --> 00:09:15,320
也使它能成为Mapcache的竞争者

314
00:09:15,320 --> 00:09:15,500
好

315
00:09:15,500 --> 00:09:16,480
那么这里呢

316
00:09:16,480 --> 00:09:17,120
老师呢有一段

317
00:09:17,120 --> 00:09:20,020
关于Redis和Mapcache它的一个对比

318
00:09:20,020 --> 00:09:20,960
其实我们可以看一下

319
00:09:20,960 --> 00:09:22,680
Redis和Mapcache它的讨论

320
00:09:22,680 --> 00:09:24,020
也就是一个热门的话题

321
00:09:24,020 --> 00:09:24,660
谁更好

322
00:09:24,660 --> 00:09:25,840
其实呢

323
00:09:25,840 --> 00:09:27,480
我们只需要看结论

324
00:09:27,480 --> 00:09:29,160
因此在新项目中

325
00:09:29,160 --> 00:09:30,660
使用Redis去代替Mapcache

326
00:09:30,660 --> 00:09:31,740
将是非常好的选择

327
00:09:31,740 --> 00:09:32,640
为什么呀

328
00:09:32,640 --> 00:09:33,540
因为在性能上

329
00:09:33,540 --> 00:09:34,740
Redis它是单性成

330
00:09:34,740 --> 00:09:35,400
模型

331
00:09:35,400 --> 00:09:36,420
而MAMCATS呢

332
00:09:36,420 --> 00:09:37,380
只是多线程

333
00:09:37,380 --> 00:09:38,680
那么在多核服务器上

334
00:09:38,680 --> 00:09:39,460
理论上性能

335
00:09:39,460 --> 00:09:41,100
MAMCATS会更高一些

336
00:09:41,100 --> 00:09:42,520
但是前面介绍过

337
00:09:42,520 --> 00:09:43,120
Reddit的性能

338
00:09:43,120 --> 00:09:44,040
已经足够的优异

339
00:09:44,040 --> 00:09:46,000
那么在绝大部分场合下面

340
00:09:46,000 --> 00:09:47,120
其性能都会出现瓶颈

341
00:09:47,120 --> 00:09:47,800
所以说

342
00:09:47,800 --> 00:09:48,780
我们的重点

343
00:09:48,780 --> 00:09:49,960
应该关注的是什么呢

344
00:09:49,960 --> 00:09:50,700
功能上的区别

345
00:09:50,700 --> 00:09:52,560
那么随着Reddit 3.0的推出

346
00:09:52,560 --> 00:09:53,380
也就标志着

347
00:09:53,380 --> 00:09:54,800
MAMCATS几乎所有的功能

348
00:09:54,800 --> 00:09:55,880
都成为了Reddit的字迹

349
00:09:55,880 --> 00:09:57,200
说明什么问题

350
00:09:57,200 --> 00:09:58,840
你MAMCATS有了

351
00:09:58,840 --> 00:09:59,440
我Reddit都有

352
00:09:59,440 --> 00:10:00,860
而且我的性能也不弱

353
00:10:00,860 --> 00:10:02,880
所以说MamCut逐渐淡出了人们的视野

354
00:10:02,880 --> 00:10:05,160
Redis现在也就是越来越火的一个原因

355
00:10:05,160 --> 00:10:07,740
那么作为缓存系统

356
00:10:07,740 --> 00:10:09,040
Redis还可以限定数据

357
00:10:09,040 --> 00:10:10,900
它所占的最大空间

358
00:10:10,900 --> 00:10:11,580
什么意思

359
00:10:11,580 --> 00:10:15,380
也就是说你一个数据存储超过了一定的限制

360
00:10:15,380 --> 00:10:17,940
Redis它就会去自动双储不需要的K

361
00:10:17,940 --> 00:10:20,880
那么Redis的列表类型还可以用来实现对列

362
00:10:20,880 --> 00:10:22,480
并且支持主设置读取

363
00:10:22,480 --> 00:10:24,880
可以很容易的去实现一个高性能的优先级对列

364
00:10:24,880 --> 00:10:28,620
这是Redis它的实现对列的一个条件

365
00:10:28,620 --> 00:10:30,160
也就是列表类型

366
00:10:30,160 --> 00:10:32,240
而且呢Redis还可以支持发布订阅

367
00:10:32,240 --> 00:10:34,040
可以呢去构建聊天室等系统

368
00:10:34,040 --> 00:10:35,220
这里呢是它的一个应用范畴

369
00:10:35,220 --> 00:10:36,520
我们来看一下第四个特点

370
00:10:36,520 --> 00:10:37,480
简单稳定

371
00:10:37,480 --> 00:10:39,760
什么意思

372
00:10:39,760 --> 00:10:41,940
一个框架或者说一个工具

373
00:10:41,940 --> 00:10:42,960
你的功能再复杂

374
00:10:42,960 --> 00:10:44,020
如果你很复杂

375
00:10:44,020 --> 00:10:44,900
也没什么人用

376
00:10:44,900 --> 00:10:45,720
因为大家都很忙

377
00:10:45,720 --> 00:10:46,620
时间也很紧张

378
00:10:46,620 --> 00:10:47,980
没时间去搞了些乱七八糟

379
00:10:47,980 --> 00:10:48,360
对吧

380
00:10:48,360 --> 00:10:48,760
好

381
00:10:48,760 --> 00:10:50,240
那我们来看一下

382
00:10:50,240 --> 00:10:51,120
在关系数据库中

383
00:10:51,120 --> 00:10:52,420
比如说我们要去获取一段

384
00:10:52,420 --> 00:10:53,740
POST的表

385
00:10:53,740 --> 00:10:55,580
ID唯一的Title这段

386
00:10:55,580 --> 00:10:56,460
怎么去实现

387
00:10:56,460 --> 00:10:58,360
其实在买SOCO里面

388
00:10:58,360 --> 00:10:58,980
SOCO语句呢

389
00:10:58,980 --> 00:10:59,580
这样去写

390
00:10:59,580 --> 00:11:01,560
select title from post

391
00:11:01,560 --> 00:11:04,060
从posts里面去拿一个title字段

392
00:11:04,060 --> 00:11:04,700
拿谁呢

393
00:11:04,700 --> 00:11:05,540
id等于1

394
00:11:05,540 --> 00:11:07,080
而且呢限制为一条

395
00:11:07,080 --> 00:11:08,440
这呢是circle语句很长

396
00:11:08,440 --> 00:11:10,560
那么radis怎么去写呢

397
00:11:10,560 --> 00:11:13,020
直接hget post冒号1title

398
00:11:13,020 --> 00:11:14,900
这里呢就是radis的一个命令

399
00:11:14,900 --> 00:11:17,040
那么radis它一共有100多个命令

400
00:11:17,040 --> 00:11:17,760
虽然说很多

401
00:11:17,760 --> 00:11:19,360
但是常用的却只有十几个

402
00:11:19,360 --> 00:11:20,680
所以说同学们不要害怕

403
00:11:20,680 --> 00:11:22,140
它比circle语句要简单的多

404
00:11:22,140 --> 00:11:24,480
这里呢是一张radis它所有指令的一个截图

405
00:11:24,480 --> 00:11:26,300
第二个特点呢

406
00:11:26,300 --> 00:11:28,900
radis它是使用c元开发的

407
00:11:28,900 --> 00:11:32,220
代表量只有三万号 其实比较少的 不知道大家了解过react 它的一个原码

408
00:11:32,220 --> 00:11:34,920
 核心原码有多少 其实react 它的核心原码也只有1万多行

409
00:11:34,920 --> 00:11:37,800
这呢 降低了用户通过修改redis 原代码

410
00:11:37,800 --> 00:11:44,740
使之更适合自己项目需要的门槛 什么意思 你的原代码少 说明了我可以去定制化一些我的个性化需求

411
00:11:44,740 --> 00:11:48,360
 所以说呢redis 它在一个开源项目里面也是它越来越火的一个原因

412
00:11:48,360 --> 00:11:53,340
好 这里呢 就是redis 它的一些特点介绍 我们一起来总结一下

413
00:11:53,340 --> 00:11:54,660
首先

414
00:11:58,720 --> 00:11:59,060
第一章

415
00:11:59,060 --> 00:12:02,920
Redis 我们来看一下他的一个发展

416
00:12:02,920 --> 00:12:04,160
发展过程

417
00:12:04,160 --> 00:12:09,400
他的作者有几个人呢 同学们 这不作者两人 哪一年开源的

418
00:12:09,400 --> 00:12:13,040
是不是2009年

419
00:12:13,040 --> 00:12:19,680
他的有多少颗 star 39.xk 对吧

420
00:12:19,680 --> 00:12:20,840
不知道多少 反正就是3万

421
00:12:20,840 --> 00:12:24,680
3万多个 star 好 我们再来看一下他有哪些特例

422
00:12:26,400 --> 00:12:28,700
第1个他的数据结构

423
00:12:28,700 --> 00:12:31,000
是不是以字典的形式

424
00:12:31,000 --> 00:12:32,800
而且呢

425
00:12:32,800 --> 00:12:34,840
K是不是多种类型

426
00:12:34,840 --> 00:12:36,640
多种

427
00:12:36,640 --> 00:12:38,440
类型

428
00:12:38,440 --> 00:12:39,960
好第2个特点呢

429
00:12:39,960 --> 00:12:41,240
持久化

430
00:12:41,240 --> 00:12:42,280
对吧

431
00:12:42,280 --> 00:12:43,800
第3个特点呢

432
00:12:43,800 --> 00:12:45,080
是不是功能

433
00:12:45,080 --> 00:12:46,120
丰富

434
00:12:46,120 --> 00:12:47,140
他可以用来做什么

435
00:12:47,140 --> 00:12:48,160
一个缓存

436
00:12:48,160 --> 00:12:48,920
第2个呢

437
00:12:48,920 --> 00:12:50,720
对列

438
00:12:50,720 --> 00:12:52,760
做咱们的消息对列

439
00:12:52,760 --> 00:12:54,560
好第4个特点

440
00:12:54,560 --> 00:12:56,100
是不是API简单了

441
00:12:56,360 --> 00:12:58,400
他比Sircle语句简单很多

442
00:12:58,400 --> 00:13:00,720
好这里呢就是这节课的内容

