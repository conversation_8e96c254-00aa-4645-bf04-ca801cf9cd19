1
00:00:00,000 --> 00:00:03,260
好 那么这一节课我们就来看一下Engines

2
00:00:03,260 --> 00:00:08,200
我们怎么样通过Engines去进行咱们一个多进程的一个搭建

3
00:00:08,200 --> 00:00:11,520
首先Engines同学们可能没有听说过

4
00:00:11,520 --> 00:00:14,040
这里呢我给大家来简单介绍一下什么是Engines

5
00:00:14,040 --> 00:00:17,200
如果说有兴趣的同学可以去百度或者Google去看一下

6
00:00:17,200 --> 00:00:18,900
Engines呢其实它非常的简单

7
00:00:18,900 --> 00:00:21,620
它是俄罗斯人编写的一个十分清亮的http服务器

8
00:00:21,620 --> 00:00:23,280
什么是http服务器呀

9
00:00:23,280 --> 00:00:25,260
咱们是不是可以通过loads自己去写一个

10
00:00:25,260 --> 00:00:27,260
包括呢也有现成的比如说阿帕奇服务器

11
00:00:28,620 --> 00:00:30,820
那么endix它的翻译为ing

12
00:00:30,820 --> 00:00:34,360
它是一个高性能的http和反向代理服务器

13
00:00:34,360 --> 00:00:36,180
刚才咱们是不是讲解了什么是http服务器

14
00:00:36,180 --> 00:00:38,740
是不是就是处理request和response

15
00:00:38,740 --> 00:00:39,660
那么反向代理

16
00:00:39,660 --> 00:00:41,160
什么是反向代理

17
00:00:41,160 --> 00:00:42,400
那么这里说反向代理

18
00:00:42,400 --> 00:00:44,880
咱们就需要去提到什么是正向代理

19
00:00:44,880 --> 00:00:46,920
那么咱们先来看一下什么是正向代理

20
00:00:46,920 --> 00:00:50,580
正向代理就是客户端为代理

21
00:00:50,580 --> 00:00:52,260
服务器不知道客户是谁

22
00:00:52,260 --> 00:00:53,020
什么意思

23
00:00:53,020 --> 00:00:55,540
比如说你找马云去借钱

24
00:00:55,540 --> 00:00:57,620
你说喂

25
00:00:57,620 --> 00:00:58,980
马老师你好借我五万块钱

26
00:00:58,980 --> 00:01:00,320
你觉得他借不借

27
00:01:00,320 --> 00:01:01,340
他应该不会借吧

28
00:01:01,340 --> 00:01:02,960
他肯定会问你你是谁啊

29
00:01:02,960 --> 00:01:04,360
但是如果说你认识王思聪

30
00:01:04,360 --> 00:01:05,400
你跟王思聪说

31
00:01:05,400 --> 00:01:07,280
你说聪哥帮我找马云借五万块钱

32
00:01:07,280 --> 00:01:08,480
我跟他打电话他不借给我

33
00:01:08,480 --> 00:01:09,080
好

34
00:01:09,080 --> 00:01:09,880
那么这个时候呢

35
00:01:09,880 --> 00:01:11,540
王思聪就会打电话给马云

36
00:01:11,540 --> 00:01:13,520
说我有一个朋友需要用钱借五万块钱给我

37
00:01:13,520 --> 00:01:14,780
那你说马云借不借他肯定借

38
00:01:14,780 --> 00:01:15,600
但是马云呢

39
00:01:15,600 --> 00:01:17,640
他不知道找他借钱的人是谁

40
00:01:17,640 --> 00:01:20,580
也就是说服务器不知道客户端是谁

41
00:01:20,580 --> 00:01:21,020
好

42
00:01:21,020 --> 00:01:22,760
那么我就来看一下反向代理是什么

43
00:01:22,760 --> 00:01:24,800
反向代理其实还是和

44
00:01:24,800 --> 00:01:27,660
咱们再举一个借钱的例子

45
00:01:27,660 --> 00:01:28,220
比如说

46
00:01:28,220 --> 00:01:30,420
大家都知道借贝吧

47
00:01:30,420 --> 00:01:31,600
制服宝里面的

48
00:01:31,600 --> 00:01:33,240
我们平时缺钱的时候

49
00:01:33,240 --> 00:01:33,860
可能会用一下

50
00:01:33,860 --> 00:01:34,260
是吧

51
00:01:34,260 --> 00:01:36,360
那么你去找借贝借钱

52
00:01:36,360 --> 00:01:38,740
咱们全中国人民都找借贝去借钱

53
00:01:38,740 --> 00:01:39,200
他

54
00:01:39,200 --> 00:01:42,320
他借不借得过来呀

55
00:01:42,320 --> 00:01:42,940
你十四亿人

56
00:01:42,940 --> 00:01:43,900
每人找他借一块钱

57
00:01:43,900 --> 00:01:44,700
就是十四亿

58
00:01:44,700 --> 00:01:45,120
何况

59
00:01:45,120 --> 00:01:47,680
像大家喜欢借几千几万的

60
00:01:47,680 --> 00:01:48,940
那可能借贝没这么多钱

61
00:01:48,940 --> 00:01:49,800
那怎么办呢

62
00:01:49,800 --> 00:01:50,800
他借贝

63
00:01:50,800 --> 00:01:51,880
他是不是只是一个中间商

64
00:01:51,880 --> 00:01:54,380
他会去找一些其他的第三方机构

65
00:01:54,380 --> 00:01:55,240
去合作

66
00:01:55,240 --> 00:01:56,180
你比如说

67
00:01:56,180 --> 00:01:57,160
借备会找一些

68
00:01:57,160 --> 00:01:58,280
什么什么金融机构

69
00:01:58,280 --> 00:01:58,840
什么什么银行

70
00:01:58,840 --> 00:01:59,340
说我钱不够

71
00:01:59,340 --> 00:02:00,620
你帮我拿点钱出来借给我的客户

72
00:02:00,620 --> 00:02:01,940
好

73
00:02:01,940 --> 00:02:02,600
这个时候呢

74
00:02:02,600 --> 00:02:03,620
你找借备借钱

75
00:02:03,620 --> 00:02:04,380
他也找借备借钱

76
00:02:04,380 --> 00:02:05,160
但是呢

77
00:02:05,160 --> 00:02:06,260
你不知道

78
00:02:06,260 --> 00:02:06,960
你实际上

79
00:02:06,960 --> 00:02:08,660
实际上

80
00:02:08,660 --> 00:02:10,100
是谁给的钱你的吧

81
00:02:10,100 --> 00:02:11,020
你只知道你找的是借备

82
00:02:11,020 --> 00:02:12,160
但是借备找的什么银行

83
00:02:12,160 --> 00:02:13,240
或者找的什么金融机构

84
00:02:13,240 --> 00:02:14,140
你不知道吧

85
00:02:14,140 --> 00:02:14,520
你只知道

86
00:02:14,520 --> 00:02:15,800
你找的借备

87
00:02:15,800 --> 00:02:16,420
所以说呢

88
00:02:16,420 --> 00:02:17,200
此时你是客户端

89
00:02:17,200 --> 00:02:19,000
那么真正借钱给你的是服务端

90
00:02:19,000 --> 00:02:19,360
服务端

91
00:02:19,360 --> 00:02:20,420
你是不知道他是谁的

92
00:02:20,420 --> 00:02:21,860
你只知道你的代理是戒备

93
00:02:21,860 --> 00:02:23,040
所以这里就是反向代理

94
00:02:23,040 --> 00:02:26,380
那么Engines它的作用就类似于咱们刚才举的例子里面的戒备

95
00:02:26,380 --> 00:02:27,200
好

96
00:02:27,200 --> 00:02:29,640
那么我们来看一下怎么样去配置一个Engines

97
00:02:29,640 --> 00:02:30,840
它的配置很简单

98
00:02:30,840 --> 00:02:32,140
首先呢

99
00:02:32,140 --> 00:02:34,660
会配置四个关口

100
00:02:34,660 --> 00:02:36,940
四个关口就是你本地所起的Sever了

101
00:02:36,940 --> 00:02:38,180
也就是说你去

102
00:02:38,180 --> 00:02:40,400
比如说你的NodeGX去create一个HTTP

103
00:02:40,400 --> 00:02:41,360
是不是就起一个关口啊

104
00:02:41,360 --> 00:02:42,260
比如说3000

105
00:02:42,260 --> 00:02:43,560
然后你再起一个关口

106
00:02:43,560 --> 00:02:45,360
HTTP.createSever3001

107
00:02:45,360 --> 00:02:47,640
然后此时你同时起了四个进程

108
00:02:47,640 --> 00:02:49,640
但是客户端去访问的时候

109
00:02:49,640 --> 00:02:51,180
他不可能访问你四个端口吧

110
00:02:51,180 --> 00:02:52,340
他肯定只需要访问一个

111
00:02:52,340 --> 00:02:52,900
所以呢

112
00:02:52,900 --> 00:02:54,460
N进士就做了一个这样一个带领

113
00:02:54,460 --> 00:02:55,460
他去监听80端口

114
00:02:55,460 --> 00:02:56,440
所有的请求

115
00:02:56,440 --> 00:02:58,760
他都会转发到这四个进程上面去

116
00:02:58,760 --> 00:03:00,600
而且他里面还可以去配置一些规则

117
00:03:00,600 --> 00:03:03,140
比如说我3000端口

118
00:03:03,140 --> 00:03:05,240
他的一个权重是多少

119
00:03:05,240 --> 00:03:05,840
比如说3000

120
00:03:05,840 --> 00:03:06,940
这个进程他强一点

121
00:03:06,940 --> 00:03:07,800
他能处理2000条

122
00:03:07,800 --> 00:03:08,720
那么3000你要呢

123
00:03:08,720 --> 00:03:09,520
他只能处理1000条

124
00:03:09,520 --> 00:03:11,180
这里呢也是通过N进士的配置可以去配的

125
00:03:11,180 --> 00:03:13,280
这里呢老师就不详细介绍了

126
00:03:13,280 --> 00:03:14,860
我来画一个图

127
00:03:14,860 --> 00:03:15,920
来让大家来理解一下

128
00:03:15,920 --> 00:03:17,200
到底什么事通过ngs去带领

129
00:03:17,200 --> 00:03:17,960
首先

130
00:03:17,960 --> 00:03:19,240
我们是不是客户端发起请求

131
00:03:19,240 --> 00:03:20,780
然后呢ngs去带领

132
00:03:20,780 --> 00:03:22,320
带领哪个帮口呢

133
00:03:22,320 --> 00:03:24,620
带领呢咱们的假说80端口

134
00:03:24,620 --> 00:03:25,400
好

135
00:03:25,400 --> 00:03:26,420
那么带领80端口

136
00:03:26,420 --> 00:03:27,440
我们提供服务

137
00:03:27,440 --> 00:03:29,480
提供服务肯定不是一个进程可以搞定吧

138
00:03:29,480 --> 00:03:30,760
肯定需要很多个进程

139
00:03:30,760 --> 00:03:31,540
好

140
00:03:31,540 --> 00:03:33,580
这里呢

141
00:03:33,580 --> 00:03:34,360
比如说我们在

142
00:03:34,360 --> 00:03:35,880
local

143
00:03:35,880 --> 00:03:37,160
host

144
00:03:37,160 --> 00:03:37,940
8000

145
00:03:37,940 --> 00:03:38,700
咱们是不是

146
00:03:38,700 --> 00:03:39,480
起了个服务啊

147
00:03:39,480 --> 00:03:40,500
nogs create7

148
00:03:40,500 --> 00:03:41,000
好

149
00:03:41,000 --> 00:03:42,040
那么一个端口肯定不够

150
00:03:42,040 --> 00:03:43,320
那么我要create两个

151
00:03:43,320 --> 00:03:44,340
这里呢

152
00:03:45,620 --> 00:03:48,180
我呢又在8001起了一个

153
00:03:48,180 --> 00:03:49,200
两个可能也不够

154
00:03:49,200 --> 00:03:50,740
那么呢我起三个

155
00:03:50,740 --> 00:03:55,100
好这里呢只是举个例子

156
00:03:55,100 --> 00:04:00,220
这里呢就是我们的NGX的一个模型比如说你客户端发起请求到NGX

157
00:04:00,220 --> 00:04:02,780
那么NGX呢他呢会去定制一些规则

158
00:04:02,780 --> 00:04:06,100
比如说他去找8000找8001找8002去提供服务

159
00:04:06,100 --> 00:04:08,140
这里呢就是NGX他的一个

160
00:04:08,140 --> 00:04:09,420
逻辑

161
00:04:09,420 --> 00:04:11,220
那么这样一种进行多进程

162
00:04:11,220 --> 00:04:12,760
服务的方式他有什么

163
00:04:12,760 --> 00:04:15,060
推点呢大家注意到没有

164
00:04:15,580 --> 00:04:16,600
它也没什么缺点啊同学们

165
00:04:16,600 --> 00:04:20,440
大家觉得它也没什么缺点

166
00:04:20,440 --> 00:04:20,960
其实

167
00:04:20,960 --> 00:04:22,500
如果说

168
00:04:22,500 --> 00:04:26,340
我们之前是不是讲到过咱们的cluster是可以exit的

169
00:04:26,340 --> 00:04:27,100
包括disconnect

170
00:04:27,100 --> 00:04:28,120
也就是说咱们的进程

171
00:04:28,120 --> 00:04:30,180
可能会被关闭或者说会发生一些错误

172
00:04:30,180 --> 00:04:31,960
然后呢cluster需要去重新的fork

173
00:04:31,960 --> 00:04:34,020
咱们fork是不是就会创建一个新进程

174
00:04:34,020 --> 00:04:35,800
但大家注意到没有

175
00:04:35,800 --> 00:04:36,580
我们的engines

176
00:04:36,580 --> 00:04:38,360
它假如说3000端口

177
00:04:38,360 --> 00:04:39,140
它给挂掉了

178
00:04:39,140 --> 00:04:40,160
假如说挂掉了

179
00:04:40,160 --> 00:04:42,720
那么我们有没有什么办法去重启

180
00:04:42,720 --> 00:04:44,260
重启没有吧

181
00:04:44,500 --> 00:04:46,240
你可能需要重启整个n进食服务器

182
00:04:46,240 --> 00:04:47,680
那么当你重启n进食服务器

183
00:04:47,680 --> 00:04:48,820
那么咱们n进食是入口吧

184
00:04:48,820 --> 00:04:49,560
就跟戒备一样

185
00:04:49,560 --> 00:04:50,300
戒备是入口

186
00:04:50,300 --> 00:04:51,220
那如果说戒备挂了

187
00:04:51,220 --> 00:04:52,780
那你是不是整个戒险的平台都挂了

188
00:04:52,780 --> 00:04:54,740
那么咱们n进食是不是不能重启啊

189
00:04:54,740 --> 00:04:55,760
那么这server怎么办

190
00:04:55,760 --> 00:04:56,780
三千端口挂掉了

191
00:04:56,780 --> 00:04:57,840
那么挂掉它就挂掉了

192
00:04:57,840 --> 00:04:59,080
如果说3001挂掉了

193
00:04:59,080 --> 00:05:00,440
它也就挂掉了

194
00:05:00,440 --> 00:05:01,960
它是没有重启机制的

195
00:05:01,960 --> 00:05:03,760
所以说n进食它的实际用场景

196
00:05:03,760 --> 00:05:06,040
它比较适合稳定的服务

197
00:05:06,040 --> 00:05:07,360
咱们在项目中是不适合

198
00:05:07,360 --> 00:05:09,700
去用Engine去提一个多进程的服务的

199
00:05:09,700 --> 00:05:11,640
它比较适合一些静态资源服务器

200
00:05:11,640 --> 00:05:14,820
你比如说去请求一些js,CSS,HTML

201
00:05:14,820 --> 00:05:17,380
这样一些静态的文件它是不是很稳定了

202
00:05:17,380 --> 00:05:19,180
静态的文件不可能出错吧

203
00:05:19,180 --> 00:05:20,060
所以咱们动态的

204
00:05:20,060 --> 00:05:22,000
你比如说你去查询一条Secure语句

205
00:05:22,000 --> 00:05:23,160
它可能你可能写错了

206
00:05:23,160 --> 00:05:24,760
然后会导致整个服务器挂掉

207
00:05:24,760 --> 00:05:25,880
包括了企业级的集群

208
00:05:25,880 --> 00:05:27,440
怎么理解企业级的集群

209
00:05:27,440 --> 00:05:29,100
咱们企业级的集群是不是非常稳定

210
00:05:29,100 --> 00:05:31,380
你比如说你可以以Engine为代理

211
00:05:31,380 --> 00:05:33,780
你直接代理到某一个公司的端口上面去

212
00:05:37,360 --> 00:05:39,920
另一个集群 这样他是不是很稳定 那么N进士呢他

213
00:05:39,920 --> 00:05:41,460
比较适合稳定的服务

214
00:05:41,460 --> 00:05:43,240
好 那么我们就来了

215
00:05:43,240 --> 00:05:44,780
回顾一下这节课的内容

216
00:05:44,780 --> 00:05:46,840
这节课呢 我们其实就是介绍N进士

217
00:05:46,840 --> 00:05:47,860
N进士呢

218
00:05:47,860 --> 00:05:49,140
他是怎么一回事

219
00:05:49,140 --> 00:05:52,340
是不是咱们通过客户端去发起请求到我们的

220
00:05:52,340 --> 00:05:53,240
N进士代理

221
00:05:53,240 --> 00:05:54,000
那是花币

222
00:05:54,000 --> 00:05:55,540
花币呢 他可能没这么多钱

223
00:05:55,540 --> 00:05:57,840
他就去找其他一些代理商 比如说他把80端口

224
00:05:57,840 --> 00:05:59,640
比如说咱们客户请求的是80端口

225
00:05:59,640 --> 00:06:01,160
但是80端口

226
00:06:01,160 --> 00:06:04,240
他会把这些请求给分发到其他那些进程 比如说8000

227
00:06:04,240 --> 00:06:05,260
80001 80002

228
00:06:05,260 --> 00:06:05,780
然后呢

229
00:06:05,780 --> 00:06:06,280
这些

230
00:06:07,360 --> 00:06:11,960
进进程咱们获取到数据之后再返回给Nginx的80然后再给扣端提供服务

231
00:06:11,960 --> 00:06:13,760
这里呢就是Nginx他的一个

232
00:06:13,760 --> 00:06:16,060
他提供服务的一个过程

233
00:06:16,060 --> 00:06:18,620
那么Nginx呢实际上他是比较适合稳定的

234
00:06:18,620 --> 00:06:19,140
服务

235
00:06:19,140 --> 00:06:22,980
比如说境态资源服务器比如说企业纪的机群好这里呢就是这节课内容

