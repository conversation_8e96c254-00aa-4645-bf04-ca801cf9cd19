1
00:00:00,000 --> 00:00:01,780
好,那么接下来我们来做一个总结

2
00:00:01,780 --> 00:00:03,020
那么对于ATPS来说

3
00:00:03,020 --> 00:00:04,640
其实就是在ATPS的基础上

4
00:00:04,640 --> 00:00:07,760
使用了TLS和SSL进行了数据的加密

5
00:00:07,760 --> 00:00:08,760
然后再进行传输的

6
00:00:08,760 --> 00:00:11,900
那么相对于ATPS的传输数据的这种方式

7
00:00:11,900 --> 00:00:13,160
要更加的安全一些

8
00:00:13,160 --> 00:00:15,980
而我们为了防止中间人的劫持的出现

9
00:00:15,980 --> 00:00:17,980
那我们需要对我们的服务器进行一个认证

10
00:00:17,980 --> 00:00:20,100
那么认证是由第三方的CE机构进行认证的

11
00:00:20,100 --> 00:00:23,800
而我们在不知道去怎么申请国际CE证书的认证的时候

12
00:00:23,800 --> 00:00:25,280
我们可以在本地进行模拟

13
00:00:25,280 --> 00:00:26,520
但实际上并没有什么用

14
00:00:26,520 --> 00:00:28,240
因为你就算模拟出来生成了证书

15
00:00:28,240 --> 00:00:29,160
那么浏览器也不认

16
00:00:29,160 --> 00:00:30,600
所以在前面讲解的时候

17
00:00:30,600 --> 00:00:31,480
我就说那个命令

18
00:00:31,480 --> 00:00:32,460
其实如果你感兴趣

19
00:00:32,460 --> 00:00:32,980
你就去查一查

20
00:00:32,980 --> 00:00:33,640
如果你不感兴趣

21
00:00:33,640 --> 00:00:34,300
也没有关系

22
00:00:34,300 --> 00:00:35,260
因为我们真实的情况

23
00:00:35,260 --> 00:00:36,320
根本就不用

24
00:00:36,320 --> 00:00:37,860
但是我们可以借助于

25
00:00:37,860 --> 00:00:39,420
模拟的CA机构产生的证书

26
00:00:39,420 --> 00:00:40,340
来搭建起一个

27
00:00:40,340 --> 00:00:41,460
来进行加密传输的

28
00:00:41,460 --> 00:00:42,780
ATBS这样的一个服务器

29
00:00:42,780 --> 00:00:43,500
只不过浏览器

30
00:00:43,500 --> 00:00:44,520
会给我们报一个警告而已

31
00:00:44,520 --> 00:00:45,460
那么为了不让浏览器

32
00:00:45,460 --> 00:00:45,940
产生这个警告

33
00:00:45,940 --> 00:00:47,300
我们需要去我们国内的

34
00:00:47,300 --> 00:00:47,820
像阿里云亚

35
00:00:47,820 --> 00:00:48,260
通讯云亚

36
00:00:48,260 --> 00:00:49,400
这些CA机构的一些代理商

37
00:00:49,400 --> 00:00:50,360
那里去申请一些

38
00:00:50,360 --> 00:00:51,560
CA机构的证书

39
00:00:51,560 --> 00:00:53,140
那么在申请的时候需要注意一点

40
00:00:53,140 --> 00:00:55,500
你需要提前申请好一个真实的域名

41
00:00:55,500 --> 00:00:57,860
需要备案通过的真实的域名和服务器

42
00:00:57,860 --> 00:01:00,700
那么才能够通过国际的CA机构的一个认证

43
00:01:00,700 --> 00:01:02,280
那么得到这个证书之后

44
00:01:02,280 --> 00:01:03,400
我们把证书下的下来

45
00:01:03,400 --> 00:01:05,600
根据我们在Node中的一个代码提示

46
00:01:05,600 --> 00:01:08,680
把证书由模拟CA的换成我们真实CA的这个证书

47
00:01:08,680 --> 00:01:09,920
就可以正常上线和运行了

48
00:01:09,920 --> 00:01:12,360
那么代码和HTB的代码几乎没有什么区别

49
00:01:12,360 --> 00:01:14,480
只是加了一个证书的认证而已

50
00:01:14,480 --> 00:01:18,420
好那么这就是整个的ATBS服务器的一个答件

