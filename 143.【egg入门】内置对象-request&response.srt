1
00:00:00,000 --> 00:00:01,540
好 这节课我们就来看一下

2
00:00:01,540 --> 00:00:03,580
request和response

3
00:00:03,580 --> 00:00:07,160
那么request和response 实际上呢 是不是咱们经常会使用啊 它挂载在哪里啊

4
00:00:07,160 --> 00:00:08,700
是不是在咱们的context对象下面呢

5
00:00:08,700 --> 00:00:10,760
那么request和response

6
00:00:10,760 --> 00:00:12,540
它是不是去解析我们的

7
00:00:12,540 --> 00:00:16,640
解析我们的一个请求和我们的一个response 那我们就来看一下

8
00:00:16,640 --> 00:00:22,020
好 那比如说

9
00:00:22,020 --> 00:00:24,060
比如说我们就以根目录

10
00:00:24,060 --> 00:00:27,400
比如说我们就以 比如说我们访问了一个根目录 然后呢 去对应到咱们的一个

11
00:00:27,400 --> 00:00:29,180
home的index 这样一个contrl上面

12
00:00:30,000 --> 00:00:36,060
好 那么这里呢 我们来看一下 比如说我们去挡印一下

13
00:00:36,060 --> 00:00:38,660
contest.re

14
00:00:38,660 --> 00:00:40,440
re

15
00:00:40,440 --> 00:00:45,780
creas的点 什么呢 我们来看一下他的query是不是就可以解析我们的一个

16
00:00:45,780 --> 00:00:50,240
怎么样 是不是可以解析我们的一个参数啊 地址哪个参数 那么我们就来

17
00:00:50,240 --> 00:00:53,880
看一下咱们的request能不能够去解析

18
00:00:53,880 --> 00:00:57,880
好 比如说我们现在访问根部录 然后呢 我给他一些参数 比如说name

19
00:00:57,880 --> 00:00:59,300
name等于deck

20
00:01:00,300 --> 00:01:01,620
然后呢age等于

21
00:01:01,620 --> 00:01:06,060
18好 走里好 那我们来看一下空材打印出来的 大家可以看到

22
00:01:06,060 --> 00:01:14,380
我们的contest.request.query是不是打印出来是name jackage18这里呢 request 这样一个对象是不是可以帮我们去解析咱们一个参数

23
00:01:14,380 --> 00:01:16,800
包括呢readbounce是不是也可以去做一些事情呢

24
00:01:16,800 --> 00:01:22,020
好 这里呢就是我们的一个request和readbounce他这样的两个对象好 我们这里来

25
00:01:22,020 --> 00:01:23,900
回顾一下

26
00:01:23,900 --> 00:01:28,860
刚才我们是不是介绍了request和咱们的一个read

27
00:01:29,300 --> 00:01:31,860
创始它在哪里它的作用是什么

28
00:01:31,860 --> 00:01:32,880
作用是不是就是

29
00:01:32,880 --> 00:01:35,180
解析咱们一个请求参数

30
00:01:35,180 --> 00:01:37,500
那么在哪里可以读取啊是不是挂在

31
00:01:37,500 --> 00:01:41,340
contest对象上面好这里呢就是我们这几个的内容

