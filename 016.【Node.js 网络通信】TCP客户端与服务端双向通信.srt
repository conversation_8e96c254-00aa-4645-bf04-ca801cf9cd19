1
00:00:00,000 --> 00:00:02,760
那我们这一小节目就来看一下

2
00:00:02,760 --> 00:00:05,660
就是当客户端和服务端建立连接成功以后

3
00:00:05,660 --> 00:00:08,260
接下来双方如何去进行数据交互

4
00:00:08,260 --> 00:00:09,140
就是说白了

5
00:00:09,140 --> 00:00:10,200
如何去调用API

6
00:00:10,200 --> 00:00:12,500
就是完成数据的发送和数据的接收

7
00:00:12,500 --> 00:00:14,700
那首先我们带来看一下

8
00:00:14,700 --> 00:00:15,960
那服务端

9
00:00:15,960 --> 00:00:17,580
就是当我们这个服务端

10
00:00:17,580 --> 00:00:19,860
就是这个connection事件被触发以后

11
00:00:19,860 --> 00:00:21,220
我们就拿到这个client socket

12
00:00:21,220 --> 00:00:24,220
也就是当前与之通信的客户端的这个端口

13
00:00:24,220 --> 00:00:26,740
我拿到它就可以来给这个当前连接的客户端

14
00:00:26,740 --> 00:00:28,120
去进行数据的发送了

15
00:00:28,120 --> 00:00:29,360
所以说

16
00:00:29,360 --> 00:00:34,820
那么这个就是给客户端发消息

17
00:00:34,820 --> 00:00:37,980
这是服务端给客户端发消息

18
00:00:37,980 --> 00:00:38,760
就这么难做

19
00:00:38,760 --> 00:00:40,720
那么客户端给服务端

20
00:00:40,720 --> 00:00:42,200
该怎么去发消息呢

21
00:00:42,200 --> 00:00:43,720
其实也非常的简单

22
00:00:43,720 --> 00:00:45,320
我们在这里

23
00:00:45,320 --> 00:00:50,080
当这个客户端与服务端连接

24
00:00:50,080 --> 00:00:54,640
就是说建立连接成功以后

25
00:00:54,640 --> 00:00:57,120
然后客户端就可以给这个服务端

26
00:00:57,120 --> 00:00:58,600
发送数据

27
00:00:58,600 --> 00:00:59,420
发送数据了

28
00:00:59,420 --> 00:01:00,240
怎么去发

29
00:01:00,240 --> 00:01:01,400
我们大概就是client

30
00:01:01,400 --> 00:01:02,240
并write

31
00:01:02,240 --> 00:01:03,180
client并write

32
00:01:03,180 --> 00:01:04,500
因为我们在这儿

33
00:01:04,500 --> 00:01:05,220
给它输出一个

34
00:01:05,220 --> 00:01:06,420
输出一个vode

35
00:01:06,420 --> 00:01:08,460
输出一个vode

36
00:01:08,460 --> 00:01:08,940
好

37
00:01:08,940 --> 00:01:09,980
那这样的话

38
00:01:09,980 --> 00:01:11,280
我们就把它去发过来了

39
00:01:11,280 --> 00:01:12,240
那发过来以后

40
00:01:12,240 --> 00:01:13,720
服务端怎么去接收

41
00:01:13,720 --> 00:01:14,880
服务端就是说

42
00:01:14,880 --> 00:01:15,940
怎么接收客户端

43
00:01:15,940 --> 00:01:16,880
发过来的数据呢

44
00:01:16,880 --> 00:01:18,600
那这里就像这么来做

45
00:01:18,600 --> 00:01:20,100
就是说服务端

46
00:01:20,100 --> 00:01:21,660
就是监听

47
00:01:21,660 --> 00:01:23,160
监听什么呢

48
00:01:23,160 --> 00:01:24,240
这个叫client

49
00:01:24,240 --> 00:01:25,760
socket的

50
00:01:25,760 --> 00:01:26,700
它的这个叫做

51
00:01:26,700 --> 00:01:27,820
data事件

52
00:01:27,820 --> 00:01:30,220
当这个data事件被触发

53
00:01:30,220 --> 00:01:31,280
那么就表示收到

54
00:01:31,280 --> 00:01:32,240
客户端发过了的数据了

55
00:01:32,240 --> 00:01:33,900
所以说我们在这个client

56
00:01:33,900 --> 00:01:35,980
socket.onData

57
00:01:35,980 --> 00:01:37,020
我们就得这么来写

58
00:01:37,020 --> 00:01:39,260
同样的这个data呢

59
00:01:39,260 --> 00:01:40,140
它默认情况下呢

60
00:01:40,140 --> 00:01:40,740
都是二进制

61
00:01:40,740 --> 00:01:42,240
所以说我们再去给它

62
00:01:42,240 --> 00:01:42,860
toolstream一下

63
00:01:42,860 --> 00:01:43,720
转成我们这个

64
00:01:43,720 --> 00:01:44,800
能识别的字符串

65
00:01:44,800 --> 00:01:45,820
所以就这么来做

66
00:01:45,820 --> 00:01:48,820
那服务端给客户端发的消息

67
00:01:48,820 --> 00:01:49,740
客户端怎么来接呢

68
00:01:49,740 --> 00:01:51,160
那客户端就是这样来接

69
00:01:51,160 --> 00:01:52,580
所以我们再给它加上

70
00:01:52,580 --> 00:01:53,600
客户端

71
00:01:53,600 --> 00:01:56,080
监听

72
00:01:56,080 --> 00:01:58,180
这个也是这个叫data事件

73
00:01:58,180 --> 00:02:00,200
data事件

74
00:02:00,200 --> 00:02:01,820
就是当服务端

75
00:02:01,820 --> 00:02:04,300
发消息过来

76
00:02:04,300 --> 00:02:06,420
就会触发该

77
00:02:06,420 --> 00:02:07,580
事件啊所以就这个意思

78
00:02:07,580 --> 00:02:09,260
好了那我们在这儿

79
00:02:09,260 --> 00:02:12,060
我们这个叫服务端说

80
00:02:12,060 --> 00:02:14,200
对吧我们可以在这里

81
00:02:14,200 --> 00:02:17,020
加上客户端说

82
00:02:17,020 --> 00:02:18,900
那这样我们看起来就会更清晰一点

83
00:02:18,900 --> 00:02:20,660
好了那写好以后

84
00:02:20,660 --> 00:02:22,440
接下来就会回到我们命连行当中

85
00:02:22,440 --> 00:02:23,480
我们就可以来看一下

86
00:02:23,480 --> 00:02:24,060
来看一下

87
00:02:24,060 --> 00:02:24,860
打明灵行

88
00:02:24,860 --> 00:02:26,400
那么这个时候

89
00:02:26,400 --> 00:02:27,180
我们再来看一下

90
00:02:27,180 --> 00:02:28,280
我先把服务端给它挂掉

91
00:02:28,280 --> 00:02:29,440
我们都给它挂掉

92
00:02:29,440 --> 00:02:32,020
我们先把服务端

93
00:02:32,020 --> 00:02:33,200
来给它启动起来

94
00:02:33,200 --> 00:02:35,260
那这个就是32.js

95
00:02:35,260 --> 00:02:36,100
好了

96
00:02:36,100 --> 00:02:36,560
我们可以看到

97
00:02:36,560 --> 00:02:37,480
服务端启动成功

98
00:02:37,480 --> 00:02:38,560
然后接下来

99
00:02:38,560 --> 00:02:39,860
来启动我们的客户端

100
00:02:39,860 --> 00:02:40,940
也就是client.js

101
00:02:40,940 --> 00:02:42,160
好 启动成功

102
00:02:42,160 --> 00:02:43,080
然后接下来

103
00:02:43,080 --> 00:02:43,880
我们再来就可以看到

104
00:02:43,880 --> 00:02:45,120
首先

105
00:02:45,120 --> 00:02:46,560
我们的客户端

106
00:02:46,560 --> 00:02:47,680
成功的连接到了服务器

107
00:02:47,680 --> 00:02:49,040
然后收到了服务器

108
00:02:49,040 --> 00:02:49,600
发这个消息

109
00:02:49,600 --> 00:02:49,940
就是Hello

110
00:02:49,940 --> 00:02:52,140
当连到服务器以后

111
00:02:52,140 --> 00:02:54,520
服务器的这个叫做connection事件

112
00:02:54,520 --> 00:02:55,020
被触发

113
00:02:55,020 --> 00:02:56,520
所以服务端就输出了

114
00:02:56,520 --> 00:02:57,480
有新的连接进来了

115
00:02:57,480 --> 00:03:01,380
然后当然我们通过客户端这个socket

116
00:03:01,380 --> 00:03:02,240
给客户端发这个消息

117
00:03:02,240 --> 00:03:03,660
客户端监听了data

118
00:03:03,660 --> 00:03:05,340
拿到了服务端发过来的数据

119
00:03:05,340 --> 00:03:07,760
然后客户端与服务端连接接成功以后

120
00:03:07,760 --> 00:03:09,400
给服务端发了一个消息

121
00:03:09,400 --> 00:03:13,580
服务端监听了当前客户端的data事件

122
00:03:13,580 --> 00:03:16,500
就拿到了当前客户端发过来的数据了

123
00:03:16,500 --> 00:03:17,960
所以说我们待会就能看到

124
00:03:17,960 --> 00:03:21,660
双方互相发送的消息数据了

