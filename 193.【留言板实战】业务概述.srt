1
00:00:00,000 --> 00:00:03,340
到上节目为止

2
00:00:03,340 --> 00:00:06,220
关于GWK2的客户端开发和服务端开发的主要的预发细节

3
00:00:06,220 --> 00:00:07,380
我们做了一个系统的铺垫

4
00:00:07,380 --> 00:00:09,720
那接下来我们通过一个稍微复杂意志的案例

5
00:00:09,720 --> 00:00:11,200
把这些预发点揉合进来

6
00:00:11,200 --> 00:00:12,740
从而大家全面的来认识一下

7
00:00:12,740 --> 00:00:15,640
如何基于GWK2来做一个前后端的交互

8
00:00:15,640 --> 00:00:17,500
那首先我们来认识一下

9
00:00:17,500 --> 00:00:18,820
这个留言板的案例

10
00:00:18,820 --> 00:00:19,660
它的这个业务细节

11
00:00:19,660 --> 00:00:22,820
那这儿呢我们准备好了一个实际的效果

12
00:00:22,820 --> 00:00:24,060
那从页面来看的话

13
00:00:24,060 --> 00:00:25,960
这个留言板的功能主要包括两部分

14
00:00:25,960 --> 00:00:27,240
一个就是列表的展示

15
00:00:27,240 --> 00:00:30,420
就把曾经流过的信息展示出来

16
00:00:30,420 --> 00:00:32,440
另外一个就是可以添加新的信息

17
00:00:32,440 --> 00:00:34,260
这里边可以输入留言的内容

18
00:00:34,260 --> 00:00:35,000
还有姓名

19
00:00:35,000 --> 00:00:35,760
然后点发布

20
00:00:35,760 --> 00:00:37,060
就可以产生一条新的记录

21
00:00:37,060 --> 00:00:38,260
这是读写的功能

22
00:00:38,260 --> 00:00:40,420
但是单纯的留言板的功能

23
00:00:40,420 --> 00:00:41,540
比较单一

24
00:00:41,540 --> 00:00:42,740
所以说在这个基础之上

25
00:00:42,740 --> 00:00:44,400
我们添加了一个新的功能

26
00:00:44,400 --> 00:00:46,760
就是下面的友情链接

27
00:00:46,760 --> 00:00:48,480
这样我们的数据就稍微复杂一点

28
00:00:48,480 --> 00:00:49,580
并且在这个右侧

29
00:00:49,580 --> 00:00:50,520
我们又添加了一个新功能

30
00:00:50,520 --> 00:00:51,560
就是查询天气的数据

31
00:00:51,560 --> 00:00:53,200
所以说我们这个音频的结构

32
00:00:53,200 --> 00:00:54,260
相对要复杂一些

33
00:00:54,260 --> 00:00:55,620
从查询的角度来说

34
00:00:55,620 --> 00:00:57,020
实际上我们包含三部分数据

35
00:00:57,020 --> 00:00:58,340
一个就是流言版的列表

36
00:00:58,340 --> 00:00:59,420
另外一个就是有形链接

37
00:00:59,420 --> 00:01:01,100
还有一个就是天气数据

38
00:01:01,100 --> 00:01:03,360
那首先我们直观的来看一下

39
00:01:03,360 --> 00:01:04,260
这里边查出的数据

40
00:01:04,260 --> 00:01:05,180
长什么样子

41
00:01:05,180 --> 00:01:06,420
打开控制台

42
00:01:06,420 --> 00:01:07,640
然后我们再刷新一下

43
00:01:07,640 --> 00:01:10,260
然后我们看这里边数据的结构

44
00:01:10,260 --> 00:01:11,620
实际上包含三部分

45
00:01:11,620 --> 00:01:13,720
在那英文下面有三个子属性

46
00:01:13,720 --> 00:01:15,140
其中这个list表示的

47
00:01:15,140 --> 00:01:17,180
就是流言版的列表数据

48
00:01:17,180 --> 00:01:18,040
那这个有形链接的话

49
00:01:18,040 --> 00:01:18,820
是link

50
00:01:18,820 --> 00:01:21,020
然后右侧的天气数据

51
00:01:21,020 --> 00:01:21,740
就是wiler

52
00:01:21,740 --> 00:01:23,720
这是主要包含三部分数据

53
00:01:23,720 --> 00:01:25,920
如果说我们一开始在做这个功能的时候

54
00:01:25,920 --> 00:01:27,500
有可能只有留言版的功能

55
00:01:27,500 --> 00:01:29,060
但是后来我们加了新功能

56
00:01:29,060 --> 00:01:29,720
比如说有信链接

57
00:01:29,720 --> 00:01:31,640
再后来我们又加了天气的功能

58
00:01:31,640 --> 00:01:32,680
所以说我们这个时候

59
00:01:32,680 --> 00:01:35,560
这个客户端可以按照我们的需求来查询数据

60
00:01:35,560 --> 00:01:37,320
这个我们可以直观的演示一下

61
00:01:37,320 --> 00:01:38,780
比如说我们在这个浏览器中

62
00:01:38,780 --> 00:01:39,800
可以先做一个测试

63
00:01:39,800 --> 00:01:42,820
如果说我们只需要留言版相关的数据

64
00:01:42,820 --> 00:01:43,800
我们是这样做的

65
00:01:43,800 --> 00:01:44,940
这里边要查询的是list

66
00:01:44,940 --> 00:01:46,480
它当中的有这个UderName

67
00:01:46,480 --> 00:01:48,340
还有是内容Content

68
00:01:48,340 --> 00:01:49,340
还有这个Data

69
00:01:49,340 --> 00:01:50,820
然后我们点查询

70
00:01:50,820 --> 00:01:53,440
这样得到的就是只有留言版相关的

71
00:01:53,440 --> 00:01:54,080
这个数据

72
00:01:54,080 --> 00:01:55,320
如果说后来呢

73
00:01:55,320 --> 00:01:56,140
我们又添加了一个新功能

74
00:01:56,140 --> 00:01:56,860
就是有情链接

75
00:01:56,860 --> 00:01:57,660
那这个时候呢

76
00:01:57,660 --> 00:01:58,600
我们就可以把对应的数据

77
00:01:58,600 --> 00:01:59,080
给查出来

78
00:01:59,080 --> 00:01:59,940
这就是link

79
00:01:59,940 --> 00:02:01,620
这里边有l

80
00:02:01,620 --> 00:02:02,420
name

81
00:02:02,420 --> 00:02:03,360
还有一个l

82
00:02:03,360 --> 00:02:03,820
url

83
00:02:03,820 --> 00:02:04,580
然后呢

84
00:02:04,580 --> 00:02:05,020
再点查询

85
00:02:05,020 --> 00:02:05,560
这样的话

86
00:02:05,560 --> 00:02:06,440
我们就多出一份数据

87
00:02:06,440 --> 00:02:07,460
好

88
00:02:07,460 --> 00:02:08,280
那如果说后来呢

89
00:02:08,280 --> 00:02:09,040
我们又增加了

90
00:02:09,040 --> 00:02:09,720
这个天气的功能

91
00:02:09,720 --> 00:02:10,520
那这个时候你就可以

92
00:02:10,520 --> 00:02:12,280
再查询天气的数据

93
00:02:12,280 --> 00:02:12,700
那就是

94
00:02:12,700 --> 00:02:13,740
weather

95
00:02:13,740 --> 00:02:15,140
这里边呢

96
00:02:15,140 --> 00:02:16,320
有一个wea

97
00:02:16,320 --> 00:02:17,060
wea

98
00:02:17,060 --> 00:02:17,740
这表示天气

99
00:02:17,740 --> 00:02:18,680
另外一个的话

100
00:02:18,680 --> 00:02:19,200
还有就是温度

101
00:02:19,200 --> 00:02:19,660
time

102
00:02:19,660 --> 00:02:20,360
time preacher

103
00:02:20,360 --> 00:02:21,100
然后呢

104
00:02:21,100 --> 00:02:21,500
点查询

105
00:02:21,500 --> 00:02:22,560
这样我们得到的

106
00:02:22,560 --> 00:02:23,620
就包含了天气的数据

107
00:02:23,620 --> 00:02:25,300
所以说这里边就体现了

108
00:02:25,300 --> 00:02:26,900
我们按需查询的这个特性

109
00:02:26,900 --> 00:02:28,520
这也是这个国务Q2

110
00:02:28,520 --> 00:02:29,200
它的一个原则

111
00:02:29,200 --> 00:02:31,180
这是关于数据层面

112
00:02:31,180 --> 00:02:32,440
我们先有一个整体的认识

113
00:02:32,440 --> 00:02:35,500
这是关于我们要做的

114
00:02:35,500 --> 00:02:36,000
流言本案例

115
00:02:36,000 --> 00:02:37,400
我们先熟悉到这里

