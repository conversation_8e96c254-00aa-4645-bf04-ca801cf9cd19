1
00:00:00,000 --> 00:00:02,760
好,这节课我们就来看一下多进程它的一个增强版

2
00:00:02,760 --> 00:00:05,080
那么这节课内容呢其实难度是稍微有点高的

3
00:00:05,080 --> 00:00:06,660
这里可能需要跟同学们去打一个预防针

4
00:00:06,660 --> 00:00:08,460
那么同学们如果说能够听懂当然最好

5
00:00:08,460 --> 00:00:10,060
如果说听不懂的话其实也没关系

6
00:00:10,060 --> 00:00:11,280
听不懂才是正常的

7
00:00:11,280 --> 00:00:12,020
那么可以呢

8
00:00:12,020 --> 00:00:15,440
接下来来去工作中积累一些经验之后再活过头来看来理解

9
00:00:15,440 --> 00:00:16,320
这样也是可以的

10
00:00:16,320 --> 00:00:17,900
好,那么我们来看一下

11
00:00:17,900 --> 00:00:20,020
在前面所讲解的多进程模型中

12
00:00:20,020 --> 00:00:22,900
适合agint进程的有一种常见的场景

13
00:00:22,900 --> 00:00:24,000
是什么同学们还记得吗

14
00:00:24,000 --> 00:00:26,040
是不是通过我们的课端和符端要建立常链接

15
00:00:26,040 --> 00:00:27,460
也就是咱们需要去设计到socket

16
00:00:27,460 --> 00:00:28,580
我们可以去使用agent

17
00:00:28,580 --> 00:00:29,900
那么如果说没有agent

18
00:00:29,900 --> 00:00:33,040
我们的多进度模型会导致n倍的连接被创建

19
00:00:33,040 --> 00:00:33,820
其实大家可以看到

20
00:00:33,820 --> 00:00:34,900
n层m个链接

21
00:00:34,900 --> 00:00:36,440
它其实复杂度是非常之高的

22
00:00:36,440 --> 00:00:37,200
那么我们就来看一下

23
00:00:37,200 --> 00:00:39,300
到底什么是图中了一个n层m个链接

24
00:00:39,300 --> 00:00:40,260
它会导致什么样的问题

25
00:00:40,260 --> 00:00:41,560
好

26
00:00:41,560 --> 00:00:43,320
那么我们就一起来看一下

27
00:00:43,320 --> 00:00:48,260
看一下这幅图

28
00:00:48,260 --> 00:00:49,500
这幅图我们来看一下

29
00:00:49,500 --> 00:00:51,060
那么其实呢

30
00:00:51,060 --> 00:00:52,580
大家可以看到我们是不是分为两块

31
00:00:52,580 --> 00:00:54,460
是个worker和我们的socket的服务器

32
00:00:54,460 --> 00:00:55,960
我们现在所讲的是不是没有进度

33
00:00:55,960 --> 00:00:57,760
也就是建立出信之前的一种处理方式

34
00:00:57,760 --> 00:00:59,040
如果说我们要进行长运接

35
00:00:59,040 --> 00:01:00,180
首先我们的应用程序

36
00:01:00,180 --> 00:01:01,160
假如说它有适合

37
00:01:01,160 --> 00:01:02,100
那么是不是会有四个worker

38
00:01:02,100 --> 00:01:02,480
对吧

39
00:01:02,480 --> 00:01:04,080
那么有四个worker

40
00:01:04,080 --> 00:01:05,080
四个worker代表什么呢

41
00:01:05,080 --> 00:01:05,620
它代表什么

42
00:01:05,620 --> 00:01:07,580
代表我们的一个应用程序

43
00:01:07,580 --> 00:01:07,880
对吧

44
00:01:07,880 --> 00:01:10,420
代表我们的应用程序

45
00:01:10,420 --> 00:01:10,900
好

46
00:01:10,900 --> 00:01:11,580
那么呢

47
00:01:11,580 --> 00:01:12,200
Socket的服务器

48
00:01:12,200 --> 00:01:13,480
是不是代表我们通用的服务

49
00:01:13,480 --> 00:01:14,740
它指的是什么呢

50
00:01:14,740 --> 00:01:16,100
Socket的服务器指的

51
00:01:16,100 --> 00:01:17,480
是不是我们自己写的EG的代码

52
00:01:17,480 --> 00:01:18,060
其实不是的

53
00:01:18,060 --> 00:01:18,380
它是它

54
00:01:18,380 --> 00:01:19,720
它是远程的一种Socket的服务器

55
00:01:19,720 --> 00:01:20,380
因为我们Socket

56
00:01:20,380 --> 00:01:21,900
往往会单独拿出来做一个服务

57
00:01:21,900 --> 00:01:22,460
比如说呢

58
00:01:22,460 --> 00:01:22,920
阿里民

59
00:01:22,920 --> 00:01:25,480
比如说阿里民也会提供一些Socket服务

60
00:01:25,480 --> 00:01:27,380
那么我们的应用程序是不是会连接它

61
00:01:27,380 --> 00:01:28,480
那么我们此时

62
00:01:28,480 --> 00:01:29,600
比如说我们的服务器是适合

63
00:01:29,600 --> 00:01:30,160
对吧

64
00:01:30,160 --> 00:01:31,360
应用程序代表我们自己的服务器

65
00:01:31,360 --> 00:01:31,960
假如说适合

66
00:01:31,960 --> 00:01:33,480
那么它和阿里民会进行几个连接

67
00:01:33,480 --> 00:01:35,100
他们之间会进行几个连接

68
00:01:35,100 --> 00:01:35,820
同学们

69
00:01:35,820 --> 00:01:37,700
他们是不是会进行四个连接

70
00:01:37,700 --> 00:01:38,120
为什么呀

71
00:01:38,120 --> 00:01:39,000
我们是不是有四个Worker

72
00:01:39,000 --> 00:01:42,080
那么每一个Worker里面都会调用一个Socket的连接方法

73
00:01:42,080 --> 00:01:42,420
Connect

74
00:01:42,420 --> 00:01:43,740
和我们的Socket服务器进行连接

75
00:01:43,740 --> 00:01:44,400
因为呢

76
00:01:44,400 --> 00:01:45,720
我们去处理用户请求的时候

77
00:01:45,720 --> 00:01:46,140
对吧

78
00:01:46,140 --> 00:01:48,140
因为我们的用户请求

79
00:01:48,140 --> 00:01:49,240
是不是可能会进入他

80
00:01:49,240 --> 00:01:50,080
进入他

81
00:01:50,080 --> 00:01:50,460
进入他

82
00:01:50,460 --> 00:01:51,340
对吧

83
00:01:51,340 --> 00:01:52,120
因为我们有四个进程

84
00:01:52,120 --> 00:01:53,040
去处理我们的用户请求

85
00:01:53,040 --> 00:01:54,220
那么我们四个进程

86
00:01:54,220 --> 00:01:55,060
是不是都要和我们的

87
00:01:55,060 --> 00:01:55,580
阿里民服务器

88
00:01:55,580 --> 00:01:56,060
去进行一个

89
00:01:56,060 --> 00:01:56,620
所有可能链接

90
00:01:56,620 --> 00:01:57,460
对吧

91
00:01:57,460 --> 00:01:58,220
那么这样会造成

92
00:01:58,220 --> 00:01:59,000
什么问题啊

93
00:01:59,000 --> 00:01:59,620
同学们

94
00:01:59,620 --> 00:02:00,240
是不是我们

95
00:02:00,240 --> 00:02:00,740
比如说

96
00:02:00,740 --> 00:02:01,800
我们此时有一个应用

97
00:02:01,800 --> 00:02:02,900
就需要让阿里民造成

98
00:02:02,900 --> 00:02:03,700
进行几个

99
00:02:03,700 --> 00:02:04,200
是不是进行

100
00:02:04,200 --> 00:02:04,840
四十链接

101
00:02:04,840 --> 00:02:06,060
也就是需要有四个

102
00:02:06,060 --> 00:02:07,480
四个长链接的通道

103
00:02:07,480 --> 00:02:08,620
那么其实我们是不是

104
00:02:08,620 --> 00:02:10,060
会有很多的服务器

105
00:02:10,060 --> 00:02:11,440
就是我们会有很多的服务

106
00:02:11,440 --> 00:02:12,580
和我们的阿里民去相连了

107
00:02:12,580 --> 00:02:15,800
你比如说我的服务会和阿里民进行试验链接

108
00:02:15,800 --> 00:02:17,900
那么你的服务可能会进行八个链接

109
00:02:17,900 --> 00:02:19,860
还有一些其他的第三方的服务

110
00:02:19,860 --> 00:02:22,420
比如说你的一个16核可能会进行16核链接

111
00:02:22,420 --> 00:02:24,300
那么这里就会造成一个很严重的问题

112
00:02:24,300 --> 00:02:24,540
是什么

113
00:02:24,540 --> 00:02:26,540
是不是会咱们的链接数量会非常的庞大

114
00:02:26,540 --> 00:02:29,140
也就是造成咱们讲义里面的一个什么问题

115
00:02:29,140 --> 00:02:31,040
是不是n乘以m这样的一个问题

116
00:02:31,040 --> 00:02:35,140
那么什么是n什么是m呢

117
00:02:35,140 --> 00:02:36,280
我们来解释一下

118
00:02:36,280 --> 00:02:38,460
多进程模型会导致n倍的链接被创建

119
00:02:38,460 --> 00:02:40,620
n是不是代表你的CPU的一个核数

120
00:02:40,620 --> 00:02:41,460
对吧

121
00:02:41,460 --> 00:02:42,360
那么M呢

122
00:02:42,360 --> 00:02:44,140
M是不是代表

123
00:02:44,140 --> 00:02:45,180
我们的阿里云服务器

124
00:02:45,180 --> 00:02:46,780
需要处理多少个服务

125
00:02:46,780 --> 00:02:47,740
你比如说和我相连

126
00:02:47,740 --> 00:02:49,160
它是需要有一个sever

127
00:02:49,160 --> 00:02:50,040
和它相连呢

128
00:02:50,040 --> 00:02:50,760
有另外一个sever

129
00:02:50,760 --> 00:02:51,240
对吧

130
00:02:51,240 --> 00:02:52,140
所以就造成一个

131
00:02:52,140 --> 00:02:53,480
N乘以M这样的一个问题

132
00:02:53,480 --> 00:02:54,800
因为我们的长链接

133
00:02:54,800 --> 00:02:56,180
因为长链接

134
00:02:56,180 --> 00:02:57,420
它是非常宝贵的

135
00:02:57,420 --> 00:02:57,900
大家去看

136
00:02:57,900 --> 00:02:59,420
长链接是对服务器的损耗

137
00:02:59,420 --> 00:03:00,060
是非常之大的

138
00:03:00,060 --> 00:03:01,300
所以我们后来

139
00:03:01,300 --> 00:03:02,780
怎么样去改进它

140
00:03:02,780 --> 00:03:03,900
我们后来是不是

141
00:03:03,900 --> 00:03:05,720
EGG里面去单独创建了一个

142
00:03:05,720 --> 00:03:06,740
精彩在那个进程

143
00:03:06,740 --> 00:03:07,700
去做这样的一件事情

144
00:03:07,700 --> 00:03:09,080
那么我们这里来画一下

145
00:03:09,080 --> 00:03:09,960
精彩在那个模型

146
00:03:10,620 --> 00:03:12,420
这样一种情况会造成一个什么样的问题

147
00:03:12,420 --> 00:03:14,460
是不是会造成咱们那个n*m的问题

148
00:03:14,460 --> 00:03:15,740
好 我们来

149
00:03:15,740 --> 00:03:19,580
n*m的问题

150
00:03:19,580 --> 00:03:21,880
好 那么我们来看一下引入agint之后

151
00:03:21,880 --> 00:03:23,680
是什么那个情况

152
00:03:23,680 --> 00:03:31,360
好 那么首先呢

153
00:03:31,360 --> 00:03:33,660
此时我们是不是会有一个

154
00:03:33,660 --> 00:03:34,940
agint 对吧

155
00:03:34,940 --> 00:03:36,480
agint

156
00:03:36,480 --> 00:03:39,800
我们的通信是谁跟谁通信

157
00:03:40,320 --> 00:03:42,360
我们的通信是不是agint和我们呢

158
00:03:42,360 --> 00:04:01,060
我们的服务是不是是什么呀

159
00:04:01,060 --> 00:04:04,640
我们的服务是不是我们的agint和我们sokin的服务器去连接

160
00:04:04,640 --> 00:04:06,680
那么我们怎么样去通信呢

161
00:04:06,680 --> 00:04:07,460
怎么样去通信

162
00:04:07,460 --> 00:04:10,020
你比如说

163
00:04:11,080 --> 00:04:12,620
我们是不是会有很多的一个worker

164
00:04:12,620 --> 00:04:14,680
我们之前是不是其实画过这样一个图

165
00:04:14,680 --> 00:04:18,520
这里呢我就不画太多了

166
00:04:18,520 --> 00:04:20,300
首先呢我们的进程里面是不是有一个agint

167
00:04:20,300 --> 00:04:22,860
有很多的一些worker对吧

168
00:04:22,860 --> 00:04:23,640
然后呢

169
00:04:23,640 --> 00:04:25,160
还有一个什么是不是还有一个

170
00:04:25,160 --> 00:04:26,440
master呀同学们

171
00:04:26,440 --> 00:04:31,320
好那么这里呢我们agint跟socket的服务器啊有一个

172
00:04:31,320 --> 00:04:35,160
我们agint跟咱们socket的服务器

173
00:04:35,160 --> 00:04:35,920
相连

174
00:04:35,920 --> 00:04:37,960
他们呢进行一个是不是进行我们的socket的通信

175
00:04:37,960 --> 00:04:40,280
好那么agint呢得到咱们一个通讯结果之后

176
00:04:40,320 --> 00:04:42,620
是不是需要和我们Walker进程之间去进行通信

177
00:04:42,620 --> 00:04:44,880
那么它怎么和我们三个Walker进行通信呢

178
00:04:44,880 --> 00:04:45,800
怎么样去做

179
00:04:45,800 --> 00:04:47,680
是不是通过Master的一些转发

180
00:04:47,680 --> 00:04:49,500
那么这样其实会造成一个问题

181
00:04:49,500 --> 00:04:51,100
造成一个什么问题呢

182
00:04:51,100 --> 00:04:53,100
那么我们来看讲义

183
00:04:53,100 --> 00:04:55,160
那么大家可以看到

184
00:04:55,160 --> 00:04:58,920
我们会把它放到进程进程里面去维护

185
00:04:58,920 --> 00:05:00,180
然后通过Message

186
00:05:00,180 --> 00:05:01,260
Message我们之前是不是讲过

187
00:05:01,260 --> 00:05:04,980
它可以去帮助我们是不是去监听一些数据去进行发并月

188
00:05:04,980 --> 00:05:06,840
那么这种做法其实是可行的

189
00:05:06,840 --> 00:05:09,440
但是往往需要写大量的代码去封装接口

190
00:05:09,440 --> 00:05:10,620
和实现数据的传递

191
00:05:10,620 --> 00:05:11,360
非常的麻烦

192
00:05:11,360 --> 00:05:12,320
它的麻烦指什么

193
00:05:12,320 --> 00:05:13,060
是不是我们的进程

194
00:05:13,060 --> 00:05:14,260
和咱们的worker之间的数据通信

195
00:05:14,260 --> 00:05:15,100
非常麻烦

196
00:05:15,100 --> 00:05:15,700
哪里麻烦呢

197
00:05:15,700 --> 00:05:17,420
它是不是都要通过master去转发

198
00:05:17,420 --> 00:05:18,520
那么这里会造成一个问题

199
00:05:18,520 --> 00:05:20,260
首先你通过master去传递

200
00:05:20,260 --> 00:05:22,100
数据的效率是比较低的

201
00:05:22,100 --> 00:05:22,680
然后呢

202
00:05:22,680 --> 00:05:24,140
它都需要master去做中转

203
00:05:24,140 --> 00:05:24,480
对吧

204
00:05:24,480 --> 00:05:25,660
因为我们构成的桥梁就是master

205
00:05:25,660 --> 00:05:27,440
那么万一你的IPC通道

206
00:05:27,440 --> 00:05:29,420
IPC通道就是咱们进程间通信的通道

207
00:05:29,420 --> 00:05:30,060
那么如果说

208
00:05:30,060 --> 00:05:31,480
你去传递一些错误的数据

209
00:05:31,480 --> 00:05:33,440
把咱们的master给搞挂了

210
00:05:33,440 --> 00:05:35,140
那么master搞挂是不是非常严重

211
00:05:35,140 --> 00:05:37,440
因为你所有的Jint Walker都是Master去管理的

212
00:05:37,440 --> 00:05:38,560
那么如果说Master挂掉了

213
00:05:38,560 --> 00:05:40,340
那你是不是就整个服气就崩了呀

214
00:05:40,340 --> 00:05:41,780
所以它是非常不稳定的

215
00:05:41,780 --> 00:05:43,400
所以我们提供了一种新的模式

216
00:05:43,400 --> 00:05:45,340
来降低这类客户端封装的复杂度

217
00:05:45,340 --> 00:05:47,880
通过建立Jint和Walker的Socket

218
00:05:47,880 --> 00:05:49,260
直接跳过Master的中转

219
00:05:49,260 --> 00:05:50,820
那么Jint作为对外的门面

220
00:05:50,820 --> 00:05:53,380
维护Docker为Walker进程的共享链接

221
00:05:53,380 --> 00:05:53,720
什么意思

222
00:05:53,720 --> 00:05:56,600
那么这里我们还是画一幅图

223
00:05:56,600 --> 00:05:58,920
好 我们刚才所讲解的

224
00:05:58,920 --> 00:05:59,940
这里是通过我们Jint

225
00:05:59,940 --> 00:06:01,220
对吧 通过Master去转发

226
00:06:01,220 --> 00:06:03,560
好 我们来稍微标注一下

227
00:06:03,560 --> 00:06:07,140
通过了Master转发

228
00:06:07,140 --> 00:06:09,000
好 那么我们来看一下有没有一种什么

229
00:06:09,000 --> 00:06:12,840
看一下有没有一种什么新的模式

230
00:06:12,840 --> 00:06:14,560
首先我们来选中

231
00:06:14,560 --> 00:06:28,340
好 首先我们这里有一点不变的是什么呢

232
00:06:28,340 --> 00:06:31,660
有一点不变我们的Agent还是和我们Socket的福气相连

233
00:06:31,660 --> 00:06:33,220
那么此时可能就

234
00:06:33,220 --> 00:06:35,460
我们刚才所产生的问题核心是什么

235
00:06:35,460 --> 00:06:37,000
是不是需要master去做转发

236
00:06:37,000 --> 00:06:40,600
那么这里我们就不用master去做转发

237
00:06:40,600 --> 00:06:41,460
首先把它给丢到一边

238
00:06:41,460 --> 00:06:42,700
这里新方案的核心

239
00:06:42,700 --> 00:06:43,740
就是把master给去掉

240
00:06:43,740 --> 00:06:44,920
不需要它去做转发

241
00:06:44,920 --> 00:06:46,260
那么怎么样去做呢

242
00:06:46,260 --> 00:06:48,080
其实这套方案的核心就是通过

243
00:06:48,080 --> 00:06:50,360
通过咱们的进尺

244
00:06:50,360 --> 00:06:52,180
和咱们的walker之间

245
00:06:52,180 --> 00:06:52,900
去建立一个什么

246
00:06:52,900 --> 00:06:54,520
建立一个什么

247
00:06:54,520 --> 00:06:55,420
建立一个socket

248
00:06:55,420 --> 00:06:56,980
链接

249
00:06:56,980 --> 00:06:58,480
那么这套方案它的核心就是

250
00:06:58,480 --> 00:06:59,300
你比如说

251
00:06:59,300 --> 00:06:59,820
我们的

252
00:06:59,820 --> 00:07:03,600
我们的agent和咱们的socket的服务器

253
00:07:03,600 --> 00:07:05,040
那么socket的服务器代表谁啊

254
00:07:05,040 --> 00:07:05,500
它代表谁

255
00:07:05,500 --> 00:07:06,440
它是不是就代表我们一个

256
00:07:06,440 --> 00:07:07,140
阿里云

257
00:07:07,140 --> 00:07:08,820
也就是一个巨大的socket的服务器

258
00:07:08,820 --> 00:07:10,960
那么我们只通过agent和咱们的一个

259
00:07:10,960 --> 00:07:11,640
阿里云相连

260
00:07:11,640 --> 00:07:13,460
是不是保证了我们每一个应用程序

261
00:07:13,460 --> 00:07:14,900
和咱们的云服务器

262
00:07:14,900 --> 00:07:15,680
只有一条连接通道

263
00:07:15,680 --> 00:07:17,460
这里就不会造成nxm的一个问题

264
00:07:17,460 --> 00:07:19,000
那么另外一个问题是什么呀

265
00:07:19,000 --> 00:07:20,340
另外一个问题是不是我们的master

266
00:07:20,340 --> 00:07:21,600
master需要去做转发

267
00:07:21,600 --> 00:07:22,280
那么我们怎么解决呢

268
00:07:22,280 --> 00:07:25,040
其实解决方案就是通过agent和我们的worker之间

269
00:07:25,040 --> 00:07:25,820
创建一个什么呢

270
00:07:25,820 --> 00:07:27,880
agent和worker

271
00:07:27,880 --> 00:07:32,760
创建本地的

272
00:07:32,760 --> 00:07:34,880
说可以链接

273
00:07:34,880 --> 00:07:36,960
通信

274
00:07:36,960 --> 00:07:39,320
什么意思

275
00:07:39,320 --> 00:07:41,900
我们刚才说

276
00:07:41,900 --> 00:07:42,860
造成一个问题

277
00:07:42,860 --> 00:07:44,980
就是agint通过master去转发和worker之间通信

278
00:07:44,980 --> 00:07:46,600
那么他们之间的通信效率不高

279
00:07:46,600 --> 00:07:48,480
而且会导致我们的master去挂掉

280
00:07:48,480 --> 00:07:50,960
那么此时我们想一种办法是什么呢

281
00:07:50,960 --> 00:07:52,440
把agint和worker之间

282
00:07:52,440 --> 00:07:53,720
我们在应用程序之内

283
00:07:53,720 --> 00:07:55,120
我们创建一个自己的说给的通道

284
00:07:55,120 --> 00:07:55,680
那么这样

285
00:07:55,680 --> 00:07:56,800
我们的agint和worker

286
00:07:56,800 --> 00:07:57,780
他们之间是不是就可以

287
00:07:57,780 --> 00:07:59,300
关联起来了

288
00:07:59,300 --> 00:07:59,620
对吧

289
00:07:59,620 --> 00:08:00,280
通过Socket

290
00:08:00,280 --> 00:08:01,420
通过他们自己的Socket

291
00:08:01,420 --> 00:08:02,560
我们呢

292
00:08:02,560 --> 00:08:03,860
不通过阿里云去创建

293
00:08:03,860 --> 00:08:04,340
对吧

294
00:08:04,340 --> 00:08:05,300
我们自己来一个

295
00:08:05,300 --> 00:08:07,160
内部闭环的一个Socket的链接

296
00:08:07,160 --> 00:08:08,060
这样是不是不会造成

297
00:08:08,060 --> 00:08:09,240
我们阿里云服务器的资源浪费

298
00:08:09,240 --> 00:08:10,160
那么这里呢

299
00:08:10,160 --> 00:08:10,920
就解决了这个问题

300
00:08:10,920 --> 00:08:11,780
好

301
00:08:11,780 --> 00:08:12,280
我们来看一下

302
00:08:12,280 --> 00:08:13,040
多进程

303
00:08:13,040 --> 00:08:14,720
增强

304
00:08:14,720 --> 00:08:15,460
那么这里呢

305
00:08:15,460 --> 00:08:16,180
我们是不是首先呢

306
00:08:16,180 --> 00:08:17,240
介绍了三种和服务器

307
00:08:17,240 --> 00:08:18,640
进行Socket的链接方法

308
00:08:18,640 --> 00:08:18,940
对吧

309
00:08:18,940 --> 00:08:19,820
那么第一种

310
00:08:19,820 --> 00:08:20,940
是不是就是我们的Worker

311
00:08:20,940 --> 00:08:22,780
每一个Worker都和

312
00:08:22,780 --> 00:08:23,840
都和我们的一个服务器

313
00:08:23,840 --> 00:08:24,980
建立链接啊

314
00:08:24,980 --> 00:08:25,180
是吧

315
00:08:25,180 --> 00:08:29,740
每一个Worker都和咱们的Socket

316
00:08:29,740 --> 00:08:34,440
服务器进行链接

317
00:08:34,440 --> 00:08:36,620
是不是会造成N层M的问题

318
00:08:36,620 --> 00:08:39,320
造成N层M的问题

319
00:08:39,320 --> 00:08:41,300
那么第二种解决方案是什么呢

320
00:08:41,300 --> 00:08:43,200
进层链接

321
00:08:43,200 --> 00:08:44,080
但是呢

322
00:08:44,080 --> 00:08:46,300
进层和是不是和Worker之间的通信

323
00:08:46,300 --> 00:08:48,220
需要谁转发

324
00:08:48,220 --> 00:08:48,820
需要master

325
00:08:48,820 --> 00:08:52,260
master转发

326
00:08:52,260 --> 00:08:53,360
它会造成什么问题

327
00:08:53,360 --> 00:08:54,480
是不是效率低

328
00:08:54,480 --> 00:08:56,420
不稳定了

329
00:08:56,420 --> 00:08:58,760
那么第三种方案是什么

330
00:08:58,760 --> 00:09:01,140
是不是还是Jint和我们的

331
00:09:01,140 --> 00:09:01,820
Sockets

332
00:09:01,820 --> 00:09:04,060
服务连接

333
00:09:04,060 --> 00:09:04,900
但是呢

334
00:09:04,900 --> 00:09:08,300
但是我们的Jint是不是和Walker之间会创建一个本地的Sockets通道

335
00:09:08,300 --> 00:09:10,360
但是Jint和Walker

336
00:09:10,360 --> 00:09:16,140
会创建本地Sockets通道

337
00:09:16,140 --> 00:09:17,960
那么这里就是我们这几个的内容

