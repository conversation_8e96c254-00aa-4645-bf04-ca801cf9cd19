1
00:00:00,000 --> 00:00:03,700
好,我们这节课的主要内容就是来看一下

2
00:00:03,700 --> 00:00:06,560
我们到底怎么样去选择多进程还是多线程

3
00:00:06,560 --> 00:00:08,100
首先同学们注意

4
00:00:08,100 --> 00:00:11,400
千万不要限于一种非此即彼的误区

5
00:00:11,400 --> 00:00:14,320
多线程和多进程它要结合起来使用

6
00:00:14,320 --> 00:00:16,400
是不是就和咱们选择read和view是一样的呀

7
00:00:16,400 --> 00:00:17,880
千万不要你死我活

8
00:00:17,880 --> 00:00:20,220
read和view它是不是各有千秋

9
00:00:20,220 --> 00:00:22,000
我们一定要根据项目去选择

10
00:00:22,000 --> 00:00:24,400
好,这里来看一下他们之间的对比

11
00:00:24,400 --> 00:00:25,660
这里为了防止大家看不见

12
00:00:25,660 --> 00:00:27,900
我们来个大图看一下

13
00:00:28,400 --> 00:00:30,340
首先来看一下多进程和多线程

14
00:00:30,340 --> 00:00:32,660
它在数据共享同步方面的一个区别

15
00:00:32,660 --> 00:00:36,900
那么在数据共享同步方面

16
00:00:36,900 --> 00:00:38,620
多进程它是比较复杂的

17
00:00:38,620 --> 00:00:40,760
然后呢需要使用IPC通信协议

18
00:00:40,760 --> 00:00:42,040
IPC通信协议是什么呢

19
00:00:42,040 --> 00:00:42,620
是咱们计算机

20
00:00:42,620 --> 00:00:45,020
所谓的多进程语言都会去使用一种

21
00:00:45,020 --> 00:00:46,180
多进程的通信方法

22
00:00:46,180 --> 00:00:47,260
老师的后面会去介绍

23
00:00:47,260 --> 00:00:51,000
那么进程它的数据共享和同步

24
00:00:51,000 --> 00:00:52,040
关键字就是复杂

25
00:00:52,040 --> 00:00:53,460
复杂

26
00:00:53,460 --> 00:00:55,320
好那我们来看一下线程

27
00:00:55,320 --> 00:00:57,740
线程之间的数据共享

28
00:00:57,740 --> 00:00:58,360
简单

29
00:00:58,360 --> 00:01:00,080
为什么呀

30
00:01:00,080 --> 00:01:02,860
我们浏览器里面是不是分为渲染线程和js线程

31
00:01:02,860 --> 00:01:04,720
那么DOM是不是属于渲染线程

32
00:01:04,720 --> 00:01:06,840
咱们js是不是经常去操作DOM

33
00:01:06,840 --> 00:01:07,840
说明什么问题

34
00:01:07,840 --> 00:01:09,200
它们之间的共享其实比较简单

35
00:01:09,200 --> 00:01:11,740
那么但是呢又导致一个原因

36
00:01:11,740 --> 00:01:12,760
让它很复杂

37
00:01:12,760 --> 00:01:13,920
为什么呀

38
00:01:13,920 --> 00:01:15,640
是不是因为我们浏览器很复杂之后

39
00:01:15,640 --> 00:01:16,420
导致js和DOM

40
00:01:16,420 --> 00:01:17,380
它们之间的通信

41
00:01:17,380 --> 00:01:19,720
它们之间的状态不是很好管理

42
00:01:19,720 --> 00:01:21,620
也就是我们为什么需要RECT需要REDUCT

43
00:01:21,620 --> 00:01:22,340
它的一个原因

44
00:01:22,340 --> 00:01:23,320
所以说总结来看

45
00:01:23,320 --> 00:01:24,140
各有优势

46
00:01:24,140 --> 00:01:25,160
它们各有千秋

47
00:01:25,160 --> 00:01:26,700
那咱们再来看一下

48
00:01:26,700 --> 00:01:27,840
内存和CPU方面

49
00:01:27,840 --> 00:01:30,560
其实这里就是比较好理解

50
00:01:30,560 --> 00:01:31,820
多进程

51
00:01:31,820 --> 00:01:32,740
咱们一个进程

52
00:01:32,740 --> 00:01:34,020
在浏览器里面是代表一个窗口

53
00:01:34,020 --> 00:01:34,800
你开很多的窗口

54
00:01:34,800 --> 00:01:36,900
是不是内存就会占有很多

55
00:01:36,900 --> 00:01:37,740
而且在窗口之间

56
00:01:37,740 --> 00:01:39,280
之间是没有什么关系的

57
00:01:39,280 --> 00:01:40,020
所以说他们切换

58
00:01:40,020 --> 00:01:40,960
复杂CPU66低

59
00:01:40,960 --> 00:01:42,940
在线程里面

60
00:01:42,940 --> 00:01:44,500
线程是不是很轻量啊

61
00:01:44,500 --> 00:01:45,080
这也是

62
00:01:45,080 --> 00:01:46,740
他们进去切换就比较简单

63
00:01:46,740 --> 00:01:47,420
占有内存也少

64
00:01:47,420 --> 00:01:49,220
所以在内存CPU方面

65
00:01:49,220 --> 00:01:50,180
线程是占优的

66
00:01:50,180 --> 00:01:51,160
好

67
00:01:51,160 --> 00:01:51,920
我们再来看一下

68
00:01:51,920 --> 00:01:53,460
创建销毁切换

69
00:01:53,460 --> 00:01:58,440
好 这里其实也比较好理解

70
00:01:58,440 --> 00:02:00,500
咱们的进程是不是比较重

71
00:02:00,500 --> 00:02:02,360
所以它的速度慢 切换复杂

72
00:02:02,360 --> 00:02:03,840
那么线程比较轻量

73
00:02:03,840 --> 00:02:05,120
它的创建销毁切换简单

74
00:02:05,120 --> 00:02:05,740
所以速度很快

75
00:02:05,740 --> 00:02:07,040
那么这里也是线程占优

76
00:02:07,040 --> 00:02:08,040
那么我们再来看一下

77
00:02:08,040 --> 00:02:09,720
它的一个编程调试方面

78
00:02:09,720 --> 00:02:11,960
之前咱们是不是讲到了

79
00:02:11,960 --> 00:02:13,300
我们是不是讲到了

80
00:02:13,300 --> 00:02:17,620
进程之间它是隔离的

81
00:02:17,620 --> 00:02:18,220
没有什么关系

82
00:02:18,220 --> 00:02:19,680
所以它的编程会简单一些

83
00:02:19,680 --> 00:02:20,880
包括调试也会更简单

84
00:02:20,880 --> 00:02:21,760
那么同样的

85
00:02:21,760 --> 00:02:23,000
线程之间它涉及到通信

86
00:02:23,000 --> 00:02:24,020
所以它的编程复杂

87
00:02:24,020 --> 00:02:24,520
调述复杂

88
00:02:24,520 --> 00:02:25,820
所以这里进程占优

89
00:02:25,820 --> 00:02:27,960
可靠性也很好理解

90
00:02:27,960 --> 00:02:29,360
进程是隔离的

91
00:02:29,360 --> 00:02:30,000
所以它更可靠

92
00:02:30,000 --> 00:02:31,720
那么线程之间会有偶合

93
00:02:31,720 --> 00:02:32,680
之前我们是不是讲过

94
00:02:32,680 --> 00:02:33,260
一个线程挂掉

95
00:02:33,260 --> 00:02:34,160
会导致整个进程的挂掉

96
00:02:34,160 --> 00:02:35,460
所以这里稳定性方面

97
00:02:35,460 --> 00:02:36,140
是进程占优

98
00:02:36,140 --> 00:02:37,780
咱们再来看一下扩展性

99
00:02:37,780 --> 00:02:38,500
也就是分布式

100
00:02:38,500 --> 00:02:39,520
因为进程之间

101
00:02:39,520 --> 00:02:40,180
它没什么关系

102
00:02:40,180 --> 00:02:41,440
比如说我们起一个漏的DS

103
00:02:41,440 --> 00:02:43,120
起一个漏的HTTP服务

104
00:02:43,120 --> 00:02:44,280
咱们的HTTP服务

105
00:02:44,280 --> 00:02:45,480
如果说服务器扛不住了

106
00:02:45,480 --> 00:02:46,420
咱们只需要再起一个服务

107
00:02:46,420 --> 00:02:47,840
其很多的进程就可以了

108
00:02:47,840 --> 00:02:49,540
所以它这个扩展性更好进程

109
00:02:49,540 --> 00:02:50,380
那么多线程

110
00:02:50,380 --> 00:02:52,560
它这个扩展性可能就不如进程

111
00:02:52,560 --> 00:02:53,160
为什么呀

112
00:02:53,160 --> 00:02:54,080
因为进程是隔离的

113
00:02:54,080 --> 00:02:55,280
但是线程它错综复杂

114
00:02:55,280 --> 00:02:56,420
它们之间有些偶合关系

115
00:02:56,420 --> 00:02:58,020
你需要先去把它解开再去扩展

116
00:02:58,020 --> 00:02:59,700
所以这里也是进程占优

117
00:02:59,700 --> 00:03:02,920
好

118
00:03:02,920 --> 00:03:05,860
我们来看一下它的特点

119
00:03:05,860 --> 00:03:07,440
我们来看一下

120
00:03:07,440 --> 00:03:08,840
在一些应用场景中

121
00:03:08,840 --> 00:03:10,560
怎么样去选择一些实际的业务场景中

122
00:03:10,560 --> 00:03:12,960
首先需要频繁创建销毁的

123
00:03:12,960 --> 00:03:14,500
应用优先选择

124
00:03:14,500 --> 00:03:15,240
需要线程

125
00:03:15,240 --> 00:03:16,320
比如说

126
00:03:16,320 --> 00:03:17,720
我们常见的应用就是web服务器

127
00:03:17,720 --> 00:03:19,940
你来那个连接就断掉断了就销毁

128
00:03:19,940 --> 00:03:21,520
但是如果用进程你就创建销毁

129
00:03:21,520 --> 00:03:23,620
是不是这样一个过程会比较的笨重

130
00:03:23,620 --> 00:03:24,960
现成它的特点是什么

131
00:03:24,960 --> 00:03:25,740
轻量快

132
00:03:25,740 --> 00:03:27,320
所以你需要频繁的创建销毁

133
00:03:27,320 --> 00:03:28,220
就需要用现成

134
00:03:28,220 --> 00:03:30,320
是不是就有点类似于咱们在view中的

135
00:03:30,320 --> 00:03:33,220
一副秀和一副

136
00:03:33,220 --> 00:03:37,460
就比如说咱们div里面的display hidden和display

137
00:03:37,460 --> 00:03:39,060
对吧

138
00:03:39,060 --> 00:03:39,900
visible hidden和display hidden

139
00:03:39,900 --> 00:03:41,540
包括view里面有一个api

140
00:03:41,540 --> 00:03:43,420
在文档里面其实写的很清楚

141
00:03:43,420 --> 00:03:44,440
不知道大家有没有印象

142
00:03:44,440 --> 00:03:45,900
其实有点类似

143
00:03:45,900 --> 00:03:50,980
第二个需要进行大量计算的优先使用线程

144
00:03:50,980 --> 00:03:52,560
为什么呀

145
00:03:52,560 --> 00:03:54,380
因为大量计算需要消耗CPU

146
00:03:54,380 --> 00:03:55,580
那么切换就很频繁

147
00:03:55,580 --> 00:03:56,820
你CPU切换频繁

148
00:03:56,820 --> 00:03:59,140
是不是线程更大优势

149
00:03:59,140 --> 00:04:00,020
因为线程它更轻量

150
00:04:00,020 --> 00:04:01,060
切换的代价更小

151
00:04:01,060 --> 00:04:01,620
所以说呢

152
00:04:01,620 --> 00:04:03,140
大量计算优先使用线程

153
00:04:03,140 --> 00:04:04,860
那么第三个

154
00:04:04,860 --> 00:04:06,180
咱们强相关的业务场景

155
00:04:06,180 --> 00:04:09,400
使用线程若相关的使用进程

156
00:04:09,400 --> 00:04:10,280
怎么样理解

157
00:04:10,280 --> 00:04:13,300
强相关的使用线程

158
00:04:13,300 --> 00:04:15,380
咱们DS它和Dome是不是强相关

159
00:04:15,380 --> 00:04:16,460
所以它们之间是现成关系

160
00:04:16,460 --> 00:04:17,140
那么若相关

161
00:04:17,140 --> 00:04:18,700
什么是若相关

162
00:04:18,700 --> 00:04:21,000
咱们浏览器的两个窗口之间

163
00:04:21,000 --> 00:04:21,720
是不是若相关呢

164
00:04:21,720 --> 00:04:22,780
咱们打开一个百度

165
00:04:22,780 --> 00:04:23,380
和打开一个淘宝

166
00:04:23,380 --> 00:04:24,260
它们之间没什么关系吧

167
00:04:24,260 --> 00:04:26,380
所以这种关系属于进程

168
00:04:26,380 --> 00:04:27,220
这是浏览器里面

169
00:04:27,220 --> 00:04:27,980
那么我们来看一下

170
00:04:27,980 --> 00:04:29,140
在服务端里面怎么去理解

171
00:04:29,140 --> 00:04:33,540
一般来讲咱们的服务里面

172
00:04:33,540 --> 00:04:34,060
需要去处理

173
00:04:34,060 --> 00:04:35,460
消息收发和消息处理

174
00:04:35,460 --> 00:04:36,920
那么消息收发和消息处理

175
00:04:36,920 --> 00:04:37,820
就是若相关的任务

176
00:04:37,820 --> 00:04:38,600
为什么呀

177
00:04:38,600 --> 00:04:39,520
因为我可以搜

178
00:04:39,520 --> 00:04:40,320
也可以发

179
00:04:40,320 --> 00:04:41,960
但是这些收发的消息

180
00:04:41,960 --> 00:04:42,720
我是不是可以不处理

181
00:04:42,720 --> 00:04:43,460
我爱处理就处理

182
00:04:43,460 --> 00:04:44,420
我不爱处理就不处理

183
00:04:44,420 --> 00:04:45,620
所以他们是弱相关的

184
00:04:45,620 --> 00:04:46,940
而消息处理里面

185
00:04:46,940 --> 00:04:48,400
就分为消息解码和业务处理

186
00:04:48,400 --> 00:04:49,480
那么这两个任务

187
00:04:49,480 --> 00:04:51,740
它的一个相关性就比较强

188
00:04:51,740 --> 00:04:52,580
为什么呀

189
00:04:52,580 --> 00:04:54,440
因为我们想进行业务处理

190
00:04:54,440 --> 00:04:55,580
你必须进行消息解码

191
00:04:55,580 --> 00:04:58,240
就比如说我们两个人在对话

192
00:04:58,240 --> 00:04:59,060
你跟我说英语

193
00:04:59,060 --> 00:04:59,880
我跟你说中文

194
00:04:59,880 --> 00:05:01,340
是不是首先我还听懂你的意思

195
00:05:01,340 --> 00:05:01,900
我们才能沟通

196
00:05:01,900 --> 00:05:04,140
所以说他们有一个依赖关系

197
00:05:04,140 --> 00:05:05,760
有依赖关系强偶和的

198
00:05:05,760 --> 00:05:06,760
适合现成

199
00:05:06,760 --> 00:05:07,460
好

200
00:05:07,460 --> 00:05:08,740
第四种

201
00:05:08,740 --> 00:05:11,500
可能要扩展到多积分布的

202
00:05:11,500 --> 00:05:12,880
也就是说咱们一个服务

203
00:05:12,880 --> 00:05:13,860
可能一台服气扛不住

204
00:05:13,860 --> 00:05:14,740
需要两台三台

205
00:05:14,740 --> 00:05:17,380
这个时候咱们就去利用县城

206
00:05:17,380 --> 00:05:18,480
为什么因为县城它是隔离的

207
00:05:18,480 --> 00:05:19,180
没什么关系

208
00:05:19,180 --> 00:05:21,580
第五个也是最重要的

209
00:05:21,580 --> 00:05:23,100
还是老师刚才提到的

210
00:05:23,100 --> 00:05:23,740
我们千万不要

211
00:05:23,740 --> 00:05:25,020
非此即彼

212
00:05:25,020 --> 00:05:26,140
你熟悉什么就用什么

213
00:05:26,140 --> 00:05:27,660
你最拿手什么就用什么

214
00:05:27,660 --> 00:05:29,700
好那咱们总结一下

215
00:05:29,700 --> 00:05:32,480
在多进程和多县城的选择中

216
00:05:32,480 --> 00:05:34,700
多进程它的一个特点是什么

217
00:05:34,700 --> 00:05:36,500
是不是比较笨重

218
00:05:36,500 --> 00:05:37,960
切换成本比较高

219
00:05:37,960 --> 00:05:38,520
但是

220
00:05:38,520 --> 00:05:41,240
它是不是安全性比较高

221
00:05:41,240 --> 00:05:41,840
稳定性比较高

222
00:05:41,840 --> 00:05:43,400
因为进程真是没有什么关系

223
00:05:43,400 --> 00:05:45,280
线程比较快

224
00:05:45,280 --> 00:05:46,980
线程是不是比较快

225
00:05:46,980 --> 00:05:49,040
CPU占用内存少切换

226
00:05:49,040 --> 00:05:49,460
简单

227
00:05:49,460 --> 00:05:52,040
但是它的调试复杂

228
00:05:52,040 --> 00:05:53,540
多进程里面

229
00:05:53,540 --> 00:05:55,320
它扩展性比较好

230
00:05:55,320 --> 00:05:56,920
它比较适合多机分布

231
00:05:56,920 --> 00:05:57,660
那么多线程

232
00:05:57,660 --> 00:05:58,580
它只适合一机分布

233
00:05:58,580 --> 00:05:59,240
多核分布

234
00:05:59,240 --> 00:06:00,920
所以多进程和多线程

235
00:06:00,920 --> 00:06:01,940
它各有千秋

236
00:06:01,940 --> 00:06:03,120
那么总结一句话

237
00:06:03,120 --> 00:06:03,820
就是线程快

238
00:06:03,820 --> 00:06:05,060
而进程可靠性高

239
00:06:05,060 --> 00:06:06,780
好

240
00:06:06,780 --> 00:06:08,020
这里就是这节课的内容

