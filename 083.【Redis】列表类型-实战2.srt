1
00:00:00,000 --> 00:00:01,120
好

2
00:00:01,120 --> 00:00:02,640
我们再来看一下下一个例子

3
00:00:02,640 --> 00:00:03,860
怎么样去存储评论列表

4
00:00:03,860 --> 00:00:05,700
我们在文章中或者博客中

5
00:00:05,700 --> 00:00:07,160
是不是都会有一个评论的功能

6
00:00:07,160 --> 00:00:08,220
那么评论的功能

7
00:00:08,220 --> 00:00:09,900
用Reddit怎么去实现呢

8
00:00:09,900 --> 00:00:11,900
其实很简单

9
00:00:11,900 --> 00:00:13,980
我们能不能够用过闪列去实现呢

10
00:00:13,980 --> 00:00:15,640
其实也可以

11
00:00:15,640 --> 00:00:16,200
但是没必要

12
00:00:16,200 --> 00:00:16,820
为什么呢

13
00:00:16,820 --> 00:00:17,820
因为我们的评论

14
00:00:17,820 --> 00:00:19,560
它一般字段是非常固定的

15
00:00:19,560 --> 00:00:20,720
你比如说像作者

16
00:00:20,720 --> 00:00:21,520
时间

17
00:00:21,520 --> 00:00:22,100
内容

18
00:00:22,100 --> 00:00:23,960
其实这三个核心的要素就够了

19
00:00:23,960 --> 00:00:24,960
但是我们文章不一样

20
00:00:24,960 --> 00:00:26,440
我们的文章你可能需要给它打标签

21
00:00:26,440 --> 00:00:27,800
你今天要加一个标签

22
00:00:27,800 --> 00:00:29,620
明天可能要加一个颜色

23
00:00:29,620 --> 00:00:32,300
或者说要加一个什么卡片之类的

24
00:00:32,300 --> 00:00:34,140
咱们文章的字段会不断的去添加

25
00:00:34,140 --> 00:00:34,840
但是我们的评论

26
00:00:34,840 --> 00:00:36,680
它的字段很难去变化

27
00:00:36,680 --> 00:00:38,620
所以我们可以有一种更好的方式去存储

28
00:00:38,620 --> 00:00:39,420
什么呢

29
00:00:39,420 --> 00:00:40,260
字图串

30
00:00:40,260 --> 00:00:41,440
为什么呀

31
00:00:41,440 --> 00:00:43,600
因为我们字图串去序列化我们的评论

32
00:00:43,600 --> 00:00:44,180
它的效率

33
00:00:44,180 --> 00:00:46,220
它在用的空间是不是比我们的闪力低

34
00:00:46,220 --> 00:00:47,180
为什么呀

35
00:00:47,180 --> 00:00:48,040
字图串它不用去

36
00:00:48,040 --> 00:00:51,640
它肯定比对象在的空间小吧

37
00:00:51,640 --> 00:00:52,200
所以呢

38
00:00:52,200 --> 00:00:54,000
这里我们去存储评论

39
00:00:54,000 --> 00:00:55,160
只需要用字图串就够了

40
00:00:55,160 --> 00:00:56,160
那我们就来看一下

41
00:00:56,160 --> 00:00:57,280
怎么样去写这段逻辑

42
00:00:57,280 --> 00:01:02,960
好

43
00:01:02,960 --> 00:01:04,480
首先

44
00:01:04,480 --> 00:01:05,880
刚才我们是不是讲到了

45
00:01:05,880 --> 00:01:07,940
评论只需要用

46
00:01:07,940 --> 00:01:09,060
字幕串

47
00:01:09,060 --> 00:01:10,780
存储

48
00:01:10,780 --> 00:01:12,340
好

49
00:01:12,340 --> 00:01:13,780
那么我们首先来定义一段字幕串

50
00:01:13,780 --> 00:01:16,860
比如说commentstr等于什么呢

51
00:01:16,860 --> 00:01:19,140
json.是军范

52
00:01:19,140 --> 00:01:21,200
对象

53
00:01:21,200 --> 00:01:22,700
比如说我们的评论一般有什么

54
00:01:22,700 --> 00:01:23,640
是不是作者

55
00:01:23,640 --> 00:01:25,140
XXX

56
00:01:25,140 --> 00:01:26,400
还有时间

57
00:01:26,400 --> 00:01:28,400
time xxx 然后呢

58
00:01:28,400 --> 00:01:29,400
内容吧

59
00:01:29,400 --> 00:01:32,400
xxxx 好 这里呢就是我们需要去存储处创

60
00:01:32,400 --> 00:01:35,400
那么重点是我们怎么去存 client 点

61
00:01:35,400 --> 00:01:37,400
我们是不是也要把评论给存储到

62
00:01:37,400 --> 00:01:39,400
一个列表里面去啊 为什么呀

63
00:01:39,400 --> 00:01:44,400
评论要存到

64
00:01:44,400 --> 00:01:46,400
列表中 为什么

65
00:01:46,400 --> 00:01:47,400
因为我们

66
00:01:47,400 --> 00:01:49,400
一篇文章会有多个评论吧

67
00:01:49,400 --> 00:01:51,400
对吧

68
00:01:51,400 --> 00:01:54,400
因为一篇文章会有

69
00:01:54,400 --> 00:01:57,980
那么一篇文章会有多个评论

70
00:01:57,980 --> 00:01:59,540
我们应该怎么样去定义它的字段

71
00:01:59,540 --> 00:02:02,280
你比如说client.push

72
00:02:02,280 --> 00:02:03,240
push什么呢

73
00:02:03,240 --> 00:02:05,380
我们怎么样给它的类型取名

74
00:02:05,380 --> 00:02:06,480
比如说post

75
00:02:06,480 --> 00:02:07,920
我们根据什么样去存

76
00:02:07,920 --> 00:02:08,800
是不是根据ID啊

77
00:02:08,800 --> 00:02:10,320
比如说我们一个ID下面是不是有多个评论

78
00:02:10,320 --> 00:02:13,520
也就是我们会通过ID去存

79
00:02:13,520 --> 00:02:16,000
那么ID后面是不是需要跟上一个关键字

80
00:02:16,000 --> 00:02:17,260
因为我们的postID

81
00:02:17,260 --> 00:02:18,500
这样一个类型

82
00:02:18,500 --> 00:02:20,300
你看不出来它是一个评论吧

83
00:02:20,300 --> 00:02:22,300
它肯定看不出来它是评论列表吧

84
00:02:22,300 --> 00:02:23,500
可能它是文章列表也说不定

85
00:02:23,500 --> 00:02:25,900
所以我们需要加入一个comments去区分

86
00:02:25,900 --> 00:02:27,160
代表了我们存储了

87
00:02:27,160 --> 00:02:29,640
是这个id下面的评论列表

88
00:02:29,640 --> 00:02:29,840
好

89
00:02:29,840 --> 00:02:33,200
接下来呢

90
00:02:33,200 --> 00:02:33,860
我们lpush

91
00:02:33,860 --> 00:02:34,740
push什么呢

92
00:02:34,740 --> 00:02:36,980
这是我们的属性名

93
00:02:36,980 --> 00:02:37,540
那么呢

94
00:02:37,540 --> 00:02:38,560
我们需要去存储了

95
00:02:38,560 --> 00:02:39,560
是不是我们的comment str

96
00:02:39,560 --> 00:02:41,460
也就是这样一个字幕创

97
00:02:41,460 --> 00:02:42,140
好

98
00:02:42,140 --> 00:02:42,660
这里呢

99
00:02:42,660 --> 00:02:44,840
就实现了我们文章评论的一个存储

100
00:02:44,840 --> 00:02:46,400
那我们来总结一下

101
00:02:46,400 --> 00:02:48,100
我们用什么存呢

102
00:02:48,100 --> 00:02:48,880
是不是存储字幕创

103
00:02:48,880 --> 00:02:49,180
为什么

104
00:02:49,180 --> 00:02:51,200
因为评论它的字段比较固定

105
00:02:51,200 --> 00:02:52,200
不会经常变化

106
00:02:52,200 --> 00:02:53,840
所以我们用字串去存出来就够了

107
00:02:53,840 --> 00:02:54,660
第二个呢

108
00:02:54,660 --> 00:02:55,880
用什么存

109
00:02:55,880 --> 00:02:56,460
列表吧

110
00:02:56,460 --> 00:02:57,320
怎么命名

111
00:02:57,320 --> 00:02:58,700
是不是pose id comments

112
00:02:58,700 --> 00:02:59,500
只要有辨识度

113
00:02:59,500 --> 00:02:59,900
然后呢

114
00:02:59,900 --> 00:03:01,740
根据id去区分就可以了

115
00:03:01,740 --> 00:03:01,900
好

116
00:03:01,900 --> 00:03:02,700
这里呢

117
00:03:02,700 --> 00:03:03,860
就是咱们这节课的内容

