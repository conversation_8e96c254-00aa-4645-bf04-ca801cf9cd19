1
00:00:00,260 --> 00:00:02,300
好同学们大家好那么从这节课开始了

2
00:00:02,300 --> 00:00:05,120
我来接下来带同学们去学习乐阶势世界循环

3
00:00:05,120 --> 00:00:06,920
如果说你能够看到

4
00:00:06,920 --> 00:00:08,700
这一个视频可以说你非常幸运

5
00:00:08,700 --> 00:00:09,480
为什么这么说

6
00:00:09,480 --> 00:00:10,760
因为在我

7
00:00:10,760 --> 00:00:16,640
做前段开发的初期一直在理解乐阶势世界循环这块可以说我理解了好几年都没有搞清楚包括很多开发者

8
00:00:16,640 --> 00:00:18,180
他们可能工作了三五年

9
00:00:18,180 --> 00:00:22,280
甚至五到十年一些开发者可能他们都没有搞清楚乐阶势世界循环到底是怎么一回事

10
00:00:22,280 --> 00:00:23,040
为什么

11
00:00:23,040 --> 00:00:26,880
因为乐阶势世界循环这块可以说是非常的复杂也是非常的难理解

12
00:00:26,880 --> 00:00:28,680
所以说很多人他搞不明白

13
00:00:28,920 --> 00:00:29,940
如果说你能看到这个视频

14
00:00:29,940 --> 00:00:32,000
我相信你跟着老师一定能够去学好

15
00:00:32,000 --> 00:00:33,280
所以说你们是非常幸运的

16
00:00:33,280 --> 00:00:35,060
你们能通过这样一系列课程

17
00:00:35,060 --> 00:00:37,620
去理解其他一期开发者很长时间理解不了的内容

18
00:00:37,620 --> 00:00:39,920
你们就可以说占据了一个技术了

19
00:00:39,920 --> 00:00:43,000
一个先机这样是对你们来说是有非常大的好处了

20
00:00:43,000 --> 00:00:45,040
好那么我们就来证实看一下肉类杰S

21
00:00:45,040 --> 00:00:47,100
事件循环到底是怎么怎么样一回事

22
00:00:47,100 --> 00:00:51,440
这节课老师主要会去讲解肉类杰S事件循环他的一个概念以及他是怎么一回事

23
00:00:51,440 --> 00:00:52,220
然后

24
00:00:52,220 --> 00:00:54,780
在后面的内容呢老师会通过代码

25
00:00:54,780 --> 00:00:56,820
去帮助同学们去理解他

26
00:00:57,080 --> 00:01:01,440
所以同学们放心 尽管它复杂 但是一定可以学好 这节课我们就想就来先介绍概念

27
00:01:01,440 --> 00:01:07,060
当nodejs启动时会初始化event loop 每一个event loop都会包含如下

28
00:01:07,060 --> 00:01:08,600
六个循环阶段

29
00:01:08,600 --> 00:01:11,680
loads的试镜循环和浏览器的试镜循环完全不一样

30
00:01:11,680 --> 00:01:12,960
同学们看到没有

31
00:01:12,960 --> 00:01:15,520
loads的试镜循环和浏览器的试镜循环完全不一样

32
00:01:15,520 --> 00:01:17,820
所以说同学们在学习的过程中一定要把浏览器

33
00:01:17,820 --> 00:01:19,360
它那个试镜循环给忘掉

34
00:01:19,600 --> 00:01:23,110
瀏覽器的视频循环是什么样的 红对链 微对链 对吧 其实都是建设和他是完全不一样的

35
00:01:23,110 --> 00:01:23,560
 所以说同学们

36
00:01:23,560 --> 00:01:26,660
需要把浏览器的视频循环 首先给忘掉

37
00:01:26,660 --> 00:01:30,640
好 那我们就来看一下 今天是他视频循环为哪六个阶段

38
00:01:30,640 --> 00:01:31,390
第一个 台博士 第二个 爱欧 爱的 扑 扑 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣

39
00:01:31,390 --> 00:01:32,260
 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 扣 �

40
00:01:32,260 --> 00:01:36,360
第1个timers 第2个io either pull check close callback

41
00:01:36,360 --> 00:01:37,640
是不是有一种

42
00:01:37,640 --> 00:01:43,520
想使的感觉 同学们看到这样一个一幅图 咱们的浏览器里面红对链 微对链已经非常的

43
00:01:43,520 --> 00:01:44,540
恶心

44
00:01:44,540 --> 00:01:47,360
那么nope 也是他1分的nope 他分为6阶段

45
00:01:47,360 --> 00:01:48,140
可以说

46
00:01:48,140 --> 00:01:51,200
更加的恶心 更加复杂 也更加的会色难懂

47
00:01:51,200 --> 00:01:54,280
那么我们就来看一下 没关系 难 不要怕

48
00:01:54,280 --> 00:01:56,060
我来带你们去学习他

49
00:01:56,060 --> 00:01:57,340
首先

50
00:01:57,340 --> 00:01:58,620
这里有一个tips

51
00:01:58,620 --> 00:01:59,400
同学注意看

52
00:01:59,400 --> 00:02:01,440
图中的每个方框被称作视觉循环的一个阶段

53
00:02:01,700 --> 00:02:03,300
那么这六个阶段为一轮世界循环

54
00:02:03,300 --> 00:02:04,980
在浏览器里面是不是

55
00:02:04,980 --> 00:02:07,360
一轮

56
00:02:07,360 --> 00:02:09,440
红对列和所谓的微对列执行

57
00:02:09,440 --> 00:02:10,360
被称为一轮世界循环

58
00:02:10,360 --> 00:02:11,560
那么在NORALGS呢

59
00:02:11,560 --> 00:02:13,980
它是每六个这样一个阶段

60
00:02:13,980 --> 00:02:15,800
所发生的时候就称为一轮世界循环

61
00:02:15,800 --> 00:02:17,200
好 我们来看一下这六个阶段

62
00:02:17,200 --> 00:02:18,340
分辨做了什么事情

63
00:02:18,340 --> 00:02:19,280
首先

64
00:02:19,280 --> 00:02:21,640
timers此阶段执行那些

65
00:02:21,640 --> 00:02:23,140
由settimeout和setinterfa

66
00:02:23,140 --> 00:02:24,160
调度的回调函数

67
00:02:24,160 --> 00:02:25,040
怎么样理解

68
00:02:25,040 --> 00:02:26,380
settimeout和setinterfa

69
00:02:26,380 --> 00:02:27,260
我们可以统称为什么

70
00:02:27,260 --> 00:02:29,040
是不是统称为定时器

71
00:02:29,040 --> 00:02:30,720
也就是说它第一个阶段

72
00:02:30,720 --> 00:02:31,320
去执行

73
00:02:31,320 --> 00:02:32,880
也就是timers这个阶段去执行

74
00:02:32,880 --> 00:02:35,140
去执行什么呢

75
00:02:35,140 --> 00:02:35,700
执行定时器

76
00:02:35,700 --> 00:02:36,240
它的一些回调

77
00:02:36,240 --> 00:02:37,420
你比如说你写了一个setthamout0

78
00:02:37,420 --> 00:02:38,040
setthamout10

79
00:02:38,040 --> 00:02:39,280
当10毫秒之后

80
00:02:39,280 --> 00:02:40,340
它的callback回来的时候

81
00:02:40,340 --> 00:02:41,780
它执行的时机一定是timers

82
00:02:41,780 --> 00:02:42,280
这样一个时机

83
00:02:42,280 --> 00:02:42,700
好

84
00:02:42,700 --> 00:02:43,420
那么我们就来看一下

85
00:02:43,420 --> 00:02:43,920
很好理解吧

86
00:02:43,920 --> 00:02:44,160
timers

87
00:02:44,160 --> 00:02:46,080
那我们来看一下io这样一个阶段

88
00:02:46,080 --> 00:02:48,940
此阶段会执行几乎所有的回调函数

89
00:02:48,940 --> 00:02:49,800
除了

90
00:02:49,800 --> 00:02:50,640
同学们注意看

91
00:02:50,640 --> 00:02:52,640
它会执行所有的回调函数

92
00:02:52,640 --> 00:02:53,200
除了什么呢

93
00:02:53,200 --> 00:02:54,180
closecallbacks

94
00:02:54,180 --> 00:02:55,080
它不常用

95
00:02:55,080 --> 00:02:55,480
不用管

96
00:02:55,480 --> 00:02:57,240
和那些由timers和set

97
00:02:57,240 --> 00:02:58,320
immediate调度回调

98
00:02:58,320 --> 00:03:14,740
什么意思

99
00:03:28,320 --> 00:03:33,500
0 这里是一个call back 怎么要理解 为什么是约等于约等于是什么意思啊

100
00:03:33,500 --> 00:03:36,420
 同学们约等于是不是就是很相似 但是呢 他们又不同

101
00:03:36,420 --> 00:03:40,320
这里呢 首先 我们来看一下他们的相同点是什么 老师这里有一段呆漠

102
00:03:40,320 --> 00:03:47,320
塞里面对着咱们呢 康收一个222 在外面呢 111 因为老师刚才说了 他约等于是他老的0

103
00:03:47,320 --> 00:03:50,620
 所以说呢 随线执行111先执行 然后再执行222 对吧 好 我们来看一下

104
00:03:50,620 --> 00:03:53,820
11122 对吧 好

105
00:03:54,820 --> 00:03:56,160
那么它具体有什么不同点

106
00:03:56,160 --> 00:03:59,680
这里呢 我们后面去分析它这个视线循环真正的阶段时候 我们会去讲

107
00:03:59,680 --> 00:04:04,620
然后第三个阶段是IDAL 空转 此阶段只在内部使用

108
00:04:04,620 --> 00:04:09,080
同学们注意到没有 老师呢 加粗了三个阶段 Timer IO和PUR

109
00:04:09,080 --> 00:04:12,860
那么IDAL没有加粗 说明了它使用的非常少 因为它是nodejs内部去处理的一些事情

110
00:04:12,860 --> 00:04:13,860
 我们暂时不允许关注

111
00:04:13,860 --> 00:04:15,260
那咱们来看一下PUR

112
00:04:15,260 --> 00:04:21,580
PUR这一个阶段是非常复杂 也是非常重要的一个阶段 整个nodejs的视线循环的周期它的核心就是PUR

113
00:04:21,580 --> 00:04:24,160
它是什么意思 同学们跟大家老师看

114
00:04:24,620 --> 00:04:27,940
检索新的IO事件,在恰当的时候RODE会阻塞在这个阶段

115
00:04:27,940 --> 00:04:29,740
什么意思

116
00:04:29,740 --> 00:04:33,060
我们之前学RODEGS的时候,老师是不是讲过

117
00:04:33,060 --> 00:04:36,900
RODE他是一个什么样的语言,是不是非阻塞IO,事件驱动

118
00:04:36,900 --> 00:04:40,240
那么明明是非阻塞,为什么说这样一个阶段它会被阻塞,这不是矛盾吗

119
00:04:40,240 --> 00:04:42,800
其实并不是这样的,我们来继续看一下PRO他的描述

120
00:04:42,800 --> 00:04:47,660
可能有同学会问,怎么这么长啊,确实是这样的

121
00:04:47,660 --> 00:04:52,520
一定要把它搞清楚,可能呢,这里来老师去介绍的过程中,可能有的同学会听不懂,或者说有点迷糊

122
00:04:52,520 --> 00:04:54,320
听不懂的同学可以多看几遍

123
00:04:54,580 --> 00:04:56,620
实在听不懂也没关系 我们后面通过代码

124
00:04:56,620 --> 00:05:01,240
去执行 去分析的时候 同学们一定能够搞懂 这里先跟着老师去理解一下他的概念

125
00:05:01,240 --> 00:05:06,100
我们来看一下 如果evennope进了pro阶段且代码未设定timer

126
00:05:06,100 --> 00:05:07,120
那么

127
00:05:07,120 --> 00:05:10,460
未设定timer封为下面这两种情况 那么还有另一种情况就是什么呀

128
00:05:10,460 --> 00:05:12,760
当他进入pro的时候 如果说你代码设定timer

129
00:05:12,760 --> 00:05:17,880
人们去理解 也就是你代码里面写的timerout 和没有写的timerout 也就是说你代码里面

130
00:05:17,880 --> 00:05:19,420
写的定时器就走下面

131
00:05:19,420 --> 00:05:21,720
没写定时器就走上面 对吧 很好理解

132
00:05:21,720 --> 00:05:23,760
我们先来看一下你没写定时器的情况

133
00:05:24,580 --> 00:05:25,600
如果扑不为空

134
00:05:25,600 --> 00:05:28,560
扑不为空怎么理解

135
00:05:28,560 --> 00:05:30,180
也就是说咱们在扑这个阶段

136
00:05:30,180 --> 00:05:31,180
咱们的对面里面

137
00:05:31,180 --> 00:05:32,340
有call back的时候

138
00:05:32,340 --> 00:05:33,480
那么他就会一直去执行他

139
00:05:33,480 --> 00:05:34,420
假如你有很多的call back

140
00:05:34,420 --> 00:05:35,560
那么就会一个一个的执行

141
00:05:35,560 --> 00:05:36,320
他的核心是什么

142
00:05:36,320 --> 00:05:36,920
同步的执行

143
00:05:36,920 --> 00:05:37,900
好

144
00:05:37,900 --> 00:05:38,820
那么咱们再来看一下

145
00:05:38,820 --> 00:05:39,600
如果

146
00:05:39,600 --> 00:05:41,160
扑为空

147
00:05:41,160 --> 00:05:42,160
也就是说咱们扑这个阶段

148
00:05:42,160 --> 00:05:43,340
没有什么call back去执行

149
00:05:43,340 --> 00:05:43,940
那我们再来看

150
00:05:43,940 --> 00:05:44,840
他又有两种情况

151
00:05:44,840 --> 00:05:45,460
一统呢

152
00:05:45,460 --> 00:05:47,920
如果代码已经被set immediate设定了call back

153
00:05:47,920 --> 00:05:48,700
也就是说呢

154
00:05:48,700 --> 00:05:50,860
你的代码里面有通过set immediate

155
00:05:50,860 --> 00:05:52,460
去设定了一些异步的回调

156
00:05:52,460 --> 00:05:53,700
那么even的note

157
00:05:53,700 --> 00:05:54,660
将结束破阶段

158
00:05:54,660 --> 00:05:55,740
进入check阶段

159
00:05:55,740 --> 00:05:56,160
什么意思

160
00:05:56,160 --> 00:05:58,040
破这个阶段就会直接

161
00:05:58,040 --> 00:05:59,200
也就是说

162
00:05:59,200 --> 00:05:59,980
如果说你的代码

163
00:05:59,980 --> 00:06:00,800
没有去写定时期

164
00:06:00,800 --> 00:06:02,640
但是当破为空的时候

165
00:06:02,640 --> 00:06:04,160
你又用set immediate设定了callback

166
00:06:04,160 --> 00:06:06,220
那么破这个阶段就会跳过

167
00:06:06,220 --> 00:06:07,480
就会到下一个阶段

168
00:06:07,480 --> 00:06:07,820
check

169
00:06:07,820 --> 00:06:08,700
check是做什么的

170
00:06:08,700 --> 00:06:10,460
check是set immediate的回调

171
00:06:10,460 --> 00:06:11,480
会在此阶段被调用

172
00:06:11,480 --> 00:06:12,360
也就是说破这个阶段

173
00:06:12,360 --> 00:06:13,520
直接会通过进入下一个阶段

174
00:06:13,520 --> 00:06:14,740
check走set immediate

175
00:06:14,740 --> 00:06:15,320
它来回调

176
00:06:15,320 --> 00:06:17,080
那么第二个

177
00:06:17,080 --> 00:06:19,220
如果代码没有设定set immediate

178
00:06:19,220 --> 00:06:21,000
even noble将阻塞在该阶段

179
00:06:21,000 --> 00:06:22,240
等待callback加入破

180
00:06:22,240 --> 00:06:23,140
什么意思

181
00:06:23,140 --> 00:06:25,520
也就是说如果说你没有去设定时器

182
00:06:25,520 --> 00:06:27,240
且普尔他也为空

183
00:06:27,240 --> 00:06:28,900
那么咱们就会一直

184
00:06:28,900 --> 00:06:30,060
阻塞在普尔这个阶段

185
00:06:30,060 --> 00:06:31,280
在恰当的时候

186
00:06:31,280 --> 00:06:32,220
什么是在恰当的时候

187
00:06:32,220 --> 00:06:33,560
其实就是这样一种情况

188
00:06:33,560 --> 00:06:34,840
这就是在恰当的时候

189
00:06:34,840 --> 00:06:36,840
event loop将一直阻塞在普尔这个阶段

190
00:06:36,840 --> 00:06:38,100
去等待回调函数

191
00:06:38,100 --> 00:06:39,700
好那么我们再来看

192
00:06:39,700 --> 00:06:43,100
看咱们写的定时器是什么样的情况

193
00:06:43,100 --> 00:06:44,760
如果普尔为空

194
00:06:44,760 --> 00:06:47,060
那么event loop将不断的去检查timer

195
00:06:47,060 --> 00:06:48,460
也就是说他不断的去检查

196
00:06:48,460 --> 00:06:49,480
你的定时器的时间到了没有

197
00:06:49,480 --> 00:06:50,800
到了就去执行他的callback

198
00:06:50,800 --> 00:06:52,060
其实这一段很好理解

199
00:06:52,060 --> 00:06:56,420
然后event loop 将按顺序进入 timer 阶段并执行 timer 也就是说

200
00:06:56,420 --> 00:06:58,460
你的定时期的时间到了

201
00:06:58,460 --> 00:07:01,020
PRO 就会往下走到 check 到 close callbacks 然后呢

202
00:07:01,020 --> 00:07:04,600
到下一轮新的时间循环里面去去 timer 里面去执行定时期的回调

203
00:07:04,600 --> 00:07:07,420
那么这里就是咱们乐阶S 时间循环 它的一个概念

204
00:07:07,420 --> 00:07:08,440
我们来回顾一下

205
00:07:08,440 --> 00:07:11,260
乐阶S 它的时间循环总共分为六阶段

206
00:07:11,260 --> 00:07:14,080
Timer IO IDER PRO CHECK CLOSE CALLBACK 同学们呢

207
00:07:14,080 --> 00:07:17,140
需要去关注的就是 timer IO

208
00:07:17,140 --> 00:07:18,180
和PRO 阶段

209
00:07:18,180 --> 00:07:19,700
这三个阶段是很重要的

210
00:07:19,700 --> 00:07:22,020
然后PRO 阶段会有些描述它分为几种情况

211
00:07:22,060 --> 00:07:23,500
首先就是你

212
00:07:23,500 --> 00:07:25,580
写了定时期和没写定时期

213
00:07:25,580 --> 00:07:27,280
然后没写定时期里面又分两种情况

214
00:07:27,280 --> 00:07:28,880
一种呢是普尔维

215
00:07:28,880 --> 00:07:29,840
普尔不为空

216
00:07:29,840 --> 00:07:30,700
一种呢是普尔维空

217
00:07:30,700 --> 00:07:31,980
那么为空的时候又分两种情况

218
00:07:31,980 --> 00:07:33,840
一种是你写了三里面对的毁掉

219
00:07:33,840 --> 00:07:34,580
一种呢是没写

220
00:07:34,580 --> 00:07:36,200
你没写他就会一直阻塞在那里

221
00:07:36,200 --> 00:07:37,580
这里呢就是乐的经验史

222
00:07:37,580 --> 00:07:39,540
他事件循环的一个概念上面的一个介绍

223
00:07:39,540 --> 00:07:40,540
同学们听不懂没有关系

224
00:07:40,540 --> 00:07:41,420
从下几个开始

225
00:07:41,420 --> 00:07:42,740
老师呢就会通过代码

226
00:07:42,740 --> 00:07:44,100
带着同学们一步一步的去分析

227
00:07:44,100 --> 00:07:45,540
乐的经验史事件循环到底是怎么回事

228
00:07:45,540 --> 00:07:46,140
好

229
00:07:46,140 --> 00:07:47,540
咱们这节课就到这里

