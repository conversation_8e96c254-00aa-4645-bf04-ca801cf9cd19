1
00:00:00,260 --> 00:00:01,800
好 这节课我们就来看一下

2
00:00:01,800 --> 00:00:03,840
EGG它里面的一个渐进式开发

3
00:00:03,840 --> 00:00:05,120
那么什么是渐进式开发

4
00:00:05,120 --> 00:00:09,220
那么渐进式开发其实是EGG里面它一种非常重要的设计思想

5
00:00:09,220 --> 00:00:10,500
不知道大家有没有注意到

6
00:00:10,500 --> 00:00:12,280
咱们是不是学习过

7
00:00:12,280 --> 00:00:13,060
view啊

8
00:00:13,060 --> 00:00:15,360
那么view它其实也是一种渐进式的

9
00:00:15,360 --> 00:00:16,640
开发框架

10
00:00:16,640 --> 00:00:19,200
不信的话我们可以去看一下view它的一个光芒

11
00:00:19,200 --> 00:00:21,000
它最显眼的一句话是什么呀

12
00:00:21,000 --> 00:00:22,780
最显眼的是什么

13
00:00:22,780 --> 00:00:24,580
渐进式JavaScript的框架

14
00:00:24,580 --> 00:00:25,600
其实很多同学

15
00:00:25,600 --> 00:00:27,640
包括很多开发者他工作了很多年

16
00:00:27,640 --> 00:00:28,920
他都理解不了什么是渐进式

17
00:00:29,180 --> 00:00:30,460
只是看一看而已 没有深入的思考过

18
00:00:30,460 --> 00:00:31,740
那么我们这几个就来看一下

19
00:00:31,740 --> 00:00:34,820
在1GG里面它的一个监警师到底是什么样的一个概念

20
00:00:34,820 --> 00:00:39,680
其实监警师它是咱们软件开放领域的一种非常重要的设计思想

21
00:00:39,680 --> 00:00:41,220
首先我们来举个例子

22
00:00:41,220 --> 00:00:43,520
你比如说我们需要去封装一些方法

23
00:00:43,520 --> 00:00:45,300
我们要写一个扩展的插件

24
00:00:45,300 --> 00:00:47,360
在Extend里面去写一个context.js

25
00:00:47,360 --> 00:00:49,400
context.js它的作用是什么呢

26
00:00:49,400 --> 00:00:50,180
其实就是

27
00:00:50,180 --> 00:00:51,700
context

28
00:00:51,700 --> 00:00:52,480
对象

29
00:00:52,480 --> 00:00:53,500
扩展

30
00:00:53,500 --> 00:00:54,020
属性

31
00:00:54,020 --> 00:00:54,520
或者

32
00:00:54,520 --> 00:00:55,300
方法

33
00:00:55,300 --> 00:00:57,340
什么意思 比如说咱们在讲义里面

34
00:00:57,340 --> 00:00:58,620
我们需要去扩展一个

35
00:00:58,620 --> 00:01:03,820
你是不是iOS在那个判断方法 然后他会认为我不是iOS 因为里面的会有一些详细的判断逻辑

36
00:01:03,820 --> 00:01:05,460
 这里呢 我能给省略了 我们直接来看一下

37
00:01:05,460 --> 00:01:09,420
好 比如说我们在existent里面

38
00:01:09,420 --> 00:01:16,220
是不是需要去写一个js啊 是什么呀 是不是叫做contest.js 好 我们的mod

39
00:01:16,220 --> 00:01:19,620
module.expose等于

40
00:01:19,620 --> 00:01:23,220
get get什么呢 意思ios

41
00:01:24,120 --> 00:01:27,840
这样一个方法是不是就去判断你的设备是不是ls 那么中间的过程同样的我们省略

42
00:01:27,840 --> 00:01:31,800
你不是ls好 我们就就这样搞简单一点

43
00:01:31,800 --> 00:01:33,440
好 那么其实

44
00:01:33,440 --> 00:01:38,510
就是如此的简单 那么大家可以看到 我们在controller里面其实是可以访问到

45
00:01:38,510 --> 00:01:40,160
 你比如说我们在contest里面直接去调用

46
00:01:40,160 --> 00:01:42,500
isls

47
00:01:42,500 --> 00:01:45,620
好 那么我们来看一下会发生什么事情

48
00:01:45,620 --> 00:01:49,040
你不是ls

49
00:01:49,040 --> 00:01:51,200
大家可以看到是不是很神奇

50
00:01:51,200 --> 00:01:52,840
是不是非常神奇啊

51
00:01:54,120 --> 00:02:02,060
那么这里呢就是我们在一阶级里面它去写扩展的一种方式咱们呢可以在context.js里面去给咱们的cts在一个对象去扩展一些属性

52
00:02:02,060 --> 00:02:05,000
好那么这里呢是我们需要去封装一些方法

53
00:02:05,000 --> 00:02:12,000
那么你比如说我们是不是需要去判断你是不是ls包括呢还需要去判断你是不是安卓包括可能还需要去判断你是不是mike你是不是windows

54
00:02:12,000 --> 00:02:15,180
那么我们业务写多了我们的判断多了是不是需要去封装插件

55
00:02:15,180 --> 00:02:20,040
那么这是我们的第一步何谓渐进式渐进式最开始是不是咱们最早期的需求对吧

56
00:02:20,040 --> 00:02:22,980
它呢是我们最早期的需求

57
00:02:22,980 --> 00:02:27,360
浮行对吧 我们需要ls 需要安卓是慢慢的扩展 我们需要判断的设备越来越多

58
00:02:27,360 --> 00:02:29,220
 然后此时我们是不是需要去考虑

59
00:02:29,220 --> 00:02:30,900
我们封装一个插件了

60
00:02:30,900 --> 00:02:35,940
那么怎么去封装插件呢 其实在一机里面 他也提供了一个封装插件的插件的一个约定

61
00:02:35,940 --> 00:02:36,500
 你比如说

62
00:02:36,500 --> 00:02:39,540
你比如说假装我们要去封装一个判断

63
00:02:39,540 --> 00:02:45,420
判断设备的插件在一机里面 它提供的逆复的这样一个文件夹 你可以在里面去

64
00:02:45,420 --> 00:02:49,780
比如说写一些plugin 把你去比如说刚才的一只ls 一只安卓 你怎么去判断设备

65
00:02:49,780 --> 00:02:51,220
 咱们封装成一个plugin

66
00:02:52,980 --> 00:02:56,980
你呢就可以去封装这些插件然后呢再给它去加载

67
00:02:56,980 --> 00:03:00,520
在哪里呢是不是在config的plugin.js里面去挂载

68
00:03:00,520 --> 00:03:03,640
好那这是不是我们项目发展到一定阶段

69
00:03:03,640 --> 00:03:06,480
我们可以把我们需求是不是封装成一些简单的插件

70
00:03:06,480 --> 00:03:08,880
那么时间长了时间长了对吧

71
00:03:08,880 --> 00:03:12,580
那么时间长了咱们自己封装的插件是不是会越来越稳定了

72
00:03:12,580 --> 00:03:13,800
所以此时你需要做什么

73
00:03:13,800 --> 00:03:18,280
你是不是需要把你的插件是不是给封装成一个npm包啊

74
00:03:18,280 --> 00:03:20,360
那么此时是不是比你的第二个阶段更加的稳定了

75
00:03:20,360 --> 00:03:22,260
因为第二个阶段是对你的需求的一些封装

76
00:03:22,260 --> 00:03:23,980
那么此时第三个阶段你要把它给

77
00:03:23,980 --> 00:03:25,720
是不是给封装成NPM包

78
00:03:25,720 --> 00:03:27,340
那么此时你的项目说明了越来越稳定

79
00:03:27,340 --> 00:03:28,460
那么怎么样去做呢

80
00:03:28,460 --> 00:03:29,760
你可能此时呢会单独的

81
00:03:29,760 --> 00:03:31,480
是不是会单独的去创建一个GitHub的仓库

82
00:03:31,480 --> 00:03:32,940
比如说你去创建一个EGG

83
00:03:32,940 --> 00:03:33,720
什么UA

84
00:03:33,720 --> 00:03:35,840
或者说你去判断设备的这样一个NPM的库

85
00:03:35,840 --> 00:03:36,400
好

86
00:03:36,400 --> 00:03:38,520
那么你上传到你们团队的一个GitHub

87
00:03:38,520 --> 00:03:40,060
或者说GitLab这样的一个仓库里面去

88
00:03:40,060 --> 00:03:41,920
大家就可以通过NPM去下载它

89
00:03:41,920 --> 00:03:44,080
然后是不是自己引入到自己的各自不同的EGG

90
00:03:44,080 --> 00:03:44,800
这样的项目里面去

91
00:03:44,800 --> 00:03:45,260
好

92
00:03:45,260 --> 00:03:46,460
这里呢就是到了第三个阶段

93
00:03:46,460 --> 00:03:47,720
说明你的团队已经非常稳定

94
00:03:47,720 --> 00:03:48,620
业务也很稳定了

95
00:03:48,620 --> 00:03:51,040
你就可以去封装成N片包

96
00:03:51,040 --> 00:03:52,780
那么到第四个阶段

97
00:03:52,780 --> 00:03:54,400
你的N片包是不是会越来越多

98
00:03:54,400 --> 00:03:55,700
比如说你需要去判断设备

99
00:03:55,700 --> 00:03:58,280
有一些你可能还需要去做一些音视频相关的

100
00:03:58,280 --> 00:04:00,420
然后可能需要去做一些登录建权

101
00:04:00,420 --> 00:04:01,900
你有各种各样的一GG的插件包

102
00:04:01,900 --> 00:04:03,580
那么当你这样的包越来越多之后

103
00:04:03,580 --> 00:04:06,460
你是不是会对你的N片包进行一些整合

104
00:04:06,460 --> 00:04:07,220
那么整合之后

105
00:04:07,220 --> 00:04:09,160
是不是就形成了你自己的一套框架

106
00:04:09,160 --> 00:04:11,780
你比如说我们的一GG做OA系统非常多

107
00:04:11,780 --> 00:04:12,900
那么你可以封装一个

108
00:04:12,900 --> 00:04:14,940
OA-EGG这样的一个框架

109
00:04:14,940 --> 00:04:17,120
你通过package接生的显示去引入各种各样

110
00:04:17,120 --> 00:04:19,120
你这些OA业务所需要的一些插件

111
00:04:19,120 --> 00:04:19,640
或者说一些包

112
00:04:19,640 --> 00:04:20,520
你比如说还可以

113
00:04:20,520 --> 00:04:22,020
你比如说基于一些音视频播放的

114
00:04:22,020 --> 00:04:22,740
Player EGG

115
00:04:22,740 --> 00:04:24,220
包括你如果说是做音乐的业务

116
00:04:24,220 --> 00:04:26,080
你也可以去封装成他的一些

117
00:04:26,080 --> 00:04:27,620
跟音乐相关的一些业务框架

118
00:04:27,620 --> 00:04:29,300
那么我们总结一下

119
00:04:29,300 --> 00:04:30,280
到底什么是

120
00:04:30,280 --> 00:04:31,480
渐渐时开发

121
00:04:31,480 --> 00:04:33,200
那么一般来说

122
00:04:33,200 --> 00:04:35,480
你的应用中可能会有复用到的代码

123
00:04:35,480 --> 00:04:36,480
比如说你这个时候

124
00:04:36,480 --> 00:04:38,080
你是不是直接放到你的立法目录里面去

125
00:04:38,080 --> 00:04:39,700
它是EGG自己提供的一个插件目录

126
00:04:39,700 --> 00:04:41,220
那么当插件功能稳定以后

127
00:04:41,220 --> 00:04:44,100
所以你此时就可以独立的做一个NPM包

128
00:04:44,100 --> 00:04:45,400
独立的一个Node Modules

129
00:04:45,400 --> 00:04:47,080
那么长此以往

130
00:04:47,080 --> 00:04:48,120
你是不是会有越来越多

131
00:04:48,120 --> 00:04:48,860
这样单独的插件

132
00:04:48,860 --> 00:04:49,580
此时

133
00:04:49,580 --> 00:04:51,840
是不是就成了你某一类业务场景的解决方案

134
00:04:51,840 --> 00:04:54,020
那么此时你就可以把抽象为独立的一个framework

135
00:04:54,020 --> 00:04:55,060
framework是什么意思

136
00:04:55,060 --> 00:04:55,760
是不是框架

137
00:04:55,760 --> 00:04:57,900
那么刚才我们那幅图是不是讲到了

138
00:04:57,900 --> 00:04:59,620
大家还记住我们之前画的那幅图

139
00:04:59,620 --> 00:05:01,300
我们来看一下

140
00:05:01,300 --> 00:05:02,720
我们之前画的那幅图

141
00:05:02,720 --> 00:05:06,880
我们来看一下

142
00:05:06,880 --> 00:05:09,860
首先我们的1GG

143
00:05:09,860 --> 00:05:11,620
你封装一些插件之后

144
00:05:11,620 --> 00:05:13,460
是不是可以沉淀到你各自的业务

145
00:05:13,460 --> 00:05:13,940
这里呢

146
00:05:13,940 --> 00:05:14,980
是不是就是这样一个框架

147
00:05:14,980 --> 00:05:16,040
它的一个核心思想

148
00:05:16,040 --> 00:05:17,840
也是他们以及团队

149
00:05:17,840 --> 00:05:18,220
以及作者

150
00:05:18,220 --> 00:05:19,640
想向我们去传达的一些信息

151
00:05:19,640 --> 00:05:22,840
你可以这样去设计你的项目

152
00:05:22,840 --> 00:05:24,380
这里呢

153
00:05:24,380 --> 00:05:26,060
其实就是一种渐进式的开发思想

154
00:05:26,060 --> 00:05:27,400
不知道同学们有没有理不到

155
00:05:27,400 --> 00:05:27,640
好

156
00:05:27,640 --> 00:05:28,880
那么我们就来总结一下

157
00:05:28,880 --> 00:05:31,360
这一节课我们所讲解的内容

158
00:05:31,360 --> 00:05:35,480
我们刚才是不是讲到了一种渐进式开发思想

159
00:05:35,480 --> 00:05:36,520
那么渐进式开发思想

160
00:05:36,520 --> 00:05:37,420
是不是主要风味

161
00:05:37,420 --> 00:05:39,160
是不是主要风味是不啊

162
00:05:39,160 --> 00:05:39,460
同学们

163
00:05:39,460 --> 00:05:41,260
低谱

164
00:05:41,260 --> 00:05:43,040
通过是不是通过

165
00:05:43,040 --> 00:05:44,920
1GG提供的

166
00:05:44,920 --> 00:05:45,900
逆谱

167
00:05:45,900 --> 00:05:46,920
封装

168
00:05:46,920 --> 00:05:48,160
是不是封装一些方法

169
00:05:48,160 --> 00:05:51,840
然后呢

170
00:05:51,840 --> 00:05:54,160
然后你的方法稳定之后

171
00:05:54,160 --> 00:05:56,660
可以是不是可以发布成

172
00:05:56,660 --> 00:05:58,120
发布成咱们的N片包

173
00:05:58,120 --> 00:06:00,300
那么发布成N片包之后

174
00:06:00,300 --> 00:06:01,060
到了一个阶段

175
00:06:01,060 --> 00:06:01,780
你的N片包

176
00:06:01,780 --> 00:06:02,900
是不是越来越多

177
00:06:02,900 --> 00:06:05,420
那么越来越多之后

178
00:06:05,420 --> 00:06:06,860
你是不是可以形成你的一套解决方案

179
00:06:06,860 --> 00:06:07,400
对吧

180
00:06:07,400 --> 00:06:09,120
把N片包是不是根据

181
00:06:09,120 --> 00:06:16,160
业务场景形成你自己的解决方案

182
00:06:16,160 --> 00:06:17,420
那么这里呢就是一机器

183
00:06:17,420 --> 00:06:18,720
它那个渐进式的一个开发思想

184
00:06:18,720 --> 00:06:20,420
而且呢它会在它的框架上面

185
00:06:20,420 --> 00:06:21,500
去提供一些支持

186
00:06:21,500 --> 00:06:23,180
希望大家能够好好地理解一下

187
00:06:23,180 --> 00:06:24,080
可能从你们现在去

188
00:06:24,080 --> 00:06:25,780
可能从你们现在有一点听不懂

189
00:06:25,780 --> 00:06:26,320
其实没关系

190
00:06:26,320 --> 00:06:28,020
但是我们只要好好地去理解它

191
00:06:28,020 --> 00:06:29,180
包括以后的工作中

192
00:06:29,180 --> 00:06:30,180
慢慢地去理解

193
00:06:30,180 --> 00:06:32,020
大家一定可以去领悟到它那个真谛

194
00:06:32,020 --> 00:06:33,860
好 这里呢就是我们这节课的内容

