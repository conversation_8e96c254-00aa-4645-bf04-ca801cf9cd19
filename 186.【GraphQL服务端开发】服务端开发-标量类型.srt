1
00:00:00,000 --> 00:00:02,680
前面我们主要熟悉了这个类型的定义

2
00:00:02,680 --> 00:00:03,740
以及参数的传递

3
00:00:03,740 --> 00:00:05,280
不管在类型定义的字段

4
00:00:05,280 --> 00:00:06,740
还是在参数传递的那个参数

5
00:00:06,740 --> 00:00:08,080
它都需要有类型的约束

6
00:00:08,080 --> 00:00:09,900
也包括我们用到的那个query类型

7
00:00:09,900 --> 00:00:11,500
其实这些都属于内置类型

8
00:00:11,500 --> 00:00:14,220
就是默认query这种规则所提供的类型

9
00:00:14,220 --> 00:00:15,620
首先我们再来明确一下

10
00:00:15,620 --> 00:00:17,100
关于这个query类型

11
00:00:17,100 --> 00:00:18,860
它用于实现这个特殊的查询

12
00:00:18,860 --> 00:00:19,800
就是默认的查询入口

13
00:00:19,800 --> 00:00:20,780
所以说这个非常重要

14
00:00:20,780 --> 00:00:21,640
我们也一直在用它

15
00:00:21,640 --> 00:00:23,300
其实与query类似的还有一个类型

16
00:00:23,300 --> 00:00:23,660
叫mutation

17
00:00:23,660 --> 00:00:26,240
就是做数据变更的这个操作

18
00:00:26,240 --> 00:00:27,780
这个后边我们会单组的分析

19
00:00:27,780 --> 00:00:28,800
这个是非常重要的

20
00:00:28,800 --> 00:00:29,660
因为除了查询之外

21
00:00:29,660 --> 00:00:31,280
我们还经常要做数据的变更操作

22
00:00:31,280 --> 00:00:34,000
主要就包括数据的添加修改和删除

23
00:00:34,000 --> 00:00:36,560
所以说这两个类型是非常重要的

24
00:00:36,560 --> 00:00:37,900
没有添加我们后边单独再说

25
00:00:37,900 --> 00:00:40,140
然后的话我们首先把这个标量类型

26
00:00:40,140 --> 00:00:41,240
再给分析一下

27
00:00:41,240 --> 00:00:43,660
所谓的标量类型其实就是普通值

28
00:00:43,660 --> 00:00:44,920
这里边我们都用过了

29
00:00:44,920 --> 00:00:45,680
大部分都用过了

30
00:00:45,680 --> 00:00:46,880
比如说Int,Plot,Spin

31
00:00:46,880 --> 00:00:49,360
除此之外还有一个Fore和ID类型

32
00:00:49,360 --> 00:00:51,620
这个比较色素的就是这个ID类型

33
00:00:51,620 --> 00:00:52,780
它其实主要的作用呢

34
00:00:52,780 --> 00:00:53,800
就是用于实践

35
00:00:53,800 --> 00:00:54,820
这个数据的唯一标识

36
00:00:54,820 --> 00:00:55,540
一般呢

37
00:00:55,540 --> 00:00:57,100
这个是不需要人类能够看懂

38
00:00:57,100 --> 00:00:58,700
比如说随机生成一个粗犯

39
00:00:58,700 --> 00:01:00,000
那这个的话

40
00:01:00,000 --> 00:01:00,820
我们待会会演示一下

41
00:01:00,820 --> 00:01:02,380
那接下来的话

42
00:01:02,380 --> 00:01:03,220
我们可以通过一个例的

43
00:01:03,220 --> 00:01:05,000
把这五种这个标量类型

44
00:01:05,000 --> 00:01:05,560
注意啊

45
00:01:05,560 --> 00:01:06,100
这个官方的说法

46
00:01:06,100 --> 00:01:06,680
叫标量类型

47
00:01:06,680 --> 00:01:07,720
我们统一的把它们

48
00:01:07,720 --> 00:01:09,540
分类到这个内置类型当中

49
00:01:09,540 --> 00:01:11,080
那接下来我们通过一个例的呢

50
00:01:11,080 --> 00:01:11,600
把这几个类型

51
00:01:11,600 --> 00:01:12,180
再来演示一下

52
00:01:12,180 --> 00:01:13,460
就像我们这样来做

53
00:01:13,460 --> 00:01:15,260
还是以这个作为基准

54
00:01:15,260 --> 00:01:16,560
然后在这个基础之上呢

55
00:01:16,560 --> 00:01:17,860
我们来定一个thedent类

56
00:01:17,860 --> 00:01:21,600
它里边我们提供五种标量类型

57
00:01:21,600 --> 00:01:23,400
首先第一个是thedent name

58
00:01:23,400 --> 00:01:25,240
这个肯定是读不识的

59
00:01:25,240 --> 00:01:26,500
然后的话还有年龄

60
00:01:26,500 --> 00:01:27,520
这个我们用的是intern

61
00:01:27,520 --> 00:01:29,660
再给一个真的

62
00:01:29,660 --> 00:01:30,340
性别

63
00:01:30,340 --> 00:01:31,780
性别的话只有两种

64
00:01:31,780 --> 00:01:32,960
要么是男要么是女

65
00:01:32,960 --> 00:01:33,760
所以说我们用触号force

66
00:01:33,760 --> 00:01:34,460
就可以区分出来

67
00:01:34,460 --> 00:01:36,780
所以说我们这用到的就是布尔

68
00:01:36,780 --> 00:01:38,220
布尼

69
00:01:38,220 --> 00:01:39,980
这是另外一种类型

70
00:01:39,980 --> 00:01:41,180
还有什么呢

71
00:01:41,180 --> 00:01:42,600
还有就是float

72
00:01:42,600 --> 00:01:43,760
这我们给一个分数

73
00:01:43,760 --> 00:01:45,520
考到分数是允许有小数的

74
00:01:45,520 --> 00:01:46,440
float

75
00:01:46,440 --> 00:01:48,100
然后还有一个就是特殊的

76
00:01:48,100 --> 00:01:48,980
我们放到第一位

77
00:01:48,980 --> 00:01:50,020
这个是ID

78
00:01:50,020 --> 00:01:51,340
它的类型是DAT的ID

79
00:01:51,340 --> 00:01:54,760
这是包含了全部的五个标量类型

80
00:01:54,760 --> 00:01:57,420
然后的话我们提供一下查询

81
00:01:57,420 --> 00:02:00,460
在这个位置我们查询学生的信息

82
00:02:00,460 --> 00:02:01,660
这类型的话是student

83
00:02:01,660 --> 00:02:06,440
然后我们要准备一下STO对应的数据

84
00:02:06,440 --> 00:02:07,820
所以说我们再提供一个resolver

85
00:02:07,820 --> 00:02:11,940
这里边我们直接去返回对应的数据

86
00:02:11,940 --> 00:02:14,420
sname这个是姓名

87
00:02:14,420 --> 00:02:15,660
比如说我们就是章三

88
00:02:15,660 --> 00:02:17,960
好了然后的话呢是年龄

89
00:02:17,960 --> 00:02:18,940
给他12岁

90
00:02:18,940 --> 00:02:21,140
还有呢就是性别

91
00:02:21,140 --> 00:02:21,760
真的

92
00:02:21,760 --> 00:02:23,060
这个的话我们就用处

93
00:02:23,060 --> 00:02:24,080
比如说表示男

94
00:02:24,080 --> 00:02:24,960
Force就是表示女

95
00:02:24,960 --> 00:02:26,200
还有呢就是

96
00:02:26,200 --> 00:02:27,580
Store就是分数

97
00:02:27,580 --> 00:02:30,720
分数的话比如说考了99.5分

98
00:02:30,720 --> 00:02:31,520
好

99
00:02:31,520 --> 00:02:32,880
还有最后一个这个ID

100
00:02:32,880 --> 00:02:33,480
有点测出

101
00:02:33,480 --> 00:02:34,700
现在的话我们先给他个值

102
00:02:34,700 --> 00:02:35,320
比如说就给个1

103
00:02:35,320 --> 00:02:36,840
那这样的话呢

104
00:02:36,840 --> 00:02:37,800
我们这个数据就准备好了

105
00:02:37,800 --> 00:02:38,640
然后呢我们启动

106
00:02:38,640 --> 00:02:40,640
好启动之后的话

107
00:02:40,640 --> 00:02:42,080
我们待会通过浏览器要访问

108
00:02:42,080 --> 00:02:43,360
这是零次

109
00:02:43,360 --> 00:02:46,100
然后在浏览器中

110
00:02:46,100 --> 00:02:48,000
我们这里边直接查询

111
00:02:48,000 --> 00:02:49,780
要查询的是STO

112
00:02:49,780 --> 00:02:51,360
它当中的相关的字段

113
00:02:51,360 --> 00:02:52,680
比如说我们现在先查一个

114
00:02:52,680 --> 00:02:53,000
sname

115
00:02:53,000 --> 00:02:53,960
然后查询

116
00:02:53,960 --> 00:02:54,700
有数据了

117
00:02:54,700 --> 00:02:56,040
然后还有就是年龄

118
00:02:56,040 --> 00:02:57,280
性别

119
00:02:57,280 --> 00:02:59,400
还有什么呢

120
00:02:59,400 --> 00:03:00,460
还有分数

121
00:03:00,460 --> 00:03:01,160
此过

122
00:03:01,160 --> 00:03:02,340
还有ID

123
00:03:02,340 --> 00:03:03,880
然后点查询

124
00:03:03,880 --> 00:03:05,020
全部的数据都出来了

125
00:03:05,020 --> 00:03:06,840
那数据是有了

126
00:03:06,840 --> 00:03:07,720
但是还有些细节

127
00:03:07,720 --> 00:03:08,160
需要我们注意

128
00:03:08,160 --> 00:03:09,080
就是这个类型

129
00:03:09,080 --> 00:03:10,260
到底有什么作用呢

130
00:03:10,260 --> 00:03:11,020
它其实可以

131
00:03:11,020 --> 00:03:11,920
这个约束

132
00:03:11,920 --> 00:03:12,820
这个数据的格式

133
00:03:12,820 --> 00:03:13,420
是不是合理

134
00:03:13,420 --> 00:03:15,460
比如说你这个年龄

135
00:03:15,460 --> 00:03:16,840
如果说你给它的值不是数值

136
00:03:16,840 --> 00:03:17,760
它是什么呢

137
00:03:17,760 --> 00:03:18,340
你给个字不上

138
00:03:18,340 --> 00:03:19,000
ABC

139
00:03:19,000 --> 00:03:20,620
然后我们在这重启

140
00:03:20,620 --> 00:03:23,060
重启之后你再去查询

141
00:03:23,060 --> 00:03:24,420
看这就报错了

142
00:03:24,420 --> 00:03:24,900
它说什么呢

143
00:03:24,900 --> 00:03:26,220
它说你这个int

144
00:03:26,220 --> 00:03:28,160
不能够表示非int的值

145
00:03:28,160 --> 00:03:28,880
就是ABC

146
00:03:28,880 --> 00:03:30,900
所以说有了这个类型的约束之后

147
00:03:30,900 --> 00:03:32,540
如果说你给它的值不合理的话

148
00:03:32,540 --> 00:03:33,720
那就会产生一个异常

149
00:03:33,720 --> 00:03:35,140
这样的话我们就能够

150
00:03:35,140 --> 00:03:36,060
很容易的区分出来

151
00:03:36,060 --> 00:03:37,560
到底有没有异常的数据

152
00:03:37,560 --> 00:03:38,720
所以说这个类型的约束

153
00:03:38,720 --> 00:03:39,640
还是非常重要的

154
00:03:39,640 --> 00:03:41,240
可以防止出现一些不合理的数据

155
00:03:41,240 --> 00:03:43,460
但是这里边有个细节是什么呢

156
00:03:43,460 --> 00:03:44,600
我们把这个改回去

157
00:03:44,600 --> 00:03:47,020
你如果说给这个XNAME它是个子准

158
00:03:47,020 --> 00:03:48,120
你给这个123

159
00:03:48,120 --> 00:03:48,900
这个不会有错误

160
00:03:48,900 --> 00:03:49,780
为什么呢

161
00:03:49,780 --> 00:03:50,120
我们看一下

162
00:03:50,120 --> 00:03:54,020
那是因为这个123它可以转化成子准

163
00:03:54,020 --> 00:03:55,440
这个是可以转化的

164
00:03:55,440 --> 00:03:55,880
不会出现问题

165
00:03:55,880 --> 00:03:56,420
我们看一下

166
00:03:56,420 --> 00:03:57,600
再一点查询

167
00:03:57,600 --> 00:03:58,720
这个是有值的

168
00:03:58,720 --> 00:04:00,000
也不会出现错误

169
00:04:00,000 --> 00:04:01,300
所以说这有个细节就是什么呢

170
00:04:01,300 --> 00:04:02,680
如果这个数据可以合理化的

171
00:04:02,680 --> 00:04:03,820
转化成对应的类型

172
00:04:03,820 --> 00:04:04,540
这就不会有问题

173
00:04:04,540 --> 00:04:06,220
如果说没有办法转化

174
00:04:06,220 --> 00:04:06,680
那就出错

175
00:04:06,680 --> 00:04:08,040
比如说你把一个字幕上

176
00:04:08,040 --> 00:04:09,120
ETP转化成年龄的数值

177
00:04:09,120 --> 00:04:10,580
这个明显是不合理的

178
00:04:10,580 --> 00:04:11,860
所以说这个细节要注意

179
00:04:11,860 --> 00:04:13,820
这是关于类型的约束

180
00:04:13,820 --> 00:04:14,680
我们要知道它的含义

181
00:04:14,680 --> 00:04:17,240
除此之外还有一个细节就是那个ID

182
00:04:17,240 --> 00:04:18,780
它的值一般怎么产生呢

183
00:04:18,780 --> 00:04:19,800
我们有的时候会用到

184
00:04:19,800 --> 00:04:21,580
一个特殊的产生规则

185
00:04:21,580 --> 00:04:23,120
我们称为UUID

186
00:04:23,120 --> 00:04:26,460
这个的话有一单方的包可以帮我们生成这个UUID

187
00:04:26,460 --> 00:04:27,740
在这我们可以演示一下

188
00:04:27,740 --> 00:04:28,760
怎么用呢

189
00:04:28,760 --> 00:04:29,780
其实我们需要先装一个包

190
00:04:29,780 --> 00:04:31,580
在这我们先给它停掉

191
00:04:31,580 --> 00:04:32,340
然后ncm

192
00:04:32,340 --> 00:04:33,360
拧死到

193
00:04:33,360 --> 00:04:33,880
安装一个包

194
00:04:33,880 --> 00:04:34,900
就方便就叫UUID

195
00:04:34,900 --> 00:04:36,940
然后呢

196
00:04:36,940 --> 00:04:39,000
安装完之后我们这里边就要导入这个包

197
00:04:39,500 --> 00:04:41,960
hauts uuid等于require

198
00:04:41,960 --> 00:04:43,520
包名的话我们就是uid

199
00:04:43,520 --> 00:04:45,180
好导进来之后的话

200
00:04:45,180 --> 00:04:46,620
我们可以直接调用这个uid

201
00:04:46,620 --> 00:04:47,620
产生一个随机的诸步事

202
00:04:47,620 --> 00:04:48,980
它可以保证不重复

203
00:04:48,980 --> 00:04:50,280
所以说很多时候

204
00:04:50,280 --> 00:04:51,580
我们这个id是随机生成的

205
00:04:51,580 --> 00:04:52,880
好这儿安装成功了

206
00:04:52,880 --> 00:04:53,380
安装成功之后

207
00:04:53,380 --> 00:04:54,820
我们先把这个业务给它完成

208
00:04:54,820 --> 00:04:55,840
下面的id的话

209
00:04:55,840 --> 00:04:57,580
我们在这直接调用

210
00:04:57,580 --> 00:04:59,280
就会生成一个随机的诸步事

211
00:04:59,280 --> 00:05:00,780
然后我们再重启

212
00:05:00,780 --> 00:05:02,800
行刺回车

213
00:05:02,800 --> 00:05:04,120
好其中之后的话

214
00:05:04,120 --> 00:05:04,680
我们再来查询

215
00:05:04,680 --> 00:05:05,320
点击

216
00:05:05,320 --> 00:05:06,960
我们会发现这里边这个id的话

217
00:05:06,960 --> 00:05:07,900
是一个随机的诸步事

218
00:05:07,900 --> 00:05:08,980
然后你再点的话

219
00:05:08,980 --> 00:05:10,240
每次点它都变

220
00:05:10,240 --> 00:05:11,760
所以说这个ID它是唯一的

221
00:05:11,760 --> 00:05:12,520
它保证不重复

222
00:05:12,520 --> 00:05:15,060
主要的作用就是唯一的来区分一份数据

223
00:05:15,060 --> 00:05:16,400
所以说这个ID的话

224
00:05:16,400 --> 00:05:18,420
一般我们看到的就是类似这个样子的

225
00:05:18,420 --> 00:05:20,820
这是关于这个ID类型要了解

226
00:05:20,820 --> 00:05:23,280
关于这个标量类型的话

227
00:05:23,280 --> 00:05:24,240
我们就说到这里

228
00:05:24,240 --> 00:05:26,260
最终的结论是什么呢

229
00:05:26,260 --> 00:05:28,520
结论就是这个类型可以约束数据的格式

230
00:05:28,520 --> 00:05:30,240
ID这个类型的话有点特殊

231
00:05:30,240 --> 00:05:32,640
一般就是用于唯一做标识

232
00:05:32,640 --> 00:05:34,160
这个是不需要我们可读

233
00:05:34,160 --> 00:05:34,920
因为它这个规则

234
00:05:34,920 --> 00:05:37,660
就是这样这种形式的一个作用事

235
00:05:37,660 --> 00:05:39,060
就是能够维区分数据就可以了

236
00:05:39,060 --> 00:05:39,840
好

237
00:05:39,840 --> 00:05:40,520
那最后呢

238
00:05:40,520 --> 00:05:41,380
还有一个细节需要注意

239
00:05:41,380 --> 00:05:42,460
就是这个标量类型呢

240
00:05:42,460 --> 00:05:43,340
一般用于

241
00:05:43,340 --> 00:05:44,400
什么地方呢

242
00:05:44,400 --> 00:05:46,040
就是查询数据的ES节点

243
00:05:46,040 --> 00:05:47,020
如何理解这个词呢

244
00:05:47,020 --> 00:05:48,820
实际上所谓的ES节点呢

245
00:05:48,820 --> 00:05:50,100
就是这个属性下面

246
00:05:50,100 --> 00:05:52,000
没有更多的这个子级属性了

247
00:05:52,000 --> 00:05:53,020
什么意思呢

248
00:05:53,020 --> 00:05:53,680
你比如说在SDO

249
00:05:53,680 --> 00:05:54,200
它里边呢

250
00:05:54,200 --> 00:05:54,700
是一个对象

251
00:05:54,700 --> 00:05:55,780
它里边还有更多的字段

252
00:05:55,780 --> 00:05:56,480
但是呢

253
00:05:56,480 --> 00:05:57,340
你SNAME它就是

254
00:05:57,340 --> 00:05:58,940
最里层的那个字段了

255
00:05:58,940 --> 00:05:59,960
它下面没有更多的信息了

256
00:05:59,960 --> 00:06:00,540
所以说这个呢

257
00:06:00,540 --> 00:06:01,680
我们就称为ES节点

258
00:06:01,680 --> 00:06:03,000
那这个标量类型呢

259
00:06:03,000 --> 00:06:04,340
一般就用于这个意思节点

260
00:06:04,340 --> 00:06:05,680
好 这个细节我们要注意

261
00:06:05,680 --> 00:06:07,900
好了 关于标亮内容的话

262
00:06:07,900 --> 00:06:08,700
我们就说到这里

