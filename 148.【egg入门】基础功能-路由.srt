1
00:00:00,000 --> 00:00:02,340
好 这一节课我们就来看一下路由

2
00:00:02,340 --> 00:00:04,580
那么路由我们是不是已经写了很多呀

3
00:00:04,580 --> 00:00:07,000
不仅在1G里面 包括Core里面我们都写了非常多

4
00:00:07,000 --> 00:00:11,580
所以我们先简单的回顾一下我们之前的Router内容

5
00:00:11,580 --> 00:00:13,020
那么Router是不是非常简单

6
00:00:13,020 --> 00:00:13,640
比如说我们要get

7
00:00:13,640 --> 00:00:14,780
那么直接就用Router.get

8
00:00:14,780 --> 00:00:16,000
然后呢一个地址

9
00:00:16,000 --> 00:00:16,820
后面的跟上咱们的Ctrl

10
00:00:16,820 --> 00:00:18,260
如果说你想对单个路由生效

11
00:00:18,260 --> 00:00:19,920
是不是中间可以加入我们的一个中间键

12
00:00:19,920 --> 00:00:22,160
那么中间键在哪里是不是也在我们APP站一个对象上面

13
00:00:22,160 --> 00:00:24,040
那么那下面呢是一个Ctrl的示例

14
00:00:24,040 --> 00:00:25,160
我们也写了很多了

15
00:00:25,160 --> 00:00:26,780
同样呢我们Router是不是支持很多方法

16
00:00:26,780 --> 00:00:28,860
比如说get,put,post,patch,delete

17
00:00:28,860 --> 00:00:30,400
那么其实我们的重点是什么呀

18
00:00:30,400 --> 00:00:31,940
我们重点其实下面内容

19
00:00:31,940 --> 00:00:32,960
你假如说

20
00:00:32,960 --> 00:00:35,260
我们想去在我们项目中去定义RESF风格

21
00:00:35,260 --> 00:00:37,300
那么RESF风格其实现在是非常流行

22
00:00:37,300 --> 00:00:39,360
什么是RESF可能有些同学们不是很理解

23
00:00:39,360 --> 00:00:40,380
那么我们就来

24
00:00:40,380 --> 00:00:41,920
简单来看一下

25
00:00:41,920 --> 00:00:44,220
那么这里呢是阮一峰老师他的一个文章

26
00:00:44,220 --> 00:00:46,520
我们来看一下他的介绍

27
00:00:46,520 --> 00:00:48,580
RESF是目前最流行的API设计规范

28
00:00:48,580 --> 00:00:50,100
用于Web数据接口的设计

29
00:00:50,100 --> 00:00:51,900
那么我们这里是不是可以了解了RESF是什么

30
00:00:51,900 --> 00:00:52,920
它其实是一种规范

31
00:00:52,920 --> 00:00:53,440
它呢

32
00:00:53,440 --> 00:00:54,460
会

33
00:00:54,460 --> 00:00:57,540
它会告诉我们怎么样去设计我们的API接口

34
00:00:57,540 --> 00:00:58,560
它其实就是一种规范

35
00:00:58,820 --> 00:01:00,100
我们来看一下这种规范是什么

36
00:01:00,100 --> 00:01:05,980
Restful它的核心思想就是什么

37
00:01:05,980 --> 00:01:07,020
客户端发售的数据

38
00:01:07,020 --> 00:01:08,800
操作指令都是动词加冰域的结构

39
00:01:08,800 --> 00:01:10,340
你比如

40
00:01:10,340 --> 00:01:11,620
getartics

41
00:01:11,620 --> 00:01:12,380
这个命令

42
00:01:12,380 --> 00:01:13,920
那么get是什么动词

43
00:01:13,920 --> 00:01:15,460
artics是冰域

44
00:01:15,460 --> 00:01:17,500
什么意思是不是动词获取我们的一个

45
00:01:17,500 --> 00:01:18,020
文章

46
00:01:18,020 --> 00:01:20,320
动词通常就是五种的http方法

47
00:01:20,320 --> 00:01:20,840
对应的

48
00:01:20,840 --> 00:01:21,860
对应就是数据库操作

49
00:01:21,860 --> 00:01:22,380
你比如说get

50
00:01:22,380 --> 00:01:22,880
读取

51
00:01:22,880 --> 00:01:23,140
pose

52
00:01:23,140 --> 00:01:23,660
新建

53
00:01:23,660 --> 00:01:23,900
put

54
00:01:23,900 --> 00:01:24,160
更新

55
00:01:24,160 --> 00:01:24,680
patch

56
00:01:24,680 --> 00:01:25,700
也更新

57
00:01:25,700 --> 00:01:26,460
它它属于部分更新

58
00:01:26,460 --> 00:01:27,740
那么delete就是删除

59
00:01:28,000 --> 00:01:28,880
你比如说我们的文章

60
00:01:28,880 --> 00:01:30,700
比如说我们数据库

61
00:01:30,700 --> 00:01:32,740
假如说我们的数据库里面

62
00:01:32,740 --> 00:01:33,420
数据库里面

63
00:01:33,420 --> 00:01:34,400
假如说这样一个方框

64
00:01:34,400 --> 00:01:35,340
就是我们的数据库

65
00:01:35,340 --> 00:01:36,340
它里面是不是有很多的文章

66
00:01:36,340 --> 00:01:37,260
一条一条的很多条很多条

67
00:01:37,260 --> 00:01:37,720
是吧

68
00:01:37,720 --> 00:01:38,480
那么我们的读取

69
00:01:38,480 --> 00:01:40,300
如果说我们需要去操作

70
00:01:40,300 --> 00:01:41,340
文章是不是会有一个读取

71
00:01:41,340 --> 00:01:42,840
我们会去一条一条的去读取

72
00:01:42,840 --> 00:01:43,620
那么你读取文章

73
00:01:43,620 --> 00:01:44,760
是不是还会去新建呢

74
00:01:44,760 --> 00:01:46,140
那么这里就是Post去做的事情

75
00:01:46,140 --> 00:01:47,460
那么如果说你一张文章

76
00:01:47,460 --> 00:01:48,220
内容写了有问题

77
00:01:48,220 --> 00:01:49,000
你是不是还需要去改

78
00:01:49,000 --> 00:01:50,540
这里就可以使用Poot或者Push

79
00:01:50,540 --> 00:01:52,500
包括那你还可以去删除文章

80
00:01:52,500 --> 00:01:54,180
那么Resif它的核心是什么

81
00:01:54,180 --> 00:01:55,320
是不是就是对咱们的

82
00:01:55,320 --> 00:01:56,980
某一个兵域

83
00:01:56,980 --> 00:01:57,860
也就是某一个

84
00:01:57,860 --> 00:01:58,940
咱们

85
00:01:58,940 --> 00:02:00,680
是不是咱们项目中的某一个

86
00:02:00,680 --> 00:02:01,420
事情

87
00:02:01,420 --> 00:02:02,540
咱们对它进行一些操作

88
00:02:02,540 --> 00:02:03,120
你比如说user

89
00:02:03,120 --> 00:02:04,780
对咱们的用户列表去做一些操作

90
00:02:04,780 --> 00:02:05,680
包括了像article

91
00:02:05,680 --> 00:02:06,340
对咱们的文章

92
00:02:06,340 --> 00:02:08,120
或者说你对登录做一些操作

93
00:02:08,120 --> 00:02:09,880
这里其实就是Resif它的一个核心思想

94
00:02:09,880 --> 00:02:10,840
那么如果说

95
00:02:10,840 --> 00:02:12,320
对Resif想更深入了解同学

96
00:02:12,320 --> 00:02:14,180
可以去看一下我们的讲义

97
00:02:14,180 --> 00:02:16,180
这里我已经把文章的链接给贴过去了

98
00:02:16,180 --> 00:02:18,460
包括同学们也可以自己去查一下其他的一些文章

99
00:02:18,460 --> 00:02:19,940
好

100
00:02:19,940 --> 00:02:20,900
那我们刚才了解到了

101
00:02:20,900 --> 00:02:21,460
我们其实

102
00:02:22,500 --> 00:02:25,060
它是一它是一个组合一个动作的组合

103
00:02:25,060 --> 00:02:27,100
那么我们不可能说你假如说我们现在有个

104
00:02:27,100 --> 00:02:27,880
API

105
00:02:27,880 --> 00:02:28,900
对吧

106
00:02:28,900 --> 00:02:29,920
API user是什么

107
00:02:29,920 --> 00:02:31,720
是不是操作我们用户相关的

108
00:02:31,720 --> 00:02:32,480
接口啊

109
00:02:32,480 --> 00:02:36,060
但是如果说我们想操作用户是不是也涉及到跟增山改查

110
00:02:36,060 --> 00:02:36,840
我们可不可以

111
00:02:36,840 --> 00:02:37,340
这样啊

112
00:02:37,340 --> 00:02:38,380
比如说我们去get

113
00:02:38,380 --> 00:02:39,900
get是不是就是获取咱们user列表

114
00:02:39,900 --> 00:02:42,220
你比如说你可能是不是还需要去delete它

115
00:02:42,220 --> 00:02:42,980
然后呢

116
00:02:42,980 --> 00:02:44,520
你是不是还有可能去通过什么呀

117
00:02:44,520 --> 00:02:45,800
是不是通过post去

118
00:02:45,800 --> 00:02:47,340
添加我们用户

119
00:02:47,340 --> 00:02:48,100
你这样是不是很麻烦

120
00:02:48,100 --> 00:02:49,900
你每个路由要写五个动作

121
00:02:49,900 --> 00:02:52,200
这里呢其实在1G里面就给我们封装了一个方法

122
00:02:52,460 --> 00:02:53,480
叫做resources

123
00:02:53,480 --> 00:02:56,300
这里呢他可以一次性的帮你生成所有的内容

124
00:02:56,300 --> 00:02:57,840
再来就可以节约咱们的一个时间

125
00:02:57,840 --> 00:02:59,380
那么这里呢就是他所对应的一个表

126
00:02:59,380 --> 00:03:01,160
你比如说我们是不是有get

127
00:03:01,160 --> 00:03:03,720
有post有put有delete

128
00:03:03,720 --> 00:03:05,520
那么同样的对应了我们的controller里面

129
00:03:05,520 --> 00:03:07,300
你比如说我们都是在post里面

130
00:03:07,300 --> 00:03:08,080
我们重点关注这里

131
00:03:08,080 --> 00:03:09,860
你get对应的方法是index

132
00:03:09,860 --> 00:03:10,900
post对应呢是create

133
00:03:10,900 --> 00:03:11,920
put对应是update

134
00:03:11,920 --> 00:03:13,200
删除了对应的是distory

135
00:03:13,200 --> 00:03:14,980
你比如说你去包括了

136
00:03:14,980 --> 00:03:17,040
所对应的什么new show都是可以的

137
00:03:17,040 --> 00:03:18,060
那么我们这里呢

138
00:03:18,060 --> 00:03:20,360
就来写一段

139
00:03:20,360 --> 00:03:22,160
看一下到底是什么回事

140
00:03:22,460 --> 00:03:26,600
好 首先我们是不是要进入root呀

141
00:03:26,600 --> 00:03:28,900
那么其实这里呢 我已经写好了

142
00:03:28,900 --> 00:03:30,720
我们这里是不是添加了一个路由

143
00:03:30,720 --> 00:03:33,740
我们这里呢 给添加了一个路由 是什么呢

144
00:03:33,740 --> 00:03:35,100
root.resources

145
00:03:35,100 --> 00:03:36,440
API post

146
00:03:36,440 --> 00:03:39,060
是不是我们要根据我们的post这样一个名词

147
00:03:39,060 --> 00:03:39,800
去做一些操作

148
00:03:39,800 --> 00:03:42,280
我们呢 resources是不是帮我们自动生成了很多路由

149
00:03:42,280 --> 00:03:44,140
但是呢 我们的controller对应的是什么呀

150
00:03:44,140 --> 00:03:44,660
是不是post

151
00:03:44,660 --> 00:03:46,400
比如说我们之前对应home的时候

152
00:03:46,400 --> 00:03:47,260
是不是对应到某一个方法

153
00:03:47,260 --> 00:03:48,540
那么如果说我们是resources

154
00:03:48,540 --> 00:03:50,680
我们只需要对应到它的一个控制器就可以了

155
00:03:50,680 --> 00:03:54,200
比如说我们在Ctrl里面

156
00:03:54,200 --> 00:03:55,360
是不是有一个Post这样的一个

157
00:03:55,360 --> 00:03:56,720
那样

158
00:03:56,720 --> 00:03:59,620
那么在Root里面只需要对应到它就可以了

159
00:03:59,620 --> 00:04:01,460
比如说我们这里拿去定义了一个

160
00:04:01,460 --> 00:04:03,560
定义了一个什么呢

161
00:04:03,560 --> 00:04:04,880
定义一个秀方法和一个Index

162
00:04:04,880 --> 00:04:06,660
那么大家注意

163
00:04:06,660 --> 00:04:09,440
那么同学注意秀和Index分别对应什么呀

164
00:04:09,440 --> 00:04:10,700
分别对应什么呀

165
00:04:10,700 --> 00:04:11,480
那么我们来看一下讲义

166
00:04:11,480 --> 00:04:13,980
秀和Index

167
00:04:13,980 --> 00:04:15,580
大家可以看到秀方法

168
00:04:15,580 --> 00:04:19,900
大家可以看到秀方法

169
00:04:19,900 --> 00:04:21,640
它是不是对应我们在那个路由post

170
00:04:21,640 --> 00:04:23,160
post id 说明什么意思

171
00:04:23,160 --> 00:04:24,940
你比如说post 它是一个文章列表

172
00:04:24,940 --> 00:04:27,000
那么我们的秀方法是不是可以通过传递

173
00:04:27,000 --> 00:04:28,900
id 的参数去获取某一条文章

174
00:04:28,900 --> 00:04:30,520
对吧 这里是秀方法

175
00:04:30,520 --> 00:04:31,520
那么index是什么

176
00:04:31,520 --> 00:04:32,660
index

177
00:04:32,660 --> 00:04:35,060
post 它不用传入任何的参数

178
00:04:35,060 --> 00:04:35,920
那么它呢

179
00:04:35,920 --> 00:04:37,140
什么情况下不用传入参数

180
00:04:37,140 --> 00:04:38,140
是不是我们获取所有文章

181
00:04:38,140 --> 00:04:39,460
不需要传参数啊 为什么呀

182
00:04:39,460 --> 00:04:40,600
因为你获取所有

183
00:04:40,600 --> 00:04:42,200
你没有文章没有id

184
00:04:42,200 --> 00:04:43,600
你当然是获取所有啊 对吧

185
00:04:43,600 --> 00:04:44,140
好

186
00:04:44,140 --> 00:04:45,940
那么我们就来看一下

187
00:04:45,940 --> 00:04:49,080
你比如说我们现在的路由是api posts

188
00:04:49,080 --> 00:04:52,660
那么如果说我们想对应到秀方法是不是需要给他后面去加入一个什么呀

189
00:04:52,660 --> 00:04:53,680
是不是加入一个

190
00:04:53,680 --> 00:04:54,720
ID呀

191
00:04:54,720 --> 00:04:56,240
那么这里呢我们就

192
00:04:56,240 --> 00:04:57,780
咱们来打印一下

193
00:04:57,780 --> 00:05:03,160
好

194
00:05:03,160 --> 00:05:05,200
那么我们来访问咱们的API post

195
00:05:05,200 --> 00:05:08,540
API post

196
00:05:08,540 --> 00:05:11,360
比如说我们给他一个ID对吧这里呢给他一个ID

197
00:05:11,360 --> 00:05:12,380
好

198
00:05:12,380 --> 00:05:16,480
大家可以看到我们的秀是不是是不是路由走到了我们的秀

199
00:05:16,480 --> 00:05:18,780
那么如果说我们想或许我们的ID怎么样怎么样做

200
00:05:19,080 --> 00:05:21,080
我们可以通过console.log 什么呢

201
00:05:21,080 --> 00:05:23,080
contest.params

202
00:05:23,080 --> 00:05:25,080
那么我们来看一下params是什么

203
00:05:25,080 --> 00:05:28,080
大家可以看到

204
00:05:28,080 --> 00:05:29,080
id

205
00:05:29,080 --> 00:05:31,080
我们刚才访问的id是不是他

206
00:05:31,080 --> 00:05:34,080
那么这个id他来源于哪里啊

207
00:05:34,080 --> 00:05:35,080
id来源于哪里

208
00:05:35,080 --> 00:05:37,080
是不是egg所约定的呀

209
00:05:37,080 --> 00:05:38,080
大家可以看这张表

210
00:05:38,080 --> 00:05:40,080
egg所约定的

211
00:05:40,080 --> 00:05:41,080
那么同样的index这里呢

212
00:05:41,080 --> 00:05:42,080
我就不给同学们去演示了

213
00:05:42,080 --> 00:05:43,080
他呢

214
00:05:43,080 --> 00:05:44,080
他呢

215
00:05:44,080 --> 00:05:46,080
框架会帮助我们生成所有的

216
00:05:46,080 --> 00:05:47,080
那么这里呢

217
00:05:47,080 --> 00:05:49,080
我们刚才是不是通过params

218
00:05:49,080 --> 00:05:50,280
是不是获取了我们的一个

219
00:05:50,280 --> 00:05:52,080
获取了我们的一个参数

220
00:05:52,080 --> 00:05:52,960
但是同样

221
00:05:52,960 --> 00:05:54,760
我们去get某一个请求的时候

222
00:05:54,760 --> 00:05:55,960
比如说我们在地址上面

223
00:05:55,960 --> 00:05:57,020
是不是还需要一些参数

224
00:05:57,020 --> 00:05:58,360
那么我们怎么样去获取它呢

225
00:05:58,360 --> 00:06:00,180
你比如说

226
00:06:00,180 --> 00:06:01,400
我们

227
00:06:01,400 --> 00:06:02,700
这样

228
00:06:02,700 --> 00:06:04,100
比如说nameJack

229
00:06:04,100 --> 00:06:05,200
我们这样去

230
00:06:05,200 --> 00:06:06,660
输入一个地址

231
00:06:06,660 --> 00:06:07,760
那么我们怎么样获取到

232
00:06:07,760 --> 00:06:08,980
nameJack

233
00:06:08,980 --> 00:06:10,000
我们来看一下

234
00:06:10,000 --> 00:06:11,300
刚才的params里面有没有

235
00:06:11,300 --> 00:06:12,940
好 不好意思

236
00:06:12,940 --> 00:06:18,440
name

237
00:06:18,440 --> 00:06:20,700
等于Jack好走理

238
00:06:20,700 --> 00:06:23,780
大家可以看到我们的params里面是不是没有啊

239
00:06:23,780 --> 00:06:24,540
其实它在哪里呢

240
00:06:24,540 --> 00:06:27,980
它在我们的contest的点叫query里面

241
00:06:27,980 --> 00:06:29,560
好那么我们再试一下

242
00:06:29,560 --> 00:06:32,980
大家可以看到是不是有一个nameJack这样一个对象

243
00:06:32,980 --> 00:06:38,500
那么同学们这里呢就需要去区分我们的query和params的一个区别

244
00:06:38,500 --> 00:06:40,900
那么query就对于咱们地址栏上面的一些参数

245
00:06:40,900 --> 00:06:43,700
那么params就定义咱们是不是定义咱们路由上面

246
00:06:43,700 --> 00:06:46,400
你需要去截取的一些咱们的一些参数啊

247
00:06:46,400 --> 00:06:47,960
好这里呢就是咱们路由这里呢

248
00:06:48,480 --> 00:06:49,760
内容我们来总结一下

249
00:06:49,760 --> 00:06:55,900
那么路由

250
00:06:55,900 --> 00:06:56,920
我们是不是写了很多

251
00:06:56,920 --> 00:06:58,720
刚才我们重点讲解内容是什么

252
00:06:58,720 --> 00:07:00,240
我们刚才是不是重点讲解了

253
00:07:00,240 --> 00:07:01,780
resources他的一个使用啊

254
00:07:01,780 --> 00:07:02,560
他的作用是什么

255
00:07:02,560 --> 00:07:03,840
是不是自动帮我们

256
00:07:03,840 --> 00:07:04,860
生成

257
00:07:04,860 --> 00:07:06,140
是不是restful

258
00:07:06,140 --> 00:07:06,900
API

259
00:07:06,900 --> 00:07:07,920
那么restful是什么

260
00:07:07,920 --> 00:07:09,200
restful是不是咱们一个

261
00:07:09,200 --> 00:07:11,000
API设计的一个标准

262
00:07:11,000 --> 00:07:12,020
那么同学们可以去

263
00:07:12,020 --> 00:07:13,300
看一下我们刚才发的文章

264
00:07:13,300 --> 00:07:13,820
对吧

265
00:07:13,820 --> 00:07:15,100
那么他自动生成的API

266
00:07:15,100 --> 00:07:17,140
那么resources他还有一个什么特点

267
00:07:17,400 --> 00:07:18,480
是不是对应

268
00:07:18,480 --> 00:07:20,960
是不是对应Contruna的类啊

269
00:07:20,960 --> 00:07:24,620
而不是某一个具体的方法

270
00:07:24,620 --> 00:07:26,720
那么而且呢还会有一张表对应那些

271
00:07:26,720 --> 00:07:29,500
是不是还会有一张表对应我们的一些关系

272
00:07:29,500 --> 00:07:30,560
那么这个表的话

273
00:07:30,560 --> 00:07:31,680
同学们可以下去之后自己去看

274
00:07:31,680 --> 00:07:33,440
那么这里呢我们就不去回顾这张表了

275
00:07:33,440 --> 00:07:35,080
那么我们在路由的过程中

276
00:07:35,080 --> 00:07:36,660
是不是还需要去获取一些

277
00:07:36,660 --> 00:07:38,760
还需要去获取什么

278
00:07:38,760 --> 00:07:40,520
是不是获取一些参数啊

279
00:07:40,520 --> 00:07:43,700
那么参数我们是不是分两种

280
00:07:43,700 --> 00:07:46,300
什么是不是query和params

281
00:07:46,300 --> 00:07:47,280
那么他们有什么区别

282
00:07:47,280 --> 00:07:48,920
query是不是对应地址栏参数对吧

283
00:07:48,920 --> 00:07:50,260
地址栏参数

284
00:07:50,260 --> 00:07:52,080
那么params呢是不是路由

285
00:07:52,080 --> 00:07:55,240
约定的参数

286
00:07:55,240 --> 00:07:58,500
好那这里呢就是我们路由这里的内容

