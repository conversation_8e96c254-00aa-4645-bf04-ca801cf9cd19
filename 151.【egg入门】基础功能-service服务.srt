1
00:00:00,000 --> 00:00:01,660
好 这一节课我们就来看一下

2
00:00:01,660 --> 00:00:04,080
service 我们之前是不是已经提过很多次了

3
00:00:04,080 --> 00:00:05,780
那么我们就来看一下它到底是什么

4
00:00:05,780 --> 00:00:07,960
那么它的作用的话 简单来说

5
00:00:07,960 --> 00:00:10,600
service就是专门用来请求和组装数据的

6
00:00:10,600 --> 00:00:13,900
说白了 在咱们的后端服务中经常用它来去操作数据库

7
00:00:13,900 --> 00:00:15,520
那么它的使用场景呢

8
00:00:15,520 --> 00:00:18,780
都有两个 第一个 复杂数据的处理

9
00:00:18,780 --> 00:00:20,640
你比如说要去展现的一些信息

10
00:00:20,640 --> 00:00:21,660
需要从数据库去获取

11
00:00:21,660 --> 00:00:23,780
然后还需要经过一些规则计算

12
00:00:23,780 --> 00:00:25,700
才能够去返回给咱们的一个界面

13
00:00:25,700 --> 00:00:28,240
那么还有一个就是咱们第三方服务的调用

14
00:00:28,240 --> 00:00:29,740
比如说去获取咱们的GitHub

15
00:00:29,740 --> 00:00:30,940
我们之前是不是举过一个

16
00:00:30,940 --> 00:00:33,740
咱们去获取第三方天气局报接口的一个例子

17
00:00:33,740 --> 00:00:35,620
那么如果说你的数据非常复杂

18
00:00:35,620 --> 00:00:37,480
你需要去对它做一个组合

19
00:00:37,480 --> 00:00:38,600
那么这样的一些操作

20
00:00:38,600 --> 00:00:40,240
你最好去放入Service

21
00:00:40,240 --> 00:00:41,980
但是如果说业务逻辑比较简单

22
00:00:41,980 --> 00:00:44,040
你直接放进Ctrl也没有关系

23
00:00:44,040 --> 00:00:45,660
这里可以根据你自己的业务去判断

24
00:00:45,660 --> 00:00:47,620
那么这里我们来简单的写一个Service

25
00:00:47,620 --> 00:00:48,100
来看一下

26
00:00:48,100 --> 00:00:49,920
到底什么是Service

27
00:00:49,920 --> 00:00:52,300
那么这里我们先添加一个路由

28
00:00:52,300 --> 00:00:55,220
比如说我们去Get就叫Service

29
00:00:55,220 --> 00:00:59,400
然后我们在控制器里面去添加一个方法

30
00:00:59,400 --> 00:01:01,780
比如说我们去

31
00:01:01,780 --> 00:01:03,860
获取一段数据

32
00:01:03,860 --> 00:01:13,380
好 我们首先来

33
00:01:13,380 --> 00:01:15,640
context 咱们获取一下context

34
00:01:15,640 --> 00:01:18,240
那么我们获取数据

35
00:01:18,240 --> 00:01:19,780
这一段数据实际上在我们的service里面

36
00:01:19,780 --> 00:01:21,320
所以我们此时需要去创建一个service

37
00:01:21,320 --> 00:01:23,080
那么service的创建也非常简单

38
00:01:23,080 --> 00:01:25,580
在APP下面直接去建一个service的文件夹

39
00:01:25,580 --> 00:01:27,360
你比如说

40
00:01:27,360 --> 00:01:31,440
我们去创建了一个叫做data.js

41
00:01:31,440 --> 00:01:33,420
这里呢我们需要去

42
00:01:33,420 --> 00:01:34,920
把咱们的service这样的一个类

43
00:01:34,920 --> 00:01:37,000
那么service呢它和controller是一样的

44
00:01:37,000 --> 00:01:37,800
其实它也是一个

45
00:01:37,800 --> 00:01:39,900
怎么是不是也是一个类啊

46
00:01:39,900 --> 00:01:41,660
等于require

47
00:01:41,660 --> 00:01:45,500
edg.service

48
00:01:45,500 --> 00:01:47,300
好那么呢我们直接通过

49
00:01:47,300 --> 00:01:48,320
比如说我们去建一个class

50
00:01:48,320 --> 00:01:49,220
data

51
00:01:49,220 --> 00:01:52,560
servicex

52
00:01:52,560 --> 00:01:56,140
去继承我们的服务

53
00:01:56,140 --> 00:01:58,700
好 我们这里来随便定一个方法

54
00:01:58,700 --> 00:02:00,120
比如说我们去定一个

55
00:02:00,120 --> 00:02:02,280
就叫index吧

56
00:02:02,280 --> 00:02:05,620
好 那么中间呢 我们来省略了一些

57
00:02:05,620 --> 00:02:08,020
比如说我们可能需要去数据库里面去读取数据

58
00:02:08,020 --> 00:02:12,200
好 这里呢 我们直接去retain一个对象

59
00:02:12,200 --> 00:02:13,560
比如说我们retain一个

60
00:02:13,560 --> 00:02:15,600
比如说我们的lim

61
00:02:15,600 --> 00:02:17,180
jack

62
00:02:17,180 --> 00:02:19,480
age18

63
00:02:19,480 --> 00:02:21,760
我们经过一系列操作之后 获取到了一段数据

64
00:02:21,760 --> 00:02:23,140
然后我们把它给导注一下

65
00:02:23,140 --> 00:02:24,380
module.export

66
00:02:24,380 --> 00:02:28,760
好 那么我们怎么去调用呢

67
00:02:28,760 --> 00:02:32,940
咱们先来看一下home

68
00:02:32,940 --> 00:02:33,940
比如说我们getdata

69
00:02:33,940 --> 00:02:36,700
咱们是不是访问home的getdata

70
00:02:36,700 --> 00:02:39,280
那么首先我们要读取这样一段数据

71
00:02:39,280 --> 00:02:40,700
比如说咱们挖一个data等于什么

72
00:02:40,700 --> 00:02:41,740
第四点

73
00:02:41,740 --> 00:02:43,300
那么咱们之前讲过

74
00:02:43,300 --> 00:02:45,000
service是不是直接就在咱们

75
00:02:45,000 --> 00:02:47,960
咱们controller的dx这样一个对象下面呢

76
00:02:47,960 --> 00:02:49,300
它能有一个service这样的属性

77
00:02:49,300 --> 00:02:50,480
那么service也是一个对象

78
00:02:50,480 --> 00:02:51,620
那么我们怎么样去调用呢

79
00:02:52,140 --> 00:02:53,660
我怎么去调用 大家可以看到

80
00:02:53,660 --> 00:02:58,020
我们的service下面是比如有一个data.js 那么data的data.js里面

81
00:02:58,020 --> 00:02:59,820
它是不是有一个index方法

82
00:02:59,820 --> 00:03:01,860
所以呢 我们去调用的时候 咱们可以直接

83
00:03:01,860 --> 00:03:06,980
直接了data 然后呢 它的index 咱们直接执行 那么在咱们

84
00:03:06,980 --> 00:03:10,820
获取了data之后 咱们把它输出到咱们的页面上面来看一下效果

85
00:03:10,820 --> 00:03:13,380
contest.body.data

86
00:03:13,380 --> 00:03:16,960
好 那么我们来访问一下在一个路由 来看一下到底是不是这么回事

87
00:03:21,060 --> 00:03:25,420
Local host 7001 访问咱们这个service 多的放的 好 那么我们来看一下

88
00:03:25,420 --> 00:03:27,200
到底是什么原因多的放的

89
00:03:27,200 --> 00:03:31,300
其实大家可以看到

90
00:03:31,300 --> 00:03:35,140
这里呢 希望大家注意个问题 我们是不是定义了index 是一个异步的含蓄

91
00:03:35,140 --> 00:03:38,220
 async 所以说呢 我们此时读取的时候是不是需要加上什么呀 是不是

92
00:03:38,220 --> 00:03:40,000
 await 关键词 这里呢 希望大家不要去忘记

93
00:03:40,000 --> 00:03:41,280
好 我们再来访问一下

94
00:03:41,280 --> 00:03:43,340
好 还是不行

95
00:03:43,340 --> 00:03:44,860
好 还是不行

96
00:03:44,860 --> 00:03:48,460
问题出在这里 咱们呢

97
00:03:49,480 --> 00:03:51,340
咱们controller的一个关键字给写错了

98
00:03:51,340 --> 00:03:52,300
好 我们再来看一下

99
00:03:52,300 --> 00:03:57,300
好 大家可以看到我们是不是可以访问到咱们的jack和一个age18呀

100
00:03:57,300 --> 00:03:59,840
那么我们来回顾一下service是怎么样去调用的

101
00:03:59,840 --> 00:04:01,680
那么比如说首先我们去进一个router

102
00:04:01,680 --> 00:04:03,520
然后对应到我们的一个控制器

103
00:04:03,520 --> 00:04:05,260
那么我们控制器里面是不是需要去

104
00:04:05,260 --> 00:04:07,080
比如说我们通过service去处理一段逻辑

105
00:04:07,080 --> 00:04:07,900
我们获取了一个data

106
00:04:07,900 --> 00:04:09,320
那么service的话它在哪里呢

107
00:04:09,320 --> 00:04:10,940
是不是也在app的一个service文件夹下面

108
00:04:10,940 --> 00:04:13,440
我们也是通过咱们和咱们controller类似的一个关键字

109
00:04:13,440 --> 00:04:15,340
service下面那个data.js

110
00:04:15,340 --> 00:04:16,440
然后里面的index方法

111
00:04:16,440 --> 00:04:18,200
我们去获取了一段数据

112
00:04:18,200 --> 00:04:19,780
然后再通过界面

113
00:04:19,780 --> 00:04:20,180
通过玻璃

114
00:04:20,180 --> 00:04:20,820
我们把它放回

115
00:04:20,820 --> 00:04:22,280
这里就是咱们service的一个使用

116
00:04:22,280 --> 00:04:23,560
其实非常的简单

117
00:04:23,560 --> 00:04:26,160
那么我们就来

118
00:04:26,160 --> 00:04:28,360
简单的来总结一下

119
00:04:28,360 --> 00:04:29,680
服务

120
00:04:29,680 --> 00:04:32,000
也就是咱们的一个service

121
00:04:32,000 --> 00:04:32,780
那么service

122
00:04:32,780 --> 00:04:34,400
它是不是直接在app下面

123
00:04:34,400 --> 00:04:35,480
创建一个service

124
00:04:35,480 --> 00:04:36,480
在文件夹目录就可以了

125
00:04:36,480 --> 00:04:37,380
其实它是不是合

126
00:04:37,380 --> 00:04:38,800
是不是合

127
00:04:38,800 --> 00:04:39,740
我控制器类似

128
00:04:39,740 --> 00:04:41,680
不也是

129
00:04:41,680 --> 00:04:42,960
咱们1GG下面的一个

130
00:04:42,960 --> 00:04:44,220
好

131
00:04:44,220 --> 00:04:45,780
这里就是我们这些个内容

