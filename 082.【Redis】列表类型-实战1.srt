1
00:00:00,520 --> 00:00:04,360
好 同学们我们这一节课就来看一下怎么样去存储文章的ID 列表

2
00:00:04,360 --> 00:00:06,140
什么意思 我们现在有个需求

3
00:00:06,140 --> 00:00:08,960
就是说我们存储的文章是不是需要展示许多条啊

4
00:00:08,960 --> 00:00:10,240
就比如说我们去访问一篇

5
00:00:10,240 --> 00:00:13,060
一个比如说减书 知乎 绝经这样的文章

6
00:00:13,060 --> 00:00:16,120
那么我们去搜索一个话题的时候是不是会有很多很多的一些文章

7
00:00:16,120 --> 00:00:17,920
那么我们怎么样去做分页功能呢

8
00:00:17,920 --> 00:00:20,740
这里我们就来通过列表来实现分页的功能

9
00:00:20,740 --> 00:00:25,080
好 那么如果说我们要解决这样一个问题

10
00:00:25,080 --> 00:00:26,620
我们是不是需要通过一个

11
00:00:26,620 --> 00:00:29,180
POSE ID来去记录咱们文章的ID列表啊

12
00:00:29,440 --> 00:00:32,440
比如说post的历史咱们在js里面是不是需要去存储宿主

13
00:00:32,440 --> 00:00:35,040
因为我们去拉取文章的时候肯定是宿主吧

14
00:00:35,040 --> 00:00:37,140
那么呢当发布新文章的时候

15
00:00:37,140 --> 00:00:39,000
我们是不是要把一篇文章给push到宿主里面去

16
00:00:39,000 --> 00:00:41,100
也就是在咱们redis里面通过lpush

17
00:00:41,100 --> 00:00:43,660
去把它推入到列表中

18
00:00:43,660 --> 00:00:44,960
那么再来我们删除

19
00:00:44,960 --> 00:00:47,460
所以也要把这篇文章同时呢根据id给删掉

20
00:00:47,460 --> 00:00:49,120
删掉了就是通过lrem

21
00:00:49,120 --> 00:00:51,760
好那么呢我们就直接来看一下

22
00:00:51,760 --> 00:00:54,320
通过代码怎么样去实现我们的存储文章的id列表

23
00:00:54,320 --> 00:00:58,300
好这里呢老师建了一个this.demo.js

24
00:00:58,300 --> 00:01:04,800
首先我们老规矩是不是是不是需要引入一个redis 等于require redis

25
00:01:04,800 --> 00:01:07,800
然后呢咱们需要去溜它

26
00:01:07,800 --> 00:01:12,800
中间的是一些配置

27
00:01:12,800 --> 00:01:19,800
好首先首先同学们假设假设我们的post list 已经有了

28
00:01:19,800 --> 00:01:23,800
post list 已经有值

29
00:01:25,300 --> 00:01:27,100
通过什么呀 通过

30
00:01:27,100 --> 00:01:29,900
哎哟 復席命令 也就是说假设假设我们的

31
00:01:29,900 --> 00:01:30,940
Post的历史

32
00:01:30,940 --> 00:01:32,980
在一个属性里面已经它是一个

33
00:01:32,980 --> 00:01:35,020
列表 那么列表了已经有一些文章的一些

34
00:01:35,020 --> 00:01:36,300
内容 这里呢

35
00:01:36,300 --> 00:01:39,640
存储的过程 我们就不写了 我们直接来看分页这功能怎么去实现

36
00:01:39,640 --> 00:01:41,420
首先我们当前

37
00:01:41,420 --> 00:01:42,960
我们定一个变量 current

38
00:01:42,960 --> 00:01:44,240
Current page

39
00:01:44,240 --> 00:01:46,800
等于1 也就是当前页

40
00:01:46,800 --> 00:01:48,600
唯一

41
00:01:48,600 --> 00:01:52,180
好 那么当前列表我们是不是还要去定一个什么呀 是不是我们文章的长度啊

42
00:01:52,180 --> 00:01:53,720
比如说 this

43
00:01:53,980 --> 00:01:58,260
lens=10

44
00:01:58,260 --> 00:02:02,680
比如说我们要做分页 我们知道页嘛

45
00:02:02,680 --> 00:02:05,760
知道某一页的长度 那么我们是不是需要知道

46
00:02:05,760 --> 00:02:07,040
我们文章

47
00:02:07,040 --> 00:02:09,340
开始的index和结束index

48
00:02:09,340 --> 00:02:10,880
比如说你要第9页

49
00:02:10,880 --> 00:02:20,340
第9页是不是第80篇文章到第90篇文章 那我们是不是要算出它的index 比如说start=currentpage-1乘以

50
00:02:20,340 --> 00:02:21,620
乘以什么呀

51
00:02:21,880 --> 00:02:24,960
是不是我们的list length 这就是我们的开始

52
00:02:24,960 --> 00:02:25,460
好

53
00:02:25,460 --> 00:02:27,760
start 然后呢end

54
00:02:27,760 --> 00:02:30,080
end是什么呢 直接就是current page

55
00:02:30,080 --> 00:02:31,360
乘以

56
00:02:31,360 --> 00:02:32,120
什么呀

57
00:02:32,120 --> 00:02:33,920
list length -1

58
00:02:33,920 --> 00:02:34,940
为什么要 -1啊

59
00:02:34,940 --> 00:02:36,220
比如说我们访问第1页

60
00:02:36,220 --> 00:02:40,060
第1页第1页是不是0字9啊 同学们 0字9它才是10条嘛

61
00:02:40,060 --> 00:02:41,340
1-1等于0

62
00:02:41,340 --> 00:02:42,360
所以呢它是0

63
00:02:42,360 --> 00:02:44,660
那么呢 1乘10等于是-190到9没毛病嘛

64
00:02:44,660 --> 00:02:45,680
好 那我们继续

65
00:02:45,680 --> 00:02:47,740
此时我们把index已经给算出来了

66
00:02:47,740 --> 00:02:49,280
那么我们再怎么样去取到

67
00:02:49,280 --> 00:02:50,300
我们的列表

68
00:02:51,320 --> 00:02:55,160
比如说我们去 run 一个 post id 的什么呀, 数组

69
00:02:55,160 --> 00:02:59,260
因为我们去取,我们通过分页去取一个数据,在我们前端肯定是数组

70
00:02:59,260 --> 00:03:03,360
那我们通过 client 点什么呢,比如说我们要取第0,调到第9条数据

71
00:03:03,360 --> 00:03:05,140
在列表里面我们怎么去取

72
00:03:05,140 --> 00:03:06,940
是不是,l range啊

73
00:03:06,940 --> 00:03:08,980
l range

74
00:03:08,980 --> 00:03:11,800
l range

75
00:03:11,800 --> 00:03:14,620
什么呢, post叫什么,post list

76
00:03:14,620 --> 00:03:21,020
多了,start,然后多了,end,此时呢,我们已经取到了我们的值

77
00:03:22,080 --> 00:03:24,140
那么取到了它此时是不是宿主啊

78
00:03:24,140 --> 00:03:25,420
它此时还不是的吧

79
00:03:25,420 --> 00:03:27,720
它是一个咱们Redis里面返回的一个

80
00:03:27,720 --> 00:03:28,480
闪链

81
00:03:28,480 --> 00:03:30,280
那么我们怎么样去读取呢

82
00:03:30,280 --> 00:03:32,840
PoseID array

83
00:03:32,840 --> 00:03:34,120
我们是不是要通过

84
00:03:34,120 --> 00:03:34,880
复义起

85
00:03:34,880 --> 00:03:37,200
复义起

86
00:03:37,200 --> 00:03:41,040
传入一个方形

87
00:03:41,040 --> 00:03:42,560
第一参数ID

88
00:03:42,560 --> 00:03:43,340
好

89
00:03:43,340 --> 00:03:45,380
那么我们怎么样去读取这样一个对象

90
00:03:45,380 --> 00:03:46,660
这个对象怎么读取呢

91
00:03:46,660 --> 00:03:47,440
我们要通过

92
00:03:47,440 --> 00:03:48,200
我们闪链

93
00:03:48,200 --> 00:03:49,740
闪链怎么把它解析成对象

94
00:03:49,740 --> 00:03:51,280
我们的PoseID此时还是闪链

95
00:03:51,320 --> 00:04:02,320
大家注意 post id array 此时还是一个列表里面存储的散列

96
00:04:02,320 --> 00:04:05,320
其实叫做 array hash 这还是不是会准确一点呢

97
00:04:05,320 --> 00:04:06,820
以免同学们把它理解为了一个数据

98
00:04:06,820 --> 00:04:09,320
其实呢 此时它还是一个列表里面存储的是散列

99
00:04:09,320 --> 00:04:15,320
也就是说我们的 id 它此时我们准备去取呢

100
00:04:15,320 --> 00:04:19,320
通过 h get one 对吧

101
00:04:19,320 --> 00:04:22,640
然后PostID

102
00:04:22,640 --> 00:04:25,820
多了ID

103
00:04:25,820 --> 00:04:28,660
好那么我们的值是不是在回调里面了

104
00:04:28,660 --> 00:04:33,820
这里呢才我们得到我们真正的文章它的一个对象

105
00:04:33,820 --> 00:04:35,340
把对象打印出来

106
00:04:35,340 --> 00:04:38,980
这里呢我们就已经通过列表的形式去实现了一个分页的功能

107
00:04:38,980 --> 00:04:40,320
并且呢把它的值给读取到了

108
00:04:40,320 --> 00:04:42,080
那么我们来看一下

109
00:04:42,080 --> 00:04:43,940
我们来看一下

110
00:04:43,940 --> 00:04:46,860
它还存在哪些问题

111
00:04:46,860 --> 00:04:48,380
比如说美中不足的是

112
00:04:48,380 --> 00:04:50,680
闪电它没有类似自创的mget的命令

113
00:04:50,680 --> 00:04:51,580
那样可以通过一条命令

114
00:04:51,580 --> 00:04:53,200
同时获取多个件的件质的版本

115
00:04:53,200 --> 00:04:53,700
什么意思

116
00:04:53,700 --> 00:04:55,260
我们的post id array

117
00:04:55,260 --> 00:04:56,020
它是什么呀

118
00:04:56,020 --> 00:04:57,340
是不是我们一个列表里面

119
00:04:57,340 --> 00:04:57,900
装的是闪电

120
00:04:57,900 --> 00:04:59,940
我们可不可以通过mget这样的命令

121
00:04:59,940 --> 00:05:02,240
直接把所有的闪电给得到

122
00:05:02,240 --> 00:05:02,860
不行吧

123
00:05:02,860 --> 00:05:04,360
我们必须要一条一条的取

124
00:05:04,360 --> 00:05:05,920
然后呢一条一条的通过去

125
00:05:05,920 --> 00:05:07,960
去get wall来得到我们的对象

126
00:05:07,960 --> 00:05:08,980
这里呢也是

127
00:05:08,980 --> 00:05:12,520
也是我们存在的一个问题

128
00:05:12,520 --> 00:05:14,820
那么如果说每个id都去请求一次数据库

129
00:05:14,820 --> 00:05:15,960
我们再让一个for each

130
00:05:15,960 --> 00:05:18,180
是不是每次都去请求 通过HGateWall去请求啊

131
00:05:18,180 --> 00:05:20,040
那么它就会产生一个问题叫做什么呢

132
00:05:20,040 --> 00:05:21,940
叫做往返食言

133
00:05:21,940 --> 00:05:24,380
因为你一个数据库往返的去读取它肯定会产生一些延迟

134
00:05:24,380 --> 00:05:27,300
那么我们后面会通过管道和脚本来优化这样一个问题

135
00:05:27,300 --> 00:05:28,340
这里后面会讲到

136
00:05:28,340 --> 00:05:29,680
另外它还会有两个问题

137
00:05:29,680 --> 00:05:30,360
你比如说

138
00:05:30,360 --> 00:05:32,760
比如说我们用列表去存储文章ID有两个问题

139
00:05:32,760 --> 00:05:33,220
第一个

140
00:05:33,220 --> 00:05:35,340
我们文章的发布时间不易修改

141
00:05:35,340 --> 00:05:35,900
为什么呀

142
00:05:35,900 --> 00:05:38,840
我们文章的存储是不是按照时间顺序

143
00:05:38,840 --> 00:05:42,120
去通过比如说R push或者L push进去了

144
00:05:42,120 --> 00:05:44,160
那么如果说你把文章给更新了

145
00:05:44,160 --> 00:05:45,040
那么它只是变成最新的

146
00:05:45,040 --> 00:05:46,220
你是不是要把它移到最后面了

147
00:05:46,220 --> 00:05:46,960
那么这样呢

148
00:05:46,960 --> 00:05:48,360
你会不断的需要去重新排列

149
00:05:48,360 --> 00:05:50,180
咱们POST的历史中的元素顺序

150
00:05:50,180 --> 00:05:50,860
这些操作呢

151
00:05:50,860 --> 00:05:51,640
是比较繁琐的

152
00:05:51,640 --> 00:05:52,220
在咱们业务里面

153
00:05:52,220 --> 00:05:53,540
我们想一想都很复杂

154
00:05:53,540 --> 00:05:53,960
对吧

155
00:05:53,960 --> 00:05:54,700
你比如说你改了一个time

156
00:05:54,700 --> 00:05:55,700
然后你还要

157
00:05:55,700 --> 00:05:56,560
你还要去关注它

158
00:05:56,560 --> 00:05:57,360
它在数据哪个位置

159
00:05:57,360 --> 00:05:58,200
你每次都要去看一下

160
00:05:58,200 --> 00:05:58,700
检查一遍

161
00:05:58,700 --> 00:05:59,240
数据很麻烦

162
00:05:59,240 --> 00:06:00,140
而且很繁琐

163
00:06:00,140 --> 00:06:00,500
这里呢

164
00:06:00,500 --> 00:06:01,900
它是列表存在的一个问题

165
00:06:01,900 --> 00:06:02,540
第二个呢

166
00:06:02,540 --> 00:06:03,260
我们之前讲到过

167
00:06:03,260 --> 00:06:04,500
在数量比较多的时候

168
00:06:04,500 --> 00:06:05,900
列表是不是它操作头位

169
00:06:05,900 --> 00:06:06,680
效率会比较高

170
00:06:06,680 --> 00:06:08,680
那么操作中间的元素效率其实并不高

171
00:06:08,680 --> 00:06:10,220
这里是列表存在的一个问题

172
00:06:10,220 --> 00:06:10,980
好

173
00:06:10,980 --> 00:06:12,560
我们来总结一下这节课的内容

174
00:06:12,560 --> 00:06:13,600
我们是不是讲到了

175
00:06:13,600 --> 00:06:15,180
怎么去存储我们文章的

176
00:06:15,180 --> 00:06:16,800
通过列表怎么去存储

177
00:06:16,800 --> 00:06:17,280
首先

178
00:06:17,280 --> 00:06:19,560
是不是我们会有一个简单的算法

179
00:06:19,560 --> 00:06:20,420
去计算我们的一个

180
00:06:20,420 --> 00:06:21,920
开始和结束的一个index

181
00:06:21,920 --> 00:06:24,180
然后通过lrang去取出一个列表

182
00:06:24,180 --> 00:06:25,240
里面能存储的是闪列

183
00:06:25,240 --> 00:06:27,340
那么我们通过循环这样一个列表

184
00:06:27,340 --> 00:06:28,160
得到ID

185
00:06:28,160 --> 00:06:29,420
我们通过ID

186
00:06:29,420 --> 00:06:31,140
然后通过hget wall

187
00:06:31,140 --> 00:06:32,380
去读取我们闪列中

188
00:06:32,380 --> 00:06:34,240
所有的数据把它转成一个js的对象

189
00:06:34,240 --> 00:06:34,680
叫做data

190
00:06:34,680 --> 00:06:36,060
那么它会存在一些问题

191
00:06:36,060 --> 00:06:36,960
其实一共有三个问题

192
00:06:36,960 --> 00:06:38,460
第一个就是我们的列表

193
00:06:38,460 --> 00:06:39,960
我们的列表

194
00:06:39,960 --> 00:06:41,100
它不能通过mget的命令

195
00:06:41,100 --> 00:06:44,200
可以获得多个键的键子对

196
00:06:44,200 --> 00:06:45,360
所以我们每一个文章ID

197
00:06:45,360 --> 00:06:46,500
都需要去请求一次数据库

198
00:06:46,500 --> 00:06:48,040
这里会产生一个食言

199
00:06:48,040 --> 00:06:48,620
后面会解决

200
00:06:48,620 --> 00:06:49,700
第二个

201
00:06:49,700 --> 00:06:52,020
就是我们是不是列表

202
00:06:52,020 --> 00:06:53,880
它对咱们的时间之段不是很友好

203
00:06:53,880 --> 00:06:54,680
那么第三个

204
00:06:54,680 --> 00:06:55,740
也就是说它的性能比较差

205
00:06:55,740 --> 00:06:57,000
操作头尾的比较高

206
00:06:57,000 --> 00:06:58,560
操作中间的性能就不是很好的

207
00:06:58,560 --> 00:07:01,020
这里就是我们这节课的内容

