1
00:00:00,000 --> 00:00:05,120
好 那么这节课我们来看一下sort里面通过by来根据时间排序

2
00:00:05,120 --> 00:00:06,660
什么意思 其实呢

3
00:00:06,660 --> 00:00:07,940
说白了 我们怎么样

4
00:00:07,940 --> 00:00:13,060
用sortby在一个关键字去进行排序 我们之前是不是讲到sort可以给谁排序

5
00:00:13,060 --> 00:00:14,080
 是不是可以给我们的列表

6
00:00:14,080 --> 00:00:16,120
也可以给我们的有序结合来排序

7
00:00:16,120 --> 00:00:18,440
那么我们如果说需要给闪链排序怎么办

8
00:00:18,440 --> 00:00:21,000
大家还记得我们之前

9
00:00:21,000 --> 00:00:22,780
实现过文章的一个例子吗

10
00:00:22,780 --> 00:00:26,360
比如说我们用闪链去存储文章的一些信息 我们通过hmset

11
00:00:26,360 --> 00:00:27,640
比如说posed1

12
00:00:27,640 --> 00:00:28,920
也就是id为1的文章

13
00:00:29,180 --> 00:00:30,200
我们去给的他一些title

14
00:00:30,200 --> 00:00:31,740
然后呢还有

15
00:00:31,740 --> 00:00:33,540
时间戳咱们重点是时间戳

16
00:00:33,540 --> 00:00:34,820
比如说给他的时间戳为100

17
00:00:34,820 --> 00:00:36,340
好这里我们去实现一个

18
00:00:36,340 --> 00:00:37,380
呃咱们

19
00:00:37,380 --> 00:00:38,900
文章的一个闪念我们来get一下

20
00:00:38,900 --> 00:00:43,260
post

21
00:00:43,260 --> 00:00:45,300
一好这里了

22
00:00:45,300 --> 00:00:46,080
title

23
00:00:46,080 --> 00:00:47,100
时间戳好

24
00:00:47,100 --> 00:00:50,420
那么呢一篇文章肯定是不够的我们演示的话肯定是需要

25
00:00:50,420 --> 00:00:51,960
比如说我来多添加几篇文章

26
00:00:51,960 --> 00:00:52,740
id

27
00:00:52,740 --> 00:00:55,040
时间了150

28
00:00:58,100 --> 00:00:59,380
id3时间呢200

29
00:00:59,380 --> 00:01:02,960
4时间呢

30
00:01:02,960 --> 00:01:08,340
120吧好这里呢我呢添加了四篇文章我们如果说想给这四篇文章通过

31
00:01:08,340 --> 00:01:09,880
时间排序

32
00:01:09,880 --> 00:01:14,480
通过他的时间戳去进行排序也就是通过咱们文章的一个发布时间来做怎么办

33
00:01:14,480 --> 00:01:21,400
这里呢为了方便大家理解我呢用一幅图来方便来带大家去理解一下比如说我们现在呢有这么这样一些文章

34
00:01:21,400 --> 00:01:24,220
好自己稍微有点小了把它调大一点

35
00:01:25,240 --> 00:01:28,320
16号 这样会好一点 假如说我们现在有posed

36
00:01:28,320 --> 00:01:32,400
好 删掉两个吧 假如说我们现在有四篇文章

37
00:01:32,400 --> 00:01:37,780
分别呢 有title time 他们的一些时间戳 那么我们要对这四篇文章进行排序

38
00:01:37,780 --> 00:01:38,300
 怎么去做

39
00:01:38,300 --> 00:01:40,860
排序的话 肯定是根据时间去排序

40
00:01:40,860 --> 00:01:47,900
大家还记得我们之前 每篇文章 他是不是都会有一个缩列名 那么缩列名是不是和文章单独

41
00:01:47,900 --> 00:01:48,800
 我们单独是不是

42
00:01:48,800 --> 00:01:49,820
建了一个

43
00:01:49,820 --> 00:01:51,860
给他们单独建了一个什么呀

44
00:01:52,120 --> 00:01:54,000
是不是给他们单独建立一个闪链

45
00:01:54,000 --> 00:01:57,940
然后来去给他们的缩猎名和我们文章的ID来建立一个联系

46
00:01:57,940 --> 00:01:58,500
这里呢

47
00:01:58,500 --> 00:01:59,360
它的作用是什么

48
00:01:59,360 --> 00:02:01,620
是不是我们每次去查询

49
00:02:01,620 --> 00:02:03,120
缩猎名是否可用的时候

50
00:02:03,120 --> 00:02:03,760
我们只要去查

51
00:02:03,760 --> 00:02:04,980
它这样一个属性存不存在

52
00:02:04,980 --> 00:02:06,080
我们就知道缩猎名可不可用吧

53
00:02:06,080 --> 00:02:06,800
同样的

54
00:02:06,800 --> 00:02:08,080
我们想对它进行排序

55
00:02:08,080 --> 00:02:09,740
我们是不是需要得到一个排序结果

56
00:02:09,740 --> 00:02:10,160
所以呢

57
00:02:10,160 --> 00:02:13,600
我们需要用一个列表去存储文章所谓的ID

58
00:02:13,600 --> 00:02:14,800
比如说我们现在有四篇文章

59
00:02:14,800 --> 00:02:15,800
我们需要用一个列表

60
00:02:15,800 --> 00:02:16,900
每存储一篇文章

61
00:02:16,900 --> 00:02:19,240
就需要把ID存到额外的一个列表里面去

62
00:02:19,240 --> 00:02:21,300
大家可以理解吗

63
00:02:22,120 --> 00:02:23,960
因为我们的文章

64
00:02:23,960 --> 00:02:27,540
你比如说你在你的博客里面去发表了十篇文章

65
00:02:27,540 --> 00:02:28,080
二十篇文章

66
00:02:28,080 --> 00:02:29,820
你需不需要知道你列表的总长度

67
00:02:29,820 --> 00:02:31,080
你所有的文章发表了多少篇

68
00:02:31,080 --> 00:02:33,700
那么你是不是可以直接通过咱们列表里面的LL认识

69
00:02:33,700 --> 00:02:35,700
就可以获取你发表文章的篇数

70
00:02:35,700 --> 00:02:37,140
这样就很方便

71
00:02:37,140 --> 00:02:40,080
所以说我们可以利用列表和我们的

72
00:02:40,080 --> 00:02:43,240
这样一些闪念来实现对文章实现的排序

73
00:02:43,240 --> 00:02:44,340
那么通过什么呢

74
00:02:44,340 --> 00:02:46,080
也就是我们刚才介绍的by在一个关键词

75
00:02:46,080 --> 00:02:47,620
那么我们就来看一下具体怎么样去做

76
00:02:52,120 --> 00:02:57,580
好刚才呢我们是不是已经存了四篇文章post1234分别了有title和一些times

77
00:02:57,580 --> 00:03:03,120
那么我们此时是不是还会需要有一个刚才我们讲到的是不是需要有一个列表来维护id之间的关系啊

78
00:03:03,120 --> 00:03:08,320
你比如说我们去要曝写一个列表叫做post

79
00:03:08,320 --> 00:03:18,020
post的冒号list好我们咱们的列表里面有哪些呢是不是存储的id分别是1234好大家可以看到我们存储了四个id

80
00:03:18,020 --> 00:03:18,880
那么我们通过

81
00:03:18,880 --> 00:03:20,220
此时的我们现在是不是要对它

82
00:03:20,220 --> 00:03:22,120
对文章根据时间错来进行排序

83
00:03:22,120 --> 00:03:23,340
那么我们就来看一下怎么样去排序

84
00:03:23,340 --> 00:03:25,520
我们现在看一下by它的语法是什么

85
00:03:25,520 --> 00:03:28,020
by参数的语法为by的参考件

86
00:03:28,020 --> 00:03:29,740
其中参考件可以是自创类型

87
00:03:29,740 --> 00:03:30,960
或者是闪电类型的某个之段

88
00:03:30,960 --> 00:03:32,200
如果提供了by参数

89
00:03:32,200 --> 00:03:35,100
sort命令将不在依据元素自身的值进行排序

90
00:03:35,100 --> 00:03:36,700
而是对元素每个元素的值

91
00:03:36,700 --> 00:03:39,820
替换参考件中的第一个新并获取其值

92
00:03:39,820 --> 00:03:40,500
什么意思

93
00:03:40,500 --> 00:03:41,260
其实说简单一点

94
00:03:41,260 --> 00:03:42,420
我们之前通过sort

95
00:03:42,420 --> 00:03:44,220
对列表和我们的有序集合进行排序

96
00:03:44,220 --> 00:03:45,820
它是不是根据自身的子进行排序了

97
00:03:45,820 --> 00:03:47,460
那么此时我们的需求变成了什么

98
00:03:47,460 --> 00:03:50,540
这会变成了根据我们闪列的时间这样一个属性的进行排序

99
00:03:50,540 --> 00:03:51,920
所以我们就要用by

100
00:03:51,920 --> 00:03:52,600
后面呢

101
00:03:52,600 --> 00:03:53,640
后面呢说了一段

102
00:03:53,640 --> 00:03:54,860
听不懂的话

103
00:03:54,860 --> 00:03:56,860
什么根据第一个新来获取其职

104
00:03:56,860 --> 00:03:57,660
没关系

105
00:03:57,660 --> 00:03:59,020
我们这里呢

106
00:03:59,020 --> 00:03:59,860
我来给大家演示一下

107
00:03:59,860 --> 00:04:01,200
大家就可以很快的能够理解

108
00:04:01,200 --> 00:04:02,320
by它怎么去用

109
00:04:02,320 --> 00:04:04,360
比如说我们要给文章排序

110
00:04:04,360 --> 00:04:05,380
首先sort

111
00:04:05,380 --> 00:04:07,800
第二个属性是什么呢

112
00:04:07,800 --> 00:04:08,580
第二个属性

113
00:04:08,580 --> 00:04:10,180
第二个属性其实是列表

114
00:04:10,180 --> 00:04:13,380
比如说我们去输入post list

115
00:04:13,380 --> 00:04:15,180
列表里面哪几个字1234

116
00:04:15,180 --> 00:04:17,980
好我们此时by什么呢

117
00:04:17,980 --> 00:04:18,760
首先

118
00:04:18,760 --> 00:04:23,100
第一个是列表列表by什么是不是by我们的闪地啊post1234

119
00:04:23,100 --> 00:04:24,380
好那么我们

120
00:04:24,380 --> 00:04:26,700
如果说要对它排序我们需要这样去写吗

121
00:04:26,700 --> 00:04:28,480
这样肯定是不行的你如果说有一千个

122
00:04:28,480 --> 00:04:30,540
你有一千篇文章你不能这么去写

123
00:04:30,540 --> 00:04:32,840
而呢你可以去用新去代替

124
00:04:32,840 --> 00:04:33,600
因为呢

125
00:04:33,600 --> 00:04:35,900
post list by post的新

126
00:04:35,900 --> 00:04:37,180
就稍微有一点

127
00:04:37,180 --> 00:04:40,780
负行环的感觉他可以把1234分辨了给传给post的新号

128
00:04:40,780 --> 00:04:42,820
所以呢这里就是post1234

129
00:04:43,080 --> 00:04:44,980
然后这里还会有一个箭头

130
00:04:44,980 --> 00:04:46,040
代表什么呢

131
00:04:46,040 --> 00:04:47,860
代表你是要根据它的哪一个属性

132
00:04:47,860 --> 00:04:49,620
比如说你要根据title去排序

133
00:04:49,620 --> 00:04:51,160
你呢这里一个箭头

134
00:04:51,160 --> 00:04:51,860
然后title

135
00:04:51,860 --> 00:04:52,860
如果说你是要根据

136
00:04:52,860 --> 00:04:53,980
咱们现在是要根据时间吧

137
00:04:53,980 --> 00:04:55,440
所以呢咱们就用这样一个符号

138
00:04:55,440 --> 00:04:56,600
来输入times

139
00:04:56,600 --> 00:04:58,480
好我们来看一下结果是什么

140
00:04:58,480 --> 00:04:59,480
好大家可以看到

141
00:04:59,480 --> 00:05:00,740
我们的排序是不是已经出来了

142
00:05:00,740 --> 00:05:01,540
1423

143
00:05:01,540 --> 00:05:03,300
1423是不是分别代表我们文章的id

144
00:05:03,300 --> 00:05:03,940
我们来看一下

145
00:05:03,940 --> 00:05:04,440
对不对

146
00:05:04,440 --> 00:05:05,320
1是多少

147
00:05:05,320 --> 00:05:06,380
1是100吧

148
00:05:06,380 --> 00:05:07,480
4呢120

149
00:05:07,480 --> 00:05:09,100
2呢150

150
00:05:09,100 --> 00:05:10,200
3呢200

151
00:05:10,200 --> 00:05:10,560
对吧

152
00:05:10,560 --> 00:05:12,160
它是从小到大进行一个排序

153
00:05:12,160 --> 00:05:12,500
那么

154
00:05:12,500 --> 00:05:15,360
我们得到的结果就是我们文章的一个ID

155
00:05:15,360 --> 00:05:17,640
我们是不是我们通过ID就可以查到每篇文章

156
00:05:17,640 --> 00:05:18,320
它的详细信息啊

157
00:05:18,320 --> 00:05:18,640
所以说呢

158
00:05:18,640 --> 00:05:21,640
其实到这里就已经实现了我们文章排序的一个需求

159
00:05:21,640 --> 00:05:22,660
那我们再来看一下

160
00:05:22,660 --> 00:05:24,020
现在是从小到大排序

161
00:05:24,020 --> 00:05:25,080
如果说你想从大到小

162
00:05:25,080 --> 00:05:26,920
同样的我们可以加入什么呀

163
00:05:26,920 --> 00:05:28,260
是不是第一LC这样一个参数

164
00:05:28,260 --> 00:05:29,080
所以呢

165
00:05:29,080 --> 00:05:31,620
他就进行了一个倒序排序

166
00:05:31,620 --> 00:05:34,420
好

167
00:05:34,420 --> 00:05:35,640
那么我们再来看一下

168
00:05:35,640 --> 00:05:36,880
如果说

169
00:05:36,880 --> 00:05:40,560
如果说你此时我们获取的是不是ID啊

170
00:05:40,560 --> 00:05:41,580
那么如果说你想

171
00:05:41,580 --> 00:05:44,400
你想直接获取我们文章的标题

172
00:05:44,400 --> 00:05:45,940
你如果说这样一种情况

173
00:05:45,940 --> 00:05:47,220
我们是不是要通过h get wall

174
00:05:47,220 --> 00:05:48,760
通过id去h get wall

175
00:05:48,760 --> 00:05:49,780
文章所有的信息

176
00:05:49,780 --> 00:05:51,060
然后拿到他的title

177
00:05:51,060 --> 00:05:52,600
但是呢我们可以通过这一条命令

178
00:05:52,600 --> 00:05:53,880
直接去获取文章的title

179
00:05:53,880 --> 00:05:54,900
怎么样去做呢

180
00:05:54,900 --> 00:05:56,440
这里我来给大家演示一下

181
00:05:56,440 --> 00:05:57,200
我们刚才

182
00:05:57,200 --> 00:05:59,000
disc

183
00:05:59,000 --> 00:06:00,020
我们如果说

184
00:06:00,020 --> 00:06:03,340
我们如果说想去获取他的

185
00:06:03,340 --> 00:06:04,360
文章的标题

186
00:06:04,360 --> 00:06:06,680
我们可以利用一个属性叫做get

187
00:06:08,980 --> 00:06:10,780
我们去比如说我们去get什么呢

188
00:06:10,780 --> 00:06:13,080
好这里为了方便区分我们大写get

189
00:06:13,080 --> 00:06:14,100
post

190
00:06:14,100 --> 00:06:15,640
新

191
00:06:15,640 --> 00:06:19,740
依然是一个类似箭头一样的东西比如说我们要去获得获取咱们排序之后的title

192
00:06:19,740 --> 00:06:22,540
title 叉叉叉叉叉叉为什么这里还是叉叉叉呢

193
00:06:22,540 --> 00:06:24,080
因为我们之前存储的时候

194
00:06:24,080 --> 00:06:25,360
因为我们之前存储的时候

195
00:06:25,360 --> 00:06:26,900
title 就是叉叉叉

196
00:06:26,900 --> 00:06:30,740
这里不用过分的去关注如果说有兴趣的同学可以下去之后自己去演示一下

197
00:06:30,740 --> 00:06:33,300
如果说我们想直接获得取title就可以通过这样一个东西

198
00:06:33,300 --> 00:06:34,840
但是这里可能有的同学

199
00:06:34,840 --> 00:06:36,620
又想到一个问题什么问题呢

200
00:06:36,620 --> 00:06:37,660
如果说我

201
00:06:38,420 --> 00:06:39,960
既想获得title又想获得ID怎么办

202
00:06:39,960 --> 00:06:42,780
ID在不在post的闪电的属性里面呢

203
00:06:42,780 --> 00:06:45,080
不在吧大家可以注意到看大家可以注意看

204
00:06:45,080 --> 00:06:48,660
我们的post2他是箭直吧但是他不是ID

205
00:06:48,660 --> 00:06:52,240
他的属性里面没有ID这项那我们如果想获取排序之后同时获取ID怎么办

206
00:06:52,240 --> 00:06:53,780
我们呢其实可以利用

207
00:06:53,780 --> 00:06:54,800
get

208
00:06:54,800 --> 00:06:56,860
警号警号就代表

209
00:06:56,860 --> 00:07:00,440
其实警号代表什么代表新号里面那种新号是不是就是我们的ID

210
00:07:00,440 --> 00:07:02,480
我们来看一下结果是不是3241

211
00:07:02,480 --> 00:07:04,280
叉叉叉叉叉叉叉叉是不是代表我们的

212
00:07:04,280 --> 00:07:05,040
title

213
00:07:06,580 --> 00:07:10,160
这里就是我们通过by来进行时间排序的一个方法

214
00:07:10,160 --> 00:07:11,440
我们来回顾一下

215
00:07:11,440 --> 00:07:21,940
我们刚才是不是讲到使用

216
00:07:21,940 --> 00:07:26,040
使用by来进行

217
00:07:26,040 --> 00:07:27,320
排序

218
00:07:27,320 --> 00:07:29,360
那么by他是为什么进行排序

219
00:07:29,360 --> 00:07:29,880
可以

220
00:07:29,880 --> 00:07:33,200
是不是可以对散烈进行排序

221
00:07:33,200 --> 00:07:35,000
而且可以根据其中的某一个属性吧

222
00:07:36,020 --> 00:07:37,580
我们刚才是不是根据时间来排序的

223
00:07:37,580 --> 00:07:38,560
那么他的语法是什么

224
00:07:38,560 --> 00:07:40,620
他的语法是不是sort

225
00:07:40,620 --> 00:07:41,700
sort的什么列表吧

226
00:07:41,700 --> 00:07:43,860
然后呢by什么

227
00:07:43,860 --> 00:07:44,540
闪列

228
00:07:44,540 --> 00:07:46,900
闪列我们是不是要冒号

229
00:07:46,900 --> 00:07:49,800
其实叫做闪列的字段名吧

230
00:07:49,800 --> 00:07:51,360
然后新

231
00:07:51,360 --> 00:07:52,760
你如果说想获取哪个属性

232
00:07:52,760 --> 00:07:53,400
是不是一个箭头

233
00:07:53,400 --> 00:07:55,420
属性名对吧

234
00:07:55,420 --> 00:07:56,100
然后呢

235
00:07:56,100 --> 00:07:58,660
此时是不是就可以进行排序了

236
00:07:58,660 --> 00:07:59,720
那么如果说你想去

237
00:07:59,720 --> 00:08:00,980
比如说你想从大到小

238
00:08:00,980 --> 00:08:02,040
是不是可以加入第一sc

239
00:08:02,040 --> 00:08:02,920
而且呢

240
00:08:02,920 --> 00:08:04,140
我们刚才是不是还讲到一个什么

241
00:08:04,140 --> 00:08:05,240
是不是还讲到一个

242
00:08:05,240 --> 00:08:10,440
get 呀 对吧 那么get 它是做什么用的 是不是可以

243
00:08:10,440 --> 00:08:14,600
可以直接返回属性了

244
00:08:14,600 --> 00:08:20,440
怎么使用 怎么使用 是不是我们直接通过get 比如说你

245
00:08:20,440 --> 00:08:22,040
闪链的

246
00:08:22,040 --> 00:08:23,640
字段名

247
00:08:23,640 --> 00:08:26,000
是不是还是猫好心

248
00:08:26,000 --> 00:08:29,300
箭头属性名吧

249
00:08:29,300 --> 00:08:32,240
那么如果说你想返回 id 呢

250
00:08:32,240 --> 00:08:34,940
直接返回 id 怎么办

251
00:08:35,240 --> 00:08:37,040
是不是直接get

252
00:08:37,040 --> 00:08:38,180
紧了对吧

253
00:08:38,180 --> 00:08:40,240
好这里呢就是我们这节课的内容

254
00:08:40,240 --> 00:08:42,640
by 虽然说他很复杂

255
00:08:42,640 --> 00:08:45,040
但是其实我们使用起来还稍微也比较好理解吧

256
00:08:45,040 --> 00:08:47,840
其实他就是有点类似于咱们的一个负循环

257
00:08:47,840 --> 00:08:49,640
好希望大家能够好好理解一下

258
00:08:49,640 --> 00:08:51,040
好我们先把视频停一下

