1
00:00:00,000 --> 00:00:02,860
好,这节目我们来看一下有序集合中的一些命令

2
00:00:02,860 --> 00:00:04,380
首先第一个增加元素

3
00:00:04,380 --> 00:00:06,520
增加元素使用的是zadd

4
00:00:06,520 --> 00:00:08,040
我们来看一下zadd

5
00:00:08,040 --> 00:00:09,100
add谁呢?noms

6
00:00:09,100 --> 00:00:12,680
比如说我去添加一些同学他的分数

7
00:00:12,680 --> 00:00:13,960
89分

8
00:00:13,960 --> 00:00:14,400
Tom

9
00:00:14,400 --> 00:00:15,780
67

10
00:00:15,780 --> 00:00:16,440
Peter

11
00:00:16,440 --> 00:00:18,360
100分

12
00:00:18,360 --> 00:00:19,140
David

13
00:00:19,140 --> 00:00:20,100
好

14
00:00:20,100 --> 00:00:22,560
我们已经添加成功了

15
00:00:22,560 --> 00:00:24,040
好,这里假如说

16
00:00:24,040 --> 00:00:26,800
我们发现Peter他的分数我们录错了

17
00:00:26,800 --> 00:00:27,920
应该是76分

18
00:00:27,920 --> 00:00:28,760
但是我们录成了67

19
00:00:28,760 --> 00:00:29,180
怎么办

20
00:00:29,180 --> 00:00:30,520
我们可以直接这样

21
00:00:30,520 --> 00:00:31,140
直接add

22
00:00:31,140 --> 00:00:32,260
nums

23
00:00:32,260 --> 00:00:33,880
76

24
00:00:33,880 --> 00:00:34,740
Peter

25
00:00:34,740 --> 00:00:36,820
这里我们就已经修改成功了

26
00:00:36,820 --> 00:00:40,160
那么我们的分数

27
00:00:40,160 --> 00:00:41,200
不仅可以是整数

28
00:00:41,200 --> 00:00:42,280
还可以是一些浮点数

29
00:00:42,280 --> 00:00:44,060
比如说我们可以add某一个

30
00:00:44,060 --> 00:00:45,700
很大的数字

31
00:00:45,700 --> 00:00:46,660
包括了一些浮点数

32
00:00:46,660 --> 00:00:47,680
包括咱们的

33
00:00:47,680 --> 00:00:49,800
正的inf和负的inf

34
00:00:49,800 --> 00:00:50,360
它是什么意思

35
00:00:50,360 --> 00:00:52,900
其实它是正无穷大和负无穷大的意思

36
00:00:52,900 --> 00:00:54,380
那么我们就来看一下

37
00:00:54,380 --> 00:00:55,540
怎么去获得元素的分数

38
00:00:55,540 --> 00:00:57,700
刚才我们是不是已经改了Peter

39
00:00:57,700 --> 00:00:59,000
改了Peter之后

40
00:00:59,000 --> 00:00:59,940
我们来获取看一下

41
00:00:59,940 --> 00:01:02,700
获取它的命令叫做

42
00:01:02,700 --> 00:01:03,300
贼斯扣

43
00:01:03,300 --> 00:01:04,600
贼斯扣

44
00:01:04,600 --> 00:01:06,540
获取谁呢

45
00:01:06,540 --> 00:01:07,960
刚才我们改的谁是不是皮特

46
00:01:07,960 --> 00:01:09,020
我们看一下是不是76

47
00:01:09,020 --> 00:01:09,760
大家可以看到

48
00:01:09,760 --> 00:01:11,000
确实是76

49
00:01:11,000 --> 00:01:13,360
接下来我们来看一下第三个命令

50
00:01:13,360 --> 00:01:16,160
获得排名在某个范围的元素列表

51
00:01:16,160 --> 00:01:17,620
什么意思

52
00:01:17,620 --> 00:01:18,900
什么意思

53
00:01:18,900 --> 00:01:23,100
我们刚才是不是添加了三个人

54
00:01:23,100 --> 00:01:24,180
Tom,Peter和David

55
00:01:24,180 --> 00:01:26,460
他们的分数分别是89,67,100分

56
00:01:26,460 --> 00:01:28,620
假如说我们想去获取

57
00:01:28,620 --> 00:01:31,960
假如说我们想去获取一个范围

58
00:01:31,960 --> 00:01:36,040
获取一个范围

59
00:01:36,040 --> 00:01:40,340
好 首先我们来看一下这样一个命令

60
00:01:40,340 --> 00:01:41,060
贼乱几

61
00:01:41,060 --> 00:01:42,860
获取什么样的范围

62
00:01:42,860 --> 00:01:44,720
咱们贼乱几

63
00:01:44,720 --> 00:01:48,120
Nams 0到2

64
00:01:48,120 --> 00:01:50,320
Peter Tom David

65
00:01:50,320 --> 00:01:53,160
Peter呢 100分

66
00:01:53,160 --> 00:01:55,100
David Peter 67

67
00:01:55,100 --> 00:01:55,920
David 100分

68
00:01:55,920 --> 00:01:56,800
皮特

69
00:01:56,800 --> 00:01:58,040
皮特

70
00:01:58,040 --> 00:01:59,960
皮特67

71
00:01:59,960 --> 00:02:00,600
Tom 89

72
00:02:00,600 --> 00:02:02,220
达威德呢

73
00:02:02,220 --> 00:02:03,000
100分

74
00:02:03,000 --> 00:02:04,340
说明了咱们去执行

75
00:02:04,340 --> 00:02:04,700
得乱击

76
00:02:04,700 --> 00:02:06,640
他是按什么一个顺序去执行的呀

77
00:02:06,640 --> 00:02:07,300
是不是按一个

78
00:02:07,300 --> 00:02:08,720
升序啊

79
00:02:08,720 --> 00:02:09,520
皮特的分最少

80
00:02:09,520 --> 00:02:11,700
那么他会逐渐逐渐的按照升序

81
00:02:11,700 --> 00:02:13,180
去进行一个排序

82
00:02:13,180 --> 00:02:13,980
那假如说我们去

83
00:02:13,980 --> 00:02:14,600
得乱击

84
00:02:14,600 --> 00:02:15,760
那是一个

85
00:02:15,760 --> 00:02:19,200
2到2

86
00:02:19,200 --> 00:02:20,480
也就是咱们最大的一个值

87
00:02:20,480 --> 00:02:21,460
他是不是应该

88
00:02:21,460 --> 00:02:22,300
打算说谁

89
00:02:22,300 --> 00:02:23,380
达威德吧

90
00:02:23,380 --> 00:02:24,260
达威德

91
00:02:24,260 --> 00:02:26,200
好

92
00:02:26,200 --> 00:02:27,340
那么我们如果说

93
00:02:27,340 --> 00:02:32,600
我们如果说需要同时去获取我们元素的分数

94
00:02:32,600 --> 00:02:34,300
就可以在咱们Zruns的命令尾部

95
00:02:34,300 --> 00:02:36,420
加上with scores这样一个参数

96
00:02:36,420 --> 00:02:37,280
那我们来试一下

97
00:02:37,280 --> 00:02:41,580
Zruns 0到-1

98
00:02:41,580 --> 00:02:42,580
加上一个参数什么

99
00:02:42,580 --> 00:02:44,180
with scores

100
00:02:44,180 --> 00:02:45,540
好大家可以看到

101
00:02:45,540 --> 00:02:47,300
Peter 76 89 100

102
00:02:47,300 --> 00:02:48,140
这里呢

103
00:02:48,140 --> 00:02:50,240
它会携带上咱们存储的值的分数

104
00:02:50,240 --> 00:02:52,000
那么还有

105
00:02:52,000 --> 00:02:55,280
如果说我们

106
00:02:55,280 --> 00:02:57,180
我们现在是降序

107
00:02:57,180 --> 00:02:57,820
现在是升序

108
00:02:57,820 --> 00:02:59,600
那么我们如果说需要去降序排序怎么办

109
00:02:59,600 --> 00:03:00,400
一摆他在前面

110
00:03:00,400 --> 00:03:01,160
89 76

111
00:03:01,160 --> 00:03:03,100
我们可以用Z21RUNGE

112
00:03:03,100 --> 00:03:04,600
咱们来看一下

113
00:03:04,600 --> 00:03:08,340
ZREVRUNGENOMS 0-1

114
00:03:08,340 --> 00:03:10,860
WITH SCORES

115
00:03:10,860 --> 00:03:12,320
好大家可以看到

116
00:03:12,320 --> 00:03:13,620
现在我们是不是降序排序

117
00:03:13,620 --> 00:03:15,820
那么我们的排序规则

118
00:03:15,820 --> 00:03:18,680
不仅仅可以去基于咱们的数字去排序

119
00:03:18,680 --> 00:03:21,400
而且我们还可以根据我们字典的一个顺序

120
00:03:21,400 --> 00:03:23,960
你比如说它会按照咱们的一个编码顺序

121
00:03:23,960 --> 00:03:25,660
比如说咱们的0和9它在前面

122
00:03:25,660 --> 00:03:27,640
后面是根据字母这样的顺序去排

123
00:03:27,640 --> 00:03:29,640
那么如果说我们的元素是中文怎么办

124
00:03:29,640 --> 00:03:31,980
它也是取决于我们中文的一个编码方式

125
00:03:31,980 --> 00:03:33,420
你比如说你去存储一些中文

126
00:03:33,420 --> 00:03:35,240
马华 牛庸 什么司马光等等

127
00:03:35,240 --> 00:03:38,940
它会按照它中文编码的一个顺序去进行排序

128
00:03:38,940 --> 00:03:45,640
好 我们来看一下第四个命令

129
00:03:45,640 --> 00:03:47,640
获得指定分数范围

130
00:03:47,640 --> 00:03:49,520
指定分数范围的元素

131
00:03:49,520 --> 00:03:51,800
什么意思啊

132
00:03:51,800 --> 00:03:52,980
也就是说咱们现在有三个分数

133
00:03:52,980 --> 00:03:54,600
189 76

134
00:03:54,600 --> 00:03:55,620
我们想获得

135
00:03:55,620 --> 00:03:57,440
假设我们想获得80分到100分

136
00:03:57,440 --> 00:03:57,960
它的一个区间

137
00:03:57,960 --> 00:03:58,500
我们来看一下

138
00:03:58,500 --> 00:04:00,580
这个命令是什么呢

139
00:04:00,580 --> 00:04:02,080
Zerang by score

140
00:04:02,080 --> 00:04:02,600
根据谁啊

141
00:04:02,600 --> 00:04:03,140
根据分数

142
00:04:03,140 --> 00:04:07,300
Zerang by score

143
00:04:07,300 --> 00:04:10,360
Narms

144
00:04:10,360 --> 00:04:11,880
我们来看一下80分到100分

145
00:04:11,880 --> 00:04:13,420
是不是有Tom和David啊

146
00:04:13,420 --> 00:04:15,820
那么同时它的语法呢

147
00:04:15,820 --> 00:04:16,340
也可以

148
00:04:16,340 --> 00:04:17,140
比如说它

149
00:04:17,140 --> 00:04:18,200
现在咱们Narms

150
00:04:18,200 --> 00:04:19,840
是不是大于等于80小于等于100

151
00:04:19,840 --> 00:04:21,160
那么我们呢也可以

152
00:04:21,160 --> 00:04:23,100
假如说如果说我们这样去写

153
00:04:23,100 --> 00:04:25,100
zrunge by com

154
00:04:25,100 --> 00:04:26,840
noms 80

155
00:04:26,840 --> 00:04:28,880
半个括号100

156
00:04:28,880 --> 00:04:30,220
这里呢它的意思就是说

157
00:04:30,220 --> 00:04:32,200
大于等于80小于100

158
00:04:32,200 --> 00:04:34,260
zrunge nom

159
00:04:34,260 --> 00:04:36,880
我们命令写错了

160
00:04:36,880 --> 00:04:38,680
命令写错了

161
00:04:38,680 --> 00:04:39,900
好没关系

162
00:04:39,900 --> 00:04:43,400
好我们再来看一下

163
00:04:43,400 --> 00:04:43,860
大家看到没有

164
00:04:43,860 --> 00:04:45,000
Tom为什么呀

165
00:04:45,000 --> 00:04:46,700
他呢就把David排除在外了

166
00:04:46,700 --> 00:04:47,660
因为David是100分

167
00:04:47,660 --> 00:04:50,160
但是我们现在的规则是大于等于80而小于100

168
00:04:50,160 --> 00:04:56,240
同时呢我们我们的分数他也支持我们的

169
00:04:56,240 --> 00:04:57,640
正无穷大

170
00:04:57,640 --> 00:04:59,700
比如说啊比如说我这样去写这乱

171
00:04:59,700 --> 00:05:01,220
这乱乱

172
00:05:01,220 --> 00:05:03,540
那

173
00:05:03,540 --> 00:05:05,060
假如说我们去大于80分

174
00:05:05,060 --> 00:05:06,860
然后呢小于什么小于一个正无穷大

175
00:05:06,860 --> 00:05:09,940
痛搭配的因为你有时候

176
00:05:09,940 --> 00:05:10,440
你

177
00:05:10,440 --> 00:05:16,340
有一个需求你想获取大于80分的同学但是你不知道最高分有多少这时呢你就可以去用正无穷大同样的

178
00:05:16,580 --> 00:05:18,580
你有时候想获取比如说80分以下的

179
00:05:18,580 --> 00:05:19,740
你呢不知道最低分是多少

180
00:05:19,740 --> 00:05:22,240
你那就可以用-inf 也就是咱们的-无穷的

181
00:05:22,240 --> 00:05:27,040
好 那么接下来呢

182
00:05:27,040 --> 00:05:28,160
我们去进行

183
00:05:28,160 --> 00:05:29,500
咱们取一个分数范围

184
00:05:29,500 --> 00:05:30,640
在runge by score的时候呢

185
00:05:30,640 --> 00:05:32,620
也可以加上with scores这样一个参数

186
00:05:32,620 --> 00:05:33,760
去呢把我们

187
00:05:33,760 --> 00:05:35,860
tom和david他对应的分数

188
00:05:35,860 --> 00:05:37,100
给取出来

189
00:05:37,100 --> 00:05:40,700
比如说我们去with scores

190
00:05:40,700 --> 00:05:41,200
大家看到没有

191
00:05:41,200 --> 00:05:42,420
就可以把分数给带上

192
00:05:42,420 --> 00:05:46,360
好 那么如果说我们还有一个需求

193
00:05:46,360 --> 00:05:50,380
如果说我们想获取分数低于或等于100分的前三个人怎么办

194
00:05:50,380 --> 00:05:52,440
也就是说我们现在有个需求是什么呢

195
00:05:52,440 --> 00:05:53,740
我们现在有个需求

196
00:05:53,740 --> 00:05:56,740
比如说我们班上有三个同学分别是76分

197
00:05:56,740 --> 00:05:59,560
分别是76 89 100分

198
00:05:59,560 --> 00:06:01,840
我现在想获取小于90分的

199
00:06:01,840 --> 00:06:04,360
小于90分的所有同学中的

200
00:06:04,360 --> 00:06:05,640
第一个是谁

201
00:06:05,640 --> 00:06:06,380
是不是Peter

202
00:06:06,380 --> 00:06:07,640
那么我们怎么样去做呢

203
00:06:07,640 --> 00:06:09,120
其实就可以这样去做

204
00:06:09,120 --> 00:06:12,300
咱们可以在runs by score

205
00:06:12,300 --> 00:06:13,580
runs

206
00:06:13,580 --> 00:06:15,960
假如说我们需要去

207
00:06:15,960 --> 00:06:17,860
假如说我们需要去

208
00:06:17,860 --> 00:06:21,000
小于100

209
00:06:21,000 --> 00:06:23,120
小于100

210
00:06:23,120 --> 00:06:24,400
就是-inf

211
00:06:24,400 --> 00:06:25,900
比如说我们去小于

212
00:06:25,900 --> 00:06:27,260
小于90分吧

213
00:06:27,260 --> 00:06:28,540
小于90

214
00:06:28,540 --> 00:06:30,300
然后呢

215
00:06:30,300 --> 00:06:30,940
第一个人

216
00:06:30,940 --> 00:06:31,960
咱们可以加上一个limit

217
00:06:31,960 --> 00:06:33,540
这里我大写

218
00:06:33,540 --> 00:06:34,420
来提示大家limit

219
00:06:34,420 --> 00:06:38,940
好大家看到没有

220
00:06:38,940 --> 00:06:40,260
我们是不是获取到了Peter

221
00:06:40,260 --> 00:06:40,980
Peter是多少分

222
00:06:40,980 --> 00:06:41,580
Peter

223
00:06:41,580 --> 00:06:42,540
Peter是不是76

224
00:06:42,540 --> 00:06:44,860
这里呢就实现我们需求

225
00:06:44,860 --> 00:06:46,480
获得小于90分的第一个人

226
00:06:46,480 --> 00:06:47,560
为什么呀

227
00:06:47,560 --> 00:06:49,700
根据分数去排名

228
00:06:49,700 --> 00:06:50,600
排名谁

229
00:06:50,600 --> 00:06:51,680
咱们的LAMS

230
00:06:51,680 --> 00:06:52,720
LAMS里面有三个人

231
00:06:52,720 --> 00:06:53,380
范围是什么

232
00:06:53,380 --> 00:06:55,540
富五穷大到小于90分的同学

233
00:06:55,540 --> 00:06:57,920
是不是有Tom和Peter

234
00:06:57,920 --> 00:06:59,600
那么呢咱们还会有限制条件

235
00:06:59,600 --> 00:07:00,900
就是第一个人LIMIT01

236
00:07:00,900 --> 00:07:01,800
所以呢是Peter

237
00:07:01,800 --> 00:07:04,020
好我们来看一下

238
00:07:04,020 --> 00:07:04,980
第五个命令

239
00:07:04,980 --> 00:07:06,400
增加某个元素的分数

240
00:07:06,400 --> 00:07:08,020
增加某个元素的分数

241
00:07:08,020 --> 00:07:09,660
这里是不是就很简单了

242
00:07:09,660 --> 00:07:11,380
比如说咱们JNCR

243
00:07:11,380 --> 00:07:12,300
by

244
00:07:12,300 --> 00:07:14,340
我们要获取nums

245
00:07:14,340 --> 00:07:15,020
peter

246
00:07:15,020 --> 00:07:16,040
peter分有点低

247
00:07:16,040 --> 00:07:16,860
我们给他加一点

248
00:07:16,860 --> 00:07:18,480
我们给他加4分

249
00:07:18,480 --> 00:07:19,620
给他加到80分

250
00:07:19,620 --> 00:07:22,040
value is not a valid float

251
00:07:22,040 --> 00:07:27,620
哦 写反了

252
00:07:27,620 --> 00:07:28,340
参数写反了

253
00:07:28,340 --> 00:07:29,900
分数在前面

254
00:07:29,900 --> 00:07:31,940
zincrbynums

255
00:07:31,940 --> 00:07:34,160
nums 4分加给谁

256
00:07:34,160 --> 00:07:34,460
peter

257
00:07:34,460 --> 00:07:37,180
好 此时peter是不是已经涨到了80分

258
00:07:37,180 --> 00:07:38,760
好 那我到这里了

259
00:07:38,760 --> 00:07:39,320
我们一起来

260
00:07:39,320 --> 00:07:40,760
总结一下

261
00:07:40,760 --> 00:07:45,700
刚才我们是不是讲了咱们什么集合

262
00:07:45,700 --> 00:07:47,360
有序集合吧

263
00:07:47,360 --> 00:07:48,620
讲了什么命令

264
00:07:48,620 --> 00:07:50,000
第一个命令我们讲的是什么

265
00:07:50,000 --> 00:07:51,180
是不是增加元素啊

266
00:07:51,180 --> 00:07:52,680
在有序集合里面增加元素

267
00:07:52,680 --> 00:07:53,580
是不是zadd

268
00:07:53,580 --> 00:07:54,900
然后呢

269
00:07:54,900 --> 00:07:56,340
还讲了咱们怎么样去获得

270
00:07:56,340 --> 00:07:58,020
咱们有序集合的一个分数

271
00:07:58,020 --> 00:07:59,160
获取分数

272
00:07:59,160 --> 00:07:59,840
获取分数

273
00:07:59,840 --> 00:08:00,920
通过什么呢

274
00:08:00,920 --> 00:08:02,120
叫做zscore

275
00:08:02,120 --> 00:08:05,920
然后获取分数之后

276
00:08:05,920 --> 00:08:07,160
我们是不是需要去

277
00:08:07,160 --> 00:08:08,700
根据咱们的一个范围

278
00:08:08,700 --> 00:08:09,680
获取我们的元素列表

279
00:08:10,440 --> 00:08:10,960
通过什么呀

280
00:08:10,960 --> 00:08:13,000
zrunge和

281
00:08:13,000 --> 00:08:15,320
zrunge是升序还是降序同学们

282
00:08:15,320 --> 00:08:16,600
是不是升序啊

283
00:08:16,600 --> 00:08:18,120
那么如果我们想降序呢

284
00:08:18,120 --> 00:08:19,920
z21vrunge

285
00:08:19,920 --> 00:08:21,200
降序

286
00:08:21,200 --> 00:08:26,060
降序

287
00:08:26,060 --> 00:08:28,880
那么我们除了讲解咱们的一个

288
00:08:28,880 --> 00:08:30,420
获取范围是不是还讲到了

289
00:08:30,420 --> 00:08:32,460
还讲到了你可以通过

290
00:08:32,460 --> 00:08:34,260
获得指定分数范围呀

291
00:08:34,260 --> 00:08:35,020
获取

292
00:08:35,020 --> 00:08:36,040
指定

293
00:08:36,040 --> 00:08:38,100
分数范围的

294
00:08:38,100 --> 00:08:39,120
元素

295
00:08:39,680 --> 00:08:41,760
命令是什么

296
00:08:41,760 --> 00:08:45,000
they run by score

297
00:08:45,000 --> 00:08:47,240
是不是很流畅和咱们的英文是一样的

298
00:08:47,240 --> 00:08:50,700
比如说I want you find number by score

299
00:08:50,700 --> 00:08:52,640
咱们的命令和我们平时的生活

300
00:08:52,640 --> 00:08:54,980
咱们的思维是比较相似

301
00:08:54,980 --> 00:08:59,300
那么获取分数范围有什么特点

302
00:08:59,300 --> 00:09:01,360
是不是可以大于等于也可以小于

303
00:09:01,360 --> 00:09:03,180
等于包括也可以小于

304
00:09:03,180 --> 00:09:04,440
还可以正五穷大负五穷大

305
00:09:04,440 --> 00:09:04,860
对吧

306
00:09:04,860 --> 00:09:06,700
比如说正inf-inf

307
00:09:06,700 --> 00:09:08,420
然后还可以带上一个括号

308
00:09:08,420 --> 00:09:09,180
代表什么意思

309
00:09:09,180 --> 00:09:10,980
是不是代表小于90分对吧

310
00:09:10,980 --> 00:09:11,420
好

311
00:09:11,420 --> 00:09:13,640
那么我们获取了指定分数的元素

312
00:09:13,640 --> 00:09:14,580
还需要什么

313
00:09:14,580 --> 00:09:15,580
还学了一个什么命令

314
00:09:15,580 --> 00:09:16,460
你比如说Peter

315
00:09:16,460 --> 00:09:17,580
我给他卷子改错了

316
00:09:17,580 --> 00:09:18,800
给他加几分对吧

317
00:09:18,800 --> 00:09:19,520
增加分数

318
00:09:19,520 --> 00:09:21,200
那么提到增加

319
00:09:21,200 --> 00:09:22,420
我们想到一个词incr

320
00:09:22,420 --> 00:09:24,200
所以在这里就是zincr

321
00:09:24,200 --> 00:09:25,320
要加上by

322
00:09:25,320 --> 00:09:25,840
好

323
00:09:25,840 --> 00:09:27,520
这里呢就是我们这一节课的内容

