1
00:00:00,000 --> 00:00:03,040
TCP是面向临阶的协议

2
00:00:03,040 --> 00:00:04,800
它最重要的一个特征呢

3
00:00:04,800 --> 00:00:05,800
就是在传输之前

4
00:00:05,800 --> 00:00:07,940
需要去进行三次握手绘画

5
00:00:07,940 --> 00:00:09,820
什么是三次握手绘画呢

6
00:00:09,820 --> 00:00:10,940
其实就是

7
00:00:10,940 --> 00:00:12,020
我们在这可以看一下

8
00:00:12,020 --> 00:00:13,080
我们有个客户端

9
00:00:13,080 --> 00:00:14,060
有个服务器端

10
00:00:14,060 --> 00:00:16,040
那么双方要想进行通信

11
00:00:16,040 --> 00:00:18,140
我们就需要先确保

12
00:00:18,140 --> 00:00:20,640
双方都能互相收到对方的数据

13
00:00:20,640 --> 00:00:22,200
所以大家在这呢

14
00:00:22,200 --> 00:00:22,820
就可以来看到

15
00:00:22,820 --> 00:00:25,080
首先我们客户端

16
00:00:25,080 --> 00:00:27,120
或者说我们发送数据的A端

17
00:00:27,120 --> 00:00:29,620
先发一个信号给B端

18
00:00:29,620 --> 00:00:31,580
那么数据发过去以后

19
00:00:31,580 --> 00:00:32,540
那么对于A端来讲

20
00:00:32,540 --> 00:00:34,840
他就他只是把数据发出去了

21
00:00:34,840 --> 00:00:36,640
但是他并不知道这个B端

22
00:00:36,640 --> 00:00:38,920
到底有没有收到自己发的数据

23
00:00:38,920 --> 00:00:40,420
然后所以说接下来这个B端

24
00:00:40,420 --> 00:00:41,060
就怎么着呢

25
00:00:41,060 --> 00:00:42,300
再回他一个信号

26
00:00:42,300 --> 00:00:43,600
就告诉他你这个数据

27
00:00:43,600 --> 00:00:45,260
我能是正确收到

28
00:00:45,260 --> 00:00:47,320
那B端把这个数据发来以后

29
00:00:47,320 --> 00:00:49,040
那A端现在就确保

30
00:00:49,040 --> 00:00:52,280
我一定能把数据正确的发给这个B端

31
00:00:52,280 --> 00:00:54,320
就是说这个B端是存在的

32
00:00:54,320 --> 00:00:55,380
但这个时候有一个问题

33
00:00:55,380 --> 00:00:57,580
就是说B端把这个数据发过来以后

34
00:00:57,580 --> 00:00:59,260
这个A端他并不清楚

35
00:00:59,260 --> 00:01:00,780
或者说B端并不知道

36
00:01:00,780 --> 00:01:03,400
我这个数据能不能发给这个A端

37
00:01:03,400 --> 00:01:06,060
因为这也是B端第一次发给这个A端

38
00:01:06,060 --> 00:01:06,880
所以说接下来

39
00:01:06,880 --> 00:01:08,860
当A端收到这个想象数据的时候

40
00:01:08,860 --> 00:01:11,180
再发一个数据给这个B端

41
00:01:11,180 --> 00:01:11,780
那这样的话

42
00:01:11,780 --> 00:01:15,460
他们就能确保双方都能去收到对方的数据了

43
00:01:15,460 --> 00:01:18,260
就是说A端能确保B端能收到自己发的数据

44
00:01:18,260 --> 00:01:21,520
B端能确保A端能收到自己发的数据

45
00:01:21,520 --> 00:01:23,120
这个就是所谓的三次握手连接

46
00:01:23,120 --> 00:01:24,240
好

47
00:01:24,240 --> 00:01:26,020
那么只有当通过这个三次握手

48
00:01:26,020 --> 00:01:27,640
我们形成绘画之后

49
00:01:27,640 --> 00:01:28,840
服务端和客户端之间

50
00:01:28,840 --> 00:01:30,700
才能进行互相发送数据

51
00:01:30,700 --> 00:01:32,780
那么在创建这个绘画的过程当中

52
00:01:32,780 --> 00:01:34,420
服务端和这个客户端呢

53
00:01:34,420 --> 00:01:36,180
分别会提供一个套接字

54
00:01:36,180 --> 00:01:37,780
什么是套接字呢

55
00:01:37,780 --> 00:01:39,180
其实也就是所谓的端口

56
00:01:39,180 --> 00:01:40,400
端口就是一个终点

57
00:01:40,400 --> 00:01:40,880
一个端点

58
00:01:40,880 --> 00:01:41,960
那么这个套接字

59
00:01:41,960 --> 00:01:43,640
其实就是代表着对方的

60
00:01:43,640 --> 00:01:45,000
IP地址和它的端口号

61
00:01:45,000 --> 00:01:46,900
那么也就是接下来

62
00:01:46,900 --> 00:01:47,720
我们就要通过什么呢

63
00:01:47,720 --> 00:01:48,820
通过这两个套接字

64
00:01:48,820 --> 00:01:50,300
就是形成一个连接

65
00:01:50,300 --> 00:01:51,840
然后服务端和客户端

66
00:01:51,840 --> 00:01:53,640
就通过这个套接字

67
00:01:53,640 --> 00:01:55,580
去实现这个通信的

68
00:01:55,580 --> 00:01:56,440
相关的一些操作

69
00:01:56,440 --> 00:01:57,900
所以它就是这样的一个

70
00:01:57,900 --> 00:02:00,240
谢谢大家

