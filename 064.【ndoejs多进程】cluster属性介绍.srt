1
00:00:00,000 --> 00:00:02,820
这节课呢我们就来学习cluster

2
00:00:02,820 --> 00:00:04,440
那么cluster怎么样去学习呢

3
00:00:04,440 --> 00:00:07,920
首先它是nodejs里面的一个模块

4
00:00:07,920 --> 00:00:10,520
什么模块呢

5
00:00:10,520 --> 00:00:12,080
是不是进行多进程的模块啊

6
00:00:12,080 --> 00:00:14,600
好那么呢它会有一些属性

7
00:00:14,600 --> 00:00:15,840
还有一些方法

8
00:00:15,840 --> 00:00:17,600
包括了它可以监听一些事件

9
00:00:17,600 --> 00:00:19,200
那么我们这几个怎么样去学习

10
00:00:19,200 --> 00:00:22,120
我们cluster老师呢会带着同学们去看

11
00:00:22,120 --> 00:00:24,220
咱们nodejs里面的cluster里面的文档

12
00:00:24,220 --> 00:00:26,360
因为老师之前是不是讲过

13
00:00:26,360 --> 00:00:27,620
我们一定要学会去看文档

14
00:00:27,620 --> 00:00:28,600
所以呢cluster学习

15
00:00:28,600 --> 00:00:30,480
我就带着同学们去一个一个的

16
00:00:30,480 --> 00:00:32,800
去进行他们API以及世界的学习

17
00:00:32,800 --> 00:00:35,080
这里呢可能会分为几个视频去讲

18
00:00:35,080 --> 00:00:37,880
好首先我们来看一下class里面的一些属性

19
00:00:37,880 --> 00:00:40,060
它比较重点的属性总共有三个

20
00:00:40,060 --> 00:00:41,900
一只master 一只walker和fork

21
00:00:41,900 --> 00:00:42,920
fork是一个方法

22
00:00:42,920 --> 00:00:43,620
它是做什么呢

23
00:00:43,620 --> 00:00:47,300
其实就是咱们来fork开启咱们的一个进程

24
00:00:47,300 --> 00:00:48,520
叫做walker

25
00:00:48,520 --> 00:00:49,400
返回一个work对象

26
00:00:49,400 --> 00:00:50,800
是不是和咱们的processchew的很类似

27
00:00:50,800 --> 00:00:52,020
fork的时候返回一个对象

28
00:00:52,020 --> 00:00:53,060
然后产生一个pipe

29
00:00:53,060 --> 00:00:55,320
pipe可以进行复制之间的一个通信

30
00:00:55,320 --> 00:00:56,700
好我们就来看一下

31
00:00:56,700 --> 00:00:58,760
之前我们

32
00:00:58,760 --> 00:01:02,040
是不是写了一个cluster的例子

33
00:01:02,040 --> 00:01:03,260
leadsmaster

34
00:01:03,260 --> 00:01:05,540
咱们在cluster里面

35
00:01:05,540 --> 00:01:07,060
首先它有一个leadsmaster

36
00:01:07,060 --> 00:01:08,640
是不是去判断你是不是主线层

37
00:01:08,640 --> 00:01:09,300
如果说你是的

38
00:01:09,300 --> 00:01:11,120
然后就去fork一个纸线层出来

39
00:01:11,120 --> 00:01:12,800
然后如果说你是紫禁层

40
00:01:12,800 --> 00:01:13,920
就去create一个server

41
00:01:13,920 --> 00:01:15,400
一个http的一个服务器

42
00:01:15,400 --> 00:01:16,860
那么这里呢

43
00:01:16,860 --> 00:01:18,320
我们就来重新写一个cluster

44
00:01:18,320 --> 00:01:19,180
首先

45
00:01:19,180 --> 00:01:24,980
cluster等于requirecluster

46
00:01:26,700 --> 00:01:30,540
好 我们怎么样去fork一个止进程呢

47
00:01:30,540 --> 00:01:33,100
是不是首先我们要通过判断了

48
00:01:33,100 --> 00:01:43,860
if class.is master 它是不是有两个属性

49
00:01:43,860 --> 00:01:48,200
一个 is master 一个 is worker 分别去判断它是主进程还是工作进程

50
00:01:48,200 --> 00:01:52,300
好 那么如果说你是主进程 咱们呢就是干嘛呀 是不是去判断

51
00:01:52,300 --> 00:01:53,060
哇

52
00:01:53,060 --> 00:01:55,380
cpu的核数

53
00:01:56,660 --> 00:02:03,580
等于requireOS.cpu.lens

54
00:02:03,580 --> 00:02:05,620
是不是如果

55
00:02:05,620 --> 00:02:08,700
写的是不是有个缝行环for

56
00:02:08,700 --> 00:02:13,040
yi等于0i小于cpu的长度

57
00:02:13,040 --> 00:02:15,340
i加加

58
00:02:15,340 --> 00:02:17,140
然后呢咱们通过

59
00:02:17,140 --> 00:02:22,260
cluster去调用它的fork方法这样呢咱们就开启了多个进程

60
00:02:22,260 --> 00:02:24,820
如果说它是紫进程咱们打印一个

61
00:02:26,660 --> 00:02:30,160
我是紫进程

62
00:02:30,160 --> 00:02:35,560
好 我们来看一下运行一下

63
00:02:35,560 --> 00:02:40,400
先退出去

64
00:02:40,400 --> 00:02:42,840
cd进入cluster模块

65
00:02:42,840 --> 00:02:45,960
咱们刚才是min.js

66
00:02:45,960 --> 00:02:48,700
大家看到没有

67
00:02:48,700 --> 00:02:49,980
咱们是不是创建了八个进程

68
00:02:49,980 --> 00:02:50,840
我是紫进程

69
00:02:50,840 --> 00:02:52,580
好 同学们可能

70
00:02:52,580 --> 00:02:53,760
这里会有一个疑问

71
00:02:53,760 --> 00:02:54,560
咱们通过

72
00:02:54,560 --> 00:02:56,860
Porture process fork 怎么去fork啊 同学们

73
00:02:56,860 --> 00:03:00,460
是不是咱们去fork 一个止进程的路径

74
00:03:00,460 --> 00:03:04,160
那么我们在class里面为什么不这么去做呢 而是通过if else

75
00:03:04,160 --> 00:03:10,760
假如说同学们可能会想 如果说我class的.fork 咱们在class里面也去创建一个ture.js

76
00:03:10,760 --> 00:03:15,260
咱们把它打console.log

77
00:03:15,260 --> 00:03:17,660
我是止进程

78
00:03:17,660 --> 00:03:18,260
好

79
00:03:18,260 --> 00:03:21,060
咱们class的fork

80
00:03:23,060 --> 00:03:25,100
chill的点接 大家觉得这样行不行

81
00:03:25,100 --> 00:03:26,900
大家觉得这样行不行

82
00:03:26,900 --> 00:03:29,720
好 那么我们就来试一下

83
00:03:29,720 --> 00:03:32,280
是不是不可以啊

84
00:03:32,280 --> 00:03:33,300
没有档出来

85
00:03:33,300 --> 00:03:35,100
原因是什么呢 这里呢

86
00:03:35,100 --> 00:03:37,660
我就画一幅图给大家分析一下

87
00:03:37,660 --> 00:03:40,720
好

88
00:03:40,720 --> 00:03:41,740
大家看

89
00:03:41,740 --> 00:03:43,020
首先

90
00:03:43,020 --> 00:03:47,380
工作进程 好 首先在这里咱们分为几个模块

91
00:03:47,380 --> 00:03:49,680
一个master 一个walk

92
00:03:49,680 --> 00:03:51,220
咱们的cpu是几核呀

93
00:03:51,740 --> 00:03:52,500
是不是8合呀

94
00:03:52,500 --> 00:03:55,580
所以我们这里就有8个work 这里老师简化一下 只有4个work

95
00:03:55,580 --> 00:03:57,380
首先我们的代码是怎么样去写的

96
00:03:57,380 --> 00:03:58,900
比如说咱们有一段js

97
00:03:58,900 --> 00:04:00,180
有一段js去执行

98
00:04:00,180 --> 00:04:01,720
首先

99
00:04:01,720 --> 00:04:03,520
有一段js去执行

100
00:04:03,520 --> 00:04:06,080
咱们刚才代码是什么

101
00:04:06,080 --> 00:04:07,360
是不是if

102
00:04:07,360 --> 00:04:11,460
好 调整一下 稍等

103
00:04:11,460 --> 00:04:16,060
ifcluster.is master

104
00:04:16,060 --> 00:04:18,100
咱们 咱们刚才去执行

105
00:04:18,100 --> 00:04:19,140
load

106
00:04:19,140 --> 00:04:21,440
main.js的时候

107
00:04:21,700 --> 00:04:23,240
他是在哪个县城去执行的同学们

108
00:04:23,240 --> 00:04:24,900
他是在哪个县城去执行

109
00:04:24,900 --> 00:04:27,840
是不是在咱们的master去执行load 命连接验室呀

110
00:04:27,840 --> 00:04:28,860
那么他在主县城

111
00:04:28,860 --> 00:04:29,900
那么主县城

112
00:04:29,900 --> 00:04:30,920
走哪一段

113
00:04:30,920 --> 00:04:32,460
主县城走哪一段

114
00:04:32,460 --> 00:04:34,240
主县城是不是走上面的呀

115
00:04:34,240 --> 00:04:36,300
是不是走上面的for循环了

116
00:04:36,300 --> 00:04:37,820
同学们对不对

117
00:04:37,820 --> 00:04:39,360
对不对

118
00:04:39,360 --> 00:04:44,740
咱们的主县城

119
00:04:44,740 --> 00:04:47,300
是不是走上面这段代码呀

120
00:04:47,300 --> 00:04:49,100
咱把它标大一点

121
00:04:49,100 --> 00:04:50,880
醒目标成红色

122
00:04:51,400 --> 00:04:52,880
咱们的主线程是不是走这一段代码

123
00:04:52,880 --> 00:04:54,720
对吧

124
00:04:54,720 --> 00:04:55,760
这咱们主线程

125
00:04:55,760 --> 00:04:57,040
他会做什么事情呢

126
00:04:57,040 --> 00:04:58,560
首先去检测咱们的cpu的

127
00:04:58,560 --> 00:05:00,880
检测咱们cpu的核数

128
00:05:00,880 --> 00:05:03,680
8核然后呢去class的点fork

129
00:05:03,680 --> 00:05:06,500
这一段去掉刚才是不是验证过了不生效fork

130
00:05:06,500 --> 00:05:08,040
fork之后会发生什么事情

131
00:05:08,040 --> 00:05:10,600
fork是不是就会根据你的cpu核数产生

132
00:05:10,600 --> 00:05:12,400
8个worker这里的简化4个

133
00:05:12,400 --> 00:05:14,440
那么在咱们worker诞生的时候

134
00:05:14,440 --> 00:05:17,520
咱们worker诞生的时候是不是又会执行咱们的

135
00:05:17,520 --> 00:05:18,800
node main.js

136
00:05:18,800 --> 00:05:20,580
是不是对吧

137
00:05:21,400 --> 00:05:22,400
这里呢

138
00:05:22,400 --> 00:05:25,600
老师记录一下

139
00:05:25,600 --> 00:05:26,600
在

140
00:05:26,600 --> 00:05:30,700
cluster.fork

141
00:05:30,700 --> 00:05:33,700
调用的时候

142
00:05:33,700 --> 00:05:36,600
相当于再次执行了

143
00:05:36,600 --> 00:05:38,700
load.main.js

144
00:05:38,700 --> 00:05:41,500
这里呢就是cluster.fork和

145
00:05:41,500 --> 00:05:43,100
咱们process.chew的

146
00:05:43,100 --> 00:05:44,500
它的一个不同的地方

147
00:05:44,500 --> 00:05:46,100
这里就是和

148
00:05:49,500 --> 00:05:53,280
Process.show的不一样的地方

149
00:05:53,280 --> 00:05:56,240
好 这是咱们的笔记

150
00:05:56,240 --> 00:05:58,000
所以说

151
00:05:58,000 --> 00:06:00,860
咱们在Worker里面继续去执行load命.js

152
00:06:00,860 --> 00:06:01,600
是不是执行了还是他

153
00:06:01,600 --> 00:06:03,920
那么Worker他是master吗

154
00:06:03,920 --> 00:06:04,160
不是

155
00:06:04,160 --> 00:06:05,660
所以就会走s下面的代码

156
00:06:05,660 --> 00:06:07,620
是不是就会走concel.lock

157
00:06:07,620 --> 00:06:08,400
我是执行程

158
00:06:08,400 --> 00:06:11,660
所以Worker他会走下面的代码

159
00:06:11,660 --> 00:06:17,760
这是Worker

160
00:06:17,760 --> 00:06:19,000
他所做的事情

161
00:06:19,000 --> 00:06:20,520
那么呢

162
00:06:20,520 --> 00:06:21,900
每一个worker都会去执行它

163
00:06:21,900 --> 00:06:23,100
是不是就做到了

164
00:06:23,100 --> 00:06:24,000
咱们的

165
00:06:24,000 --> 00:06:26,400
利用了咱们CPU的

166
00:06:26,400 --> 00:06:27,500
多核

167
00:06:27,500 --> 00:06:28,200
所以说

168
00:06:28,200 --> 00:06:29,280
我们的cluster

169
00:06:29,280 --> 00:06:31,520
它就不需要去创建一个

170
00:06:31,520 --> 00:06:33,540
它就不需要和咱们的chill的process

171
00:06:33,540 --> 00:06:35,540
fork一样去创建一个chill的和一个main

172
00:06:35,540 --> 00:06:37,360
其实咱们的cluster呢

173
00:06:37,360 --> 00:06:37,760
只需要

174
00:06:37,760 --> 00:06:40,140
只需要一个main.js就够了

175
00:06:40,140 --> 00:06:41,340
你在else里面去写你的

176
00:06:41,340 --> 00:06:43,000
要不但吗

177
00:06:43,000 --> 00:06:46,120
好

178
00:06:46,120 --> 00:06:47,200
那么这里呢

179
00:06:47,960 --> 00:06:50,280
就是对cluster属性的介绍以及呢

180
00:06:50,280 --> 00:06:52,580
他和process的一个区别

181
00:06:52,580 --> 00:06:53,340
好

182
00:06:53,340 --> 00:06:55,140
我们来回顾一下刚才所讲解的内容

183
00:06:55,140 --> 00:06:58,200
cluster里面是不是有三个属性

184
00:06:58,200 --> 00:06:59,740
一个 is master is worker

185
00:06:59,740 --> 00:07:01,020
master是判断他是不是主进程

186
00:07:01,020 --> 00:07:03,320
Worker呢 is worker就是判断是不是工作进程

187
00:07:03,320 --> 00:07:05,380
fork方法呢是不是就是创建一个

188
00:07:05,380 --> 00:07:06,660
新的Worker对象

189
00:07:06,660 --> 00:07:08,960
那么他的区别是什么呀

190
00:07:08,960 --> 00:07:10,760
他的区别

191
00:07:10,760 --> 00:07:13,560
和

192
00:07:13,560 --> 00:07:16,380
process.child的区别

193
00:07:17,200 --> 00:07:18,740
是不是不用创建

194
00:07:18,740 --> 00:07:21,560
一个新的chill的点

195
00:07:21,560 --> 00:07:27,700
这就是fork的一个特点他直接在咱们的命点建设里面通过 ismaster去判断然后创建自己的执行程然后重新执行他自己

196
00:07:27,700 --> 00:07:33,320
这里呢就是class他的一个属性那么下一节可能我们就会去讲解class里面的一些

197
00:07:33,320 --> 00:07:36,140
事件他会监听他怎么样去监听事件好

198
00:07:36,140 --> 00:07:36,920
咱们先

