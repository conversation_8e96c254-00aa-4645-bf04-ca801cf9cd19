1
00:00:00,000 --> 00:00:02,000
好这节课我们来看一下框架

2
00:00:02,000 --> 00:00:04,000
统一的异常处理

3
00:00:04,000 --> 00:00:06,000
那么统一的异常处理是什么意思呢

4
00:00:06,000 --> 00:00:08,000
你比如说我们是不是代码发生了错误

5
00:00:08,000 --> 00:00:10,000
刚才我们发生错误是不是把我们页面直接给劫持了

6
00:00:10,000 --> 00:00:12,000
包括了一些500核式的错误

7
00:00:12,000 --> 00:00:14,000
那么我们再把刚才的场景来重新复现一下

8
00:00:14,000 --> 00:00:16,000
那么比如说呢

9
00:00:16,000 --> 00:00:18,000
我们在home里面去执行一个hand data

10
00:00:18,000 --> 00:00:20,000
那么它是不是一定会报错对吧

11
00:00:20,000 --> 00:00:22,000
为什么因为里面有个XXX方法

12
00:00:22,000 --> 00:00:24,000
其实是没有定义的所以呢它一定会报错

13
00:00:24,000 --> 00:00:26,000
那么这个错误呢我们是不是没有给tri-catch

14
00:00:26,000 --> 00:00:28,000
对吧其实呢我们页面是不是应该会去报一个错误

15
00:00:28,000 --> 00:00:30,440
我们来看一下一GG是怎么帮我们去处理的

16
00:00:30,440 --> 00:00:31,440
我们来刷新

17
00:00:31,440 --> 00:00:32,840
好 大家可以看到

18
00:00:32,840 --> 00:00:34,240
我们的页面

19
00:00:34,240 --> 00:00:35,840
我重新刷新一下

20
00:00:35,840 --> 00:00:37,440
大家可以看到我们的错误码是不是500

21
00:00:37,440 --> 00:00:39,640
然后呢返回的结果是什么

22
00:00:39,640 --> 00:00:41,640
是不是一段HTML页面

23
00:00:41,640 --> 00:00:44,080
然后里面有我们的一些错误的代码

24
00:00:44,080 --> 00:00:45,320
包括了一些堆栈信息

25
00:00:45,320 --> 00:00:47,000
甚至呢一GG他做得非常好

26
00:00:47,000 --> 00:00:48,400
把我们的代码都已经选出来了

27
00:00:48,400 --> 00:00:50,640
那么这里呢其实就是一GG帮我们去做了一个兜底

28
00:00:50,640 --> 00:00:52,360
也就是他的一个错误的统计处理

29
00:00:52,360 --> 00:00:56,560
其实这就是咱们错误处理的一个概念

30
00:00:56,560 --> 00:00:57,760
它就是统一的一场处理

31
00:00:57,760 --> 00:00:59,040
只是呢它所在我们的开发环境

32
00:00:59,040 --> 00:01:00,580
好 那么呢

33
00:01:00,580 --> 00:01:01,860
其实

34
00:01:01,860 --> 00:01:05,180
框架它既然有统一处理的地方那么它肯定是不是可以去定制啊

35
00:01:05,180 --> 00:01:07,240
那么比如说你说哎刚才那个错处理我不爽

36
00:01:07,240 --> 00:01:08,520
用的不好 我要换

37
00:01:08,520 --> 00:01:09,280
怎么办

38
00:01:09,280 --> 00:01:11,320
其实呢在配置里面呢有一项在那配置

39
00:01:11,320 --> 00:01:14,660
比如说我们在config的default里面它呢里面有个on error这样的属性

40
00:01:14,660 --> 00:01:15,940
如果说你跟它配上有默

41
00:01:15,940 --> 00:01:17,980
说明了所有错误是不是都按再去处理

42
00:01:17,980 --> 00:01:19,520
然后呢比如说你想返回

43
00:01:19,520 --> 00:01:21,320
你需要的格式好 那么我们就来看一下

44
00:01:21,320 --> 00:01:23,100
是不是怎么回事

45
00:01:23,100 --> 00:01:25,400
好 我们现在呢在config default介绍

46
00:01:26,180 --> 00:01:27,420
我们现在给config一个什么

47
00:01:27,420 --> 00:01:28,740
是不是一个en error属性

48
00:01:28,740 --> 00:01:30,780
好 这里给它一个wall

49
00:01:30,780 --> 00:01:32,840
然后咱们把它的body改为error

50
00:01:32,840 --> 00:01:33,940
status改为500

51
00:01:33,940 --> 00:01:35,660
那么我们此时再来刷新一下页面

52
00:01:35,660 --> 00:01:36,940
看会发生一件什么事情

53
00:01:36,940 --> 00:01:40,520
好像没有生效

54
00:01:40,520 --> 00:01:42,060
很尴尬

55
00:01:42,060 --> 00:01:43,580
好 我们来重新运行一下

56
00:01:43,580 --> 00:01:49,480
好 大家可以看到

57
00:01:49,480 --> 00:01:51,020
此时返回的错误是不是

58
00:01:51,020 --> 00:01:53,060
我们刚才在配置文件里面所定义的

59
00:01:53,060 --> 00:01:53,820
抓了码500

60
00:01:53,820 --> 00:01:54,600
那么它的body是什么

61
00:01:54,600 --> 00:01:55,360
是不是有error

62
00:01:55,620 --> 00:01:56,380
自不创

63
00:01:56,380 --> 00:01:56,900
对吧

64
00:01:56,900 --> 00:01:59,080
当然了你想改为其他的也可以啊

65
00:01:59,080 --> 00:02:01,000
比如说你错误了

66
00:02:01,000 --> 00:02:03,820
对吧

67
00:02:03,820 --> 00:02:06,120
这里呢其实就是我们所自定义的一种错误处理的方式

68
00:02:06,120 --> 00:02:08,420
当然了1G他原生处理的是不是非常的好啊

69
00:02:08,420 --> 00:02:09,700
对吧

70
00:02:09,700 --> 00:02:10,220
我们

71
00:02:10,220 --> 00:02:12,520
有没有必要多识一句啊

72
00:02:12,520 --> 00:02:13,280
好像没有

73
00:02:13,280 --> 00:02:14,300
那么其实生产环境呢

74
00:02:14,300 --> 00:02:16,360
我们有必要去把它给配置一下

75
00:02:16,360 --> 00:02:18,660
但是在该环境其实1G做的已经非常好

76
00:02:18,660 --> 00:02:19,680
好

77
00:02:19,680 --> 00:02:20,460
那么我们继续

78
00:02:20,460 --> 00:02:23,260
那么刚才我们看的是500的错误

79
00:02:23,260 --> 00:02:23,780
那么当然了

80
00:02:23,780 --> 00:02:25,020
是不是也可以去

81
00:02:25,020 --> 00:02:28,040
是你是不是咱们框架应该也有默认的处理方式吧

82
00:02:28,040 --> 00:02:30,740
那其实其实框架他默认处理方式怎么样的呢

83
00:02:30,740 --> 00:02:32,620
比如说啊他判定你需要节省

84
00:02:32,620 --> 00:02:34,040
他就会返回一个message多的范围

85
00:02:34,040 --> 00:02:36,120
如果说判定了判定你需要HTML

86
00:02:36,120 --> 00:02:37,540
那么怎么判断是不是根据你的head

87
00:02:37,540 --> 00:02:39,140
比如说你的head有个accept之段

88
00:02:39,140 --> 00:02:40,980
你比如说你accept的节省他肯定返回一个节省

89
00:02:40,980 --> 00:02:43,980
你如果说accept的HTML他就给你返回一段HTML

90
00:02:43,980 --> 00:02:45,980
你要什么给你什么服务器呢非常的贴心

91
00:02:45,980 --> 00:02:48,140
好 那我们就来重新演示一下

92
00:02:48,140 --> 00:02:51,140
是他到底返回的是什么样的情况

93
00:02:51,520 --> 00:02:53,160
那么我们怎么样去给我们页面一个逝离师呢

94
00:02:53,160 --> 00:02:55,020
是不是直接把他的status改为逝离师就可以了

95
00:02:55,020 --> 00:02:56,420
我们来看一下那页面默认会返回什么

96
00:02:56,420 --> 00:02:59,820
好 周里大家可以看到

97
00:02:59,820 --> 00:03:02,820
我们呢 此时是不是得到的是一个什么样

98
00:03:02,820 --> 00:03:06,420
周里大家可以看到我们默认得到的是不是一段

99
00:03:06,420 --> 00:03:08,820
html片段了 对吧

100
00:03:08,820 --> 00:03:10,120
其实呢 咱们的服务器默认

101
00:03:10,120 --> 00:03:12,220
认为你是不是需要html

102
00:03:12,220 --> 00:03:13,620
那么如果说我们

103
00:03:13,620 --> 00:03:14,920
哎 觉得这个页面很丑

104
00:03:14,920 --> 00:03:17,420
我们来看一下其他的一些网站的逝离师页面长什么样

105
00:03:17,420 --> 00:03:18,520
比如说淘宝

106
00:03:20,620 --> 00:03:24,020
假如说我们去搜一个比较敏感的一个词汇

107
00:03:24,020 --> 00:03:26,260
我们就搜叫做,我们下搜吧

108
00:03:26,260 --> 00:03:26,760
下搜

109
00:03:26,760 --> 00:03:28,820
哦,不行

110
00:03:28,820 --> 00:03:31,120
我来想一下有没有什么比较敏感的词汇

111
00:03:31,120 --> 00:03:35,220
好,比如说我们搜VPN

112
00:03:35,220 --> 00:03:37,000
好,大家是不是能看到

113
00:03:37,000 --> 00:03:39,560
页面中呢,其实是不是出现了一段非常好的一段提示

114
00:03:39,560 --> 00:03:42,120
非常抱歉,没有找到有一个VPN相关的宝贝

115
00:03:42,120 --> 00:03:44,680
那么其实这里呢,就有点类似于咱们一个市里市页面一样

116
00:03:44,680 --> 00:03:45,960
它是不是对用户的一个引导

117
00:03:45,960 --> 00:03:46,980
对吧

118
00:03:46,980 --> 00:03:48,520
你如果说页面这样的一个返回

119
00:03:48,520 --> 00:03:49,800
我还以为你服气是不是爆炸呢

120
00:03:49,800 --> 00:03:51,200
所以说你需要去做一个美美的页面

121
00:03:51,200 --> 00:03:52,540
这样是不是对用户稍微好一点

122
00:03:52,540 --> 00:03:54,780
同学们应该在平时上网过程中

123
00:03:54,780 --> 00:03:57,080
也看到很多一些视频室和500的一些页面

124
00:03:57,080 --> 00:03:58,200
做得非常的精美

125
00:03:58,200 --> 00:04:00,020
也算是对用户的一个补偿吧

126
00:04:00,020 --> 00:04:01,180
好

127
00:04:01,180 --> 00:04:03,260
那么这里如果说我们希望去制定义

128
00:04:03,260 --> 00:04:04,440
我们的视频室页面

129
00:04:04,440 --> 00:04:05,600
当然也是可以的

130
00:04:05,600 --> 00:04:06,720
那么在config里面呢

131
00:04:06,720 --> 00:04:07,940
有一个什么呀

132
00:04:07,940 --> 00:04:08,440
是不是有一个

133
00:04:08,440 --> 00:04:09,760
Rodfunk的这段

134
00:04:09,760 --> 00:04:11,180
我们来看一下咱们的讲义

135
00:04:11,180 --> 00:04:13,860
我们在视频室里面同样的

136
00:04:13,860 --> 00:04:16,800
当然也对应了一个Rodfunk的这样的属性

137
00:04:16,800 --> 00:04:18,200
然后呢如果说发生错误

138
00:04:18,200 --> 00:04:20,200
它呢可以让你重定向你

139
00:04:20,200 --> 00:04:22,200
到你所希望的页面

140
00:04:22,200 --> 00:04:24,200
那么这里呢我们就来试一下

141
00:04:24,200 --> 00:04:26,200
比如说

142
00:04:26,200 --> 00:04:28,200
比如说我们的PageURL

143
00:04:28,200 --> 00:04:30,200
我们直接给改为什么呢

144
00:04:30,200 --> 00:04:32,200
也就是说你只要发生了室灵师的错误

145
00:04:32,200 --> 00:04:34,200
那么他就把给你

146
00:04:34,200 --> 00:04:36,200
是不是把你给路由到在一个界面上面去

147
00:04:36,200 --> 00:04:38,200
那我们现在是不是还没有

148
00:04:38,200 --> 00:04:40,200
那么我们在root里面稍微去

149
00:04:40,200 --> 00:04:42,200
稍微去添加一下就可以了

150
00:04:42,200 --> 00:04:44,200
那么呢我们稍微来改造一下

151
00:04:44,200 --> 00:04:46,200
比如说我们的service我们把它改为室灵师

152
00:04:46,200 --> 00:04:48,200
然后呢页面我们把它的返回值稍微修改一下

153
00:04:48,200 --> 00:04:49,480
为什么呢

154
00:04:49,480 --> 00:04:52,560
这里呢我们只是

155
00:04:52,560 --> 00:04:55,120
失忆啊就我们兄弟你

156
00:04:55,120 --> 00:04:56,140
是你是

157
00:04:56,140 --> 00:05:00,240
好那么我们来看一下到底是不是怎么回事

158
00:05:00,240 --> 00:05:02,800
也就说我们只要是不是发生了是你事的错误

159
00:05:02,800 --> 00:05:03,820
对吧就呢

160
00:05:03,820 --> 00:05:08,940
会访问到我们的一个这样一个页面兄弟你是你是我们来看一下在配置他有没有生效

161
00:05:08,940 --> 00:05:10,220
走大家可以看到

162
00:05:10,220 --> 00:05:15,840
是不是实现了我们对是你事业的一个定制化那么如果说你想去把它做的更加的优美一点

163
00:05:16,620 --> 00:05:18,920
想做的更加优美点 我们可以从哪里去入手

164
00:05:18,920 --> 00:05:23,020
是不是把咱们这里通过contest去run的一个

165
00:05:23,020 --> 00:05:24,300
什么呀是不是run的一个模板

166
00:05:24,300 --> 00:05:26,100
那么这里就可以去制作一个

167
00:05:26,100 --> 00:05:27,380
精美的页面来

168
00:05:27,380 --> 00:05:30,700
run的模板

169
00:05:30,700 --> 00:05:31,980
写一个

170
00:05:31,980 --> 00:05:33,780
优美的

171
00:05:33,780 --> 00:05:38,380
404页面 这么一项坚决的任务就交给同学们自己

172
00:05:38,380 --> 00:05:41,700
那么我们来看一下

173
00:05:41,700 --> 00:05:45,540
我们除了刚才通过配置的方式 其实还有另外一种方式可以去制定我们的404页面

174
00:05:45,800 --> 00:05:47,100
我们刚才是通过Config

175
00:05:47,100 --> 00:05:47,820
那么它呢

176
00:05:47,820 --> 00:05:49,400
是不是会劫持我们的一个http

177
00:05:49,400 --> 00:05:50,140
式灵式的状态

178
00:05:50,140 --> 00:05:50,780
做了一个

179
00:05:50,780 --> 00:05:51,740
其实就是做了一个重定项

180
00:05:51,740 --> 00:05:53,720
那么其实我们还可以通过一种方式

181
00:05:53,720 --> 00:05:54,160
是什么呢

182
00:05:54,160 --> 00:05:55,120
同学们来思考一下

183
00:05:55,120 --> 00:05:56,660
可以思考一下

184
00:05:56,660 --> 00:05:57,620
是不是可以通过我们的一个

185
00:05:57,620 --> 00:05:58,440
什么呀

186
00:05:58,440 --> 00:05:59,320
是不是可以通过我们的中间键

187
00:05:59,320 --> 00:05:59,840
为什么

188
00:05:59,840 --> 00:06:02,220
大家还记得我们的洋葱模型吗

189
00:06:02,220 --> 00:06:03,060
我们的洋葱模型

190
00:06:03,060 --> 00:06:05,020
是不是每一次请求都会一进一出

191
00:06:05,020 --> 00:06:05,860
一进一出

192
00:06:05,860 --> 00:06:07,840
那么我们的式灵式是不是

193
00:06:07,840 --> 00:06:09,780
Response所反复的结果

194
00:06:09,780 --> 00:06:11,320
那么我们只需要去劫持我们的一个

195
00:06:11,320 --> 00:06:12,480
怎么样是不是Response

196
00:06:12,480 --> 00:06:14,180
如果说我们的Stadius等于式灵式

197
00:06:14,180 --> 00:06:15,660
那么呢我们就去判断

198
00:06:15,660 --> 00:06:17,980
我们的contest.accept.json

199
00:06:17,980 --> 00:06:19,600
它是不是其实就是我们head里面的一个字段

200
00:06:19,600 --> 00:06:21,120
如果说你客户端需要节省

201
00:06:21,120 --> 00:06:22,120
我们就给它返回一个nodefun

202
00:06:22,120 --> 00:06:23,520
如果说我们的客户端需要html

203
00:06:23,520 --> 00:06:24,320
就给它返回一段

204
00:06:24,320 --> 00:06:25,980
我们html的一个页面

205
00:06:25,980 --> 00:06:27,760
那么这里呢其实就是我们通过

206
00:06:27,760 --> 00:06:32,420
中间键去做生理事页面的一个方法

207
00:06:32,420 --> 00:06:35,340
好那么这里呢就是我们呢

208
00:06:35,340 --> 00:06:36,900
这一节课的内容我们来总结一下

209
00:06:36,900 --> 00:06:41,460
好那么这里呢我们是不是

210
00:06:41,460 --> 00:06:43,760
讲了一下统一

211
00:06:43,760 --> 00:06:46,800
异常处理的方式

212
00:06:46,800 --> 00:06:47,200
对吧

213
00:06:47,200 --> 00:06:48,600
那么第一种是什么

214
00:06:48,600 --> 00:06:49,740
是不是我们的一个500错误

215
00:06:49,740 --> 00:06:51,200
那么500怎么去做

216
00:06:51,200 --> 00:06:52,120
是不是通过我们

217
00:06:52,120 --> 00:06:53,760
config里面的一个

218
00:06:53,760 --> 00:06:54,680
config点

219
00:06:54,680 --> 00:06:56,180
它里面是不是有一个

220
00:06:56,180 --> 00:06:57,180
en error

221
00:06:57,180 --> 00:06:58,780
en error里面咱们去

222
00:06:58,780 --> 00:07:01,000
是不是修改它的word呀

223
00:07:01,000 --> 00:07:01,520
对吧

224
00:07:01,520 --> 00:07:04,200
那么除了讲解500

225
00:07:04,200 --> 00:07:04,740
是不是还有一个

226
00:07:04,740 --> 00:07:06,020
那我生意识是什么

227
00:07:06,020 --> 00:07:06,940
是不是config点

228
00:07:06,940 --> 00:07:07,420
en

229
00:07:07,420 --> 00:07:10,800
是不是我们config点

230
00:07:10,800 --> 00:07:13,020
rodfund这样的一个

231
00:07:13,020 --> 00:07:15,200
属性对吧

232
00:07:15,200 --> 00:07:16,960
那么除了除了通过我们的配置

233
00:07:16,960 --> 00:07:18,440
是不是还可以通过中间键呢

234
00:07:18,440 --> 00:07:20,100
其实同样我们的500错误

235
00:07:20,100 --> 00:07:21,580
是不是也可以通过中间键去处理

236
00:07:21,580 --> 00:07:23,340
其实都可以去通过中间键

237
00:07:23,340 --> 00:07:23,780
好

238
00:07:23,780 --> 00:07:25,040
那么这里呢

239
00:07:25,040 --> 00:07:26,140
就是我们这节目的内容

