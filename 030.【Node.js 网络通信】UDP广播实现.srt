1
00:00:00,000 --> 00:00:01,720
然后接下来我们来看一下

2
00:00:01,720 --> 00:00:03,860
在Node中实现UDP广播

3
00:00:03,860 --> 00:00:05,740
那么对于实现UDP广播

4
00:00:05,740 --> 00:00:07,480
我们首先对于服务端来讲

5
00:00:07,480 --> 00:00:09,780
我们只需要在这个Listening成功以后

6
00:00:09,780 --> 00:00:11,520
我们调用BySocket

7
00:00:11,520 --> 00:00:13,540
去调用这个SetBrowCut这个方法

8
00:00:13,540 --> 00:00:14,760
然后给一个True

9
00:00:14,760 --> 00:00:15,300
那么这样的话

10
00:00:15,300 --> 00:00:16,960
我们就表示开启了广播模式

11
00:00:16,960 --> 00:00:18,520
那么只有开启了广播模式

12
00:00:18,520 --> 00:00:20,160
然后接下来我们就可以上这个广播地址

13
00:00:20,160 --> 00:00:20,780
去发送消息

14
00:00:20,780 --> 00:00:22,720
那么其他代码都是一样的

15
00:00:22,720 --> 00:00:24,300
那么接下来我们回到我们的编辑器当中

16
00:00:24,300 --> 00:00:26,140
我们把刚才这个事例来演示一下

17
00:00:26,140 --> 00:00:28,560
那么首先我们把刚才单播中

18
00:00:28,560 --> 00:00:29,800
这个Sever和Clank分别

19
00:00:29,800 --> 00:00:31,520
拿到我们这个UDP广播当中

20
00:00:31,520 --> 00:00:33,320
那么首先我们在这个sour当中

21
00:00:33,320 --> 00:00:35,440
我们要做的就是在这里去sour

22
00:00:35,440 --> 00:00:36,920
sit

23
00:00:36,920 --> 00:00:38,700
broadcast

24
00:00:38,700 --> 00:00:40,680
那么这里我们要给标记为true

25
00:00:40,680 --> 00:00:43,140
那么这个就表示开启广播模式

26
00:00:43,140 --> 00:00:45,800
那么同样的如果说你要关闭这个广播模式

27
00:00:45,800 --> 00:00:47,760
你只需要再重新调记方法

28
00:00:47,760 --> 00:00:49,160
给个force就可以了

29
00:00:49,160 --> 00:00:50,440
那么开启广播模式以后

30
00:00:50,440 --> 00:00:52,280
然后接下来我们在这里呢

31
00:00:52,280 --> 00:00:53,280
我们就来干一件事

32
00:00:53,280 --> 00:00:55,320
我们就是每隔

33
00:00:55,320 --> 00:00:57,560
我们让它每隔两秒

34
00:00:57,560 --> 00:00:59,620
每隔一秒两秒

35
00:00:59,620 --> 00:01:00,800
发送一条

36
00:01:00,800 --> 00:01:02,180
广播消息

37
00:01:02,180 --> 00:01:02,540
好

38
00:01:02,540 --> 00:01:03,080
那么这里的话

39
00:01:03,080 --> 00:01:04,440
我们就可以在这里这样来做

40
00:01:04,440 --> 00:01:05,120
好

41
00:01:05,120 --> 00:01:05,880
我们在这每个两面

42
00:01:05,880 --> 00:01:06,640
所以说我们在这里可以

43
00:01:06,640 --> 00:01:07,040
satama

44
00:01:07,040 --> 00:01:07,900
sat灵头

45
00:01:07,900 --> 00:01:08,120
好

46
00:01:08,120 --> 00:01:08,580
给2000

47
00:01:08,580 --> 00:01:09,180
2000毫米

48
00:01:09,180 --> 00:01:09,580
就是两面

49
00:01:09,580 --> 00:01:10,980
然后我们在这个单脑当中

50
00:01:10,980 --> 00:01:12,940
我们就可以在这里去serve.find

51
00:01:12,940 --> 00:01:13,580
好

52
00:01:13,580 --> 00:01:14,340
我们要发一个什么呢

53
00:01:14,340 --> 00:01:15,560
例如我们就来发一个hello

54
00:01:15,560 --> 00:01:16,320
好

55
00:01:16,320 --> 00:01:18,000
然后接下来我们要发送的端口号

56
00:01:18,000 --> 00:01:18,720
注意这里

57
00:01:18,720 --> 00:01:20,220
我们就要在这里去指定一个端口号

58
00:01:20,220 --> 00:01:20,760
好

59
00:01:20,760 --> 00:01:21,480
那么这个端口号

60
00:01:21,480 --> 00:01:22,880
例如我们指定一个8000

61
00:01:22,880 --> 00:01:23,640
好

62
00:01:23,640 --> 00:01:24,640
那么写完以后

63
00:01:24,640 --> 00:01:25,640
然后地址是多少呢

64
00:01:25,640 --> 00:01:25,800
注意

65
00:01:25,800 --> 00:01:27,000
只是这个广播地址

66
00:01:27,000 --> 00:01:28,640
我们刚才在概念介绍中

67
00:01:28,640 --> 00:01:31,060
我们说到广播有两种地址

68
00:01:31,060 --> 00:01:33,060
一种就是直接地址

69
00:01:33,060 --> 00:01:34,980
一种就是这个受限地址

70
00:01:34,980 --> 00:01:38,020
那么受限地址最简单的就是25.25.25.25

71
00:01:38,020 --> 00:01:38,580
也就是4.25

72
00:01:38,580 --> 00:01:39,900
也就是受限地址

73
00:01:39,900 --> 00:01:41,460
它不会经过这个路由转发

74
00:01:41,460 --> 00:01:43,160
它指的就是当前你这个

75
00:01:43,160 --> 00:01:45,400
直接连接的这个路由器局板当中

76
00:01:45,400 --> 00:01:48,020
而这个直接地址是可以进行路由器转发的

77
00:01:48,020 --> 00:01:49,920
也就是说可以跨往端进行传输

78
00:01:49,920 --> 00:01:51,680
那么这里我们首先来看一下

79
00:01:51,680 --> 00:01:53,140
这个最简单的受限地址

80
00:01:53,140 --> 00:01:55,000
所以说我们在这里给它4.25就可以了

81
00:01:55,000 --> 00:01:57,060
就在当前路由器举一网当中去进行传播

82
00:01:57,060 --> 00:02:00,280
那么我们这种就是最简单的

83
00:02:00,280 --> 00:02:01,440
这个方式就给它写好了

84
00:02:01,440 --> 00:02:02,320
就是每隔两秒

85
00:02:02,320 --> 00:02:04,560
然后就发送一条广播数据

86
00:02:04,560 --> 00:02:07,160
当然我们可以让它一上来就来发送一次

87
00:02:07,160 --> 00:02:08,440
一上来就来发送一次

88
00:02:08,440 --> 00:02:09,960
然后接下来每隔两秒就发一次

89
00:02:09,960 --> 00:02:10,880
每隔两秒发一次

90
00:02:10,880 --> 00:02:12,000
所以就是这样一种方式

91
00:02:12,000 --> 00:02:15,660
那么在这里我们把这个代码给它写好以后

92
00:02:15,660 --> 00:02:18,020
然后接下来回到我们这个就是Client当中

93
00:02:18,020 --> 00:02:19,280
我们在Client当中注意

94
00:02:19,280 --> 00:02:21,460
此时我们让这个模型的稍微简单一些

95
00:02:21,460 --> 00:02:25,340
所以说我们首先client不需要来发消息

96
00:02:25,340 --> 00:02:26,500
不需要来发消息

97
00:02:26,500 --> 00:02:28,560
我们只想让他来接消息就可以了

98
00:02:28,560 --> 00:02:30,020
也就是说他接到消息以后

99
00:02:30,020 --> 00:02:31,640
他就把这个消息呢去输出一下

100
00:02:31,640 --> 00:02:32,440
就这么简单

101
00:02:32,440 --> 00:02:34,180
好了我们在这里写好以后

102
00:02:34,180 --> 00:02:35,780
然后接下来我们就可以来测试一下

103
00:02:35,780 --> 00:02:38,060
这个广播消息到底能不能广播出去

104
00:02:38,060 --> 00:02:40,220
所以这个时候回到我们的命令行当中

105
00:02:40,220 --> 00:02:44,280
然后首先我们来通过Node来执行这个就是32.js

106
00:02:44,280 --> 00:02:47,460
当然注意此时他肯定是每个两秒在发一条消息

107
00:02:47,460 --> 00:02:48,620
那么到底有没有发出去

108
00:02:48,620 --> 00:02:49,820
我们只要开启这个client

109
00:02:49,820 --> 00:02:51,200
我们看一下有没有收到就可以了

110
00:02:51,200 --> 00:02:52,840
所以我们加这NodeClient.js

111
00:02:52,840 --> 00:02:54,900
那么Client启动成功以后

112
00:02:54,900 --> 00:02:56,360
那么这个时候我们就会发现

113
00:02:56,360 --> 00:02:58,000
我们每个两秒是不是就收到了

114
00:02:58,000 --> 00:03:00,020
来自于这个SOR的一个Hello

115
00:03:00,020 --> 00:03:02,180
来自于我们这个SOR的这个地址

116
00:03:02,180 --> 00:03:02,960
10.217

117
00:03:02,960 --> 00:03:04,060
然后来自于3000多少号

118
00:03:04,060 --> 00:03:04,880
这样我们就收到了

119
00:03:04,880 --> 00:03:07,260
我们发现我们这里写的这个地址

120
00:03:07,260 --> 00:03:08,640
并不是一个具体的地址

121
00:03:08,640 --> 00:03:09,440
也不是本机地址

122
00:03:09,440 --> 00:03:11,060
而是一个广播地址

123
00:03:11,060 --> 00:03:13,100
那么这个时候我们为了验证一下

124
00:03:13,100 --> 00:03:14,860
因为我现在这两个脚本呢

125
00:03:14,860 --> 00:03:17,440
都是同在一个电脑机器当中

126
00:03:17,440 --> 00:03:18,300
我们只需要

127
00:03:18,300 --> 00:03:19,580
我们如果说带来一台机器

128
00:03:19,580 --> 00:03:20,180
我们带来一个

129
00:03:20,180 --> 00:03:20,880
就是同一个局板

130
00:03:20,880 --> 00:03:21,800
只不过IP地址不一样

131
00:03:21,800 --> 00:03:22,400
我们看一下

132
00:03:22,400 --> 00:03:23,780
同一个局域网当中的另一台机器

133
00:03:23,780 --> 00:03:24,900
能不能收到这条广播消息

134
00:03:24,900 --> 00:03:25,720
就可以了

135
00:03:25,720 --> 00:03:27,120
所以说我在这里呢

136
00:03:27,120 --> 00:03:28,140
就开启了一个虚拟机

137
00:03:28,140 --> 00:03:29,200
那么这个虚拟机

138
00:03:29,200 --> 00:03:29,960
它的IP地址是

139
00:03:29,960 --> 00:03:31,860
**************.171

140
00:03:31,860 --> 00:03:34,200
它和我这台电脑所处的这个

141
00:03:34,200 --> 00:03:35,040
就是IP

142
00:03:35,040 --> 00:03:36,780
都是同一个局域网

143
00:03:36,780 --> 00:03:38,220
都是这个192.168.10

144
00:03:38,220 --> 00:03:39,460
这个局域网当中

145
00:03:39,460 --> 00:03:40,140
那么接下来

146
00:03:40,140 --> 00:03:41,600
我们只需要在这个虚拟机

147
00:03:41,600 --> 00:03:43,120
也就是一台真实的电脑当中

148
00:03:43,120 --> 00:03:44,380
我们再直接去

149
00:03:44,380 --> 00:03:45,700
node执行这个Client

150
00:03:45,700 --> 00:03:47,960
那么在这个计算机当中

151
00:03:47,960 --> 00:03:48,800
我们来看一下

152
00:03:48,800 --> 00:03:49,920
它同样的

153
00:03:49,920 --> 00:03:50,820
就是也收到了

154
00:03:50,820 --> 00:03:52,940
来自于我们这个广播中的消息

155
00:03:52,940 --> 00:03:53,920
也就是说

156
00:03:53,920 --> 00:03:54,900
你现在的话

157
00:03:54,900 --> 00:03:55,360
这个就是

158
00:03:55,360 --> 00:03:57,100
1100 168 10这个网段

159
00:03:57,100 --> 00:03:59,140
中的所有的这个计算机

160
00:03:59,140 --> 00:03:59,920
或者说网络设备

161
00:03:59,920 --> 00:04:00,840
就只要是

162
00:04:00,840 --> 00:04:01,720
你监听了这个就是

163
00:04:01,720 --> 00:04:02,560
8000端口号

164
00:04:02,560 --> 00:04:03,740
那么你就都会收到

165
00:04:03,740 --> 00:04:05,440
来自于3000的这个端

166
00:04:05,440 --> 00:04:07,020
所发来的广播消息

167
00:04:07,020 --> 00:04:08,080
所以说

168
00:04:08,080 --> 00:04:09,040
这就是我们这个

169
00:04:09,040 --> 00:04:11,180
最基本的这种广播方式

170
00:04:11,180 --> 00:04:12,220
当然这个是我们

171
00:04:12,220 --> 00:04:13,980
这个就是受限地址

172
00:04:13,980 --> 00:04:15,200
那什么是直接地址呢

173
00:04:15,200 --> 00:04:15,660
直接就是

174
00:04:15,660 --> 00:04:16,400
直接地址呢

175
00:04:16,400 --> 00:04:17,420
其实最简单的方式

176
00:04:17,420 --> 00:04:18,420
就是我们刚才

177
00:04:18,420 --> 00:04:20,160
所看到的一样

178
00:04:20,160 --> 00:04:21,340
就是我们在这里看到的

179
00:04:21,340 --> 00:04:23,480
我们只需要把这个地址当中

180
00:04:23,480 --> 00:04:24,840
把这个地址当中

181
00:04:24,840 --> 00:04:26,840
把这个地址当中最后一段

182
00:04:26,840 --> 00:04:27,960
我们给它搞成二物

183
00:04:27,960 --> 00:04:29,720
一般情况下最基本的

184
00:04:29,720 --> 00:04:31,140
最简单的方式就是这样来做

185
00:04:31,140 --> 00:04:31,780
那么这个时候

186
00:04:31,780 --> 00:04:33,560
我们只要把它拿过来

187
00:04:33,560 --> 00:04:34,140
拿过来以后

188
00:04:34,140 --> 00:04:34,800
我们把这个地址呢

189
00:04:34,800 --> 00:04:35,520
我们给它追

190
00:04:35,520 --> 00:04:38,180
我们给它放到这里

191
00:04:38,180 --> 00:04:41,980
我们把这个地址拿过来

192
00:04:41,980 --> 00:04:43,340
拿过来以后

193
00:04:43,340 --> 00:04:44,500
注意这是一个直接地址

194
00:04:44,500 --> 00:04:45,820
那么直接地址如果说

195
00:04:45,820 --> 00:04:47,400
你这个有多个路由设备

196
00:04:47,400 --> 00:04:48,480
就是说相互连接

197
00:04:48,480 --> 00:04:49,580
也就是说有不同的网段

198
00:04:49,580 --> 00:04:51,080
那么这个直接地址

199
00:04:51,080 --> 00:04:52,600
是可以进行跨网段传输的

200
00:04:52,600 --> 00:04:54,180
这里就是了解一下就可以了

201
00:04:54,180 --> 00:04:55,400
那么这个时候改完以后

202
00:04:55,400 --> 00:04:56,020
我们再来看一下

203
00:04:56,020 --> 00:04:58,600
这段直接地址能否去广播呢

204
00:04:58,600 --> 00:05:00,660
同样的我们回到这个命令行当中

205
00:05:00,660 --> 00:05:02,100
我们把sore关闭一下

206
00:05:02,100 --> 00:05:03,340
我们把sore关掉以后

207
00:05:03,340 --> 00:05:03,880
我们可以看到

208
00:05:03,880 --> 00:05:04,580
现在这个client

209
00:05:04,580 --> 00:05:06,000
就不会再去收到

210
00:05:06,000 --> 00:05:06,600
来自于这个

211
00:05:06,600 --> 00:05:07,680
那一段的广播消息了

212
00:05:07,680 --> 00:05:08,740
没关系

213
00:05:08,740 --> 00:05:09,120
我们这个时候

214
00:05:09,120 --> 00:05:09,860
我们再把这个sore

215
00:05:09,860 --> 00:05:11,040
来给大家启动起来

216
00:05:11,040 --> 00:05:11,960
那么启动起来以后

217
00:05:11,960 --> 00:05:13,040
我们这里再来看一下

218
00:05:13,040 --> 00:05:14,940
此时会是否会收到

219
00:05:14,940 --> 00:05:16,140
这个所谓的广播消息

220
00:05:16,140 --> 00:05:18,420
当然这样的话

221
00:05:18,420 --> 00:05:19,460
我们看的并不太明显

222
00:05:19,460 --> 00:05:20,980
因为这个内容都是一样的

223
00:05:20,980 --> 00:05:22,160
我们可以Ctrl C

224
00:05:22,160 --> 00:05:23,300
把它先打断一下

225
00:05:23,300 --> 00:05:24,480
我们再来把它Client

226
00:05:24,480 --> 00:05:25,160
去给它打开

227
00:05:25,160 --> 00:05:26,440
然后这个时候我们看一下

228
00:05:26,440 --> 00:05:27,420
它能否继续收到

229
00:05:27,420 --> 00:05:28,160
那这样的话

230
00:05:28,160 --> 00:05:29,080
你会看的更明显一些

231
00:05:29,080 --> 00:05:30,620
就是说我们家里可以看到

232
00:05:30,620 --> 00:05:31,820
我们这样的一个

233
00:05:31,820 --> 00:05:33,220
直接广播地址

234
00:05:33,220 --> 00:05:34,800
也是可以把这个数据

235
00:05:34,800 --> 00:05:36,200
去广播出去的

236
00:05:36,200 --> 00:05:37,720
只不过这个直接广播地址

237
00:05:37,720 --> 00:05:39,300
是可以进行这个

238
00:05:39,300 --> 00:05:41,260
就是画网段数据传输的

239
00:05:41,260 --> 00:05:42,600
例如跟你这个路由器

240
00:05:42,600 --> 00:05:43,820
就是同属一个网段的

241
00:05:43,820 --> 00:05:44,660
有可能还有这个

242
00:05:44,660 --> 00:05:48,260
例如192.168.11.movaIP地址

243
00:05:48,260 --> 00:05:49,100
就是另一个网段

244
00:05:49,100 --> 00:05:50,800
只要你还是在同一个这个

245
00:05:50,800 --> 00:05:51,700
就是组网当中

246
00:05:51,700 --> 00:05:53,640
那么这样这个直接地址也是可以的

247
00:05:53,640 --> 00:05:53,820
好

248
00:05:53,820 --> 00:05:54,340
那么这个东西呢

249
00:05:54,340 --> 00:05:54,920
就了解一下

250
00:05:54,920 --> 00:05:55,920
大家有条件的话呢

251
00:05:55,920 --> 00:05:58,200
也可以去搞个就是多网段的这种网络环境

252
00:05:58,200 --> 00:05:59,860
然后自行的去测试一下就可以了

253
00:05:59,860 --> 00:06:00,620
好

254
00:06:00,620 --> 00:06:02,460
所以这就是我们在Node中

255
00:06:02,460 --> 00:06:05,440
对UDP广播它的最简单的一个实现

