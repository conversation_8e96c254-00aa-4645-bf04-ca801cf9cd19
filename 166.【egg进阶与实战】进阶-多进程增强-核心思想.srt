1
00:00:00,000 --> 00:00:02,720
好,我们来看一下它的核心思想是什么

2
00:00:02,720 --> 00:00:04,120
什么的核心思想啊

3
00:00:04,120 --> 00:00:05,860
是不是就是我们刚才所画的这幅图

4
00:00:05,860 --> 00:00:06,480
它的核心思想

5
00:00:06,480 --> 00:00:07,860
那么它的核心思想是什么

6
00:00:07,860 --> 00:00:09,560
是不是我们的Gint和我们的一个Socket

7
00:00:09,560 --> 00:00:11,340
去和我们的远程云附区区相连

8
00:00:11,340 --> 00:00:14,220
然后呢,那么Gint怎么样去和Walker之间通信呢

9
00:00:14,220 --> 00:00:14,880
是不是通过

10
00:00:14,880 --> 00:00:17,160
通过Gint和Walker之间

11
00:00:17,160 --> 00:00:19,020
他们之间去创建一个内部的Socket

12
00:00:19,020 --> 00:00:21,300
他们专门去进行一个通信

13
00:00:21,300 --> 00:00:22,420
然后进行数据的交流

14
00:00:22,420 --> 00:00:23,820
然后呢,把咱们的一个什么样

15
00:00:23,820 --> 00:00:25,140
把Masker给抛弃

16
00:00:25,140 --> 00:00:26,700
这里呢,就是我们这样介绍的一种

17
00:00:26,700 --> 00:00:28,940
解决我们刚才所存在一些问题的方式

18
00:00:28,940 --> 00:00:29,940
那么我们来看一下

19
00:00:29,940 --> 00:00:31,180
它呢其实是受到

20
00:00:31,180 --> 00:00:32,980
Needer for order这样一个模式的启发

21
00:00:32,980 --> 00:00:34,460
那么什么是Needer for order呢

22
00:00:34,460 --> 00:00:35,340
那么其实它是一种

23
00:00:35,340 --> 00:00:37,100
现成和进成之间那种架构方式

24
00:00:37,100 --> 00:00:39,160
也是咱们一种很高深的技术

25
00:00:39,160 --> 00:00:40,820
我们来看一下

26
00:00:40,820 --> 00:00:41,980
简单看一下

27
00:00:41,980 --> 00:00:44,400
好 这里呢是我收到的一篇文章

28
00:00:44,400 --> 00:00:45,760
其实大家可以看到

29
00:00:45,760 --> 00:00:47,340
好 我先把它放大一下

30
00:00:47,340 --> 00:00:50,560
好 其实呢大家可以看到

31
00:00:50,560 --> 00:00:51,560
那么参考

32
00:00:51,560 --> 00:00:56,980
参考58省建大神架构之路上的文章

33
00:00:56,980 --> 00:00:57,620
那么谈一谈

34
00:00:57,620 --> 00:00:58,720
Lead for water现成模型

35
00:00:58,720 --> 00:01:00,300
那么谁是省建大神呢

36
00:01:00,300 --> 00:01:00,960
省建大神呢

37
00:01:00,960 --> 00:01:02,960
其实它是58同城的技术中间

38
00:01:02,960 --> 00:01:03,840
好

39
00:01:03,840 --> 00:01:04,900
那么我们来看一下

40
00:01:04,900 --> 00:01:07,040
它到底什么是Lead for water的现成模型

41
00:01:07,040 --> 00:01:09,760
那么现成呢有三种状态

42
00:01:09,760 --> 00:01:11,780
领导就是Leader

43
00:01:11,780 --> 00:01:14,580
Process和追随Floin

44
00:01:14,580 --> 00:01:16,140
那么其实我们大家可以看到

45
00:01:16,140 --> 00:01:17,000
重点关注的是什么呢

46
00:01:17,000 --> 00:01:18,240
现成有三种状态

47
00:01:18,240 --> 00:01:19,500
其实我们刚才解决的问题

48
00:01:19,500 --> 00:01:20,300
是解决谁的问题啊

49
00:01:20,300 --> 00:01:20,960
是不是进程呢

50
00:01:20,960 --> 00:01:22,680
我们其实是设计进程的一种模式

51
00:01:22,680 --> 00:01:25,420
那么其实Lead of order它的最开始解决的是什么问题

52
00:01:25,420 --> 00:01:26,420
其实是线程之间的问题

53
00:01:26,420 --> 00:01:28,560
那么线程和进程的区别和联系是什么

54
00:01:28,560 --> 00:01:30,640
是我们一个进程里面可以包含多个线程

55
00:01:30,640 --> 00:01:32,440
那么你有多少个合就有多少个进程

56
00:01:32,440 --> 00:01:34,300
那么假设我们有N个线程

57
00:01:34,300 --> 00:01:35,600
那么其中有一个Leader

58
00:01:35,600 --> 00:01:36,900
也就是领导

59
00:01:36,900 --> 00:01:39,220
有一个有X个processing线程

60
00:01:39,220 --> 00:01:40,220
就是咱们那个干活的线程

61
00:01:40,220 --> 00:01:43,280
然后剩下有N-E-X个blowing线程

62
00:01:43,280 --> 00:01:45,300
也就是有多少个空险的线程

63
00:01:45,300 --> 00:01:47,160
blowing代表了空险的线程

64
00:01:47,160 --> 00:01:48,020
而且有一把锁

65
00:01:48,020 --> 00:01:50,080
那么谁抢到就是Leader

66
00:01:50,080 --> 00:01:51,280
也就是说

67
00:01:51,280 --> 00:01:52,000
比如说我们有十个县城

68
00:01:52,000 --> 00:01:52,500
对吧

69
00:01:52,500 --> 00:01:53,480
那么十个人去抢一把数

70
00:01:53,480 --> 00:01:54,460
那么谁抢到

71
00:01:54,460 --> 00:01:55,400
就是

72
00:01:55,400 --> 00:01:56,600
谁就是我们的一个老大

73
00:01:56,600 --> 00:01:57,280
那你就是老大

74
00:01:57,280 --> 00:01:57,520
好

75
00:01:57,520 --> 00:01:59,060
那么当事件任务来临时呢

76
00:01:59,060 --> 00:02:00,340
老大就会对县城进行处理

77
00:02:00,340 --> 00:02:01,620
从而转化成一个

78
00:02:01,620 --> 00:02:02,560
干活的状态

79
00:02:02,560 --> 00:02:03,080
对吧

80
00:02:03,080 --> 00:02:03,740
什么意思

81
00:02:03,740 --> 00:02:05,100
其实就是当我们来的活

82
00:02:05,100 --> 00:02:06,600
leader会去指派一些任务

83
00:02:06,600 --> 00:02:08,020
然后给processing去干活

84
00:02:08,020 --> 00:02:09,420
然后处理完之后

85
00:02:09,420 --> 00:02:10,280
那么我们的活干完了

86
00:02:10,280 --> 00:02:11,020
咱们的工人

87
00:02:11,020 --> 00:02:12,640
walker是不是就变成了一个什么呀

88
00:02:12,640 --> 00:02:13,340
变成了一个

89
00:02:13,340 --> 00:02:14,120
following

90
00:02:14,120 --> 00:02:14,880
也就是空闲状态

91
00:02:14,880 --> 00:02:15,100
好

92
00:02:15,100 --> 00:02:16,020
那么活干完了

93
00:02:16,020 --> 00:02:17,080
对吧

94
00:02:17,080 --> 00:02:17,980
老大就不存在了

95
00:02:17,980 --> 00:02:18,680
没活力就不是老大

96
00:02:18,680 --> 00:02:19,680
此时又会开始抢数

97
00:02:19,680 --> 00:02:21,300
那么抢到就会变成一个新的老大

98
00:02:21,300 --> 00:02:22,900
否则就会保持一个空险

99
00:02:22,900 --> 00:02:24,600
那么其实空险什么都不干

100
00:02:24,600 --> 00:02:25,160
就是抢数

101
00:02:25,160 --> 00:02:26,300
力图的成为leader

102
00:02:26,300 --> 00:02:26,880
什么意思

103
00:02:26,880 --> 00:02:28,060
其实核心是什么呢

104
00:02:28,060 --> 00:02:29,940
核心就是我们的leader是去派活

105
00:02:29,940 --> 00:02:31,400
我们的processing去干活

106
00:02:31,400 --> 00:02:32,860
那么空险的人就等待数

107
00:02:32,860 --> 00:02:34,680
谁抢到了谁就成为新的老大

108
00:02:34,680 --> 00:02:36,280
那么这里就是leader forwarding

109
00:02:36,280 --> 00:02:37,780
他这种思想

110
00:02:37,780 --> 00:02:40,400
那么其实和我们所讲到的

111
00:02:40,400 --> 00:02:42,420
这种模式其实区别还是挺大的

112
00:02:42,420 --> 00:02:42,720
为什么呀

113
00:02:42,720 --> 00:02:43,940
因为它是解决现成之间的问题

114
00:02:43,940 --> 00:02:44,900
其实我们解决是进程

115
00:02:44,900 --> 00:02:46,360
只是说大家其实也可以看到

116
00:02:46,360 --> 00:02:47,940
受到的是启发

117
00:02:47,940 --> 00:02:49,460
所以和它并不是一样的

118
00:02:49,460 --> 00:02:50,120
我们来看一下

119
00:02:50,120 --> 00:02:51,000
我们的客户端呢

120
00:02:51,000 --> 00:02:52,020
此时会分为两种角色

121
00:02:52,020 --> 00:02:53,400
Leader

122
00:02:53,400 --> 00:02:54,040
Follow

123
00:02:54,040 --> 00:02:55,520
那么什么是客户端

124
00:02:55,520 --> 00:02:56,960
其实这里我们需要去注意一个点

125
00:02:56,960 --> 00:02:57,620
什么是客户端呢

126
00:02:57,620 --> 00:02:58,900
客户端其实就是我们的应用程序

127
00:02:58,900 --> 00:03:00,040
就是我们的一GG

128
00:03:00,040 --> 00:03:01,540
就是我们一GG所写的代码

129
00:03:01,540 --> 00:03:02,340
其实它就代表客户端

130
00:03:02,340 --> 00:03:03,220
那么什么是Solver呢

131
00:03:03,220 --> 00:03:05,040
Solver其实代表的是我们的Socket服务器

132
00:03:05,040 --> 00:03:05,800
也比如说阿里云

133
00:03:05,800 --> 00:03:06,720
很大的云服务

134
00:03:06,720 --> 00:03:07,800
它呢代表服务端

135
00:03:07,800 --> 00:03:10,520
我们这里其实就不要把这个概念给混淆了

136
00:03:10,520 --> 00:03:12,000
那么如何确定谁是Leader

137
00:03:12,000 --> 00:03:12,840
随时follow呢

138
00:03:12,840 --> 00:03:14,200
有两种模式

139
00:03:14,200 --> 00:03:15,340
我们刚才所讲到了

140
00:03:15,340 --> 00:03:16,380
是不是自由进行模式

141
00:03:16,380 --> 00:03:17,660
我们的空闲会去抢锁

142
00:03:17,660 --> 00:03:18,680
但是这里呢

143
00:03:18,680 --> 00:03:19,500
还有一种

144
00:03:19,500 --> 00:03:20,400
是不是强指令模式

145
00:03:20,400 --> 00:03:20,900
就是呢

146
00:03:20,900 --> 00:03:21,800
你说谁是老大

147
00:03:21,800 --> 00:03:22,500
谁就是老大

148
00:03:22,500 --> 00:03:23,740
那么剩下来都是follow

149
00:03:23,740 --> 00:03:24,380
都是干活了

150
00:03:24,380 --> 00:03:25,460
或者是空闲了

151
00:03:25,460 --> 00:03:26,200
那么在一机器

152
00:03:26,200 --> 00:03:26,940
在一个框架里面呢

153
00:03:26,940 --> 00:03:27,740
我们所采取的是

154
00:03:27,740 --> 00:03:29,240
强制指令模式

155
00:03:29,240 --> 00:03:29,720
什么意思

156
00:03:29,720 --> 00:03:30,540
强制指令模式就是

157
00:03:30,540 --> 00:03:31,560
咱们的一个

158
00:03:31,560 --> 00:03:33,200
在一个框架里面

159
00:03:33,200 --> 00:03:34,200
它会去指令随时老大

160
00:03:34,200 --> 00:03:34,980
那么呢

161
00:03:34,980 --> 00:03:35,600
框架启动的时候

162
00:03:35,600 --> 00:03:36,620
Master会随机选择一个

163
00:03:36,620 --> 00:03:37,160
可用的端口

164
00:03:37,160 --> 00:03:39,920
作为class client去监听我们的一个通讯端口

165
00:03:39,920 --> 00:03:41,480
什么意思

166
00:03:41,480 --> 00:03:43,120
其实就是master它会随机选一个人

167
00:03:43,120 --> 00:03:44,500
去监听我们的一个通讯端口

168
00:03:44,500 --> 00:03:45,840
就是和我们的socket的服务器

169
00:03:45,840 --> 00:03:46,540
去建立一个链接

170
00:03:46,540 --> 00:03:48,660
然后把它通过参数传递给gint

171
00:03:48,660 --> 00:03:49,620
和我们的一个app walker

172
00:03:49,620 --> 00:03:50,980
那么leader和follow之间呢

173
00:03:50,980 --> 00:03:52,160
就通过socket的直连

174
00:03:52,160 --> 00:03:53,680
不再需要master中转

175
00:03:53,680 --> 00:03:54,700
那么leader和follow

176
00:03:54,700 --> 00:03:55,520
随时leader随时follow

177
00:03:55,520 --> 00:03:56,400
是不是就是我们的gint

178
00:03:56,400 --> 00:03:57,180
和我们的walker之间呢

179
00:03:57,180 --> 00:03:58,120
就直接进行通信

180
00:03:58,120 --> 00:03:58,880
那么其实呢

181
00:03:58,880 --> 00:03:59,920
我们就可以直接看到这幅图

182
00:03:59,920 --> 00:04:00,980
这幅图

183
00:04:00,980 --> 00:04:03,700
我们的leader是谁啊

184
00:04:03,700 --> 00:04:04,460
我们的leader

185
00:04:04,460 --> 00:04:08,080
我们的leader是不是就是我们的一个jent

186
00:04:08,080 --> 00:04:10,820
那么我们的jent就和我们的扣端相连

187
00:04:10,820 --> 00:04:12,860
和扣端相连去通信

188
00:04:12,860 --> 00:04:14,180
那么通信完成之后

189
00:04:14,180 --> 00:04:14,680
通过什么呀

190
00:04:14,680 --> 00:04:16,360
是不是通过tcp connect

191
00:04:16,360 --> 00:04:19,040
tcp connect其实就代表我们一个内部的socket

192
00:04:19,040 --> 00:04:19,800
他们之间呢

193
00:04:19,800 --> 00:04:21,020
通过这样的模式和我们的follow

194
00:04:21,020 --> 00:04:22,040
也就是我们walker去通信

195
00:04:22,040 --> 00:04:23,820
是不是和我们刚才讲的是一模一样的

196
00:04:23,820 --> 00:04:26,700
好 这里呢就是我们的一个核心的思想

197
00:04:26,700 --> 00:04:29,420
好 那么我们来总结一下

198
00:04:29,420 --> 00:04:31,760
其实呢 我们的核心思想是什么

199
00:04:31,760 --> 00:04:34,080
核心思想是不是参考leaderforwall

200
00:04:34,080 --> 00:04:36,560
NeedFollow这样的一种模式

201
00:04:36,560 --> 00:04:38,060
那么NeedFollow其实呢

202
00:04:38,060 --> 00:04:39,260
它其实来源于什么呀

203
00:04:39,260 --> 00:04:40,180
是不是来源于多进程

204
00:04:40,180 --> 00:04:43,240
那么只是说了我们把这种思想

205
00:04:43,240 --> 00:04:45,640
借鉴到我们的一种多进程的模式里面

206
00:04:45,640 --> 00:04:46,800
那么它的核心是什么

207
00:04:46,800 --> 00:04:47,640
就是Need

208
00:04:47,640 --> 00:04:49,360
其实Need其实就是我们的Gint

209
00:04:49,360 --> 00:04:51,100
和远程链接

210
00:04:51,100 --> 00:04:56,700
然后它通过内部的Circuit和Walker通信

211
00:04:56,700 --> 00:04:58,180
其实这就是它的一个核心思想

212
00:04:58,180 --> 00:05:00,500
那么这里就是我们这节目的内容

