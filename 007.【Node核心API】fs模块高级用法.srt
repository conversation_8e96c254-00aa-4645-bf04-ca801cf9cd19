1
00:00:00,000 --> 00:00:03,080
好 接下来我们来看一下fs的一些高级方法

2
00:00:03,080 --> 00:00:05,120
首先第一个打开文件open

3
00:00:05,120 --> 00:00:10,240
他接收四个参数 第一个pass 第二个flog 第三个也就是我们的mod 权限位

4
00:00:10,240 --> 00:00:11,520
 第四个就是一个cobike

5
00:00:11,520 --> 00:00:14,600
他是一个eable的方法 那么open对应的其实没有同步的

6
00:00:14,600 --> 00:00:16,120
我们来看一下他如何使用

7
00:00:16,120 --> 00:00:20,740
比如说我们去open open的一个tst 然后给他的一个标志位为r

8
00:00:20,740 --> 00:00:24,580
回调函数里面会给到一个fd 也就是我们的文件的描述符

9
00:00:25,860 --> 00:00:29,320
然后open呢 其实没有做什么事情 因为open之后呢 我们可以对它进行写入

10
00:00:29,320 --> 00:00:31,060
 其实和我们的写入 包括读取呢

11
00:00:31,060 --> 00:00:35,080
它是进行一个配合的一个操作 你单纯的去open 对一个文件open 其实

12
00:00:35,080 --> 00:00:39,680
没有什么太大的一些实质性的效果 好 这里呢 我呢 准备了一个open.js

13
00:00:39,680 --> 00:00:41,480
好 假如说我这里呢 去

14
00:00:41,480 --> 00:00:47,490
open了我们的一个 咱们刚才的tsd文件 也是1.tsd 我们来看一下 它的一个执行结果是什么

15
00:00:47,490 --> 00:00:50,560
 大家可以看到了 此时我们是不是返回了它的一个文件描述符啊

16
00:00:50,560 --> 00:00:55,160
是不是 34 对吧 也就是ft 这里呢 就是open

17
00:00:55,540 --> 00:00:56,560
接下来我们来看一下close

18
00:00:56,560 --> 00:01:00,140
Close呢它的使用呢也很简单

19
00:01:00,140 --> 00:01:02,200
我们呢直接调用它的一个close方法

20
00:01:02,200 --> 00:01:03,740
然后传入我们的什么呢

21
00:01:03,740 --> 00:01:05,020
那么第1个参数呢

22
00:01:05,020 --> 00:01:07,060
close你必须传入它的一个文件描述符

23
00:01:07,060 --> 00:01:09,100
那么如果说你传入一个路径的话可能就

24
00:01:09,100 --> 00:01:09,880
不好使

25
00:01:09,880 --> 00:01:12,700
这里呢就是它的一个close的操作

26
00:01:12,700 --> 00:01:14,220
那么close有什么作用呢

27
00:01:14,220 --> 00:01:15,760
比如说我们把一个文件open之后呢

28
00:01:15,760 --> 00:01:17,560
我对它进行写入或者读取

29
00:01:17,560 --> 00:01:18,840
那么我们写入或者读取完成之后

30
00:01:18,840 --> 00:01:20,120
你是不是要把我们的文件

31
00:01:20,120 --> 00:01:22,160
把它的状态把它给关闭呀

32
00:01:22,160 --> 00:01:24,220
接下来我们来看一下我们读取文件

33
00:01:24,460 --> 00:01:26,760
那么RED是一个什么样的方法呢

34
00:01:26,760 --> 00:01:28,820
RED的方法与我们的RED非要不同

35
00:01:28,820 --> 00:01:30,860
那么一般针对于文件太大

36
00:01:30,860 --> 00:01:33,420
那么无法一次性读取全部内容到缓冲中

37
00:01:33,420 --> 00:01:35,460
或者我们的文件大小未知的情况

38
00:01:35,460 --> 00:01:38,020
比如说你是不是可以把你的文件

39
00:01:38,020 --> 00:01:40,580
以多次的方法读到我们的那个buffer中

40
00:01:40,580 --> 00:01:41,360
大家有没有

41
00:01:41,360 --> 00:01:44,680
想到什么呀是不是我们的RED

42
00:01:44,680 --> 00:01:46,740
Random load to stream

43
00:01:46,740 --> 00:01:48,520
也就是我们进行浮端渲染的时候

44
00:01:48,520 --> 00:01:50,320
如果说我们的HTML太大

45
00:01:50,320 --> 00:01:52,100
是不是我们可以以牛的形式分段

46
00:01:52,100 --> 00:01:53,380
输入到我们的一个浏览器中

47
00:01:53,640 --> 00:01:58,500
其实这里和我们的一个red的他的思想 是非常相似的 我们来看一下他是如何去使用的

48
00:01:58,500 --> 00:02:04,640
首先呢 进入fs模块 然后呢 通过buffer.alloc 创建一个长度为6的一个数组

49
00:02:04,640 --> 00:02:04,900
 对吧

50
00:02:04,900 --> 00:02:08,240
这里呢 不能够叫做数组 如果说叫做数组是不准确的

51
00:02:08,240 --> 00:02:12,080
那么长度为6的一个二进制的一个buffer 你应该这么去理解

52
00:02:12,080 --> 00:02:15,660
好 我们能打开一个文件之后呢 是不是可以得到他的一个文件描述符

53
00:02:15,660 --> 00:02:20,520
好 我们通过调用fs.red的第一个参数传入我们的一个文件描述符 然后传入我们的buffer

54
00:02:20,780 --> 00:02:23,660
好 最后呢 回到函数里面 师傅就可以给我们 咱们

55
00:02:23,660 --> 00:02:28,780
通过去读取Buffer的一些信息啊 对吧 好 这里呢 就是我们red的一个使用

56
00:02:28,780 --> 00:02:29,480
 其实呢

57
00:02:29,480 --> 00:02:33,820
我们在真正的实际的项目中呢 如果说你遇到一个非常大的一个文件的话

58
00:02:33,820 --> 00:02:35,620
 其实我们使用最多的是 create

59
00:02:35,620 --> 00:02:36,660
redstream

60
00:02:36,660 --> 00:02:41,110
那么其实它呢 你可以理解为是对我们red这样一个方法的一个封装 好

61
00:02:41,110 --> 00:02:42,540
 我们来看一下它是如何去使用的

62
00:02:42,540 --> 00:02:46,900
好 这里呢 我们来创建一个

63
00:02:46,900 --> 00:02:49,700
create redstream

64
00:02:50,780 --> 00:03:00,260
那么其实这样的一个方法呢 可能大家需要去结合 我们后面会讲到loadgs里面有一个stream模块

65
00:03:00,260 --> 00:03:05,620
也就是我们所谓的一个牛 如果说大家学习过stream呢 可能我们理解这个可能会更好去理解

66
00:03:05,620 --> 00:03:06,400
 不过没有关系

67
00:03:06,400 --> 00:03:14,070
不影响大家一个学习 比如说这里呢 我呢 首先创建一个restream 通过fs.createrestream这个方法

68
00:03:14,070 --> 00:03:16,120
 创建我们的一个可读的一个牛

69
00:03:16,120 --> 00:03:19,200
创建可读

70
00:03:20,480 --> 00:03:24,580
好 那么第一个参数就传入我们的就是文件的一个路径了

71
00:03:24,580 --> 00:03:27,900
路径我就从咱们前面所编写的代码中我们把它给复制过来

72
00:03:27,900 --> 00:03:32,000
然后它的编码就是utf-8

73
00:03:32,000 --> 00:03:34,560
好 大家可以看到

74
00:03:34,560 --> 00:03:36,860
我们在redstream上面通过

75
00:03:36,860 --> 00:03:38,400
去监听了一些视线

76
00:03:38,400 --> 00:03:39,940
所以说这里我们是不是可以

77
00:03:39,940 --> 00:03:41,480
能够联想到

78
00:03:41,480 --> 00:03:45,060
我们的create redstream它所返回的一个对象是不是继承于谁啊

79
00:03:45,060 --> 00:03:47,100
是不是继承于我们的event的这样一个对象

80
00:03:47,100 --> 00:03:48,640
其实确实也是这样的

81
00:03:48,900 --> 00:03:51,980
好 那么他呢先听了 arrow 以及open 包括ready事件

82
00:03:51,980 --> 00:03:56,330
包括我们的data 好 那么error是什么呢 自然就是我们去读取文件发生错误的时候就会触发

83
00:03:56,330 --> 00:03:56,580
 arrow

84
00:03:56,580 --> 00:03:57,860
那么open 就是我们

85
00:03:57,860 --> 00:04:01,960
因为刚才我们讲到了 我们去对文件进行读写的时候 是不是首先debite进行open来打开

86
00:04:01,960 --> 00:04:06,820
好 那么打开之后呢 需要我们的文件是不是需要去ready ready之后呢 你才能够去对它进行读写

87
00:04:06,820 --> 00:04:11,420
当你读到数据之后呢 就会触发beta这样一个方法 里面就会去打印出我们所需要的数据

88
00:04:11,420 --> 00:04:13,220
那么我们去读取完成之后呢

89
00:04:13,220 --> 00:04:16,040
是不是会触发一个end这样的一个实践呢

90
00:04:17,320 --> 00:04:20,640
那么这里呢我们就来看一下它的读写的一个过程

91
00:04:20,640 --> 00:04:26,020
我们执行loadfs createresume.js

92
00:04:26,020 --> 00:04:27,560
好 我们来看一下

93
00:04:27,560 --> 00:04:30,380
大家可以看到 首先第一步我们文件是不是要打开

94
00:04:30,380 --> 00:04:32,420
接下来的我们文件需要准备好

95
00:04:32,420 --> 00:04:33,960
那么准备好之后呢是不是开始

96
00:04:33,960 --> 00:04:36,780
想用我们的data事件开始去读取我们的一个数据

97
00:04:36,780 --> 00:04:38,560
那么如果说你的数据非常的大

98
00:04:38,560 --> 00:04:40,880
那么我们的data事件可能会触发多次

99
00:04:40,880 --> 00:04:41,380
对吧

100
00:04:41,380 --> 00:04:43,940
我们假如你的文章你的一个txt有100兆

101
00:04:43,940 --> 00:04:45,220
100万字的一本小说

102
00:04:45,480 --> 00:04:47,780
我们的data会分 多次去返回

103
00:04:47,780 --> 00:04:50,340
然后呢 我们是不是需要在外面去定义一个

104
00:04:50,340 --> 00:04:53,160
咱们的一个数据 或是一个其他的一些方式

105
00:04:53,160 --> 00:04:55,200
我们来接收这些truck 然后把数据拼接

106
00:04:55,200 --> 00:04:58,540
好 这里就是咱们redstream所常用的一些

107
00:04:58,540 --> 00:04:59,820
应用场景

108
00:04:59,820 --> 00:05:02,120
我们fs的一些高级使用呢 咱们就

109
00:05:02,120 --> 00:05:03,140
学习到这里

110
00:05:03,140 --> 00:05:04,680
我们常见的就是open

111
00:05:04,680 --> 00:05:05,440
close

112
00:05:05,440 --> 00:05:06,480
red

113
00:05:06,480 --> 00:05:06,980
以及呢

114
00:05:06,980 --> 00:05:08,780
createredstream

115
00:05:08,780 --> 00:05:10,820
那么我们最常用的其实就是我们

116
00:05:10,820 --> 00:05:12,100
createredstream这样一个方法

117
00:05:12,100 --> 00:05:14,660
好 那么什么是stream呢 后面我们会在

118
00:05:14,920 --> 00:05:19,440
那咱们react stream这个模块里面呢 会去做一些更详细的分析 好 这里就是我们这几个的内容

