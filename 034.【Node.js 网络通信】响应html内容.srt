1
00:00:00,000 --> 00:00:01,700
我们现在可以看到

2
00:00:01,700 --> 00:00:03,840
在我们所响应回来的这种数据消息呢

3
00:00:03,840 --> 00:00:05,480
目前呢都是普通的字符

4
00:00:05,480 --> 00:00:07,540
那么我们接下来我们就看一下

5
00:00:07,540 --> 00:00:09,720
如何在这个HTTP服务当中呢

6
00:00:09,720 --> 00:00:11,800
来响应XMIRR格式的内容数据

7
00:00:11,800 --> 00:00:13,260
那么这里的话呢

8
00:00:13,260 --> 00:00:15,900
我们在这里呢就新建一个02XMIRR.js

9
00:00:15,900 --> 00:00:17,860
我们基于我们刚才的00

10
00:00:17,860 --> 00:00:18,640
来进行构建

11
00:00:18,640 --> 00:00:20,680
那么现在的话我们就简单一点

12
00:00:20,680 --> 00:00:21,580
我们可以在这里

13
00:00:21,580 --> 00:00:24,100
我们先把这段代码先给它揍掉

14
00:00:24,100 --> 00:00:26,440
然后接下来我们希望当请搜过来以后

15
00:00:26,440 --> 00:00:27,820
我们响应一段XMIRR内容

16
00:00:27,820 --> 00:00:29,520
那么响应一段XMIRR内容的话

17
00:00:29,520 --> 00:00:31,440
注意我们其实最简单的方式

18
00:00:31,440 --> 00:00:33,220
就是我们还是这段代码

19
00:00:33,220 --> 00:00:33,780
我们不动它

20
00:00:33,780 --> 00:00:34,920
其实我们不需要来动

21
00:00:34,920 --> 00:00:36,500
我们首先把text干

22
00:00:36,500 --> 00:00:38,040
把这个clean我们来改成atmar

23
00:00:38,040 --> 00:00:39,380
那么这个就表示

24
00:00:39,380 --> 00:00:41,200
我们此时为这个浏览器

25
00:00:41,200 --> 00:00:42,100
或者说为客户端

26
00:00:42,100 --> 00:00:43,080
响应的告诉他

27
00:00:43,080 --> 00:00:44,060
我给你发的数据

28
00:00:44,060 --> 00:00:45,680
内容类型是文本

29
00:00:45,680 --> 00:00:47,420
并且它的格式是atmar格式

30
00:00:47,420 --> 00:00:49,860
然后接下来在这个response.end当中

31
00:00:49,860 --> 00:00:51,480
我们接下来不再去想写这个holloworld了

32
00:00:51,480 --> 00:00:52,740
我们可以在这里简单一点

33
00:00:52,740 --> 00:00:53,580
利用我们来个h1

34
00:00:53,580 --> 00:00:55,660
利用在这里来个holloworld

35
00:00:55,660 --> 00:00:58,120
那么这样把它改好以后

36
00:00:58,120 --> 00:00:59,900
然后接下来回到我们的命连号当中

37
00:00:59,900 --> 00:01:02,060
我们来把这个脚本来给它启动起来

38
00:01:02,060 --> 00:01:03,380
好 回到这里

39
00:01:03,380 --> 00:01:05,380
首先我们把刚才这个来给它关掉

40
00:01:05,380 --> 00:01:08,180
然后接下来我们来执行我们的02-amr.js

41
00:01:08,180 --> 00:01:09,800
好 那么启动成功以后

42
00:01:09,800 --> 00:01:11,660
然后接下来回到我们的浏览器当中

43
00:01:11,660 --> 00:01:12,940
好 我们在浏览器当中

44
00:01:12,940 --> 00:01:14,460
我们在这里来刷新一下

45
00:01:14,460 --> 00:01:15,760
好 那么此时我们就可以看到

46
00:01:15,760 --> 00:01:17,660
我们正确的收到了这个Hello World

47
00:01:17,660 --> 00:01:17,660
好 那么此时我们就可以看到我们正确的收到了这个Hello World

48
00:01:17,660 --> 00:01:17,660
好 那么此时我们就可以看到我们正确的收到了这个Hello World

49
00:01:17,660 --> 00:01:17,660
好 那么此时我们就可以看到我们正确的收到了这个Hello World

50
00:01:17,660 --> 00:01:19,660
我们可以在这里查看网友的代码

51
00:01:19,660 --> 00:01:22,840
我们就看到此时就是我们看到的这个响应的消息内容

52
00:01:22,840 --> 00:01:27,220
当然除了通过这种方式来查看我们本次请求收到的响应消息之外

53
00:01:27,220 --> 00:01:30,460
我们还可以通过打开我们浏览器的开发者工具

54
00:01:30,460 --> 00:01:31,280
打开这个控制台

55
00:01:31,280 --> 00:01:33,640
我们也可以看到在这个network当中

56
00:01:33,640 --> 00:01:36,240
也能看到这个请求头响应头

57
00:01:36,240 --> 00:01:37,500
包括这个response

58
00:01:37,500 --> 00:01:40,180
看到我们本次的响应消息也是可以的

59
00:01:40,180 --> 00:01:43,920
那么这个呢是发送了这个简单的一个ADMark格式内容

60
00:01:43,920 --> 00:01:46,180
那么如果说我们这里对于中文该怎么办

61
00:01:46,180 --> 00:01:47,440
我们在这里来看一下

62
00:01:47,440 --> 00:01:50,100
当然我为了让这个内容写的更多一些

63
00:01:50,100 --> 00:01:50,800
我们可以这样

64
00:01:50,800 --> 00:01:52,980
我们用这个ES6的这个模板自无串

65
00:01:52,980 --> 00:01:55,820
这样的话我们就可以让这个蛋去进行换行了

66
00:01:55,820 --> 00:01:57,220
首先我们拿个H1

67
00:01:57,220 --> 00:01:58,380
我们再来个批标签

68
00:01:58,380 --> 00:02:00,140
流离行实际

69
00:02:00,140 --> 00:02:01,820
那么中文写好以后

70
00:02:01,820 --> 00:02:03,660
那么同样的对于我们服务端脚本

71
00:02:03,660 --> 00:02:04,660
此时已经发生改变了

72
00:02:04,660 --> 00:02:06,920
所以我们继续回到控制档当中

73
00:02:06,920 --> 00:02:08,700
Ctrl C把它打断

74
00:02:08,700 --> 00:02:09,460
那么此时的话

75
00:02:09,460 --> 00:02:10,820
我就不再去使用Node的命令

76
00:02:10,820 --> 00:02:11,900
来执行这个js脚本了

77
00:02:11,900 --> 00:02:13,740
因为我们每次改用代码来手动重启

78
00:02:13,740 --> 00:02:14,260
特别麻烦

79
00:02:14,260 --> 00:02:15,860
所以大家在这里可以使用一个

80
00:02:15,860 --> 00:02:18,580
就是GNode开发的一个全局命令和工具

81
00:02:18,580 --> 00:02:19,740
叫做node.mo

82
00:02:19,740 --> 00:02:21,140
那么这个工具的使用

83
00:02:21,140 --> 00:02:22,520
那么首先你要来全局安装

84
00:02:22,520 --> 00:02:25,340
它的安装命令就是npm install.g.node.mo

85
00:02:25,340 --> 00:02:26,780
那么我这里已经装过了

86
00:02:26,780 --> 00:02:28,240
所以我就不再去执行这条命令了

87
00:02:28,240 --> 00:02:28,920
如果你没有的话

88
00:02:28,920 --> 00:02:29,840
你可以执行这条命令

89
00:02:29,840 --> 00:02:31,220
来安装一下node.mo这个工具

90
00:02:31,220 --> 00:02:32,360
安装好以后

91
00:02:32,360 --> 00:02:33,760
那么它就可以帮我们去

92
00:02:33,760 --> 00:02:35,360
监视我们文件的改动

93
00:02:35,360 --> 00:02:36,600
如果文件发生改变

94
00:02:36,600 --> 00:02:39,100
那么它会帮我们自动来重启这个服务

95
00:02:39,100 --> 00:02:40,080
所以现在的话

96
00:02:40,080 --> 00:02:41,020
我这已经装好了

97
00:02:41,020 --> 00:02:42,160
所以说我直接再去去node.mo

98
00:02:42,160 --> 00:02:43,920
然后02.atmo.js

99
00:02:43,920 --> 00:02:45,120
那么这样的话

100
00:02:45,120 --> 00:02:46,040
它就以监视模式

101
00:02:46,040 --> 00:02:47,580
启动了我们这个HTTP服务

102
00:02:47,580 --> 00:02:48,680
接下来就是当文件发生改变

103
00:02:48,680 --> 00:02:49,380
它会自动重启

104
00:02:49,380 --> 00:02:50,500
好那么这个时候

105
00:02:50,500 --> 00:02:52,260
我们回到我们的浏览器当中

106
00:02:52,260 --> 00:02:53,060
我们来测试一下

107
00:02:53,060 --> 00:02:53,600
我们来刷新

108
00:02:53,600 --> 00:02:54,580
我们可以看到

109
00:02:54,580 --> 00:02:56,760
首先我们本次所想的内容

110
00:02:56,760 --> 00:02:57,540
肯定是对的

111
00:02:57,540 --> 00:02:57,860
H1

112
00:02:57,860 --> 00:02:59,040
P标签你好世界

113
00:02:59,040 --> 00:03:00,680
但是它在渲染显示

114
00:03:00,680 --> 00:03:01,500
在这个页面的时候

115
00:03:01,500 --> 00:03:02,680
它并没有正常显示

116
00:03:02,680 --> 00:03:04,440
而是这个时候它乱买了

117
00:03:04,440 --> 00:03:05,460
那么这个原因呢

118
00:03:05,460 --> 00:03:06,040
也主要就是

119
00:03:06,040 --> 00:03:07,340
它的解决方式呢

120
00:03:07,340 --> 00:03:07,800
非常简单

121
00:03:07,800 --> 00:03:09,320
就是我们只需要

122
00:03:09,320 --> 00:03:11,360
在你发送这个内容的时候

123
00:03:11,360 --> 00:03:12,340
这个想头这里

124
00:03:12,340 --> 00:03:13,780
我们此时告诉它

125
00:03:13,780 --> 00:03:15,060
是文本ATMR格式

126
00:03:15,060 --> 00:03:16,500
我们还要再来加一个东西

127
00:03:16,500 --> 00:03:17,760
叫做Charsight

128
00:03:17,760 --> 00:03:20,940
Charsight是UTF-8

129
00:03:20,940 --> 00:03:22,040
我们要告诉他

130
00:03:22,040 --> 00:03:24,120
我们本次所给你想的这个数据的编码格式

131
00:03:24,120 --> 00:03:25,000
是UTF-8

132
00:03:25,000 --> 00:03:25,820
那么这样的话

133
00:03:25,820 --> 00:03:27,220
浏览器才会正确的

134
00:03:27,220 --> 00:03:29,140
把这个中文去正常确认

135
00:03:29,140 --> 00:03:30,060
那么改好以后

136
00:03:30,060 --> 00:03:31,100
我们首先来看一下

137
00:03:31,100 --> 00:03:31,900
在我们的命令行当中

138
00:03:31,900 --> 00:03:32,480
我们发现

139
00:03:32,480 --> 00:03:34,740
诶 服务端价值是不是就已经正常的自动重启了

140
00:03:34,740 --> 00:03:36,100
那么重启好以后

141
00:03:36,100 --> 00:03:37,800
我们回到浏览器里面

142
00:03:37,800 --> 00:03:38,680
此时我们再来刷新

143
00:03:38,680 --> 00:03:39,320
我们就会看到

144
00:03:39,320 --> 00:03:40,360
这个中文在这里呢

145
00:03:40,360 --> 00:03:41,960
就正常的被显示出来了

146
00:03:41,960 --> 00:03:44,360
所以说它在这里就是这样的

147
00:03:44,360 --> 00:03:45,780
那么现在的话我们可以看到

148
00:03:45,780 --> 00:03:48,560
我们现在我们所发送的这些数据

149
00:03:48,560 --> 00:03:49,280
都是什么的

150
00:03:49,280 --> 00:03:50,840
都是就是我们写词的这种

151
00:03:50,840 --> 00:03:52,400
就是HMR格式的字符串

152
00:03:52,400 --> 00:03:53,340
对于写词这种

153
00:03:53,340 --> 00:03:54,120
那肯定很不方便

154
00:03:54,120 --> 00:03:56,280
例如如果说我们要来修改这个HMR结构

155
00:03:56,280 --> 00:03:57,180
改了你们的内容

156
00:03:57,180 --> 00:03:58,660
那我们还得来改Node+Stimer

157
00:03:58,660 --> 00:03:59,620
那这样的话就太麻烦

158
00:03:59,620 --> 00:04:00,860
所以说我们通常情况下

159
00:04:00,860 --> 00:04:03,140
会把我们的这个HMR格式的内容

160
00:04:03,140 --> 00:04:04,480
会给它写到一个

161
00:04:04,480 --> 00:04:06,920
就是1.HMR结尾的这样一个文件当中

162
00:04:06,920 --> 00:04:08,880
那么接下来我们如何

163
00:04:08,880 --> 00:04:09,940
也就是说我们如何

164
00:04:09,940 --> 00:04:11,580
把这个文件中的内容

165
00:04:11,580 --> 00:04:12,820
去发给这个浏览器呢

166
00:04:12,820 --> 00:04:13,480
也就是说

167
00:04:13,480 --> 00:04:14,760
我们要把我们这个

168
00:04:14,760 --> 00:04:15,980
就是页面的内容

169
00:04:15,980 --> 00:04:17,140
写到ATML文件当中

170
00:04:17,140 --> 00:04:18,080
不仅有高亮

171
00:04:18,080 --> 00:04:18,840
写起来也方便

172
00:04:18,840 --> 00:04:20,020
维护起来也更好了

173
00:04:20,020 --> 00:04:21,220
那也就是说

174
00:04:21,220 --> 00:04:22,400
那既然你写到文件里面了

175
00:04:22,400 --> 00:04:23,060
那也就是接下来

176
00:04:23,060 --> 00:04:23,920
我们不应该在这里

177
00:04:23,920 --> 00:04:24,720
直接去sign的这个

178
00:04:24,720 --> 00:04:25,640
我们写死的这个字符传

179
00:04:25,640 --> 00:04:27,720
我们如何sign的这个页面呢

180
00:04:27,720 --> 00:04:28,340
所以说这个时候

181
00:04:28,340 --> 00:04:29,220
我们就需要用到

182
00:04:29,220 --> 00:04:29,800
在Node中

183
00:04:29,800 --> 00:04:31,260
为我们提供的一个核心模块

184
00:04:31,260 --> 00:04:32,260
也就是FS核心模块

185
00:04:32,260 --> 00:04:33,520
那它的作用呢

186
00:04:33,520 --> 00:04:35,240
就是专门用来操作文件的

187
00:04:35,240 --> 00:04:37,460
也就是说它可以去通过读取文件的方式

188
00:04:37,460 --> 00:04:38,940
把这个文件的内容给它读出来

189
00:04:38,940 --> 00:04:40,100
读到以后

190
00:04:40,100 --> 00:04:43,180
然后我们再来把它去发送到我们的这个客户端

191
00:04:43,180 --> 00:04:45,160
那么它读取文件的方式呢

192
00:04:45,160 --> 00:04:45,860
也非常简单

193
00:04:45,860 --> 00:04:47,600
我们只需要在这里这样来做

194
00:04:47,600 --> 00:04:48,780
我们只要在这去

195
00:04:48,780 --> 00:04:50,660
fs.reader file

196
00:04:50,660 --> 00:04:52,940
另外我这里我要来读取

197
00:04:52,940 --> 00:04:55,060
我当前目录的这个叫index.atmr这个文件

198
00:04:55,060 --> 00:04:56,860
然后接下来我们可以

199
00:04:56,860 --> 00:04:58,180
error一个data

200
00:04:58,180 --> 00:05:00,780
通过回调函数来接收这个结果

201
00:05:00,780 --> 00:05:03,000
如果error就表示我们在这里读取失败

202
00:05:03,000 --> 00:05:04,140
我们在这里简单一点

203
00:05:04,140 --> 00:05:04,900
throw error一下

204
00:05:04,900 --> 00:05:05,680
抛出一场

205
00:05:05,680 --> 00:05:06,960
如果说没有问题

206
00:05:06,960 --> 00:05:09,000
那么接下来我们就把这段代码

207
00:05:09,000 --> 00:05:10,280
来给它放到这里

208
00:05:10,280 --> 00:05:13,100
给它放到这里

209
00:05:13,100 --> 00:05:13,800
放过来以后

210
00:05:13,800 --> 00:05:15,660
然后接下来我们在这里去response.end

211
00:05:15,660 --> 00:05:16,300
end什么呢

212
00:05:16,300 --> 00:05:18,600
end我们通过readfile读到的这个

213
00:05:18,600 --> 00:05:20,260
稳件的数据data

214
00:05:20,260 --> 00:05:21,600
我们把稳件中的内容

215
00:05:21,600 --> 00:05:24,100
在这里就发送给了客户端用版器

216
00:05:24,100 --> 00:05:25,140
那么这样改好以后

217
00:05:25,140 --> 00:05:25,760
让家来注意

218
00:05:25,760 --> 00:05:26,920
此时我们注意看

219
00:05:26,920 --> 00:05:27,760
我们在这个一面当中

220
00:05:27,760 --> 00:05:28,980
我们写了只有一个he

221
00:05:28,980 --> 00:05:29,680
然后是Hello World

222
00:05:29,680 --> 00:05:31,540
我们来刷新看一下

223
00:05:31,540 --> 00:05:32,820
那么此时我们就可以看到

224
00:05:32,820 --> 00:05:34,080
此时只看到了一个号外

225
00:05:34,080 --> 00:05:36,260
好我们打开这个响应题

226
00:05:36,260 --> 00:05:36,800
我们就可以看到

227
00:05:36,800 --> 00:05:38,620
此时的response响应结果

228
00:05:38,620 --> 00:05:41,600
就是我们这个音频中所写的内容

229
00:05:41,600 --> 00:05:42,320
好那么接下来

230
00:05:42,320 --> 00:05:43,920
我们可以利用我们来做一个简单的修改

231
00:05:43,920 --> 00:05:44,820
利用我们再来个批标签

232
00:05:44,820 --> 00:05:46,440
例如你好世界

233
00:05:46,440 --> 00:05:48,460
好那么这个改好以后

234
00:05:48,460 --> 00:05:50,220
然后接下来我们再来哈欣重新请求

235
00:05:50,220 --> 00:05:51,680
那么重新请求我们就看到

236
00:05:51,680 --> 00:05:52,660
这里又看到你好世界

237
00:05:52,660 --> 00:05:53,680
好我们打开它

238
00:05:53,680 --> 00:05:55,100
我们看到这个响应题当中

239
00:05:55,100 --> 00:05:57,900
实际上就是这个文件所对应的内容

240
00:05:57,900 --> 00:06:00,760
那么这个就是我们在node中

241
00:06:00,760 --> 00:06:04,860
如何去输出一个页面到客户端

242
00:06:04,860 --> 00:06:08,000
主要就是通过这种读文件的这种方式

243
00:06:08,000 --> 00:06:10,280
那渲染就是或者说发送ATML数据

244
00:06:10,280 --> 00:06:12,000
我们一定或者说最好

245
00:06:12,000 --> 00:06:13,400
要来给它指定这个小型头

246
00:06:13,400 --> 00:06:14,060
Content Type

247
00:06:14,060 --> 00:06:15,500
要把它设置为TextATML

248
00:06:15,500 --> 00:06:17,980
并且指定Tyreset为UTF-8类型

249
00:06:17,980 --> 00:06:18,780
否则的话

250
00:06:18,780 --> 00:06:19,980
你的中文可能会乱买

