1
00:00:00,000 --> 00:00:01,980
熟悉了这个对象类型定义之后呢

2
00:00:01,980 --> 00:00:03,020
关于这个对象中的字段

3
00:00:03,020 --> 00:00:04,240
其实也可以传递参数

4
00:00:04,240 --> 00:00:04,980
也就是说呢

5
00:00:04,980 --> 00:00:06,680
我们在客户端查询数据的时候

6
00:00:06,680 --> 00:00:07,400
可以指定条件

7
00:00:07,400 --> 00:00:08,220
这样的话呢

8
00:00:08,220 --> 00:00:09,600
我们就可以更加灵活的

9
00:00:09,600 --> 00:00:10,900
来处理这个数据的查询操作

10
00:00:10,900 --> 00:00:11,800
并且的话

11
00:00:11,800 --> 00:00:13,160
这里的参数可以有零个

12
00:00:13,160 --> 00:00:14,800
或者多个都是可以的

13
00:00:14,800 --> 00:00:15,880
那这个参数的助理

14
00:00:15,880 --> 00:00:16,820
需要注意几个地方

15
00:00:16,820 --> 00:00:17,500
首先呢

16
00:00:17,500 --> 00:00:18,220
在定义类型的时候

17
00:00:18,220 --> 00:00:19,600
可以在这个查询的

18
00:00:19,600 --> 00:00:21,020
这个字段上面加上

19
00:00:21,020 --> 00:00:21,860
这个参数的定义

20
00:00:21,860 --> 00:00:23,840
然后客户端在查询的时候呢

21
00:00:23,840 --> 00:00:24,580
可以通过这种形式

22
00:00:24,580 --> 00:00:25,920
来具体的传递数据

23
00:00:25,920 --> 00:00:28,080
那这个服务端怎么去得到这个数据呢

24
00:00:28,080 --> 00:00:29,780
实际上是在resolver当中

25
00:00:29,780 --> 00:00:31,120
它的这个函数的第二个参数

26
00:00:31,120 --> 00:00:33,240
可以得到你客户端传过来的具体的数据

27
00:00:33,240 --> 00:00:34,820
那接下来我们就演示一下

28
00:00:34,820 --> 00:00:36,080
这个参数的传递和获取

29
00:00:36,080 --> 00:00:37,140
具体呢我们这样来做

30
00:00:37,140 --> 00:00:39,540
还是我们准备一个新的文件

31
00:00:39,540 --> 00:00:40,340
在这个技术之上呢

32
00:00:40,340 --> 00:00:41,980
我们来测试这个参数的传递

33
00:00:41,980 --> 00:00:43,880
那首先我们在这定义一个新的类型

34
00:00:43,880 --> 00:00:44,680
还是以学生为例

35
00:00:44,680 --> 00:00:46,940
这呢是学生类型

36
00:00:46,940 --> 00:00:49,040
这通过type

37
00:00:49,040 --> 00:00:50,400
再定义

38
00:00:50,400 --> 00:00:53,280
这呢我们同样的比较一个sname

39
00:00:53,280 --> 00:00:55,020
表示这个学生的性命

40
00:00:55,020 --> 00:00:57,280
然后的话我们准备一个年龄

41
00:00:57,280 --> 00:00:58,660
我们用int

42
00:00:58,660 --> 00:01:00,580
这是内置类型

43
00:01:00,580 --> 00:01:02,620
好 这个类型准备好之后

44
00:01:02,620 --> 00:01:05,380
我们需要提供这个书记的解析

45
00:01:05,380 --> 00:01:07,100
同样的我们在解析之前

46
00:01:07,100 --> 00:01:08,780
还要在这个查询的这个类型当中

47
00:01:08,780 --> 00:01:09,940
来指定一个名字

48
00:01:09,940 --> 00:01:11,140
要查询的是学生

49
00:01:11,140 --> 00:01:13,240
这个解析的话

50
00:01:13,240 --> 00:01:14,880
我们在这个query当中来处理

51
00:01:14,880 --> 00:01:16,020
stu

52
00:01:16,020 --> 00:01:17,280
然后这是一个函数

53
00:01:17,280 --> 00:01:20,060
在这儿我们要返回一点数据

54
00:01:20,060 --> 00:01:20,740
repen

55
00:01:20,740 --> 00:01:22,420
有这个sname

56
00:01:22,420 --> 00:01:24,560
这我们就指定一个b4

57
00:01:24,560 --> 00:01:26,840
然后年龄是12岁

58
00:01:26,840 --> 00:01:29,840
然后这个数据怎么获取

59
00:01:29,840 --> 00:01:32,000
首先要在这个查询的类型当中

60
00:01:32,000 --> 00:01:33,740
指定一个参数名称

61
00:01:33,740 --> 00:01:34,760
这个是自定义的

62
00:01:34,760 --> 00:01:35,960
它的类型的话也可以约束

63
00:01:35,960 --> 00:01:37,180
比如说我们用inter类型

64
00:01:37,180 --> 00:01:39,540
然后这个n从哪得到呢

65
00:01:39,540 --> 00:01:40,840
在这个标准的这个函数的

66
00:01:40,840 --> 00:01:41,640
第二个参数中得到

67
00:01:41,640 --> 00:01:43,280
第一个参数就随便写一个名字就可以了

68
00:01:43,280 --> 00:01:44,820
然后第二个参数我们用ax

69
00:01:44,820 --> 00:01:46,760
在这我们就先把它打印一下

70
00:01:46,760 --> 00:01:48,560
我们这样来做

71
00:01:48,560 --> 00:01:50,420
打印出这个结果

72
00:01:50,420 --> 00:01:51,260
如果能够得到

73
00:01:51,260 --> 00:01:52,340
这个客户端传过来的数据

74
00:01:52,340 --> 00:01:54,340
这就证明我们这个流程是走通了

75
00:01:54,340 --> 00:01:56,340
然后我们启动服务

76
00:01:56,340 --> 00:02:00,340
在这个位置我们去执行03

77
00:02:00,340 --> 00:02:02,340
好 启动成功之后我们来做测试

78
00:02:02,340 --> 00:02:05,340
在测试的时候我们再新开一个页面

79
00:02:05,340 --> 00:02:07,340
在这我们要查询的是STO

80
00:02:07,340 --> 00:02:09,340
TAR当中的什么呢

81
00:02:09,340 --> 00:02:11,340
SNEAM我们先查一下这里边有没有值

82
00:02:11,340 --> 00:02:13,340
查询 这里边有值 理次

83
00:02:13,340 --> 00:02:16,340
然后的话还有名领 查询 没问题

84
00:02:16,340 --> 00:02:18,340
然后这里边我们可以谈一点参数

85
00:02:18,340 --> 00:02:19,340
现在没有传参 你看服务端

86
00:02:19,340 --> 00:02:21,340
这里边打印出两个空对象

87
00:02:21,340 --> 00:02:23,480
如果说我在这个位置传递参数了

88
00:02:23,480 --> 00:02:24,940
传一个n它的值是12

89
00:02:24,940 --> 00:02:26,260
然后外景查询

90
00:02:26,260 --> 00:02:28,480
然后再看服务端这里边就值了

91
00:02:28,480 --> 00:02:29,300
n的值是12

92
00:02:29,300 --> 00:02:31,680
所以说我们在服务端可以根据这个条件

93
00:02:31,680 --> 00:02:34,580
来区分返回来不同的数据

94
00:02:34,580 --> 00:02:35,580
那具体我们可以这样来做

95
00:02:35,580 --> 00:02:39,160
就是如果这个args当中的

96
00:02:39,160 --> 00:02:41,040
如果args当中的什么呀

97
00:02:41,040 --> 00:02:43,600
这个一个属性名叫n

98
00:02:43,600 --> 00:02:45,860
它的值如果大于10

99
00:02:45,860 --> 00:02:49,060
我们返回这个这份数据

100
00:02:50,360 --> 00:02:51,900
否则的话我们返回另一份数据

101
00:02:51,900 --> 00:02:53,020
这样就有逻辑处理的

102
00:02:53,020 --> 00:02:55,420
这个位置改成8

103
00:02:55,420 --> 00:02:57,180
好 这样就可以区分出来

104
00:02:57,180 --> 00:02:58,260
然后我们再重启

105
00:02:58,260 --> 00:03:01,240
好 重启之后我们再来查询

106
00:03:01,240 --> 00:03:02,340
现在是12

107
00:03:02,340 --> 00:03:03,740
然后点查询

108
00:03:03,740 --> 00:03:04,480
看幅端

109
00:03:04,480 --> 00:03:06,580
这里边我们现在没有打印

110
00:03:06,580 --> 00:03:08,420
但是这里边查出来的是12

111
00:03:08,420 --> 00:03:10,720
如果说你把这个值改一下

112
00:03:10,720 --> 00:03:12,180
改成5

113
00:03:12,180 --> 00:03:13,520
然后再点查询

114
00:03:13,520 --> 00:03:15,540
你会发现返回来的是年龄变成了8

115
00:03:15,540 --> 00:03:17,720
这样的话数据就有区分了

116
00:03:17,720 --> 00:03:19,180
好 这是关于参数的传递

117
00:03:19,180 --> 00:03:20,940
除此之外

118
00:03:20,940 --> 00:03:21,960
其实还有一个地节需要注意

119
00:03:21,960 --> 00:03:23,000
就是这参数的默认值

120
00:03:23,000 --> 00:03:24,800
可以在后面默认加一个值

121
00:03:24,800 --> 00:03:26,980
如果我们这个默认值给它添加的话

122
00:03:26,980 --> 00:03:27,940
我们看一下有什么区别

123
00:03:27,940 --> 00:03:29,460
这里边默认给它一个值11

124
00:03:29,460 --> 00:03:30,920
然后保存

125
00:03:30,920 --> 00:03:32,780
然后我们再重启

126
00:03:32,780 --> 00:03:34,380
重启之后

127
00:03:34,380 --> 00:03:35,220
我们再次查询

128
00:03:35,220 --> 00:03:36,200
查询

129
00:03:36,200 --> 00:03:36,980
现在没有变化

130
00:03:36,980 --> 00:03:38,340
如果说我这个位置

131
00:03:38,340 --> 00:03:39,120
把这个参数去掉

132
00:03:39,120 --> 00:03:39,860
我点查询

133
00:03:39,860 --> 00:03:41,140
实际上它查出来的是谁

134
00:03:41,140 --> 00:03:43,580
是参数是11的情况

135
00:03:43,580 --> 00:03:44,940
那是因为我们后边

136
00:03:44,940 --> 00:03:46,000
给了一个默认值

137
00:03:46,000 --> 00:03:46,480
就是11

138
00:03:46,480 --> 00:03:47,560
为了证明这一点

139
00:03:47,560 --> 00:03:48,400
其实你可以把这个放开

140
00:03:48,400 --> 00:03:49,300
把它打印出来

141
00:03:49,300 --> 00:03:50,440
然后我们再试一试

142
00:03:50,440 --> 00:03:52,240
然后我们再检查询

143
00:03:52,240 --> 00:03:53,040
看符端

144
00:03:53,040 --> 00:03:54,060
再打印出来的是11

145
00:03:54,060 --> 00:03:56,440
所以说这个默认值就发挥作用了

146
00:03:56,440 --> 00:03:58,300
那如果说你这里边显示的传入了一个值

147
00:03:58,300 --> 00:03:59,620
比如说传入的是15

148
00:03:59,620 --> 00:04:00,920
然后再检查询

149
00:04:00,920 --> 00:04:02,460
这反过来的这个年龄是12

150
00:04:02,460 --> 00:04:03,280
因此在这个位置

151
00:04:03,280 --> 00:04:04,600
打印出来的是N15

152
00:04:04,600 --> 00:04:06,020
就得到了你传记的这个参数

153
00:04:06,020 --> 00:04:08,220
所以说这个默认值的这个作用

154
00:04:08,220 --> 00:04:09,000
就是你不传参的时候

155
00:04:09,000 --> 00:04:09,400
以它为准

156
00:04:09,400 --> 00:04:10,520
传记的话就把它覆盖掉

157
00:04:10,520 --> 00:04:12,300
这是关于这个默认值

158
00:04:12,300 --> 00:04:14,040
那这个位置我们注意一下

159
00:04:14,040 --> 00:04:14,740
在这也可以备注

160
00:04:14,740 --> 00:04:15,980
里边还可以加警号

161
00:04:15,980 --> 00:04:18,240
这里边的参数可以指定默认值

162
00:04:18,240 --> 00:04:21,080
参数可以指定默认值

163
00:04:21,080 --> 00:04:22,220
默认值的话

164
00:04:22,220 --> 00:04:24,120
就是不传参策的时候发挥作用

165
00:04:24,120 --> 00:04:25,220
传参的话就把它覆盖掉

166
00:04:25,220 --> 00:04:26,500
这是一个细节

167
00:04:26,500 --> 00:04:28,920
除此之外的话

168
00:04:28,920 --> 00:04:29,880
如果你有多个参数的话

169
00:04:29,880 --> 00:04:30,780
后边可以接着往后加

170
00:04:30,780 --> 00:04:31,760
就是通过逗号的方式

171
00:04:31,760 --> 00:04:32,680
再加更多的参数

172
00:04:32,680 --> 00:04:34,000
这格式是类似的

173
00:04:34,000 --> 00:04:34,940
这个我们就不再演示了

174
00:04:34,940 --> 00:04:37,160
这是关于参数的处理

175
00:04:37,160 --> 00:04:37,840
需要注意的地方

176
00:04:37,840 --> 00:04:39,540
最后需要注意的细节

177
00:04:39,540 --> 00:04:40,560
在这有说明

178
00:04:40,560 --> 00:04:42,160
就是默认值

179
00:04:42,160 --> 00:04:44,620
它的作用要清楚

180
00:04:44,620 --> 00:04:45,460
是可选的

181
00:04:45,460 --> 00:04:47,140
有模仁值之后这个参数就是可选的了

182
00:04:47,140 --> 00:04:48,100
那另外一点的话

183
00:04:48,100 --> 00:04:49,280
就是参数的获取方式

184
00:04:49,280 --> 00:04:50,700
就是通过Resolver函数的第二个参数

185
00:04:50,700 --> 00:04:52,340
获取到我们传过来的参数

186
00:04:52,340 --> 00:04:55,460
好 这个专业参数的基本使用

187
00:04:55,460 --> 00:04:56,140
我们就说到这里

