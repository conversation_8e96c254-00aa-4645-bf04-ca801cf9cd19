1
00:00:00,260 --> 00:00:02,560
好我们刚才是不是已经讲解了红任务与微任务

2
00:00:02,560 --> 00:00:04,860
那么这节课老师会通过两道题目

3
00:00:04,860 --> 00:00:07,420
去加深同学们对红任务与微任务的一个理解

4
00:00:07,420 --> 00:00:08,200
我们为什么

5
00:00:08,200 --> 00:00:08,960
要

6
00:00:08,960 --> 00:00:10,760
去设计这两道题目呢

7
00:00:10,760 --> 00:00:12,800
因为红任务与微任务在面试中

8
00:00:12,800 --> 00:00:14,600
经常会有面试官喜欢问

9
00:00:14,600 --> 00:00:15,880
他们都很变态

10
00:00:15,880 --> 00:00:17,160
特别喜欢问这样的问题

11
00:00:17,160 --> 00:00:18,440
所以呢我们一定要

12
00:00:18,440 --> 00:00:19,200
学好

13
00:00:19,200 --> 00:00:20,740
首先我们来看第一题

14
00:00:20,740 --> 00:00:25,080
第一题

15
00:00:25,080 --> 00:00:27,900
我们来只是在刚才的例子里面

16
00:00:28,160 --> 00:00:30,620
Promise.Result里面又去添加了一个Promise.Result

17
00:00:30,620 --> 00:00:31,600
那么此时

18
00:00:31,600 --> 00:00:35,900
我们的Satomout和Promise2

19
00:00:35,900 --> 00:00:37,400
他们两个随先执行

20
00:00:37,400 --> 00:00:40,140
是不是他们的执行顺序就是咱们问题的关键

21
00:00:40,140 --> 00:00:42,200
同学们可以自己下去思考一下

22
00:00:42,200 --> 00:00:45,100
这里呢老师来带同学们来分析一下

23
00:00:45,100 --> 00:00:47,800
首先

24
00:00:47,800 --> 00:00:49,700
我们的队列是不是分为

25
00:00:49,700 --> 00:00:52,240
红任务

26
00:00:52,240 --> 00:00:53,300
还有

27
00:00:53,300 --> 00:00:56,160
V任务

28
00:00:58,160 --> 00:01:01,560
然后呢,还有IO

29
00:01:01,560 --> 00:01:08,320
首先,咱们的队列中有什么

30
00:01:08,320 --> 00:01:09,620
咱们的主线层

31
00:01:09,620 --> 00:01:13,000
是不是set timeout

32
00:01:13,000 --> 00:01:14,380
然后呢

33
00:01:14,380 --> 00:01:17,340
promise.resolve

34
00:01:17,340 --> 00:01:20,440
接下来log

35
00:01:20,440 --> 00:01:24,220
好,随先进入

36
00:01:24,220 --> 00:01:25,720
咱们的红人物是不是timeout

37
00:01:25,720 --> 00:01:27,540
然后呢log

38
00:01:27,540 --> 00:01:28,900
setTimeout

39
00:01:28,900 --> 00:01:31,500
setTimeout

40
00:01:31,500 --> 00:01:32,820
是不是会进入咱们的IO对吧

41
00:01:32,820 --> 00:01:33,780
好

42
00:01:33,780 --> 00:01:34,780
接下来呢

43
00:01:34,780 --> 00:01:35,400
红动物执行完

44
00:01:35,400 --> 00:01:35,880
出战

45
00:01:35,880 --> 00:01:36,560
他第一个执行完

46
00:01:36,560 --> 00:01:37,780
然后promise.resolve

47
00:01:37,780 --> 00:01:38,620
再入战

48
00:01:38,620 --> 00:01:39,940
然后呢

49
00:01:39,940 --> 00:01:42,120
咱们点证方法里面的整个callback

50
00:01:42,120 --> 00:01:44,260
也就是他

51
00:01:44,260 --> 00:01:46,800
是不是整个都会进入咱们的vroom对吧

52
00:01:46,800 --> 00:01:47,380
所以呢

53
00:01:47,380 --> 00:01:48,020
首先

54
00:01:48,020 --> 00:01:50,760
log

55
00:01:50,760 --> 00:01:53,640
promise

56
00:01:53,640 --> 00:01:56,080
会进入到咱们的v动物里面去

57
00:01:56,080 --> 00:01:56,780
然后呢

58
00:01:56,780 --> 00:01:58,760
promise.resolve.证

59
00:01:58,760 --> 00:02:01,000
里面是不是又是一个微软瓦同学们

60
00:02:01,000 --> 00:02:01,840
所以呢

61
00:02:01,840 --> 00:02:03,000
此时

62
00:02:03,000 --> 00:02:05,320
promise.resolve

63
00:02:05,320 --> 00:02:09,520
是不是还有一个logpromise2

64
00:02:09,520 --> 00:02:10,560
对吧

65
00:02:10,560 --> 00:02:12,120
log

66
00:02:12,120 --> 00:02:15,160
promise2

67
00:02:15,160 --> 00:02:16,400
是不是也会进入咱们的微特例

68
00:02:16,400 --> 00:02:17,020
对吧

69
00:02:17,020 --> 00:02:17,380
好

70
00:02:17,380 --> 00:02:20,280
此时咱们再来继续执行咱们的主线程

71
00:02:20,280 --> 00:02:24,520
promise.resolve先出账

72
00:02:24,520 --> 00:02:26,080
log命入账

73
00:02:26,080 --> 00:02:27,260
执行完出炸

74
00:02:27,260 --> 00:02:28,520
然后接下来

75
00:02:28,520 --> 00:02:30,480
是不是在咱们执行微任务对列

76
00:02:30,480 --> 00:02:31,340
首先

77
00:02:31,340 --> 00:02:33,220
Promise

78
00:02:33,220 --> 00:02:34,140
执行完

79
00:02:34,140 --> 00:02:35,000
Promise 2

80
00:02:35,000 --> 00:02:36,000
执行完

81
00:02:36,000 --> 00:02:37,020
然后呢

82
00:02:37,020 --> 00:02:40,220
咱们在下一轮新的世界循环里面

83
00:02:40,220 --> 00:02:41,060
红任务

84
00:02:41,060 --> 00:02:45,900
咱们的settimeout0

85
00:02:45,900 --> 00:02:47,880
它实际上它的意义是什么呀

86
00:02:47,880 --> 00:02:48,560
settimeout0

87
00:02:48,560 --> 00:02:52,280
它就是在下一轮新的世界循环里面的红任务里面去执行

88
00:02:52,280 --> 00:02:54,160
也就是咱们新一轮的红任务

89
00:02:55,520 --> 00:02:58,080
可能同学们这里稍微会有一点疑问

90
00:02:58,080 --> 00:03:01,160
我们的promise.resolve.证

91
00:03:01,160 --> 00:03:03,960
第1个回调里面的

92
00:03:03,960 --> 00:03:06,280
V任务为什么和他嵌套里面的

93
00:03:06,280 --> 00:03:07,560
promise.证里面的V任务

94
00:03:07,560 --> 00:03:09,600
在同一个V任务对面里面

95
00:03:09,600 --> 00:03:10,360
其实这里呢

96
00:03:10,360 --> 00:03:11,900
涉及了浏览器里面他对

97
00:03:11,900 --> 00:03:13,180
事件循环他一个解释

98
00:03:13,180 --> 00:03:14,200
实际上老师刚才

99
00:03:14,200 --> 00:03:15,740
在讲义里面已经写到了

100
00:03:15,740 --> 00:03:17,280
咱们可以得到一个结论是什么

101
00:03:17,280 --> 00:03:19,840
一轮

102
00:03:19,840 --> 00:03:22,140
事件循环

103
00:03:22,140 --> 00:03:23,680
会

104
00:03:23,680 --> 00:03:25,480
执行一次

105
00:03:25,520 --> 00:03:27,460
红任务

106
00:03:27,460 --> 00:03:34,980
怎么红字红任务

107
00:03:34,980 --> 00:03:36,240
以及

108
00:03:36,240 --> 00:03:40,340
所有的

109
00:03:40,340 --> 00:03:43,480
这里呢是浏览器

110
00:03:43,480 --> 00:03:45,480
它那个标准里面的去定义

111
00:03:45,480 --> 00:03:46,380
怎么去理解呢

112
00:03:46,380 --> 00:03:47,380
就是说咱们

113
00:03:47,380 --> 00:03:48,780
settimeout

114
00:03:48,780 --> 00:03:50,740
里面再去嵌套settimeout

115
00:03:50,740 --> 00:03:51,560
它会产生一个

116
00:03:51,560 --> 00:03:53,040
在下一轮时间循环里面去执行

117
00:03:53,040 --> 00:03:53,500
但是

118
00:03:53,500 --> 00:03:55,280
咱们promise and resolve里面

119
00:03:55,280 --> 00:03:55,880
它的微任务

120
00:03:55,880 --> 00:03:58,240
假如说咱们的promised的resolve和promised的resolve

121
00:03:58,240 --> 00:04:00,020
它嵌套很多次

122
00:04:00,020 --> 00:04:01,820
比如说嵌套一次两次

123
00:04:01,820 --> 00:04:02,940
你嵌套无数次

124
00:04:02,940 --> 00:04:03,740
那么我们

125
00:04:03,740 --> 00:04:06,460
它都会在一次世界循环里面去执行

126
00:04:06,460 --> 00:04:08,060
也就是刚才老师所解释的

127
00:04:08,060 --> 00:04:09,640
一轮世界循环会执行一次红任务

128
00:04:09,640 --> 00:04:10,740
以及所有的微任务

129
00:04:10,740 --> 00:04:11,640
那么咱们来做一个实验

130
00:04:11,640 --> 00:04:12,120
来看一下

131
00:04:12,120 --> 00:04:13,160
到底是不是这样的

132
00:04:13,160 --> 00:04:17,200
比如说我们在promised的resolve.证里面

133
00:04:17,200 --> 00:04:20,820
再去执行一个promised.resolve

134
00:04:20,820 --> 00:04:24,240
点证

135
00:04:24,240 --> 00:04:27,360
council.nog

136
00:04:27,360 --> 00:04:30,000
promise3

137
00:04:30,000 --> 00:04:32,280
大家应该已经能够猜到结果

138
00:04:32,280 --> 00:04:33,120
他的结果是main

139
00:04:33,120 --> 00:04:34,280
promise123

140
00:04:34,280 --> 00:04:35,460
然后猜是sell them out

141
00:04:35,460 --> 00:04:36,180
不相信

142
00:04:36,180 --> 00:04:37,340
咱们可以跑一下看一下

143
00:04:37,340 --> 00:04:41,180
好

144
00:04:41,180 --> 00:04:44,640
咱们刚才开的地址不对

145
00:04:44,640 --> 00:04:45,240
老实换一下

146
00:04:45,240 --> 00:04:52,100
好像还是没执行

147
00:04:52,100 --> 00:04:55,180
为什么

148
00:04:55,180 --> 00:04:56,200
好老师看一下

149
00:04:56,200 --> 00:04:56,900
不要着急

150
00:04:56,900 --> 00:04:59,020
好

151
00:04:59,020 --> 00:05:00,640
看了是不是看到咱们的结果

152
00:05:00,640 --> 00:05:02,140
main promise promise 23

153
00:05:02,140 --> 00:05:04,320
所以说咱们

154
00:05:04,320 --> 00:05:05,880
在微任务对面里面

155
00:05:05,880 --> 00:05:07,020
一定会让他去执行完

156
00:05:07,020 --> 00:05:08,200
所有的微任务再去执行

157
00:05:08,200 --> 00:05:09,060
下一轮新的事件循环

158
00:05:09,060 --> 00:05:13,020
所以咱们通过第一道面试题

159
00:05:13,020 --> 00:05:13,720
可以得出一个结论

160
00:05:13,720 --> 00:05:14,840
每轮事件循环

161
00:05:14,840 --> 00:05:17,620
直信一个红任务和所有的微任务

162
00:05:17,620 --> 00:05:18,620
好那么咱们来看一下

163
00:05:18,620 --> 00:05:19,300
下一题

164
00:05:22,100 --> 00:05:26,400
那么这里呢老师在settimeout里面去嵌套了一个promise.resolve

165
00:05:26,400 --> 00:05:29,100
然后呢在promise.resolve里面去嵌套了一个settimeout

166
00:05:29,100 --> 00:05:30,100
是不是很变态呀

167
00:05:30,100 --> 00:05:32,100
同学们是不是非常变态

168
00:05:32,100 --> 00:05:34,100
其实一些大企业的面试官就喜欢问这样的问题

169
00:05:34,100 --> 00:05:36,100
所以同学们一定要经常的虐自己

170
00:05:36,100 --> 00:05:38,100
出去之后才不会被别人虐

171
00:05:38,100 --> 00:05:39,100
好

172
00:05:39,100 --> 00:05:40,100
同学们思考一下

173
00:05:40,100 --> 00:05:42,100
那我们就来看一下这一题怎么样去分析

174
00:05:42,100 --> 00:05:45,100
首先

175
00:05:45,100 --> 00:05:46,100
老规矩

176
00:05:46,100 --> 00:05:48,100
什么呀三件套对吧

177
00:05:48,100 --> 00:05:49,100
好

178
00:05:49,100 --> 00:05:51,100
红任务

179
00:05:51,100 --> 00:05:54,240
围终

180
00:05:54,240 --> 00:05:56,740
io

181
00:05:56,740 --> 00:05:58,580
咱们的主线程

182
00:05:58,580 --> 00:06:02,080
是不是有settimeout

183
00:06:02,080 --> 00:06:05,860
promise.ray

184
00:06:05,860 --> 00:06:07,140
solve

185
00:06:07,140 --> 00:06:09,480
然后呢log命

186
00:06:09,480 --> 00:06:15,100
首先settimeout

187
00:06:15,100 --> 00:06:16,000
是不是进入咱们的红队列

188
00:06:16,000 --> 00:06:16,980
好

189
00:06:16,980 --> 00:06:18,320
执行完成之后

190
00:06:18,320 --> 00:06:19,580
是不是会

191
00:06:19,580 --> 00:06:21,860
把咱们的promise.resolve.证

192
00:06:21,860 --> 00:06:23,200
它这一整块

193
00:06:23,200 --> 00:06:25,040
会加入到咱们的IO里面去

194
00:06:25,040 --> 00:06:25,420
对吧

195
00:06:25,420 --> 00:06:26,060
那么这里呢

196
00:06:26,060 --> 00:06:26,900
老师给它取个名字

197
00:06:26,900 --> 00:06:28,220
方便咱们的理解

198
00:06:28,220 --> 00:06:28,760
叫做IOE

199
00:06:28,760 --> 00:06:30,840
所以呢

200
00:06:30,840 --> 00:06:31,900
这里会把IOE

201
00:06:31,900 --> 00:06:33,000
丢到咱们的IO里面去

202
00:06:33,000 --> 00:06:33,440
IOE

203
00:06:33,440 --> 00:06:35,320
为什么老师取个名字呢

204
00:06:35,320 --> 00:06:36,000
因为它是个代码块

205
00:06:36,000 --> 00:06:37,240
这里的话图不是很好画

206
00:06:37,240 --> 00:06:39,000
所以咱们用IOE去表示它

207
00:06:39,000 --> 00:06:41,060
同学们只要跟着老师师傅走就可以了

208
00:06:41,060 --> 00:06:41,260
好

209
00:06:41,260 --> 00:06:42,600
那么咱们promise.resolve

210
00:06:42,600 --> 00:06:43,360
接下来

211
00:06:43,360 --> 00:06:44,540
咱们设置的tomout

212
00:06:44,540 --> 00:06:46,500
是不是先出战了

213
00:06:46,500 --> 00:06:47,460
它的中文完成了

214
00:06:47,460 --> 00:06:47,640
好

215
00:06:47,640 --> 00:06:48,760
接下来promise.resolve

216
00:06:48,760 --> 00:06:50,440
进入了咱们的红队链

217
00:06:50,440 --> 00:06:51,240
执行的过程中

218
00:06:51,240 --> 00:06:53,260
是不是会把这一块

219
00:06:53,260 --> 00:06:55,120
这一整块

220
00:06:55,120 --> 00:06:56,760
是不是会加入到咱们的

221
00:06:56,760 --> 00:06:57,640
微任务对面里面去

222
00:06:57,640 --> 00:06:58,140
同学们

223
00:06:58,140 --> 00:06:58,980
好 咱们先把它加入

224
00:06:58,980 --> 00:07:00,040
这里呢

225
00:07:00,040 --> 00:07:01,680
许个名字

226
00:07:01,680 --> 00:07:02,540
叫做

227
00:07:02,540 --> 00:07:03,920
叫做微吧

228
00:07:03,920 --> 00:07:04,420
微一

229
00:07:04,420 --> 00:07:04,980
好

230
00:07:04,980 --> 00:07:06,980
那咱们是不是会把整个

231
00:07:06,980 --> 00:07:09,660
微一加入到微任务里面去

232
00:07:09,660 --> 00:07:09,840
好

233
00:07:09,840 --> 00:07:10,680
那么Promission Resolve

234
00:07:10,680 --> 00:07:11,700
他的任务完成了

235
00:07:11,700 --> 00:07:12,420
出战

236
00:07:12,420 --> 00:07:13,480
此时呢

237
00:07:13,480 --> 00:07:14,140
洛克命入战

238
00:07:14,140 --> 00:07:15,020
再出战

239
00:07:15,020 --> 00:07:15,900
好

240
00:07:15,900 --> 00:07:17,400
那么此时我们的

241
00:07:17,400 --> 00:07:18,860
红任务已经执行完了

242
00:07:18,860 --> 00:07:19,140
对吧

243
00:07:19,140 --> 00:07:20,680
接下来开始进入咱们的微动务

244
00:07:20,680 --> 00:07:21,460
微动是什么

245
00:07:21,460 --> 00:07:22,820
微就是执行这样一个方法

246
00:07:22,820 --> 00:07:23,500
SetTimeout0

247
00:07:23,500 --> 00:07:24,600
Council.log

248
00:07:24,600 --> 00:07:25,700
SetTimeout

249
00:07:25,700 --> 00:07:27,280
此时是不是会把咱们的

250
00:07:27,280 --> 00:07:28,620
把咱们的

251
00:07:28,620 --> 00:07:29,120
什么呀

252
00:07:29,120 --> 00:07:30,000
是不是log

253
00:07:30,000 --> 00:07:31,740
SetTimeout会加入咱们的IO里面去

254
00:07:31,740 --> 00:07:32,580
对吧

255
00:07:32,580 --> 00:07:32,800
好

256
00:07:32,800 --> 00:07:35,280
log

257
00:07:35,280 --> 00:07:38,700
SetTimeout

258
00:07:38,700 --> 00:07:39,160
加入IO

259
00:07:39,160 --> 00:07:39,720
好

260
00:07:39,720 --> 00:07:40,420
那么呢

261
00:07:40,420 --> 00:07:41,560
微动务执行完

262
00:07:41,560 --> 00:07:42,300
出战

263
00:07:42,300 --> 00:07:43,280
那么

264
00:07:43,280 --> 00:07:45,280
微动务出战

265
00:07:45,280 --> 00:07:45,760
也就是这块

266
00:07:45,760 --> 00:07:46,160
是不是出战

267
00:07:46,160 --> 00:07:47,060
这个方法

268
00:07:47,060 --> 00:07:48,160
Sitemount整个这样一个方法

269
00:07:48,160 --> 00:07:48,760
执行完出场

270
00:07:48,760 --> 00:07:48,980
好

271
00:07:48,980 --> 00:07:51,320
那么此时我们这一轮时间循环是不是已经结束了

272
00:07:51,320 --> 00:07:52,600
红任务微任务执行完了

273
00:07:52,600 --> 00:07:53,800
还有没有微任务没有了吧

274
00:07:53,800 --> 00:07:55,280
那么咱们这一轮时间循环已经执行完了

275
00:07:55,280 --> 00:07:56,120
是不是接下来

276
00:07:56,120 --> 00:07:57,580
是不是开始进入下一轮时间循环了

277
00:07:57,580 --> 00:07:57,960
同学们

278
00:07:57,960 --> 00:07:58,880
好

279
00:07:58,880 --> 00:07:59,940
下一轮时间循环

280
00:07:59,940 --> 00:08:01,220
也就是说呢

281
00:08:01,220 --> 00:08:02,160
它会有一个新的红任务

282
00:08:02,160 --> 00:08:02,480
对吧

283
00:08:02,480 --> 00:08:03,960
新的红任务

284
00:08:03,960 --> 00:08:08,280
好

285
00:08:08,280 --> 00:08:09,980
老师来写一下红

286
00:08:09,980 --> 00:08:10,900
好

287
00:08:10,900 --> 00:08:12,440
简写一下红

288
00:08:12,440 --> 00:08:12,980
喂

289
00:08:12,980 --> 00:08:14,020
好

290
00:08:14,020 --> 00:08:15,680
那么这一轮红任务会有什么任务加进来的

291
00:08:15,680 --> 00:08:16,300
是不是咱们的

292
00:08:16,300 --> 00:08:18,540
IoE啊

293
00:08:18,540 --> 00:08:18,920
同学们

294
00:08:18,920 --> 00:08:20,300
是不是IoE

295
00:08:20,300 --> 00:08:21,920
为什么呀

296
00:08:21,920 --> 00:08:23,580
因为我们的任务对立

297
00:08:23,580 --> 00:08:24,480
它讲究一个什么原则

298
00:08:24,480 --> 00:08:25,420
先进先出

299
00:08:25,420 --> 00:08:26,620
那么咱们IoE先进去了

300
00:08:26,620 --> 00:08:27,320
它是不是先出来

301
00:08:27,320 --> 00:08:28,800
所以说只是这种红对立

302
00:08:28,800 --> 00:08:30,120
红对立是IoE

303
00:08:30,120 --> 00:08:32,140
那么咱们来看一下IoE是什么

304
00:08:32,140 --> 00:08:33,640
Promise.resolve.证

305
00:08:33,640 --> 00:08:35,880
它是不是又会产生一个V任务啊

306
00:08:35,880 --> 00:08:36,840
叫做LogPromise

307
00:08:36,840 --> 00:08:40,520
Vlog

308
00:08:40,520 --> 00:08:44,340
Promise

309
00:08:44,340 --> 00:08:46,440
好

310
00:08:46,440 --> 00:08:48,200
那么咱们IO1是不是执行完了

311
00:08:48,200 --> 00:08:49,140
出战

312
00:08:49,140 --> 00:08:49,960
接下来

313
00:08:49,960 --> 00:08:50,980
红任务执行完了

314
00:08:50,980 --> 00:08:51,580
是不是执行微任务

315
00:08:51,580 --> 00:08:52,480
log promise

316
00:08:52,480 --> 00:08:53,260
出战

317
00:08:53,260 --> 00:08:55,700
然后IO里面是不是还有一个set timeout

318
00:08:55,700 --> 00:08:56,180
对吧

319
00:08:56,180 --> 00:08:56,680
他呢

320
00:08:56,680 --> 00:08:59,180
会在下一轮世界循环里面去执行

321
00:08:59,180 --> 00:09:00,000
他呢

322
00:09:00,000 --> 00:09:00,480
加入了咱们了

323
00:09:00,480 --> 00:09:01,040
红任务

324
00:09:01,040 --> 00:09:03,200
红

325
00:09:03,200 --> 00:09:05,280
好

326
00:09:05,280 --> 00:09:05,840
执行完

327
00:09:05,840 --> 00:09:06,320
出战

328
00:09:06,320 --> 00:09:07,660
接下来是不是没有微任务了

329
00:09:07,660 --> 00:09:09,680
那么咱们整个执行过程就已经结束了

330
00:09:09,680 --> 00:09:11,400
执行顺序是什么

331
00:09:11,400 --> 00:09:11,700
啊同学们

332
00:09:11,700 --> 00:09:12,140
log bin

333
00:09:12,140 --> 00:09:13,020
log promise

334
00:09:13,020 --> 00:09:14,020
log set timeout

335
00:09:14,020 --> 00:09:20,680
好保存一下我们来看一下咱们在浏览器中看一下是不是这样的老师有没有分析错

336
00:09:20,680 --> 00:09:25,020
好可能地址有点问题老师看一下

337
00:09:25,020 --> 00:09:34,760
好大家看到没有

338
00:09:34,760 --> 00:09:36,040
Main promise set them out

339
00:09:36,040 --> 00:09:37,820
和老师分析的是一样的对吧

340
00:09:37,820 --> 00:09:39,100
资金顺序是什么呢

341
00:09:39,100 --> 00:09:41,420
第一个

342
00:09:41,420 --> 00:09:42,940
Main

343
00:09:44,020 --> 00:09:47,920
Promise setTimeout

344
00:09:47,920 --> 00:09:50,680
好这里呢我们其实可以得出一个结论

345
00:09:50,680 --> 00:09:54,940
任务对立它一定会保持先进先出的顺序去执行

346
00:09:54,940 --> 00:09:57,980
也就是咱们通过这样一个例子去学习到的

347
00:09:57,980 --> 00:09:59,940
那么我们来回顾一下咱们刚才这两个面试题

348
00:09:59,940 --> 00:10:01,100
首先第一题

349
00:10:01,100 --> 00:10:03,000
我们是不是学习到了

350
00:10:03,000 --> 00:10:05,660
不管咱们的微容它执行多少次嵌到多少层

351
00:10:05,660 --> 00:10:08,360
它一定是在这一轮事件循环里面去执行完

352
00:10:08,360 --> 00:10:11,440
那么setTimeout这样的红任务

353
00:10:11,440 --> 00:10:13,580
它是不是一定会在下一轮事件循环的开始

354
00:10:13,580 --> 00:10:17,040
所以说咱们的PromissionResolve它永远在SetTimeout前面

355
00:10:17,040 --> 00:10:19,040
那么咱们第二道屏幕怎么样去分析呢

356
00:10:19,040 --> 00:10:20,680
我们其实可以根据

357
00:10:20,680 --> 00:10:22,920
咱们这两个SetTimeout0

358
00:10:22,920 --> 00:10:24,640
它们在Io里面随先进去了

359
00:10:24,640 --> 00:10:25,460
谁就先出来

360
00:10:25,460 --> 00:10:26,720
去分析

361
00:10:26,720 --> 00:10:28,180
以这样的一个突破口

362
00:10:28,180 --> 00:10:34,260
随先进入Io

363
00:10:34,260 --> 00:10:38,060
随先出来

364
00:10:38,060 --> 00:10:39,200
那咱们这里随先进去

365
00:10:39,200 --> 00:10:42,240
是不是我们的第一个SetTimeout

366
00:10:42,240 --> 00:10:43,060
它先进去

367
00:10:43,060 --> 00:10:45,100
然后呢在唯一动物里面是的timeout

368
00:10:45,100 --> 00:10:46,140
第2个他后进去

369
00:10:46,140 --> 00:10:47,420
所以说咱们他先出来

370
00:10:47,420 --> 00:10:50,740
咱们只要按照这样的思路一步一步去分析一定能得出结果

371
00:10:50,740 --> 00:10:51,760
好这里呢

372
00:10:51,760 --> 00:10:53,000
就是咱们这节课的内容

