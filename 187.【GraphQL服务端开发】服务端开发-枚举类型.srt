1
00:00:00,000 --> 00:00:01,960
好 接下来我们继续来看另外一种内置类型

2
00:00:01,960 --> 00:00:02,880
叫美举类型

3
00:00:02,880 --> 00:00:04,220
那什么是美举类型呢

4
00:00:04,220 --> 00:00:06,020
其实它也是一种标量

5
00:00:06,020 --> 00:00:07,100
只不过有一点特殊

6
00:00:07,100 --> 00:00:08,520
特殊在什么地方呢

7
00:00:08,520 --> 00:00:10,660
其实美举类型它表示的是

8
00:00:10,660 --> 00:00:12,740
一个可选的值的集合

9
00:00:12,740 --> 00:00:14,460
什么意思呢

10
00:00:14,460 --> 00:00:16,280
比如说你有一个爱好

11
00:00:16,280 --> 00:00:18,420
那你的爱好的话有可能是某几个

12
00:00:18,420 --> 00:00:20,140
所以说这种场景的话

13
00:00:20,140 --> 00:00:21,180
就可以用美举的方式来描述

14
00:00:21,180 --> 00:00:23,360
比如说你定义这个爱好美举类型的话

15
00:00:23,360 --> 00:00:24,520
可以这样定义

16
00:00:24,520 --> 00:00:26,540
它里边包含了三个爱好

17
00:00:26,540 --> 00:00:28,240
游泳 写代码 还有唱歌

18
00:00:28,240 --> 00:00:31,500
也就是说你在得到爱好类型数据的时候

19
00:00:31,500 --> 00:00:33,460
只能得到其中一个数据

20
00:00:33,460 --> 00:00:35,300
得到别的数据是不可以的

21
00:00:35,300 --> 00:00:37,100
这就是枚举类型的应用场景

22
00:00:37,100 --> 00:00:38,680
说到枚举类型的话

23
00:00:38,680 --> 00:00:41,300
其实我们学过第三期的同学应该有印象

24
00:00:41,300 --> 00:00:43,820
就在飞语言当中是有枚举类型的

25
00:00:43,820 --> 00:00:45,320
但是我们在前端的直言词中

26
00:00:45,320 --> 00:00:48,060
是没有枚举类型这个概念的

27
00:00:48,060 --> 00:00:49,240
但是在GraphK2当中

28
00:00:49,240 --> 00:00:50,940
其实我们从使用的角度不用关心这个

29
00:00:50,940 --> 00:00:53,600
因为它的底层其实可以模拟出枚举类型

30
00:00:53,600 --> 00:00:55,880
所以说接下来我们就通过一个例子

31
00:00:55,880 --> 00:00:57,760
来继续演示一下枚举类型的应用场景

32
00:00:57,760 --> 00:00:59,080
具体我们这样来做

33
00:00:59,080 --> 00:01:01,220
在这我们就定义一个美举类型

34
00:01:01,220 --> 00:01:03,760
定义的关键字叫ENUM

35
00:01:03,760 --> 00:01:05,840
后面就是美举类型的名称

36
00:01:05,840 --> 00:01:06,640
在这个情名

37
00:01:06,640 --> 00:01:07,200
比如说爱好

38
00:01:07,200 --> 00:01:07,940
就叫Favor

39
00:01:07,940 --> 00:01:09,740
爱好的话

40
00:01:09,740 --> 00:01:11,860
你可以自己定义几个特殊的作物事就可以了

41
00:01:11,860 --> 00:01:12,760
比如说你喜欢游泳

42
00:01:12,760 --> 00:01:14,040
随名

43
00:01:14,040 --> 00:01:15,760
你喜欢跳舞

44
00:01:15,760 --> 00:01:16,880
当行

45
00:01:16,880 --> 00:01:18,620
还有喜欢写代码

46
00:01:18,620 --> 00:01:19,740
coding

47
00:01:19,740 --> 00:01:22,140
这就是一个美举类型

48
00:01:22,140 --> 00:01:23,880
定义好之后怎么用呢

49
00:01:23,880 --> 00:01:24,640
我们具体这样做

50
00:01:24,640 --> 00:01:27,440
在这我们就加一个查询的字段

51
00:01:27,440 --> 00:01:29,560
后面跟的是这个favor

52
00:01:29,560 --> 00:01:32,920
然后这数据需要怎么提供呢

53
00:01:32,920 --> 00:01:33,860
这个就简单一些

54
00:01:33,860 --> 00:01:35,300
这里边来个infer

55
00:01:35,300 --> 00:01:37,480
后面需要提供值

56
00:01:37,480 --> 00:01:40,000
注意啊这个值是有规则的

57
00:01:40,000 --> 00:01:40,960
它只能返回什么呢

58
00:01:40,960 --> 00:01:43,420
只能返回这里边的其中一个值

59
00:01:43,420 --> 00:01:44,660
比如说你返回游泳

60
00:01:44,660 --> 00:01:46,460
这个是可以的

61
00:01:46,460 --> 00:01:47,680
你要返回一个唱歌

62
00:01:47,680 --> 00:01:48,140
经营

63
00:01:48,140 --> 00:01:48,700
这就不可以

64
00:01:48,700 --> 00:01:49,100
为什么呢

65
00:01:49,100 --> 00:01:49,600
因为这里边没有

66
00:01:49,600 --> 00:01:51,340
所以说这个美举类型就余数了

67
00:01:51,340 --> 00:01:52,860
你只能得到这里边其中一个值

68
00:01:52,860 --> 00:01:53,600
那我们就测试一下

69
00:01:53,600 --> 00:01:56,460
在这呢我们去运行一下

70
00:01:56,460 --> 00:02:00,400
然后我们在浏览器中做查询

71
00:02:00,400 --> 00:02:01,860
这里边要查询的就是inf

72
00:02:01,860 --> 00:02:03,820
然后点读印是inf

73
00:02:03,820 --> 00:02:06,820
然后我们点查询

74
00:02:06,820 --> 00:02:08,420
这里边就拿到了所谓名

75
00:02:08,420 --> 00:02:10,720
如果说你这里边改一个别的值

76
00:02:10,720 --> 00:02:11,620
随便改一个

77
00:02:11,620 --> 00:02:12,400
比如说就改成EDC

78
00:02:12,400 --> 00:02:14,740
这明显不在上面这个枚矩类型当中

79
00:02:14,740 --> 00:02:16,320
然后我们再运行

80
00:02:16,320 --> 00:02:17,760
然后你点查询的时候

81
00:02:17,760 --> 00:02:18,780
这就出错了

82
00:02:18,780 --> 00:02:19,280
它说什么呢

83
00:02:19,280 --> 00:02:20,040
它激发得到一个值

84
00:02:20,040 --> 00:02:20,540
什么值啊

85
00:02:20,540 --> 00:02:21,520
就是这个favor类型的值

86
00:02:21,520 --> 00:02:24,520
但是你给的值明显不是这个favor类型

87
00:02:24,520 --> 00:02:26,560
因为这个值不在它的范围内

88
00:02:26,560 --> 00:02:28,180
所以说这就禁止了

89
00:02:28,180 --> 00:02:30,700
你只能拿到其中的某几个值

90
00:02:30,700 --> 00:02:31,900
就是固定的这几个值

91
00:02:31,900 --> 00:02:34,640
这就是关于每一局类型的用法

92
00:02:34,640 --> 00:02:35,680
其实我们只要记住

93
00:02:35,680 --> 00:02:36,880
每一局类型就是用于

94
00:02:36,880 --> 00:02:38,020
规定好几个值

95
00:02:38,020 --> 00:02:39,020
你在脱去的时候

96
00:02:39,020 --> 00:02:40,960
只能从其中拿一个就可以了

97
00:02:40,960 --> 00:02:41,480
就这意思

98
00:02:41,480 --> 00:02:42,520
相对也比较简单一点

99
00:02:42,520 --> 00:02:45,940
最后我们在这有一个结论

100
00:02:45,940 --> 00:02:48,280
对这种类型的经历来说

101
00:02:48,280 --> 00:02:50,300
其实你只能拿到三种值之一

102
00:02:50,300 --> 00:02:52,140
出此之外别的值你是拿不到的

103
00:02:52,140 --> 00:02:53,400
这是我们这有一个例子

104
00:02:53,400 --> 00:02:53,980
好了

105
00:02:53,980 --> 00:02:54,700
关于这个媒体类型

106
00:02:54,700 --> 00:02:55,820
我们记住这个特点

107
00:02:55,820 --> 00:02:56,400
就可以了

