1
00:00:00,000 --> 00:00:03,940
前面我们主要实现了

2
00:00:03,940 --> 00:00:05,680
这个原版案例的服务端的诱乐地

3
00:00:05,680 --> 00:00:07,060
那接下来我们开始实现

4
00:00:07,060 --> 00:00:08,140
客户端的效果

5
00:00:08,140 --> 00:00:09,100
那对客户端开发来说

6
00:00:09,100 --> 00:00:10,720
我们之前主要通过调试工具的方式

7
00:00:10,720 --> 00:00:12,380
来测试GOLPR的接口交用

8
00:00:12,380 --> 00:00:13,700
那接下来我们就采用

9
00:00:13,700 --> 00:00:14,540
真实的场景

10
00:00:14,540 --> 00:00:15,900
来实现这个接口的对接

11
00:00:15,900 --> 00:00:17,800
这儿我们选择的技术站是VUE

12
00:00:17,800 --> 00:00:19,200
其实这个Apple Server

13
00:00:19,200 --> 00:00:21,160
它不仅仅提供了对服务端的知识

14
00:00:21,160 --> 00:00:22,600
也对这个客户端的开发

15
00:00:22,600 --> 00:00:24,340
也提供了广泛的知识

16
00:00:24,340 --> 00:00:25,340
我们首先看一下

17
00:00:25,340 --> 00:00:26,800
Apple Server的官网

18
00:00:26,800 --> 00:00:27,660
在这儿有一个Client

19
00:00:27,660 --> 00:00:30,060
这一个列表展示了

20
00:00:30,060 --> 00:00:30,820
它支持的客户端

21
00:00:30,820 --> 00:00:32,960
包括主流的前段框架

22
00:00:32,960 --> 00:00:34,580
像Vue React和安格拉

23
00:00:34,580 --> 00:00:35,580
它都是支持的

24
00:00:35,580 --> 00:00:37,400
它也支持APP的开发

25
00:00:37,400 --> 00:00:38,440
比如说XS和安卓

26
00:00:38,440 --> 00:00:40,000
所以说Opload Server

27
00:00:40,000 --> 00:00:40,760
是非常强大的

28
00:00:40,760 --> 00:00:42,800
在这我们选择的是Vue

29
00:00:42,800 --> 00:00:43,660
把它点开

30
00:00:43,660 --> 00:00:45,220
然后点这个链接

31
00:00:45,220 --> 00:00:46,680
进到它的官网

32
00:00:46,680 --> 00:00:47,840
然后点getstudied

33
00:00:47,840 --> 00:00:49,040
这是它的说明文档

34
00:00:49,040 --> 00:00:50,160
在这个位置

35
00:00:50,160 --> 00:00:51,380
有详细的说明

36
00:00:51,380 --> 00:00:53,200
就是如何基于Vue的环境

37
00:00:53,200 --> 00:00:54,440
来整合Opload Server

38
00:00:54,440 --> 00:00:55,460
从而来调用

39
00:00:55,460 --> 00:00:56,660
QR的后台的交口

40
00:00:56,660 --> 00:00:58,440
这是官方的文档

41
00:00:58,440 --> 00:01:01,460
那这里边我们就不详细的来看这个说明了

42
00:01:01,460 --> 00:01:02,800
我已经把这里边核心的步骤

43
00:01:02,800 --> 00:01:03,640
摘取出来

44
00:01:03,640 --> 00:01:05,300
整理成了这样的文档

45
00:01:05,300 --> 00:01:07,080
这样我们看起来会更加直观

46
00:01:07,080 --> 00:01:08,840
主要就包括这么六个步骤

47
00:01:08,840 --> 00:01:11,100
那接下来我们就具体的

48
00:01:11,100 --> 00:01:13,600
按照这个步骤来实现前段的功能

49
00:01:13,600 --> 00:01:15,120
那首先我们要做的就是产生一个

50
00:01:15,120 --> 00:01:16,640
这个客户端的项目

51
00:01:16,640 --> 00:01:17,280
那这个项目的话

52
00:01:17,280 --> 00:01:18,500
我们可以借助BUY的脚手架

53
00:01:18,500 --> 00:01:19,720
来产生一个项目

54
00:01:19,720 --> 00:01:21,460
然后我们来实现这个布局的效果

55
00:01:21,460 --> 00:01:22,500
就是留言板的一面布局

56
00:01:22,500 --> 00:01:23,640
这个前两步的话

57
00:01:23,640 --> 00:01:24,860
我已经帮大家做好了

58
00:01:24,860 --> 00:01:26,360
我们的重心来放到

59
00:01:26,360 --> 00:01:27,200
这个GrowthQR的

60
00:01:27,200 --> 00:01:27,980
这个接口的对接上

61
00:01:27,980 --> 00:01:29,080
所以说呢

62
00:01:29,080 --> 00:01:29,700
首先我们来看一下

63
00:01:29,700 --> 00:01:31,340
这我们准备好的这个项目

64
00:01:31,340 --> 00:01:33,700
这个叫MyDemoClient

65
00:01:33,700 --> 00:01:34,820
这个项目就是通过

66
00:01:34,820 --> 00:01:35,980
WE脚手架产生的

67
00:01:35,980 --> 00:01:37,060
这里边这个入口文件

68
00:01:37,060 --> 00:01:37,720
是没有点点子

69
00:01:37,720 --> 00:01:39,640
然后样式已经提供好了

70
00:01:39,640 --> 00:01:40,760
那这个模板的话呢

71
00:01:40,760 --> 00:01:41,880
就是这个文件

72
00:01:41,880 --> 00:01:43,020
这里边纯粹的

73
00:01:43,020 --> 00:01:43,780
都是静态的效果

74
00:01:43,780 --> 00:01:44,900
没有这个动态的数据

75
00:01:44,900 --> 00:01:46,160
那首先呢

76
00:01:46,160 --> 00:01:46,680
我们预览一下

77
00:01:46,680 --> 00:01:48,280
整体的这个页面效果

78
00:01:48,280 --> 00:01:48,760
在这呢

79
00:01:48,760 --> 00:01:49,680
我们打开一个终端

80
00:01:49,680 --> 00:01:51,240
通过NPM

81
00:01:51,240 --> 00:01:51,920
Run

82
00:01:51,920 --> 00:01:52,620
Serve

83
00:01:52,620 --> 00:01:53,140
推车

84
00:01:53,140 --> 00:01:54,500
我们看一下这个

85
00:01:54,500 --> 00:01:55,820
静态的留言板的效果

86
00:01:55,820 --> 00:01:56,740
长什么样子

87
00:01:56,740 --> 00:01:58,180
其实我们之前是见过的

88
00:01:58,180 --> 00:01:58,940
这儿我们先看一下

89
00:01:58,940 --> 00:01:59,960
这静态的效果

90
00:01:59,960 --> 00:02:00,740
这地址是它

91
00:02:00,740 --> 00:02:02,020
然后打开浏览器

92
00:02:02,020 --> 00:02:03,400
贴到这个位置

93
00:02:03,400 --> 00:02:03,980
回车

94
00:02:03,980 --> 00:02:05,540
这是静态的效果

95
00:02:05,540 --> 00:02:06,680
那接下来我们要做的

96
00:02:06,680 --> 00:02:07,520
就是把这个数据

97
00:02:07,520 --> 00:02:08,640
就成动态的

98
00:02:08,640 --> 00:02:10,180
我们通过WOWPR的接口

99
00:02:10,180 --> 00:02:11,020
来拿到数据

100
00:02:11,020 --> 00:02:12,900
那接下来我们要做的

101
00:02:12,900 --> 00:02:13,620
就是按照这六个步骤

102
00:02:13,620 --> 00:02:15,420
来实现这个接口的调用了

103
00:02:15,420 --> 00:02:16,460
那第一步

104
00:02:16,460 --> 00:02:18,180
就是安装相关的这个包

105
00:02:18,180 --> 00:02:19,280
那这些包呢

106
00:02:19,280 --> 00:02:20,260
其实我已经帮大家装好了

107
00:02:20,260 --> 00:02:21,340
所以说我们从第二步开始

108
00:02:21,340 --> 00:02:22,680
那第二步的话呢

109
00:02:22,680 --> 00:02:25,420
我们要导入ApolloServer相关的插件

110
00:02:25,420 --> 00:02:28,040
然后通过use的方式整合进来

111
00:02:28,040 --> 00:02:29,020
所以说我们要的是这一局

112
00:02:29,020 --> 00:02:32,520
打拼我们的入口文件在这

113
00:02:32,520 --> 00:02:37,280
我们第一步要做的就是导入Apollo相关插件

114
00:02:37,280 --> 00:02:40,680
导入插件之后来启用插件

115
00:02:40,680 --> 00:02:44,340
这里边启用的方式通过Vue的use这个方法

116
00:02:44,340 --> 00:02:46,300
把这个放进去

117
00:02:46,300 --> 00:02:47,760
这是我们的第二步

118
00:02:47,760 --> 00:02:48,860
这分号的话我们就不加了

119
00:02:48,860 --> 00:02:49,400
统一都不加

120
00:02:49,400 --> 00:02:50,820
然后我们看下一步

121
00:02:50,820 --> 00:02:53,060
我们要产生

122
00:02:53,060 --> 00:02:55,260
Apollo客户端的实际立项

123
00:02:55,260 --> 00:02:56,260
但是这个实际立项

124
00:02:56,260 --> 00:02:57,320
也要导入对应的包

125
00:02:57,320 --> 00:02:59,760
导入之后就可以实际化了

126
00:02:59,760 --> 00:03:00,540
所以说就是这段代码

127
00:03:00,540 --> 00:03:02,160
把它拿过来

128
00:03:02,160 --> 00:03:04,160
然后这里边先备注

129
00:03:04,160 --> 00:03:09,020
产生Apollo客户端实际立项

130
00:03:09,020 --> 00:03:10,700
但是你要产生对象的话

131
00:03:10,700 --> 00:03:11,220
先得导入

132
00:03:11,220 --> 00:03:12,100
这是必然

133
00:03:12,100 --> 00:03:13,640
这里边需要强调的

134
00:03:13,640 --> 00:03:15,480
产生客户端实际立项的时候

135
00:03:15,480 --> 00:03:16,040
要指定一下

136
00:03:16,040 --> 00:03:17,380
你要调用的接口地址

137
00:03:17,380 --> 00:03:17,800
是什么

138
00:03:17,800 --> 00:03:18,960
要通过这个属性URI

139
00:03:18,960 --> 00:03:19,440
来指定

140
00:03:19,440 --> 00:03:20,180
注意是URI

141
00:03:20,180 --> 00:03:21,000
不是URL

142
00:03:21,000 --> 00:03:22,520
那这个地址的话呢

143
00:03:22,520 --> 00:03:23,560
要对接我们上节课

144
00:03:23,560 --> 00:03:24,600
完成的那个服务端的

145
00:03:24,600 --> 00:03:25,180
那个接口的地址

146
00:03:25,180 --> 00:03:26,220
这端口的话是4000

147
00:03:26,220 --> 00:03:28,120
好 这是产生实际对象

148
00:03:28,120 --> 00:03:28,720
然后的话

149
00:03:28,720 --> 00:03:29,960
把这实际对象整合到

150
00:03:29,960 --> 00:03:31,380
这个上一步的这个插件中

151
00:03:31,380 --> 00:03:32,180
所以说呢

152
00:03:32,180 --> 00:03:32,740
有这么一步

153
00:03:32,740 --> 00:03:34,860
这是万一的客户端对象

154
00:03:34,860 --> 00:03:36,300
我们需要注意的

155
00:03:36,300 --> 00:03:36,760
然后呢

156
00:03:36,760 --> 00:03:37,100
再往下

157
00:03:37,100 --> 00:03:37,880
还有一步

158
00:03:37,880 --> 00:03:38,620
就是呢

159
00:03:38,620 --> 00:03:40,400
把这个Apollo的客户端的实际对象呢

160
00:03:40,400 --> 00:03:41,900
要挂载到V01的实际中

161
00:03:41,900 --> 00:03:42,680
所以说

162
00:03:42,680 --> 00:03:44,140
这还有一步

163
00:03:44,140 --> 00:03:44,940
就是

164
00:03:44,940 --> 00:03:47,600
把Apollo

165
00:03:47,600 --> 00:03:49,060
这个实际对象

166
00:03:49,060 --> 00:03:53,140
挂载到这个VoE实力中

167
00:03:53,140 --> 00:03:54,340
那怎么挂载呢

168
00:03:54,340 --> 00:03:55,400
这个就比较简单了

169
00:03:55,400 --> 00:03:58,240
在这个位置直接把它放进来就可以了

170
00:03:58,240 --> 00:03:58,800
加上多号

171
00:03:58,800 --> 00:04:01,920
好这是相关的一些步骤

172
00:04:01,920 --> 00:04:02,980
然后呢我们再往下

173
00:04:02,980 --> 00:04:03,760
第五步

174
00:04:03,760 --> 00:04:05,240
第五步我们就可以导入

175
00:04:05,240 --> 00:04:06,620
相关的Apollo的API

176
00:04:06,620 --> 00:04:07,580
来做这个接口交用了

177
00:04:07,580 --> 00:04:08,500
所以说这里边呢

178
00:04:08,500 --> 00:04:09,500
要导入对应的包

179
00:04:09,500 --> 00:04:10,440
这个导入的话呢

180
00:04:10,440 --> 00:04:12,220
我们要在具体的组件中来用了

181
00:04:12,220 --> 00:04:13,540
所以说在这个位置呢

182
00:04:13,540 --> 00:04:14,120
我们要导入

183
00:04:14,120 --> 00:04:15,660
这个Apollo

184
00:04:15,660 --> 00:04:17,380
客户端

185
00:04:17,380 --> 00:04:18,620
相关API

186
00:04:18,620 --> 00:04:21,020
导入之后我们就可以直接用了

187
00:04:21,020 --> 00:04:22,880
我们看下一步

188
00:04:22,880 --> 00:04:23,840
怎么来用呢

189
00:04:23,840 --> 00:04:26,680
我们需要在组件的参数当中

190
00:04:26,680 --> 00:04:27,980
提供一个Apollo这样一个属性

191
00:04:27,980 --> 00:04:29,900
里边做接触的调用

192
00:04:29,900 --> 00:04:31,160
首先我们需要提供的

193
00:04:31,160 --> 00:04:32,400
就是Data中要提供一个Infer

194
00:04:32,400 --> 00:04:34,160
这个属性来接收

195
00:04:34,160 --> 00:04:35,640
这个服务端反复来的数据

196
00:04:35,640 --> 00:04:36,620
所以说我们这样来做

197
00:04:36,620 --> 00:04:38,500
在这先提供一个Infer

198
00:04:38,500 --> 00:04:39,940
这样一个属性

199
00:04:39,940 --> 00:04:41,160
它的值实际上是一个对象

200
00:04:41,160 --> 00:04:42,160
这个对象当中

201
00:04:42,160 --> 00:04:43,280
我们需要得到的一个是List

202
00:04:43,280 --> 00:04:44,760
这我们都先给它制空

203
00:04:44,760 --> 00:04:46,300
这边注意一下

204
00:04:46,300 --> 00:04:47,780
就是Infer

205
00:04:47,780 --> 00:04:49,460
表示服务端

206
00:04:49,460 --> 00:04:52,920
返回的数据

207
00:04:52,920 --> 00:04:54,860
然后这个数据怎么返回呢

208
00:04:54,860 --> 00:04:57,000
我们需要单独的提供一个属性叫Polo

209
00:04:57,000 --> 00:04:58,140
注意啊

210
00:04:58,140 --> 00:04:59,900
Polo这个属性属于Polo

211
00:04:59,900 --> 00:05:01,140
它的相关的ETN

212
00:05:01,140 --> 00:05:02,040
这个名字是固定的

213
00:05:02,040 --> 00:05:02,940
它后边呢

214
00:05:02,940 --> 00:05:03,960
也是一个对象

215
00:05:03,960 --> 00:05:05,640
那这个对象里边放什么呢

216
00:05:05,640 --> 00:05:06,140
看文章

217
00:05:06,140 --> 00:05:08,600
它里边要放一个属性名

218
00:05:08,600 --> 00:05:09,380
和下面要匹配

219
00:05:09,380 --> 00:05:10,300
否则的话

220
00:05:10,300 --> 00:05:12,580
它找不到你这个数据要返回给谁

221
00:05:12,580 --> 00:05:13,320
所以说呢

222
00:05:13,320 --> 00:05:14,760
我们这里边要放上这个

223
00:05:14,760 --> 00:05:15,640
音笔属性

224
00:05:15,640 --> 00:05:17,040
这个名字和下面这个data中的音笔

225
00:05:17,040 --> 00:05:17,500
要对得上

226
00:05:17,500 --> 00:05:20,460
然后这里边要做的就是查询的动作

227
00:05:20,460 --> 00:05:21,840
怎么查

228
00:05:21,840 --> 00:05:22,800
这个很关键

229
00:05:22,800 --> 00:05:25,500
看看这有一个query属性

230
00:05:25,500 --> 00:05:27,740
这个属性的名字也是固定的

231
00:05:27,740 --> 00:05:29,400
属于Polo来提供的EPI

232
00:05:29,400 --> 00:05:30,940
这个后边就是调用

233
00:05:30,940 --> 00:05:33,700
GTRL这个方法来触发

234
00:05:33,700 --> 00:05:34,560
这个程序的动作

235
00:05:34,560 --> 00:05:36,740
这里边用到的标签模板

236
00:05:36,740 --> 00:05:38,500
和我们之前所学的查询语法

237
00:05:38,500 --> 00:05:39,800
就完全对得上了

238
00:05:39,800 --> 00:05:41,120
所以说接下来我们要做的是

239
00:05:41,120 --> 00:05:43,260
做这个调用的动作

240
00:05:43,260 --> 00:05:44,720
那就是在这我们要提供一个属性

241
00:05:44,720 --> 00:05:45,340
叫query

242
00:05:45,340 --> 00:05:46,120
要做查询

243
00:05:46,120 --> 00:05:47,760
那查询的动作后边通过这个

244
00:05:47,760 --> 00:05:48,460
GPL

245
00:05:48,460 --> 00:05:50,420
这个函数

246
00:05:50,420 --> 00:05:51,460
或者叫方法

247
00:05:51,460 --> 00:05:52,220
来做交用

248
00:05:52,220 --> 00:05:53,580
后边是标签模板

249
00:05:53,580 --> 00:05:55,700
这里边要写的就是

250
00:05:55,700 --> 00:05:57,460
瓜夫QL的这个查询的预法规则

251
00:05:57,460 --> 00:05:58,940
在这我们要查询的

252
00:05:58,940 --> 00:05:59,440
就是

253
00:05:59,440 --> 00:06:01,460
这个留言板相关的

254
00:06:01,460 --> 00:06:02,360
这个列表信息

255
00:06:02,360 --> 00:06:03,480
这让我们提个名字

256
00:06:03,480 --> 00:06:04,940
这叫

257
00:06:04,940 --> 00:06:05,740
什么呀

258
00:06:05,740 --> 00:06:06,500
就叫Infer版

259
00:06:06,500 --> 00:06:08,160
或者你提个别的名字

260
00:06:08,160 --> 00:06:08,680
也是可以的

261
00:06:08,680 --> 00:06:10,000
这起个查询的名称

262
00:06:10,000 --> 00:06:11,120
然后呢

263
00:06:11,120 --> 00:06:12,300
这里边要指定一下

264
00:06:12,300 --> 00:06:13,840
你要查哪些字段信息

265
00:06:13,840 --> 00:06:15,400
那具体我们要查的

266
00:06:15,400 --> 00:06:15,940
其实就是

267
00:06:15,940 --> 00:06:16,760
infer这个字段

268
00:06:16,760 --> 00:06:17,720
然后infer下边

269
00:06:17,720 --> 00:06:18,480
有谁呢

270
00:06:18,480 --> 00:06:18,960
有list

271
00:06:18,960 --> 00:06:19,960
那list的下边

272
00:06:19,960 --> 00:06:20,840
有youtername

273
00:06:20,840 --> 00:06:22,520
还有content

274
00:06:22,520 --> 00:06:23,600
还有呢

275
00:06:23,600 --> 00:06:24,080
就是date

276
00:06:24,080 --> 00:06:25,720
好

277
00:06:25,720 --> 00:06:26,340
那这就是

278
00:06:26,340 --> 00:06:27,940
查询的主体的

279
00:06:27,940 --> 00:06:28,660
业务流程

280
00:06:28,660 --> 00:06:30,320
那其中这个模板字幕式当中的

281
00:06:30,320 --> 00:06:30,940
这些代码呢

282
00:06:30,940 --> 00:06:32,440
我们之前都详细的分析过

283
00:06:32,440 --> 00:06:33,560
这就是

284
00:06:33,560 --> 00:06:35,240
quarkl的查询的语法规则

285
00:06:35,240 --> 00:06:36,380
好

286
00:06:36,380 --> 00:06:37,400
那到此为止呢

287
00:06:37,400 --> 00:06:38,120
我们整个六个固扯

288
00:06:38,120 --> 00:06:39,000
就完成了

289
00:06:39,000 --> 00:06:41,220
那现在我们要验证的就是

290
00:06:41,220 --> 00:06:42,800
这个请求能不能发出去

291
00:06:42,800 --> 00:06:44,580
就是当我们这个组件展示的时候

292
00:06:44,580 --> 00:06:44,960
实际上

293
00:06:44,960 --> 00:06:46,000
会自动的来触发

294
00:06:46,000 --> 00:06:48,020
这里边这个请求的动作

295
00:06:48,020 --> 00:06:48,840
那接下来呢

296
00:06:48,840 --> 00:06:49,260
我们看一下

297
00:06:49,260 --> 00:06:50,880
这个服务端呢

298
00:06:50,880 --> 00:06:51,900
这个服务有没有报错

299
00:06:51,900 --> 00:06:52,660
在这呢没有报错

300
00:06:52,660 --> 00:06:54,140
那然后呢

301
00:06:54,140 --> 00:06:55,300
我们看这个页面

302
00:06:55,300 --> 00:06:56,300
打开控制台

303
00:06:56,300 --> 00:06:57,580
然后看这个Network

304
00:06:57,580 --> 00:06:58,280
我们刷新一下

305
00:06:58,280 --> 00:06:59,740
然后呢

306
00:06:59,740 --> 00:07:00,340
我们点开这个接口

307
00:07:00,340 --> 00:07:00,960
看这里

308
00:07:00,960 --> 00:07:01,960
我们就返回了

309
00:07:01,960 --> 00:07:02,780
我们需要的数据

310
00:07:02,780 --> 00:07:03,480
就是这个list

311
00:07:03,480 --> 00:07:04,860
那就证明了

312
00:07:04,860 --> 00:07:06,100
我们刚才在这里边

313
00:07:06,100 --> 00:07:07,360
所做的这个查询呢

314
00:07:07,360 --> 00:07:08,500
其实已经生效了

315
00:07:08,500 --> 00:07:09,820
就是当这个组件显示的时候呢

316
00:07:09,820 --> 00:07:10,780
就会发出去这个请求

317
00:07:10,780 --> 00:07:12,000
那这个数据给谁呢

318
00:07:12,000 --> 00:07:13,080
实际上已经把数据

319
00:07:13,080 --> 00:07:14,340
复制给了这个infer

320
00:07:14,340 --> 00:07:16,160
所以说下一步我们要做的

321
00:07:16,160 --> 00:07:17,260
就是把这个数据展示出来

322
00:07:17,260 --> 00:07:17,720
那就可以

323
00:07:17,720 --> 00:07:19,460
好那到此为止呢

324
00:07:19,460 --> 00:07:20,560
关于这个基本的六个步骤

325
00:07:20,560 --> 00:07:21,660
我们就说到这里

326
00:07:21,660 --> 00:07:24,320
这是关于VoE整合PoloSever

327
00:07:24,320 --> 00:07:25,040
科目端

328
00:07:25,040 --> 00:07:26,940
不要用GovQR的整体的步骤

329
00:07:26,940 --> 00:07:27,620
就这么多

