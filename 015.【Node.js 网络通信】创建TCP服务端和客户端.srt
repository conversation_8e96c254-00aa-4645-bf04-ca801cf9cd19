1
00:00:00,000 --> 00:00:03,500
在基本了解了TCP的工作原理之后

2
00:00:03,500 --> 00:00:06,220
接下来我们就可以使用Node.js提供的Net模块

3
00:00:06,220 --> 00:00:08,800
来构建TCP服务端和客户端了

4
00:00:08,800 --> 00:00:11,840
那这个时候来到我们的编辑器当中

5
00:00:11,840 --> 00:00:13,480
我们在接下来

6
00:00:13,480 --> 00:00:14,880
我们在我们这个代码目录当中

7
00:00:14,880 --> 00:00:17,160
我们首先来创建一个文件

8
00:00:17,160 --> 00:00:20,500
我们把它称之为Server.js

9
00:00:20,500 --> 00:00:22,280
我们先来创建一个服务端

10
00:00:22,280 --> 00:00:23,780
我们待会有了服务端之后

11
00:00:23,780 --> 00:00:24,960
我们再来创建这个客户端

12
00:00:24,960 --> 00:00:27,060
那这个怎么来做

13
00:00:27,060 --> 00:00:27,700
非常简单

14
00:00:27,700 --> 00:00:30,980
首先我们来加载Night模块

15
00:00:30,980 --> 00:00:33,140
加载Night模块进来以后

16
00:00:33,140 --> 00:00:35,980
然后接下来我们调用Night模块提供的一个方法

17
00:00:35,980 --> 00:00:38,840
叫做CreteServe

18
00:00:38,840 --> 00:00:39,900
叫做CreteServe

19
00:00:39,900 --> 00:00:41,820
那么它返回一个实例

20
00:00:41,820 --> 00:00:43,500
我们称之为Serve

21
00:00:43,500 --> 00:00:45,560
就是创建一个服务

22
00:00:45,560 --> 00:00:46,540
创建好以后

23
00:00:46,540 --> 00:00:47,760
然后接下来

24
00:00:47,760 --> 00:00:49,080
那么服务要干嘛

25
00:00:49,080 --> 00:00:51,560
那服务要干的就是要等待客户端的连接

26
00:00:51,560 --> 00:00:53,260
然后处理这个连接请求

27
00:00:53,260 --> 00:00:54,980
所以说我们这个服务呢

28
00:00:54,980 --> 00:00:56,560
有一个事件

29
00:00:56,560 --> 00:00:57,960
服务有一个世界

30
00:00:57,960 --> 00:00:59,280
那么这个世界的名字呢

31
00:00:59,280 --> 00:01:00,400
叫做Connection

32
00:01:00,400 --> 00:01:01,780
就是说

33
00:01:01,780 --> 00:01:03,560
当客户端连接上来以后

34
00:01:03,560 --> 00:01:05,220
它就要来做对应的操作

35
00:01:05,220 --> 00:01:06,580
然后接下来

36
00:01:06,580 --> 00:01:07,340
我们在这儿

37
00:01:07,340 --> 00:01:09,460
就可以有一个回调函数

38
00:01:09,460 --> 00:01:10,060
回调函数

39
00:01:10,060 --> 00:01:12,820
Client Socket

40
00:01:12,820 --> 00:01:15,900
那么这个什么是Client Socket的呢

41
00:01:15,900 --> 00:01:16,860
那这个东西呢

42
00:01:16,860 --> 00:01:18,480
其实就是我们客户端 Socket

43
00:01:18,480 --> 00:01:19,280
客户端 Socket

44
00:01:19,280 --> 00:01:21,040
我们接下来

45
00:01:21,040 --> 00:01:21,860
我们大家来答应一下

46
00:01:21,860 --> 00:01:22,340
就是说

47
00:01:22,340 --> 00:01:24,160
有新的连接

48
00:01:24,160 --> 00:01:26,540
新的连接进来了

49
00:01:26,540 --> 00:01:28,540
然后至于这个ClientSocket

50
00:01:28,540 --> 00:01:29,460
它里面有什么东西

51
00:01:29,460 --> 00:01:30,180
我们待会再说

52
00:01:30,180 --> 00:01:31,620
然后这样的话

53
00:01:31,620 --> 00:01:32,800
我们就把什么呢

54
00:01:32,800 --> 00:01:35,080
我们就监听了这个连接请求

55
00:01:35,080 --> 00:01:37,500
那么监听好以后

56
00:01:37,500 --> 00:01:38,400
监听好以后

57
00:01:38,400 --> 00:01:39,600
然后接下来呢

58
00:01:39,600 --> 00:01:41,660
我们可以在这呢

59
00:01:41,660 --> 00:01:43,340
我们还可以干一件事

60
00:01:43,340 --> 00:01:46,480
通过ClientSocket

61
00:01:46,480 --> 00:01:51,080
给这个给当前连接的客户端

62
00:01:51,080 --> 00:01:52,600
发送数据

63
00:01:52,600 --> 00:01:53,060
发送数据

64
00:01:53,060 --> 00:01:53,880
就是干这么一件事

65
00:01:53,880 --> 00:01:54,980
所以说这个时候

66
00:01:54,980 --> 00:01:55,760
我们可以来看一下

67
00:01:55,760 --> 00:01:57,120
我们直接在这 client socket

68
00:01:57,120 --> 00:01:58,140
怎么去发呢

69
00:01:58,140 --> 00:01:59,540
我们在这点 write

70
00:01:59,540 --> 00:02:01,340
随便给他想一个数据过去

71
00:02:01,340 --> 00:02:02,400
例如这个 hello

72
00:02:02,400 --> 00:02:03,520
我们是服务端

73
00:02:03,520 --> 00:02:04,420
就是发这个 hello

74
00:02:04,420 --> 00:02:05,840
给我们这个当前邻居的客户端

75
00:02:05,840 --> 00:02:06,620
非常简单

76
00:02:06,620 --> 00:02:07,480
好了

77
00:02:07,480 --> 00:02:08,700
那么这个写好以后

78
00:02:08,700 --> 00:02:10,300
然后接下来我们在这

79
00:02:10,300 --> 00:02:11,220
我们在这

80
00:02:11,220 --> 00:02:13,460
我们要让我们的服务端机启动起来

81
00:02:13,460 --> 00:02:14,400
那么这个服务端呢

82
00:02:14,400 --> 00:02:15,080
需要干嘛呢

83
00:02:15,080 --> 00:02:16,760
就需要去绑定一个端口号

84
00:02:16,760 --> 00:02:18,380
serve.listen

85
00:02:18,380 --> 00:02:19,560
我们在这

86
00:02:19,560 --> 00:02:19,560
我们在这

87
00:02:19,560 --> 00:02:21,040
我们在绑定一个3000

88
00:02:21,040 --> 00:02:21,980
我们别带一个端口号

89
00:02:21,980 --> 00:02:23,480
因为TCP

90
00:02:23,480 --> 00:02:25,220
它就是这个所谓的socket的通信

91
00:02:25,220 --> 00:02:26,900
是要对向啊

92
00:02:26,900 --> 00:02:28,200
我们这个端口的啊

93
00:02:28,200 --> 00:02:28,580
端到端

94
00:02:28,580 --> 00:02:29,300
好了

95
00:02:29,300 --> 00:02:30,720
然后接下来啊

96
00:02:30,720 --> 00:02:31,960
它就有一个回调啊

97
00:02:31,960 --> 00:02:32,840
我们再简单输出一下

98
00:02:32,840 --> 00:02:34,480
比方说我们的server

99
00:02:34,480 --> 00:02:35,820
runny啊

100
00:02:35,820 --> 00:02:36,660
就是运行在啊

101
00:02:36,660 --> 00:02:37,240
运行在哪里呢

102
00:02:37,240 --> 00:02:38,400
我们这个127

103
00:02:38,400 --> 00:02:40,940
127.0.0.1

104
00:02:40,940 --> 00:02:43,460
然后3000

105
00:02:43,460 --> 00:02:45,840
运行在这样一个地址啊

106
00:02:45,840 --> 00:02:48,060
就是运行在IP地址为127.0.0.1

107
00:02:48,060 --> 00:02:49,420
然后端口号是3000

108
00:02:49,420 --> 00:02:50,880
所以它就是这样的

109
00:02:50,880 --> 00:02:51,060
好了

110
00:02:51,060 --> 00:02:52,540
我们最基本的一个复端代码

111
00:02:52,540 --> 00:02:53,180
在这就写好了

112
00:02:53,180 --> 00:02:54,140
那写好以后

113
00:02:54,140 --> 00:02:55,900
接下来我们还得把客户端创建出来

114
00:02:55,900 --> 00:02:57,360
你才好去测试

115
00:02:57,360 --> 00:02:58,320
当然了

116
00:02:58,320 --> 00:03:01,040
这个时候我们可以先打开我们的控制台

117
00:03:01,040 --> 00:03:02,000
把这个服务端

118
00:03:02,000 --> 00:03:03,160
就是TCP客户端

119
00:03:03,160 --> 00:03:04,280
去给它启动起来

120
00:03:04,280 --> 00:03:06,380
来到我们的面对行当中

121
00:03:06,380 --> 00:03:08,040
我们为了方便

122
00:03:08,040 --> 00:03:09,080
我们在那儿可以使用

123
00:03:09,080 --> 00:03:10,360
node mode这个工具

124
00:03:10,360 --> 00:03:11,220
工具模块

125
00:03:11,220 --> 00:03:12,800
执行这个server.js

126
00:03:12,800 --> 00:03:13,960
那么这样的话

127
00:03:13,960 --> 00:03:15,400
就是说当我们这个server.js

128
00:03:15,400 --> 00:03:16,660
这个文件发生变化

129
00:03:16,660 --> 00:03:18,140
node mode会帮我们去自动重启

130
00:03:18,140 --> 00:03:19,340
我们就不需要就是说

131
00:03:19,340 --> 00:03:20,960
每次改善代码都来手动重启

132
00:03:20,960 --> 00:03:21,680
这就麻烦了

133
00:03:21,680 --> 00:03:23,060
这就是这样的

134
00:03:23,060 --> 00:03:23,240
好

135
00:03:23,240 --> 00:03:24,600
我们大家可以看到这里就给出提示

136
00:03:24,600 --> 00:03:25,500
说server running

137
00:03:25,500 --> 00:03:27,840
那就证明我们这个TCP服务端

138
00:03:27,840 --> 00:03:30,940
已经被成功的创建并且运行起来了

139
00:03:30,940 --> 00:03:33,100
那这个服务端创建好以后

140
00:03:33,100 --> 00:03:36,400
然后接下来我们要创建一个客户端来进行验证

141
00:03:36,400 --> 00:03:39,000
那么客户端我们再来就创建一个文件

142
00:03:39,000 --> 00:03:40,500
叫做client.js

143
00:03:40,500 --> 00:03:43,840
那么这个客户端该怎么来写

144
00:03:43,840 --> 00:03:45,540
我们待会可以来看一下

145
00:03:45,540 --> 00:03:48,900
我们在这里的话

146
00:03:48,900 --> 00:03:50,580
我们其实还是一样的

147
00:03:50,580 --> 00:03:52,020
我们为了方便

148
00:03:52,020 --> 00:03:53,940
我们把这个server啊

149
00:03:53,940 --> 00:03:56,000
我们把它放到右边client放到左边

150
00:03:56,000 --> 00:03:57,260
好 我们在这里的话

151
00:03:57,260 --> 00:04:00,880
来const首先来加载这个net模块

152
00:04:00,880 --> 00:04:02,280
好 拿进来以后

153
00:04:02,280 --> 00:04:04,740
接下来我们调用net的一个方法

154
00:04:04,740 --> 00:04:07,300
就是服务端叫create server

155
00:04:07,300 --> 00:04:08,120
客户端呢

156
00:04:08,120 --> 00:04:09,080
当然它叫什么呢

157
00:04:09,080 --> 00:04:11,500
它叫做create connection

158
00:04:11,500 --> 00:04:13,340
就是创建一个连接

159
00:04:13,340 --> 00:04:14,920
好 要连接谁呢

160
00:04:14,920 --> 00:04:15,700
你得告诉他

161
00:04:15,700 --> 00:04:17,480
要来连接我们这个服务端呀

162
00:04:17,480 --> 00:04:19,040
那么第一个参数呢

163
00:04:19,040 --> 00:04:20,820
它就是一个选项对象

164
00:04:20,820 --> 00:04:22,240
你得告诉他

165
00:04:22,240 --> 00:04:22,900
要连接

166
00:04:22,900 --> 00:04:23,760
首先host

167
00:04:23,760 --> 00:04:25,040
连到哪呢

168
00:04:25,040 --> 00:04:26,200
他默认

169
00:04:26,200 --> 00:04:27,220
就是localhost

170
00:04:27,220 --> 00:04:27,820
也就是

171
00:04:27,820 --> 00:04:30,540
127.0.0.1

172
00:04:30,540 --> 00:04:31,940
他默认就会连到这里

173
00:04:31,940 --> 00:04:32,980
然后port

174
00:04:32,980 --> 00:04:34,460
所以告诉他

175
00:04:34,460 --> 00:04:34,880
这个端口号

176
00:04:34,880 --> 00:04:36,160
我们服务当中端口号

177
00:04:36,160 --> 00:04:36,740
是3000

178
00:04:36,740 --> 00:04:39,220
这是最基本的一个配置

179
00:04:39,220 --> 00:04:40,000
然后接下来

180
00:04:40,000 --> 00:04:41,320
我们大家就可以来接收一下

181
00:04:41,320 --> 00:04:42,940
这个credit connection之后

182
00:04:42,940 --> 00:04:44,120
我们就会得到

183
00:04:44,120 --> 00:04:45,520
他的一个结果

184
00:04:45,520 --> 00:04:46,260
得到这个结果

185
00:04:46,260 --> 00:04:48,400
所以说我们大家就可以得到

186
00:04:48,400 --> 00:04:49,400
这个叫client

187
00:04:49,400 --> 00:04:51,120
拿到它以后

188
00:04:51,120 --> 00:04:52,700
然后接下来

189
00:04:52,700 --> 00:04:54,880
接下来我们就可以在这儿去

190
00:04:54,880 --> 00:04:56,960
然后让这个

191
00:04:56,960 --> 00:04:58,020
我们就可以去测试了

192
00:04:58,020 --> 00:04:59,720
实际上就可以去测试了

193
00:04:59,720 --> 00:05:01,080
就是我们在这儿去把这个连接

194
00:05:01,080 --> 00:05:02,460
给它去创建出来

195
00:05:02,460 --> 00:05:03,760
一个是host 一个是part

196
00:05:03,760 --> 00:05:06,000
来那这个时候我们怎么

197
00:05:06,000 --> 00:05:06,700
注意就这样就行了

198
00:05:06,700 --> 00:05:08,060
接下来我们怎么去测

199
00:05:08,060 --> 00:05:10,300
当然了也别忘了一件事

200
00:05:10,300 --> 00:05:11,500
就是说我怎么知道这个东西呢

201
00:05:11,500 --> 00:05:12,920
它是否创建成功了

202
00:05:12,920 --> 00:05:13,960
对吧

203
00:05:13,960 --> 00:05:16,140
所以说这个client呢

204
00:05:16,140 --> 00:05:17,500
它也有一个事件

205
00:05:17,500 --> 00:05:18,280
也有一个事件

206
00:05:18,280 --> 00:05:21,180
他这个事件呢叫做connect

207
00:05:21,180 --> 00:05:25,420
那么这个就表示啊

208
00:05:25,420 --> 00:05:30,500
这个叫成功的连接到服务器

209
00:05:30,500 --> 00:05:32,360
就是成功连接到服务器

210
00:05:32,360 --> 00:05:33,060
好了

211
00:05:33,060 --> 00:05:34,760
那接下来这个代码写好以后

212
00:05:34,760 --> 00:05:35,680
那么这个时候

213
00:05:35,680 --> 00:05:38,400
我们把这个文件给它调换一下

214
00:05:38,400 --> 00:05:39,520
左边是我们的server

215
00:05:39,520 --> 00:05:40,560
右边是我们的client

216
00:05:40,560 --> 00:05:42,840
然后这个时候打开我们的控制台

217
00:05:42,840 --> 00:05:44,920
我们在控制台当中

218
00:05:44,920 --> 00:05:45,880
左边是server

219
00:05:45,880 --> 00:05:47,240
右边我们来执行一下

220
00:05:47,240 --> 00:05:48,380
我们这个client

221
00:05:48,380 --> 00:05:49,400
我们来看一下

222
00:05:49,400 --> 00:05:51,560
我们在这里呢

223
00:05:51,560 --> 00:05:53,900
就是我们可以直接去

224
00:05:53,900 --> 00:05:55,120
none more这个client

225
00:05:55,120 --> 00:05:59,260
我们进入这个代码目录

226
00:05:59,260 --> 00:06:01,100
然后在这里我们去none more

227
00:06:01,100 --> 00:06:02,320
之前这个client点加字

228
00:06:02,320 --> 00:06:03,060
回车

229
00:06:03,060 --> 00:06:04,940
那么此时我们大家就会看到

230
00:06:04,940 --> 00:06:07,260
当我们这个客户端

231
00:06:07,260 --> 00:06:09,580
与服务端建立连接成功以后

232
00:06:09,580 --> 00:06:10,720
那么这里就输出了

233
00:06:10,720 --> 00:06:12,200
成功的连接到服务器

234
00:06:12,200 --> 00:06:13,580
同时我们也可以看到

235
00:06:13,580 --> 00:06:14,800
那么服务端这

236
00:06:14,800 --> 00:06:16,620
那是不是这个connection

237
00:06:16,620 --> 00:06:17,800
这个事件就被处罚了呀

238
00:06:17,800 --> 00:06:18,860
那证明就是说

239
00:06:18,860 --> 00:06:20,480
有新的连接进来了

240
00:06:20,480 --> 00:06:21,760
有新的连接进来了

241
00:06:21,760 --> 00:06:22,360
好

242
00:06:22,360 --> 00:06:23,940
那接下来啊

243
00:06:23,940 --> 00:06:24,480
就证明什么呢

244
00:06:24,480 --> 00:06:26,380
就是双方已经可以进行通信了

245
00:06:26,380 --> 00:06:27,560
那进行通信的话

246
00:06:27,560 --> 00:06:28,660
我们家里可以看到

247
00:06:28,660 --> 00:06:29,020
就是说

248
00:06:29,020 --> 00:06:30,140
我们刚才在这里

249
00:06:30,140 --> 00:06:31,540
去给客户端发了一个数据

250
00:06:31,540 --> 00:06:34,460
但是我们客户端是没有收到啊

251
00:06:34,460 --> 00:06:35,200
然后就是说

252
00:06:35,200 --> 00:06:36,080
我们家里可以看到客户端

253
00:06:36,080 --> 00:06:37,120
并没有去收到

254
00:06:37,120 --> 00:06:39,060
或者去打印那条数据

255
00:06:39,060 --> 00:06:40,180
那这个东西怎么办呢

256
00:06:40,180 --> 00:06:41,960
这个就需要我们自己单独来写代码

257
00:06:41,960 --> 00:06:44,240
来接收服务端发送的数据了

258
00:06:44,240 --> 00:06:45,660
就是说你虽然发过来了

259
00:06:45,660 --> 00:06:47,320
但是客户单并没有去接收

260
00:06:47,320 --> 00:06:49,180
所以接下来我们在这就要去单独去写

261
00:06:49,180 --> 00:06:49,700
单独去写

262
00:06:49,700 --> 00:06:51,200
那我们在这拍手来演示一下

263
00:06:51,200 --> 00:06:53,400
那么这个就是client

264
00:06:53,400 --> 00:06:55,680
client本身呢

265
00:06:55,680 --> 00:06:56,320
也有个事件

266
00:06:56,320 --> 00:06:57,800
叫做data事件

267
00:06:57,800 --> 00:07:01,180
然后我们再去date

268
00:07:01,180 --> 00:07:06,420
我们可以看一下这个data到底是什么

269
00:07:06,420 --> 00:07:07,980
这个时候我们可以再来看一下

270
00:07:07,980 --> 00:07:09,840
回到我们的控制台当中

271
00:07:09,840 --> 00:07:11,040
那这个时候我们可以看到

272
00:07:11,040 --> 00:07:13,660
此时你看是不是就打印出了一条数据

273
00:07:13,660 --> 00:07:15,380
当然它默认情况下

274
00:07:15,380 --> 00:07:16,580
都是二精致数据

275
00:07:16,580 --> 00:07:19,180
那你要想看到这个内容结果

276
00:07:19,180 --> 00:07:20,300
那么建议大家这里去

277
00:07:20,300 --> 00:07:21,320
data.2

278
00:07:21,320 --> 00:07:22,500
Stream一下

279
00:07:22,500 --> 00:07:23,540
data.2.3

280
00:07:23,540 --> 00:07:24,260
好了

281
00:07:24,260 --> 00:07:25,000
完了以后

282
00:07:25,000 --> 00:07:26,220
这个时候我们再来看

283
00:07:26,220 --> 00:07:28,720
我们再来看一下

284
00:07:28,720 --> 00:07:30,140
我们把这个东西给它打断

285
00:07:30,140 --> 00:07:32,200
大家可以看到

286
00:07:32,200 --> 00:07:33,740
你看有新的连接进来了

287
00:07:33,740 --> 00:07:34,760
然后扣端

288
00:07:34,760 --> 00:07:36,160
是不是输出了很多网

289
00:07:36,160 --> 00:07:37,720
那这句代码的含义就是说

290
00:07:37,720 --> 00:07:39,780
监听服务端

291
00:07:39,780 --> 00:07:40,980
发给自己的数据

292
00:07:40,980 --> 00:07:42,580
一旦接收到服务端

293
00:07:42,580 --> 00:07:43,200
发给自己的数据

294
00:07:43,200 --> 00:07:44,820
那么这里就去输出

295
00:07:44,820 --> 00:07:46,200
这个对应的结果

296
00:07:46,200 --> 00:07:47,080
所以他在这呢

297
00:07:47,080 --> 00:07:48,000
就是这个意思

298
00:07:48,000 --> 00:07:49,140
当然

299
00:07:49,140 --> 00:07:51,220
我们这个连接结束以后

300
00:07:51,220 --> 00:07:51,700
例如说

301
00:07:51,700 --> 00:07:52,980
我不想再去

302
00:07:52,980 --> 00:07:53,880
接收这个

303
00:07:53,880 --> 00:07:55,080
这个想象数据了

304
00:07:55,080 --> 00:07:55,740
那么你也可以

305
00:07:55,740 --> 00:07:56,780
你也可以去结束连接

306
00:07:56,780 --> 00:07:57,720
那么这个就是他

307
00:07:57,720 --> 00:07:59,520
其他相关API的一个使用

308
00:07:59,520 --> 00:08:00,080
那这里的话

309
00:08:00,080 --> 00:08:01,880
我们这一小节讲的

310
00:08:01,880 --> 00:08:03,900
就是创建服务端和客户端

311
00:08:03,900 --> 00:08:04,920
然后完成了一个

312
00:08:04,920 --> 00:08:05,960
最最基本的

313
00:08:05,960 --> 00:08:08,080
让双方首先建立连接

314
00:08:08,080 --> 00:08:09,580
下一步我们要考虑的

315
00:08:09,580 --> 00:08:10,120
就是这个

316
00:08:10,120 --> 00:08:11,760
如何去进行通信的过程

