1
00:00:00,000 --> 00:00:04,880
好 接下来我们继续来看内置类型相关的内容

2
00:00:04,880 --> 00:00:05,880
就是列表和非空

3
00:00:05,880 --> 00:00:07,680
列表的话我们之前是用过的

4
00:00:07,680 --> 00:00:09,620
其实就类似于我们之前所学的数组

5
00:00:09,620 --> 00:00:11,160
这儿的关键其实就是关于非空

6
00:00:11,160 --> 00:00:12,300
什么是非空呢

7
00:00:12,300 --> 00:00:13,720
它就用碳号来表示非空

8
00:00:13,720 --> 00:00:14,720
就在这个字段中

9
00:00:14,720 --> 00:00:16,120
如果这个类型的后边有了一个碳号

10
00:00:16,120 --> 00:00:17,120
那就表示这个字段

11
00:00:17,120 --> 00:00:18,960
它的值是不允许为空的

12
00:00:18,960 --> 00:00:20,580
那这个非空碳段也可以用于列表中

13
00:00:20,580 --> 00:00:21,660
列表它的最后

14
00:00:21,660 --> 00:00:24,360
这个表示列表本身不允许为空

15
00:00:24,360 --> 00:00:26,400
那如果这碳号在里面的数据项当中

16
00:00:26,400 --> 00:00:27,840
那就表示里面的数据项

17
00:00:27,840 --> 00:00:29,520
它也是不可以为空的

18
00:00:29,520 --> 00:00:30,500
那这个场景的话

19
00:00:30,500 --> 00:00:31,640
我们都一块来演示一下

20
00:00:31,640 --> 00:00:32,640
具体我们这样来做

21
00:00:32,640 --> 00:00:34,420
首先呢

22
00:00:34,420 --> 00:00:35,160
我们要准备一个类型

23
00:00:35,160 --> 00:00:36,960
我们还是以student为例

24
00:00:36,960 --> 00:00:39,080
首先我们提供两个词段

25
00:00:39,080 --> 00:00:39,940
使用

26
00:00:39,940 --> 00:00:41,160
然后呢

27
00:00:41,160 --> 00:00:42,040
来一个age

28
00:00:42,040 --> 00:00:43,980
然后呢

29
00:00:43,980 --> 00:00:44,820
在这个snim的后边呢

30
00:00:44,820 --> 00:00:45,460
我们给它加上碳号

31
00:00:45,460 --> 00:00:46,060
这就表示呢

32
00:00:46,060 --> 00:00:47,260
这个snim的这个值呢

33
00:00:47,260 --> 00:00:48,160
是不允许为空的

34
00:00:48,160 --> 00:00:49,180
但是age可以为空

35
00:00:49,180 --> 00:00:50,860
首先我们在这个查询这个位置

36
00:00:50,860 --> 00:00:51,740
再加上sto

37
00:00:51,740 --> 00:00:53,120
然后要查询

38
00:00:53,120 --> 00:00:53,660
这个student

39
00:00:53,660 --> 00:00:54,640
这个关键点呢

40
00:00:54,640 --> 00:00:55,540
其实是后边这个resolver

41
00:00:55,540 --> 00:00:57,340
这是sto

42
00:00:57,340 --> 00:00:59,940
在这儿呢

43
00:00:59,940 --> 00:01:01,660
我们直接去返回一个对象

44
00:01:01,660 --> 00:01:02,420
sname

45
00:01:02,420 --> 00:01:03,060
在这儿呢

46
00:01:03,060 --> 00:01:03,500
我们是离子

47
00:01:03,500 --> 00:01:04,800
然后年龄

48
00:01:04,800 --> 00:01:06,020
是12

49
00:01:06,020 --> 00:01:07,400
然后的话我们启动

50
00:01:07,400 --> 00:01:08,960
在这个启动的时候呢

51
00:01:08,960 --> 00:01:10,280
我们直接用这个node

52
00:01:10,280 --> 00:01:10,640
mon

53
00:01:10,640 --> 00:01:11,580
就这种方式启动

54
00:01:11,580 --> 00:01:12,280
这样的话呢

55
00:01:12,280 --> 00:01:13,160
我们不需要反复的重启

56
00:01:13,160 --> 00:01:15,300
好

57
00:01:15,300 --> 00:01:15,920
启动成功之后呢

58
00:01:15,920 --> 00:01:16,460
我们在浏览器中

59
00:01:16,460 --> 00:01:17,060
来做一个查询

60
00:01:17,060 --> 00:01:18,160
在这儿呢

61
00:01:18,160 --> 00:01:18,880
我们开一个新的标签

62
00:01:18,880 --> 00:01:20,780
在这儿要查的是

63
00:01:20,780 --> 00:01:22,140
浏览器中的

64
00:01:22,140 --> 00:01:22,740
sname

65
00:01:22,740 --> 00:01:24,340
和什么年龄

66
00:01:24,340 --> 00:01:25,180
然后呢

67
00:01:25,180 --> 00:01:25,680
我们查询

68
00:01:25,680 --> 00:01:27,620
这没什么区别

69
00:01:27,620 --> 00:01:29,100
但是如果我们改一改数据

70
00:01:29,100 --> 00:01:30,220
就有差异了

71
00:01:30,220 --> 00:01:31,000
改谁呢

72
00:01:31,000 --> 00:01:33,120
把这个年龄数据给它注掉

73
00:01:33,120 --> 00:01:35,040
注掉之后

74
00:01:35,040 --> 00:01:35,960
我们再去查询

75
00:01:35,960 --> 00:01:37,260
你会发现

76
00:01:37,260 --> 00:01:39,140
年龄的字段依然可以查询出来

77
00:01:39,140 --> 00:01:41,020
但是它的值是空的

78
00:01:41,020 --> 00:01:41,760
这个不会报错

79
00:01:41,760 --> 00:01:44,000
那是因为年龄字段上面

80
00:01:44,000 --> 00:01:45,040
没有加低空的约数

81
00:01:45,040 --> 00:01:47,500
所以说这个时候是没问题的

82
00:01:47,500 --> 00:01:50,160
但是如果说你把年龄的值放开

83
00:01:50,160 --> 00:01:51,740
把sname给它注掉

84
00:01:51,740 --> 00:01:52,680
那就是说

85
00:01:52,680 --> 00:01:54,420
redover当中没有提供sname的数据

86
00:01:54,420 --> 00:01:55,440
所以说它的值是空的

87
00:01:55,440 --> 00:01:56,260
那在这种情况下

88
00:01:56,260 --> 00:01:56,980
如果说你再去查询

89
00:01:56,980 --> 00:01:57,620
它就会爆错

90
00:01:57,620 --> 00:01:58,140
我们看一下

91
00:01:58,140 --> 00:01:58,700
查询

92
00:01:58,700 --> 00:02:00,240
这有个错误

93
00:02:00,240 --> 00:02:00,780
它说什么呢

94
00:02:00,780 --> 00:02:02,580
说你这个不能够返回一个空纸

95
00:02:02,580 --> 00:02:03,340
对谁呢

96
00:02:03,340 --> 00:02:04,160
对一个非空的语

97
00:02:04,160 --> 00:02:04,800
就是字段

98
00:02:04,800 --> 00:02:06,720
所以说有了这个

99
00:02:06,720 --> 00:02:07,880
碳号的约束之后呢

100
00:02:07,880 --> 00:02:08,620
就是有一些数据

101
00:02:08,620 --> 00:02:09,560
它必须是提供的

102
00:02:09,560 --> 00:02:10,320
如果你不提供

103
00:02:10,320 --> 00:02:11,020
它就会爆错

104
00:02:11,020 --> 00:02:12,600
所以说这种约束呢

105
00:02:12,600 --> 00:02:13,260
是非常必要的

106
00:02:13,260 --> 00:02:14,480
因为我们在实际的开发当中

107
00:02:14,480 --> 00:02:15,580
有一些字段的数据

108
00:02:15,580 --> 00:02:16,880
是必须提供的

109
00:02:16,880 --> 00:02:18,060
这是关于碳号

110
00:02:18,060 --> 00:02:18,860
它的作用

111
00:02:18,860 --> 00:02:20,500
那除了这个

112
00:02:20,500 --> 00:02:21,480
基本的字段之外呢

113
00:02:21,480 --> 00:02:23,880
如果我们这个资料当中有一个列表形式的数据

114
00:02:23,880 --> 00:02:25,180
这个就稍微较大一点

115
00:02:25,180 --> 00:02:27,280
比如说我们这还是再加一个这个分数

116
00:02:27,280 --> 00:02:28,180
scores

117
00:02:28,180 --> 00:02:29,580
那这scores呢它是一个列表

118
00:02:29,580 --> 00:02:31,180
它当中呢包含了一系列的课程

119
00:02:31,180 --> 00:02:32,180
我们就叫cost

120
00:02:32,180 --> 00:02:33,580
这课程呢也加上参号

121
00:02:33,580 --> 00:02:35,180
然后最后呢也加上参号

122
00:02:35,180 --> 00:02:38,480
然后呢我们再提供一个这个课程的类型

123
00:02:38,480 --> 00:02:39,480
就是type

124
00:02:39,480 --> 00:02:40,480
cost

125
00:02:40,480 --> 00:02:41,780
然后呢有finning

126
00:02:41,780 --> 00:02:42,880
就是课程的名称

127
00:02:42,880 --> 00:02:43,880
还是sting

128
00:02:43,880 --> 00:02:45,480
然后的话呢是分数

129
00:02:45,480 --> 00:02:46,280
score

130
00:02:46,280 --> 00:02:47,580
这个的话我们用float

131
00:02:47,580 --> 00:02:49,980
这个是浮点数

132
00:02:49,980 --> 00:02:52,240
这样的话这个类型就提供好了

133
00:02:52,240 --> 00:02:53,260
然后解析数据的时候

134
00:02:53,260 --> 00:02:55,420
在这个位置还应该提供什么数据

135
00:02:55,420 --> 00:02:57,720
我们再提供一个scores数据

136
00:02:57,720 --> 00:02:59,680
这个scores数据它是一个列表

137
00:02:59,680 --> 00:03:02,600
它当中也包含了是cos的类型的数据

138
00:03:02,600 --> 00:03:03,760
有这个thiname

139
00:03:03,760 --> 00:03:05,520
比如说我们这给它一个数学

140
00:03:05,520 --> 00:03:07,160
数学

141
00:03:07,160 --> 00:03:08,380
然后它考了多少分呢

142
00:03:08,380 --> 00:03:09,120
给个分数

143
00:03:09,120 --> 00:03:10,620
比如说99.5

144
00:03:10,620 --> 00:03:13,340
然后下面我们再提供一份数据

145
00:03:13,340 --> 00:03:14,740
thiname

146
00:03:14,740 --> 00:03:16,960
注意这个名字要写对

147
00:03:16,960 --> 00:03:18,980
thiname这是语文

148
00:03:18,980 --> 00:03:21,820
这再来一个分数

149
00:03:21,820 --> 00:03:23,940
这个值得我们考了89.5

150
00:03:23,940 --> 00:03:24,740
好

151
00:03:24,740 --> 00:03:25,980
那这就有数据了

152
00:03:25,980 --> 00:03:26,900
然后保存

153
00:03:26,900 --> 00:03:27,760
我们再去查询

154
00:03:27,760 --> 00:03:29,660
在这个位置呢

155
00:03:29,660 --> 00:03:30,860
我们不再去查这个SNAME了

156
00:03:30,860 --> 00:03:31,480
我们直接呢

157
00:03:31,480 --> 00:03:32,240
去查这个SCOAT

158
00:03:32,240 --> 00:03:34,460
然后呢

159
00:03:34,460 --> 00:03:35,340
我们查询

160
00:03:35,340 --> 00:03:36,540
这里边你注意

161
00:03:36,540 --> 00:03:38,080
光这样查是不可以的

162
00:03:38,080 --> 00:03:39,380
因为这SCOAT它当中呢

163
00:03:39,380 --> 00:03:41,140
还是有这个具体的类型的

164
00:03:41,140 --> 00:03:42,240
所以说你要查这个类型的

165
00:03:42,240 --> 00:03:43,620
这个子属性

166
00:03:43,620 --> 00:03:45,040
或者叫子字段

167
00:03:45,040 --> 00:03:46,740
这里边要简直的给它加上

168
00:03:46,740 --> 00:03:47,660
我们要查的是CNAME

169
00:03:47,660 --> 00:03:48,420
和什么

170
00:03:48,420 --> 00:03:49,780
和sql这样才可以

171
00:03:49,780 --> 00:03:50,620
否则的话

172
00:03:50,620 --> 00:03:51,360
它不知道

173
00:03:51,360 --> 00:03:52,040
你要查询

174
00:03:52,040 --> 00:03:53,060
这个类型下边的

175
00:03:53,060 --> 00:03:53,960
它的子类型是什么

176
00:03:53,960 --> 00:03:55,060
好然后点查询

177
00:03:55,060 --> 00:03:55,860
这就可以了

178
00:03:55,860 --> 00:03:56,940
好这样的话

179
00:03:56,940 --> 00:03:57,420
我们得到了

180
00:03:57,420 --> 00:03:58,660
所有的课程的信息

181
00:03:58,660 --> 00:04:00,360
如果说在这个时候

182
00:04:00,360 --> 00:04:01,920
你把sql的数据

183
00:04:01,920 --> 00:04:02,560
全部除掉

184
00:04:02,560 --> 00:04:03,720
全部除掉

185
00:04:03,720 --> 00:04:04,840
然后这个时候

186
00:04:04,840 --> 00:04:05,620
其实也会报错

187
00:04:05,620 --> 00:04:06,180
我们看一下

188
00:04:06,180 --> 00:04:06,920
保存之后

189
00:04:06,920 --> 00:04:07,500
我们再去查询

190
00:04:07,500 --> 00:04:08,700
又出错了

191
00:04:08,700 --> 00:04:09,180
它说什么呢

192
00:04:09,180 --> 00:04:10,760
它说这个sql也不允许为空

193
00:04:10,760 --> 00:04:12,140
那是因为什么呢

194
00:04:12,140 --> 00:04:13,120
因为我们在这个

195
00:04:13,120 --> 00:04:14,480
列表的最后

196
00:04:14,480 --> 00:04:15,240
也加了一个参号

197
00:04:15,240 --> 00:04:15,920
它的作用

198
00:04:15,920 --> 00:04:16,800
其实和这个参号的作用

199
00:04:16,800 --> 00:04:18,040
是类似的

200
00:04:18,040 --> 00:04:19,180
就是这个列表本身

201
00:04:19,180 --> 00:04:20,040
它也不允许为空

202
00:04:20,040 --> 00:04:21,740
所以说这是这个碳号的作用

203
00:04:21,740 --> 00:04:25,100
所以说我们这个sport也是必须提供数据的

204
00:04:25,100 --> 00:04:26,300
然后我们再保存

205
00:04:26,300 --> 00:04:27,440
保存之后你再查询

206
00:04:27,440 --> 00:04:28,920
这个就正常了

207
00:04:28,920 --> 00:04:31,680
但是里边这个碳号有什么用呢

208
00:04:31,680 --> 00:04:32,300
就是这个碳号

209
00:04:32,300 --> 00:04:33,780
这里边的这个碳号

210
00:04:33,780 --> 00:04:35,340
它有啥用

211
00:04:35,340 --> 00:04:37,500
它用来约束里边的课程

212
00:04:37,500 --> 00:04:39,360
每一项数据都不允许为空

213
00:04:39,360 --> 00:04:41,200
如果说我把第二项数据

214
00:04:41,200 --> 00:04:42,020
把它去掉

215
00:04:42,020 --> 00:04:42,520
换成什么

216
00:04:42,520 --> 00:04:43,240
换成空

217
00:04:43,240 --> 00:04:45,280
然后我们再去查询

218
00:04:45,280 --> 00:04:46,960
这个时候也会报错

219
00:04:46,960 --> 00:04:48,020
为什么呢

220
00:04:48,020 --> 00:04:49,420
因为里边的碳号又发挥作用了

221
00:04:49,420 --> 00:04:50,980
所以说在这种情况下

222
00:04:50,980 --> 00:04:53,960
那就必须得保证里边的数据也是正常的

223
00:04:53,960 --> 00:04:55,340
不允许为空才可以

224
00:04:55,340 --> 00:04:57,980
然后再查询就没问题了

225
00:04:57,980 --> 00:05:00,060
这是关于列表中非空的处理

226
00:05:00,060 --> 00:05:01,360
稍微复杂一点

227
00:05:01,360 --> 00:05:02,700
最后的结论是什么

228
00:05:02,700 --> 00:05:04,680
关键点就是列表中的这两个碳号

229
00:05:04,680 --> 00:05:05,660
这有一个说明

230
00:05:05,660 --> 00:05:07,780
就是如果这碳号放到里边

231
00:05:07,780 --> 00:05:08,900
表示什么

232
00:05:08,900 --> 00:05:11,760
表示这个数据本身它可以是空的

233
00:05:11,760 --> 00:05:14,220
但是里边的数据项不能有空值

234
00:05:14,220 --> 00:05:16,040
如果说你把它的碳号加外边

235
00:05:16,040 --> 00:05:16,720
里边的碳号不加

236
00:05:16,720 --> 00:05:17,440
那就表示呢

237
00:05:17,440 --> 00:05:18,660
这数组本身不能为空

238
00:05:18,660 --> 00:05:20,500
但是里边的值可以是空的

239
00:05:20,500 --> 00:05:22,000
那如果这个两个汉号都加上

240
00:05:22,000 --> 00:05:23,040
那就是数组不能为空

241
00:05:23,040 --> 00:05:24,080
里边的值也不能为空

242
00:05:24,080 --> 00:05:26,060
这是关于这个非空的处理

243
00:05:26,060 --> 00:05:27,560
我们就说到这里

