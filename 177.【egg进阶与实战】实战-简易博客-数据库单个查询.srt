1
00:00:00,000 --> 00:00:04,860
好 接下来我们来看一下怎么去查询单个数据 那么查询单个数据是不是使用秀这样一个方法

2
00:00:04,860 --> 00:00:08,320
 为什么 因为他是不是带有一个id这样的一个参数

3
00:00:08,320 --> 00:00:12,140
好 那么呢 我们来接下来看一下怎么去写

4
00:00:12,140 --> 00:00:13,660
dsync

5
00:00:13,660 --> 00:00:18,900
我们的查询单个数据

6
00:00:18,900 --> 00:00:21,700
好 那么首先呢 我们是不是要获取我们的

7
00:00:21,700 --> 00:00:27,440
获取我们这样一个id 那么在一个id怎么去获取呢 其实前面是不是也讲过

8
00:00:27,440 --> 00:00:28,880
 所以说在这里呢 就咱们不去

9
00:00:29,500 --> 00:00:31,500
想起什么去写的

10
00:00:31,500 --> 00:00:33,500
首先第一步呢我们要

11
00:00:33,500 --> 00:00:35,500
获取文章的id

12
00:00:35,500 --> 00:00:37,500
第二步呢就是查呀

13
00:00:37,500 --> 00:00:39,500
所以呢就是这么的简单

14
00:00:39,500 --> 00:00:41,500
首先呢我们去获取id

15
00:00:41,500 --> 00:00:43,500
那么id呢其实咱们比如说

16
00:00:43,500 --> 00:00:45,500
我们去conc的一个

17
00:00:45,500 --> 00:00:51,500
那么这个id呢其实就是contest.request.params.id

18
00:00:51,500 --> 00:00:57,500
好 那么呢我们来把它给打印出来看一下

19
00:00:57,500 --> 00:00:58,460
给打印出来看一下

20
00:00:58,460 --> 00:01:00,460
这个id或许我们前面是不是讲过呀

21
00:01:00,460 --> 00:01:01,840
跟query一起讲的

22
00:01:01,840 --> 00:01:04,140
所以说呢如果说有遗忘的同学去看一下前面的内容

23
00:01:04,140 --> 00:01:06,440
好 我们来把它给打印出来看一下

24
00:01:06,440 --> 00:01:10,540
那么我们要打印它是不是首先得访问咱们的浏览器呀

25
00:01:10,540 --> 00:01:14,240
好 比如说我们首先到浏览器里面

26
00:01:14,240 --> 00:01:17,240
咱们去随便随便输入一个id 加速

27
00:01:17,240 --> 00:01:19,460
大家可以看到

28
00:01:19,460 --> 00:01:21,460
cannot write properly id of id find

29
00:01:21,460 --> 00:01:28,980
好 其实这里呢我们是不需要request

30
00:01:28,980 --> 00:01:30,200
我们直接通过context的点

31
00:01:30,200 --> 00:01:32,220
paloms的ID就可以访问到我们的一个ID了

32
00:01:32,220 --> 00:01:33,040
好 那么我们再来看一下

33
00:01:33,040 --> 00:01:35,760
好 刷新 走里

34
00:01:35,760 --> 00:01:38,080
大家可以看到我们是不是已经获取到了我们的一个ID

35
00:01:38,080 --> 00:01:41,020
所以说呢 接下来我们只需要去查询就可以了吧

36
00:01:41,020 --> 00:01:43,800
那么查询是不是还是通过我们刚才的什么样

37
00:01:43,800 --> 00:01:45,180
是不是mos post的点

38
00:01:45,180 --> 00:01:46,680
发音单我们只需要去传入一个参数

39
00:01:46,680 --> 00:01:49,100
那么前面我们之所以没有去介绍怎么样去

40
00:01:49,100 --> 00:01:50,940
使用我们的一个增长感染的API

41
00:01:50,940 --> 00:01:52,900
其实它太简单了一看就知道怎么用

42
00:01:52,900 --> 00:01:54,580
所以说呢我前面就没有去讲

43
00:01:54,580 --> 00:01:56,960
那么这里下号线ID我们通过ID去查

44
00:01:56,960 --> 00:01:59,280
那么有的同学可能会问了

45
00:01:59,280 --> 00:02:01,580
那么它的ID从哪里来来

46
00:02:01,580 --> 00:02:03,400
我们在schema里面是不是没有定义这样一个ID

47
00:02:03,400 --> 00:02:04,840
其实它呢是我们mongos

48
00:02:04,840 --> 00:02:07,780
mongos里面它去自动的帮我们去生成一个索引

49
00:02:07,780 --> 00:02:09,620
那么具体是怎么回事

50
00:02:09,620 --> 00:02:10,600
大家可以去看文档

51
00:02:10,600 --> 00:02:12,500
因为我们一般在数据库里面建表的时候

52
00:02:12,500 --> 00:02:13,860
你要么去自己指定索引

53
00:02:13,860 --> 00:02:15,960
要么呢它呢会自动的帮你去建索引

54
00:02:15,960 --> 00:02:18,200
所以说呢如果说对这块有兴趣的同学可以去看一下文档

55
00:02:18,200 --> 00:02:19,660
那么这里的ID呢就是自动生成了

56
00:02:19,660 --> 00:02:21,100
所以我们就用它自己自动生成的

57
00:02:21,100 --> 00:02:23,360
因为现在还没有咱们一些定制化的需求

58
00:02:23,360 --> 00:02:25,060
所以我们这里呢ID传入什么呢

59
00:02:25,060 --> 00:02:26,280
是不是传入我们的一个postsID

60
00:02:26,280 --> 00:02:27,040
好

61
00:02:27,040 --> 00:02:28,960
那么咱们获取了readbounce之后

62
00:02:28,960 --> 00:02:29,580
接下来做什么

63
00:02:29,580 --> 00:02:30,700
是不是还是一样的

64
00:02:30,700 --> 00:02:33,660
我们去通过success去调用我们的ID

65
00:02:33,660 --> 00:02:33,940
好

66
00:02:33,940 --> 00:02:36,120
那么我们接下来就来真正的去查一条数据看一下

67
00:02:36,120 --> 00:02:37,880
那么ID它很复杂吧

68
00:02:37,880 --> 00:02:39,360
那么我们一定要去数据库里面去读

69
00:02:39,360 --> 00:02:40,680
我们来随便复制一条

70
00:02:40,680 --> 00:02:41,100
好

71
00:02:41,100 --> 00:02:42,220
进入咱们的一个浏览器

72
00:02:42,220 --> 00:02:43,580
好

73
00:02:43,580 --> 00:02:44,500
走

74
00:02:44,500 --> 00:02:46,200
大家可以看到我们是不是查出了

75
00:02:46,200 --> 00:02:47,960
在那条数据是不是非常好用

76
00:02:47,960 --> 00:02:48,740
messageokcode0

77
00:02:48,740 --> 00:02:51,640
然后接下来的数组里面呢有一条我们需要说查询的数据

78
00:02:51,640 --> 00:02:54,920
好 那么这里呢不知道大家有没有注意到一个问题

79
00:02:54,920 --> 00:02:56,620
我们是不是还是没有去写trycatch

80
00:02:56,620 --> 00:02:58,620
所以说呢 我们呢把它给

81
00:02:58,620 --> 00:03:01,340
进行一下咱们一个trycatch

82
00:03:01,340 --> 00:03:05,780
因为我们所有的数据和查询操作你都要在里面去写

83
00:03:05,780 --> 00:03:06,560
因为防止咱们

84
00:03:06,560 --> 00:03:08,740
出现一些不必要的错误

85
00:03:08,740 --> 00:03:10,100
把咱们的一个服务搞怪了

86
00:03:10,100 --> 00:03:11,280
那就都尝试

87
00:03:11,280 --> 00:03:12,080
好

88
00:03:12,080 --> 00:03:13,500
avit

89
00:03:13,500 --> 00:03:15,080
这叫copy吧

90
00:03:15,080 --> 00:03:17,660
就是咱们代码写多了其实都是copy

91
00:03:17,660 --> 00:03:21,140
后端其实也没有什么寄出含量

92
00:03:21,140 --> 00:03:22,860
因为如果说你去专门去写一些crude

93
00:03:22,860 --> 00:03:24,620
常年累月的都是一些业务

94
00:03:24,620 --> 00:03:25,900
那么如果说你去做一些高频发

95
00:03:25,900 --> 00:03:27,060
当然还是有一些难度的

96
00:03:27,060 --> 00:03:29,880
好我们现在进行了一个tack catch

97
00:03:29,880 --> 00:03:31,780
那么其实大家有没有考虑一种情况

98
00:03:31,780 --> 00:03:32,980
如果说我id

99
00:03:32,980 --> 00:03:36,360
如果说id瞎传会发生一件什么事情

100
00:03:36,360 --> 00:03:37,280
大家猜一下

101
00:03:37,280 --> 00:03:38,140
走理

102
00:03:38,140 --> 00:03:39,260
大家看到没有

103
00:03:39,260 --> 00:03:42,520
因为我们是不是已经做了一个通用的错误逻辑

104
00:03:42,520 --> 00:03:43,200
所以说呢

105
00:03:43,200 --> 00:03:46,080
它呢可以在我们屏幕上面去给我们返回一些错误信息

106
00:03:46,080 --> 00:03:46,840
这里大家可能看不到

107
00:03:46,840 --> 00:03:47,780
我来把它放大一点

108
00:03:47,780 --> 00:03:52,680
因为我们这里的ID那是瞎数的

109
00:03:52,680 --> 00:03:54,720
所存的数据库里面肯定没有这样一条数据

110
00:03:54,720 --> 00:03:56,760
那么我们来看一下错误会返回什么

111
00:03:56,760 --> 00:03:59,020
首先message for失败

112
00:03:59,020 --> 00:04:00,600
说明我们是不是读取失败

113
00:04:00,600 --> 00:04:02,000
那么这里的data里面返回什么

114
00:04:02,000 --> 00:04:04,080
message大家重点看这样一句话

115
00:04:04,080 --> 00:04:05,540
value什么什么什么

116
00:04:05,540 --> 00:04:06,820
art我们什么的什么什么

117
00:04:06,820 --> 00:04:07,680
说明一个什么问题

118
00:04:07,680 --> 00:04:10,060
是不是说明我们没有查询到这样一条数据

119
00:04:10,060 --> 00:04:12,940
所以说我们全开启是不是还是有用的

120
00:04:12,940 --> 00:04:13,900
好

121
00:04:13,900 --> 00:04:16,400
那么这里就是我们这节课的内容

122
00:04:16,400 --> 00:04:17,600
我们来总结一下

123
00:04:17,600 --> 00:04:19,560
我们怎么样去做我们单个数据的一个查询

124
00:04:19,560 --> 00:04:20,660
首先呢

125
00:04:20,660 --> 00:04:22,160
我们是不是要获取我们的文章ID

126
00:04:22,160 --> 00:04:22,560
通过什么

127
00:04:22,560 --> 00:04:23,820
是不是contestparampt.id

128
00:04:23,820 --> 00:04:24,540
那么呢

129
00:04:24,540 --> 00:04:25,900
查咱们获取了ID之后

130
00:04:25,900 --> 00:04:26,860
通过咱们的model的

131
00:04:26,860 --> 00:04:27,580
是不是find的方法

132
00:04:27,580 --> 00:04:28,820
然后传入我们的一个查询条件

133
00:04:28,820 --> 00:04:30,460
比如说我们要通过ID去查

134
00:04:30,460 --> 00:04:30,820
当然呢

135
00:04:30,820 --> 00:04:32,240
你也可以通过title和content去查

136
00:04:32,240 --> 00:04:33,460
但是在我们实际的项目中

137
00:04:33,460 --> 00:04:35,460
一般前端传立给后台的一般都是ID

138
00:04:35,460 --> 00:04:35,760
没有

139
00:04:35,760 --> 00:04:38,640
基本上很少有情况去通过文章的标题

140
00:04:38,640 --> 00:04:39,560
或者说是内容去查

141
00:04:39,560 --> 00:04:40,800
一般那样是属于模糊查询

142
00:04:40,800 --> 00:04:57,100
所以我们就先把视频停一下

