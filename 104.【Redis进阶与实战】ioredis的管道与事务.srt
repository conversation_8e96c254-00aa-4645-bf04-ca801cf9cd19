1
00:00:00,000 --> 00:00:02,200
好 这节课我们就来看一下

2
00:00:02,200 --> 00:00:04,540
咱们IoRedis

3
00:00:04,540 --> 00:00:08,040
在操作Redis过程中 我们需要去经常使用的两个东西

4
00:00:08,040 --> 00:00:09,940
一个是Pipeline 还有一个是咱们的事物

5
00:00:09,940 --> 00:00:11,920
那么什么是Pipeline呢 也就是管道

6
00:00:11,920 --> 00:00:15,220
它可以做什么呢 是不是避免我们前面提到的一个往返食言

7
00:00:15,220 --> 00:00:17,040
什么是往返食言 同学们还记得吗

8
00:00:17,040 --> 00:00:19,580
这样一幅图 对吧 大家看到这样两幅图

9
00:00:19,580 --> 00:00:21,360
是不是就可以了解到咱们什么是往返食言呢

10
00:00:21,360 --> 00:00:24,080
你比如说我们一条命令 像服务器发送的时候

11
00:00:24,080 --> 00:00:25,440
服务器是不是要和我们建立一层连接

12
00:00:25,440 --> 00:00:26,920
那么发送三条是不是建立三次连接

13
00:00:26,920 --> 00:00:28,820
那么我们能不能一口气把所有的命令

14
00:00:28,820 --> 00:00:30,520
全部推给咱们的Redis服务器

15
00:00:30,520 --> 00:00:32,400
它所执行完了之后再给我们绑结果呢

16
00:00:32,400 --> 00:00:33,060
其实是可以了

17
00:00:33,060 --> 00:00:35,500
所以我们就需要去借助PipeLine

18
00:00:35,500 --> 00:00:36,800
这样一个东西

19
00:00:36,800 --> 00:00:38,600
那么PipeLine它可以怎么样去使用呢

20
00:00:38,600 --> 00:00:39,700
其实使用很简单

21
00:00:39,700 --> 00:00:42,540
比如说我们去调用咱们Redis的PipeLine方法

22
00:00:42,540 --> 00:00:43,560
可以返回一个PipeLine对象

23
00:00:43,560 --> 00:00:45,540
它可以去比如说你去SetDeal

24
00:00:45,540 --> 00:00:47,160
这里都是咱们多条命令吧

25
00:00:47,160 --> 00:00:47,860
可以合成一个

26
00:00:47,860 --> 00:00:50,440
你执行完之后去调用ExEC就可以了

27
00:00:50,440 --> 00:00:51,840
是不是和我们的事物有点类似

28
00:00:51,840 --> 00:00:55,180
包括你不仅可以通过咱们一行一行的调用

29
00:00:55,180 --> 00:00:56,400
而且你还可以通过

30
00:00:56,400 --> 00:00:57,560
链式调用是不是类似解query

31
00:00:57,560 --> 00:00:59,720
而且你还可以使用promise

32
00:00:59,720 --> 00:01:00,160
什么意思

33
00:01:00,160 --> 00:01:00,960
你比如说

34
00:01:00,960 --> 00:01:03,180
我们去执行完了setget

35
00:01:03,180 --> 00:01:04,040
因为咱们操作radis

36
00:01:04,040 --> 00:01:04,860
随库是不是需要时间

37
00:01:04,860 --> 00:01:05,600
它是一个异步的过程

38
00:01:05,600 --> 00:01:06,920
你如果说

39
00:01:06,920 --> 00:01:08,220
希望咱们pipeline

40
00:01:08,220 --> 00:01:09,240
刚好执行完成之后

41
00:01:09,240 --> 00:01:09,760
去做一些事情

42
00:01:09,760 --> 00:01:10,700
我们就可能可以了

43
00:01:10,700 --> 00:01:11,340
去调用promise

44
00:01:11,340 --> 00:01:12,140
演证这样一个方法

45
00:01:12,140 --> 00:01:12,560
好

46
00:01:12,560 --> 00:01:13,380
那么这里呢

47
00:01:13,380 --> 00:01:15,080
我们就来操作一下pipeline

48
00:01:15,080 --> 00:01:15,680
它的使用

49
00:01:15,680 --> 00:01:17,560
好

50
00:01:17,560 --> 00:01:17,980
这里呢

51
00:01:17,980 --> 00:01:20,000
我们先把代表注释一下

52
00:01:25,180 --> 00:01:29,400
首先

53
00:01:29,400 --> 00:01:31,580
首先

54
00:01:31,580 --> 00:01:33,900
我们是不是要实力化一个pipeline对象

55
00:01:33,900 --> 00:01:34,740
pipeline

56
00:01:34,740 --> 00:01:35,600
等于

57
00:01:35,600 --> 00:01:36,580
等于什么

58
00:01:36,580 --> 00:01:36,840
是不是

59
00:01:36,840 --> 00:01:39,220
redis

60
00:01:39,220 --> 00:01:42,300
pipeline

61
00:01:42,300 --> 00:01:42,740
好

62
00:01:42,740 --> 00:01:45,500
我们这里来通过链式调用来感受一下

63
00:01:45,500 --> 00:01:46,740
比如说我们去pipeline一点

64
00:01:46,740 --> 00:01:48,540
比如说我们去set

65
00:01:48,540 --> 00:01:50,520
set

66
00:01:50,520 --> 00:01:51,620
hello

67
00:01:51,620 --> 00:01:53,420
word

68
00:01:53,420 --> 00:01:55,360
我们再去设一个

69
00:01:55,360 --> 00:01:56,420
什么呢

70
00:01:56,420 --> 00:01:57,560
你好

71
00:01:57,560 --> 00:01:59,020
你好什么呢

72
00:01:59,020 --> 00:01:59,460
你好

73
00:01:59,460 --> 00:02:02,240
我们不执行多

74
00:02:02,240 --> 00:02:03,600
只是感受一下他的用法

75
00:02:03,600 --> 00:02:04,280
然后结束

76
00:02:04,280 --> 00:02:04,740
结束是什么

77
00:02:04,740 --> 00:02:05,540
是不是exec

78
00:02:05,540 --> 00:02:09,060
exec

79
00:02:09,060 --> 00:02:11,700
这里我们其实就已经执行完了

80
00:02:11,700 --> 00:02:12,880
我们来看一下

81
00:02:12,880 --> 00:02:14,340
这里我们就来看一下

82
00:02:14,340 --> 00:02:15,620
是不是这么回事

83
00:02:15,620 --> 00:02:17,760
走

84
00:02:17,760 --> 00:02:20,580
我们来积极库里面去看一下

85
00:02:20,580 --> 00:02:22,060
刷新

86
00:02:22,060 --> 00:02:22,900
好大家可以看到

87
00:02:22,900 --> 00:02:24,020
咱们的hello和你好

88
00:02:24,020 --> 00:02:25,340
是不是都已经设置成功了呀

89
00:02:25,340 --> 00:02:26,560
hello world 你好 China

90
00:02:26,560 --> 00:02:28,480
好这里呢就是我们pipeline

91
00:02:28,480 --> 00:02:29,220
它有用法

92
00:02:29,220 --> 00:02:30,420
它有用法

93
00:02:30,420 --> 00:02:31,520
这里接下来我们

94
00:02:31,520 --> 00:02:34,500
pipeline它还有另外一种调用方式

95
00:02:34,500 --> 00:02:36,760
你可以通过宿主的形式去调用

96
00:02:36,760 --> 00:02:39,480
比如说你去加入一些集合

97
00:02:39,480 --> 00:02:40,460
把复网和bar

98
00:02:40,460 --> 00:02:42,100
首先呢加入一个set的集合里面去

99
00:02:42,100 --> 00:02:46,420
包括呢你还可以去进行一些get的方法

100
00:02:46,420 --> 00:02:47,340
所以呢这里呢

101
00:02:47,340 --> 00:02:48,200
它只是一个语法堂而已

102
00:02:48,200 --> 00:02:50,220
所以这里呢我就不去做过多的介绍了

103
00:02:50,220 --> 00:02:50,860
接下来我们来看一下

104
00:02:50,860 --> 00:02:56,240
咱们的这样一个酷 他是怎么样去解决事物的问题的 其实也很简单 是不是和我们拍不拿也非常类似啊

105
00:02:56,240 --> 00:02:57,600
 比如说我们去redis点

106
00:02:57,600 --> 00:03:03,360
不跳不跳 是不是代表咱们事物开始 然后去set 然后get 然后去退出 好 这里呢

107
00:03:03,360 --> 00:03:04,540
 我们就来看一下

108
00:03:04,540 --> 00:03:08,320
在redis里面 它的事物是怎么去操作 我们来简单的写个例子

109
00:03:08,320 --> 00:03:16,460
好 比如说我们调用一个redis点

110
00:03:17,060 --> 00:03:21,020
5t2是不是代表什么呢 试试开始我们去set

111
00:03:21,020 --> 00:03:27,300
比如说我们去设定一个151给他一个字AOA然后去设定一个

112
00:03:27,300 --> 00:03:32,420
152给他一个字BBB

113
00:03:32,420 --> 00:03:37,280
然后呢我们去调整他的结束反数EXEC好结束那么我们来看一下效果

114
00:03:37,280 --> 00:03:45,220
刷新好大家看到我们的试试1和2他都已经设置成功了那么我们来

115
00:03:45,980 --> 00:03:46,740
测试一下

116
00:03:46,740 --> 00:03:48,800
假如说我们这里给一个不存在的只能瞎给

117
00:03:48,800 --> 00:03:50,080
好 我们在第二个给

118
00:03:50,080 --> 00:03:51,360
我们在第二个我们瞎给

119
00:03:51,360 --> 00:03:53,140
这里肯定是会报错

120
00:03:53,140 --> 00:03:55,700
所以我们来看一下我们的事物一到底设置值成功了没有

121
00:03:55,700 --> 00:03:56,980
我们现在不能给一吧

122
00:03:56,980 --> 00:03:57,760
我们给三合适

123
00:03:57,760 --> 00:03:59,300
因为刚才一我们是不是已经使用了

124
00:03:59,300 --> 00:04:00,060
给三合适

125
00:04:00,060 --> 00:04:02,100
我们来看一下这样一串逻辑他能不能走通

126
00:04:02,100 --> 00:04:04,660
走

127
00:04:04,660 --> 00:04:06,460
大家可以看到我们这里呢其实已经

128
00:04:06,460 --> 00:04:07,480
其实已经报错了

129
00:04:07,480 --> 00:04:09,280
asd asd is not defined

130
00:04:09,280 --> 00:04:11,580
我们来看一下数据库里面他有没有去存储

131
00:04:11,580 --> 00:04:12,340
有没有去存储

132
00:04:12,340 --> 00:04:14,900
大家可以看到是不是都没有执行成功啊

133
00:04:15,160 --> 00:04:18,360
事物三合四 这一颗k 它都是没有给存储进去的

134
00:04:18,360 --> 00:04:21,660
这里呢 就是radis里面 它的一个事物

135
00:04:21,660 --> 00:04:25,660
我们这里来简单总结一下 其实这一颗讲解内容是非常简单的

136
00:04:25,660 --> 00:04:29,160
我们是不是讲解了我们的一个管道和什么 和事物

137
00:04:29,160 --> 00:04:31,360
好 那么管道

138
00:04:31,360 --> 00:04:32,660
它

139
00:04:32,660 --> 00:04:34,660
怎么使用 是不是

140
00:04:34,660 --> 00:04:36,360
radis点

141
00:04:36,360 --> 00:04:38,660
radis点

142
00:04:38,660 --> 00:04:40,460
拍不来呀

143
00:04:40,460 --> 00:04:44,060
好 而且呢 结束之后 要干嘛 你是不是执行一些操作

144
00:04:44,060 --> 00:04:46,620
执行式操作然后调用exec对吧

145
00:04:46,620 --> 00:04:47,140
好

146
00:04:47,140 --> 00:04:48,660
那么我们的事物是不同样的

147
00:04:48,660 --> 00:04:49,440
我们的事物

148
00:04:49,440 --> 00:04:51,220
你redis是不是调用什么

149
00:04:51,220 --> 00:04:54,040
母T

150
00:04:54,040 --> 00:04:57,620
然后再去调用咱们exec

151
00:04:57,620 --> 00:04:59,160
这里就是咱们通过logs

152
00:04:59,160 --> 00:05:00,960
被一些酷去操作redis

153
00:05:00,960 --> 00:05:04,280
其实它可以很大程度上去减少我们的一些api的操作

154
00:05:04,280 --> 00:05:04,800
非常的精简

155
00:05:04,800 --> 00:05:05,560
比我们命令行

156
00:05:05,560 --> 00:05:08,900
比如说你去命令行去操作一个什么set或者hset方便很多

157
00:05:08,900 --> 00:05:10,420
好这里就是我们

158
00:05:10,420 --> 00:05:12,480
第二章的全部内容

159
00:05:12,980 --> 00:05:14,420
好我们这期课就先到这里

