1
00:00:00,000 --> 00:00:02,580
好,这一节课我们就来看一下Redis进阶里面

2
00:00:02,580 --> 00:00:08,200
最后的一部分内容,我们会讲到性能管道和怎么样去节省空间

3
00:00:08,200 --> 00:00:09,720
都是一些比较概念性的东西

4
00:00:09,720 --> 00:00:13,500
那么这节课讲完了,我们就会去进入咱们node.js的一个实战

5
00:00:13,500 --> 00:00:14,620
首先我们来看一下性能

6
00:00:14,620 --> 00:00:18,400
你比如说我们刚才的Socket是不是很复杂

7
00:00:18,400 --> 00:00:20,240
它也是Redis最复杂的命令之一

8
00:00:20,240 --> 00:00:22,320
你如果用了不好就会去造成性能的瓶颈

9
00:00:22,320 --> 00:00:24,700
比如说Socket命令它这个复杂度是多少呢

10
00:00:24,700 --> 00:00:26,420
是On加上mlogm

11
00:00:26,420 --> 00:00:27,220
log

12
00:00:27,220 --> 00:00:28,520
log

13
00:00:28,520 --> 00:00:30,100
m乘以logm

14
00:00:30,100 --> 00:00:32,680
log我们读高中的时候都已经学过

15
00:00:32,680 --> 00:00:34,660
它的复杂度是不是小于m的平方

16
00:00:34,660 --> 00:00:35,300
而大于m

17
00:00:35,300 --> 00:00:37,460
它属于m和m的平方之间

18
00:00:37,460 --> 00:00:40,240
那么所以m它比较容易成为我们的一个性能瓶颈

19
00:00:40,240 --> 00:00:41,920
那么这里的n就代表

20
00:00:41,920 --> 00:00:43,820
我们排序列表的一个长度

21
00:00:43,820 --> 00:00:44,640
你比如说你一个

22
00:00:44,640 --> 00:00:46,260
一个列表它的长度是1000

23
00:00:46,260 --> 00:00:48,880
但是你只需要去对它前100名进行排序排名

24
00:00:48,880 --> 00:00:51,260
那么m就代表你需要进行排名的个数

25
00:00:51,260 --> 00:00:53,400
所以我们怎么样去

26
00:00:53,400 --> 00:00:55,440
怎么样去提升我们的性能呢

27
00:00:55,440 --> 00:00:56,900
比如说你可以尽量的去减少

28
00:00:56,900 --> 00:01:02,840
你可以去尽量的减少你需要排序元素的一个个数

29
00:01:02,840 --> 00:01:03,880
什么意思

30
00:01:03,880 --> 00:01:07,780
你比如说你去对我们学生成绩去进行排序的时候

31
00:01:07,780 --> 00:01:13,020
你对咱们全校的学生进行排序

32
00:01:13,020 --> 00:01:15,660
你可以尽量的去减少我们需要排序的数量

33
00:01:15,660 --> 00:01:18,140
而且第二个你可以利用咱们的一个粒米的参数

34
00:01:18,140 --> 00:01:19,080
去使m尽量的小

35
00:01:19,080 --> 00:01:19,400
什么意思

36
00:01:19,400 --> 00:01:20,820
你比如说你学校有1000个人

37
00:01:20,820 --> 00:01:22,940
但是你不可能说

38
00:01:22,940 --> 00:01:25,600
你需要把1000个人的排名全部列出来

39
00:01:25,600 --> 00:01:27,140
因为成绩好的同学还好

40
00:01:27,140 --> 00:01:28,660
成绩不好的同学是不是就比较的侮辱

41
00:01:28,660 --> 00:01:30,220
所以你可以利用nebit参数

42
00:01:30,220 --> 00:01:31,660
你只把全球的前十给排出来

43
00:01:31,660 --> 00:01:33,120
这样可以降低它的一个复杂度

44
00:01:33,120 --> 00:01:35,280
而且如果说你排序的数量比较大

45
00:01:35,280 --> 00:01:38,280
你能可以通过我们的缓存来解决这些问题

46
00:01:38,280 --> 00:01:39,340
就和我们的算法类似

47
00:01:39,340 --> 00:01:41,620
我们可以用内存去换时间

48
00:01:41,620 --> 00:01:42,460
好

49
00:01:42,460 --> 00:01:44,080
再来看一下第二个概念

50
00:01:44,080 --> 00:01:44,600
管道

51
00:01:44,600 --> 00:01:45,820
什么是管道呢

52
00:01:45,820 --> 00:01:47,120
我们之前在读取

53
00:01:47,120 --> 00:01:49,740
通过hgetware去读取我们数据的时候

54
00:01:49,740 --> 00:01:50,360
是不是讲到

55
00:01:50,360 --> 00:01:51,820
我们如果说用货血管去读

56
00:01:51,820 --> 00:01:53,560
它一直会有一个往返时延

57
00:01:53,560 --> 00:01:54,140
为什么呀

58
00:01:54,140 --> 00:01:56,760
因为我们的客户端和Redis进行连接

59
00:01:56,760 --> 00:01:57,520
是通过什么协议

60
00:01:57,520 --> 00:01:58,460
是不是通过TCP呀

61
00:01:58,460 --> 00:01:59,240
为什么呀

62
00:01:59,240 --> 00:02:00,880
因为比如说我们浏览区去访问服务器

63
00:02:00,880 --> 00:02:03,820
那么我们Redis其实可能也存储在另外一个服务器

64
00:02:03,820 --> 00:02:04,760
那么他们之间的通信

65
00:02:04,760 --> 00:02:06,620
其实也要基于咱们TCP的一个连接协议

66
00:02:06,620 --> 00:02:08,000
TCP同学们还记得吧

67
00:02:08,000 --> 00:02:09,740
它是不是需要去进行三次的一个握手

68
00:02:09,740 --> 00:02:11,780
那么每次握手都是需要去时间的

69
00:02:11,780 --> 00:02:14,160
那么你如果说去执行一条命令

70
00:02:14,160 --> 00:02:14,860
他们是不是就去

71
00:02:14,860 --> 00:02:16,880
就会需要去进行一次通信了

72
00:02:16,880 --> 00:02:19,480
那么我们可以通过下面的两幅图来理解一下

73
00:02:19,480 --> 00:02:20,040
你比如说

74
00:02:21,820 --> 00:02:23,360
我们去读取一条post1的title

75
00:02:23,360 --> 00:02:24,640
我们去读取

76
00:02:24,640 --> 00:02:27,200
读取一条返回是吧我们扣端向

77
00:02:27,200 --> 00:02:29,240
我们的扣端

78
00:02:29,240 --> 00:02:31,540
扣端向辅端去读取

79
00:02:31,540 --> 00:02:33,080
这这边是咱们的

80
00:02:33,080 --> 00:02:35,140
扣端

81
00:02:35,140 --> 00:02:36,420
这边是我们的

82
00:02:36,420 --> 00:02:38,460
辅端

83
00:02:38,460 --> 00:02:40,000
我们扣端读一次

84
00:02:40,000 --> 00:02:41,540
辅端是不是要返回一次

85
00:02:41,540 --> 00:02:42,040
读一次

86
00:02:42,040 --> 00:02:46,140
那么我们每次扣端和辅端去通信是不是都需要去经历TCP了

87
00:02:46,140 --> 00:02:48,440
三次握手和几次挥手是不是四次挥手

88
00:02:48,440 --> 00:02:51,000
那么每次都要进行这样一个过程是不是很浪费时间

89
00:02:51,000 --> 00:02:52,020
性能肯定会有影响吧

90
00:02:52,020 --> 00:02:54,080
那么我们就可以利用什么呢

91
00:02:54,080 --> 00:02:55,360
在radis它的底层

92
00:02:55,360 --> 00:02:58,160
通信协议里面对管道也就是pipeline提供的支持

93
00:02:58,160 --> 00:03:00,980
我们可以一次性的比如说

94
00:03:00,980 --> 00:03:04,060
我们可以一次性的把三条命令全部发送过去然后呢

95
00:03:04,060 --> 00:03:04,560
radis

96
00:03:04,560 --> 00:03:06,880
服务器再返回到我们的扣端把结果一次性返回

97
00:03:06,880 --> 00:03:09,680
这里呢是不是有点类似于我们http里面的一个什么概念

98
00:03:09,680 --> 00:03:10,720
是不是多路服用

99
00:03:10,720 --> 00:03:11,480
对吧

100
00:03:11,480 --> 00:03:15,060
好这里的radis其实也是对管道提供支持有兴趣的同学可以了

101
00:03:15,060 --> 00:03:17,120
下去了去了解一下

102
00:03:17,120 --> 00:03:18,400
pipeline这个概念

103
00:03:18,640 --> 00:03:21,840
好我们这里再来看一下radis它是怎么样去节省空间的

104
00:03:21,840 --> 00:03:24,920
比如说我们radis作为一个内存型数据库

105
00:03:24,920 --> 00:03:26,120
它的节省空间

106
00:03:26,120 --> 00:03:28,480
更重要也非常重要

107
00:03:28,480 --> 00:03:29,760
因为我们的内存是非常有限的

108
00:03:29,760 --> 00:03:33,640
我们的我们个个人电脑里面内存比较大的16G32G已经非常大了

109
00:03:33,640 --> 00:03:35,040
但是硬盘呢可以达到多少

110
00:03:35,040 --> 00:03:36,880
可能得到一T两T几十T都有可能

111
00:03:36,880 --> 00:03:38,560
但是你的内存是非常的昂贵的

112
00:03:38,560 --> 00:03:41,880
那么所以说我们需要去学会怎么去精简简直和鉴名

113
00:03:41,880 --> 00:03:46,240
你比如说你比如说我们有一个鉴名叫做vv very important people

114
00:03:46,840 --> 00:03:48,800
2020代表ID也就是说你的一个重要客户

115
00:03:48,800 --> 00:03:49,460
他的ID为20

116
00:03:49,460 --> 00:03:51,400
我们是不是可以改成VIP20

117
00:03:51,400 --> 00:03:52,620
这里呢我们读到VIP

118
00:03:52,620 --> 00:03:54,320
其实也知道你是一个非常重要的客户

119
00:03:54,320 --> 00:03:56,040
但是你要把握好尺度

120
00:03:56,040 --> 00:03:57,080
什么意思

121
00:03:57,080 --> 00:03:57,900
你能不能改成

122
00:03:57,900 --> 00:04:00,060
你能不能将VIP20改为V20呢

123
00:04:00,060 --> 00:04:01,040
这样是不是好

124
00:04:01,040 --> 00:04:01,400
大家看到

125
00:04:01,400 --> 00:04:02,420
比如说你现在写了个V

126
00:04:02,420 --> 00:04:03,720
你知道V代表VIP

127
00:04:03,720 --> 00:04:04,380
如果说哪天

128
00:04:04,380 --> 00:04:05,220
我来给你维护代码

129
00:04:05,220 --> 00:04:05,740
我一看到V

130
00:04:05,740 --> 00:04:07,200
V20VVVV什么呢

131
00:04:07,200 --> 00:04:07,560
V是吧

132
00:04:07,560 --> 00:04:08,160
V是谁呀

133
00:04:08,160 --> 00:04:08,520
所以呢

134
00:04:08,520 --> 00:04:09,640
我们可以精简

135
00:04:09,640 --> 00:04:10,840
但是不要过量

136
00:04:10,840 --> 00:04:12,880
再比如说

137
00:04:12,880 --> 00:04:14,820
我们去存储咱们用户性别

138
00:04:14,820 --> 00:04:16,500
我们的用户性别有几种

139
00:04:16,500 --> 00:04:18,740
两种没有三种在泰国肯定有三种是吧

140
00:04:18,740 --> 00:04:20,400
好那么我们的取值是不是只有

141
00:04:20,400 --> 00:04:22,680
命或者非没有就是男和女

142
00:04:22,680 --> 00:04:24,860
这里呢通过这样一个字不算去存储

143
00:04:24,860 --> 00:04:25,700
是不是还是稍微有点大

144
00:04:25,700 --> 00:04:27,660
我们能不能把它改为m和f

145
00:04:27,660 --> 00:04:29,480
如果更极致一点之前是不是讲过

146
00:04:29,480 --> 00:04:31,880
我们是不是可以通过二进制的零和一来表示

147
00:04:31,880 --> 00:04:34,760
对吧好那么我们就来回归一下

148
00:04:34,760 --> 00:04:36,500
我们这节课的一个内容

149
00:04:36,500 --> 00:04:43,560
好我们刚才是不是讲到了我们的一个

150
00:04:43,560 --> 00:04:45,800
性能呢

151
00:04:45,800 --> 00:04:49,000
性能瓶颈sort是不是比较容易发生瓶颈怎么做

152
00:04:49,000 --> 00:04:50,100
是不是减小

153
00:04:50,100 --> 00:04:52,900
是不是减小我们

154
00:04:52,900 --> 00:04:56,900
需要排序的长度第二个

155
00:04:56,900 --> 00:04:59,300
通过limit

156
00:04:59,300 --> 00:05:00,900
是不是减少我们的返回值上

157
00:05:00,900 --> 00:05:02,500
也就是说你千个人排名

158
00:05:02,500 --> 00:05:03,700
你不可能全部排名吧

159
00:05:03,700 --> 00:05:04,800
你排前十就够了

160
00:05:04,800 --> 00:05:07,500
然后呢除了sort

161
00:05:07,500 --> 00:05:08,900
我们还讲过什么

162
00:05:08,900 --> 00:05:10,900
是不是还讲了一个管道啊

163
00:05:10,900 --> 00:05:12,100
管道

164
00:05:12,100 --> 00:05:14,200
管道它的重用是什么

165
00:05:14,200 --> 00:05:15,100
是不是将

166
00:05:15,800 --> 00:05:20,740
将命令一起执行并返回

167
00:05:20,740 --> 00:05:24,760
它是不是有点类似咱们http里面的一个多路服用和长链接

168
00:05:24,760 --> 00:05:25,280
它的一个概念

169
00:05:25,280 --> 00:05:25,840
对吧

170
00:05:25,840 --> 00:05:28,200
这里是redis里面的一个管道

171
00:05:28,200 --> 00:05:30,000
好

172
00:05:30,000 --> 00:05:30,700
我们再来看一下

173
00:05:30,700 --> 00:05:33,520
我们还可以去节省空间吗

174
00:05:33,520 --> 00:05:34,900
节省空间可以通过什么方式

175
00:05:34,900 --> 00:05:37,780
是不是咱们的命名

176
00:05:37,780 --> 00:05:40,580
命名是不是可以精简

177
00:05:40,580 --> 00:05:44,660
而且了数据存储更合理

178
00:05:44,660 --> 00:05:46,660
你比如说男和女可以通过0和1去存

179
00:05:46,660 --> 00:05:49,420
好这里就是我们redis进阶的内容

180
00:05:49,420 --> 00:05:50,980
那么接下来的课程我们就会去讲解

181
00:05:50,980 --> 00:05:53,500
loads它的一个实战到底我们怎么样去操作

182
00:05:53,500 --> 00:05:55,080
包括我们的性能管道节省空间

183
00:05:55,080 --> 00:05:57,020
在我们后面的实战都会去体现

184
00:05:57,020 --> 00:05:58,040
到底怎么样去玩他们

185
00:05:58,040 --> 00:05:59,920
好这里就是我们这节课的内容

