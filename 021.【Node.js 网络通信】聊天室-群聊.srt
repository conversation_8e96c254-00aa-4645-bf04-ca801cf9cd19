1
00:00:00,000 --> 00:00:04,560
接下来呢我们就可以来实现一下这个群聊功能

2
00:00:04,560 --> 00:00:07,220
那么当用户登陆成功以后呢

3
00:00:07,220 --> 00:00:08,300
我们这样就可以看到

4
00:00:08,300 --> 00:00:10,180
然后接下来

5
00:00:10,180 --> 00:00:12,060
我们让它return

6
00:00:12,060 --> 00:00:13,940
那么这个代码就不会往后执行了

7
00:00:13,940 --> 00:00:16,000
也就是说我们这里啊

8
00:00:16,000 --> 00:00:18,200
就是往后所有的代码都是这个聊天的

9
00:00:18,200 --> 00:00:20,020
这个小发送的一个处理

10
00:00:20,020 --> 00:00:21,440
所以说我们接下来呢

11
00:00:21,440 --> 00:00:24,560
我们在这我们就可以直接去client.write

12
00:00:24,560 --> 00:00:27,040
write这个叫decent.3in5

13
00:00:27,040 --> 00:00:28,900
然后发送一个对象

14
00:00:28,900 --> 00:00:30,920
那么这个消息的类型呢

15
00:00:30,920 --> 00:00:33,740
就是TAPS里面的这个叫做Broadcast

16
00:00:33,740 --> 00:00:35,520
然后这个Message呢

17
00:00:35,520 --> 00:00:36,160
就是这个Data

18
00:00:36,160 --> 00:00:38,440
那么这个就是群聊

19
00:00:38,440 --> 00:00:40,620
群聊的消息

20
00:00:40,620 --> 00:00:42,700
那么同样的对应我们的服务端

21
00:00:42,700 --> 00:00:44,900
那么服务端收到这个消息以后

22
00:00:44,900 --> 00:00:47,160
那么就匹配到这个TAPS Broadcast了

23
00:00:47,160 --> 00:00:48,980
他匹配到他以后

24
00:00:48,980 --> 00:00:50,060
那么服务端要干嘛呢

25
00:00:50,060 --> 00:00:51,320
服务端要把这个消息呢

26
00:00:51,320 --> 00:00:53,480
是不是要发送给所有的客户端呀

27
00:00:53,480 --> 00:00:54,980
所以说这个时候呢

28
00:00:54,980 --> 00:00:57,160
他要做的就是便利整个User

29
00:00:57,160 --> 00:00:59,080
就是users for each

30
00:00:59,080 --> 00:01:00,400
item

31
00:01:00,400 --> 00:01:02,200
然后在每便利一个

32
00:01:02,200 --> 00:01:04,220
然后就让item.writ

33
00:01:04,220 --> 00:01:06,240
然后每一个item是什么东西呢

34
00:01:06,240 --> 00:01:09,300
每个item那是不是就是我们这个client sort it

35
00:01:09,300 --> 00:01:11,560
然后我们在这个writ里面

36
00:01:11,560 --> 00:01:13,400
同理json.screen file

37
00:01:13,400 --> 00:01:18,340
然后这个type的类型是群聊

38
00:01:18,340 --> 00:01:20,880
就是taps.worldcast

39
00:01:20,880 --> 00:01:22,340
然后同样的

40
00:01:22,340 --> 00:01:24,420
然后这个nickname就表示说

41
00:01:24,420 --> 00:01:25,140
它来自于谁

42
00:01:25,140 --> 00:01:28,480
就是来自于我们这个client socket

43
00:01:28,480 --> 00:01:29,420
就是当前用户

44
00:01:29,420 --> 00:01:31,140
他的这个nickname就是昵称

45
00:01:31,140 --> 00:01:34,120
然后同样的包括一个叫做message

46
00:01:34,120 --> 00:01:35,400
message是什么呢

47
00:01:35,400 --> 00:01:38,320
就是我们当前这个data的message

48
00:01:38,320 --> 00:01:41,280
这个是群聊

49
00:01:41,280 --> 00:01:42,800
然后这个是当前用户的昵称

50
00:01:42,800 --> 00:01:44,740
client socket就是当前用户

51
00:01:44,740 --> 00:01:47,360
data.message就是这个数据里面的这个消息内容

52
00:01:47,360 --> 00:01:50,200
好那这样的话服务单就把消息给发过来了

53
00:01:50,200 --> 00:01:53,080
发过来以后客户单在这里出发这个data事情又接到

54
00:01:53,080 --> 00:01:54,780
接到以后客户单再判断

55
00:01:54,780 --> 00:01:55,720
如果是login的

56
00:01:55,720 --> 00:01:58,140
这里就是cast的处理

57
00:01:58,140 --> 00:01:59,440
那么在这里的话

58
00:01:59,440 --> 00:02:01,140
客户的要做的事非常简单

59
00:02:01,140 --> 00:02:04,100
我们就把它输出到当前这个控制台就可以了

60
00:02:04,100 --> 00:02:05,780
这个时候我们带着直接console.log

61
00:02:05,780 --> 00:02:08,160
console.log我们直接去

62
00:02:08,160 --> 00:02:10,140
date.nickname

63
00:02:10,140 --> 00:02:11,100
好了

64
00:02:11,100 --> 00:02:11,980
然后冒号

65
00:02:11,980 --> 00:02:13,440
然后加dollar括起来

66
00:02:13,440 --> 00:02:15,600
然后是date.message

67
00:02:15,600 --> 00:02:16,840
那么这个就是

68
00:02:16,840 --> 00:02:18,820
用户名

69
00:02:18,820 --> 00:02:20,380
然后他要说的话

70
00:02:20,380 --> 00:02:21,460
就是这个意思

71
00:02:21,460 --> 00:02:22,180
好

72
00:02:22,180 --> 00:02:23,700
那我们在这呢

73
00:02:23,700 --> 00:02:25,240
就把这个群聊这个代码

74
00:02:25,240 --> 00:02:26,560
基本上已经消了

75
00:02:26,560 --> 00:02:27,080
消了以后

76
00:02:27,080 --> 00:02:27,880
接下来回到控制台

77
00:02:27,880 --> 00:02:28,700
我们来测试一下

78
00:02:28,700 --> 00:02:30,640
那这个时候我们在这

79
00:02:30,640 --> 00:02:32,120
关闭关闭

80
00:02:32,120 --> 00:02:33,880
我们全部都来重启一下

81
00:02:33,880 --> 00:02:35,380
然后node client

82
00:02:35,380 --> 00:02:36,900
这个时候他输入Jack

83
00:02:36,900 --> 00:02:38,500
这个也一样的

84
00:02:38,500 --> 00:02:39,120
node client

85
00:02:39,120 --> 00:02:40,240
他来输入Mac

86
00:02:40,240 --> 00:02:41,300
我们举个例子

87
00:02:41,300 --> 00:02:42,640
例如Mac在这里输入hello

88
00:02:42,640 --> 00:02:43,260
然后一挥车

89
00:02:43,260 --> 00:02:44,540
我们大家就可以看到

90
00:02:44,540 --> 00:02:46,220
这里是不是也输出了Mac hello

91
00:02:46,220 --> 00:02:47,620
这里输出Mac hello

92
00:02:47,620 --> 00:02:48,940
例如Jack在这看到了

93
00:02:48,940 --> 00:02:49,580
这个群聊的消息

94
00:02:49,580 --> 00:02:50,780
Jack也输入一个hello

95
00:02:50,780 --> 00:02:51,520
一挥车

96
00:02:51,520 --> 00:02:53,280
我们就可以看到Jack在这说Hello

97
00:02:53,280 --> 00:02:54,720
然后这里Jack在这说Hello

98
00:02:54,720 --> 00:02:56,260
当然大家在这看到

99
00:02:56,260 --> 00:02:58,420
我在这里输的这个Hello这个消息

100
00:02:58,420 --> 00:02:59,140
它在这里

101
00:02:59,140 --> 00:03:01,240
这个Hello是我们用户输入的内容

102
00:03:01,240 --> 00:03:02,400
我们在终端当中

103
00:03:02,400 --> 00:03:03,840
就是说你输入的内容

104
00:03:03,840 --> 00:03:04,540
它可以停留在这里

105
00:03:04,540 --> 00:03:06,540
这个是群聊收到的这个消息

106
00:03:06,540 --> 00:03:09,320
所以说对于这个群聊的功能

107
00:03:09,320 --> 00:03:10,580
我们大家在这就看到

108
00:03:10,580 --> 00:03:11,900
就已经实现了

