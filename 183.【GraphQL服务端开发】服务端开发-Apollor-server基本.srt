1
00:00:00,000 --> 00:00:02,180
熟悉了ApolloServer的基本特征之后

2
00:00:02,180 --> 00:00:03,680
我们接下来详细的来分析一下

3
00:00:03,680 --> 00:00:05,900
如何基于ApolloServer来做这个服务端的接口开发

4
00:00:05,900 --> 00:00:09,040
首先我们就按照这个流程来创建一个服务端的项目

5
00:00:09,040 --> 00:00:10,040
具体我们这样来做

6
00:00:10,040 --> 00:00:12,020
给这项目提个名字

7
00:00:12,020 --> 00:00:14,020
Apollo-demo

8
00:00:14,020 --> 00:00:16,800
后续将冠的特征我们就在这个项目当中来进行测试

9
00:00:16,800 --> 00:00:18,240
首先我们创建一个入口文件

10
00:00:18,240 --> 00:00:20,640
那是Apollo-

11
00:00:20,640 --> 00:00:22,480
这是基本的开发步骤

12
00:00:22,480 --> 00:00:26,540
然后我们初始化一个package.getton

13
00:00:26,540 --> 00:00:27,320
这个项目的文件

14
00:00:27,320 --> 00:00:29,900
通过NPM-init-y

15
00:00:29,900 --> 00:00:32,640
那产生这个文件之后

16
00:00:32,640 --> 00:00:34,360
我们就可以把相关的依赖包安装一下

17
00:00:34,360 --> 00:00:36,640
放进来

18
00:00:36,640 --> 00:00:37,740
然后回车

19
00:00:37,740 --> 00:00:39,360
安装好这个包之后

20
00:00:39,360 --> 00:00:41,040
我们就可以做具体的代码开发了

21
00:00:41,040 --> 00:00:41,840
那在开发之前

22
00:00:41,840 --> 00:00:42,540
我们把这个流程

23
00:00:42,540 --> 00:00:43,620
再稍微的描述一下

24
00:00:43,620 --> 00:00:45,240
具体就是基于Apollo Server

25
00:00:45,240 --> 00:00:45,680
来做开发

26
00:00:45,680 --> 00:00:47,280
我们首先要导入相关的依赖包

27
00:00:47,280 --> 00:00:48,100
这是第一步

28
00:00:48,100 --> 00:00:51,280
就是导入相关的依赖包

29
00:00:51,280 --> 00:00:53,120
导入包之后

30
00:00:53,120 --> 00:00:56,000
我们要做的就是定义数据类型

31
00:00:56,000 --> 00:00:57,720
因为Graphcare2

32
00:00:57,720 --> 00:00:59,220
它提供了一套完善的类型系统

33
00:00:59,220 --> 00:01:29,200
然后进5步

34
00:01:29,220 --> 00:01:30,560
这要提供

35
00:01:30,560 --> 00:01:31,620
简清

36
00:01:31,620 --> 00:01:32,520
要兼信一个窗口

37
00:01:32,520 --> 00:01:33,620
然后就可以通过12地址

38
00:01:33,620 --> 00:01:35,020
在旁边后台的这个接口

39
00:01:35,020 --> 00:01:36,720
那首先我们来做第一步

40
00:01:36,720 --> 00:01:38,020
这里面导入的包

41
00:01:38,020 --> 00:01:39,020
我们首先要导入的是

42
00:01:39,020 --> 00:01:39,320
Epress

43
00:01:39,320 --> 00:01:40,720
通过require

44
00:01:40,720 --> 00:01:41,920
把它导进来

45
00:01:41,920 --> 00:01:44,120
然后是ApolloServer

46
00:01:44,120 --> 00:01:45,220
相关的这个函数

47
00:01:45,220 --> 00:01:46,520
这里面要结构复制一下

48
00:01:46,520 --> 00:01:47,420
我们要导入的是

49
00:01:47,420 --> 00:01:49,220
ApolloServer

50
00:01:49,220 --> 00:01:49,820
还有一个

51
00:01:49,820 --> 00:01:50,720
就是GraphKr

52
00:01:50,720 --> 00:01:51,520
这三个单词的缩解

53
00:01:51,520 --> 00:01:53,020
实际上这样的都是方法

54
00:01:53,020 --> 00:01:54,320
这通过require

55
00:01:54,320 --> 00:01:55,620
这个包名叫

56
00:01:55,620 --> 00:01:56,020
Apollo

57
00:01:56,020 --> 00:01:57,720
Server

58
00:01:57,720 --> 00:01:58,720
Epress

59
00:01:58,720 --> 00:02:00,620
好 现在的话我们就完成了导入

60
00:02:00,620 --> 00:02:02,420
那第二步就是做数据类型的定义

61
00:02:02,420 --> 00:02:04,160
这儿起个名字

62
00:02:04,160 --> 00:02:05,900
我们就叫拆仓EFS

63
00:02:05,900 --> 00:02:08,100
后边通过GraphQL

64
00:02:08,100 --> 00:02:08,920
如一这是一个函数

65
00:02:08,920 --> 00:02:10,100
后边如果加括号的话

66
00:02:10,100 --> 00:02:10,840
这就是函数交用

67
00:02:10,840 --> 00:02:12,460
这里边可以传一些参数

68
00:02:12,460 --> 00:02:12,960
比如说123

69
00:02:12,960 --> 00:02:14,960
但是我们这儿是不是这样写的

70
00:02:14,960 --> 00:02:15,940
怎么写呢

71
00:02:15,940 --> 00:02:19,120
是用的是ES6的标签模板

72
00:02:19,120 --> 00:02:21,340
通过翻译号的方式

73
00:02:21,340 --> 00:02:22,920
GraphQL这个方法传参

74
00:02:22,920 --> 00:02:24,100
为什么要这样做呢

75
00:02:24,100 --> 00:02:26,620
因为这里边可以传递更加复杂的参数

76
00:02:26,620 --> 00:02:27,820
这里边传入的参数

77
00:02:27,820 --> 00:02:28,700
其实就是类型的定义

78
00:02:28,700 --> 00:02:30,020
具体的预法是这样的

79
00:02:30,020 --> 00:02:30,880
通过Type关键字

80
00:02:30,880 --> 00:02:32,320
来定义一个query类型

81
00:02:32,320 --> 00:02:33,500
这个类型大中

82
00:02:33,500 --> 00:02:34,600
可以包含一些字段

83
00:02:34,600 --> 00:02:35,820
最简单的我们就是Hello

84
00:02:35,820 --> 00:02:37,440
后面给一个类型的约束

85
00:02:37,440 --> 00:02:38,520
这有个内置的类型

86
00:02:38,520 --> 00:02:39,000
叫Street

87
00:02:39,000 --> 00:02:40,940
这是GraphPR的内置类型

88
00:02:40,940 --> 00:02:41,960
这样的话

89
00:02:41,960 --> 00:02:43,020
我们就完成了一个类型的定义

90
00:02:43,020 --> 00:02:44,240
就是query类型

91
00:02:44,240 --> 00:02:45,460
关于query类型的话

92
00:02:45,460 --> 00:02:46,160
需要强调一下

93
00:02:46,160 --> 00:02:47,540
这注意

94
00:02:47,540 --> 00:02:53,620
query类型是默认客户端查询的类型

95
00:02:53,620 --> 00:02:55,460
也就是说客户端在查数据的时候

96
00:02:55,460 --> 00:02:56,800
会从query类型中进行查询

97
00:02:56,800 --> 00:02:58,200
当然它也可以包含别的类型

98
00:02:58,200 --> 00:02:58,960
这个我们后续再说

99
00:02:58,960 --> 00:03:00,380
这儿需要强调的就是什么

100
00:03:00,380 --> 00:03:03,860
并且该类型

101
00:03:03,860 --> 00:03:05,180
就是在服务端

102
00:03:05,180 --> 00:03:06,640
必须存在

103
00:03:06,640 --> 00:03:09,140
并且是唯一的

104
00:03:09,140 --> 00:03:11,560
也就是说这个类型

105
00:03:11,560 --> 00:03:12,280
不可以定义两次

106
00:03:12,280 --> 00:03:13,220
只能有一个

107
00:03:13,220 --> 00:03:14,640
这是关于类型的定义

108
00:03:14,640 --> 00:03:15,500
就这么多

109
00:03:15,500 --> 00:03:16,460
有了类型之后

110
00:03:16,460 --> 00:03:18,020
我们就可以给这类型提供数据了

111
00:03:18,020 --> 00:03:21,320
在下面再定义一个名字

112
00:03:21,320 --> 00:03:23,240
这名字我们就叫resolve

113
00:03:23,240 --> 00:03:24,700
resolve

114
00:03:24,700 --> 00:03:25,360
s

115
00:03:25,360 --> 00:03:26,700
后面是一个推向

116
00:03:26,700 --> 00:03:29,040
这里边这个类型名字要匹配上

117
00:03:29,040 --> 00:03:29,760
我们就叫query

118
00:03:29,760 --> 00:03:31,280
也是一个对象

119
00:03:31,280 --> 00:03:33,340
这里边要指定一下

120
00:03:33,340 --> 00:03:34,120
这个Hello这个属性

121
00:03:34,120 --> 00:03:34,960
它对应的数据是什么

122
00:03:34,960 --> 00:03:35,820
这个数据的话

123
00:03:35,820 --> 00:03:37,020
通过一个函数的方式来进行提供

124
00:03:37,020 --> 00:03:39,260
这儿我们就反复一个简单的Hello2

125
00:03:39,260 --> 00:03:41,980
这样的话就相当于

126
00:03:41,980 --> 00:03:43,420
把这个数据提供给了Hello

127
00:03:43,420 --> 00:03:44,940
这个具体的字段

128
00:03:44,940 --> 00:03:46,400
到此为止

129
00:03:46,400 --> 00:03:49,620
关于这个类型定义和这个数据解析

130
00:03:49,620 --> 00:03:50,140
我们就做完了

131
00:03:50,140 --> 00:03:52,680
然后第四步做这个整合操作

132
00:03:52,680 --> 00:03:53,540
整合的时候

133
00:03:53,540 --> 00:03:54,640
我们就要先产生一个

134
00:03:54,640 --> 00:03:56,800
GrabQR的server对象

135
00:03:56,800 --> 00:03:58,000
其实就是AppoloServer当中的

136
00:03:58,000 --> 00:03:58,940
对GrabQR的一个实现

137
00:03:58,940 --> 00:03:59,860
这个对象

138
00:03:59,860 --> 00:04:00,800
我们要实力化一个

139
00:04:00,800 --> 00:04:02,600
这是Server

140
00:04:02,600 --> 00:04:04,920
请你有AppoloServer

141
00:04:04,920 --> 00:04:06,340
并且把上两部

142
00:04:06,340 --> 00:04:07,420
准备好的

143
00:04:07,420 --> 00:04:09,140
一个是数据类型的定义

144
00:04:09,140 --> 00:04:09,860
放进来

145
00:04:09,860 --> 00:04:12,500
另外一个是Reserves

146
00:04:12,500 --> 00:04:15,480
所以这两个属性名是固定的

147
00:04:15,480 --> 00:04:16,380
是固定的

148
00:04:16,380 --> 00:04:17,860
这需要注意

149
00:04:17,860 --> 00:04:19,360
别注意一下

150
00:04:19,360 --> 00:04:20,200
就是type

151
00:04:20,200 --> 00:04:22,260
这个DFS和谁

152
00:04:22,260 --> 00:04:23,340
和这个Reserves

153
00:04:23,340 --> 00:04:24,880
和relover

154
00:04:24,880 --> 00:04:25,900
s

155
00:04:25,900 --> 00:04:27,440
这两个属性名是固定的

156
00:04:27,440 --> 00:04:30,000
名称是

157
00:04:30,000 --> 00:04:31,020
固定的

158
00:04:31,020 --> 00:04:32,040
就是必须交流名字

159
00:04:32,040 --> 00:04:34,860
但是它后边这个变量名其实是可以改的

160
00:04:34,860 --> 00:04:36,400
但是前边这个名字

161
00:04:36,400 --> 00:04:37,160
必须是固定的

162
00:04:37,160 --> 00:04:37,940
这一点需要注意

163
00:04:37,940 --> 00:04:40,500
这属于PoloServer的API的一部分

164
00:04:40,500 --> 00:04:42,020
产生这个实际立项之后

165
00:04:42,020 --> 00:04:42,800
我们再产生一个

166
00:04:42,800 --> 00:04:44,080
ePrize的实际立项

167
00:04:44,080 --> 00:04:45,860
就是这样

168
00:04:45,860 --> 00:04:46,900
App

169
00:04:46,900 --> 00:04:47,920
等于什么

170
00:04:47,920 --> 00:04:48,680
ePrize

171
00:04:48,680 --> 00:04:49,460
交流

172
00:04:49,460 --> 00:04:52,260
然后的话把ePrize作为中间键

173
00:04:52,520 --> 00:04:53,540
放到这个server当中

174
00:04:53,540 --> 00:04:55,340
通过一个API 叫 apply

175
00:04:55,340 --> 00:04:56,620
然后是 middle

176
00:04:56,620 --> 00:04:57,900
然后后边是 where

177
00:04:57,900 --> 00:05:00,720
然后把这个APP放进去

178
00:05:00,720 --> 00:05:01,740
这样就可以了

179
00:05:01,740 --> 00:05:03,020
这就完成了整合操作

180
00:05:03,020 --> 00:05:04,800
最后一步我们这启动监听

181
00:05:04,800 --> 00:05:06,860
就通过API 就是一个press的实际配项

182
00:05:06,860 --> 00:05:07,620
来监听

183
00:05:07,620 --> 00:05:08,640
Listen

184
00:05:08,640 --> 00:05:09,680
指定一个章口

185
00:05:09,680 --> 00:05:10,440
port

186
00:05:10,440 --> 00:05:12,480
这我们用的是四天

187
00:05:12,480 --> 00:05:14,280
然后后边指定一个回交函数

188
00:05:14,280 --> 00:05:16,320
这打印一点信息

189
00:05:16,320 --> 00:05:17,360
叫running

190
00:05:17,360 --> 00:05:19,660
到此为止

191
00:05:19,660 --> 00:05:21,440
关于这整个的五个步骤我们就做完了

192
00:05:21,700 --> 00:05:22,760
然后我们来启动这个服务

193
00:05:22,760 --> 00:05:25,740
通过Node01

194
00:05:25,740 --> 00:05:28,000
这样的running就打印出来了

195
00:05:28,000 --> 00:05:29,740
就表示我们这个服务已经启动成功

196
00:05:29,740 --> 00:05:31,360
然后我们通过浏览器来进行访问

197
00:05:31,360 --> 00:05:33,400
这儿是localhost的

198
00:05:33,400 --> 00:05:34,700
注意一下我们这儿用的是4000

199
00:05:34,700 --> 00:05:36,260
这个端口

200
00:05:36,260 --> 00:05:37,200
然后后面有一个路径

201
00:05:37,200 --> 00:05:37,940
我们回车

202
00:05:37,940 --> 00:05:39,940
注意这路径管会是附近的

203
00:05:39,940 --> 00:05:41,360
然后在这个调试工具当中

204
00:05:41,360 --> 00:05:42,060
我们做一个查询

205
00:05:42,060 --> 00:05:43,360
我们访问hello

206
00:05:43,360 --> 00:05:44,200
这个字段

207
00:05:44,200 --> 00:05:44,800
然后点击

208
00:05:44,800 --> 00:05:45,760
这儿就出来了数据

209
00:05:45,760 --> 00:05:46,460
好

210
00:05:46,460 --> 00:05:48,340
到此为止这个整个的流程我们就做通了

211
00:05:48,340 --> 00:05:50,400
那最后我们再来分析一下

212
00:05:50,400 --> 00:05:51,780
整个的这个步骤当中的一些细节

213
00:05:51,780 --> 00:05:53,420
其实最核心的就是两部分

214
00:05:53,420 --> 00:05:55,100
一个是数据类型的定义

215
00:05:55,100 --> 00:05:56,980
另外一个就是数据解析的处理

216
00:05:56,980 --> 00:05:58,020
就是数据类型有了

217
00:05:58,020 --> 00:05:59,500
然后类型这个字段对应的数据

218
00:05:59,500 --> 00:06:01,060
要通过这种方式再进行提供

219
00:06:01,060 --> 00:06:02,960
这是最核心的两部分

220
00:06:02,960 --> 00:06:04,800
剩下的这个一次五步

221
00:06:04,800 --> 00:06:05,880
这个都是固定的模式

222
00:06:05,880 --> 00:06:07,060
我们后续主要就是

223
00:06:07,060 --> 00:06:08,380
在这两个位置做调整

224
00:06:08,380 --> 00:06:10,080
我们可以定义更多的类型

225
00:06:10,080 --> 00:06:12,540
我们可以定义更多的results来提供数据

226
00:06:12,540 --> 00:06:14,700
这是关于Apollo Server

227
00:06:14,700 --> 00:06:16,760
它主要的这个开发流程

228
00:06:16,760 --> 00:06:17,240
就这么多

229
00:06:17,240 --> 00:06:19,540
这份代码的话

230
00:06:19,540 --> 00:06:20,180
我们就带着大家

231
00:06:20,180 --> 00:06:20,680
写了一遍

232
00:06:20,680 --> 00:06:22,020
然后客户端的话

233
00:06:22,020 --> 00:06:23,440
就是以这种方式查询

234
00:06:23,440 --> 00:06:24,440
然后返回的结果

235
00:06:24,440 --> 00:06:25,520
就是你查询的

236
00:06:25,520 --> 00:06:27,500
特性的自端的信息

237
00:06:27,500 --> 00:06:28,920
关于这个结果的分析

238
00:06:28,920 --> 00:06:30,040
主要就这么三点

239
00:06:30,040 --> 00:06:32,180
就是服务端要定义数据的类型

240
00:06:32,180 --> 00:06:33,680
这个类型就约束了

241
00:06:33,680 --> 00:06:35,180
服务端可以提供哪些数据

242
00:06:35,180 --> 00:06:37,740
然后数据类型的相关的自端

243
00:06:37,740 --> 00:06:38,660
要通过resolve的方式

244
00:06:38,660 --> 00:06:39,640
来进行数据的解析

245
00:06:39,640 --> 00:06:41,180
然后客户端

246
00:06:41,180 --> 00:06:42,700
就可以根据服务端定义的数据类型

247
00:06:42,700 --> 00:06:43,500
来获取你

248
00:06:43,500 --> 00:06:45,400
希望得到的相关的数据了

249
00:06:45,400 --> 00:06:47,260
这是一个完整的流程

250
00:06:47,260 --> 00:06:48,020
好

251
00:06:48,020 --> 00:06:48,500
到最为止

252
00:06:48,500 --> 00:06:49,660
关于Follow Server的

253
00:06:49,660 --> 00:06:50,520
基本的开发布置

254
00:06:50,520 --> 00:06:51,340
我们就说到这里

