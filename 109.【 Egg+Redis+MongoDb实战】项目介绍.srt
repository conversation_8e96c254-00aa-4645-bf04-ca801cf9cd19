1
00:00:00,000 --> 00:00:02,040
好 同学们大家好

2
00:00:02,040 --> 00:00:03,840
那么这一系列课程我们来学习

3
00:00:03,840 --> 00:00:07,760
egg加上radis和mongodb来实现我们的一个登录流程

4
00:00:07,760 --> 00:00:10,060
其实在我们整个课程设计里面

5
00:00:10,060 --> 00:00:11,040
也就是no soccer school

6
00:00:11,040 --> 00:00:12,420
其实没有第五章一个规划

7
00:00:12,420 --> 00:00:13,760
但是它出现的原因是什么呢

8
00:00:13,760 --> 00:00:15,400
咱们为什么后面有一个括号叫做补充

9
00:00:15,400 --> 00:00:17,860
其实这节课视频是我后面去补录的

10
00:00:17,860 --> 00:00:18,020
为什么

11
00:00:18,020 --> 00:00:20,780
因为有的同学在学习完第二章之后

12
00:00:20,780 --> 00:00:21,800
给老师去留言

13
00:00:21,800 --> 00:00:23,340
提到了radis实战这块

14
00:00:23,340 --> 00:00:24,960
可能咱们的demo设计的稍微有点简单

15
00:00:24,960 --> 00:00:26,460
所以说为了满足同学们的要求

16
00:00:26,460 --> 00:00:28,060
我这里就补录了一系列课程

17
00:00:28,060 --> 00:00:29,580
完成咱们的一个登录流程

18
00:00:29,580 --> 00:00:30,600
我们来看一看

19
00:00:30,600 --> 00:00:32,020
Redis它在我们企业中

20
00:00:32,020 --> 00:00:33,140
到底是如何去存储

21
00:00:33,140 --> 00:00:34,240
我们用户的一些登录信息

22
00:00:34,240 --> 00:00:35,180
包括了如何去完成

23
00:00:35,180 --> 00:00:36,340
我们整个登录流程

24
00:00:36,340 --> 00:00:37,640
好 那么废话不多说

25
00:00:37,640 --> 00:00:39,600
我们直接来进入咱们的一个学习

26
00:00:39,600 --> 00:00:41,840
那么在学习登录流程之前

27
00:00:41,840 --> 00:00:42,800
我们首先来看一下

28
00:00:42,800 --> 00:00:44,420
什么是登录流程

29
00:00:44,420 --> 00:00:46,120
可能大家对这块稍微有点疑问

30
00:00:46,120 --> 00:00:47,740
我们首先明确我们学习的目标是什么

31
00:00:47,740 --> 00:00:49,940
比如说我这里就以博学股为例子

32
00:00:49,940 --> 00:00:51,080
因为大家学习我们的视频

33
00:00:51,080 --> 00:00:52,000
是不是都在博学股上面

34
00:00:52,000 --> 00:00:52,880
所以对他很熟悉

35
00:00:52,880 --> 00:00:56,280
那么我们如果说想看到咱们所购买的课程

36
00:00:56,280 --> 00:00:57,500
咱们第一步是不是要点击登录

37
00:00:57,500 --> 00:00:58,120
对吧

38
00:00:58,120 --> 00:00:58,880
因为你不登录

39
00:00:58,880 --> 00:01:00,060
谁知道你有没有买课程

40
00:01:00,060 --> 00:01:01,440
服务器他不知道你是谁

41
00:01:01,440 --> 00:01:01,700
对吧

42
00:01:01,700 --> 00:01:02,720
所以我们需要去点击登录

43
00:01:02,720 --> 00:01:03,100
好

44
00:01:03,100 --> 00:01:03,860
那么这里呢

45
00:01:03,860 --> 00:01:04,860
我那就登录一下

46
00:01:04,860 --> 00:01:07,500
这里呢

47
00:01:07,500 --> 00:01:08,500
我已经登录成功了

48
00:01:08,500 --> 00:01:09,280
那么登录的过程呢

49
00:01:09,280 --> 00:01:10,240
就不给同学们去看了

50
00:01:10,240 --> 00:01:10,540
因为呢

51
00:01:10,540 --> 00:01:12,460
会暴露我的个人电话

52
00:01:12,460 --> 00:01:12,860
好

53
00:01:12,860 --> 00:01:13,660
首先呢

54
00:01:13,660 --> 00:01:14,120
我登录了

55
00:01:14,120 --> 00:01:15,620
此时咱们的页面是不是知道我是谁了

56
00:01:15,620 --> 00:01:16,080
于是呢

57
00:01:16,080 --> 00:01:17,920
我点击到我的课程里面

58
00:01:17,920 --> 00:01:19,240
不知道大家注意到一个问题没有

59
00:01:19,240 --> 00:01:21,100
同学们有没有发现

60
00:01:21,100 --> 00:01:22,660
我不停的在刷新页面

61
00:01:22,660 --> 00:01:24,660
但是呢我的登录状态一直都在

62
00:01:24,660 --> 00:01:26,260
大家思考一下

63
00:01:26,260 --> 00:01:28,320
因为我们可能天天都会去上淘宝

64
00:01:28,320 --> 00:01:28,840
上天猫

65
00:01:28,840 --> 00:01:30,040
而且大家也会发现

66
00:01:30,040 --> 00:01:32,640
我们去刷新或者跳转到不同页面之间

67
00:01:32,640 --> 00:01:34,160
的时候我们的登录态还在

68
00:01:34,160 --> 00:01:35,720
这是怎么做到

69
00:01:35,720 --> 00:01:38,240
服务端是如何去保持我们的登录态的

70
00:01:38,240 --> 00:01:40,540
那么我们为什么每一次刷新页面

71
00:01:40,540 --> 00:01:42,340
都去重新输入用户名或者密码呢

72
00:01:42,340 --> 00:01:43,400
对吧

73
00:01:43,400 --> 00:01:44,380
大家给思考一下

74
00:01:44,380 --> 00:01:46,260
好这里呢就是我能拿博学股

75
00:01:46,260 --> 00:01:47,640
来给同学们举一个登录的例子

76
00:01:47,640 --> 00:01:48,640
其实我们这节课的核心

77
00:01:48,640 --> 00:01:51,040
就是学习我们如何去保持这样一个登录态

78
00:01:51,040 --> 00:01:52,660
而且在中间我们运用到了一种技术

79
00:01:52,660 --> 00:01:54,060
也就是咱们之前学习的redis

80
00:01:54,060 --> 00:01:54,760
好

81
00:01:54,760 --> 00:01:56,680
那么接下来我们来看一下咱们项目的一个效果

82
00:01:56,680 --> 00:01:57,000
是什么

83
00:01:57,000 --> 00:01:59,000
我们最终要实现的一个效果

84
00:01:59,000 --> 00:02:00,080
首先呢

85
00:02:00,080 --> 00:02:00,560
我们访问

86
00:02:00,560 --> 00:02:01,960
访问我们的项目根目录

87
00:02:01,960 --> 00:02:03,280
我们的项目的一个根端口呢

88
00:02:03,280 --> 00:02:03,800
就是7001

89
00:02:03,800 --> 00:02:05,020
比如说我们访问在一个端口回车

90
00:02:05,020 --> 00:02:06,500
大家可以发现我们的页面辞职是什么

91
00:02:06,500 --> 00:02:07,600
7001 login

92
00:02:07,600 --> 00:02:08,860
因为我们辞职没有登录

93
00:02:08,860 --> 00:02:10,460
所以我们呢就跳转到了login页面

94
00:02:10,460 --> 00:02:11,980
然后呢页面要求我们去登录

95
00:02:11,980 --> 00:02:12,380
对吧

96
00:02:12,380 --> 00:02:17,140
那也就是和咱们博学股的啊 在那个登录界面非常的啊 我呢我呢退出一下

97
00:02:17,140 --> 00:02:22,740
就好像呢咱们刚开始进入页面之前啊 平台需要我们登录 此时呢咱们的项目也需要我们去登录

98
00:02:22,740 --> 00:02:24,180
 好 那么我呢这里就登录一下

99
00:02:24,180 --> 00:02:28,900
比如说呢 我去输入了ABC123456 这里呢 是我去注册一个账号 我呢点击登录

100
00:02:28,900 --> 00:02:33,940
好 大家可以看到此时呢 我已经进入到了首页 Logohost 701 好 我们的页面非常的粗糙

101
00:02:33,940 --> 00:02:36,980
 同学们呢不要过分的关注 我们重点学习咱们的登录流程 大家可以看到了

102
00:02:36,980 --> 00:02:38,780
 恭喜你已经登录 可以正常访问

103
00:02:38,780 --> 00:02:40,500
好 此时同学们

104
00:02:41,100 --> 00:02:43,920
你可以发现我们的界面同样的呢也可以去保持一个登录态

105
00:02:43,920 --> 00:02:45,460
比如说我把这样一个页面给关掉

106
00:02:45,460 --> 00:02:48,260
好我重新来打开loko或者7101大家可以发现

107
00:02:48,260 --> 00:02:49,800
我们依然是一个登录的状态

108
00:02:49,800 --> 00:02:52,620
大家思考一下同学们也可以把视频暂停一下

109
00:02:52,620 --> 00:02:55,440
根据咱们前面所有所学习的过程

110
00:02:55,440 --> 00:02:58,500
同学们思考一下我们是如何去保持这样一个登录态的

111
00:02:58,500 --> 00:03:01,320
好那么有的同学可能会想到

112
00:03:01,320 --> 00:03:03,880
Cookie我们的登录都是Cookie去保持的

113
00:03:03,880 --> 00:03:06,180
好那么我们来看一下咱们的页面中有没有Cookie

114
00:03:06,180 --> 00:03:07,720
好那么我们来看一下

115
00:03:07,720 --> 00:03:10,280
在application里面

116
00:03:11,100 --> 00:03:12,400
有一个字段叫做Cookies

117
00:03:12,400 --> 00:03:14,100
那么我们的Cookies其实有两个Lame

118
00:03:14,100 --> 00:03:15,400
说明了我们Cookies有两个字段

119
00:03:15,400 --> 00:03:16,360
一个叫做

120
00:03:16,360 --> 00:03:20,200
一个是CSRF token

121
00:03:20,200 --> 00:03:22,520
那么是不是他们在帮助我们去保持登陆态呢

122
00:03:22,520 --> 00:03:24,380
那么这里可以告诉同学答案

123
00:03:24,380 --> 00:03:25,020
确实是这样的

124
00:03:25,020 --> 00:03:26,040
但是你们有没有思考过

125
00:03:26,040 --> 00:03:28,620
这样一个Cookies它是从哪里来的

126
00:03:28,620 --> 00:03:31,040
然后我们如何去保证咱们的登陆态

127
00:03:31,040 --> 00:03:33,160
那么这一串支付创代表什么

128
00:03:33,160 --> 00:03:34,240
在后台我们的Redis

129
00:03:34,240 --> 00:03:35,920
我们的MongoDB里面到底发生了什么事情

130
00:03:35,920 --> 00:03:38,020
同学思考一下心里有没有头绪

131
00:03:38,020 --> 00:03:39,820
可能大部分同学我相信

132
00:03:39,820 --> 00:03:41,620
对这块是不了解的

133
00:03:41,620 --> 00:03:44,100
那么如果说不了解的同学

134
00:03:44,100 --> 00:03:46,980
我们后面的课程就会给同学们去解答

135
00:03:46,980 --> 00:03:50,540
那么这里就是我们项目的一个演示

136
00:03:50,540 --> 00:03:52,040
刚才我们演示了保持登录态

137
00:03:52,040 --> 00:03:53,360
此时我们还支持一个功能

138
00:03:53,360 --> 00:03:54,420
退出登录

139
00:03:54,420 --> 00:03:55,280
大家可以发现

140
00:03:55,280 --> 00:03:56,300
当我们退出登录之后

141
00:03:56,300 --> 00:03:57,340
我们再来访问7001

142
00:03:57,340 --> 00:04:00,100
页面就会自动跳转到一个登录的页面

143
00:04:00,100 --> 00:04:00,940
需要我们去登录

144
00:04:00,940 --> 00:04:01,840
如果说我们再次登录

145
00:04:01,840 --> 00:04:04,200
我们同样又可以进入咱们的一个首页

146
00:04:04,200 --> 00:04:06,460
那么这里就是我们项目完成之后的样子

147
00:04:06,460 --> 00:04:08,420
我们重点是去完成咱们这样的一个登录流程

148
00:04:08,420 --> 00:04:09,240
其实大家可以发现

149
00:04:09,240 --> 00:04:12,890
虽然说我们的页面很简陋 但是和我们博学谷的一个登录流程其实是非常相似的

150
00:04:12,890 --> 00:04:13,600
 对吧 我们登录之后

151
00:04:13,600 --> 00:04:18,200
然后呢 输入手机号 输入密码 登录之后我们去刷新页面 我们的登录它其实还是存在的

152
00:04:18,200 --> 00:04:23,840
好 那么接下来的课程里面呢 我呢就带着同学们从您开始慢慢的完成咱们这样一个过程

