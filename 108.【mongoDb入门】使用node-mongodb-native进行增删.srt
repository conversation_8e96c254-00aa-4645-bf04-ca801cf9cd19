1
00:00:00,000 --> 00:00:03,320
好 这节课我们就来看一下咱们蒙哥DB里面

2
00:00:03,320 --> 00:00:05,120
他的一些增商改查是怎么去实现的

3
00:00:05,120 --> 00:00:07,680
首先我们在讲解之前呢 我们需要去搞清楚三个概念

4
00:00:07,680 --> 00:00:10,500
其实在咱们蒙哥DB里面 他有三个比较重要概念是什么呢 一个是数据库

5
00:00:10,500 --> 00:00:11,520
 一个是集合 一个是文档

6
00:00:11,520 --> 00:00:16,640
那么我们的增商改查其实都是基于他们去做咱们的一些数据的一些修改或者去查询

7
00:00:16,640 --> 00:00:17,660
那么到底是什么

8
00:00:17,660 --> 00:00:19,200
那么他们到底是什么呢

9
00:00:19,200 --> 00:00:23,800
其实同学们别着急 我们通过咱们增商改查的一个过程 我们就可以去理解他们到底是什么

10
00:00:23,800 --> 00:00:25,340
好 首先我们来看一下第一个例子

11
00:00:25,340 --> 00:00:27,140
咱们去怎么样去增加数据

12
00:00:30,000 --> 00:00:37,030
好 这里其实我已经把代码给写好了 因为咱们增上改查代码量也比较大

13
00:00:37,030 --> 00:00:42,280
 为了节约同学们的时间呢 我就不去一行一行去写了 因为大部分的代码都是比较类似的

14
00:00:42,280 --> 00:00:50,220
我会带着同学们去走一遍咱们的思路 把思路走完就可以了 首先我们来看一下

15
00:00:50,220 --> 00:00:54,580
首先我们来看一下咱们mongo服务起来了没有

16
00:00:55,600 --> 00:00:58,800
首先我们是不是和redis一样啊 我们是不是要首先是不是要启动咱们呢

17
00:00:58,800 --> 00:01:00,720
 咱们咱们蒙哥db的一个服务啊

18
00:01:00,720 --> 00:01:02,760
其实蒙哥db的服务怎么去起来 其实很简单

19
00:01:02,760 --> 00:01:06,610
蒙哥d 如果说同学们能够自己去安装一遍蒙哥 大家就可以知道怎么样去启动服务

20
00:01:06,610 --> 00:01:07,380
 就叫蒙哥d

21
00:01:07,380 --> 00:01:11,220
大家可以看到 其实我们现在已经启动了蒙哥db 他那个数据库

22
00:01:11,220 --> 00:01:16,340
和redis是类似的 所以我这里就不去过头介绍 我们通过蒙哥的这个命令就可以启动咱们一个蒙哥的一个数据

23
00:01:16,340 --> 00:01:18,640
好 那我们就连接来看一下 看里面到底有哪些数据

24
00:01:18,640 --> 00:01:19,920
好 query ford

25
00:01:21,460 --> 00:01:28,060
大家可以看到 我们现在有个数据库叫做myproject 里面有一个集合 叫做dokomens

26
00:01:28,060 --> 00:01:29,240
 首先我们来把它双除一下

27
00:01:29,240 --> 00:01:33,650
这是老师之前做测试用的数据 我们先把它双掉 先把它双掉 好 先清空

28
00:01:33,650 --> 00:01:33,960
 好

29
00:01:33,960 --> 00:01:37,940
行 那么我们来看一下首先怎么样去创建数据 怎么去创建数据

30
00:01:37,940 --> 00:01:42,760
比如说首先 我们是不是需要去把咱们的mongo 他的一个库引入啊

31
00:01:42,760 --> 00:01:49,140
引入库 对吧 require mongodb.mongo client 然后呢 去require一个aset 它是一个断言库

32
00:01:49,140 --> 00:01:50,200
 这里的暂时我们不需要

33
00:01:50,980 --> 00:01:53,420
url 就是我们去数据库的地址 我们数据个地址

34
00:01:53,420 --> 00:01:57,860
其实他就和咱们的一个比如说ftp 或者我们邮箱的一些潜缀 包括我们浏览器潜缀http

35
00:01:57,860 --> 00:02:01,660
那么这里呢 mongodb 数据库他的潜缀呢 其实就是mongodb 然后呢 冒号双鞋

36
00:02:01,660 --> 00:02:02,320
 双鞋线

37
00:02:02,320 --> 00:02:06,660
然后多个host 我们的27017 这里27017呢 就是我们mongodb 他那个默认端口

38
00:02:06,660 --> 00:02:07,520
 我们可以看一下

39
00:02:07,520 --> 00:02:19,140
好 数据太多了 暂时 暂时没找到 没关系

40
00:02:19,660 --> 00:02:21,620
大口大大T的就行了 其实mongo

41
00:02:21,620 --> 00:02:25,300
默认短口是什么27017

42
00:02:25,300 --> 00:02:29,900
首先呢我们我们此时是不是已经把咱们数据库的地址已经找到了

43
00:02:29,900 --> 00:02:31,820
然后呢也创建了一个我们mongo的一个扣端

44
00:02:31,820 --> 00:02:34,920
那么此时此时我们是不是要去溜一个咱们的mongo的扣端

45
00:02:34,920 --> 00:02:37,620
然后去连接他通过咱们的URL去连接他好

46
00:02:37,620 --> 00:02:40,740
去连接他 首先呢我们client去溜了一个咱们

47
00:02:40,740 --> 00:02:43,540
它是什么意思 就是实力化

48
00:02:43,540 --> 00:02:45,660
一个扣端

49
00:02:45,660 --> 00:02:46,960
并且

50
00:02:46,960 --> 00:02:48,400
连接

51
00:02:49,660 --> 00:02:54,660
好 现在已经点上 那么这里呢 我们来定义一个我们数据名称 叫做myproject

52
00:02:54,660 --> 00:03:02,660
这里呢 叫做myproject 他呢 他呢 就是咱们一个数据库 你比如说

53
00:03:02,660 --> 00:03:09,900
我们一个系统里面 我们一个系统里面 比如说我们去lue一个mongo client 一个客户端是不是就能操作我们的一个数据库啊

54
00:03:09,900 --> 00:03:11,660
 比如说我们操作一个db 就叫做咱们的myproject

55
00:03:11,660 --> 00:03:17,660
所以呢 我们这里有 首先我们操作了mongo的这样一个数据库的名称 叫做myproject

56
00:03:17,660 --> 00:03:18,660
 这里呢 就是数据库的概念

57
00:03:18,660 --> 00:03:19,420
有个概念

58
00:03:19,420 --> 00:03:21,740
好 这里呢 我们首先来看一下我们一些

59
00:03:21,740 --> 00:03:25,320
首先我们的客户端是不是要去

60
00:03:25,320 --> 00:03:28,900
连接啊 比如说 我们去调用一个client.connect方法

61
00:03:28,900 --> 00:03:29,920
这里刷掉

62
00:03:29,920 --> 00:03:33,500
我们去调用一个connect 说明了 我们现在是已经连接了我们数据

63
00:03:33,500 --> 00:03:36,320
client.connect 我们连接了数据 然后

64
00:03:36,320 --> 00:03:37,860
不好意思 然后呢

65
00:03:37,860 --> 00:03:41,440
我们的dbclient.db传入我们的

66
00:03:41,440 --> 00:03:42,460
数据名称

67
00:03:42,460 --> 00:03:46,560
这里是什么意思呢 比如说 我们通过client我们的客户端 调用一个db方法

68
00:03:46,560 --> 00:03:47,580
 然后传入我们数据

69
00:03:47,840 --> 00:03:49,140
如果我们数据库名称叫做什么

70
00:03:49,140 --> 00:03:50,040
my project

71
00:03:50,040 --> 00:03:52,240
他的意思就是创建

72
00:03:52,240 --> 00:03:57,140
一个数据库叫做叫做什么叫做my project

73
00:03:57,140 --> 00:03:57,440
好

74
00:03:57,440 --> 00:03:59,840
那么我们创建了一个数据库叫做my project

75
00:03:59,840 --> 00:04:01,640
大家还记得我们关心数据库里面

76
00:04:01,640 --> 00:04:04,340
除了数据库之外里面有什么

77
00:04:04,340 --> 00:04:05,240
存储数据

78
00:04:05,240 --> 00:04:07,040
我们数据是不是存在一张表里面了

79
00:04:07,040 --> 00:04:07,540
对吧

80
00:04:07,540 --> 00:04:08,840
我们数据是不是存在一张表里面

81
00:04:08,840 --> 00:04:10,640
之前我们讲redis的时候是不是截图过

82
00:04:10,640 --> 00:04:11,240
好

83
00:04:11,240 --> 00:04:13,140
那么我们既然数据存在一张表里面

84
00:04:13,140 --> 00:04:14,740
那么我们的mongodb里面有没有表的概念呢

85
00:04:14,740 --> 00:04:15,340
其实也有

86
00:04:15,340 --> 00:04:17,080
首先我们的db

87
00:04:17,080 --> 00:04:19,440
它是一个数据库吧 它代表咱们的myproject占一个库

88
00:04:19,440 --> 00:04:24,240
那么比如说 比如说它代表占一个库 我们首先此时是不是要建表啊 对吧

89
00:04:24,240 --> 00:04:26,560
 首先我们第一步是不是要建表啊 同学们

90
00:04:26,560 --> 00:04:33,520
是不是建表 好 那么我们建完表之后 首先我们来看一下怎么样去建表

91
00:04:33,520 --> 00:04:36,240
比如说我们去插入一条数据 这里是插入数据

92
00:04:36,240 --> 00:04:40,880
插入数据insight documents 我们来看一下这个方法到底是什么

93
00:04:40,880 --> 00:04:43,360
首先 我们是不是需要把DB给传入

94
00:04:43,360 --> 00:04:46,600
DB就代表我们数据库占一个对象吧 myproject占一个DB对象

95
00:04:46,920 --> 00:04:50,280
把它传进去 然后呢有一些回来函数 把咱们的连接给关闭 这里呢不是重点

96
00:04:50,280 --> 00:04:52,040
 我们来看一下insate document里面的一些逻辑

97
00:04:52,040 --> 00:04:53,580
好 我们点击看一下

98
00:04:53,580 --> 00:04:55,360
其实大家可以看到

99
00:04:55,360 --> 00:05:01,260
我们的db调用了一个connection方法 是什么方法呢 叫做document 也就是说什么意思

100
00:05:01,260 --> 00:05:02,280
 什么意思

101
00:05:02,280 --> 00:05:04,320
建了一张

102
00:05:04,320 --> 00:05:08,160
document 这样的

103
00:05:08,160 --> 00:05:13,800
db.connection 建了一张document 这样的表

104
00:05:13,800 --> 00:05:16,620
其实它在我们的咱们的mongodb的一个概念是什么呢

105
00:05:16,920 --> 00:05:17,840
他的概念是什么

106
00:05:17,840 --> 00:05:20,340
Connection它的英文名称翻译过来是什么

107
00:05:20,340 --> 00:05:21,060
是不是集合呀

108
00:05:21,060 --> 00:05:22,440
集合

109
00:05:22,440 --> 00:05:23,920
好我们再回到我们的讲义

110
00:05:23,920 --> 00:05:24,920
刚才我们是不是讲到了

111
00:05:24,920 --> 00:05:27,140
咱们在mongodb里面是不是有三个概念

112
00:05:27,140 --> 00:05:28,420
一个数据库一个集合一个文档

113
00:05:28,420 --> 00:05:30,380
那么刚才我们讲到的集合是什么

114
00:05:30,380 --> 00:05:31,060
是不是Connection

115
00:05:31,060 --> 00:05:34,800
是不是类似咱们的类似关系型

116
00:05:34,800 --> 00:05:36,720
数据库

117
00:05:36,720 --> 00:05:38,660
你的表啊

118
00:05:38,660 --> 00:05:39,580
那么什么是文档呢

119
00:05:39,580 --> 00:05:41,440
文档是不是就很少理解了

120
00:05:41,440 --> 00:05:43,180
咱们的数据库最上层是数据库

121
00:05:43,180 --> 00:05:44,180
接下来是集合

122
00:05:44,180 --> 00:05:45,140
集合也就是类似表

123
00:05:45,140 --> 00:05:46,060
那么表里面存的是什么

124
00:05:46,060 --> 00:05:52,760
是不是一条条数据啊 那么我们当其实就是咱们一条一条数据 一条一条的数据

125
00:05:52,760 --> 00:05:58,000
好 这里的就是咱们蒙古地比的三个概念 其实大家理解了 咱们蒙古地比就非常好学习

126
00:05:58,000 --> 00:05:58,640
 其实很简单

127
00:05:58,640 --> 00:06:02,680
好 我们再来看一下 好 首先我们是不是创建了Documents在那张表

128
00:06:02,680 --> 00:06:09,020
再咱们MyProject在一个数据库里面去创建了Documents在一个表 好 那么我们是不是创建之后要给你们去添加数据啊

129
00:06:09,020 --> 00:06:11,760
比如说这里 这里是什么呢 我们可以添加三条数据

130
00:06:11,760 --> 00:06:17,760
添加三条数据 叫做在咱们的connection下面有个方法 叫做insight money

131
00:06:17,760 --> 00:06:22,960
insight money 什么意思啊 是不是可以插入多条数据 比如说我们去插入一个数据

132
00:06:22,960 --> 00:06:27,760
a为1 b为2 a为2 b为2 a为3 b为3 什么意思

133
00:06:27,760 --> 00:06:32,160
我们插入了三条数据 他有哪些字乱呢 是不是有ab两个字乱呢

134
00:06:32,160 --> 00:06:35,660
其实我们可以看一下 可能有的同学会理解 我们是不是插入了三个对象

135
00:06:35,660 --> 00:06:39,260
 其实不是的 在mongodb里面 他会把它转为一张表里面三个字乱

136
00:06:39,260 --> 00:06:40,920
两个字了 a 和 b 这两个字了

137
00:06:40,920 --> 00:06:45,820
什么意思 为什么说我们要去使用我们的可刷工具了 我们就要看一下到底会发生一件什么事情

138
00:06:45,820 --> 00:06:49,370
首先我们去调用什么方法 是不调用咱们的insight 是不是就插入数据

139
00:06:49,370 --> 00:06:50,120
 好 我们来看一下

140
00:06:50,120 --> 00:06:51,420
我们来看一下

141
00:06:51,420 --> 00:06:57,260
好 肯定 load chapter 4 app 走

142
00:06:57,260 --> 00:06:59,360
好 其实这里来说明什么问题

143
00:06:59,360 --> 00:07:02,260
to

144
00:07:02,260 --> 00:07:06,760
好 warning 不管 我们先不管 我们先看一下结果 warning 先不管 我们来看一下结果

145
00:07:09,260 --> 00:07:09,780
好

146
00:07:09,780 --> 00:07:14,640
再还不是很清晰啊大家等一下

147
00:07:14,640 --> 00:07:21,040
Document好大家可以看到大家可以看到我们Document在一张表里面

148
00:07:21,040 --> 00:07:25,380
是不是有两个字段呢一个A和一个B然后还一个他

149
00:07:25,380 --> 00:07:32,560
现在是默认的字段ID下边ID这是他默认自己生成的一个随机注创好我们可以看到咱们刚才是不是已经添加了什么

150
00:07:32,560 --> 00:07:36,400
添加了三条数据咱们在咱们在路线里面他的表现是一个对象吗

151
00:07:36,400 --> 00:07:38,960
有A和B两个属性他有A和B两个属性

152
00:07:39,260 --> 00:07:42,580
那么其实他在数据库里面表现是什么 是两个字段 a 和 b 这两个字段

153
00:07:42,580 --> 00:07:45,140
添加三条数据字段

154
00:07:45,140 --> 00:07:47,200
有两个分别

155
00:07:47,200 --> 00:07:49,500
a 和 b 好

156
00:07:49,500 --> 00:07:53,340
好 这里呢就是我们

157
00:07:53,340 --> 00:07:54,360
我们

158
00:07:54,360 --> 00:08:00,500
通过咱们的这个库去插入数据的操作好 那么我们既然插入的数据 那我们就来看一下怎么去查询数据

159
00:08:00,500 --> 00:08:02,040
比如说我们去查询一条数据

160
00:08:02,040 --> 00:08:05,880
同样的我们是不是也去调用的在下面调用我们slack的data 传入db和cobac

161
00:08:06,140 --> 00:08:10,500
好 首先我们在查询的时候是不是首先db.connection 我们先连接在一张笔

162
00:08:10,500 --> 00:08:14,340
然后去传一些参数 比如说我们去调用connection.find

163
00:08:14,340 --> 00:08:17,140
find的是什么 查找 比如说我们去查找

164
00:08:17,140 --> 00:08:18,940
查找一个值

165
00:08:18,940 --> 00:08:19,960
b等于2

166
00:08:19,960 --> 00:08:20,740
b等于2

167
00:08:20,740 --> 00:08:23,040
然后呢 怎么把它转为一个

168
00:08:23,040 --> 00:08:24,320
数据对象

169
00:08:24,320 --> 00:08:26,100
那么我们就来看一下它的结果是什么

170
00:08:26,100 --> 00:08:27,380
好 首先我们在下面呢

171
00:08:27,380 --> 00:08:29,440
把整个inset document注释一下

172
00:08:29,440 --> 00:08:31,220
我们来看一下select 它返回一个什么

173
00:08:31,220 --> 00:08:33,020
它返回一个什么

174
00:08:33,020 --> 00:08:35,060
好 走你

175
00:08:35,580 --> 00:08:37,700
好大家可以看到 我们现在是不是成功返回了两个数据

176
00:08:37,700 --> 00:08:43,990
A1 B2 A2 B2 我们来看一下咱们数据库里面 是不是这样的一些数据 比如说A他有一些值123

177
00:08:43,990 --> 00:08:44,180
 B呢

178
00:08:44,180 --> 00:08:45,840
他的值为什么 223

179
00:08:45,840 --> 00:08:49,500
那么是不是我们成功的去查询的一些数据啊 同学们

180
00:08:49,500 --> 00:08:52,020
所以这里是可以说明什么问题

181
00:08:52,020 --> 00:08:56,420
其实很简单嘛 比如说我们先去连接我们数据库 然后呢去输入我们的查询语句

182
00:08:56,420 --> 00:08:56,960
 其实在

183
00:08:56,960 --> 00:09:01,440
mongo里面他查询语句 也是我们的精神 你比如说你去查询我们的一个B2

184
00:09:01,440 --> 00:09:03,780
 他的一些值有哪些 有两条吧 两条数据吧 对吧 在这里

185
00:09:03,780 --> 00:09:04,980
是不是有两条数据

186
00:09:05,580 --> 00:09:07,120
所以说他的一些语法是非常的

187
00:09:07,120 --> 00:09:08,400
愚化非常好

188
00:09:08,400 --> 00:09:09,160
愚化

189
00:09:09,160 --> 00:09:10,700
非常的简洁

190
00:09:10,700 --> 00:09:14,800
好同样的我们查询数据之后

191
00:09:14,800 --> 00:09:16,580
我们再来看一下怎么去更新数据对吧

192
00:09:16,580 --> 00:09:18,380
好我们先注视一下

193
00:09:18,380 --> 00:09:20,940
update我们调用下update的方法

194
00:09:20,940 --> 00:09:24,520
好我们来看一下update他是怎么去实现的

195
00:09:24,520 --> 00:09:27,340
首先还是一样的我们首先去连接document这张表

196
00:09:27,340 --> 00:09:28,100
然后

197
00:09:28,100 --> 00:09:32,200
我们要去更新一下数据首先我们是不是要把这条数据给找到啊然后呢

198
00:09:32,980 --> 00:09:34,140
你再去告诉他怎么样

199
00:09:34,140 --> 00:09:36,520
比如说我们现在collection.update

200
00:09:36,520 --> 00:09:37,980
它是一个更新的方法

201
00:09:37,980 --> 00:09:39,420
你需要去传入vrstr

202
00:09:39,420 --> 00:09:41,240
第一个参数也就是代表他查询什么内容

203
00:09:41,240 --> 00:09:41,640
对吧

204
00:09:41,640 --> 00:09:42,680
第二个参数呢

205
00:09:42,680 --> 00:09:44,740
是不是就是查询你怎么去更新呢

206
00:09:44,740 --> 00:09:46,480
你比如说去传入一个dolset

207
00:09:46,480 --> 00:09:47,880
这是mongodb它那个规定

208
00:09:47,880 --> 00:09:49,680
然后你去去查询

209
00:09:49,680 --> 00:09:52,240
把a为1的那一条

210
00:09:52,240 --> 00:09:53,080
把他的值

211
00:09:53,080 --> 00:09:53,840
把他的

212
00:09:53,840 --> 00:09:55,640
把他的b站一个字方改为1

213
00:09:55,640 --> 00:09:57,420
什么意思

214
00:09:57,420 --> 00:09:58,220
什么意思

215
00:09:58,220 --> 00:09:59,620
我们哪一条数据a为1

216
00:09:59,620 --> 00:10:00,420
是不是

217
00:10:01,680 --> 00:10:03,240
是不是这样一条数据他的A值为1

218
00:10:03,240 --> 00:10:06,620
是不是第一条数据他的A为1

219
00:10:06,620 --> 00:10:11,080
对吧

220
00:10:11,080 --> 00:10:13,340
我们要把他的B是不是改为1

221
00:10:13,340 --> 00:10:13,640
对吧

222
00:10:13,640 --> 00:10:13,980
好

223
00:10:13,980 --> 00:10:15,140
那我就来看一下怎么去改

224
00:10:15,140 --> 00:10:15,840
其实很简单

225
00:10:15,840 --> 00:10:17,980
为亚史军查询update

226
00:10:17,980 --> 00:10:18,640
告诉他怎么改

227
00:10:18,640 --> 00:10:19,280
把B改为1

228
00:10:19,280 --> 00:10:20,320
让我就执行来看一下

229
00:10:20,320 --> 00:10:21,840
直接来执行update data

230
00:10:21,840 --> 00:10:23,240
好

231
00:10:23,240 --> 00:10:24,140
我们来执行

232
00:10:24,140 --> 00:10:24,980
的app.js

233
00:10:24,980 --> 00:10:25,880
好

234
00:10:25,880 --> 00:10:27,440
successfully to server

235
00:10:27,440 --> 00:10:28,240
修改成功

236
00:10:28,240 --> 00:10:28,420
好

237
00:10:28,420 --> 00:10:29,220
我们来看一下结果

238
00:10:29,220 --> 00:10:30,280
好

239
00:10:30,280 --> 00:10:31,640
大家看到我们的B是不是已经改为1了

240
00:10:31,680 --> 00:10:38,340
好 那么我们的更新操作完成之后 接下来我们来看一下最后一个 我们刚才是不是已经实现了

241
00:10:38,340 --> 00:10:42,440
侦查改 最后是三吧 三其实是最简单的 删除是最简单的

242
00:10:42,440 --> 00:10:46,060
你比如说 我们去调用删除方法 那么我们想都不用想 删除 他需要做什么

243
00:10:46,060 --> 00:10:49,210
 是不是也需要查询条件了 为什么 因为你要把这条数据给查出来 这就算吧

244
00:10:49,210 --> 00:10:50,880
 你比如说我们去调用collecting.remove

245
00:10:50,880 --> 00:10:55,240
然后呢 去输入查询条件 a为1的这条数据 好 然后把删掉 那么我们来看一下

246
00:10:55,240 --> 00:10:56,760
是不是这回事

247
00:10:56,760 --> 00:10:57,800
手里

248
00:10:57,800 --> 00:11:00,600
好 刷新

249
00:11:01,680 --> 00:11:03,720
诶 怎么没算掉 好 我们再来看一下

250
00:11:03,720 --> 00:11:09,620
双除A为1的这条数据 好 我们再来看一下

251
00:11:09,620 --> 00:11:11,160
重新走一遍 走

252
00:11:11,160 --> 00:11:17,040
好 大家可以看到我们的数据A为1这条数据已经算掉了

253
00:11:17,040 --> 00:11:19,860
这里我们就已经完整的介绍了mongotdb

254
00:11:19,860 --> 00:11:21,900
mongotdb里面它的一个增商改查的一个过程

255
00:11:21,900 --> 00:11:24,980
其实非常简单 我们来总结这一个过程

256
00:11:24,980 --> 00:11:25,740
我们来总结一下

257
00:11:25,740 --> 00:11:27,020
首先

258
00:11:27,020 --> 00:11:28,560
首先我们第一步

259
00:11:28,560 --> 00:11:31,380
是不是需要去连接我们的数据库 比如说你去梦女

260
00:11:31,380 --> 00:11:32,460
6个mongodb的扣端

261
00:11:32,460 --> 00:11:33,280
然后呢

262
00:11:33,280 --> 00:11:33,800
输入咱们呢

263
00:11:33,800 --> 00:11:35,100
是不是传入咱们的一个mongodb的一个地址

264
00:11:35,100 --> 00:11:35,920
也就是咱们的默认

265
00:11:35,920 --> 00:11:37,320
27017这样一个端口

266
00:11:37,320 --> 00:11:37,960
然后

267
00:11:37,960 --> 00:11:39,560
你首先连接数据库之后

268
00:11:39,560 --> 00:11:40,920
连接了咱们mongodb的服务之后

269
00:11:40,920 --> 00:11:42,080
27017代表的是什么

270
00:11:42,080 --> 00:11:44,600
是不是代表mongodb这样一个服务啊

271
00:11:44,600 --> 00:11:45,340
那么你连接服务

272
00:11:45,340 --> 00:11:47,340
服务里面是不是会存在很多的数据库啊

273
00:11:47,340 --> 00:11:47,720
所以呢

274
00:11:47,720 --> 00:11:48,180
这里呢

275
00:11:48,180 --> 00:11:49,660
你需要去选择一个数据库叫做什么呢

276
00:11:49,660 --> 00:11:50,400
叫做myproject

277
00:11:50,400 --> 00:11:51,800
myproject

278
00:11:51,800 --> 00:11:52,460
你此时

279
00:11:52,460 --> 00:11:53,280
首先

280
00:11:53,280 --> 00:11:55,040
你client.collect的方法

281
00:11:55,040 --> 00:11:56,500
就是你连接咱们的mongo之后

282
00:11:56,500 --> 00:11:58,180
你需要去调用DB这样一个方法

283
00:11:58,180 --> 00:12:00,460
去创建一个数据库叫做myproject

284
00:12:00,460 --> 00:12:00,820
或者

285
00:12:00,820 --> 00:12:03,040
或者什么

286
00:12:03,040 --> 00:12:03,400
连接

287
00:12:03,400 --> 00:12:04,740
你如果说这样一个库不存在

288
00:12:04,740 --> 00:12:05,860
是不是就去创建一个myproject

289
00:12:05,860 --> 00:12:06,420
如果存在呢

290
00:12:06,420 --> 00:12:07,380
就直接去连接它了

291
00:12:07,380 --> 00:12:07,660
好

292
00:12:07,660 --> 00:12:08,740
那么你连接它之后

293
00:12:08,740 --> 00:12:09,780
是不是需要去做一些操作呀

294
00:12:09,780 --> 00:12:11,220
咱们就可以进行下面的事物操作

295
00:12:11,220 --> 00:12:11,740
你比如说

296
00:12:11,740 --> 00:12:13,100
插入

297
00:12:13,100 --> 00:12:14,420
查询

298
00:12:14,420 --> 00:12:14,980
更新

299
00:12:14,980 --> 00:12:15,740
专组

300
00:12:15,740 --> 00:12:16,720
比如说我们去

301
00:12:16,720 --> 00:12:18,220
插入

302
00:12:18,220 --> 00:12:20,140
比如说我们去调用咱们的inside这样一个方法

303
00:12:20,140 --> 00:12:22,340
是不是首先DB对象会传进来

304
00:12:22,340 --> 00:12:22,900
然后呢

305
00:12:22,900 --> 00:12:24,580
第一步我们是不是要去建表啊

306
00:12:24,580 --> 00:12:25,620
比如说Documents这样一个币

307
00:12:25,620 --> 00:12:28,240
然后我们通过inset去插入

308
00:12:28,240 --> 00:12:28,940
插入一些数据之后

309
00:12:28,940 --> 00:12:30,780
他还会有些callback去有一些返回值

310
00:12:30,780 --> 00:12:32,080
此时的我们就在Documents

311
00:12:32,080 --> 00:12:32,980
在一张表里面去插

312
00:12:32,980 --> 00:12:34,340
插入了三条数据

313
00:12:34,340 --> 00:12:35,380
然后我们怎么样去查询呢

314
00:12:35,380 --> 00:12:36,780
查询是不是需要有一个查询条件

315
00:12:36,780 --> 00:12:38,380
也是 where str

316
00:12:38,380 --> 00:12:39,340
比如说我们去传入对象

317
00:12:39,340 --> 00:12:40,820
查询b为2的一些数据

318
00:12:40,820 --> 00:12:41,040
对吧

319
00:12:41,040 --> 00:12:42,320
他的语法是非常简洁的

320
00:12:42,320 --> 00:12:43,520
更新同样的

321
00:12:43,520 --> 00:12:44,140
怎么去更新呢

322
00:12:44,140 --> 00:12:45,540
首先你需要传入查询条件吧

323
00:12:45,540 --> 00:12:46,480
第一个参数查询条件

324
00:12:46,480 --> 00:12:47,480
然后呢怎么去更新

325
00:12:47,480 --> 00:12:48,780
这里呢就是传入一个多的set

326
00:12:48,780 --> 00:12:50,140
这是蒙狗里面他的一些规定

327
00:12:50,140 --> 00:12:52,980
有信息同学可以去仔细看一下蒙狗地比他的一些文章可以去研究一下

328
00:12:52,980 --> 00:12:53,380
好

329
00:12:53,380 --> 00:12:54,060
我们去告诉他

330
00:12:54,060 --> 00:12:55,660
我们要把比改比改为一

331
00:12:55,660 --> 00:12:56,580
这里就是一个更新

332
00:12:56,580 --> 00:12:57,460
删除更简单的

333
00:12:57,460 --> 00:12:59,700
直接调用一个咱们collecting的一个remove

334
00:12:59,700 --> 00:13:01,380
传入咱们的一个查询条件

335
00:13:01,380 --> 00:13:02,420
然后把它给删掉

336
00:13:02,420 --> 00:13:02,580
好

337
00:13:02,580 --> 00:13:05,020
这里呢就是我们蒙狗地比他的一个

338
00:13:05,020 --> 00:13:07,300
增上改查的一个过程

339
00:13:07,300 --> 00:13:08,340
希望同学们好好地理解一下

340
00:13:08,340 --> 00:13:09,500
如果说对细节有疑问同学

341
00:13:09,500 --> 00:13:11,060
因为我们这里呢没有

342
00:13:11,060 --> 00:13:14,700
没有给同学们去去把咱们蒙狗里面的一些文章来从一去看点

343
00:13:14,700 --> 00:13:15,980
比如说像remove啊

344
00:13:15,980 --> 00:13:16,580
发音的呀

345
00:13:16,580 --> 00:13:18,880
因此的卖娘其实在文档里面都很清楚

346
00:13:18,880 --> 00:13:20,880
我们没有必要去一个一个去念文档吧

347
00:13:20,880 --> 00:13:21,840
所以说同学们有兴趣

348
00:13:21,840 --> 00:13:22,980
可以去仔细的看一下

349
00:13:22,980 --> 00:13:23,740
mongodb他的全貌

350
00:13:23,740 --> 00:13:25,340
他到底有哪些API有文档里面有哪些内容

351
00:13:25,340 --> 00:13:26,880
大家可以直接去看一下他的官方文档

352
00:13:26,880 --> 00:13:27,180
好

353
00:13:27,180 --> 00:13:28,340
这里呢就是我们这几个的内容

