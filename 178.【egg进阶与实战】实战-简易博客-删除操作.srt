1
00:00:00,000 --> 00:00:01,800
好,接下来我们就来看一下

2
00:00:01,800 --> 00:00:02,820
双处操作

3
00:00:02,820 --> 00:00:07,940
那么双处操作了,我们来看一下对应关系delete,是不是对应destroy这样一个方法

4
00:00:07,940 --> 00:00:09,480
好,我们就来写一下

5
00:00:09,480 --> 00:00:12,040
asyncdestroy

6
00:00:12,040 --> 00:00:14,600
我们首先来分析一下思路上去做一个双处

7
00:00:14,600 --> 00:00:16,900
首先呢,第一步肯定是获取id

8
00:00:16,900 --> 00:00:19,200
那么第二步呢,是不是就是咱们数据库的双处了

9
00:00:19,200 --> 00:00:21,760
好,那么其实它的代码大家思考一下是不是和我们获取

10
00:00:21,760 --> 00:00:23,800
查询单个数据是不是有点类似啊

11
00:00:23,800 --> 00:00:26,120
其实确实是非常的类似

12
00:00:26,120 --> 00:00:27,640
我们比如说我们可以直接括过来一改

13
00:00:28,680 --> 00:00:30,980
首先呢 我们去获取咱们的一个id 对吧

14
00:00:30,980 --> 00:00:33,280
然后咱们通过model的posts的find

15
00:00:33,280 --> 00:00:34,820
find是什么操作 是不是查找

16
00:00:34,820 --> 00:00:36,880
那么假如说我们把它改为remove呢

17
00:00:36,880 --> 00:00:38,920
是不是咱们就把id为postsid

18
00:00:38,920 --> 00:00:40,200
在那条数据直接给拴掉了

19
00:00:40,200 --> 00:00:42,000
那么其实这样就完成了 我们就拴除超过

20
00:00:42,000 --> 00:00:43,780
大家觉得是不是很爽

21
00:00:43,780 --> 00:00:46,080
好 那我们就来测试一下 到底是怎么回事

22
00:00:46,080 --> 00:00:49,920
好 那么假如说我们要拴一条数据

23
00:00:49,920 --> 00:00:51,720
咱们先去数据库里面去拿一个id

24
00:00:51,720 --> 00:00:55,820
好 比如说我们要把这条最后一条数据给拴掉 咱们把id给copy

25
00:00:55,820 --> 00:00:57,100
好 那么

26
00:00:57,860 --> 00:00:59,680
那么刷处操作我们是不是要进入哪里啊

27
00:00:59,680 --> 00:01:03,100
是不是要进入我们的poserment去刷呢

28
00:01:03,100 --> 00:01:05,740
因为你浏览器不是很好模拟delete操作吧

29
00:01:05,740 --> 00:01:07,820
好 比如说我们要去进行一个delete

30
00:01:07,820 --> 00:01:09,740
咱们把id给粘贴过来

31
00:01:09,740 --> 00:01:11,200
走你

32
00:01:11,200 --> 00:01:13,820
好 大家可以看到我们的返回值是什么

33
00:01:13,820 --> 00:01:18,180
message ok code 0 data ok 1 delete count 1

34
00:01:18,180 --> 00:01:20,900
说明了我们是不是已经把这条数据给刷出来

35
00:01:20,900 --> 00:01:21,740
那我们来看一下数据库

36
00:01:21,740 --> 00:01:24,260
那么刚才是不是刷了这条

37
00:01:24,260 --> 00:01:26,140
我们刷新一下大家可以看到这条数据其实就没了

38
00:01:26,140 --> 00:01:27,500
说明了我们已经给刷掉了

39
00:01:27,860 --> 00:01:30,380
那么这里呢

40
00:01:30,380 --> 00:01:31,940
再给同学们去演示一下

41
00:01:31,940 --> 00:01:33,140
比如说我们再来剩这一次

42
00:01:33,140 --> 00:01:34,160
是不是再刷一条

43
00:01:34,160 --> 00:01:35,360
刷一条不存在的数据

44
00:01:35,360 --> 00:01:36,660
我们来看一下会发生一件什么事情

45
00:01:36,660 --> 00:01:37,380
走里刷

46
00:01:37,380 --> 00:01:38,880
大家可以看到

47
00:01:38,880 --> 00:01:40,900
其实我们可以通过咱们

48
00:01:40,900 --> 00:01:42,920
是不是咱们通过错误性心里面的一个什么呀

49
00:01:42,920 --> 00:01:43,700
delete count0

50
00:01:43,700 --> 00:01:44,700
发现了我们这条数据

51
00:01:44,700 --> 00:01:45,420
其实并没有刷

52
00:01:45,420 --> 00:01:46,400
因为它已经不存在了

53
00:01:46,400 --> 00:01:46,840
但是呢

54
00:01:46,840 --> 00:01:48,200
其实它并没有报错

55
00:01:48,200 --> 00:01:48,600
是一个OK

56
00:01:48,600 --> 00:01:49,160
所以说呢

57
00:01:49,160 --> 00:01:50,380
对咱们的一个mongos数据库来说

58
00:01:50,380 --> 00:01:51,740
你如果说刷出一条不存在的数据

59
00:01:51,740 --> 00:01:52,400
它不会报错

60
00:01:52,400 --> 00:01:53,780
只是它会提示你这条数据

61
00:01:53,780 --> 00:01:54,680
其实根本就没有

62
00:01:54,680 --> 00:01:55,380
因为他没有酸掉

63
00:01:55,380 --> 00:01:55,840
好

64
00:01:55,840 --> 00:01:57,440
那么这里呢就是我们的一个

65
00:01:57,440 --> 00:01:59,440
酸除操作来给同学们来总结一下

66
00:01:59,440 --> 00:02:00,640
其实它呢非常简单

67
00:02:00,640 --> 00:02:02,260
是不是和我们的查询非常的类似啊

68
00:02:02,260 --> 00:02:03,460
比如说我们对应的是destroy

69
00:02:03,460 --> 00:02:04,820
我们唯一呢就获取id

70
00:02:04,820 --> 00:02:06,280
然后再去酸除吧

71
00:02:06,280 --> 00:02:07,100
那么酸除呢

72
00:02:07,100 --> 00:02:09,400
其实就调用我们model里面的一个remove操作

73
00:02:09,400 --> 00:02:09,600
好

74
00:02:09,600 --> 00:02:11,480
那么这里呢就是我们的一个酸除操作

75
00:02:11,480 --> 00:02:12,800
那么我们讲完酸除之后

76
00:02:12,800 --> 00:02:14,480
是不是只剩最后的一部分内容呢

77
00:02:14,480 --> 00:02:15,040
是什么

78
00:02:15,040 --> 00:02:17,180
是不是数据库的更新呢

79
00:02:17,180 --> 00:02:17,460
好

80
00:02:17,460 --> 00:02:18,060
那么下一节课呢

81
00:02:18,060 --> 00:02:19,420
我们就会进入更新的一个学习

