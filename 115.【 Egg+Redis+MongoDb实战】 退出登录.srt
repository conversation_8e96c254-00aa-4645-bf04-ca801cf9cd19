1
00:00:00,000 --> 00:00:01,400
好,那么其实到这里呢

2
00:00:01,400 --> 00:00:03,260
我们完成了两个接口之后

3
00:00:03,260 --> 00:00:04,760
其实咱们的页面还稍微有点问题

4
00:00:04,760 --> 00:00:05,360
有什么问题呢

5
00:00:05,360 --> 00:00:08,120
比如说我们把页面的一个aptk信先给拴掉

6
00:00:08,120 --> 00:00:10,680
也就是咱们把cookie先给清除掉

7
00:00:10,680 --> 00:00:13,000
大家可以发现我们的页面依然可以访问首页

8
00:00:13,000 --> 00:00:14,460
那么这里是什么原因呢

9
00:00:14,460 --> 00:00:15,480
其实我们少了一个判断

10
00:00:15,480 --> 00:00:17,120
我们来看一下代码

11
00:00:17,120 --> 00:00:20,400
首先我们找到我们那个home.js

12
00:00:20,400 --> 00:00:22,120
也就是咱们首页的一个控制器

13
00:00:22,120 --> 00:00:24,560
少了一行什么代码呢

14
00:00:24,560 --> 00:00:28,780
大家可以看到我们任何情况下

15
00:00:28,780 --> 00:00:29,340
我们的router

16
00:00:29,340 --> 00:00:31,880
好 我们来打开一下router

17
00:00:31,880 --> 00:00:34,140
我们的router在任何情况下

18
00:00:34,140 --> 00:00:35,580
比如说他来调用一个get根目录

19
00:00:35,580 --> 00:00:37,420
也就是用户任何情况下访问咱们的根目录

20
00:00:37,420 --> 00:00:38,520
都可以去renday home

21
00:00:38,520 --> 00:00:39,420
这样是不是不可以啊

22
00:00:39,420 --> 00:00:40,000
为什么呀

23
00:00:40,000 --> 00:00:41,700
我们是不是要给他加上权限了

24
00:00:41,700 --> 00:00:42,220
比如说里面有登录

25
00:00:42,220 --> 00:00:43,380
我就不让你去访问home

26
00:00:43,380 --> 00:00:45,520
如果说你登录了我才能够让你去访问

27
00:00:45,520 --> 00:00:45,840
对吧

28
00:00:45,840 --> 00:00:49,580
需要加上权限判断

29
00:00:49,580 --> 00:00:50,880
好 那么这里如何判断呢

30
00:00:50,880 --> 00:00:51,520
其实非常简单

31
00:00:51,520 --> 00:00:55,360
比如说我们直接去调用ifcontest.session

32
00:00:55,360 --> 00:00:56,480
那么这里的session咱们其实呢

33
00:00:56,480 --> 00:00:58,380
可以去拿到一个login这样一个自端

34
00:00:58,380 --> 00:01:01,080
那么如果说它的login等于

35
00:01:01,080 --> 00:01:03,160
这里其实不用写

36
00:01:03,160 --> 00:01:04,020
咱们直接去yev

37
00:01:04,020 --> 00:01:04,980
直接写点login

38
00:01:04,980 --> 00:01:06,420
然后呢

39
00:01:06,420 --> 00:01:06,860
我们呢就可以

40
00:01:06,860 --> 00:01:08,500
就可以去拿给它去run的home

41
00:01:08,500 --> 00:01:09,140
那么如果说

42
00:01:09,140 --> 00:01:11,700
login自然不存在

43
00:01:11,700 --> 00:01:12,640
或者说呢它为force

44
00:01:12,640 --> 00:01:13,480
说明什么问题

45
00:01:13,480 --> 00:01:14,460
是不是说明你没有登录

46
00:01:14,460 --> 00:01:15,740
或者说呢你过期了

47
00:01:15,740 --> 00:01:15,980
对吧

48
00:01:15,980 --> 00:01:16,880
所以我们呢

49
00:01:16,880 --> 00:01:18,520
需要去把它给重定项到一个

50
00:01:18,520 --> 00:01:20,160
登录页面去

51
00:01:20,160 --> 00:01:20,460
好

52
00:01:20,460 --> 00:01:21,180
那么如果说

53
00:01:21,180 --> 00:01:22,000
这样

54
00:01:22,000 --> 00:01:24,100
我们就完成了咱们首页的一个逻辑

55
00:01:24,100 --> 00:01:24,800
大家可以看一下

56
00:01:24,800 --> 00:01:26,720
比如说此时我们是不是没有登录

57
00:01:26,720 --> 00:01:27,080
对吧

58
00:01:27,080 --> 00:01:28,280
大家可以发现我们访问

59
00:01:28,280 --> 00:01:30,280
首页会自动跳到登陆界面

60
00:01:30,280 --> 00:01:32,280
但是如果说我们登陆

61
00:01:32,280 --> 00:01:34,280
我们登陆一下比如说

62
00:01:34,280 --> 00:01:38,280
传字F1密码123456登陆

63
00:01:38,280 --> 00:01:39,280
大家可以发现登陆失败

64
00:01:39,280 --> 00:01:41,280
我们重新来刷新一下

65
00:01:41,280 --> 00:01:44,280
传字F1密码123456登陆

66
00:01:44,280 --> 00:01:46,280
大家可以看到我们的页面是不是已经进行了登陆

67
00:01:46,280 --> 00:01:47,280
我们来刷新一下页面

68
00:01:47,280 --> 00:01:49,280
我们来登陆它也可以去保持

69
00:01:49,280 --> 00:01:51,280
而且当我们去清除掉我们的一个cookie的时候

70
00:01:51,280 --> 00:01:54,280
大家可以发现我们一刷新自动跳到了一个登陆界面

71
00:01:54,280 --> 00:01:56,280
那么到这里呢其实我们也对咱们首页的判断也完成了

72
00:01:56,280 --> 00:01:58,960
接下来咱们还剩最后一个逻辑没有完成 什么逻辑呢

73
00:01:58,960 --> 00:02:01,160
好 我们再来重新登录一下

74
00:02:01,160 --> 00:02:04,120
123456

75
00:02:04,120 --> 00:02:06,400
我们退出登录这样一个逻辑是不是没有完成

76
00:02:06,400 --> 00:02:08,400
好 接下来我们来看一下如何来完成退出登录

77
00:02:08,400 --> 00:02:09,360
好 我们来看一下代码

78
00:02:09,360 --> 00:02:14,200
首先呢 我们在html模板里面咱们来看一下模板

79
00:02:14,200 --> 00:02:16,600
我们的模板定义了一个A标签

80
00:02:16,600 --> 00:02:18,680
那么呢 当他去访问logout的时候

81
00:02:18,680 --> 00:02:20,400
我们呢 就会去调用退出登录的逻辑

82
00:02:20,400 --> 00:02:21,600
我们来看一下如何去退出登录

83
00:02:21,600 --> 00:02:24,560
好 退出登录

84
00:02:25,680 --> 00:02:30,320
router.get logout

85
00:02:30,320 --> 00:02:33,140
那么logout的时候呢

86
00:02:33,140 --> 00:02:34,100
我们就在控制器里面

87
00:02:34,100 --> 00:02:35,960
同样的咱们去定义一个logout这样的一个方法

88
00:02:35,960 --> 00:02:37,020
logout

89
00:02:37,020 --> 00:02:39,660
好 我们来看一下我们的一个控制器

90
00:02:39,660 --> 00:02:43,620
咱们去定义一个logout这样的一个方法

91
00:02:43,620 --> 00:02:48,540
Async logout

92
00:02:48,540 --> 00:02:51,540
好 大家思考一下

93
00:02:51,540 --> 00:02:53,080
logout咱们应该如何去写

94
00:02:53,080 --> 00:02:54,440
大家想一下

95
00:02:55,680 --> 00:02:57,680
是不是好像突然没头续啊

96
00:02:57,680 --> 00:02:58,680
对吧

97
00:02:58,680 --> 00:03:00,680
我们刚才讲了如何去注册 如何去登陆

98
00:03:00,680 --> 00:03:01,680
大家听得很high

99
00:03:01,680 --> 00:03:03,680
但突然遇到logout 我们如何退出登陆呢

100
00:03:03,680 --> 00:03:06,680
有的同学可能会认为 诶 我能不能把事情拴掉

101
00:03:06,680 --> 00:03:07,680
这当然也是一种方法

102
00:03:07,680 --> 00:03:08,680
但是呢 我们这里有一种更简单的方法

103
00:03:08,680 --> 00:03:09,680
怎么来了

104
00:03:09,680 --> 00:03:11,680
好 大家看一下 非常简单

105
00:03:11,680 --> 00:03:12,680
比如说我们首先会用context

106
00:03:12,680 --> 00:03:15,680
我们只需要去 要用context的点

107
00:03:15,680 --> 00:03:17,680
c信点

108
00:03:17,680 --> 00:03:19,680
login 我们把它改为force

109
00:03:19,680 --> 00:03:21,680
是不是可以 意味着我们的一个登陆已经退出了

110
00:03:21,680 --> 00:03:21,680
对吧

111
00:03:21,680 --> 00:03:23,680
好 那么当我们退出登陆之后

112
00:03:23,680 --> 00:03:26,820
登录之后 我们需要把我们的一个页面重定向到我们的一个

113
00:03:26,820 --> 00:03:32,100
login 也是重定向到我们的一个登录页

114
00:03:32,100 --> 00:03:36,280
好 这里呢 我们来验证一下退出登录到底实现了没有

115
00:03:36,280 --> 00:03:38,200
好 我们来进入咱们的一个页面

116
00:03:38,200 --> 00:03:40,340
好 这里呢 其实这里呢 是我们正常访问的页面

117
00:03:40,340 --> 00:03:41,720
那么如果说我这里点击退出登录

118
00:03:41,720 --> 00:03:43,280
好 我们再来访问首页

119
00:03:43,280 --> 00:03:46,760
好 大家可以看到我们页面是不是已经无法访问了呀

120
00:03:46,760 --> 00:03:48,500
那么如果说你想访问是不是必须要登录

121
00:03:48,500 --> 00:03:52,120
好 那么到这里呢 其实已经完成了我们的一个整个项目

122
00:03:52,120 --> 00:03:53,660
这里就是我们这几个内容

123
00:03:53,660 --> 00:03:55,960
下几个我们将对整个项目进行一个总结

