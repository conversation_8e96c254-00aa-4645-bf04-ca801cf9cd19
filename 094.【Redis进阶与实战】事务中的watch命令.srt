1
00:00:00,440 --> 00:00:03,940
好 这节课我们就来看一下事物中的一个沃起命令

2
00:00:03,940 --> 00:00:07,140
我们首先已经知道在事物中是不是所有的命令

3
00:00:07,140 --> 00:00:09,080
它都需要依次进行完之后

4
00:00:09,080 --> 00:00:11,660
才能够去得到一个返回的结果

5
00:00:11,660 --> 00:00:12,840
而且呢要正确的去执行

6
00:00:12,840 --> 00:00:14,620
那么沃起命令它是做什么的呢

7
00:00:14,620 --> 00:00:16,440
它可以去监控一个或者多个键

8
00:00:16,440 --> 00:00:17,980
一旦有其中的一个键被修改

9
00:00:17,980 --> 00:00:19,280
之后的事物就不会执行

10
00:00:19,280 --> 00:00:20,280
什么意思

11
00:00:20,280 --> 00:00:23,520
我们来看一下沃起这样一个命令怎么去使用

12
00:00:23,520 --> 00:00:26,880
首先我们去SET一个

13
00:00:26,880 --> 00:00:29,180
咱们去SET一个值

14
00:00:29,180 --> 00:00:30,580
比如说叫做

15
00:00:30,580 --> 00:00:34,380
嗯 就叫卧气啊 不叫卧气

16
00:00:34,380 --> 00:00:35,780
咱们就叫

17
00:00:35,780 --> 00:00:40,280
就叫卧气吧 卧气

18
00:00:40,280 --> 00:00:43,980
run number of 阿滚座

19
00:00:43,980 --> 00:00:46,080
哦 没有指咱们 set 卧气

20
00:00:46,780 --> 00:00:49,600
好喽好我们呢设定一个字段莫其一叫做好喽

21
00:00:49,600 --> 00:00:51,900
那么呢我们来

22
00:00:51,900 --> 00:00:53,180
卧起一下他

23
00:00:53,180 --> 00:00:54,200
卧起一下他

24
00:00:54,200 --> 00:00:57,020
怎么来卧起卧起一这样一个字段有点别扭是吧

25
00:00:57,020 --> 00:00:59,580
说说不想取这样一个名字好我们来卧起卧起一

26
00:00:59,580 --> 00:01:05,460
刚才我们是不是有段描述他可以监控一个或多个箭一旦其中有一个植被修改之后的事物就不会执行

27
00:01:05,460 --> 00:01:07,260
那我就来修改一下他说一旦

28
00:01:07,260 --> 00:01:08,280
一旦有一个植被修改

29
00:01:08,280 --> 00:01:09,060
修改一下

30
00:01:09,060 --> 00:01:10,340
我们来set

31
00:01:10,340 --> 00:01:11,620
卧起一为

32
00:01:11,620 --> 00:01:13,400
哈喽一

33
00:01:13,400 --> 00:01:13,920
好

34
00:01:13,920 --> 00:01:15,200
我们是不是改了呀

35
00:01:15,200 --> 00:01:15,960
我们改了吧

36
00:01:16,220 --> 00:01:17,080
那我们来执行一段事务

37
00:01:17,080 --> 00:01:19,000
目体

38
00:01:19,000 --> 00:01:21,260
在事务里面我们去再次修改

39
00:01:21,260 --> 00:01:23,240
我们去set

40
00:01:23,240 --> 00:01:24,160
我起

41
00:01:24,160 --> 00:01:25,760
hello2

42
00:01:25,760 --> 00:01:27,220
好

43
00:01:27,220 --> 00:01:28,000
咱们退出

44
00:01:28,000 --> 00:01:29,280
exec

45
00:01:29,280 --> 00:01:30,280
大家觉得

46
00:01:30,280 --> 00:01:32,400
大家觉得现在握起它值是什么

47
00:01:32,400 --> 00:01:34,480
如果说按照刚才的描述

48
00:01:34,480 --> 00:01:36,620
它的值是不是应该为hello12

49
00:01:36,620 --> 00:01:38,560
因为一旦其中有一个键被修改

50
00:01:38,560 --> 00:01:39,480
之后的事务就不会执行

51
00:01:39,480 --> 00:01:40,660
也就是咱们后面都不会执行了

52
00:01:40,660 --> 00:01:42,200
咱们在事务中的代码都不会执行

53
00:01:42,200 --> 00:01:44,280
那么我们来验证一下

54
00:01:44,280 --> 00:01:44,580
对不对

55
00:01:44,580 --> 00:01:45,760
get我起

56
00:01:45,760 --> 00:01:46,460
一走

57
00:01:46,460 --> 00:01:46,880
好了

58
00:01:46,880 --> 00:01:47,300
一对吧

59
00:01:47,300 --> 00:01:48,000
所以说呢

60
00:01:48,000 --> 00:01:49,660
我们只要握起过之后

61
00:01:49,660 --> 00:01:50,660
那么再握起

62
00:01:50,660 --> 00:01:52,280
它握起了哪一个键

63
00:01:52,280 --> 00:01:53,120
一旦这个键

64
00:01:53,120 --> 00:01:53,660
它被修改

65
00:01:53,660 --> 00:01:54,980
之后的事物就不会执行

66
00:01:54,980 --> 00:01:57,000
那么它能够解决一个什么问题呢

67
00:01:57,000 --> 00:01:57,940
不知道大家还记得

68
00:01:57,940 --> 00:01:58,560
我们之前是不是

69
00:01:58,560 --> 00:01:59,880
实现一个incr这个函数

70
00:01:59,880 --> 00:02:01,480
也就是加一这样一个函数

71
00:02:01,480 --> 00:02:03,120
当时遇到一个什么问题

72
00:02:03,120 --> 00:02:04,640
是不是遇到一个问题

73
00:02:04,640 --> 00:02:05,740
就是静态啊

74
00:02:05,740 --> 00:02:06,840
你比如说同时多个县程

75
00:02:06,840 --> 00:02:09,100
去进行incr

76
00:02:09,100 --> 00:02:10,820
你明明操作了两次

77
00:02:10,820 --> 00:02:11,760
但是它最终结果只

78
00:02:11,760 --> 00:02:12,580
做了加一

79
00:02:12,580 --> 00:02:12,780
为什么

80
00:02:12,780 --> 00:02:13,960
因为它产生了静态关系

81
00:02:13,960 --> 00:02:14,520
那么我们

82
00:02:14,520 --> 00:02:17,240
那么我们可以通过过期这个命令去解决静态这个问题

83
00:02:17,240 --> 00:02:18,020
好

84
00:02:18,020 --> 00:02:20,060
那么我们就来看一下怎么样通过过期去重构

85
00:02:20,060 --> 00:02:22,500
我们之前的INCL这个函数

86
00:02:22,500 --> 00:02:23,380
好

87
00:02:23,380 --> 00:02:24,320
我们来进入

88
00:02:24,320 --> 00:02:26,840
同样的复制过来

89
00:02:26,840 --> 00:02:31,540
好

90
00:02:31,540 --> 00:02:33,940
首先我们是不是要去重构INCL

91
00:02:33,940 --> 00:02:35,440
好

92
00:02:35,440 --> 00:02:37,040
重构之前我们来回归一下之前的代码

93
00:02:37,040 --> 00:02:40,960
大家看到没有

94
00:02:40,960 --> 00:02:42,120
其实很简单

95
00:02:42,120 --> 00:02:44,280
我们方形的接收一个字段叫做K

96
00:02:44,280 --> 00:02:45,240
首先我们去get

97
00:02:45,240 --> 00:02:46,440
如果说它没有值

98
00:02:46,440 --> 00:02:47,280
咱们给它复值为0

99
00:02:47,280 --> 00:02:47,960
如果说有值

100
00:02:47,960 --> 00:02:49,360
咱们直接给它进行加加

101
00:02:49,360 --> 00:02:50,060
就这么简单

102
00:02:50,060 --> 00:02:51,040
好

103
00:02:51,040 --> 00:02:53,400
那么在这里它会产生静态

104
00:02:53,400 --> 00:02:53,680
对吧

105
00:02:53,680 --> 00:02:54,440
那么我们就来看一下

106
00:02:54,440 --> 00:02:55,780
怎么样去通过work来重构它

107
00:02:55,780 --> 00:02:57,260
首先我们同样的

108
00:02:57,260 --> 00:02:58,880
是不是接收一个参数叫做k

109
00:02:58,880 --> 00:03:00,080
也就是咱们的这段值

110
00:03:00,080 --> 00:03:01,800
首先我们来

111
00:03:01,800 --> 00:03:04,200
首先我们是来执行work

112
00:03:04,200 --> 00:03:05,020
work谁啊

113
00:03:05,020 --> 00:03:06,020
咱们是不是要work它

114
00:03:06,020 --> 00:03:08,540
我们要work它

115
00:03:08,540 --> 00:03:09,260
为什么呀

116
00:03:09,260 --> 00:03:10,940
因为我们的k一旦被修改

117
00:03:10,940 --> 00:03:12,840
一旦

118
00:03:12,840 --> 00:03:17,560
K被修改

119
00:03:17,560 --> 00:03:18,120
就要什么

120
00:03:18,120 --> 00:03:21,220
是不是就要阻止事物的执行

121
00:03:21,220 --> 00:03:21,800
好

122
00:03:21,800 --> 00:03:22,940
首先我们来握起它

123
00:03:22,940 --> 00:03:25,660
然后我们来取一下值

124
00:03:25,660 --> 00:03:28,060
Value等于client.get

125
00:03:28,060 --> 00:03:29,380
咱们来看一下Value是什么

126
00:03:29,380 --> 00:03:31,460
get$k

127
00:03:31,460 --> 00:03:34,160
好

128
00:03:34,160 --> 00:03:35,720
我们同样的是不是需要有个判断

129
00:03:35,720 --> 00:03:36,420
对吧

130
00:03:36,420 --> 00:03:39,900
如果说Value

131
00:03:39,900 --> 00:03:41,220
没有值

132
00:03:41,220 --> 00:03:42,500
我们是不是要给它复值为0

133
00:03:42,500 --> 00:03:43,760
负责为0

134
00:03:43,760 --> 00:03:44,320
好

135
00:03:44,320 --> 00:03:45,520
接下来

136
00:03:45,520 --> 00:03:47,000
接下来

137
00:03:47,000 --> 00:03:47,380
Else

138
00:03:47,380 --> 00:03:49,100
Else是不是就是有值的情况

139
00:03:49,100 --> 00:03:50,220
那么有值怎么办

140
00:03:50,220 --> 00:03:51,120
有值

141
00:03:51,120 --> 00:03:54,280
有值我们是不是就需要去进入一个事物啊

142
00:03:54,280 --> 00:03:54,940
来防止静态

143
00:03:54,940 --> 00:03:56,100
对吧

144
00:03:56,100 --> 00:03:58,460
怎么进入静态

145
00:03:58,460 --> 00:03:59,160
Client点

146
00:03:59,160 --> 00:04:00,000
Booting

147
00:04:00,000 --> 00:04:00,600
好

148
00:04:00,600 --> 00:04:01,460
我们进入了一个事物

149
00:04:01,460 --> 00:04:03,020
然后我们在事物里面去

150
00:04:03,020 --> 00:04:04,540
Set

151
00:04:04,540 --> 00:04:05,740
Set什么

152
00:04:05,740 --> 00:04:08,060
是不是咱们去Set咱们的

153
00:04:08,060 --> 00:04:10,460
多了K啊

154
00:04:10,460 --> 00:04:10,900
给他一个值

155
00:04:10,900 --> 00:04:11,220
叫做

156
00:04:11,220 --> 00:04:16,480
对吧

157
00:04:16,480 --> 00:04:17,220
然后呢

158
00:04:17,220 --> 00:04:18,700
首先我们的Value是不是要加加啊

159
00:04:18,700 --> 00:04:19,180
好

160
00:04:19,180 --> 00:04:20,700
然后呢

161
00:04:20,700 --> 00:04:21,940
再执行执行什么

162
00:04:21,940 --> 00:04:22,800
是不是要退出

163
00:04:22,800 --> 00:04:23,820
退出之后

164
00:04:23,820 --> 00:04:24,860
咱们还需要去Return

165
00:04:24,860 --> 00:04:26,100
把咱们的Value给Return出去

166
00:04:26,100 --> 00:04:26,920
这里呢

167
00:04:26,920 --> 00:04:29,320
咱们就通过咱们的Value去重构了这样一个

168
00:04:29,320 --> 00:04:32,080
用Value去重构了咱们的

169
00:04:32,080 --> 00:04:34,020
这样一个INCIA的一个函数

170
00:04:34,020 --> 00:04:35,080
那么我们来分析一下

171
00:04:35,080 --> 00:04:37,740
为什么这样它能够去避免我们的静态

172
00:04:37,740 --> 00:04:38,340
好

173
00:04:38,340 --> 00:04:38,920
首先

174
00:04:38,920 --> 00:04:42,560
我们来画一幅图来分析一下

175
00:04:42,560 --> 00:04:50,740
首先我们假如说

176
00:04:50,740 --> 00:04:52,700
此时此时假如说我们有两个人

177
00:04:52,700 --> 00:04:53,400
一个A

178
00:04:53,400 --> 00:04:55,460
一个B

179
00:04:55,460 --> 00:04:58,980
他首先假如说我们两个课端去连接的时候

180
00:04:58,980 --> 00:04:59,940
他是不是有个先后顺序

181
00:04:59,940 --> 00:05:00,320
比如说A

182
00:05:00,320 --> 00:05:01,440
A先进来

183
00:05:01,440 --> 00:05:02,100
INCR

184
00:05:02,100 --> 00:05:03,820
INCR进来

185
00:05:03,820 --> 00:05:05,220
首先K是不是被握起了

186
00:05:05,220 --> 00:05:05,560
同学们

187
00:05:05,560 --> 00:05:07,120
K被握起了

188
00:05:07,120 --> 00:05:08,660
首先我们A执行的时候

189
00:05:08,660 --> 00:05:10,840
k已经被握起了

190
00:05:10,840 --> 00:05:11,060
好

191
00:05:11,060 --> 00:05:11,740
那么假设

192
00:05:11,740 --> 00:05:12,640
假设我们的弯率有值

193
00:05:12,640 --> 00:05:13,580
假设我们的弯率有值

194
00:05:13,580 --> 00:05:16,080
我们是不是需要走到client点

195
00:05:16,080 --> 00:05:16,860
set

196
00:05:16,860 --> 00:05:18,580
那么我们a执行的时候

197
00:05:18,580 --> 00:05:20,320
set它能不能实在成功

198
00:05:20,320 --> 00:05:21,280
可以吧

199
00:05:21,280 --> 00:05:21,540
为什么

200
00:05:21,540 --> 00:05:22,880
因为我们之前是不是讲过

201
00:05:22,880 --> 00:05:24,300
我们握起了某一个字乱

202
00:05:24,300 --> 00:05:26,260
只有它被修改过之后

203
00:05:26,260 --> 00:05:27,700
后面的事物才不能够去执行

204
00:05:27,700 --> 00:05:29,280
那么此时a去执行的时候

205
00:05:29,280 --> 00:05:31,720
咱们的握起了k这样一个字乱

206
00:05:31,720 --> 00:05:32,560
它有没有被修改过

207
00:05:32,560 --> 00:05:33,900
还没有吧

208
00:05:33,900 --> 00:05:35,120
所以说a它是第一次修改

209
00:05:35,120 --> 00:05:36,580
所以这次是可以成功的

210
00:05:36,580 --> 00:05:36,780
好

211
00:05:36,780 --> 00:05:37,940
此时a执行成功退出

212
00:05:37,940 --> 00:05:39,220
那么同时

213
00:05:39,220 --> 00:05:40,580
同时

214
00:05:40,580 --> 00:05:42,540
B是不是又来了

215
00:05:42,540 --> 00:05:43,840
B是不是又来了

216
00:05:43,840 --> 00:05:45,940
那么B过来的时候

217
00:05:45,940 --> 00:05:47,780
咱们B执行的时候

218
00:05:47,780 --> 00:05:50,720
K是不是已经被握起了

219
00:05:50,720 --> 00:05:51,860
K被握起了

220
00:05:51,860 --> 00:05:53,620
那么他再次去执行事务的时候

221
00:05:53,620 --> 00:05:54,700
B

222
00:05:54,700 --> 00:05:57,260
B他的set能不能成功

223
00:05:57,260 --> 00:05:58,700
不能吧

224
00:05:58,700 --> 00:05:59,140
为什么

225
00:05:59,140 --> 00:06:01,340
又被A握起了

226
00:06:01,340 --> 00:06:02,180
那么B再去执行

227
00:06:02,180 --> 00:06:03,160
是不是他就设置不成功

228
00:06:03,160 --> 00:06:04,800
也就是避免了我们一个静态的关系

229
00:06:04,800 --> 00:06:05,440
所以呢

230
00:06:05,440 --> 00:06:06,480
通过握起

231
00:06:06,480 --> 00:06:08,600
它可以去解决我们的一个静态的问题

232
00:06:08,600 --> 00:06:10,020
当然在Redis里面

233
00:06:10,020 --> 00:06:10,760
它解决这个问题

234
00:06:10,760 --> 00:06:12,900
实际上比我这里写的会复杂很多

235
00:06:12,900 --> 00:06:14,900
因为我这里有很多的一些边界情况都没有去处理

236
00:06:14,900 --> 00:06:16,500
这里只是给同学们一个示意

237
00:06:16,500 --> 00:06:18,480
我们可以通过Watch去解决这个问题

238
00:06:18,480 --> 00:06:20,880
这是我们去思考这样一个问题的思路

239
00:06:20,880 --> 00:06:21,620
好

240
00:06:21,620 --> 00:06:24,480
这节课我们是不是主要讲了

241
00:06:24,480 --> 00:06:26,780
Watch这样一个命令呢

242
00:06:26,780 --> 00:06:29,220
是不是讲了Watch

243
00:06:29,220 --> 00:06:30,640
Watch它是做什么用的

244
00:06:30,640 --> 00:06:33,420
是不是去监测一个字段呢

245
00:06:33,420 --> 00:06:36,220
只要被修改

246
00:06:36,220 --> 00:06:39,220
之后后续的事物将

247
00:06:39,220 --> 00:06:41,300
什么是不是无法

248
00:06:41,300 --> 00:06:44,260
无法执行了

249
00:06:44,260 --> 00:06:46,640
好这里呢就是我们这一节课的内容

