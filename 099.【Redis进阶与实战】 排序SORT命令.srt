1
00:00:00,260 --> 00:00:01,800
好 这节课我们就来看一下

2
00:00:01,800 --> 00:00:03,080
Redis里面的排序

3
00:00:03,080 --> 00:00:05,880
之前我们有没有学习过排序的一个操作

4
00:00:05,880 --> 00:00:08,440
好像没有吧

5
00:00:08,440 --> 00:00:11,260
比如说我们哪些数据类型可以去排序的同学们可以思考一下

6
00:00:11,260 --> 00:00:12,800
游戏结合吧

7
00:00:12,800 --> 00:00:14,080
因为它本身就是有顺序的

8
00:00:14,080 --> 00:00:15,360
所以你肯定可以对它进去排序

9
00:00:15,360 --> 00:00:16,380
包括列表

10
00:00:16,380 --> 00:00:17,400
列表怎么排序的

11
00:00:17,400 --> 00:00:18,440
是不是通过我们

12
00:00:18,440 --> 00:00:20,220
R push 或者R push 一个顺序

13
00:00:20,220 --> 00:00:21,500
因为它本身就是一个一个去

14
00:00:21,500 --> 00:00:23,040
它本身就是有顺序

15
00:00:23,040 --> 00:00:24,580
所以说我们列表也是有顺序

16
00:00:24,580 --> 00:00:27,400
但是我们这节课来给同学们去介绍一种新的排序方法

17
00:00:27,400 --> 00:00:28,420
Sort

18
00:00:28,420 --> 00:00:29,700
那么Sort它怎么样

19
00:00:29,960 --> 00:00:30,720
进行排序呢

20
00:00:30,720 --> 00:00:31,880
比如说我们举例子

21
00:00:31,880 --> 00:00:33,020
对列表进行排序

22
00:00:33,020 --> 00:00:34,000
好

23
00:00:34,000 --> 00:00:35,100
我们首先去定义一个

24
00:00:35,100 --> 00:00:38,620
我们去l push一个mylist

25
00:00:38,620 --> 00:00:40,300
我们给它随便给吧

26
00:00:40,300 --> 00:00:43,220
46273

27
00:00:43,220 --> 00:00:44,620
这里呢

28
00:00:44,620 --> 00:00:46,540
我们添加了一个列表

29
00:00:46,540 --> 00:00:47,160
里面有五个字

30
00:00:47,160 --> 00:00:48,880
那么我们来对它进行排序

31
00:00:48,880 --> 00:00:49,600
我们直接sort

32
00:00:49,600 --> 00:00:52,600
sort mylist

33
00:00:52,600 --> 00:00:53,100
好

34
00:00:53,100 --> 00:00:53,580
大家可以看到

35
00:00:53,580 --> 00:00:54,320
其实呢

36
00:00:54,320 --> 00:00:55,100
此时已经排序成功了

37
00:00:55,100 --> 00:00:56,260
它是从小到大去排序了

38
00:00:56,260 --> 00:00:57,640
23467

39
00:00:57,640 --> 00:00:58,860
好

40
00:00:58,860 --> 00:01:00,660
这里呢是sort对咱们列表的一个排序

41
00:01:00,660 --> 00:01:02,040
那么我们就来接下来看一下

42
00:01:02,040 --> 00:01:04,400
sort它对有序集合的一个排序

43
00:01:04,400 --> 00:01:07,060
好 我们来可利用一下

44
00:01:07,060 --> 00:01:09,340
我们来创建一个有序集合

45
00:01:09,340 --> 00:01:09,860
有序集合

46
00:01:09,860 --> 00:01:11,420
z add

47
00:01:11,420 --> 00:01:12,820
比如说my set

48
00:01:12,820 --> 00:01:14,940
一个值是不是它的分数啊

49
00:01:14,940 --> 00:01:15,520
比如说50分

50
00:01:15,520 --> 00:01:17,160
咱们见值呢叫做

51
00:01:17,160 --> 00:01:18,640
见值叫2

52
00:01:18,640 --> 00:01:20,080
它的属性

53
00:01:20,080 --> 00:01:20,820
它的值为2

54
00:01:20,820 --> 00:01:21,820
但是它的分是50

55
00:01:21,820 --> 00:01:23,040
同学们不要搞反了

56
00:01:23,040 --> 00:01:24,580
再来分数40

57
00:01:24,580 --> 00:01:25,580
值为3

58
00:01:25,580 --> 00:01:26,580
分数20

59
00:01:26,580 --> 00:01:27,300
值为1

60
00:01:27,300 --> 00:01:28,140
分数60

61
00:01:28,140 --> 00:01:28,660
值为5

62
00:01:28,660 --> 00:01:33,620
好 这里呢 我们添加了4个有序集合 那么我们来对它sort一下 看会返回一个什么样的结果

63
00:01:33,620 --> 00:01:36,520
sort my set 好 大家可以看到

64
00:01:36,520 --> 00:01:44,500
它排序也是 根据什么呀 是不是根据我们有序列表的值啊 他sort 他有没有关注咱们有序集合他的一个

65
00:01:44,500 --> 00:01:49,060
分数 好像没有吧 他把分数是忽略了的 所以我们可以得出结论

66
00:01:49,060 --> 00:01:54,220
在对有序集合类型排序时会忽略元素的分数 只针对元素自身的指数进行排序

67
00:01:54,220 --> 00:01:56,420
好 那么我们就来看一下

68
00:01:56,900 --> 00:01:58,440
对非数字类型怎么去排序

69
00:01:58,440 --> 00:02:00,480
比如说我们去l 复习一个

70
00:02:00,480 --> 00:02:02,020
my list

71
00:02:02,020 --> 00:02:03,300
使均代表了字不穿类型

72
00:02:03,300 --> 00:02:04,580
我们来添加

73
00:02:04,580 --> 00:02:07,900
acfgykb

74
00:02:07,900 --> 00:02:10,220
我们随便添加一些英文字母

75
00:02:10,220 --> 00:02:12,260
那么我们来对它排序看一下

76
00:02:12,260 --> 00:02:13,540
sort

77
00:02:13,540 --> 00:02:15,580
my list

78
00:02:15,580 --> 00:02:16,100
str

79
00:02:16,100 --> 00:02:17,380
大家可以看到其实已经报错了

80
00:02:17,380 --> 00:02:17,640
error

81
00:02:17,640 --> 00:02:20,200
one or more score can be converted into double

82
00:02:20,200 --> 00:02:20,700
什么意思呢

83
00:02:20,700 --> 00:02:21,740
它的意思就是说

84
00:02:21,740 --> 00:02:23,780
你sort一定要根据数字去排序

85
00:02:23,780 --> 00:02:25,060
但是呢你是字不穿

86
00:02:25,060 --> 00:02:26,080
它没法对底性排序

87
00:02:26,080 --> 00:02:27,620
那么怎么去解决这个问题呢

88
00:02:27,620 --> 00:02:29,400
我们可以去加入一个参数

89
00:02:29,400 --> 00:02:30,180
叫做

90
00:02:30,180 --> 00:02:30,940
阿尔法

91
00:02:30,940 --> 00:02:31,960
那么加入这个参数了

92
00:02:31,960 --> 00:02:33,760
咱们的sort就可以根据字符串去排序

93
00:02:33,760 --> 00:02:35,040
那么他的排序规则是什么呢

94
00:02:35,040 --> 00:02:36,060
我们先来看一下结果

95
00:02:36,060 --> 00:02:37,860
sort

96
00:02:37,860 --> 00:02:46,560
sort minstr

97
00:02:46,560 --> 00:02:47,320
是不加入

98
00:02:47,320 --> 00:02:48,360
阿尔法

99
00:02:48,360 --> 00:02:49,380
好大家可以看到

100
00:02:49,380 --> 00:02:50,400
a b f g g y

101
00:02:50,400 --> 00:02:52,440
是不是根据我们26英文这么的排序

102
00:02:52,440 --> 00:02:53,480
顺序排列

103
00:02:53,480 --> 00:02:55,260
那么其实呢他是根据我们的一个

104
00:02:55,520 --> 00:03:00,640
字幕创的编码,我们之前是不是讲过字幕创的编码,它根据字幕创的编码顺序来进行排序

105
00:03:00,640 --> 00:03:05,760
而且呢,我们不仅可以,大家可以发现我们不管是对数字排序还是对字幕排序都是一个

106
00:03:05,760 --> 00:03:09,600
从小到大的一个升序排序吧,那么我们来看一下怎么样去逆序呢

107
00:03:09,600 --> 00:03:14,460
怎么样从大到小去排序,其实这里还有一个参数,要做DESC,你比如说我们去

108
00:03:14,460 --> 00:03:17,280
Sort,Sort他,SortMySet

109
00:03:17,280 --> 00:03:19,580
SortMySet,我们加入一个参数

110
00:03:19,580 --> 00:03:20,860
DESC

111
00:03:20,860 --> 00:03:24,700
大家看到我们现在是不是已经实现了从大到小去排序

112
00:03:24,960 --> 00:03:28,100
而且呢你不仅可以去排序所有的字路串

113
00:03:28,100 --> 00:03:30,820
你还可以了对他进行减切

114
00:03:30,820 --> 00:03:32,720
比如说我只想显示前两名是什么

115
00:03:32,720 --> 00:03:35,060
假如说我们去sort my set dsc

116
00:03:35,060 --> 00:03:36,520
我们还可以去加入一个limit参数

117
00:03:36,520 --> 00:03:38,900
前两名怎么写什么01啊好

118
00:03:38,900 --> 00:03:40,060
5

119
00:03:40,060 --> 00:03:43,960
02不好意思02大家可以看到

120
00:03:43,960 --> 00:03:47,220
我们的limit02就是咱们的53前两名对吧好

121
00:03:47,220 --> 00:03:48,420
这里呢就是我们

122
00:03:48,420 --> 00:03:52,760
这节奏的内容我们来回顾一下刚才所讲解的这种方式

123
00:03:54,960 --> 00:04:04,740
好 我们刚才是不是讲解了咱们的redis里面一种新的排序方式

124
00:04:04,740 --> 00:04:09,240
新的排序方式叫做什么呀 是不是sort呀

125
00:04:09,240 --> 00:04:11,140
那么sort它可以给谁排序

126
00:04:11,140 --> 00:04:13,540
可以给列表吧 也可以给有序集合

127
00:04:13,540 --> 00:04:17,560
而且呢 它既可以根据数字排序 又可以根据字图创

128
00:04:17,560 --> 00:04:19,160
数字字图创

129
00:04:19,160 --> 00:04:21,520
那么字图创是根据什么去排序 是不是编码呀

130
00:04:21,520 --> 00:04:25,440
而且我们还可以通过DESC去进行

131
00:04:25,440 --> 00:04:28,440
什么 是不是进行升序还是降序

132
00:04:28,440 --> 00:04:32,020
因为我们从大到小 所以是降序去排序

133
00:04:32,020 --> 00:04:34,400
那我们还讲了什么

134
00:04:34,400 --> 00:04:39,580
是不是还可以通过Limit来展示一部分排序

135
00:04:39,580 --> 00:04:41,840
因为我们可能你比如说我们去展示学生成绩

136
00:04:41,840 --> 00:04:43,240
你不需要所有的人都展示出来

137
00:04:43,240 --> 00:04:45,320
比如说我要展示前三 我们就可以用到Limit

138
00:04:45,320 --> 00:04:48,100
好 这里就是我们这节课的内容

