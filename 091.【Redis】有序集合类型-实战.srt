1
00:00:00,000 --> 00:00:01,220
好,这节课我们就来看一下

2
00:00:01,220 --> 00:00:04,220
我们通过有序即可去实现一个demo的例子

3
00:00:04,220 --> 00:00:05,160
去实现一个什么呢

4
00:00:05,160 --> 00:00:06,920
去实现一个按点击量排名

5
00:00:06,920 --> 00:00:09,720
我们平时去看一些阅读网站的时候

6
00:00:09,720 --> 00:00:12,820
是不是经常遇到按点击排名的一些需求

7
00:00:12,820 --> 00:00:13,560
那我们就来看一下

8
00:00:13,560 --> 00:00:15,020
在Redis里面我们怎么样去存储

9
00:00:15,020 --> 00:00:16,260
这样的一种数据结构

10
00:00:16,260 --> 00:00:23,420
好,我们还是老规矩

11
00:00:23,420 --> 00:00:25,480
先去引读我们的Redis

12
00:00:25,480 --> 00:00:27,900
和咱们去实力化一个客户端

13
00:00:27,900 --> 00:00:29,220
好

14
00:00:29,220 --> 00:00:31,520
首先我们要去需求是什么呀

15
00:00:31,520 --> 00:00:37,460
需求是不是实现点击量排名

16
00:00:37,460 --> 00:00:40,980
实现点击量排名

17
00:00:40,980 --> 00:00:43,820
首先我们是不是需要有一个

18
00:00:43,820 --> 00:00:45,240
我们是不是需要有一个

19
00:00:45,240 --> 00:00:48,680
比如说post.page.view

20
00:00:48,680 --> 00:00:50,100
这样一个有序集合

21
00:00:50,100 --> 00:00:51,340
我们才能对它进行排名呢

22
00:00:51,340 --> 00:00:53,160
这里呢我们假设已经有了

23
00:00:53,160 --> 00:00:56,660
假设因为你还要一个一个去存

24
00:00:56,660 --> 00:00:58,760
我们这里就不要一个个去存了

25
00:00:58,760 --> 00:00:59,600
假设我们通过去

26
00:00:59,600 --> 00:01:00,740
jadd

27
00:01:00,740 --> 00:01:01,380
jadd什么呢

28
00:01:01,380 --> 00:01:01,740
jadd他

29
00:01:01,740 --> 00:01:03,800
去实现了

30
00:01:03,800 --> 00:01:04,940
一个

31
00:01:04,940 --> 00:01:06,520
点击量的

32
00:01:06,520 --> 00:01:07,800
有序

33
00:01:07,800 --> 00:01:08,720
集合

34
00:01:08,720 --> 00:01:09,380
我们这里呢

35
00:01:09,380 --> 00:01:11,780
假设咱们已经拥有了postpage.view

36
00:01:11,780 --> 00:01:12,720
这样一个有序集合

37
00:01:12,720 --> 00:01:13,760
好

38
00:01:13,760 --> 00:01:15,120
那么我们拥有这样一个有序集合之后

39
00:01:15,120 --> 00:01:16,740
我们是不是需要去按照点击量去排名

40
00:01:16,740 --> 00:01:18,220
那么这里需要注意哪些问题呢

41
00:01:18,220 --> 00:01:18,840
人第一个

42
00:01:18,840 --> 00:01:20,600
咱们的排名

43
00:01:20,600 --> 00:01:21,920
比如说你排名前十

44
00:01:21,920 --> 00:01:22,680
但是你

45
00:01:22,680 --> 00:01:24,860
但是你总共的数据肯定不止十条吧

46
00:01:24,860 --> 00:01:26,720
所以我们需要去完成一个什么功能呢

47
00:01:26,720 --> 00:01:27,740
需要

48
00:01:27,740 --> 00:01:28,660
分页

49
00:01:28,660 --> 00:01:30,580
需要分页

50
00:01:30,580 --> 00:01:32,580
那么分页我们可以把之前的代码

51
00:01:32,580 --> 00:01:33,460
直接给抄过来

52
00:01:33,460 --> 00:01:36,040
你比如说我们之前是不是实现过分页

53
00:01:36,040 --> 00:01:36,840
在这里

54
00:01:36,840 --> 00:01:38,500
好

55
00:01:38,500 --> 00:01:41,460
那么我们去实现分页的时候

56
00:01:41,460 --> 00:01:43,240
你是不是需要知道当期的页码是多少

57
00:01:43,240 --> 00:01:44,680
包括你一页有多少条数据

58
00:01:44,680 --> 00:01:46,700
然后它的一个开始和结束

59
00:01:46,700 --> 00:01:47,840
它的index是多少

60
00:01:47,840 --> 00:01:49,660
当我们知道这样一些参数的时候

61
00:01:49,660 --> 00:01:52,560
我们此时是不是直接进入咱们的有序集合去取

62
00:01:52,560 --> 00:01:54,400
比如说我们去挖一个post

63
00:01:54,400 --> 00:01:56,440
id等于什么呢

64
00:01:56,440 --> 00:01:57,240
client点

65
00:01:57,240 --> 00:01:58,020
怎么样去取

66
00:01:58,020 --> 00:01:58,560
有序集合

67
00:01:58,560 --> 00:01:59,160
取一个范围

68
00:01:59,160 --> 00:02:00,260
取一个范围

69
00:02:00,260 --> 00:02:02,740
我们此时是按什么排序啊

70
00:02:02,740 --> 00:02:03,800
一般咱们的排名

71
00:02:03,800 --> 00:02:06,660
是不是要按照降序排列

72
00:02:06,660 --> 00:02:11,680
为什么呀

73
00:02:11,680 --> 00:02:13,600
因为你分数最高的肯定在前面嘛

74
00:02:13,600 --> 00:02:15,020
所以降序排列的命运是什么

75
00:02:15,020 --> 00:02:15,440
还记不记得

76
00:02:15,440 --> 00:02:16,080
J

77
00:02:16,080 --> 00:02:17,320
Runge吗

78
00:02:17,320 --> 00:02:18,560
不是吧

79
00:02:18,560 --> 00:02:19,600
JRUVRunge

80
00:02:19,600 --> 00:02:21,100
咱们Runge谁呢

81
00:02:21,100 --> 00:02:22,540
是不是Runge我们刚才定义好的

82
00:02:22,540 --> 00:02:23,620
Post Page View

83
00:02:23,620 --> 00:02:25,020
好

84
00:02:25,020 --> 00:02:26,020
此时

85
00:02:26,020 --> 00:02:27,100
去要传入哪些参数

86
00:02:27,100 --> 00:02:30,940
咱们的index spot和我们的end

87
00:02:30,940 --> 00:02:32,680
实时已经通过一个

88
00:02:32,680 --> 00:02:34,820
咱们已经在有序集合中

89
00:02:34,820 --> 00:02:36,800
把我们的值给拿到了

90
00:02:36,800 --> 00:02:38,380
那么拿到之后怎么办

91
00:02:38,380 --> 00:02:39,420
怎么办

92
00:02:39,420 --> 00:02:42,440
我们是不是要去咱们的集合里面

93
00:02:42,440 --> 00:02:43,100
把值取出来

94
00:02:43,100 --> 00:02:44,000
因为你存储的是什么

95
00:02:44,000 --> 00:02:44,860
你存储的是点击量

96
00:02:44,860 --> 00:02:46,560
我们要根据查询出来的id去取值

97
00:02:46,560 --> 00:02:49,440
所以咱们还是

98
00:02:49,440 --> 00:02:52,840
postid.forEach

99
00:02:52,840 --> 00:02:55,760
键头函数

100
00:02:55,760 --> 00:02:57,320
ID

101
00:02:57,320 --> 00:02:59,240
这里代码我们已经写了好几遍了

102
00:02:59,240 --> 00:02:59,480
对吧

103
00:02:59,480 --> 00:03:00,980
client.hgetwall

104
00:03:00,980 --> 00:03:02,520
什么post

105
00:03:02,520 --> 00:03:03,380
多了

106
00:03:03,380 --> 00:03:03,880
ID

107
00:03:03,880 --> 00:03:05,220
然后回来函数

108
00:03:05,220 --> 00:03:07,360
所以把咱们的js对象

109
00:03:07,360 --> 00:03:08,200
给拿到

110
00:03:08,200 --> 00:03:08,520
这里呢

111
00:03:08,520 --> 00:03:08,900
你就可以把

112
00:03:08,900 --> 00:03:09,700
这里呢

113
00:03:09,700 --> 00:03:11,060
你就可以拿到咱们文章的所有的信息

114
00:03:11,060 --> 00:03:12,000
比如说文章的标题

115
00:03:12,000 --> 00:03:13,460
内容等等都可以拿到

116
00:03:13,460 --> 00:03:16,740
好

117
00:03:16,740 --> 00:03:17,160
这里呢

118
00:03:17,160 --> 00:03:18,640
就是咱们通过

119
00:03:18,640 --> 00:03:21,240
通过有趣集合去实现一个按点击量排名

120
00:03:21,240 --> 00:03:22,840
的一个

121
00:03:22,840 --> 00:03:27,080
那我们思考有问题

122
00:03:27,080 --> 00:03:28,180
你假如说

123
00:03:28,180 --> 00:03:29,720
假如说我们需要去实现

124
00:03:29,720 --> 00:03:31,860
此时我们是按点击量去排名

125
00:03:31,860 --> 00:03:34,000
那么实际上在我们平时的需求中

126
00:03:34,000 --> 00:03:36,420
是不是还有一个比较高频的场景

127
00:03:36,420 --> 00:03:36,820
是什么呀

128
00:03:36,820 --> 00:03:38,020
我们需要去实现

129
00:03:38,020 --> 00:03:39,700
按时间排序

130
00:03:39,700 --> 00:03:41,460
你比如说你什么时候发布的

131
00:03:41,460 --> 00:03:42,120
你昨天发布的

132
00:03:42,120 --> 00:03:42,620
今天发布的

133
00:03:42,620 --> 00:03:43,160
前天发布的

134
00:03:43,160 --> 00:03:43,800
上个月发布的

135
00:03:43,800 --> 00:03:44,960
我们需要去按时间去排序

136
00:03:44,960 --> 00:03:45,620
怎么样做呢

137
00:03:45,620 --> 00:03:46,800
我们是不是同样的呀

138
00:03:46,800 --> 00:03:48,040
你比如说你去存储的时候

139
00:03:48,040 --> 00:03:49,840
你可以去专门的

140
00:03:49,840 --> 00:03:51,140
按时间序去存储一个

141
00:03:51,140 --> 00:03:53,780
按时间序存储一个有序集合

142
00:03:53,780 --> 00:03:55,660
有序集合存储什么了

143
00:03:55,660 --> 00:03:58,080
是不是存储我们的

144
00:03:58,080 --> 00:04:00,080
时间戳呀

145
00:04:00,080 --> 00:04:01,380
你把时间戳存进去

146
00:04:01,380 --> 00:04:03,720
到时候我们通过有序集合了

147
00:04:03,720 --> 00:04:04,420
ZREV

148
00:04:04,420 --> 00:04:06,340
ZREV

149
00:04:06,340 --> 00:04:07,660
RUNGE

150
00:04:07,660 --> 00:04:10,680
是不是就可以同样的去根据咱们的时间戳去进行排名了

151
00:04:10,680 --> 00:04:11,380
好

152
00:04:11,380 --> 00:04:13,360
那么这里就是我们这节课的内容

153
00:04:13,360 --> 00:04:14,360
我们来总结一下

154
00:04:14,360 --> 00:04:18,080
这节课我们是不是主要去讲解了一个实现按点击量排名的需求

155
00:04:18,080 --> 00:04:18,820
其实很简单

156
00:04:18,820 --> 00:04:19,540
你需要去

157
00:04:19,540 --> 00:04:20,700
首先你通过

158
00:04:20,700 --> 00:04:22,320
你要是不是要通过zadd

159
00:04:22,320 --> 00:04:24,080
zadd去存储

160
00:04:24,080 --> 00:04:26,220
存储一个咱们点击量的一个有序集合

161
00:04:26,220 --> 00:04:26,920
那么呢

162
00:04:26,920 --> 00:04:28,460
你需要去获取一些分页的参数

163
00:04:28,460 --> 00:04:29,680
咱们根据这些分页的参数

164
00:04:29,680 --> 00:04:31,240
使用降序排列

165
00:04:31,240 --> 00:04:31,540
对吧

166
00:04:31,540 --> 00:04:34,020
因为我们排序一般都是从高到低

167
00:04:34,020 --> 00:04:35,960
然后你通过zivrunge

168
00:04:35,960 --> 00:04:36,420
这样一个命令

169
00:04:36,420 --> 00:04:37,940
去进行咱们有序集合的一个排序

170
00:04:37,940 --> 00:04:39,080
后面的传入它的一个index

171
00:04:39,080 --> 00:04:40,820
就可以得到我们的一个按点击量排序

172
00:04:40,820 --> 00:04:42,680
那么如果说你需要去按时间排序

173
00:04:42,680 --> 00:04:43,160
此时呢

174
00:04:43,160 --> 00:04:44,320
你可以新建一个k

175
00:04:44,320 --> 00:04:44,780
然后呢

176
00:04:44,780 --> 00:04:46,700
把时间错给存为它的一个分数

177
00:04:46,700 --> 00:04:48,780
是不是就可以实现我们按时间排序的一个需求

