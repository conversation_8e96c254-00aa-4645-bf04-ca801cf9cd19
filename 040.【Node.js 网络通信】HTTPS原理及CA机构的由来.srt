1
00:00:00,000 --> 00:00:03,560
在我们中级进修课的第四阶段

2
00:00:03,560 --> 00:00:05,120
第一章的最后一个地方

3
00:00:05,120 --> 00:00:06,680
关于HTPS模块

4
00:00:06,680 --> 00:00:08,320
以及关于我们对称加密

5
00:00:08,320 --> 00:00:09,080
非对称加密

6
00:00:09,080 --> 00:00:11,140
密文明文等一系列的内容

7
00:00:11,140 --> 00:00:12,640
都有一个详细的讲解

8
00:00:12,640 --> 00:00:14,860
那么这里我就针对于咱们前面的讲解

9
00:00:14,860 --> 00:00:16,660
做一个简单的回顾和总结

10
00:00:16,660 --> 00:00:18,400
那什么是HTPS服务器呢

11
00:00:18,400 --> 00:00:20,300
其实它是基于HTPS协议

12
00:00:20,300 --> 00:00:23,300
来对于我们的数据采用TLS和SSR

13
00:00:23,300 --> 00:00:25,200
进行加密之后传输的一种协议

14
00:00:25,200 --> 00:00:26,160
本质上来说

15
00:00:26,160 --> 00:00:28,680
HTPS协议就是HTTP的安全版本

16
00:00:28,680 --> 00:00:30,600
那么到底是怎么加密的呢

17
00:00:30,600 --> 00:00:31,160
请注意

18
00:00:31,160 --> 00:00:34,280
它把数据采用的是对称加密的方式

19
00:00:34,280 --> 00:00:35,780
也就是说你的服务器怎么加密

20
00:00:35,780 --> 00:00:37,740
那么到我的客户端就怎么去解密

21
00:00:37,740 --> 00:00:38,480
而密钥呢

22
00:00:38,480 --> 00:00:39,960
采用的是非对称加密的方式

23
00:00:39,960 --> 00:00:42,040
那么你的服务器是使用一种加密方式

24
00:00:42,040 --> 00:00:43,060
而传到我的客户端呢

25
00:00:43,060 --> 00:00:45,500
用采用另外一种解密方式进行解密

26
00:00:45,500 --> 00:00:46,620
可以在一定程度上呢

27
00:00:46,620 --> 00:00:47,980
保证我们的数据安全性

28
00:00:47,980 --> 00:00:48,380
但是呢

29
00:00:48,380 --> 00:00:50,040
我们没有办法防止中间人的出现

30
00:00:50,040 --> 00:00:51,680
因为服务器可能会被伪造

31
00:00:51,680 --> 00:00:52,580
中间截取后

32
00:00:52,580 --> 00:00:54,200
骗取密钥对数据造成泄露

33
00:00:54,200 --> 00:00:54,760
那么这里呢

34
00:00:54,760 --> 00:00:56,520
给大家做一个简单的一个讲解

35
00:00:56,520 --> 00:00:57,420
正常情况下呢

36
00:00:57,420 --> 00:01:00,020
我们的客户端是向服务器正常发送数据的

37
00:01:00,020 --> 00:01:00,580
那么数据呢

38
00:01:00,580 --> 00:01:01,400
也是经过加密的

39
00:01:01,400 --> 00:01:02,860
我们密钥也是经过加密的

40
00:01:02,860 --> 00:01:03,540
而到服务器那里呢

41
00:01:03,540 --> 00:01:04,480
进行解密解析

42
00:01:04,480 --> 00:01:05,560
然后呢进行数据的处理

43
00:01:05,560 --> 00:01:08,000
然后我们再把服务器的数据进行加密之后呢

44
00:01:08,000 --> 00:01:08,960
返回给客户端

45
00:01:08,960 --> 00:01:10,160
客户端再进行解密

46
00:01:10,160 --> 00:01:11,300
就得到了我们的数据

47
00:01:11,300 --> 00:01:13,480
但是中间人的出现会造成一种情况

48
00:01:13,480 --> 00:01:14,120
什么情况呢

49
00:01:14,120 --> 00:01:15,940
就是我们在客户端发送数据时

50
00:01:15,940 --> 00:01:17,220
是经过了加密的

51
00:01:17,220 --> 00:01:17,640
但是呢

52
00:01:17,640 --> 00:01:19,700
数据会原原本本的传到我们的中间人

53
00:01:19,700 --> 00:01:22,340
也就是他会作为一个福气和我们的客户端存在

54
00:01:22,340 --> 00:01:23,980
但是这个福气实际是一个假的

55
00:01:23,980 --> 00:01:25,300
那么中间人作为福气的时候

56
00:01:25,300 --> 00:01:25,820
拿到数据之后

57
00:01:25,820 --> 00:01:29,060
再会把原原本的数据再传到我们的福气里

58
00:01:29,060 --> 00:01:30,920
而福气已经解密处理之后

59
00:01:30,920 --> 00:01:34,120
依然会把数据再作为客户端返回给我们的中间人

60
00:01:34,120 --> 00:01:36,960
而中间人又会作为福气再返回给我们的客户端

61
00:01:36,960 --> 00:01:37,420
那么说白了

62
00:01:37,420 --> 00:01:38,940
这个中间人就会造成一个什么

63
00:01:38,940 --> 00:01:40,440
就是在客户端的角度来看

64
00:01:40,440 --> 00:01:41,200
它是一个福气

65
00:01:41,200 --> 00:01:42,260
而在福气的角度来看

66
00:01:42,260 --> 00:01:43,240
它是一个客户端

67
00:01:43,240 --> 00:01:45,300
那么它起到了中间拦截数据的一个作用

68
00:01:45,300 --> 00:01:47,900
那么此时对于我们真实的客户团和真实的福气来说

69
00:01:47,900 --> 00:01:49,900
是没有办法感知到中间的存在的

70
00:01:49,900 --> 00:01:51,620
那么此时我们的中间人拿到数据之后

71
00:01:51,620 --> 00:01:52,620
拿到了密要对

72
00:01:52,620 --> 00:01:55,200
那么此时呢就会对数据造成一个泄露

73
00:01:55,200 --> 00:01:56,440
那么为了解决这个问题

74
00:01:56,440 --> 00:01:58,560
那么我们的TCL和SS呢

75
00:01:58,560 --> 00:01:59,860
引入了数字证书认证

76
00:01:59,860 --> 00:02:02,160
那我们的客户团呢在发送请求数据时

77
00:02:02,160 --> 00:02:05,000
发送给的福气需要进行一个认证

78
00:02:05,000 --> 00:02:07,080
那么这个证书认证是包含了哪些信息

79
00:02:07,080 --> 00:02:09,500
才能够保证我们的福气是正确的

80
00:02:09,500 --> 00:02:10,320
是可信任的

81
00:02:10,320 --> 00:02:11,240
是一个安全的服务器呢

82
00:02:11,240 --> 00:02:11,720
请注意

83
00:02:11,720 --> 00:02:14,000
在数字证书中包含了服务器的名称

84
00:02:14,000 --> 00:02:14,760
主机名

85
00:02:14,760 --> 00:02:15,740
服务器的公钥

86
00:02:15,740 --> 00:02:16,920
颁发机构的名称

87
00:02:16,920 --> 00:02:18,820
来自颁发机构的签名

88
00:02:18,820 --> 00:02:20,400
得到了这些数据之后

89
00:02:20,400 --> 00:02:22,160
证明这台服务器是可信任的

90
00:02:22,160 --> 00:02:23,680
那么数据就会进行一个通信

91
00:02:23,680 --> 00:02:24,240
也就是说

92
00:02:24,240 --> 00:02:25,140
在真实的通信之前

93
00:02:25,140 --> 00:02:26,440
我们的客户端是需要对

94
00:02:26,440 --> 00:02:28,180
我要发送数据的这台服务器

95
00:02:28,180 --> 00:02:29,360
进行一个认证的

96
00:02:29,360 --> 00:02:30,400
那这个认证的机构呢

97
00:02:30,400 --> 00:02:31,160
我们把它称为一个

98
00:02:31,160 --> 00:02:32,900
这个第三方的数字认证机构

99
00:02:32,900 --> 00:02:33,860
简称就是C

100
00:02:33,860 --> 00:02:34,840
也就是说

101
00:02:34,840 --> 00:02:37,140
我们的服务器想要做成一个安全的服务器

102
00:02:37,140 --> 00:02:40,140
那必须要拿到CA认证机构颁发的证书

103
00:02:40,140 --> 00:02:42,400
以保证说给客户端说我是安全的

104
00:02:42,400 --> 00:02:45,000
那这就是在已经加密的前提下

105
00:02:45,000 --> 00:02:48,060
为什么还需要CA认证机构来颁发证书的原因

