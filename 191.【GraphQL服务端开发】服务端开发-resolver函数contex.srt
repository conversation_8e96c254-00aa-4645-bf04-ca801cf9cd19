1
00:00:00,000 --> 00:00:02,840
熟悉这resolver函数的第三个参数

2
00:00:02,840 --> 00:00:04,220
也就是contents它的用法之后

3
00:00:04,220 --> 00:00:06,280
我们接下来来对接一下真实的数据源

4
00:00:06,280 --> 00:00:07,920
这里我们选择的是文件

5
00:00:07,920 --> 00:00:09,060
作为我们的数据来源

6
00:00:09,060 --> 00:00:10,920
因为文件的操作相对简单一点

7
00:00:10,920 --> 00:00:12,120
如果要对接数据库

8
00:00:12,120 --> 00:00:14,380
或者要对接第三方的API信号的话

9
00:00:14,380 --> 00:00:16,840
其实这个模式是类似的

10
00:00:16,840 --> 00:00:18,600
然后这我们就选择单身文件

11
00:00:18,600 --> 00:00:19,560
作为我们的数据来源

12
00:00:19,560 --> 00:00:21,500
具体的话我们就封装一个方法

13
00:00:21,500 --> 00:00:21,880
get data

14
00:00:21,880 --> 00:00:24,220
把它直接从模块中导出

15
00:00:24,220 --> 00:00:26,420
然后我们只要导入db的dns

16
00:00:26,420 --> 00:00:27,680
就可以把这个db对象

17
00:00:27,680 --> 00:00:30,680
作为我们的这个context的函数中的

18
00:00:30,680 --> 00:00:31,680
这个数据源的对象了

19
00:00:31,680 --> 00:00:32,760
然后的话

20
00:00:32,760 --> 00:00:34,480
我们就可以在这个resolver函数中

21
00:00:34,480 --> 00:00:36,180
这个直接获取到DB对象

22
00:00:36,180 --> 00:00:37,280
从而我们可以调用

23
00:00:37,280 --> 00:00:38,380
它当中的get data方法

24
00:00:38,380 --> 00:00:39,440
来得到我们真实的数据

25
00:00:39,440 --> 00:00:41,580
从而来返回这个客户端

26
00:00:41,580 --> 00:00:43,420
这是整体的诱务流程

27
00:00:43,420 --> 00:00:44,400
那接下来呢

28
00:00:44,400 --> 00:00:45,760
我们就把这个流程来演示一下

29
00:00:45,760 --> 00:00:47,700
那首先的话呢

30
00:00:47,700 --> 00:00:49,160
我们要准备我们的数据

31
00:00:49,160 --> 00:00:50,240
 data.data

32
00:00:50,240 --> 00:00:51,860
那这样数据的话

33
00:00:51,860 --> 00:00:52,580
我们就准备一下

34
00:00:52,580 --> 00:00:54,520
这个学生相关的信息

35
00:00:54,520 --> 00:00:55,940
比如说有学生的姓名和年龄

36
00:00:55,940 --> 00:00:56,680
这儿呢

37
00:00:56,680 --> 00:00:57,620
我们就准备一个数组的列表

38
00:00:57,620 --> 00:01:00,260
中间是对象

39
00:01:00,260 --> 00:01:01,400
对象的这个件

40
00:01:01,400 --> 00:01:02,900
我们是sname

41
00:01:02,900 --> 00:01:04,680
值的话我们提供一下

42
00:01:04,680 --> 00:01:06,780
然后是年龄

43
00:01:06,780 --> 00:01:09,480
这个值的话我们是12岁

44
00:01:09,480 --> 00:01:11,780
这个对象我们复制两份

45
00:01:11,780 --> 00:01:14,440
然后把这个值

46
00:01:14,440 --> 00:01:15,580
我们给它调整一下

47
00:01:15,580 --> 00:01:16,480
这我们改成

48
00:01:16,480 --> 00:01:17,540
张三

49
00:01:17,540 --> 00:01:18,800
这改成18

50
00:01:18,800 --> 00:01:20,320
下面我们改成王五

51
00:01:20,320 --> 00:01:22,560
最后这个值我们改成15

52
00:01:22,560 --> 00:01:24,320
这样的话我们这个数据就有了

53
00:01:24,320 --> 00:01:25,180
有数据之后

54
00:01:25,180 --> 00:01:26,360
我们接下来来操作这个数据

55
00:01:26,360 --> 00:01:27,620
再创建一个文件

56
00:01:27,620 --> 00:01:28,400
叫db.ds

57
00:01:28,400 --> 00:01:29,840
这里要做的事情

58
00:01:29,840 --> 00:01:32,000
就是操作数据源

59
00:01:32,000 --> 00:01:33,960
这我们就直接封章一个方法

60
00:01:33,960 --> 00:01:34,380
叫get data

61
00:01:34,380 --> 00:01:35,860
然后把这个方法直接导出去

62
00:01:35,860 --> 00:01:36,840
那就是module

63
00:01:36,840 --> 00:01:38,740
expose

64
00:01:38,740 --> 00:01:41,040
然后后边加一个方法

65
00:01:41,040 --> 00:01:41,980
get data

66
00:01:41,980 --> 00:01:44,560
在这个方法里边

67
00:01:44,560 --> 00:01:45,160
我们要做的事情

68
00:01:45,160 --> 00:01:46,300
就是读取文件

69
00:01:46,300 --> 00:01:49,200
既然要读文件

70
00:01:49,200 --> 00:01:50,560
这里边我们就需要两个

71
00:01:50,560 --> 00:01:51,940
node.ds的核心模块

72
00:01:51,940 --> 00:01:53,420
一个是pass

73
00:01:53,420 --> 00:01:54,140
就是入境操作

74
00:01:54,140 --> 00:01:55,180
require进来

75
00:01:55,180 --> 00:01:58,220
然后还需要一个模块是谁呢

76
00:01:58,220 --> 00:01:59,300
是这个文件操作的模块

77
00:01:59,300 --> 00:01:59,740
ff

78
00:01:59,740 --> 00:02:02,200
这里边由于是读文件

79
00:02:02,200 --> 00:02:03,200
也是一步操作

80
00:02:03,200 --> 00:02:04,420
但是它也支持同步

81
00:02:04,420 --> 00:02:05,560
我们这都采用一步的方式

82
00:02:05,560 --> 00:02:07,280
这样的话更加通用

83
00:02:07,280 --> 00:02:08,900
这我们就需要用到promise

84
00:02:08,900 --> 00:02:10,140
所以说比较方便的做法

85
00:02:10,140 --> 00:02:12,200
我们直接就去written一个promise实力的项

86
00:02:12,200 --> 00:02:15,220
这我们提供一下参数

87
00:02:15,220 --> 00:02:16,120
resolve

88
00:02:16,120 --> 00:02:17,200
还有一个是regent

89
00:02:17,200 --> 00:02:21,620
在这个里边我们进行一步的操作

90
00:02:21,620 --> 00:02:23,040
要读文件

91
00:02:23,040 --> 00:02:24,600
我们首先需要提供一下文件的路径

92
00:02:24,600 --> 00:02:25,560
所以说在这个位置

93
00:02:25,560 --> 00:02:26,800
我们先定义一个变量

94
00:02:26,800 --> 00:02:28,340
叫FilePath

95
00:02:28,340 --> 00:02:29,100
这是路径

96
00:02:29,100 --> 00:02:30,260
我们拼接一下

97
00:02:30,260 --> 00:02:31,320
通过Jong

98
00:02:31,320 --> 00:02:33,640
这个DRNIME

99
00:02:33,640 --> 00:02:34,520
然后后面加上这个

100
00:02:34,520 --> 00:02:35,380
数据文件的名称

101
00:02:35,380 --> 00:02:36,020
Gayson

102
00:02:36,020 --> 00:02:38,180
然后在这个位置

103
00:02:38,180 --> 00:02:38,960
就可以直接读取了

104
00:02:38,960 --> 00:02:39,820
读取的方式

105
00:02:39,820 --> 00:02:42,620
通过fs.readFile

106
00:02:42,620 --> 00:02:43,620
第一个参数

107
00:02:43,620 --> 00:02:44,360
是文件的路径

108
00:02:44,360 --> 00:02:45,120
那就是FilePath

109
00:02:45,120 --> 00:02:46,100
然后第二个参数

110
00:02:46,100 --> 00:02:46,600
是一个函数

111
00:02:46,600 --> 00:02:47,660
这里边有两个参数

112
00:02:47,660 --> 00:02:48,120
一个是Error

113
00:02:48,120 --> 00:02:49,920
另外一个是我们读到的数据

114
00:02:49,920 --> 00:02:52,920
这里边要对这个结果进行处理

115
00:02:52,920 --> 00:02:54,280
我们最好先判断一下

116
00:02:54,280 --> 00:02:55,940
如果这个error它是真的

117
00:02:55,940 --> 00:02:57,940
它就证明读取失败了

118
00:02:57,940 --> 00:02:58,360
这边备注

119
00:02:58,360 --> 00:03:00,740
就是读取文件失败

120
00:03:00,740 --> 00:03:02,420
那失败的话怎么办呢

121
00:03:02,420 --> 00:03:02,920
要调用

122
00:03:02,920 --> 00:03:03,740
redent

123
00:03:03,740 --> 00:03:06,960
然后这就给它提供一个error信息

124
00:03:06,960 --> 00:03:09,120
那下边的话就是读取成功

125
00:03:09,120 --> 00:03:11,060
这是成功的

126
00:03:11,060 --> 00:03:12,300
成功之后我们就直接调用

127
00:03:12,300 --> 00:03:12,960
resolve

128
00:03:12,960 --> 00:03:15,020
然后把这数据直接带过去

129
00:03:15,020 --> 00:03:16,880
这个数据的话我们需要显示的转化成

130
00:03:16,880 --> 00:03:17,700
toSpin

131
00:03:17,700 --> 00:03:21,140
好 接下来我们这个读文件的操作就做完了

132
00:03:21,140 --> 00:03:22,740
那做完之后的话我们需要

133
00:03:22,740 --> 00:03:24,500
把这个db.js导进来

134
00:03:24,500 --> 00:03:26,600
作为我们的数据员对象

135
00:03:26,600 --> 00:03:28,000
这样的导进来

136
00:03:28,000 --> 00:03:29,160
应该是db.js

137
00:03:29,160 --> 00:03:30,960
导进来之后

138
00:03:30,960 --> 00:03:31,940
直接把我们之前填充的

139
00:03:31,940 --> 00:03:32,780
这个字符上给它替换掉

140
00:03:32,780 --> 00:03:33,440
变成db

141
00:03:33,440 --> 00:03:34,480
那这样的话呢

142
00:03:34,480 --> 00:03:35,740
我们在这个resover函数中

143
00:03:35,740 --> 00:03:36,740
就可以得到

144
00:03:36,740 --> 00:03:37,820
那个db对象了

145
00:03:37,820 --> 00:03:38,980
从而我们可以间接的调用

146
00:03:38,980 --> 00:03:41,000
get data来得到这个结果

147
00:03:41,000 --> 00:03:41,780
好那在这里呢

148
00:03:41,780 --> 00:03:42,500
我们应该拿到一个结果

149
00:03:42,500 --> 00:03:42,740
ret

150
00:03:42,740 --> 00:03:43,800
它应该等于谁呢

151
00:03:43,800 --> 00:03:44,420
context

152
00:03:44,420 --> 00:03:45,440
然后

153
00:03:45,440 --> 00:03:48,700
调用这个db当中的get data

154
00:03:48,700 --> 00:03:49,940
这样拿到数据

155
00:03:49,940 --> 00:03:51,020
然后呢

156
00:03:51,020 --> 00:03:52,240
在这直接把它返回就可以了

157
00:03:52,240 --> 00:03:54,840
那还有一个细节需要注意的

158
00:03:54,840 --> 00:03:56,020
就是这个gad data它的返回值

159
00:03:56,020 --> 00:03:57,400
实际上是一个promise实际对象

160
00:03:57,400 --> 00:03:59,520
所以说我们为了更加方便的得到这个数据的话

161
00:03:59,520 --> 00:04:01,120
我们可以把这变成a性函数

162
00:04:01,120 --> 00:04:03,680
然后在这个位置加上await

163
00:04:03,680 --> 00:04:05,880
这样就得到了这个ret结果

164
00:04:05,880 --> 00:04:07,400
注意这个结果实际上是一个字幕串

165
00:04:07,400 --> 00:04:10,180
然后保存我们启动服务

166
00:04:10,180 --> 00:04:12,500
直接去运行nodemail

167
00:04:12,500 --> 00:04:14,740
然后标用这个10文件

168
00:04:14,740 --> 00:04:15,420
然后启动

169
00:04:15,420 --> 00:04:17,560
好启动之后的话我们做一个查询

170
00:04:17,560 --> 00:04:18,960
在这我们要查的就是hello

171
00:04:18,960 --> 00:04:21,100
然后点查询

172
00:04:21,100 --> 00:04:22,100
这样我们就得到了

173
00:04:22,100 --> 00:04:23,000
我们需要的这个数据

174
00:04:23,000 --> 00:04:24,100
你看一下这个数据

175
00:04:24,100 --> 00:04:25,540
实际上和我们该的文件中

176
00:04:25,540 --> 00:04:27,080
那个数据是一样的

177
00:04:27,080 --> 00:04:29,060
这里边的这个斜杠2和斜杠N

178
00:04:29,060 --> 00:04:30,020
实际上是回车换航服

179
00:04:30,020 --> 00:04:31,120
其实我们这里边

180
00:04:31,120 --> 00:04:32,060
更希望得到的是一个

181
00:04:32,060 --> 00:04:32,840
对象形式的数据

182
00:04:32,840 --> 00:04:33,860
所以说我们这儿

183
00:04:33,860 --> 00:04:34,720
可以稍微改造一下

184
00:04:34,720 --> 00:04:35,400
把这个Spin

185
00:04:35,400 --> 00:04:36,820
我们给它换成一个列表

186
00:04:36,820 --> 00:04:38,160
然后里边我们放的是

187
00:04:38,160 --> 00:04:38,980
Student

188
00:04:38,980 --> 00:04:41,340
然后这个碳号都给它加上

189
00:04:41,340 --> 00:04:42,160
保证它不空

190
00:04:42,160 --> 00:04:42,920
非空

191
00:04:42,920 --> 00:04:44,640
然后上面我们再定义一个类型

192
00:04:44,640 --> 00:04:45,260
那就是Type

193
00:04:45,260 --> 00:04:46,660
Student

194
00:04:46,660 --> 00:04:49,080
然后有SNAME

195
00:04:49,080 --> 00:04:50,160
这儿是Spin

196
00:04:50,160 --> 00:04:51,420
还有一个年龄

197
00:04:51,420 --> 00:04:52,720
这个我们用inter

198
00:04:52,720 --> 00:04:54,160
好 这类型定义好了

199
00:04:54,160 --> 00:04:55,780
但是这个类型的话

200
00:04:55,780 --> 00:04:56,400
我们需要在这

201
00:04:56,400 --> 00:04:57,160
还要做一个转换

202
00:04:57,160 --> 00:04:58,340
因为它是一个

203
00:04:58,340 --> 00:04:59,540
数组形式的

204
00:04:59,540 --> 00:05:00,100
对象数组

205
00:05:00,100 --> 00:05:01,260
然后这做一个转换

206
00:05:01,260 --> 00:05:02,520
这样做就是obd

207
00:05:02,520 --> 00:05:03,580
那它直接等于

208
00:05:03,580 --> 00:05:05,020
json.path

209
00:05:05,020 --> 00:05:05,900
把这个red

210
00:05:05,900 --> 00:05:06,940
这个组组给它转成对象

211
00:05:06,940 --> 00:05:07,940
然后在这个位置

212
00:05:07,940 --> 00:05:09,040
直接返回的是obd

213
00:05:09,040 --> 00:05:09,920
这就可以了

214
00:05:09,920 --> 00:05:11,080
好 然后的话

215
00:05:11,080 --> 00:05:11,660
我们这个

216
00:05:11,660 --> 00:05:12,400
客户端

217
00:05:12,400 --> 00:05:13,440
也需要再调整一下

218
00:05:13,440 --> 00:05:14,380
你这查的就是

219
00:05:14,380 --> 00:05:15,300
列表中的对象

220
00:05:15,300 --> 00:05:16,280
所以说我们要指定

221
00:05:16,280 --> 00:05:17,220
你要查的是哪个字段

222
00:05:17,220 --> 00:05:17,760
sname

223
00:05:17,760 --> 00:05:19,160
然后加上年龄

224
00:05:19,160 --> 00:05:20,200
然后点查询

225
00:05:20,200 --> 00:05:21,140
这样就得到了

226
00:05:21,140 --> 00:05:23,100
这个对象形式的这个数据

227
00:05:23,100 --> 00:05:24,900
好那到此为止的话呢

228
00:05:24,900 --> 00:05:26,640
我们就把这个数据员的对接

229
00:05:26,640 --> 00:05:27,660
这个工作呢就做完了

230
00:05:27,660 --> 00:05:30,800
好最后呢我们再总结一下

231
00:05:30,800 --> 00:05:32,720
看一下这里边这个context的

232
00:05:32,720 --> 00:05:34,120
这个参数的主要作用

233
00:05:34,120 --> 00:05:35,900
就是用于对接数据员

234
00:05:35,900 --> 00:05:37,480
那这个数据员呢

235
00:05:37,480 --> 00:05:38,400
其实不仅仅包括查询

236
00:05:38,400 --> 00:05:39,040
后边的话呢

237
00:05:39,040 --> 00:05:40,760
我们还会做这个变更的操作

238
00:05:40,760 --> 00:05:42,280
那从使用的流程来说的话呢

239
00:05:42,280 --> 00:05:43,780
我们只需要在这个context当中呢

240
00:05:43,780 --> 00:05:44,660
来提供数据员的对象

241
00:05:44,660 --> 00:05:45,740
然后在resolver当中

242
00:05:45,740 --> 00:05:47,060
来通过对象来调用相关的方法

243
00:05:47,060 --> 00:05:47,740
来处理数据

244
00:05:47,740 --> 00:05:50,480
这是关于context的这个参数的应用场景

245
00:05:50,480 --> 00:05:51,320
我们就说到这里

