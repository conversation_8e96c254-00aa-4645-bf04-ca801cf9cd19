1
00:00:00,000 --> 00:00:04,220
好 那接下来我们就把拿到的真实的证书传到我们的服务器上

2
00:00:04,220 --> 00:00:05,360
我们刚才已经说过了

3
00:00:05,360 --> 00:00:08,160
直接选择使用index的证书就可以了

4
00:00:08,160 --> 00:00:09,780
好 找到我们的fstb

5
00:00:09,780 --> 00:00:12,120
然后把我们的index直接拖到我们的服务器里来

6
00:00:12,120 --> 00:00:14,280
好 那么这里已经显示传输成功

7
00:00:14,280 --> 00:00:15,500
那么我们打开命令号

8
00:00:15,500 --> 00:00:16,720
然后敲击ls

9
00:00:16,720 --> 00:00:18,480
来 我们看一下index已经有了

10
00:00:18,480 --> 00:00:19,940
我们用数照结构也来查看一下

11
00:00:19,940 --> 00:00:22,420
那么index里面包含了两个就是我们的描述文件

12
00:00:22,420 --> 00:00:28,780
之前我们的eatbs.cs这个文件里面读取的是我们自己模拟ce证书的这两个文件

13
00:00:28,780 --> 00:00:31,740
那现在呢我们只需要把1.atb.cs的文件呢

14
00:00:31,740 --> 00:00:35,820
给它改成endix这个目录下的这两个秒对文件就可以了

15
00:00:35,820 --> 00:00:37,300
好打开我们的连击器

16
00:00:37,300 --> 00:00:41,520
我们把这两个读取文件的路径给它改成endix下了就可以

17
00:00:41,520 --> 00:00:44,660
好那么此时呢我们直接把这个内容呢这个key复制一下

18
00:00:44,660 --> 00:00:46,000
好拿到我们的这里面来

19
00:00:46,000 --> 00:00:49,160
OK好当然了前面呢还缺一个endix啊

20
00:00:49,160 --> 00:00:50,260
我们也要给它洗拳

21
00:00:50,260 --> 00:00:51,680
好就endix下的2啊什么什么

22
00:00:51,680 --> 00:00:53,840
然后呢是个crt这个文件呢也是一样的

23
00:00:53,840 --> 00:00:55,100
好我们复制一下这个文件的名字

24
00:00:55,100 --> 00:00:57,280
好拿过来啊没有什么问题

25
00:00:57,280 --> 00:00:58,960
然后我们前面再给它补上这个NX

26
00:00:58,960 --> 00:01:00,000
好我们保存一下

27
00:01:00,000 --> 00:01:00,780
那代码显完之后

28
00:01:00,780 --> 00:01:03,620
我们直接把这个代码找到我们的显示这里

29
00:01:03,620 --> 00:01:06,360
我们直接把这个代码同样的传到我们的服务器上来

30
00:01:06,360 --> 00:01:07,300
直接拖过来

31
00:01:07,300 --> 00:01:08,400
此时因为两个相同

32
00:01:08,400 --> 00:01:09,460
我们直接选择覆盖就可以了

33
00:01:09,460 --> 00:01:10,100
点击确定

34
00:01:10,100 --> 00:01:10,920
好传输完成之后

35
00:01:10,920 --> 00:01:11,800
在我们的服务器里

36
00:01:11,800 --> 00:01:15,480
依然使用Node去执行当前的eATB.js

37
00:01:15,480 --> 00:01:16,920
好那么一新出来之后

38
00:01:16,920 --> 00:01:18,840
我们还是打开我们的浏览器

39
00:01:18,840 --> 00:01:19,900
我们随便找一个

40
00:01:19,900 --> 00:01:20,280
还有这个吧

41
00:01:20,280 --> 00:01:20,740
我们就在这里

42
00:01:20,740 --> 00:01:21,840
来把其他能源关掉

43
00:01:21,840 --> 00:01:23,340
OK那么此时我们一刷新

44
00:01:23,340 --> 00:01:25,120
发现没有什么变化

45
00:01:25,120 --> 00:01:26,200
还是不安全的

46
00:01:26,200 --> 00:01:26,740
这是什么问题

47
00:01:26,740 --> 00:01:29,040
我们已经拿到了国际认证的

48
00:01:29,040 --> 00:01:29,780
正确的

49
00:01:29,780 --> 00:01:30,700
浏览器也认识的

50
00:01:30,700 --> 00:01:31,720
CA机构的国际证书

51
00:01:31,720 --> 00:01:32,820
那么为什么

52
00:01:32,820 --> 00:01:35,020
它现在还是出现这样的问题

53
00:01:35,020 --> 00:01:36,380
好 请注意

54
00:01:36,380 --> 00:01:38,380
你这个证书申请的时候

55
00:01:38,380 --> 00:01:39,580
申请给了谁

56
00:01:39,580 --> 00:01:41,620
注意 你是申请给了谁

57
00:01:41,620 --> 00:01:42,900
是西林老师第二看吗

58
00:01:42,900 --> 00:01:46,180
但是现在你访问的又是谁呢

59
00:01:46,180 --> 00:01:47,460
是直接的这个什么

60
00:01:47,460 --> 00:01:48,120
RP地址

61
00:01:48,120 --> 00:01:49,140
对不对

62
00:01:49,140 --> 00:01:49,920
所以这样行不行

63
00:01:49,920 --> 00:01:50,620
肯定不行

64
00:01:50,620 --> 00:01:50,920
为什么

65
00:01:50,920 --> 00:01:52,240
你直接访问这个RP地址

66
00:01:52,240 --> 00:01:52,880
注意

67
00:01:52,880 --> 00:01:54,640
人家说了证书没啥根就没有给你

68
00:01:54,640 --> 00:01:55,280
你访问那行吗

69
00:01:55,280 --> 00:01:55,860
肯定不行

70
00:01:55,860 --> 00:01:56,800
那你只能访问什么

71
00:01:56,800 --> 00:01:59,580
就是我们在申请的时候用的那个谁呀

72
00:01:59,580 --> 00:02:00,760
哎

73
00:02:00,760 --> 00:02:02,660
注意这个叫XL.新老师.com点

74
00:02:02,660 --> 00:02:03,340
这个.com

75
00:02:03,340 --> 00:02:04,480
这个域名才可以

76
00:02:04,480 --> 00:02:05,080
那么因此呢

77
00:02:05,080 --> 00:02:06,900
我们还需要在我们的域名这里呢

78
00:02:06,900 --> 00:02:08,660
给他配置一个真实的这个域名

79
00:02:08,660 --> 00:02:10,080
叫XL.新老师.com

80
00:02:10,080 --> 00:02:10,800
那么解决线的模型

81
00:02:10,800 --> 00:02:13,180
那么记录值就是我们的IP地址就可以了

82
00:02:13,180 --> 00:02:15,860
好

83
00:02:15,860 --> 00:02:17,520
记录值啊

84
00:02:17,520 --> 00:02:18,300
这里呢是XL

85
00:02:18,300 --> 00:02:18,660
OK

86
00:02:18,660 --> 00:02:19,680
那我们直接点确定

87
00:02:19,680 --> 00:02:20,600
OK

88
00:02:20,600 --> 00:02:21,440
这就已经成功了

89
00:02:21,440 --> 00:02:21,840
好

90
00:02:21,840 --> 00:02:22,860
那么此时完了之后啊

91
00:02:22,860 --> 00:02:24,260
我们再过来

92
00:02:24,260 --> 00:02:26,680
我们就不要去访问这个IP地址了

93
00:02:26,680 --> 00:02:27,180
好

94
00:02:27,180 --> 00:02:29,020
ATTPXL.C0

95
00:02:29,020 --> 00:02:31,800
老师

96
00:02:31,800 --> 00:02:32,560
第二

97
00:02:32,560 --> 00:02:32,920
Comp

98
00:02:32,920 --> 00:02:34,080
然后还是毛号发现

99
00:02:34,080 --> 00:02:34,860
我们再来以后

100
00:02:34,860 --> 00:02:35,600
你会发现这个地方

101
00:02:35,600 --> 00:02:36,340
看里就算什么

102
00:02:36,340 --> 00:02:37,020
安全连接

103
00:02:37,020 --> 00:02:38,860
而且他也不会提醒那个警告了

104
00:02:38,860 --> 00:02:40,240
这就是一个真实的

105
00:02:40,240 --> 00:02:42,400
线上的ATBS服务器的一个搭建过程

106
00:02:42,400 --> 00:02:42,900
说白了

107
00:02:42,900 --> 00:02:44,180
代码和我们之前写的代码

108
00:02:44,180 --> 00:02:45,520
没有任何的区别

109
00:02:45,520 --> 00:02:46,340
我们依然使用

110
00:02:46,340 --> 00:02:48,260
Node当中的ATBS这个模块

111
00:02:48,260 --> 00:02:49,180
代码是没有变的

112
00:02:49,180 --> 00:02:50,140
只不过我们把证书

113
00:02:50,140 --> 00:02:52,140
由原来自己模拟的

114
00:02:52,140 --> 00:02:54,520
C机构办法证书换成国际认可的

115
00:02:54,520 --> 00:02:56,620
C机构办法证书就可以了

116
00:02:56,620 --> 00:03:00,960
那一个真实的上线的ATBS安全传输协议的服务器

117
00:03:00,960 --> 00:03:02,180
我们就打减完成了

