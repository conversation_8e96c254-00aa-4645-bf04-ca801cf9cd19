1
00:00:00,000 --> 00:00:01,240
好 这节课我们就来看一下

2
00:00:01,240 --> 00:00:02,120
MongoDB

3
00:00:02,120 --> 00:00:03,960
这期的课程我会怎么去讲的

4
00:00:03,960 --> 00:00:05,340
首先我们去介绍一下它是什么

5
00:00:05,340 --> 00:00:07,480
以及MongoDB它的一些应用场景

6
00:00:07,480 --> 00:00:09,020
以及我们去安装它

7
00:00:09,020 --> 00:00:11,740
包括这里也会带同学们去安装一个可视化工具

8
00:00:11,740 --> 00:00:14,620
然后我们去实现MongoDB它的一些增商改查

9
00:00:14,620 --> 00:00:16,000
这期课我们主要来讲解

10
00:00:16,000 --> 00:00:16,760
什么是MongoDB

11
00:00:16,760 --> 00:00:17,960
以及它的一些应用场景

12
00:00:17,960 --> 00:00:20,140
包括我们怎么去进行咱们的技术选行

13
00:00:20,140 --> 00:00:21,560
首先

14
00:00:21,560 --> 00:00:25,160
MongoDB它是一种基于分布式文件存储的数据库

15
00:00:25,160 --> 00:00:26,060
由C加压语言编写

16
00:00:26,060 --> 00:00:26,560
动力是什么

17
00:00:26,560 --> 00:00:27,880
它由C加压语言编写

18
00:00:28,680 --> 00:00:31,300
只在为web应用提供可扩展的高性能数据库

19
00:00:31,300 --> 00:00:32,460
存储解决方案

20
00:00:32,460 --> 00:00:33,940
其实重点是什么

21
00:00:33,940 --> 00:00:35,200
存储解决方案

22
00:00:35,200 --> 00:00:37,200
其实咱们的mongodb它的核心是存储

23
00:00:37,200 --> 00:00:37,700
什么意思

24
00:00:37,700 --> 00:00:40,520
因为我们都知道mongodb它是一种非关系型数据库

25
00:00:40,520 --> 00:00:42,440
那么非关系型数据库

26
00:00:42,440 --> 00:00:44,240
说明咱们的表一表之间没有什么关系

27
00:00:44,240 --> 00:00:45,600
那么我们核心是不是就是存储

28
00:00:45,600 --> 00:00:47,780
因为如果说你既没有关系又不能存储

29
00:00:47,780 --> 00:00:48,560
那你能干什么

30
00:00:48,560 --> 00:00:49,020
对吧

31
00:00:49,020 --> 00:00:50,200
你又不能跑又不能跳的

32
00:00:50,200 --> 00:00:51,100
存在的意义是什么

33
00:00:51,100 --> 00:00:51,680
就没有了吧

34
00:00:51,680 --> 00:00:54,300
而且mongodb它是一个

35
00:00:54,300 --> 00:00:56,820
介于关系型数据库和非关系型数据库之间的产品

36
00:00:56,820 --> 00:00:59,160
是非关系数据库中功能最丰富

37
00:00:59,160 --> 00:01:00,480
最像关系型数据库的

38
00:01:00,480 --> 00:01:01,020
说明什么问题

39
00:01:01,020 --> 00:01:02,620
说明了我们的mongodb

40
00:01:02,620 --> 00:01:05,240
它其实是非关系数据库

41
00:01:05,240 --> 00:01:08,580
但是它又比较接近咱们的关系数据库

42
00:01:08,580 --> 00:01:10,520
其实咱们可以总结一下是什么

43
00:01:10,520 --> 00:01:12,220
mongodb是什么

44
00:01:12,220 --> 00:01:16,640
是我们关系型数据库的

45
00:01:16,640 --> 00:01:18,220
补充

46
00:01:18,220 --> 00:01:19,880
也就是说你买soco

47
00:01:19,880 --> 00:01:21,080
不擅长的事情

48
00:01:21,080 --> 00:01:22,800
或许我mongodb可以去解决

49
00:01:22,800 --> 00:01:24,400
这里就是它存在的意义

50
00:01:24,400 --> 00:01:25,180
好 我们来看一下

51
00:01:25,180 --> 00:01:26,760
它的一些应用场景到底是什么

52
00:01:26,760 --> 00:01:29,040
首先

53
00:01:29,040 --> 00:01:30,540
蒙哥地比它比较适合的场景

54
00:01:30,540 --> 00:01:30,960
就是说

55
00:01:30,960 --> 00:01:31,960
因为刚才我们也讲到了

56
00:01:31,960 --> 00:01:33,700
它是一种存储解决方案

57
00:01:33,700 --> 00:01:34,200
也就是说我们

58
00:01:34,200 --> 00:01:36,180
比较纯粹的存储的事情

59
00:01:36,180 --> 00:01:38,620
是不是蒙哥地比比较适合去做

60
00:01:38,620 --> 00:01:39,780
你比如说我们去

61
00:01:39,780 --> 00:01:41,440
在服务去进行日志的记录

62
00:01:41,440 --> 00:01:42,440
日志

63
00:01:42,440 --> 00:01:44,480
是不是主要操作是存储

64
00:01:44,480 --> 00:01:45,040
比如说我们日志

65
00:01:45,040 --> 00:01:45,820
打一些日志

66
00:01:45,820 --> 00:01:46,640
咱们把它存在

67
00:01:46,640 --> 00:01:47,740
咱们的某一个文件夹下面去

68
00:01:47,740 --> 00:01:48,360
打一些log

69
00:01:48,360 --> 00:01:49,800
它核心操作是存储

70
00:01:49,800 --> 00:01:51,300
然后你还会去查一些日志

71
00:01:51,300 --> 00:01:51,580
对吧

72
00:01:52,800 --> 00:01:53,680
浮缺日誌记录

73
00:01:53,680 --> 00:01:55,520
包括第二个去存储一些监控数据

74
00:01:55,520 --> 00:01:57,480
其实也和日誌是不是类似啊

75
00:01:57,480 --> 00:01:58,720
第三个网站数据

76
00:01:58,720 --> 00:02:01,040
你比如说去做一些实时的插入更新与查询

77
00:02:01,040 --> 00:02:02,320
因为咱们的web站连

78
00:02:02,320 --> 00:02:03,440
比如说我们一个简单的网站

79
00:02:03,440 --> 00:02:04,880
是不是你会去存储一些文章

80
00:02:04,880 --> 00:02:05,760
或者说一些新闻

81
00:02:05,760 --> 00:02:07,320
以及一些咱们的一些活动

82
00:02:07,320 --> 00:02:07,640
这里呢

83
00:02:07,640 --> 00:02:09,880
比较适合去做网站的一些数据

84
00:02:09,880 --> 00:02:10,160
但是呢

85
00:02:10,160 --> 00:02:12,400
你不适合去做一些非常复杂的一些关系

86
00:02:12,400 --> 00:02:14,120
你比如说像淘宝的一些购物车

87
00:02:14,120 --> 00:02:15,080
它会有些关联嘛

88
00:02:15,080 --> 00:02:15,400
对吧

89
00:02:15,400 --> 00:02:17,160
你比如说去抢一个什么卷

90
00:02:17,160 --> 00:02:18,700
什么分享给某一个人需要砍价

91
00:02:18,700 --> 00:02:21,760
你只要涉及到关系很复杂的东西就不适合用mongolDB

92
00:02:21,760 --> 00:02:23,040
包括了一些大尺寸

93
00:02:23,040 --> 00:02:24,080
低价值的数据

94
00:02:24,080 --> 00:02:24,840
什么意思

95
00:02:24,840 --> 00:02:27,400
因为我们的mongolDB他本身就是存储解决方案

96
00:02:27,400 --> 00:02:29,700
所以呢他肯定是适合存储一些大型的数据

97
00:02:29,700 --> 00:02:30,480
低价值是什么意思

98
00:02:30,480 --> 00:02:31,760
低价值不是说这个数据没用

99
00:02:31,760 --> 00:02:33,280
只是说这个数据他非常简单

100
00:02:33,280 --> 00:02:34,060
他很简单

101
00:02:34,060 --> 00:02:37,380
因为呢他比较灵活没有咱们表与表之间没有些关系

102
00:02:37,380 --> 00:02:38,920
但是买SOCO它是有关系

103
00:02:38,920 --> 00:02:39,940
你任何一张表

104
00:02:39,940 --> 00:02:40,960
关系都是错综复杂的

105
00:02:41,220 --> 00:02:43,220
所以说呢你每一张表他都非常重要

106
00:02:43,220 --> 00:02:44,220
但是呢你摸个DB

107
00:02:44,220 --> 00:02:45,420
没什么关系吧

108
00:02:45,420 --> 00:02:46,220
所以说呢

109
00:02:46,220 --> 00:02:48,220
这里低价值体现在于它

110
00:02:48,220 --> 00:02:49,220
他们之间关联性不强

111
00:02:49,220 --> 00:02:50,220
而并不是这样这个数据没用

112
00:02:50,220 --> 00:02:51,220
如果没用的数据我们存起来干嘛

113
00:02:51,220 --> 00:02:52,220
对吧

114
00:02:52,220 --> 00:02:53,220
好不适合的场景是什么

115
00:02:53,220 --> 00:02:55,220
其实刚才我们也讲到了

116
00:02:55,220 --> 00:02:57,220
我们也讲到高度事务性的系统

117
00:02:57,220 --> 00:02:58,220
什么叫高度事务性的

118
00:02:58,220 --> 00:03:00,220
我们之前是不是举过一个例子

119
00:03:00,220 --> 00:03:01,220
什么意思啊

120
00:03:01,220 --> 00:03:03,220
你比如说我们钱包

121
00:03:03,220 --> 00:03:05,220
对吧我给你付款你收款

122
00:03:05,220 --> 00:03:06,220
他是一个事务

123
00:03:06,220 --> 00:03:07,220
你不能说我在

124
00:03:07,220 --> 00:03:08,220
我给你转账的时候挂了

125
00:03:08,220 --> 00:03:09,220
结果我的钱没了

126
00:03:09,220 --> 00:03:10,220
你没有收到钱

127
00:03:10,220 --> 00:03:12,620
所以说事物型的系统

128
00:03:12,620 --> 00:03:13,860
蒙古地比不是很适合的

129
00:03:13,860 --> 00:03:14,140
说白了

130
00:03:14,140 --> 00:03:15,460
关系很复杂的系统

131
00:03:15,460 --> 00:03:16,840
蒙古地比是不复杂

132
00:03:16,840 --> 00:03:19,140
那么我们到底如何去选择

133
00:03:19,140 --> 00:03:19,980
我们到底如何去选择

134
00:03:19,980 --> 00:03:20,980
这里呢

135
00:03:20,980 --> 00:03:22,280
我来给大家总结了一张表

136
00:03:22,280 --> 00:03:23,980
大家可以去做技术选型的时候

137
00:03:23,980 --> 00:03:24,500
可以参考一下

138
00:03:24,500 --> 00:03:27,940
比如说我们的应用

139
00:03:27,940 --> 00:03:29,900
首先它有一个冲药条件

140
00:03:29,900 --> 00:03:31,160
也就是说它有一个必须遵守条件

141
00:03:31,160 --> 00:03:31,960
就是咱们的应用

142
00:03:31,960 --> 00:03:32,980
不需要事物

143
00:03:32,980 --> 00:03:34,200
以及复杂就你的支持

144
00:03:34,200 --> 00:03:34,740
说白了

145
00:03:34,740 --> 00:03:36,000
就是咱们的关系型数据库

146
00:03:36,000 --> 00:03:37,040
也就是说咱们的数据之间

147
00:03:37,040 --> 00:03:37,860
没有什么关系吧

148
00:03:37,860 --> 00:03:39,020
这是一个必要条件

149
00:03:39,020 --> 00:03:39,620
好

150
00:03:39,620 --> 00:03:40,500
那其他的

151
00:03:40,500 --> 00:03:41,400
如果说

152
00:03:41,400 --> 00:03:42,780
如果说你有一个意见识

153
00:03:42,780 --> 00:03:43,920
可以考虑使用蒙古地币

154
00:03:43,920 --> 00:03:45,400
两个级以上的意见识

155
00:03:45,400 --> 00:03:46,300
你选择蒙古地币

156
00:03:46,300 --> 00:03:47,280
绝对不会后悔

157
00:03:47,280 --> 00:03:47,660
什么意思

158
00:03:47,660 --> 00:03:49,020
也就是说下面这些条件

159
00:03:49,020 --> 00:03:49,680
你只要满足一个

160
00:03:49,680 --> 00:03:50,620
你可以考虑满足两个

161
00:03:50,620 --> 00:03:51,720
你可以选它

162
00:03:51,720 --> 00:03:52,860
你比如说咱们的新应用

163
00:03:52,860 --> 00:03:53,440
需求会变吧

164
00:03:53,440 --> 00:03:54,480
需求会变

165
00:03:54,480 --> 00:03:54,960
什么意思

166
00:03:54,960 --> 00:03:56,640
比如说我们的需求经常去变化

167
00:03:56,640 --> 00:03:57,680
你如果用关系型

168
00:03:57,680 --> 00:03:59,040
咱们的业务业务之间

169
00:03:59,040 --> 00:04:00,120
都会产生一些联系

170
00:04:00,120 --> 00:04:00,700
你一个地方变化

171
00:04:00,700 --> 00:04:02,080
是不是会导致其他地方不可用

172
00:04:02,080 --> 00:04:03,580
所以这里比较适合蒙古利币

173
00:04:03,580 --> 00:04:06,640
而且应用需要2000至3000以上的读写QPS

174
00:04:06,640 --> 00:04:08,700
什么是QPS呢

175
00:04:08,700 --> 00:04:09,860
我给大家举例子

176
00:04:09,860 --> 00:04:10,640
比如说你的网站

177
00:04:10,640 --> 00:04:13,040
一秒钟能够处理100个请求

178
00:04:13,040 --> 00:04:14,220
那么你的QPS就是100

179
00:04:14,220 --> 00:04:15,220
什么意思呢

180
00:04:15,220 --> 00:04:15,920
也就是说你的应用

181
00:04:15,920 --> 00:04:16,680
如果说一秒钟

182
00:04:16,680 --> 00:04:18,300
你的服务器需要去服务

183
00:04:18,300 --> 00:04:21,280
需要去处理2000至3000个请求的时候

184
00:04:21,280 --> 00:04:23,320
你可以考虑去使用蒙古利币

185
00:04:23,320 --> 00:04:26,380
不过这里它指的是读写QPS

186
00:04:26,380 --> 00:04:27,400
而不是咱们的请求QPS

187
00:04:27,400 --> 00:04:28,200
其实是类似的

188
00:04:28,200 --> 00:04:28,860
你比如说你一秒钟

189
00:04:28,860 --> 00:04:30,320
要进行2000至3000次读写的时候

190
00:04:30,320 --> 00:04:31,200
你可以去考虑

191
00:04:31,200 --> 00:04:32,300
包括应用需要TB

192
00:04:32,300 --> 00:04:33,180
甚至PB级的数据存储

193
00:04:33,180 --> 00:04:34,500
说白了就是数据量很大

194
00:04:34,500 --> 00:04:35,180
第二个

195
00:04:35,180 --> 00:04:37,520
发展迅速需要快速水平扩展

196
00:04:37,520 --> 00:04:38,640
什么叫水平扩展

197
00:04:38,640 --> 00:04:40,180
是不是就是你一个很像扩展

198
00:04:40,180 --> 00:04:40,620
很像扩展

199
00:04:40,620 --> 00:04:42,260
说明你扩展的过程中没有依赖性

200
00:04:42,260 --> 00:04:44,920
第二个应用要求存储的数据不丢失

201
00:04:44,920 --> 00:04:45,900
这个不是废话吗

202
00:04:45,900 --> 00:04:46,600
你不管用什么

203
00:04:46,600 --> 00:04:47,560
你选拨个利比选买收口

204
00:04:47,560 --> 00:04:48,480
是吧都会

205
00:04:48,480 --> 00:04:49,420
你希望它不丢失

206
00:04:49,420 --> 00:04:50,840
所以这个可以不看

207
00:04:50,840 --> 00:04:53,560
应用需要99.999%的高可用

208
00:04:53,560 --> 00:04:55,960
说明咱们需要咱们的服务很稳定

209
00:04:55,960 --> 00:04:56,320
对吧

210
00:04:56,320 --> 00:04:59,920
而且如果说你需要大量的地理位置查询以及文本查询

211
00:04:59,920 --> 00:05:00,680
什么意思

212
00:05:00,680 --> 00:05:01,720
这不说明我们的mongodb

213
00:05:01,720 --> 00:05:03,920
它也比较适合咱们的一些文本查询

214
00:05:03,920 --> 00:05:05,200
一些简单数学查询

215
00:05:05,200 --> 00:05:06,900
它是非常适合的

216
00:05:06,900 --> 00:05:08,760
好 这里就是我们这节课的内容

217
00:05:08,760 --> 00:05:09,700
我们一起来

218
00:05:09,700 --> 00:05:11,160
我们来总结一下

219
00:05:11,160 --> 00:05:14,720
第二章 第四章

220
00:05:14,720 --> 00:05:17,140
咱们的mongodb

221
00:05:17,140 --> 00:05:19,960
好 我们刚才是不是介绍了我们的mongodb

222
00:05:19,960 --> 00:05:21,000
它是一门什么样的数据库

223
00:05:21,000 --> 00:05:23,860
是不是非关系型数据库

224
00:05:23,860 --> 00:05:25,480
它其实核心是什么

225
00:05:25,480 --> 00:05:26,200
核心是什么

226
00:05:26,200 --> 00:05:26,840
同学们

227
00:05:26,840 --> 00:05:27,760
存储型吧

228
00:05:27,760 --> 00:05:29,040
然后呢

229
00:05:29,040 --> 00:05:29,640
是什么

230
00:05:29,640 --> 00:05:30,160
是不是

231
00:05:30,160 --> 00:05:33,300
关系型

232
00:05:33,300 --> 00:05:35,720
数据库的一种

233
00:05:35,720 --> 00:05:37,740
补充啊

234
00:05:37,740 --> 00:05:39,740
好

235
00:05:39,740 --> 00:05:42,540
那么我们怎么样去进行技术选型呢

236
00:05:42,540 --> 00:05:43,700
我们可以总结一下

237
00:05:43,700 --> 00:05:45,420
比较适合和不适合的场景

238
00:05:45,420 --> 00:05:47,320
适合什么

239
00:05:47,320 --> 00:05:48,040
适合什么场景啊

240
00:05:48,040 --> 00:05:48,280
同学们

241
00:05:48,280 --> 00:05:48,880
是不是存储型

242
00:05:48,880 --> 00:05:50,720
是不是存储型

243
00:05:50,720 --> 00:05:51,800
而且呢

244
00:05:51,800 --> 00:05:53,540
数据之间

245
00:05:53,540 --> 00:05:55,460
没什么关系吧

246
00:05:55,460 --> 00:05:57,440
那么它不适合什么呢

247
00:05:57,440 --> 00:05:58,640
不适合什么

248
00:05:58,640 --> 00:06:01,800
是不是不适合就是咱们适合相反的呀

249
00:06:01,800 --> 00:06:02,980
比如说你存储型就是非存储型

250
00:06:02,980 --> 00:06:04,660
你比如说你数据之间没什么关系

251
00:06:04,660 --> 00:06:05,980
那就数据之间有什么关系就不适合

252
00:06:05,980 --> 00:06:06,440
对吧

253
00:06:06,440 --> 00:06:09,440
所以那就是数据之间关系复杂

254
00:06:09,440 --> 00:06:11,260
好这里呢就是我们这几个的内容

