1
00:00:00,000 --> 00:00:03,900
好 了解完HTTP和HTTP2这样的两个模块之后呢

2
00:00:03,900 --> 00:00:06,200
我们就来看一下HTTP它的一个发展过程

3
00:00:06,200 --> 00:00:08,060
那么也就是我们1.0和2.0的一个区别

4
00:00:08,060 --> 00:00:09,000
那么我们一起来看一下

5
00:00:09,000 --> 00:00:11,720
早在HTTP建立之初

6
00:00:11,720 --> 00:00:13,860
主要就是为了将抄文本标记语言

7
00:00:13,860 --> 00:00:15,320
也就是我们的HTML文档

8
00:00:15,320 --> 00:00:19,100
从Web服务器传送到我们的客户端的浏览器

9
00:00:19,100 --> 00:00:20,200
那么也就是说

10
00:00:20,200 --> 00:00:21,780
对于全单来讲

11
00:00:21,780 --> 00:00:24,060
我们所写的HTML页面

12
00:00:24,060 --> 00:00:26,460
是不是需要放在我们的HTTP服务器上面了

13
00:00:26,460 --> 00:00:27,180
对吧

14
00:00:27,180 --> 00:00:28,580
因为我们的HTML模板

15
00:00:28,580 --> 00:00:31,080
是不是都在lowsdx的一个咱们的static静态之类的目录下面

16
00:00:31,080 --> 00:00:34,740
那当我们去请求我们暂点的时候服务器呢就会返回我们的html这样一个文件

17
00:00:34,740 --> 00:00:35,600
对吧

18
00:00:35,600 --> 00:00:39,900
好那么用户端呢通过浏览器访问URL底子来获取我们显示的一个内容

19
00:00:39,900 --> 00:00:42,420
但是呢到了web2.0以后

20
00:00:42,420 --> 00:00:44,080
我们的页面变得越来越复杂

21
00:00:44,080 --> 00:00:46,720
不仅仅单纯的是一些简单的文字和图片

22
00:00:46,720 --> 00:00:50,320
同时呢我们的html呢也有了css和js来丰富我们页面的展示

23
00:00:50,320 --> 00:00:53,300
而且呢我们随着adjust的出现

24
00:00:53,300 --> 00:00:56,380
我们是不是也多了一种向服务端获取数据的方法

25
00:00:56,380 --> 00:00:59,380
那可能同学们从事前当行业比较晚

26
00:00:59,380 --> 00:01:01,560
那么像10年以前

27
00:01:01,560 --> 00:01:03,560
包括了15年以前的一些开放者

28
00:01:03,560 --> 00:01:04,420
可能就深有体会

29
00:01:04,420 --> 00:01:05,580
以前的页面真的非常简单

30
00:01:05,580 --> 00:01:07,620
你像现在我们的页面是不是越来越作越炫

31
00:01:07,620 --> 00:01:09,700
包括H5的出现各种动效

32
00:01:09,700 --> 00:01:11,440
其实页面是越来越的复杂

33
00:01:11,440 --> 00:01:12,700
而且包括移动端的一些出现

34
00:01:12,700 --> 00:01:14,060
对我们页面的性能也越来越高

35
00:01:14,060 --> 00:01:17,820
那么他们其实都是基于HTTP协议去传输的

36
00:01:17,820 --> 00:01:19,440
同样的到了移动互动的年代

37
00:01:19,440 --> 00:01:20,620
我们的页面也可以

38
00:01:20,620 --> 00:01:23,500
在我们的手机浏览器里面去运行

39
00:01:23,500 --> 00:01:25,940
那么手机是不是网络情况更加的复杂

40
00:01:25,940 --> 00:01:31,680
所以我们开始了不得不对HTTP进行深入理解并不断优化的过程中

41
00:01:31,680 --> 00:01:33,200
那么这里的我们指谁

42
00:01:33,200 --> 00:01:36,360
我们其实就是指HTTP这样的一个协会

43
00:01:36,360 --> 00:01:39,620
因为我们HTTP全世界是不是都在使用这样一种协议进行他们浏览器的交互

44
00:01:39,620 --> 00:01:41,860
那么当然有W3C这样的一些组织

45
00:01:41,860 --> 00:01:43,440
他们就会去定义一些协议

46
00:01:43,440 --> 00:01:46,280
我们可以看这样的一个时序图

47
00:01:46,280 --> 00:01:49,540
在1991年的时候

48
00:01:49,540 --> 00:01:51,420
HTTP第一个版本是0.9

49
00:01:51,420 --> 00:01:54,140
96年诞生了1.0

50
00:01:54,140 --> 00:01:55,020
那么99年呢

51
00:01:55,020 --> 00:01:56,520
出现了1.1的协议

52
00:01:56,520 --> 00:01:57,260
大家可以看到了

53
00:01:57,260 --> 00:01:58,200
1.1的这样一个版本

54
00:01:58,200 --> 00:01:59,560
是不是持续了非常长的一个时间

55
00:01:59,560 --> 00:02:01,140
1999年到2015年

56
00:02:01,140 --> 00:02:02,000
是不是就16年

57
00:02:02,000 --> 00:02:03,840
那么到2015年之后

58
00:02:03,840 --> 00:02:05,220
不知道大家有没有这样的一个印象

59
00:02:05,220 --> 00:02:06,740
而我们2015年的时候

60
00:02:06,740 --> 00:02:08,220
是不是出现了大量的智能手机

61
00:02:08,220 --> 00:02:09,240
那么2015年之前

62
00:02:09,240 --> 00:02:10,100
可能呢

63
00:02:10,100 --> 00:02:10,880
各种洛基亚

64
00:02:10,880 --> 00:02:13,020
各种什么三星那样翻盖机

65
00:02:13,020 --> 00:02:14,700
以前的手机能不能上网

66
00:02:14,700 --> 00:02:16,120
很少有手机可以上网吧

67
00:02:16,120 --> 00:02:16,660
对吧

68
00:02:16,660 --> 00:02:17,880
以前大家就是发短信

69
00:02:17,880 --> 00:02:18,980
打电话

70
00:02:18,980 --> 00:02:20,220
那么2015年之后呢

71
00:02:20,220 --> 00:02:22,100
就随着安卓系统的出现

72
00:02:22,100 --> 00:02:23,720
就各种游戏啊什么的

73
00:02:23,720 --> 00:02:24,020
对吧

74
00:02:24,020 --> 00:02:25,940
反正网上可以玩的东西就越来越多了

75
00:02:25,940 --> 00:02:29,300
所以为了应对这样一种情况

76
00:02:29,300 --> 00:02:31,220
于是出现了HGDP 2.0这个版本

77
00:02:31,220 --> 00:02:32,800
好 我们来看一下

78
00:02:32,800 --> 00:02:35,140
影响一个HGDP网络请求的主要因素有哪些

79
00:02:35,140 --> 00:02:36,120
主要有两点

80
00:02:36,120 --> 00:02:37,680
一个是带宽 一个是延迟

81
00:02:37,680 --> 00:02:38,900
带宽呢

82
00:02:38,900 --> 00:02:41,000
带宽其实就是我们的一个

83
00:02:41,000 --> 00:02:44,060
比如说我们去办一个宽带

84
00:02:44,060 --> 00:02:46,560
大家会让你去选择你是要100兆的还是50兆的

85
00:02:46,560 --> 00:02:48,880
那么100兆就是10兆每秒的传输速度

86
00:02:48,880 --> 00:02:51,000
50兆就是5兆每秒的一个传输速度

87
00:02:51,000 --> 00:02:53,260
像以前我读书的时候

88
00:02:53,260 --> 00:02:54,900
我们的宽带好像是一兆的

89
00:02:54,900 --> 00:02:55,780
最快的是五兆

90
00:02:55,780 --> 00:02:56,460
那么一兆的话

91
00:02:56,460 --> 00:02:57,280
其实下一部电影呢

92
00:02:57,280 --> 00:02:57,780
其实非常慢

93
00:02:57,780 --> 00:02:58,880
也是100K每秒

94
00:02:58,880 --> 00:03:00,320
那么下一部一击的电影

95
00:03:00,320 --> 00:03:01,400
可能需要花好几个小时

96
00:03:01,400 --> 00:03:02,540
但是现在100兆的网

97
00:03:02,540 --> 00:03:03,180
非常非常快

98
00:03:03,180 --> 00:03:03,960
几分钟就可以下好了

99
00:03:03,960 --> 00:03:05,060
包括现在5G的来临

100
00:03:05,060 --> 00:03:06,360
那么可能你的蓝光的一些

101
00:03:06,360 --> 00:03:07,840
电影呢也会下载的非常快

102
00:03:07,840 --> 00:03:09,380
那么第一个是带宽

103
00:03:09,380 --> 00:03:09,940
那么带宽呢

104
00:03:09,940 --> 00:03:10,840
其实就是影响我们去

105
00:03:10,840 --> 00:03:12,260
下载电影的一个时间

106
00:03:12,260 --> 00:03:12,520
对吧

107
00:03:12,520 --> 00:03:13,020
那么第二个呢

108
00:03:13,020 --> 00:03:13,420
就是延迟

109
00:03:13,420 --> 00:03:14,580
那么延迟呢

110
00:03:14,580 --> 00:03:15,940
又分为几个因素去影响它

111
00:03:15,940 --> 00:03:18,060
首先是浏览器的一个主射

112
00:03:18,060 --> 00:03:19,360
浏览器为什么会阻塞啊

113
00:03:19,360 --> 00:03:21,600
因为我们如果说去请求资源

114
00:03:21,600 --> 00:03:23,420
是不是请求js CSS等等很多很多的资源

115
00:03:23,420 --> 00:03:25,880
那么但是我们浏览器同一个域名最多

116
00:03:25,880 --> 00:03:26,880
只能有四个连接

117
00:03:26,880 --> 00:03:29,000
那么在http早期的版本

118
00:03:29,000 --> 00:03:30,220
其实一个资源就是一个连接

119
00:03:30,220 --> 00:03:32,140
比如说你同时需要去请求十个js

120
00:03:32,140 --> 00:03:34,120
那你可能需要去建立十个连接

121
00:03:34,120 --> 00:03:36,120
其实这样是效率非常的低

122
00:03:36,120 --> 00:03:37,280
包括DNS查询

123
00:03:37,280 --> 00:03:40,080
包括我们的http它是基于TCP协议的

124
00:03:40,080 --> 00:03:42,520
TCP是不是要进行三次握手一个过程

125
00:03:42,520 --> 00:03:44,740
那么我们如何简单的去理解三次握手的过程

126
00:03:44,740 --> 00:03:46,800
就好比我给你打了一个电话

127
00:03:46,800 --> 00:03:47,100
对吧

128
00:03:47,100 --> 00:03:47,780
我说你好

129
00:03:47,780 --> 00:03:48,540
可以听懂中文吗

130
00:03:48,540 --> 00:03:48,960
你说可以

131
00:03:48,960 --> 00:03:50,260
然后我再回复你

132
00:03:50,260 --> 00:03:50,420
好吧

133
00:03:50,420 --> 00:03:51,960
那我们开始通话吧

134
00:03:51,960 --> 00:03:53,740
这里就是指我们的一个三次握手

135
00:03:53,740 --> 00:03:55,400
那么延迟

136
00:03:55,400 --> 00:03:56,440
对我们影响最大的

137
00:03:56,440 --> 00:03:56,920
其实就是

138
00:03:56,920 --> 00:03:58,800
你可能我们的一个手机

139
00:03:58,800 --> 00:03:59,880
打开一个网页

140
00:03:59,880 --> 00:04:01,100
特别是在弱网的情况下

141
00:04:01,100 --> 00:04:01,360
对吧

142
00:04:01,360 --> 00:04:02,720
第二个就是我们去打一些

143
00:04:02,720 --> 00:04:03,380
实时性的游戏

144
00:04:03,380 --> 00:04:04,480
比如说LOL

145
00:04:04,480 --> 00:04:05,120
DOTA等等

146
00:04:05,120 --> 00:04:06,220
那么如果说连延迟高

147
00:04:06,220 --> 00:04:07,500
是不是打游戏会非常的难受

148
00:04:07,500 --> 00:04:08,740
好

149
00:04:08,740 --> 00:04:09,420
我们就来看

150
00:04:09,420 --> 00:04:10,180
首先我们来看一下

151
00:04:10,180 --> 00:04:12,160
HTTP 1.0和1.1

152
00:04:12,160 --> 00:04:13,720
它们之间的一个区别和关系是什么

153
00:04:13,720 --> 00:04:15,240
我们看

154
00:04:15,240 --> 00:04:17,640
那在一张图呢1.0是1996年

155
00:04:17,640 --> 00:04:18,700
那么1.1是1999年

156
00:04:18,700 --> 00:04:19,860
它们中间只隔了三年的时间

157
00:04:19,860 --> 00:04:21,620
我们来看一下它们之间的有哪些变化

158
00:04:21,620 --> 00:04:23,620
首先呢我们来看一下缓存处理

159
00:04:23,620 --> 00:04:24,380
缓存处理方面

160
00:04:24,380 --> 00:04:26,080
在hcp1.0中

161
00:04:26,080 --> 00:04:28,520
主要使用head里面的一个if

162
00:04:28,520 --> 00:04:30,320
modifiedsigns和expire

163
00:04:30,320 --> 00:04:32,400
来作为我们缓存判断的标准

164
00:04:32,400 --> 00:04:34,120
那么hcp1.1中呢

165
00:04:34,120 --> 00:04:35,460
则引入了更多的缓存策略

166
00:04:35,460 --> 00:04:36,560
比如说tag

167
00:04:36,560 --> 00:04:38,020
包括了ifmodifiedsigns

168
00:04:38,020 --> 00:04:39,520
等等在那些缓存策略

169
00:04:39,520 --> 00:04:40,640
那么它们有什么区别呢

170
00:04:40,640 --> 00:04:43,020
不知道同学有没有理解过

171
00:04:43,020 --> 00:04:45,040
有没有了解过咱们浏览器的一些缓通策略

172
00:04:45,040 --> 00:04:46,640
我们可能不能够讲太远

173
00:04:46,640 --> 00:04:48,900
因为讲的话就脱离了我们的一个课程的主线

174
00:04:48,900 --> 00:04:51,020
这里简单的可以跟同学们说一下

175
00:04:51,020 --> 00:04:52,740
那么像一个是Pile的话

176
00:04:52,740 --> 00:04:54,720
它是我们客户端和服务端

177
00:04:54,720 --> 00:04:55,940
分别生成两个时间戳

178
00:04:55,940 --> 00:04:56,820
它们两个来进行对比

179
00:04:56,820 --> 00:04:59,360
然后来判断我们的文件是否过期

180
00:04:59,360 --> 00:05:00,860
但是这样就有一个问题

181
00:05:00,860 --> 00:05:02,500
因为我们网络会有延迟

182
00:05:02,500 --> 00:05:05,260
你包括客户端的时间和咱们服务器的时间是不是很差一样

183
00:05:05,260 --> 00:05:07,820
为什么这么说的很简单

184
00:05:07,820 --> 00:05:09,620
假如我和你同样有一块手表

185
00:05:09,620 --> 00:05:10,860
那么我们两个时间会不会有区别

186
00:05:10,860 --> 00:05:11,680
那么肯定会有嘛

187
00:05:11,680 --> 00:05:12,620
要么你快几秒

188
00:05:12,620 --> 00:05:13,380
要么我的慢几秒

189
00:05:13,380 --> 00:05:14,340
甚至几分钟都是有可能的

190
00:05:14,340 --> 00:05:15,700
因为时间是无法同步的

191
00:05:15,700 --> 00:05:17,160
不知道同学们在大学的时候

192
00:05:17,160 --> 00:05:17,940
有没有学过政治

193
00:05:17,940 --> 00:05:19,580
世界上没有同意的河流

194
00:05:19,580 --> 00:05:19,860
对吧

195
00:05:19,860 --> 00:05:21,200
所以说世界上

196
00:05:21,200 --> 00:05:22,860
如果说我们按照哲学的方面

197
00:05:22,860 --> 00:05:23,880
角度来说的话

198
00:05:23,880 --> 00:05:25,100
世界上没有两块手表

199
00:05:25,100 --> 00:05:26,100
时间是完全一致的

200
00:05:26,100 --> 00:05:26,360
对吧

201
00:05:26,360 --> 00:05:28,120
那么为了解决这样一个问题

202
00:05:28,120 --> 00:05:29,360
在1.1里面

203
00:05:29,360 --> 00:05:30,520
其实就出现了一个catch

204
00:05:30,520 --> 00:05:31,400
也叫memorycatch

205
00:05:31,400 --> 00:05:32,960
可以在我们本地设置一个缓存策略

206
00:05:32,960 --> 00:05:33,800
那么它的过期时间

207
00:05:33,800 --> 00:05:35,360
其实是基于我们本地的一个时间

208
00:05:35,360 --> 00:05:37,120
所以说它的参照对象变为了自己

209
00:05:37,120 --> 00:05:38,620
这里就是1.1它的一个进步

210
00:05:38,620 --> 00:05:40,740
为了解决Expile时间不同步在那个问题

211
00:05:40,740 --> 00:05:44,100
那么所以说我们现在可以使用Expile和我们的memory cache进行一个结合

212
00:05:44,100 --> 00:05:45,320
对我们的缓存作为处理

213
00:05:45,320 --> 00:05:48,620
好我们再来看一下带宽优化以及网络连接的一个使用

214
00:05:48,620 --> 00:05:52,760
在http1.0中央存在一些浪费带宽的现象

215
00:05:52,760 --> 00:05:56,100
你比如说客户端呢只是需要某个对象的一部分

216
00:05:56,100 --> 00:05:57,900
而服务器呢却将整个对象发送过来的

217
00:05:57,900 --> 00:05:59,340
而且呢不支持断点续传

218
00:05:59,340 --> 00:06:04,000
那么看到断点续传是不是就想到了我们的文件传输

219
00:06:04,000 --> 00:06:06,200
所以说在1.0里面

220
00:06:06,200 --> 00:06:07,820
比如说你有一个一基的文件

221
00:06:07,820 --> 00:06:09,340
你需要把它给发送给浏览器

222
00:06:09,340 --> 00:06:11,840
那么你只能把它全部发送给浏览器

223
00:06:11,840 --> 00:06:12,740
这样其实效率很低

224
00:06:12,740 --> 00:06:14,180
因为可能说我只需要中间的一部分

225
00:06:14,180 --> 00:06:15,560
我只需要一百道内容

226
00:06:15,560 --> 00:06:17,820
那么1.0这样一个版本就没有办法做到

227
00:06:17,820 --> 00:06:20,160
但是1.1它在我们的请求头中

228
00:06:20,160 --> 00:06:21,020
加入了一个乱级

229
00:06:21,020 --> 00:06:22,160
也就是范围这样一个头域

230
00:06:22,160 --> 00:06:24,840
它允许我们只请求资源的某一个部分

231
00:06:24,840 --> 00:06:26,280
那么系到这里的资源

232
00:06:26,280 --> 00:06:27,860
其实我们http它的核心是什么

233
00:06:27,860 --> 00:06:29,360
就是对我们资源的请求

234
00:06:29,360 --> 00:06:30,980
接下来我们再来看一下

235
00:06:30,980 --> 00:06:32,400
错误通知的管理

236
00:06:32,400 --> 00:06:35,260
那么在http1.1中的增加了24个错误状态效应

237
00:06:35,260 --> 00:06:37,840
我们可以简单理解1.1中它的状态是不是更多了

238
00:06:37,840 --> 00:06:42,060
我们再来看一下httphost头的一个处理

239
00:06:42,060 --> 00:06:45,600
那么在1.0中的认为每一台服务器都只绑定了唯一的一个IP地址

240
00:06:45,600 --> 00:06:48,160
因此请求消息中的URL没有传递主机名

241
00:06:48,160 --> 00:06:49,680
但随着虚拟机技术的发展

242
00:06:49,680 --> 00:06:51,580
在一台物理机上可能存在多个虚拟主机

243
00:06:51,580 --> 00:06:53,280
并且他们共享一个IP地址

244
00:06:53,280 --> 00:06:56,600
http1.1的请求消息和响应消息的都支持host的投育

245
00:06:56,600 --> 00:06:59,500
那么如果说请求中没有host的话就会报告一个错误

246
00:06:59,500 --> 00:07:02,100
那么其实通过这里我们其实可以看不出

247
00:07:02,100 --> 00:07:03,380
我们其他技术的一些发展

248
00:07:03,380 --> 00:07:05,720
其实也可以去推动HTTP协议的一个发展

249
00:07:05,720 --> 00:07:06,980
因为刚才就提到了

250
00:07:06,980 --> 00:07:08,320
在我们早期的时候

251
00:07:08,320 --> 00:07:09,980
我们的机器只有一个IP

252
00:07:09,980 --> 00:07:12,000
但是后来是不是出现了一些虚拟化技术

253
00:07:12,000 --> 00:07:12,680
包括容器化

254
00:07:12,680 --> 00:07:14,860
包括现在我们可以使用QPS

255
00:07:14,860 --> 00:07:16,920
对我们的一个容器进行一个动态的扩容

256
00:07:16,920 --> 00:07:18,900
我们同一个服务IP可能会发生变化

257
00:07:18,900 --> 00:07:21,460
所以为了顺应这样一种潮流

258
00:07:21,460 --> 00:07:24,060
1.1也就出现了host这样一个处理

259
00:07:24,060 --> 00:07:25,540
我们再来看一下长链接

260
00:07:25,540 --> 00:07:27,860
那么HTTP1.1的支持长链接

261
00:07:27,860 --> 00:07:28,940
和我们的一个pipeline

262
00:07:28,940 --> 00:07:32,360
在一个TCP连接中可以去传送多个HTCP的请求和响应

263
00:07:32,360 --> 00:07:35,400
减少了建立和关闭连接的消耗和延迟

264
00:07:35,400 --> 00:07:37,320
那么这里就非常好理解了

265
00:07:37,320 --> 00:07:39,180
你比如说我们去请求一个js的时候

266
00:07:39,180 --> 00:07:40,940
是不是需要和福端去建立一个连接

267
00:07:40,940 --> 00:07:42,820
那么我们再去请求CSX的时候

268
00:07:42,820 --> 00:07:43,780
是不是又要去创立一个连接

269
00:07:43,780 --> 00:07:46,840
那么我们能不能够把这个连接通道给复用起来呢

270
00:07:46,840 --> 00:07:47,940
我们只需要用一个管道

271
00:07:47,940 --> 00:07:50,360
这里其实就是长连接它的一个意义

272
00:07:50,360 --> 00:07:54,360
我们可以去复用我们已经建立了连接

273
00:07:54,360 --> 00:07:57,940
接下来我们再来看一下HTCP2.0

274
00:07:57,940 --> 00:07:58,960
它的一些新特性

275
00:07:58,960 --> 00:08:00,200
它相比我们的1.1

276
00:08:00,200 --> 00:08:01,320
又进行了哪些优化呢

277
00:08:01,320 --> 00:08:02,200
我们就一起来看一下

278
00:08:02,200 --> 00:08:04,180
那么首先呢

279
00:08:04,180 --> 00:08:05,560
它有了一个新的二进制的格式

280
00:08:05,560 --> 00:08:08,000
HTTP1.x它的解析呢

281
00:08:08,000 --> 00:08:08,720
是基于文本

282
00:08:08,720 --> 00:08:09,800
那么基于文本呢

283
00:08:09,800 --> 00:08:11,320
就是咱们的json是不是文本

284
00:08:11,320 --> 00:08:11,920
是的吧

285
00:08:11,920 --> 00:08:12,940
那么buffer

286
00:08:12,940 --> 00:08:14,040
buffer就是我们的二进制

287
00:08:14,040 --> 00:08:15,600
那么基于文本协议的格式

288
00:08:15,600 --> 00:08:15,900
解析呢

289
00:08:15,900 --> 00:08:17,220
存在天然的缺陷

290
00:08:17,220 --> 00:08:18,860
文本的表现形式有多样性

291
00:08:18,860 --> 00:08:20,560
要做到健壮性的考虑场景

292
00:08:20,560 --> 00:08:21,180
表现有很多

293
00:08:21,180 --> 00:08:22,180
那么二进制呢

294
00:08:22,180 --> 00:08:22,640
就不一样

295
00:08:22,640 --> 00:08:23,760
它只有0和1的组合

296
00:08:23,760 --> 00:08:25,080
那么基于二进制的格式呢

297
00:08:25,080 --> 00:08:26,680
其实效率就会非常的高

298
00:08:26,680 --> 00:08:27,100
对吧

299
00:08:27,100 --> 00:08:27,780
好

300
00:08:27,780 --> 00:08:28,520
那么这里呢

301
00:08:28,520 --> 00:08:29,320
就是它的一个新特性

302
00:08:29,320 --> 00:08:29,980
好

303
00:08:29,980 --> 00:08:30,920
那么第二个

304
00:08:30,920 --> 00:08:31,260
我们来看一下

305
00:08:31,260 --> 00:08:31,700
多路服用

306
00:08:31,700 --> 00:08:33,360
链接共享

307
00:08:33,360 --> 00:08:34,880
那么我们的每一个request

308
00:08:34,880 --> 00:08:36,600
都是用作连接共享机制的

309
00:08:36,600 --> 00:08:37,900
一个request对应一个id

310
00:08:37,900 --> 00:08:38,880
然后这样一个连接呢

311
00:08:38,880 --> 00:08:39,900
可以有多个request

312
00:08:39,900 --> 00:08:41,180
那么每个连接的request

313
00:08:41,180 --> 00:08:42,240
可以随机的混杂在一起

314
00:08:42,240 --> 00:08:42,700
接收方呢

315
00:08:42,700 --> 00:08:44,540
可以去根据request的id

316
00:08:44,540 --> 00:08:45,520
将request在归属的

317
00:08:45,520 --> 00:08:46,900
各个不同的服装请求里面

318
00:08:46,900 --> 00:08:47,100
好

319
00:08:47,100 --> 00:08:48,200
什么意思呢

320
00:08:48,200 --> 00:08:49,220
其实多路服用

321
00:08:49,220 --> 00:08:49,960
这样我们看它概念

322
00:08:49,960 --> 00:08:50,460
非常复杂

323
00:08:50,460 --> 00:08:51,960
我们完全可以把它理解为

324
00:08:51,960 --> 00:08:53,320
它是长链接的一个升级吧

325
00:08:53,320 --> 00:08:56,220
那么长链接其实是1.1里面所提出来的

326
00:08:56,220 --> 00:08:59,340
但是我们的多罗夫人对它进行了一个更深层次的一个优化

327
00:08:59,340 --> 00:09:02,420
好 我们来看一下进行的什么样的一个优化

328
00:09:02,420 --> 00:09:04,240
大家可以看到这样一个标题

329
00:09:04,240 --> 00:09:07,240
HTP2.0的多罗夫人和我们的一个长链接复用有什么区别

330
00:09:07,240 --> 00:09:08,280
好 首先

331
00:09:08,280 --> 00:09:10,600
在HTP1的版本里面

332
00:09:10,600 --> 00:09:12,800
一次请求响应建立一个链接用完关闭

333
00:09:12,800 --> 00:09:14,500
每一个请求都需要去建立一个链接

334
00:09:14,500 --> 00:09:17,700
那么在1.0里面的Pipeline它的一个解决方式是什么呢

335
00:09:17,700 --> 00:09:20,740
为若干个请求排队串形化单线程处理

336
00:09:20,740 --> 00:09:24,120
后面的请求等待前面的请求的返回才能获得执行的机会

337
00:09:24,120 --> 00:09:26,080
那么一旦有请求超时

338
00:09:26,080 --> 00:09:28,660
后续的请求只能够阻塞毫无办法

339
00:09:28,660 --> 00:09:29,700
那么多个请求呢

340
00:09:29,700 --> 00:09:31,360
我们再来看一下HTB2里面

341
00:09:31,360 --> 00:09:32,040
它的多个请求呢

342
00:09:32,040 --> 00:09:33,560
可以同时在一个连接上并行的执行

343
00:09:33,560 --> 00:09:35,420
那么如果说某个人耗时严重

344
00:09:35,420 --> 00:09:37,380
不会影响到其他连接的正常执行

345
00:09:37,380 --> 00:09:40,480
我们就来理解一下咱们的一个

346
00:09:40,480 --> 00:09:44,580
若干个请求排队串行化单线程处理

347
00:09:44,580 --> 00:09:47,500
和我们多个请求可以在同一个连接上并行执行

348
00:09:47,500 --> 00:09:48,780
我们来看一下他们的区别

349
00:09:48,780 --> 00:09:49,700
其实很好理解

350
00:09:49,700 --> 00:09:51,980
那么假如说这里是我们1.1的版本

351
00:09:51,980 --> 00:09:53,580
我们去建立了一个链接之后

352
00:09:53,580 --> 00:09:56,200
我们是不是可以把它想象为一个车道

353
00:09:56,200 --> 00:09:57,940
蓝色的就是我们的车子

354
00:09:57,940 --> 00:09:59,480
我们1.1的版本

355
00:09:59,480 --> 00:10:01,960
我们车子是不是只能一辆一辆的通过

356
00:10:01,960 --> 00:10:03,420
那么如果说我们中间有一辆车

357
00:10:03,420 --> 00:10:03,840
抛锚了

358
00:10:03,840 --> 00:10:05,080
后面的车是不是都给堵住了

359
00:10:05,080 --> 00:10:07,300
我们就再来看一下咱们2.0里面的多路复用

360
00:10:07,300 --> 00:10:10,420
我们可以看到

361
00:10:10,420 --> 00:10:12,560
多路复用的它描述了

362
00:10:12,560 --> 00:10:13,580
某一个请求任务后是严重

363
00:10:13,580 --> 00:10:15,520
不会影响到其他链接的正常执行

364
00:10:15,520 --> 00:10:18,180
而且可以去并行的执行

365
00:10:18,180 --> 00:10:18,680
什么意思

366
00:10:18,680 --> 00:10:20,420
说明我们HTB2.0

367
00:10:20,420 --> 00:10:22,380
咱们的路是不是变宽了一点了

368
00:10:22,380 --> 00:10:23,980
我们的车子是不是可以并排的去

369
00:10:23,980 --> 00:10:25,720
跑啊

370
00:10:25,720 --> 00:10:26,180
对吧

371
00:10:26,180 --> 00:10:27,060
那么如果说你

372
00:10:27,060 --> 00:10:28,860
如果说你有一辆车坏了

373
00:10:28,860 --> 00:10:30,060
假如说这样一辆车坏了

374
00:10:30,060 --> 00:10:31,320
那么是不是并不影响其他的

375
00:10:31,320 --> 00:10:33,420
其他的车子去通货

376
00:10:33,420 --> 00:10:34,760
除非你全部都坏了

377
00:10:34,760 --> 00:10:36,540
当然这样的几率是非常小的

378
00:10:36,540 --> 00:10:37,720
所以说HTB2.0

379
00:10:37,720 --> 00:10:39,040
它对我们的一个链接

380
00:10:39,040 --> 00:10:40,540
又进行了一个更深层次的优化

381
00:10:40,540 --> 00:10:42,500
接下来我们来看一下黑带压缩

382
00:10:42,500 --> 00:10:44,180
那么前面提到过

383
00:10:44,180 --> 00:10:45,820
咱们HTB传输的过程中

384
00:10:45,820 --> 00:10:47,040
黑带中是不是带有大量的信息

385
00:10:47,040 --> 00:10:49,060
而且呢每次都要重复的发送

386
00:10:49,060 --> 00:10:52,500
那么HCP2.0呢就使用了一种技术

387
00:10:52,500 --> 00:10:55,180
encoder来减少我们传输head的一个大小

388
00:10:55,180 --> 00:10:57,920
那么它呢什么方式呢

389
00:10:57,920 --> 00:11:01,280
就是通过通讯双方各自卡起一份header

390
00:11:01,280 --> 00:11:06,260
这样呢就可以避免了我们去重复传输header

391
00:11:06,260 --> 00:11:07,180
什么意思呢

392
00:11:07,180 --> 00:11:09,520
那么假如说我们的浏览器同时请求了100个js

393
00:11:09,520 --> 00:11:11,160
那么每一个js的header里面

394
00:11:11,160 --> 00:11:12,940
是不是都有content type为什么呀

395
00:11:12,940 --> 00:11:15,760
是不是为rx-javascript在那个头

396
00:11:15,760 --> 00:11:17,660
我们是不是需要传输100次

397
00:11:17,660 --> 00:11:18,860
那么假如说只需要传输一次

398
00:11:18,860 --> 00:11:21,080
这样是不是就结识了100倍的一个空间

399
00:11:21,080 --> 00:11:23,800
那么2.0就是对这一块做一个优化

400
00:11:23,800 --> 00:11:24,920
那么具体如何去做优化

401
00:11:24,920 --> 00:11:26,940
有兴趣的同学可以去研究一下

402
00:11:26,940 --> 00:11:28,120
不过在我们的面试中

403
00:11:28,120 --> 00:11:29,520
很少会遇到这样的问题

404
00:11:29,520 --> 00:11:30,760
同学们可以自己斟酌一下

405
00:11:30,760 --> 00:11:31,440
要不要去了解

406
00:11:31,440 --> 00:11:34,320
2.0还有一个最后一个新的特性

407
00:11:34,320 --> 00:11:36,440
服务端的推送也是Severpress

408
00:11:36,440 --> 00:11:38,060
有兴趣的同学也可以去看一下

409
00:11:38,060 --> 00:11:42,180
这里就是我们HTTP2.0的一些特性

410
00:11:42,180 --> 00:11:43,680
这里我们来简单的回顾一下

411
00:11:43,680 --> 00:11:44,540
我们这节课的一个内容

412
00:11:44,540 --> 00:11:47,740
我们这节课是不是主要就讲解我们1.0和2.0的一个区别

413
00:11:47,740 --> 00:11:48,540
以及它的一个发展

414
00:11:48,540 --> 00:11:52,180
大家可以看到我们HTTP的发展其实并不是凭空而来的

415
00:11:52,180 --> 00:11:54,740
是不是根据咱们的实际的业务场景呢

416
00:11:54,740 --> 00:11:56,240
因为当我们移动端飞速发展之后

417
00:11:56,240 --> 00:11:58,880
大家对网络的性能要求越来越高之后

418
00:11:58,880 --> 00:12:00,960
最开始从1.0升级到了1.1

419
00:12:00,960 --> 00:12:03,000
升级之后对缓存带宽

420
00:12:03,000 --> 00:12:04,200
包括咱们的状态码

421
00:12:04,200 --> 00:12:06,460
host头长链接等等都会做一些优化

422
00:12:06,460 --> 00:12:08,600
那么做完优化之后到了15年

423
00:12:08,600 --> 00:12:10,480
就是到了我们智能手机的一个爆发时期

424
00:12:10,480 --> 00:12:12,840
发现了1.1好像也并不太够

425
00:12:12,840 --> 00:12:15,440
那么2.0也会产生一些新的优化

426
00:12:15,440 --> 00:12:16,500
首先有一个二进制

427
00:12:16,500 --> 00:12:18,280
多罗夫用head的压缩

428
00:12:18,280 --> 00:12:19,000
无端推送

429
00:12:19,000 --> 00:12:20,000
那么2.0里面

430
00:12:20,000 --> 00:12:22,200
其实意义最重大的一个优化就是多罗夫用

431
00:12:22,200 --> 00:12:25,340
以及把我们的一个传输的一个格式变为的二进制

432
00:12:25,340 --> 00:12:26,300
包括head的压缩

433
00:12:26,300 --> 00:12:28,700
其实对我们的性能提升都是非常的明显的

434
00:12:28,700 --> 00:12:30,680
好 这里就是我们这一节课的一个内容

