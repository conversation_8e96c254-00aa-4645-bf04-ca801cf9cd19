1
00:00:00,000 --> 00:00:02,280
这一节课我们就来看一下Redis的一个

2
00:00:02,280 --> 00:00:03,340
安装

3
00:00:03,340 --> 00:00:07,040
好 这里呢老师会演示

4
00:00:07,040 --> 00:00:08,600
Mac上面Redis的安装

5
00:00:08,600 --> 00:00:11,180
在Mac上面去安装Redis其实很简单

6
00:00:11,180 --> 00:00:11,960
只需要一行命令

7
00:00:11,960 --> 00:00:14,640
这里呢也就是老师不断地去推荐同学们

8
00:00:14,640 --> 00:00:15,320
去使用Mac

9
00:00:15,320 --> 00:00:16,620
有能力的同学都会去

10
00:00:16,620 --> 00:00:18,020
都需要去买台Mac的原因

11
00:00:18,020 --> 00:00:20,460
因为它这些环境确实非常的舒服

12
00:00:20,460 --> 00:00:21,160
很好用

13
00:00:21,160 --> 00:00:22,700
那么像Windows的安装的话

14
00:00:22,700 --> 00:00:24,280
这里呢老师就不会去过多的介绍了

15
00:00:24,280 --> 00:00:26,280
因为同学们学习到这样一个阶段

16
00:00:26,280 --> 00:00:27,280
可能安装Redis

17
00:00:27,280 --> 00:00:29,260
需要有些自己去学习的能力

18
00:00:29,260 --> 00:00:34,000
如果这里都需要老师去讲的话 可能就不太好 对吧 好 我们来看一下 首先

19
00:00:34,000 --> 00:00:36,240
锐围呢 它是

20
00:00:36,240 --> 00:00:39,240
麦上面自带的一个安装工具 很方便

21
00:00:39,240 --> 00:00:45,900
类似于npm 咱们直接install redis 这样呢 其实我们的redis就已经安装好 稍微等一下

22
00:00:45,900 --> 00:00:52,740
它呢 正在更新 实际上 break 它的全名叫做home break 这里呢 它是在麦上面自带了一个

23
00:00:52,740 --> 00:00:54,820
安装 安装程序

24
00:00:57,940 --> 00:01:02,240
break 就是home break

25
00:01:02,240 --> 00:01:10,060
好 我们来看一下 redis 5.05 is already installed

26
00:01:10,060 --> 00:01:13,080
其实老师之前已经安装好了 好 我们来看一下

27
00:01:13,080 --> 00:01:16,440
怎么去启动 启动的命令就是 redis-7

28
00:01:16,440 --> 00:01:18,440
redis-7

29
00:01:18,440 --> 00:01:24,820
好 如果说大家看到了这样一个头像就说明我们的 redis 其实已经安装好了

30
00:01:24,820 --> 00:01:25,640
大家看到没有

31
00:01:26,640 --> 00:01:29,960
redis 默认运行端口是6379 它的pid

32
00:01:29,960 --> 00:01:32,260
pid咱们之前在讲解load.js进程的时候是不是说过

33
00:01:32,260 --> 00:01:33,300
pid就是我们的进程号

34
00:01:33,300 --> 00:01:38,240
说明什么问题 咱们redis它的默认端口是6389

35
00:01:38,240 --> 00:01:42,000
但是如果说你想去修改的话 其实也可以通过一个参数-port

36
00:01:42,000 --> 00:01:45,080
这个属性和load.js它的命令其实是很像的

37
00:01:45,080 --> 00:01:46,360
这里呢 我就不去演示

38
00:01:46,360 --> 00:01:51,200
好 那么在redis每次运行的时候 它会去加载一个初始化的配置文件

39
00:01:51,200 --> 00:01:51,980
什么意思

40
00:01:51,980 --> 00:01:55,300
什么意思 同学们 首先我们把进程先断一下

41
00:01:55,300 --> 00:01:56,080
好

42
00:01:56,640 --> 00:02:02,600
什么意思也就说我们每次去执行redis-sever

43
00:02:02,600 --> 00:02:07,200
每次去执行redis-sever它是启动命令

44
00:02:07,200 --> 00:02:09,920
我们在启动redis的时候都会去读取这样一个配置文件

45
00:02:09,920 --> 00:02:11,160
那么在mic下面

46
00:02:11,160 --> 00:02:12,720
它的配置文件在哪里呢

47
00:02:12,720 --> 00:02:15,160
在userlocal这样一个目录下面

48
00:02:15,160 --> 00:02:17,600
好我们来进去看一下它到底长什么样

49
00:02:17,600 --> 00:02:19,460
wim

50
00:02:19,460 --> 00:02:20,800
好大家看到没有

51
00:02:20,800 --> 00:02:21,980
一大档配置文件

52
00:02:21,980 --> 00:02:23,800
好我们来看一下

53
00:02:23,800 --> 00:02:25,400
在讲义里面老师其实已经粘进去了

54
00:02:25,400 --> 00:02:30,520
首先他有一些参数 我们会经常用到 这里呢 我给同学们简单的去介绍一下

55
00:02:30,520 --> 00:02:35,900
第一个demonize这个属性 它是用于来启用守护进程的

56
00:02:35,900 --> 00:02:40,760
他的radis默认不是以守护进程的方式运行 我们之前是不是讲过什么是守护进程

57
00:02:40,760 --> 00:02:44,080
 他是不是就是像天使一样守护着你的进程 咱们刚才是不是

58
00:02:44,080 --> 00:02:51,200
咱们刚才radis-sever这样呢 启动他是不是守护进程 不是吧 为什么 因为我们可以通过

59
00:02:51,520 --> 00:02:54,600
cntr+c把它给打断 也就是把咱们的server给退出

60
00:02:54,600 --> 00:02:58,180
那么如果说它是守护进程的话 我们不能够通过这一种方式去把它给打断

61
00:02:58,180 --> 00:03:00,740
这里呢就是守护进程的一个配置 它的默认只是no

62
00:03:00,740 --> 00:03:02,780
如果说你需要

63
00:03:02,780 --> 00:03:04,320
把它变为守护进程

64
00:03:04,320 --> 00:03:07,640
你需要把它改为yes 实际上咱们真正的实际项目开放中一般都是yes

65
00:03:07,640 --> 00:03:08,920
好 这里咱们先改回no

66
00:03:08,920 --> 00:03:11,480
那么当radis以守护进程的方式运行时

67
00:03:11,480 --> 00:03:15,320
它的默认会把pid 也就是咱们的进程号会写入这样一个文件中

68
00:03:15,320 --> 00:03:16,360
这里呢

69
00:03:17,120 --> 00:03:20,440
咱们可以通过profile上一个参数去修改它在一个默认的文件

70
00:03:20,440 --> 00:03:24,540
好咱们来看一下这里有个port属性是不是咱们的默认的端口啊port

71
00:03:24,540 --> 00:03:30,680
bind就是去绑定你的主机地址默认的都是我们的127.0.0.1

72
00:03:30,680 --> 00:03:33,000
什么意思是不是我们的本地也就是localhost

73
00:03:33,000 --> 00:03:35,300
也就是

74
00:03:35,300 --> 00:03:39,900
本地好我们再来看一下timeout是什么

75
00:03:39,900 --> 00:03:43,480
timeout就是当客户端闲置多长时间后关闭连接

76
00:03:43,480 --> 00:03:44,760
什么意思啊

77
00:03:45,280 --> 00:03:47,500
咱们Redis server是不是起了一个服务

78
00:03:47,500 --> 00:03:49,860
那么如果说你长时间没有去操作它

79
00:03:49,860 --> 00:03:50,820
它就会自动的去关闭

80
00:03:50,820 --> 00:03:51,720
如果说你改为0

81
00:03:51,720 --> 00:03:53,040
说明了关闭该功能

82
00:03:53,040 --> 00:03:53,860
关闭该功能

83
00:03:53,860 --> 00:03:54,940
说明了我们的客户端

84
00:03:54,940 --> 00:03:56,120
无论闲置多久

85
00:03:56,120 --> 00:03:57,180
它都不会去关闭链接

86
00:03:57,180 --> 00:03:58,240
你如果说需要去改的话

87
00:03:58,240 --> 00:03:59,680
你就去改Timeout的属性

88
00:03:59,680 --> 00:04:01,700
接下来是log

89
00:04:01,700 --> 00:04:02,500
log

90
00:04:02,500 --> 00:04:03,400
leaf

91
00:04:03,400 --> 00:04:05,660
也就是咱们的日志记录级别

92
00:04:05,660 --> 00:04:06,700
那么咱们在项目开服中

93
00:04:06,700 --> 00:04:07,700
不管是前端loadds

94
00:04:07,700 --> 00:04:08,420
包括我们的数据库

95
00:04:08,420 --> 00:04:09,520
都会有一些日志存储

96
00:04:09,520 --> 00:04:11,220
那么它通过这个字段去修改

97
00:04:11,220 --> 00:04:13,620
它有几个级别

98
00:04:13,620 --> 00:04:15,540
Debug Lotus Warning

99
00:04:15,540 --> 00:04:16,880
Webos等等

100
00:04:16,880 --> 00:04:18,960
这里呢老师呢就不去具体的去介绍

101
00:04:18,960 --> 00:04:21,580
咱们后面的项目开发中会对它有一些使用

102
00:04:21,580 --> 00:04:23,560
这里呢是日子记录方式

103
00:04:23,560 --> 00:04:25,620
好 这里呢是Logfire

104
00:04:25,620 --> 00:04:27,500
接下来是Database

105
00:04:27,500 --> 00:04:28,680
你比如说我们的Redis

106
00:04:28,680 --> 00:04:30,100
它呢会连接很多的数据库

107
00:04:30,100 --> 00:04:32,500
如果说前面或者说有了就数据库的同学

108
00:04:32,500 --> 00:04:35,020
其实我们的一个服务里面会分为很多的数据库

109
00:04:35,020 --> 00:04:37,240
比如说你一个应用就对应一个数据库

110
00:04:37,240 --> 00:04:38,140
甚至呢多个数据库

111
00:04:38,140 --> 00:04:40,420
咱们实际上一个服务器上面是有很多数据库的

112
00:04:40,420 --> 00:04:42,720
那么Redis呢同样的它也可以去连接很多的数据库

113
00:04:42,720 --> 00:04:45,020
这里呢可以通过去修改它的一个上限

114
00:04:45,020 --> 00:04:48,100
你要去连接多少个数据库就通过database这样一个属性

115
00:04:48,100 --> 00:04:51,680
好接下来呢就是Redis可能一些比较高级的属性

116
00:04:51,680 --> 00:04:52,460
比如说

117
00:04:52,460 --> 00:04:54,760
我们呢

118
00:04:54,760 --> 00:04:57,060
之前老师师傅讲过Redis可以去做缓存

119
00:04:57,060 --> 00:04:58,600
那么我们这里呢

120
00:04:58,600 --> 00:05:01,660
我们缓存之后是不是需要把数据同步到我们的数据库里面去啊

121
00:05:01,660 --> 00:05:03,980
这里呢就是它去同步到数据库的一些规则

122
00:05:03,980 --> 00:05:06,020
比如说900秒有一个更改300

123
00:05:06,020 --> 00:05:07,040
300秒有10个更改

124
00:05:07,040 --> 00:05:08,580
同时满足这些条件的时候

125
00:05:08,840 --> 00:05:10,760
他就可以去把这些数据给同步到数据库里面去

126
00:05:10,760 --> 00:05:11,840
这部分内容呢

127
00:05:11,840 --> 00:05:13,720
咱们在实战中也会去详细的去讲解

128
00:05:13,720 --> 00:05:15,680
包括后面的一些属性

129
00:05:15,680 --> 00:05:17,000
比如说我是否要压缩

130
00:05:17,000 --> 00:05:20,420
包括我们本地数据库的一些文件名工作目录DR

131
00:05:20,420 --> 00:05:22,440
其实呢这里呢只是咱们redis

132
00:05:22,440 --> 00:05:23,540
配置的一部分内容

133
00:05:23,540 --> 00:05:26,420
还有许多的属性同学们可以去自己看一下redis的文档

134
00:05:26,420 --> 00:05:30,900
老师这里呢只是会列出一些咱们会常用的一些属性和一些配置

135
00:05:30,900 --> 00:05:32,960
好 那么我来看一下

136
00:05:32,960 --> 00:05:35,660
咱们刚才是不是已经用redis

137
00:05:35,660 --> 00:05:37,700
redis server已经起了咱们那个服务

138
00:05:37,700 --> 00:05:39,740
那么我们怎么样去操作咱们的redis呢 这样呢

139
00:05:39,740 --> 00:05:42,560
需要使用到一个东西叫做redis CLI

140
00:05:42,560 --> 00:05:47,420
他有点类似于咱们的load命令 比如说咱们执行一个load是不是进入一个交互模式

141
00:05:47,420 --> 00:05:50,500
 比如说我们去打印一个console.log

142
00:05:50,500 --> 00:05:53,060
好了

143
00:05:53,060 --> 00:05:56,900
这里咱们是不是就打印出了一个hello这样一个支付创 那么redis CLI

144
00:05:56,900 --> 00:06:00,220
同样的他也是一个交互式命令行客户端

145
00:06:00,220 --> 00:06:05,340
他有点类似于load.js 那我们来执行看一下

146
00:06:05,340 --> 00:06:07,400
redis

147
00:06:07,700 --> 00:06:13,200
-cli 好 大家看到没有 我们其实已经进入了一个交互式的命令窗口 我们呢可以去执行一些操作

148
00:06:13,200 --> 00:06:16,140
 操作谁呢 也就是操作咱们刚才起的这样一个radis的server

149
00:06:16,140 --> 00:06:19,480
比如说我们去执行一个命令 叫做sharddown

150
00:06:19,480 --> 00:06:24,080
咱们来看一下sharddown是做什么呢 他其实是关闭我们和radis的一个链接

151
00:06:24,080 --> 00:06:27,740
咱们来执行一下sharddown

152
00:06:27,740 --> 00:06:29,780
好 大家看到没有

153
00:06:29,780 --> 00:06:34,780
拜拜 说明咱们的服务已经退出了 是因为咱们的cli执行这样一个指令sharddown

154
00:06:34,780 --> 00:06:36,540
 这里也变成了lotconnected

155
00:06:37,640 --> 00:06:41,120
好 这里呢就是这一节课的内容 我们一起来总结一下

156
00:06:41,120 --> 00:06:52,240
好 刚才我们是不是讲解了redis的一个安装 它的命令是什么样

157
00:06:52,240 --> 00:06:58,000
在mac里面是不是bray install redis 好 那么在windows下面的安装呢

158
00:06:58,000 --> 00:07:04,240
请自行百度 因为学习了咱们这样一个阶段 我相信你是有

159
00:07:04,740 --> 00:07:09,340
自己去学习的一个能力好 那么我们刚才命令是不是执行的两条命令了同学们

160
00:07:09,340 --> 00:07:09,740
 第一个

161
00:07:09,740 --> 00:07:12,760
radish server 他是做什么的

162
00:07:12,760 --> 00:07:15,540
他是做什么的 是不是去

163
00:07:15,540 --> 00:07:16,940
起一个

164
00:07:16,940 --> 00:07:18,620
服务啊

165
00:07:18,620 --> 00:07:23,680
那么还有呢radish CLI 做什么 是不是操作咱的radish数据库

166
00:07:23,680 --> 00:07:28,800
他是一个命令行

167
00:07:28,800 --> 00:07:31,880
操作数据库

168
00:07:32,900 --> 00:07:36,480
包括了我们刚才还提到了一个redis.conf

169
00:07:36,480 --> 00:07:38,020
他呢根据咱们的操作系统不一样

170
00:07:38,020 --> 00:07:39,040
他的存储地方不一样

171
00:07:39,040 --> 00:07:40,320
他可以去修改哪些内容

172
00:07:40,320 --> 00:07:43,660
是不是可以修改我们的端口

173
00:07:43,660 --> 00:07:45,440
我们的一个

174
00:07:45,440 --> 00:07:46,980
数据库

175
00:07:46,980 --> 00:07:49,800
连接数包括咱们的一个

176
00:07:49,800 --> 00:07:51,080
数据

177
00:07:51,080 --> 00:07:53,380
同步的一些相关的配置

178
00:07:53,380 --> 00:07:56,460
那么redis.conf他的内容是比较多的

179
00:07:56,460 --> 00:07:57,980
这里呢同学们可以去

180
00:07:57,980 --> 00:08:00,800
参考一下文档或者说把刚才老师所讲解的

181
00:08:01,820 --> 00:08:03,380
讲义再去看一下

182
00:08:03,380 --> 00:08:05,380
这里就是这几块的内容

