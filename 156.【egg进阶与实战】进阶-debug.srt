1
00:00:00,000 --> 00:00:02,700
好,我们已经完成了咱们前面的基础学习

2
00:00:02,700 --> 00:00:05,420
那么从这节课开始我们就要进行1GG的进阶与实战

3
00:00:05,420 --> 00:00:07,760
那么我们先来看一下它的一些进阶内容

4
00:00:07,760 --> 00:00:09,460
也就是咱们一些稍微高级一点的技巧

5
00:00:09,460 --> 00:00:12,380
这节课我们就来看一下怎么样去debug我们的一个项目

6
00:00:12,380 --> 00:00:15,120
大家还记得我们之前是怎么样去debug了

7
00:00:15,120 --> 00:00:18,000
通过什么方式

8
00:00:18,000 --> 00:00:19,860
是不是通过console.log

9
00:00:19,860 --> 00:00:20,300
对吧

10
00:00:20,300 --> 00:00:22,100
我们所有的代码都是logloglog

11
00:00:22,100 --> 00:00:23,160
那么这里有什么问题啊

12
00:00:23,160 --> 00:00:23,800
通过console.log

13
00:00:23,800 --> 00:00:25,220
是不是我们有些介绍对象里

14
00:00:25,220 --> 00:00:26,120
比如说非常大的情况下

15
00:00:26,120 --> 00:00:28,380
我们是不是很难看出来一些信息

16
00:00:28,380 --> 00:00:30,100
所以我们必须要有debug的手段

17
00:00:30,100 --> 00:00:31,320
那么我们之前讲过一把

18
00:00:31,320 --> 00:00:32,080
些debug的方式

19
00:00:32,080 --> 00:00:33,080
咱们的vscode

20
00:00:33,080 --> 00:00:34,840
是不是自带这样一个小虫子

21
00:00:34,840 --> 00:00:36,040
可以去帮我们去debug

22
00:00:36,040 --> 00:00:37,780
那么它是怎么样去做的呢

23
00:00:37,780 --> 00:00:39,040
是不是咱们有个配置文件

24
00:00:39,040 --> 00:00:39,780
然后呢去

25
00:00:39,780 --> 00:00:40,380
对吧

26
00:00:40,380 --> 00:00:41,380
直接执行我们的JS

27
00:00:41,380 --> 00:00:42,760
然后就可以进入一个debug模式

28
00:00:42,760 --> 00:00:44,100
但是一GG的项目可不可以

29
00:00:44,100 --> 00:00:45,940
好像不行

30
00:00:45,940 --> 00:00:46,180
不知道

31
00:00:46,180 --> 00:00:47,100
那么我们来看一下

32
00:00:47,100 --> 00:00:48,040
它的pocket几点接生

33
00:00:48,040 --> 00:00:49,340
我们就能明白了

34
00:00:49,340 --> 00:00:50,980
好

35
00:00:50,980 --> 00:00:51,960
我们来看一下咱们项目中的

36
00:00:51,960 --> 00:00:53,040
pocket几点接生长什么样

37
00:00:53,040 --> 00:00:54,540
我们之前一直执行的命令是什么

38
00:00:54,540 --> 00:00:56,280
一直执行的命令是什么

39
00:00:56,280 --> 00:01:00,880
我们之前是一直在执行npm run dv 其实呢 它是1gg

40
00:01:00,880 --> 00:01:07,040
执行的1gg是不是干并 它那个dv 其实我们1gg 其实已经封装了我们的开发环境的一些方法

41
00:01:07,040 --> 00:01:10,880
你比如说我们保存代码之后 咱们是不是可以去自动的进行热更新 那么其实呢

42
00:01:10,880 --> 00:01:14,720
 都是1gg 它所封装的这样一个咱们一个开发一个一个开发环境

43
00:01:14,720 --> 00:01:18,770
可以提供我们这样的去使用 那么同样的 它其实也提供了一个debug的方法

44
00:01:18,770 --> 00:01:20,860
 那么怎么样去用呢 其实也很简单

45
00:01:21,880 --> 00:01:23,800
也是很简单的 你比如说我们这里

46
00:01:23,800 --> 00:01:27,440
是不是有一个debug的命令 这是我们生成项目脚手架的时候它自动生成的

47
00:01:27,440 --> 00:01:29,680
 这里呢它会执行1GG并debug

48
00:01:29,680 --> 00:01:32,880
这里呢我们就进入了一个debug的模式 那么我们就来试一下

49
00:01:32,880 --> 00:01:41,080
我们之前启动项目是不是通过runDV样 那么我们这里要通过debug来run一下看我们项目是不是照样可以去运行

50
00:01:41,080 --> 00:01:44,920
好 这里呢我们是不是通过debug已经运行的项目 那么呢我们访问701

51
00:01:44,920 --> 00:01:49,520
是不是同样的可以访问到我们之前的一些接口 包括了你去访问一些咱们之前

52
00:01:50,040 --> 00:01:52,080
我们之前是不是通过模板去渲染了一些页面

53
00:01:52,080 --> 00:01:53,880
好 我们来看一下有哪些页面

54
00:01:53,880 --> 00:01:55,420
extend

55
00:01:55,420 --> 00:01:57,980
controller 我们来看一下controller吧

56
00:01:57,980 --> 00:02:00,280
里面是不是有个random 那么呢 我们来执行一下

57
00:02:00,280 --> 00:02:01,040
hello

58
00:02:01,040 --> 00:02:03,600
其实大家可以看到 我们通过debug去

59
00:02:03,600 --> 00:02:07,440
运行我们的页面 实际上和我们的dv是一样的 但是它有一个区别是什么呢

60
00:02:07,440 --> 00:02:08,980
这里大家可以看到 它有个提示

61
00:02:08,980 --> 00:02:13,600
dvtools 然后呢 你可以访问在一个链接 就进入一个debug模式 那么我们就来看一下

62
00:02:18,720 --> 00:02:23,800
好 大家可以看到 我们是不是进入了这样一个调试界面了 这里是不是和我们去调试浏览器一模一样

63
00:02:23,800 --> 00:02:26,920
那么同样的 其实它也可以去调试我们的一个loader.js

64
00:02:26,920 --> 00:02:32,870
我们来看一下具体怎么去调试 首先我们可以看到在sauce 大家一定要注意

65
00:02:32,870 --> 00:02:34,080
 在sauce这样一个目录下面

66
00:02:34,080 --> 00:02:37,370
sauce 这里是不是有个fail 那么这里我们就可以看到我们本地开发的代码

67
00:02:37,370 --> 00:02:38,680
 大家看是不是有APP

68
00:02:38,680 --> 00:02:42,780
对吧 然后app.js是不是我们刚才去写了一些启动的生命周期

69
00:02:42,780 --> 00:02:47,640
然后在controller里面我们的home.js 对吧 和我们的业务代码是不是一模一样的

70
00:02:47,900 --> 00:02:49,060
那么我们来打个断点试一下

71
00:02:49,060 --> 00:02:50,260
看到底是不是怎么回事

72
00:02:50,260 --> 00:02:51,740
比如说我们呢

73
00:02:51,740 --> 00:02:53,540
在我们home在一个路由这里

74
00:02:53,540 --> 00:02:54,980
当我们去访问根目录的时候

75
00:02:54,980 --> 00:02:55,580
是不是就在这里

76
00:02:55,580 --> 00:02:57,380
就需要给卡住了

77
00:02:57,380 --> 00:02:57,580
对吧

78
00:02:57,580 --> 00:02:59,020
我们想看一下context是什么

79
00:02:59,020 --> 00:02:59,500
那么呢

80
00:02:59,500 --> 00:03:00,420
我们来打个断点看一下

81
00:03:00,420 --> 00:03:01,320
好

82
00:03:01,320 --> 00:03:01,980
比如说呢

83
00:03:01,980 --> 00:03:03,740
我们去访问一下根目录

84
00:03:03,740 --> 00:03:04,440
好

85
00:03:04,440 --> 00:03:05,460
这里大家可以看到

86
00:03:05,460 --> 00:03:06,700
我们的代码是不是已经

87
00:03:06,700 --> 00:03:09,180
已经走到了我们的断点

88
00:03:09,180 --> 00:03:10,140
那么我们来看一下我们的

89
00:03:10,140 --> 00:03:12,520
我们的浏览器是不是一个挂起状态啊

90
00:03:12,520 --> 00:03:12,960
说明什么

91
00:03:12,960 --> 00:03:14,340
是不是他在等待我们的一个response

92
00:03:14,340 --> 00:03:15,160
因为呢

93
00:03:15,160 --> 00:03:16,220
我们这里在response这里

94
00:03:16,220 --> 00:03:16,940
是不是给了一个断点

95
00:03:16,940 --> 00:03:18,000
所以我们的页面

96
00:03:18,000 --> 00:03:19,960
它无法得到我们的一个响应

97
00:03:19,960 --> 00:03:20,840
所以就会挂起

98
00:03:20,840 --> 00:03:24,540
那么如果说我们想要让它得到响应

99
00:03:24,540 --> 00:03:25,660
我们直接跳过这一步

100
00:03:25,660 --> 00:03:27,820
大家可以看到我们是不是就得到我们响应的结果

101
00:03:27,820 --> 00:03:28,880
当我们再次刷新的时候

102
00:03:28,880 --> 00:03:30,360
是不是又会进入到我们一个端点

103
00:03:30,360 --> 00:03:31,840
因为咱们的request又过来了

104
00:03:31,840 --> 00:03:33,540
那么我们就来看一下我们的context长什么样

105
00:03:33,540 --> 00:03:36,740
好 这里的context其实还看不到

106
00:03:36,740 --> 00:03:37,640
因为我们的端点在这一行

107
00:03:37,640 --> 00:03:38,980
其实我们可以在this里面去看

108
00:03:38,980 --> 00:03:40,920
大家可以看到context里面所有的对象

109
00:03:40,920 --> 00:03:43,020
我们是不是都可以通过浏览器调试的一种方式

110
00:03:43,020 --> 00:03:45,580
就可以看到它里面所有的对象以及属性

111
00:03:45,580 --> 00:03:46,480
包括一些方法

112
00:03:46,480 --> 00:03:49,800
那么是不是比我们直接通过concel.log在控制台里面去打印的自创

113
00:03:49,800 --> 00:03:50,580
是不是

114
00:03:50,580 --> 00:03:52,120
方便很多呀对吧

115
00:03:52,120 --> 00:03:54,420
那么这里呢就是咱们一个调试技巧

116
00:03:54,420 --> 00:03:57,480
那么我们的调试步骤

117
00:03:57,480 --> 00:04:00,820
首先呢第一步打开我们呢对吧我们去执行npm run

118
00:04:00,820 --> 00:04:04,920
debug其实呢他呢还是一些参数比如说你可以指定他的一些端口

119
00:04:04,920 --> 00:04:07,720
然后我们这里其实是不需要配置的你用默认的情况就可以了

120
00:04:07,720 --> 00:04:10,280
那么同样的你执行debug命运时那么应用呢

121
00:04:10,280 --> 00:04:12,340
他呢也是以什么环境去执行debug

122
00:04:12,340 --> 00:04:15,160
也是dv也是咱们的一个local环境读取的配置也就是咱们的

123
00:04:15,660 --> 00:04:19,900
defaultgs和我们的一个config.local.js

124
00:04:19,900 --> 00:04:20,900
那么我们的调试步骤

125
00:04:20,900 --> 00:04:22,520
首先我们执行npm run debug的时候

126
00:04:22,520 --> 00:04:24,000
是不是会给我们提供一个地址

127
00:04:24,000 --> 00:04:24,620
对吧

128
00:04:24,620 --> 00:04:25,460
提供地址

129
00:04:25,460 --> 00:04:26,460
然后呢

130
00:04:26,460 --> 00:04:29,560
然后你选择一个什么sauce

131
00:04:29,560 --> 00:04:31,260
sauce里面是不是就可以看到我们的一个原代码

132
00:04:31,260 --> 00:04:32,720
然后在你需要调试的地方打上断点

133
00:04:32,720 --> 00:04:34,660
然后进行我们的一个ftp的访问

134
00:04:34,660 --> 00:04:37,860
这里呢就是我们一级记里面去咱们进行debug的方式

135
00:04:37,860 --> 00:04:39,120
当然了还有一些其他的方式

136
00:04:39,120 --> 00:04:40,200
但是我们学会一种就可以了

137
00:04:40,200 --> 00:04:41,860
而且这种方式也是非常好的

138
00:04:41,860 --> 00:04:43,960
好这里呢就是我们这几个的内容

