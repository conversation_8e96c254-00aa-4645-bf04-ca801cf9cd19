1
00:00:00,760 --> 00:00:01,800
好 同学们大家好

2
00:00:01,800 --> 00:00:05,640
那么从今天开始我们就来学习咱们nodejs它的一些外民用开发框架

3
00:00:05,640 --> 00:00:07,680
这里我们主要会讲解

4
00:00:07,680 --> 00:00:08,700
三个

5
00:00:08,700 --> 00:00:13,060
第一个cora 第二个egg 第三个adolis 其实我们的重点是cora和egg

6
00:00:13,060 --> 00:00:16,640
那么其实呢egg它是基于cora去进行的二次开发

7
00:00:16,640 --> 00:00:20,480
也就是针对咱们的企业级 咱们的大型应用去对cora进行的一层封装

8
00:00:20,480 --> 00:00:23,800
那么egg呢 其实它也是阿里巴巴开源的一个非常火的一个nodejs框架

9
00:00:23,800 --> 00:00:28,160
所以我们重点是放在egg的 那么egg呢 它是基于cora的 所以我们也要去学习cora

10
00:00:28,160 --> 00:00:29,700
像adolis

11
00:00:29,960 --> 00:00:32,060
他和咱们的一GG这样一个框架解决问题类似

12
00:00:32,060 --> 00:00:33,420
所以我们重点介绍一GG

13
00:00:33,420 --> 00:00:35,800
到底是同学们可以去简单的了解一下

14
00:00:35,800 --> 00:00:37,500
那么当然我们的学习目标是什么

15
00:00:37,500 --> 00:00:39,920
是不是掌握nodejs后端的开发思想

16
00:00:39,920 --> 00:00:41,760
这里来问同学们一个问题

17
00:00:41,760 --> 00:00:44,580
我们为什么要使用nodejs去开发后端

18
00:00:44,580 --> 00:00:45,980
我们为什么要去学习nodejs

19
00:00:45,980 --> 00:00:49,000
其实在我们的企业应用中

20
00:00:49,000 --> 00:00:50,120
我们前端共同事的职责是什么

21
00:00:50,120 --> 00:00:51,540
是不是去写界面

22
00:00:51,540 --> 00:00:53,940
完成我们前端和后台的一些交互

23
00:00:53,940 --> 00:00:55,200
那么有的同学可能会问

24
00:00:55,200 --> 00:00:57,500
我一个前端共同事我为什么要学习后端

25
00:00:57,500 --> 00:00:59,000
我为什么要去学习后段的框架

26
00:00:59,000 --> 00:01:00,320
nosegs难道不是后段写的吗

27
00:01:00,320 --> 00:01:02,180
其实如果说在2019年的今天

28
00:01:02,180 --> 00:01:03,500
如果你还这么想

29
00:01:03,500 --> 00:01:05,480
可能说你就已经落后于别人

30
00:01:05,480 --> 00:01:05,680
为什么

31
00:01:05,680 --> 00:01:08,000
因为现在我们的市场竞争越来越激烈

32
00:01:08,000 --> 00:01:09,360
咱们前段工程师也越来越多

33
00:01:09,360 --> 00:01:10,360
如果说你还停留在

34
00:01:10,360 --> 00:01:11,420
只会写CSS js

35
00:01:11,420 --> 00:01:13,560
只会写特效动画的程度

36
00:01:13,560 --> 00:01:15,440
可能在未来你可能会被淘汰

37
00:01:15,440 --> 00:01:16,300
那么同样的

38
00:01:16,300 --> 00:01:17,640
你怎么样去提示你的竞争力

39
00:01:17,640 --> 00:01:20,060
你如果说能够成为一名全站工程师

40
00:01:20,060 --> 00:01:21,880
是不是会更加的受欢迎

41
00:01:21,880 --> 00:01:23,000
包括一些一线的企业

42
00:01:23,000 --> 00:01:25,380
他们现在对前段的要求

43
00:01:25,380 --> 00:01:26,020
你一定要会

44
00:01:26,020 --> 00:01:27,660
使用nodejs去开发福端

45
00:01:27,660 --> 00:01:29,660
因为nodejs它不仅是一门后端语言

46
00:01:29,660 --> 00:01:30,700
它呢还是一门攻击语言

47
00:01:30,700 --> 00:01:31,700
比如说wapack

48
00:01:31,700 --> 00:01:33,260
它也是基本nodejs去开发的

49
00:01:33,260 --> 00:01:33,940
那么你能说

50
00:01:33,940 --> 00:01:34,880
你作为一名前端公司

51
00:01:34,880 --> 00:01:37,020
我去理解wapack它的一些原理

52
00:01:37,020 --> 00:01:37,720
对吗

53
00:01:37,720 --> 00:01:39,260
如果说你想去更深入的

54
00:01:39,260 --> 00:01:40,460
学习前端的一些工具以及框架

55
00:01:40,460 --> 00:01:41,500
你必须要使用好nodejs

56
00:01:41,500 --> 00:01:42,960
包括了以及后端的一些思想

57
00:01:42,960 --> 00:01:44,500
这就是我们为什么要学习nodejs

58
00:01:44,500 --> 00:01:46,880
以及呢学习nodejs的一些框架的原因

59
00:01:46,880 --> 00:01:47,480
好

60
00:01:47,480 --> 00:01:48,420
那么我们的学习目标

61
00:01:48,420 --> 00:01:50,140
当然是掌握我们的后端开发思想

62
00:01:50,140 --> 00:01:50,840
第二个呢

63
00:01:50,840 --> 00:01:52,700
掌握Core的基本使用

64
00:01:52,700 --> 00:01:53,620
以及它的一些生态

65
00:01:53,620 --> 00:01:56,040
包括能够使用EGG去开发一些实际项目

66
00:01:56,040 --> 00:01:56,420
好

67
00:01:56,420 --> 00:01:58,500
这里我们了解了我们的目标以及原因

68
00:01:58,500 --> 00:02:00,180
包括咱们怎么样去学习

69
00:02:00,180 --> 00:02:02,140
那么我们就正式进入第一张

70
00:02:02,140 --> 00:02:03,240
Core入门语使用

71
00:02:03,240 --> 00:02:04,500
好

72
00:02:04,500 --> 00:02:06,840
那么这节课我主要会带同学们去学习一下

73
00:02:06,840 --> 00:02:08,320
Core它的简介与安装

74
00:02:08,320 --> 00:02:09,540
以及它的一些基本用法

75
00:02:09,540 --> 00:02:11,260
首先什么是Core呢

76
00:02:11,260 --> 00:02:11,820
大家可以看到

77
00:02:11,820 --> 00:02:14,800
这里是我从咱们Core的观望里面

78
00:02:14,800 --> 00:02:15,520
结一张图

79
00:02:15,520 --> 00:02:18,020
Core基于NodeGIS平台的下一代

80
00:02:18,020 --> 00:02:18,980
web开发框架

81
00:02:18,980 --> 00:02:19,580
什么意思

82
00:02:19,580 --> 00:02:20,380
重点是哪句话

83
00:02:20,380 --> 00:02:22,140
其实重点是下一代

84
00:02:22,140 --> 00:02:23,020
什么意思

85
00:02:23,020 --> 00:02:25,280
我们前端是不是一直在发展

86
00:02:25,280 --> 00:02:26,200
分为上一代

87
00:02:26,200 --> 00:02:26,580
下一代

88
00:02:26,580 --> 00:02:27,080
下下代

89
00:02:27,080 --> 00:02:27,960
对吧

90
00:02:27,960 --> 00:02:28,520
那么呢

91
00:02:28,520 --> 00:02:29,560
下一代的意思说明

92
00:02:29,560 --> 00:02:30,940
它的一个思想是不是很先进

93
00:02:30,940 --> 00:02:32,300
它是一个很新的一个框架

94
00:02:32,300 --> 00:02:33,400
那么有的朋友可能说

95
00:02:33,400 --> 00:02:34,380
老师我学不动了

96
00:02:34,380 --> 00:02:35,360
你今天来个上一代

97
00:02:35,360 --> 00:02:36,100
明天来个下一代

98
00:02:36,100 --> 00:02:37,520
然后后天可能来个下下代

99
00:02:37,520 --> 00:02:37,800
怎么办

100
00:02:37,800 --> 00:02:38,620
没办法

101
00:02:38,620 --> 00:02:39,140
你必须要学

102
00:02:39,140 --> 00:02:40,160
这也就是前端的一个魅力

103
00:02:40,160 --> 00:02:41,580
你如果说希望一个框架用十年

104
00:02:41,580 --> 00:02:42,200
你可以选择去

105
00:02:42,200 --> 00:02:43,140
写Java

106
00:02:43,140 --> 00:02:43,440
对吧

107
00:02:43,440 --> 00:02:44,800
那么像Java的Spring MVC

108
00:02:44,800 --> 00:02:45,680
可能说这样一个框架

109
00:02:45,680 --> 00:02:46,160
你能用10年

110
00:02:46,160 --> 00:02:47,860
但是前端是不存在的

111
00:02:47,860 --> 00:02:49,420
前端的发展确实是非常迅速

112
00:02:49,420 --> 00:02:50,100
所以说同学们

113
00:02:50,100 --> 00:02:50,780
一定不要停止

114
00:02:50,780 --> 00:02:51,580
咱们学习了一个角度

115
00:02:51,580 --> 00:02:55,580
好我们来看一下Core

116
00:02:55,580 --> 00:02:56,640
它一个正式的简介

117
00:02:56,640 --> 00:02:58,320
Core它是一个新的OP框架

118
00:02:58,320 --> 00:02:59,640
由Express

119
00:02:59,640 --> 00:03:00,800
幕后的原班人马打造

120
00:03:00,800 --> 00:03:02,380
Express可能有的同学们听说过

121
00:03:02,380 --> 00:03:03,000
也是在NodeGNS

122
00:03:03,000 --> 00:03:03,840
非常火的一个框架

123
00:03:03,840 --> 00:03:06,080
那么由原班人马打造

124
00:03:06,080 --> 00:03:07,320
可以说明什么问题

125
00:03:07,320 --> 00:03:07,900
同学们

126
00:03:07,900 --> 00:03:08,940
是不是说明Express

127
00:03:08,940 --> 00:03:10,000
它的作者都觉得

128
00:03:10,000 --> 00:03:11,400
Express好像不是很满足

129
00:03:11,400 --> 00:03:12,060
它的一些需求

130
00:03:12,060 --> 00:03:13,440
所以说他们开发了一个新的框架

131
00:03:13,440 --> 00:03:14,300
是不是叫做Core

132
00:03:14,300 --> 00:03:16,420
那么它有什么特点呢

133
00:03:16,420 --> 00:03:17,840
它可以去利用Async

134
00:03:17,840 --> 00:03:19,440
Async是不是咱们ES7里面的内容

135
00:03:19,440 --> 00:03:19,880
对吧

136
00:03:19,880 --> 00:03:21,540
Core帮你丢弃回调函数

137
00:03:21,540 --> 00:03:23,440
并且有利的去进行错误处理

138
00:03:23,440 --> 00:03:25,760
而且Core没有捆绑任何中间键

139
00:03:25,760 --> 00:03:27,200
而是提供你一套优雅的方法

140
00:03:27,200 --> 00:03:29,060
帮助你快速而愉快的编写

141
00:03:29,060 --> 00:03:29,840
服装应用程序

142
00:03:29,840 --> 00:03:31,740
那么这里我们可以提点出几个关键词

143
00:03:31,740 --> 00:03:32,920
Core它的特点是什么

144
00:03:32,920 --> 00:03:34,500
首先是不是新了

145
00:03:34,500 --> 00:03:35,600
第二个下一代

146
00:03:35,600 --> 00:03:35,980
对吧

147
00:03:35,980 --> 00:03:40,260
然后可以通过Async函数

148
00:03:40,260 --> 00:03:43,240
帮你去做一些事情

149
00:03:43,240 --> 00:03:45,320
然后还有一套优雅的方法

150
00:03:45,320 --> 00:03:47,500
新的语法

151
00:03:47,500 --> 00:03:49,060
还有优雅

152
00:03:49,060 --> 00:03:50,760
所以说Core的特点是什么

153
00:03:50,760 --> 00:03:51,320
第一个它新

154
00:03:51,320 --> 00:03:52,180
它是下一代

155
00:03:52,180 --> 00:03:53,880
下一代的咱们的前端

156
00:03:53,880 --> 00:03:54,660
研发方架

157
00:03:54,660 --> 00:03:55,620
意思是什么

158
00:03:55,620 --> 00:03:57,420
很超前

159
00:03:57,420 --> 00:04:00,000
而且它会有一些新的语法

160
00:04:00,000 --> 00:04:02,020
比如说支持咱们的S7

161
00:04:02,020 --> 00:04:02,380
对吧

162
00:04:02,380 --> 00:04:03,740
然后它很优雅

163
00:04:03,740 --> 00:04:04,680
好

164
00:04:04,680 --> 00:04:05,880
这里就是Core的一个简单的介绍

165
00:04:05,880 --> 00:04:06,860
那么我们就来安装一下

166
00:04:06,860 --> 00:04:07,980
那么怎么样去安装呢

167
00:04:07,980 --> 00:04:08,700
其实很简单

168
00:04:08,700 --> 00:04:09,740
Core的安装非常简单

169
00:04:09,740 --> 00:04:12,120
NVM install 7

170
00:04:12,120 --> 00:04:12,420
什么意思

171
00:04:12,420 --> 00:04:16,820
nym是一个nodejs 它的一个包管工具 我们先不用去关注 同学们可以自己下去去看一下

172
00:04:16,820 --> 00:04:21,540
我们实际上重点的是npm install core 就这样一行命令就可以安装咱们的core

173
00:04:21,540 --> 00:04:26,900
好 那么呢core的安装过程呢 老师就不去向同学们去安装了 因为我呢已经安装好了

174
00:04:26,900 --> 00:04:30,900
比如说咱们load install core 就这么简单 一行命就可以安装咱们的core 行

175
00:04:30,900 --> 00:04:34,140
我们来看一下core它的一个基本用法

176
00:04:34,140 --> 00:04:40,700
首先 架设一个http服务

177
00:04:41,700 --> 00:04:46,460
我们的GNS他所要做的第一件事情是不是就是起咱们的一个服务去监听个端口可以进行咱们的HTTP请求

178
00:04:46,460 --> 00:04:49,540
那么我们就来看一下Core他是怎么样去架设一个HTTP服务的

179
00:04:49,540 --> 00:04:51,860
我们这里就来开始编写咱们的第一行Core的代码

180
00:04:51,860 --> 00:04:59,580
好这里呢我来建立一个APP.js首先我们是不是要引入

181
00:04:59,580 --> 00:05:01,040
引入我们的Core呀

182
00:05:01,040 --> 00:05:03,820
比如说我们Core等于Require

183
00:05:03,820 --> 00:05:06,420
Core引入对吧

184
00:05:06,420 --> 00:05:10,200
然后呢我们需要把它给Lew一下Lew一个APP也就是咱们实力

185
00:05:10,200 --> 00:05:12,700
我们这里的调用一个方法

186
00:05:12,700 --> 00:05:14,040
什么方法呢

187
00:05:14,040 --> 00:05:15,640
app.listen

188
00:05:15,640 --> 00:05:17,340
3000

189
00:05:17,340 --> 00:05:18,560
就这样我们就可以

190
00:05:18,560 --> 00:05:20,380
启动咱们一个服务

191
00:05:20,380 --> 00:05:21,460
我们来看一下

192
00:05:21,460 --> 00:05:22,880
这里的三号大法就够了

193
00:05:22,880 --> 00:05:23,460
走向我们去引入

194
00:05:23,460 --> 00:05:24,660
然后去留一个实例

195
00:05:24,660 --> 00:05:25,460
要有listen方法

196
00:05:25,460 --> 00:05:26,580
传入咱们的一个端口

197
00:05:26,580 --> 00:05:27,040
好

198
00:05:27,040 --> 00:05:27,900
我们来看一下

199
00:05:27,900 --> 00:05:28,980
load.x1

200
00:05:28,980 --> 00:05:30,520
app.js

201
00:05:30,520 --> 00:05:31,440
好

202
00:05:31,440 --> 00:05:32,160
是不是启动了

203
00:05:32,160 --> 00:05:32,720
因为咱们的进程

204
00:05:32,720 --> 00:05:33,300
是不是挂起了

205
00:05:33,300 --> 00:05:33,640
好

206
00:05:33,640 --> 00:05:34,640
那么我们就来访问看一下

207
00:05:34,640 --> 00:05:37,820
好

208
00:05:37,820 --> 00:05:38,300
大家可以看到

209
00:05:38,300 --> 00:05:39,060
我们现在

210
00:05:39,060 --> 00:05:39,960
是不是界面上面

211
00:05:39,960 --> 00:05:42,600
那么我们的服务到底起来没有

212
00:05:42,600 --> 00:05:42,920
同学们

213
00:05:42,920 --> 00:05:43,500
试考一下

214
00:05:43,500 --> 00:05:45,020
LotFund什么意思

215
00:05:45,020 --> 00:05:46,000
是不是返回了一个404

216
00:05:46,000 --> 00:05:47,980
说明是不是说明我们服务启动的

217
00:05:47,980 --> 00:05:49,000
那么如果我们关闭

218
00:05:49,000 --> 00:05:49,600
会返回什么

219
00:05:49,600 --> 00:05:51,320
对吧

220
00:05:51,320 --> 00:05:52,140
无法好问持网站

221
00:05:52,140 --> 00:05:53,740
LotFund拒绝了我们的连接请求

222
00:05:53,740 --> 00:05:56,220
所以说咱们其实服务已经启动了

223
00:05:56,220 --> 00:05:58,160
这里就是我们Core去启动

224
00:05:58,160 --> 00:05:58,920
咱们服务的一个方法

225
00:05:58,920 --> 00:06:00,060
好

226
00:06:00,060 --> 00:06:02,540
那我们一起来回顾一下

227
00:06:02,540 --> 00:06:03,360
刚才所讲解的内容

228
00:06:03,360 --> 00:06:08,540
刚才是不是讲到了

229
00:06:08,540 --> 00:06:10,240
Core它是一个什么样的框架呀

230
00:06:10,240 --> 00:06:11,420
同学们

231
00:06:11,420 --> 00:06:13,280
也就是Core的特点

232
00:06:13,280 --> 00:06:13,480
对吧

233
00:06:13,480 --> 00:06:14,240
它有什么特点

234
00:06:14,240 --> 00:06:15,100
是不是下一代

235
00:06:15,100 --> 00:06:16,480
是不是下一代框架

236
00:06:16,480 --> 00:06:17,200
对吧

237
00:06:17,200 --> 00:06:18,540
它的说明什么问题

238
00:06:18,540 --> 00:06:21,060
是不是它的思想

239
00:06:21,060 --> 00:06:22,380
怎么

240
00:06:22,380 --> 00:06:23,060
是不是很超前

241
00:06:23,060 --> 00:06:24,560
第二个呢

242
00:06:24,560 --> 00:06:25,820
语法是不是很新

243
00:06:25,820 --> 00:06:26,620
新的语法

244
00:06:26,620 --> 00:06:27,160
支持到什么

245
00:06:27,160 --> 00:06:27,700
127

246
00:06:27,700 --> 00:06:29,540
而且呢

247
00:06:29,540 --> 00:06:30,740
非常的优雅吧

248
00:06:30,740 --> 00:06:31,820
可以优雅地进行错误处理

249
00:06:31,820 --> 00:06:33,500
优雅地咱们进行一些中间件的处理

250
00:06:33,500 --> 00:06:34,380
好

251
00:06:34,380 --> 00:06:35,700
那么除了它的特点

252
00:06:35,700 --> 00:06:36,220
我们还讲了什么

253
00:06:36,220 --> 00:06:36,880
是不是咱们Core的安装

254
00:06:36,880 --> 00:06:38,120
安装是不是非常简单

255
00:06:38,120 --> 00:06:41,560
就咱们npm install call就可以了

256
00:06:41,560 --> 00:06:44,100
我们刚才是不是还讲了一下它的基本用法

257
00:06:44,100 --> 00:06:45,960
对吧

258
00:06:45,960 --> 00:06:47,700
是不是起了个服务

259
00:06:47,700 --> 00:06:48,980
是不是起服务

260
00:06:48,980 --> 00:06:50,660
起服务怎么起

261
00:06:50,660 --> 00:06:55,040
起服务是怎么起的

262
00:06:55,040 --> 00:06:56,420
是不是直接调用app点

263
00:06:56,420 --> 00:06:58,420
理省了对吧

264
00:06:58,420 --> 00:07:00,440
是不是比如说我们去引入一个call吧

265
00:07:00,440 --> 00:07:01,620
然后去溜了一个实例

266
00:07:01,620 --> 00:07:02,240
溜了之后呢

267
00:07:02,240 --> 00:07:04,600
咱们通过app去监听它的一些端口

268
00:07:04,600 --> 00:07:06,480
这样就可以咱们启动咱们call的第一个服务

269
00:07:06,480 --> 00:07:08,000
好这里呢就是我们这几个的内容

270
00:07:08,000 --> 00:07:09,200
优优独播剧场——YoYo Television Series Exclusive

