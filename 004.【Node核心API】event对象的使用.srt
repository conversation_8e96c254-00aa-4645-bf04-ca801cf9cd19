1
00:00:00,000 --> 00:00:03,480
好 接下来我们来看一下Event事件触发器

2
00:00:03,480 --> 00:00:08,320
那么Event这样一个对象呢 其实在我们的load.js里面非常常用

3
00:00:08,320 --> 00:00:09,660
可以说使用率非常非常高

4
00:00:09,660 --> 00:00:12,440
同时其实它非常的简单

5
00:00:12,440 --> 00:00:15,960
所以我这里还是得啰嗦一下 因为它实在是太常用了

6
00:00:15,960 --> 00:00:17,760
那么我们来看一下它的介绍

7
00:00:17,760 --> 00:00:22,960
大多数load.js核心的API构建于惯用的异步事件驱动架构

8
00:00:22,960 --> 00:00:26,120
那么其中某些类型的对象 又称为触发器emitter

9
00:00:26,120 --> 00:00:29,580
会触发命名事件来调用我们的一个函数

10
00:00:29,580 --> 00:00:35,840
那么所有能够触发事情的对象呢 都是我们的一个EventMeter内的一个实力

11
00:00:35,840 --> 00:00:42,020
好 比如说net.serverfs.resdream 这里呢 我们后面的课程都会去讲到

12
00:00:42,020 --> 00:00:47,180
而且呢 这样一个对象里面的有一个Ung这样一个函数 好 那么Ung呢 其实就可以去监听我们的一个

13
00:00:47,180 --> 00:00:51,300
好 我们呢 就通过这样一个demo 我们来看一下到底如何去使用

14
00:00:51,300 --> 00:00:55,420
首先呢 我们去引入一个EventMeter 然后给它命名为一个EventMeter

15
00:00:55,420 --> 00:00:59,780
而接下来我们去创建一个类 然后去继承它 好 然后继承之后呢 咱们对它进行实际化

16
00:00:59,780 --> 00:01:02,580
好 然后在我们的myameter上面的去

17
00:01:02,580 --> 00:01:04,640
剪定一个事件叫做event

18
00:01:04,640 --> 00:01:07,960
然后再来去通过emeter的去触发它 逻辑非常简单

19
00:01:07,960 --> 00:01:09,500
我们来测试一下

20
00:01:09,500 --> 00:01:12,820
我们首先创建一个

21
00:01:12,820 --> 00:01:14,360
event

22
00:01:14,360 --> 00:01:17,180
然后呢 我们来创建一个event

23
00:01:17,180 --> 00:01:19,220
点s

24
00:01:19,220 --> 00:01:22,040
我们加入刚才的一个代码 好 那么我们直接来运行它

25
00:01:22,040 --> 00:01:23,320
load

26
00:01:25,420 --> 00:01:28,080
event.js

27
00:01:28,080 --> 00:01:31,340
好

28
00:01:31,340 --> 00:01:33,820
可能没有

29
00:01:33,820 --> 00:01:34,500
没保存

30
00:01:34,500 --> 00:01:34,980
我们再来一下

31
00:01:34,980 --> 00:01:35,500
好大家可以看到

32
00:01:35,500 --> 00:01:36,760
这里呢是不是触发了事件了

33
00:01:36,760 --> 00:01:38,240
那么如果说我们刷掉emit呢

34
00:01:38,240 --> 00:01:39,720
我们的event的事件能不能够触发呢

35
00:01:39,720 --> 00:01:42,140
那么呢这里呢肯定是不行的

36
00:01:42,140 --> 00:01:42,320
对吧

37
00:01:42,320 --> 00:01:43,660
我们一定要通过emit

38
00:01:43,660 --> 00:01:46,380
这个方法来触发我们的一个事件

39
00:01:46,380 --> 00:01:47,140
所以说呢

40
00:01:47,140 --> 00:01:49,200
它的核心的两个方法

41
00:01:49,200 --> 00:01:50,160
一个是emit

42
00:01:50,160 --> 00:01:50,840
emit用来监听

43
00:01:50,840 --> 00:01:52,560
emit用来触发我们的事件

44
00:01:52,560 --> 00:01:54,040
其实呢为什么说非常简单

45
00:01:54,040 --> 00:01:59,040
我们的Redux是不是有点类似于这样一个罗家 对吧 而且他的思想来源呢

46
00:01:59,040 --> 00:02:02,100
 其实就是我们的观察者模式 包括后来的一个发布订阅

47
00:02:02,100 --> 00:02:07,310
其实模式是非常类似的 如果说使用过这些设计模式的同学呢 使用我们的一个Event在一个对象呢

48
00:02:07,310 --> 00:02:09,560
 可以说是非常的得心应手 所以说我这点呢 其实

49
00:02:09,560 --> 00:02:14,900
不是很想跟同学们呢 啰嗦太多 好 我们来看一下他的一个第四指向

50
00:02:14,900 --> 00:02:16,220
那么如果说我们

51
00:02:16,220 --> 00:02:20,740
创建了这样一个nbit 这样一个对象之后 我们去监听Event 那么插的回调里面

52
00:02:20,740 --> 00:02:21,400
 第四指谁呢

53
00:02:21,760 --> 00:02:24,980
this其实就是指我们的my emit这样的一个对象

54
00:02:24,980 --> 00:02:28,920
好 这里呢 我就不去给同学们再去代码里面去演示

55
00:02:28,920 --> 00:02:31,380
包括了你呢 也可以去使用一个

56
00:02:31,380 --> 00:02:36,210
键道函数 因为如果说你使用键道函数的话 那么里面的this指向他就会丢失

57
00:02:36,210 --> 00:02:36,760
 对吧

58
00:02:36,760 --> 00:02:40,970
好 那么此时的this呢 他就指向一个空的对象 就不会去指向我们一个my

59
00:02:40,970 --> 00:02:43,880
 emit 这里呢 相当于也是我们的一个小的一个技巧

60
00:02:43,880 --> 00:02:49,700
好 因为我们的even emit他一个特点 他会同步的去调用我们的一个事件

61
00:02:50,220 --> 00:02:53,540
那么如果说你需要去执行一些逆步的方法了 你能可以在我们的回调函数里面

62
00:02:53,540 --> 00:02:59,180
通过一个set immediate 或者 process. next tick 来去进行我们的一个一步

63
00:02:59,180 --> 00:03:03,540
好 那么我们的 on 它有一个特点就是你无论emit多少次

64
00:03:03,540 --> 00:03:06,860
咱们的 event emitter所监定的这一个事件都会去执行

65
00:03:06,860 --> 00:03:09,940
那么假如说我们对它触发两次event的这个事件

66
00:03:09,940 --> 00:03:15,060
我们可以看到我们是不是触发了两次 如果说我们想只触发一次怎么办

67
00:03:15,060 --> 00:03:18,120
其实它也提供了给我们的一个once方法

68
00:03:18,900 --> 00:03:22,810
那么如果说我们使用once 去绑定一个事件的话啊 你无论去 emit多少次

69
00:03:22,810 --> 00:03:23,940
 他呢 都只会去触发一次

70
00:03:23,940 --> 00:03:28,300
其实看到once呢 就越来越像我们的一个观察者 对吧 我们去实现观察者的时候呢

71
00:03:28,300 --> 00:03:32,790
其实呢 也会经常去实现once这样的一个方法 好 假如我们把按改为once

72
00:03:32,790 --> 00:03:33,420
 我们来看一下结果

73
00:03:33,420 --> 00:03:37,640
好 大家可以看到 触发事件 这里是不是只掉了一次啊 好 这里呢 就是once的使用

74
00:03:37,640 --> 00:03:40,060
好 包括呢 这里呢 还会去触发一个

75
00:03:40,060 --> 00:03:44,460
啊 arrow 好 当我们去啊 触发 arrow的时候呢 我们的是不是也会去监听啊

76
00:03:44,460 --> 00:03:45,480
 arrow 这样一个啊

77
00:03:45,480 --> 00:03:47,220
事件了 对吧 好

78
00:03:48,280 --> 00:03:51,100
好 那么下面呢就是关于咱们event对象的一些

79
00:03:51,100 --> 00:03:57,240
其他的一些使用 比如说我们通过remove listen去对我们的时间来进行一个什么

80
00:03:57,240 --> 00:03:57,240
 是不是

81
00:03:57,240 --> 00:03:58,260
取消他的监听

82
00:03:58,260 --> 00:04:01,340
因为如果说我们假如你在一个负循环里面去listen了很多事件

83
00:04:01,340 --> 00:04:05,680
有可能会造成内存的浪费 如果说遇到这样一种场景呢 你可能需要主动的去调用remove

84
00:04:05,680 --> 00:04:07,220
去取消我们的时间

85
00:04:07,220 --> 00:04:11,580
好 那么后面呢 这样的一些介绍的同学们有兴趣的同学可以去读一下他的文档

86
00:04:11,580 --> 00:04:15,420
我们核心呢其实就是说关注咱们的event对象 它的一个什么使用呢

87
00:04:16,180 --> 00:04:20,180
一个on 一个meet 然后呢一个once

88
00:04:20,180 --> 00:04:22,180
同学们掌握他们就已经足够了

89
00:04:22,180 --> 00:04:24,740
好 这里就是我们event对象这一节课的一个内容

