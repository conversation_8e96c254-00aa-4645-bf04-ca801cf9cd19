1
00:00:00,000 --> 00:00:02,040
好 这节课我们就来看一下

2
00:00:02,040 --> 00:00:03,840
Io-Redis它的一些基本语法

3
00:00:03,840 --> 00:00:05,880
首先我们来看一下它的一些简单操作

4
00:00:05,880 --> 00:00:08,700
比如说我们去save 或者get 删除

5
00:00:08,700 --> 00:00:10,500
或者说去给它一个集合

6
00:00:10,500 --> 00:00:12,040
我们来看一下

7
00:00:12,040 --> 00:00:19,200
好 首先我们是不是要把这样一个库给引住啊

8
00:00:19,200 --> 00:00:22,520
比如说我们去挖一个Redis=Requell

9
00:00:22,520 --> 00:00:25,340
什么呢

10
00:00:25,340 --> 00:00:27,900
Io-Redis

11
00:00:30,000 --> 00:00:36,000
好 然后我们去new一个什么呢 我们去new一个Redis拷户端出来

12
00:00:36,000 --> 00:00:43,400
这里呢 咱们就去实力化的一个拷户端 说明了 这一行代码就代表我们的

13
00:00:43,400 --> 00:00:46,200
设计S他已经和我们的设计S服务已经给连接了

14
00:00:46,200 --> 00:00:50,400
那么我们需要去 我们设计S需要去操作我们的Redis 大家一定要注意一点是什么呢

15
00:00:50,400 --> 00:00:54,220
一定要注意我们的服务已经启动了 那么如果说你服务没有启动 你肯定是连接不上的

16
00:00:54,220 --> 00:00:55,200
 不信我们可以试一下

17
00:00:55,200 --> 00:00:57,400
比如说我们走 大家可以看到

18
00:00:57,400 --> 00:01:02,000
说明我们没有连接商 为什么 因为我们的radis服务没有启动

19
00:01:02,000 --> 00:01:03,280
我们启动一下

20
00:01:03,280 --> 00:01:07,640
这里呢 在咱们的6379这个档口

21
00:01:07,640 --> 00:01:09,940
好 我们再来

22
00:01:09,940 --> 00:01:15,840
启动 此时呢没有爆炸 而且呢我们挂起了 我们在一个进程挂起 说明呢

23
00:01:15,840 --> 00:01:17,620
咱们现在连接成功了

24
00:01:17,620 --> 00:01:21,980
首先我们来做第一个操作 比如说我们去radis点 我们去给一个key value

25
00:01:24,540 --> 00:01:25,140
最简单

26
00:01:25,140 --> 00:01:26,900
那我们执行一下

27
00:01:26,900 --> 00:01:32,100
好 我们怎么样去验证这样一个效果 其实很简单 我们刚才是不是安装了合作工具

28
00:01:32,100 --> 00:01:32,780
 让我们进去看一下

29
00:01:32,780 --> 00:01:35,140
首先重链接 好 大家可以看到

30
00:01:35,140 --> 00:01:38,820
大家可以看到现在已经连上了 那么来看一下 这里是不是有一个可以叫做负尔

31
00:01:38,820 --> 00:01:41,100
那么大家可以看到它的类型是什么

32
00:01:41,100 --> 00:01:44,700
死菌 字不算类型 值是什么 吧 也就是我们刚才

33
00:01:44,700 --> 00:01:46,380
也是我们刚才设置的

34
00:01:46,380 --> 00:01:47,220
而且

35
00:01:47,220 --> 00:01:50,260
我们呢也可以去get它 比如说我们去ready 试点

36
00:01:50,260 --> 00:01:52,060
get get谁呢 get 负尔

37
00:01:52,340 --> 00:01:56,440
那么我们的值 它会接收一个在咱们的 这样一个库里面 它会接收一个回答数

38
00:01:56,440 --> 00:02:01,750
第一个参数是error 第二个呢是result 也就是我们返回的结果 大家可以其实可以注意

39
00:02:01,750 --> 00:02:06,420
 在漏的经验室里面 我们的方形 它的参数第一个往往都是error 这是漏的经验室的一种约定

40
00:02:06,420 --> 00:02:16,920
好 那么我们把result给挡出来看一下 看在漏的经验室里面我们挡出来一个什么结果

41
00:02:16,920 --> 00:02:19,740
result

42
00:02:22,340 --> 00:02:27,980
好 首先我们重新跑遍大家可以看到我们的result吧 现在就打出来了 我们现在完成了set和get

43
00:02:27,980 --> 00:02:31,300
那么我们再来

44
00:02:31,300 --> 00:02:37,180
再来进行操作 双除 比如说我们realist 双除怎么双除 其实有这样一个方法叫做dear

45
00:02:37,180 --> 00:02:40,520
比如说我们去

46
00:02:40,520 --> 00:02:45,120
dear什么呢 我们去把我们刚才去set的for把他给拴掉

47
00:02:45,120 --> 00:02:49,220
走 我们再来看一下结果

48
00:02:49,220 --> 00:02:52,040
刷新大家可以看到for是不是消失了

49
00:02:52,340 --> 00:02:52,600
对吧

50
00:02:52,600 --> 00:02:57,200
咱们有了这样一个可造工具是不是就方便了很多 而且他可以看到什么呢

51
00:02:57,200 --> 00:03:01,820
可以看到我们的这样一个酷 他占了多大点个字节 而且会有一些见词统计

52
00:03:01,820 --> 00:03:08,460
好 那么我们继续

53
00:03:08,460 --> 00:03:13,340
我们刚才是不是完成了一些见词队 我们再来看一下怎么样去

54
00:03:13,340 --> 00:03:15,640
给集合给些词 比如说

55
00:03:15,640 --> 00:03:18,200
比如说我们

56
00:03:18,200 --> 00:03:19,480
把这样一个

57
00:03:19,980 --> 00:03:22,280
比如说我们Ready是点 怎么样去操作集合呀

58
00:03:22,280 --> 00:03:26,120
比如说我们去操作一个什么呢 比如说我们去s add

59
00:03:26,120 --> 00:03:27,140
s add

60
00:03:27,140 --> 00:03:32,020
是不是操作我们的集合 比如说我们这个集合就叫做set吧 叫做set 给它些什么值呢

61
00:03:32,020 --> 00:03:35,080
好 这样吧 我们叫

62
00:03:35,080 --> 00:03:46,340
这样我们来看一下 我们所存储的结果是什么

63
00:03:48,900 --> 00:03:52,740
好 走 抓心 我们看下set 大家可以看到

64
00:03:52,740 --> 00:03:57,860
我们是不是有个id 分别为1234 他们的value为1357

65
00:03:57,860 --> 00:04:06,670
我们的s2的 不仅可以这么样去写 我们还可以怎么样呢 我们还可以把参数当成我们的数字

66
00:04:06,670 --> 00:04:08,100
 比如说我们去进行sadd

67
00:04:08,100 --> 00:04:16,540
我们的一个数字 比如说2468 打算把这行注释一下 我们重新跑一遍

68
00:04:18,340 --> 00:04:19,360
好我们再来看一下

69
00:04:19,360 --> 00:04:26,780
好像没有成功

70
00:04:26,780 --> 00:04:28,320
好像我们268没有添加成功

71
00:04:28,320 --> 00:04:29,340
没事我们再来

72
00:04:29,340 --> 00:04:30,380
再来跑边看一下

73
00:04:30,380 --> 00:04:37,280
好大家可以看到

74
00:04:37,280 --> 00:04:39,340
我们的集合是不是变成了12345的七八样

75
00:04:39,340 --> 00:04:40,360
说明了我们

76
00:04:40,360 --> 00:04:42,660
说明我们通过咱们

77
00:04:42,660 --> 00:04:44,200
参数的形式传入

78
00:04:44,200 --> 00:04:46,240
也可以通过我们的数据传入

79
00:04:46,240 --> 00:04:47,520
是不是有点类似于我们的

80
00:04:48,340 --> 00:04:52,380
和什么呀

81
00:04:52,380 --> 00:04:54,700
是不是和call呀 其实都是可以的

82
00:04:54,700 --> 00:04:56,700
而且比如说我们

83
00:04:56,700 --> 00:04:58,380
我们有个需求是什么

84
00:04:58,380 --> 00:05:00,260
需要过期时间

85
00:05:00,260 --> 00:05:01,740
过期时间怎么做

86
00:05:01,740 --> 00:05:02,860
我们其实可以这样

87
00:05:02,860 --> 00:05:05,060
比如说我们去ready.set

88
00:05:05,060 --> 00:05:07,340
假如说我们有一个需要过期的键

89
00:05:07,340 --> 00:05:08,580
给它一个值 叫做100

90
00:05:08,580 --> 00:05:10,900
里面去做咱们在第三个参数传入EX

91
00:05:10,900 --> 00:05:13,140
然后给上它一个时间 比如说5秒

92
00:05:13,140 --> 00:05:15,100
好 那我们来看一下

93
00:05:15,100 --> 00:05:17,020
这个过期时间有没有生效

94
00:05:18,340 --> 00:05:23,820
刷新好大家可以看到我们现在是不是有过期的一个键

95
00:05:23,820 --> 00:05:25,600
但是我们再刷新一次是不是它已经不存在了

96
00:05:25,600 --> 00:05:26,880
说明了它已经过期了

97
00:05:26,880 --> 00:05:28,300
这里我们有这样一个可刷工具

98
00:05:28,300 --> 00:05:29,920
是不是很方便我们去查看

99
00:05:29,920 --> 00:05:30,560
好

100
00:05:30,560 --> 00:05:34,300
这里就是咱们redis它那些简单命令的使用

101
00:05:34,300 --> 00:05:36,340
我们再来看一下我们刚才是不是new的redis

102
00:05:36,340 --> 00:05:38,420
那么其实new的时候我们可以去传入一些参数

103
00:05:38,420 --> 00:05:40,160
比如说我们默认的端口

104
00:05:40,160 --> 00:05:42,760
我们默认的redis启动端口是不是在6379

105
00:05:42,760 --> 00:05:44,020
你比如说你想去改一下端口

106
00:05:44,020 --> 00:05:45,140
你可以传入比如说6378

107
00:05:45,140 --> 00:05:47,040
此时那就改改写了你的一些端口

108
00:05:47,040 --> 00:05:49,400
而且呢你还可以以对象的形式去潜入一些参数

109
00:05:49,400 --> 00:05:53,040
比如说你去修改你的host 去修改你的ipv4或者ipv6

110
00:05:53,040 --> 00:05:54,820
可以使用family这样一个属性

111
00:05:54,820 --> 00:05:56,780
包括了我们的radis数据库是不是会有一些

112
00:05:56,780 --> 00:05:58,140
立马呀 对吧

113
00:05:58,140 --> 00:06:01,700
包括了你可以去选择你的db去连接哪一个

114
00:06:01,700 --> 00:06:02,700
刚才我们是不是讲过了

115
00:06:02,700 --> 00:06:03,960
我们的radis有多少个db

116
00:06:03,960 --> 00:06:04,880
是不是16个

117
00:06:04,880 --> 00:06:06,500
我们刚才一直超过了操作的是几

118
00:06:06,500 --> 00:06:07,820
一直操作的是几是不是0呢

119
00:06:07,820 --> 00:06:08,620
比如说你想去操作

120
00:06:08,620 --> 00:06:11,520
其他的那么你呢就可以去通过这样一些

121
00:06:11,520 --> 00:06:13,320
6radis在一些配置去修改它

122
00:06:13,320 --> 00:06:14,880
这里呢我就不去实际操作了

123
00:06:14,880 --> 00:06:16,260
同学们可以去看一下它的一些配置

124
00:06:16,260 --> 00:06:22,920
这里呢就是redis它的一些基本命令的使用 我们来总结一下

125
00:06:22,920 --> 00:06:37,000
基本命令 首先我们准备去set数价是不是redis.setget呢

126
00:06:38,780 --> 00:06:39,300
是不是radius

127
00:06:39,300 --> 00:06:41,600
radius

128
00:06:41,600 --> 00:06:45,940
get呀好而且呢我们

129
00:06:45,940 --> 00:06:48,500
我们还可以操作什么了是不是radius.set

130
00:06:48,500 --> 00:06:50,300
那么radius.

131
00:06:50,300 --> 00:06:56,700
不对他我们怎么样去操作我们的集合是不是操作我们集合s add 而且他的参数是不是有两种参数

132
00:06:56,700 --> 00:06:59,520
参数是不是可以是

133
00:06:59,520 --> 00:07:01,820
数字也可以是什么呀

134
00:07:01,820 --> 00:07:03,860
也可以是什么是不是也可以是我们的

135
00:07:03,860 --> 00:07:05,660
行参了其实是都可以了

136
00:07:08,780 --> 00:07:16,780
那么除了这点 我们怎么样去给他加入一些过期时间 比如说过期时间

137
00:07:16,780 --> 00:07:24,780
过期时间我们是不是可以这样 比如说我们去readis 怎么去set一个 比如说我们set一个复发

138
00:07:24,780 --> 00:07:29,780
我们是不是可以在第三个参数加入一个ex 然后给他一个时间 对吧 好

139
00:07:29,780 --> 00:07:32,780
而且我们还可以去通过配置

140
00:07:32,780 --> 00:07:37,780
比如说我们去配置一下什么呢 我们去在lue

141
00:07:37,780 --> 00:07:43,160
VueRedis的时候是不是可以去传入一些配材可以修改什么了是不是可以修改我们的

142
00:07:43,160 --> 00:07:49,040
关口包括了我们的你选择的是哪个db对吧我们可以连接16个然后呢host

143
00:07:49,040 --> 00:07:50,320
还有什么

144
00:07:50,320 --> 00:07:54,420
是不是还有familia可以选择我们的ipv4或v6

145
00:07:54,420 --> 00:07:58,780
和v6吧而且呢

146
00:07:58,780 --> 00:08:01,840
你是不是还可以去设置一些密码了

147
00:08:01,840 --> 00:08:04,140
好这里呢就是我们这一节课的内容

