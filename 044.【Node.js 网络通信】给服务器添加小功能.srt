1
00:00:00,000 --> 00:00:01,260
你以为课程结束了吗

2
00:00:01,260 --> 00:00:01,560
错

3
00:00:01,560 --> 00:00:04,600
还记得前面我们在讲解HTTPs原理的时候

4
00:00:04,600 --> 00:00:05,460
我们说过一个问题

5
00:00:05,460 --> 00:00:08,240
说HTTP和HTTPs到底有什么区别呢

6
00:00:08,240 --> 00:00:11,360
那HTTPs就是HTTP的安全版本

7
00:00:11,360 --> 00:00:13,340
也就是说除了在通信过程中的数据加密

8
00:00:13,340 --> 00:00:15,400
其他的和HTTP没有什么区别

9
00:00:15,400 --> 00:00:16,640
那么为了证明这一点

10
00:00:16,640 --> 00:00:18,820
我刚刚偷偷的写了一段代码

11
00:00:18,820 --> 00:00:20,840
其实在原来的基础上又增加了点代码

12
00:00:20,840 --> 00:00:21,660
增加了什么代码呢

13
00:00:21,660 --> 00:00:24,700
你不是说HTTPs和HTTP在传输上

14
00:00:24,700 --> 00:00:25,940
除了证书其他的都一样吗

15
00:00:25,940 --> 00:00:26,140
行

16
00:00:26,140 --> 00:00:28,780
那我也在服务器里通过你的请求对象

17
00:00:28,780 --> 00:00:30,080
获取到KaliMessage

18
00:00:30,080 --> 00:00:30,360
或者什么

19
00:00:30,360 --> 00:00:31,140
你的请求方法

20
00:00:31,140 --> 00:00:31,480
是不是用Gate

21
00:00:31,480 --> 00:00:32,180
同时呢

22
00:00:32,180 --> 00:00:33,340
我也通过URL解析

23
00:00:33,340 --> 00:00:34,780
来获取到你的请求路径

24
00:00:34,780 --> 00:00:35,420
是不是斜杠

25
00:00:35,420 --> 00:00:36,160
然后呢

26
00:00:36,160 --> 00:00:37,520
我能不能通过你的URL

27
00:00:37,520 --> 00:00:39,920
来获取到你传输的Gate的参数

28
00:00:39,920 --> 00:00:40,900
如果这些都能获取到

29
00:00:40,900 --> 00:00:41,720
证明真的没有问题

30
00:00:41,720 --> 00:00:42,120
好

31
00:00:42,120 --> 00:00:42,500
那我们呢

32
00:00:42,500 --> 00:00:42,940
从手段下的

33
00:00:42,940 --> 00:00:43,540
完完整整的

34
00:00:43,540 --> 00:00:44,080
看看这个电脑

35
00:00:44,080 --> 00:00:45,660
到底做了一个什么样的功能

36
00:00:45,660 --> 00:00:46,120
首先呢

37
00:00:46,120 --> 00:00:47,820
我还是引入了ATVS这个模块

38
00:00:47,820 --> 00:00:48,340
然后呢

39
00:00:48,340 --> 00:00:49,480
引入了FS这个模块

40
00:00:49,480 --> 00:00:50,380
然后把URL这个模块呢

41
00:00:50,380 --> 00:00:50,940
也引进来

42
00:00:50,940 --> 00:00:51,160
好

43
00:00:51,160 --> 00:00:51,940
那么ATVS呢

44
00:00:51,940 --> 00:00:52,360
需要证书

45
00:00:52,360 --> 00:00:53,340
那行了和刚才一样的

46
00:00:53,340 --> 00:00:54,520
我把这个证书拿过来

47
00:00:54,520 --> 00:00:55,180
然后再接下来

48
00:00:55,180 --> 00:00:56,440
我依然使用了Create Server

49
00:00:56,440 --> 00:00:57,920
来创建了这个服务对象

50
00:00:57,920 --> 00:00:59,440
然后在回调函数里面

51
00:00:59,440 --> 00:01:01,480
开始去写我们的服务器逻辑

52
00:01:01,480 --> 00:01:02,520
在响应的投资这里

53
00:01:02,520 --> 00:01:04,040
我试试了TextATML

54
00:01:04,040 --> 00:01:04,980
X4等约UT8

55
00:01:04,980 --> 00:01:06,420
然后把E1L地址

56
00:01:06,420 --> 00:01:08,220
进行了一个对象解析

57
00:01:08,220 --> 00:01:08,700
解析完之后

58
00:01:08,700 --> 00:01:09,880
我通过请求对象

59
00:01:09,880 --> 00:01:11,680
获取到如果你的请求方法

60
00:01:11,680 --> 00:01:12,420
是get请求

61
00:01:12,420 --> 00:01:13,620
好那我就怎么怎么的

62
00:01:13,620 --> 00:01:14,860
如果不是get请求

63
00:01:14,860 --> 00:01:16,000
那么我直接给你返回一个

64
00:01:16,000 --> 00:01:16,580
Hello你好

65
00:01:16,580 --> 00:01:17,680
如果是get

66
00:01:17,680 --> 00:01:19,560
同时你的请求的地址是跟入镜

67
00:01:19,560 --> 00:01:21,920
那么我就需要判断你有没有给我传一个参数

68
00:01:21,920 --> 00:01:22,780
参数的关键字叫什么

69
00:01:22,780 --> 00:01:23,400
叫kw

70
00:01:23,400 --> 00:01:24,780
如果没有传就是安利范的

71
00:01:24,780 --> 00:01:26,300
那么老师祝你早日复刻帝国

72
00:01:26,300 --> 00:01:27,820
如果你传完了kw

73
00:01:27,820 --> 00:01:28,960
那么老师祝你

74
00:01:28,960 --> 00:01:29,900
你写什么

75
00:01:29,900 --> 00:01:30,640
我就祝你什么

76
00:01:30,640 --> 00:01:33,220
那么如果你写的不是跟入镜

77
00:01:33,220 --> 00:01:34,500
那么依然给你返回

78
00:01:34,500 --> 00:01:35,160
hello你好

79
00:01:35,160 --> 00:01:35,700
ok

80
00:01:35,700 --> 00:01:36,940
那么这个代码写完之后呢

81
00:01:36,940 --> 00:01:38,940
来我们把这个代码像刚才一样

82
00:01:38,940 --> 00:01:41,000
把它呢再传到我们的福气上

83
00:01:41,000 --> 00:01:43,900
好

84
00:01:43,900 --> 00:01:46,660
找到我们的代码传到福气

85
00:01:46,660 --> 00:01:47,520
好

86
00:01:47,520 --> 00:01:48,240
我们依然点击确定

87
00:01:48,240 --> 00:01:48,680
OK

88
00:01:48,680 --> 00:01:49,340
那传输完之后

89
00:01:49,340 --> 00:01:49,980
打开我们的命令

90
00:01:49,980 --> 00:01:51,660
来我们把上一次运行的这个服务器

91
00:01:51,660 --> 00:01:52,620
给它Ctrl C停掉

92
00:01:52,620 --> 00:01:53,020
然后呢

93
00:01:53,020 --> 00:01:54,420
我们重新去运行

94
00:01:54,420 --> 00:01:55,860
一ATBS第2js

95
00:01:55,860 --> 00:01:56,880
好运行出来之后

96
00:01:56,880 --> 00:01:58,080
我们还是打开不打优传器

97
00:01:58,080 --> 00:01:58,760
还是去访问啊

98
00:01:58,760 --> 00:01:59,280
我们一刷信

99
00:01:59,280 --> 00:02:00,640
说老师祝你早日复活帝国

100
00:02:00,640 --> 00:02:00,960
行

101
00:02:00,960 --> 00:02:01,800
现在别着急

102
00:02:01,800 --> 00:02:04,640
我真的要给你传一个KW等于

103
00:02:04,640 --> 00:02:05,360
变率

104
00:02:05,360 --> 00:02:08,260
然后翘回车

105
00:02:08,260 --> 00:02:08,500
哎

106
00:02:08,500 --> 00:02:09,680
看这里老师祝你变率

107
00:02:09,680 --> 00:02:10,580
看到了吗

108
00:02:10,580 --> 00:02:12,100
你真的是不是能够接收到

109
00:02:12,100 --> 00:02:13,220
客户团给你传过去的数据了

110
00:02:13,220 --> 00:02:14,480
这个是完全没有问题的

111
00:02:14,480 --> 00:02:15,940
那如果我请问他如果不是斜告

112
00:02:15,940 --> 00:02:17,020
我给他来一个kkk一销一销

113
00:02:17,020 --> 00:02:17,940
那么他说哈喽你好

114
00:02:17,940 --> 00:02:19,460
那么这个我们的代码怎么样

115
00:02:19,460 --> 00:02:21,300
是不是就证明我们的代码起作用了

116
00:02:21,300 --> 00:02:22,620
同时这也就证明了

117
00:02:22,620 --> 00:02:24,440
我们的htps其实呢

118
00:02:24,440 --> 00:02:26,900
就是比htp多了一个证书而已

