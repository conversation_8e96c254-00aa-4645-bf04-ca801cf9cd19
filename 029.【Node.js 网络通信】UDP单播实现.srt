1
00:00:00,000 --> 00:00:03,080
在Node中为我们提供了一个核心模块

2
00:00:03,080 --> 00:00:03,780
叫做Dgram

3
00:00:03,780 --> 00:00:06,360
这个模块就为我们提供了专门用来构建

4
00:00:06,360 --> 00:00:08,100
UDP服务的相关的一些API

5
00:00:08,100 --> 00:00:10,580
那么使用这个模块的方式也非常简单

6
00:00:10,580 --> 00:00:13,040
我们只需要在代码中把这个模块引进来

7
00:00:13,040 --> 00:00:14,280
然后调用这个模块的方法

8
00:00:14,280 --> 00:00:15,920
那么这样的话

9
00:00:15,920 --> 00:00:18,160
我们就得到了一个Socket通信对象

10
00:00:18,160 --> 00:00:19,360
有了这个通信对象以后

11
00:00:19,360 --> 00:00:21,080
然后我们就可以使用这个Socket

12
00:00:21,080 --> 00:00:22,960
所提供的一些方法和事件

13
00:00:22,960 --> 00:00:25,280
来完成我们的UDP网络通信

14
00:00:25,280 --> 00:00:27,240
那么对于这些API

15
00:00:27,240 --> 00:00:29,720
我们接下来就要分别通过三个事例来进行讲解

16
00:00:29,720 --> 00:00:31,500
那么第一个就是UDP单播

17
00:00:31,500 --> 00:00:33,260
第二就是UDP广播

18
00:00:33,260 --> 00:00:34,920
第三就是UDP组播

19
00:00:34,920 --> 00:00:36,860
那么首先我们来看一下

20
00:00:36,860 --> 00:00:38,800
如何使用Node来实现UDP单播

21
00:00:38,800 --> 00:00:39,840
那么这里的话

22
00:00:39,840 --> 00:00:41,040
我们只需要准备一个服务端

23
00:00:41,040 --> 00:00:42,060
然后再准备一个客户端

24
00:00:42,060 --> 00:00:44,180
然后让双方能互相进行单播通信

25
00:00:44,180 --> 00:00:45,300
就可以了

26
00:00:45,300 --> 00:00:47,860
那接下来我们直接回到我们的编辑器当中

27
00:00:47,860 --> 00:00:48,840
在我们编辑器当中

28
00:00:48,840 --> 00:00:50,500
在这里我们就创建了一个目录

29
00:00:50,500 --> 00:00:51,280
叫做UDP单播

30
00:00:51,280 --> 00:00:53,120
在这里我们分别创建了两个文件

31
00:00:53,120 --> 00:00:54,140
一个叫做32.js

32
00:00:54,140 --> 00:00:55,220
一个叫做client.js

33
00:00:55,220 --> 00:00:56,960
我们在32.js当中

34
00:00:56,960 --> 00:00:58,320
我刚才我们所说

35
00:00:58,320 --> 00:01:00,820
就是我们要让双方能互相进行通信

36
00:01:00,820 --> 00:01:03,740
我们只需要在客户端给服务端发一个消息

37
00:01:03,740 --> 00:01:05,260
然后在服务端再回一个消息

38
00:01:05,260 --> 00:01:07,680
我们就实现这样一种非常简单的方式

39
00:01:07,680 --> 00:01:10,600
我们要做的第一点就是先把这个模块来给引进来

40
00:01:10,600 --> 00:01:11,880
就是Dgram这个核心模块

41
00:01:11,880 --> 00:01:13,520
有了这个模块以后

42
00:01:13,520 --> 00:01:15,320
然后接下来我们要调用这个模块的方法

43
00:01:15,320 --> 00:01:16,420
也就是CurtSocket

44
00:01:16,420 --> 00:01:18,780
那么这里的话我们要传一个固定的参数

45
00:01:18,780 --> 00:01:20,020
也就是UDP4

46
00:01:20,020 --> 00:01:22,700
有了它我们在这里呢就来接收一下

47
00:01:22,700 --> 00:01:24,460
我们创定得到的这个Socket的通信对象

48
00:01:24,460 --> 00:01:25,540
我们在这里请用Sore

49
00:01:25,540 --> 00:01:27,400
那么有了它以后

50
00:01:27,400 --> 00:01:30,380
然后接下来我们要分别监听它的三个事件

51
00:01:30,380 --> 00:01:32,980
一个叫做listening

52
00:01:32,980 --> 00:01:36,200
这个listening就表示我们这个服务端

53
00:01:36,200 --> 00:01:37,220
在这里的启动成功了

54
00:01:37,220 --> 00:01:38,960
然后接下来我们分别

55
00:01:38,960 --> 00:01:41,920
我们在这里拿到我们这个服务端

56
00:01:41,920 --> 00:01:43,600
相关的一些就是地址信息

57
00:01:43,600 --> 00:01:45,060
调用它的address方法

58
00:01:45,060 --> 00:01:47,560
然后这个时候我们在这里去简单的输出一下

59
00:01:47,560 --> 00:01:49,220
sorry Johnny

60
00:01:49,220 --> 00:01:50,400
它运行在哪里呢

61
00:01:50,400 --> 00:01:53,740
也表示我们这个服务端运行在这个address

62
00:01:53,740 --> 00:01:55,400
address

63
00:01:55,400 --> 00:01:58,080
他有一个port

64
00:01:58,080 --> 00:01:59,580
也就是说一个是他的地址

65
00:01:59,580 --> 00:02:00,360
一个是他端口话

66
00:02:00,360 --> 00:02:03,520
那么接下来我们再来监听他的一个事件

67
00:02:03,520 --> 00:02:08,960
那么第二个事件就是他的message事件

68
00:02:08,960 --> 00:02:10,860
在message事件当中

69
00:02:10,860 --> 00:02:11,960
我们主要接受两个参数

70
00:02:11,960 --> 00:02:13,000
一个是消息

71
00:02:13,000 --> 00:02:14,560
一个是remote info

72
00:02:14,560 --> 00:02:16,400
也就是我们的远程

73
00:02:16,400 --> 00:02:17,400
远程目标

74
00:02:17,400 --> 00:02:18,820
也就是说这个message是

75
00:02:18,820 --> 00:02:20,980
如果接收到另一端发过来的消息

76
00:02:20,980 --> 00:02:22,080
那么这个事件就会被触发

77
00:02:22,080 --> 00:02:23,360
message就是消息内容

78
00:02:23,360 --> 00:02:24,160
remote info

79
00:02:24,160 --> 00:02:26,000
就是与我通信的另一端的

80
00:02:26,000 --> 00:02:27,280
一些相关的地址信息

81
00:02:27,280 --> 00:02:28,660
那么这里的话

82
00:02:28,660 --> 00:02:30,300
我们在这里就可以输出一下

83
00:02:30,300 --> 00:02:33,080
sort get

84
00:02:33,080 --> 00:02:34,480
说到什么呢

85
00:02:34,480 --> 00:02:35,380
说到这个message

86
00:02:35,380 --> 00:02:37,640
from来自于哪里呢

87
00:02:37,640 --> 00:02:38,700
来自于我们这个就是

88
00:02:38,700 --> 00:02:40,320
remote inferred ss

89
00:02:40,320 --> 00:02:43,080
remote inferred port

90
00:02:43,080 --> 00:02:45,480
这样我们简单的一个message式件

91
00:02:45,480 --> 00:02:46,100
在这里就写好了

92
00:02:46,100 --> 00:02:47,220
那么写好以后

93
00:02:47,220 --> 00:02:48,700
将来我们还有一个事件

94
00:02:48,700 --> 00:02:49,980
叫做error事件

95
00:02:49,980 --> 00:02:51,460
那么在这个error事件当中

96
00:02:51,460 --> 00:02:53,020
我们要来输出一下

97
00:02:53,020 --> 00:02:54,980
如果说这个事件被触发

98
00:02:54,980 --> 00:02:57,760
那么就表示我们这个服务端在这里抱错了

99
00:02:57,760 --> 00:02:58,240
就有问题

100
00:02:58,240 --> 00:02:58,880
有异常

101
00:02:58,880 --> 00:02:59,960
那么这里就是

102
00:02:59,960 --> 00:03:01,700
sir arrow

103
00:03:01,700 --> 00:03:04,140
然后我们把这个 arrow去给它输出一下

104
00:03:04,140 --> 00:03:04,960
好

105
00:03:04,960 --> 00:03:06,780
那么在这个sir的程序的最后

106
00:03:06,780 --> 00:03:08,320
我们这里要给它bind

107
00:03:08,320 --> 00:03:09,560
我们来给它绑定一个端口号

108
00:03:09,560 --> 00:03:11,240
那么这个端口号的注意

109
00:03:11,240 --> 00:03:14,080
我们在这只要就是可以使用不被占用就可以了

110
00:03:14,080 --> 00:03:15,600
例如我这里来个3000

111
00:03:15,600 --> 00:03:17,020
那么这样的话

112
00:03:17,020 --> 00:03:19,460
我这个UDP服务端在这里就充电好了

113
00:03:19,460 --> 00:03:21,060
那么这个服务端的运行

114
00:03:21,060 --> 00:03:21,880
简单来说一下

115
00:03:21,880 --> 00:03:23,680
就是首先我们来创立个socket

116
00:03:23,680 --> 00:03:25,440
然后分别监听它的三个事件

117
00:03:25,440 --> 00:03:27,580
listening就是当绑定端口号

118
00:03:27,580 --> 00:03:28,340
启动成功的时候

119
00:03:28,340 --> 00:03:29,280
它会来出发

120
00:03:29,280 --> 00:03:30,580
然后就是message的事件

121
00:03:30,580 --> 00:03:31,340
message的事件就是

122
00:03:31,340 --> 00:03:32,180
当它收到消息的时候

123
00:03:32,180 --> 00:03:32,980
它会来出发

124
00:03:32,980 --> 00:03:33,880
IRR事件的就是

125
00:03:33,880 --> 00:03:35,280
当这个服务端发生异常的时候

126
00:03:35,280 --> 00:03:36,540
它会来出发

127
00:03:36,540 --> 00:03:37,440
那么最后这个bine

128
00:03:37,440 --> 00:03:38,540
也就是绑定个端口号

129
00:03:38,540 --> 00:03:40,880
来启动这个UDP服务端

130
00:03:40,880 --> 00:03:41,820
那么服务端写好以后

131
00:03:41,820 --> 00:03:43,440
然后接下来我们把这个client

132
00:03:43,440 --> 00:03:45,120
客户端来实现一下

133
00:03:45,120 --> 00:03:46,580
那么客户端和这个服务端是一样的

134
00:03:46,580 --> 00:03:48,780
我们这里快速来把它写出来

135
00:03:48,780 --> 00:03:50,580
首先加在dgram模块

136
00:03:50,580 --> 00:03:51,900
然后掉这个模块了

137
00:03:51,900 --> 00:03:52,860
可是梳击的方法

138
00:03:52,860 --> 00:03:54,260
传入UDP4

139
00:03:54,260 --> 00:03:55,300
同样的

140
00:03:55,300 --> 00:03:56,260
我们来接收一下

141
00:03:56,260 --> 00:03:57,300
请名为client

142
00:03:57,300 --> 00:03:57,980
用了它以后

143
00:03:57,980 --> 00:03:59,300
注意我们带客户

144
00:03:59,300 --> 00:04:01,260
我们可以简单一点

145
00:04:01,260 --> 00:04:01,980
我们可以简单一点

146
00:04:01,980 --> 00:04:03,220
我们可以直接

147
00:04:03,220 --> 00:04:04,060
我们直接

148
00:04:04,060 --> 00:04:06,620
首先监听它的就是listening时间

149
00:04:06,620 --> 00:04:09,140
以及它的message时间

150
00:04:09,140 --> 00:04:11,660
当然也包括它的arror时间

151
00:04:11,660 --> 00:04:12,820
也arror时间

152
00:04:12,820 --> 00:04:13,700
好那么最后这个bind

153
00:04:13,700 --> 00:04:14,540
我们要不要呢

154
00:04:14,540 --> 00:04:15,700
注意bind实际上

155
00:04:15,700 --> 00:04:17,420
并不是不是必须的

156
00:04:17,420 --> 00:04:17,860
也就是说

157
00:04:17,860 --> 00:04:19,200
当你不给他把端口号的时候

158
00:04:19,200 --> 00:04:19,860
那么实际上他会

159
00:04:19,860 --> 00:04:21,420
操作系统会为他分配一个

160
00:04:21,420 --> 00:04:22,920
就是随机的端口号

161
00:04:22,920 --> 00:04:23,960
所以就是这样的

162
00:04:23,960 --> 00:04:24,620
好那么在这里的话

163
00:04:24,620 --> 00:04:27,320
我们要把这个API稍微的来改改

164
00:04:27,320 --> 00:04:29,200
我们为了做一个区分

165
00:04:29,200 --> 00:04:29,960
也就是client

166
00:04:29,960 --> 00:04:31,220
好那么在这里的话

167
00:04:31,220 --> 00:04:32,660
注意我们要干一件事

168
00:04:32,660 --> 00:04:33,900
我们干一件什么事呢

169
00:04:33,900 --> 00:04:36,420
我们让这client去发送一条消息

170
00:04:36,420 --> 00:04:38,860
我们可以直接让他一上来

171
00:04:38,860 --> 00:04:39,900
就发送一条消息

172
00:04:39,900 --> 00:04:41,000
我们让他发一个什么呢

173
00:04:41,000 --> 00:04:41,920
我们可以直接在这去

174
00:04:41,920 --> 00:04:43,760
sign这个什么呢

175
00:04:43,760 --> 00:04:45,860
我们在这里可以让他

176
00:04:45,860 --> 00:04:47,280
例如我发一个hello

177
00:04:47,280 --> 00:04:49,340
然后接下来

178
00:04:49,340 --> 00:04:50,280
我要告诉他

179
00:04:50,280 --> 00:04:50,960
发给哪

180
00:04:50,960 --> 00:04:51,900
另一端的那个端口号

181
00:04:51,900 --> 00:04:53,580
我们serve端口号是3000

182
00:04:53,580 --> 00:04:54,880
而第三个参数

183
00:04:54,880 --> 00:04:55,800
也就是他的地址

184
00:04:55,800 --> 00:04:56,540
那么这里的话

185
00:04:56,540 --> 00:04:57,180
就是我们的本机

186
00:04:57,180 --> 00:04:58,820
我们暂时可以去localhost

187
00:04:58,820 --> 00:05:00,100
那么这样的话

188
00:05:00,100 --> 00:05:01,020
就是我们这个client

189
00:05:01,020 --> 00:05:01,740
在启动的时候

190
00:05:01,740 --> 00:05:03,480
就直接向这个3000端口号

191
00:05:03,480 --> 00:05:04,360
去发了一条消息

192
00:05:04,360 --> 00:05:04,920
发了一个hello

193
00:05:04,920 --> 00:05:06,720
那么这里的话

194
00:05:06,720 --> 00:05:07,580
我刚才说了

195
00:05:07,580 --> 00:05:09,320
我们这个client实际上

196
00:05:09,320 --> 00:05:10,340
或者说这个socket

197
00:05:10,340 --> 00:05:11,100
bind这个端口号

198
00:05:11,100 --> 00:05:12,020
不是必须的

199
00:05:12,020 --> 00:05:12,520
因为我说

200
00:05:12,520 --> 00:05:13,920
当你不把那端口号的时候

201
00:05:13,920 --> 00:05:15,340
会自动为它分配一个

202
00:05:15,340 --> 00:05:16,440
那么

203
00:05:16,440 --> 00:05:18,120
接下来我们就分别

204
00:05:18,120 --> 00:05:19,680
把这个thor和client来启动起来

205
00:05:19,680 --> 00:05:20,980
我们来看一下这个效果

206
00:05:20,980 --> 00:05:22,300
好

207
00:05:22,300 --> 00:05:24,460
所以我们来到我们的命令行当中

208
00:05:24,460 --> 00:05:25,260
我们在这里的话

209
00:05:25,260 --> 00:05:29,280
我们首先来执行我们的thor.js

210
00:05:29,280 --> 00:05:31,680
我们可以看到thor.js在这里

211
00:05:31,680 --> 00:05:32,640
已经运行成功了

212
00:05:32,640 --> 00:05:34,100
绑定监听了3000短口号

213
00:05:34,100 --> 00:05:36,500
然后接下来就是我们的client

214
00:05:36,500 --> 00:05:38,260
我们这里来执行

215
00:05:38,260 --> 00:05:39,920
那么client执行完以后

216
00:05:39,920 --> 00:05:41,500
我们可以看到client运行在

217
00:05:41,500 --> 00:05:42,640
占用一个随机短口

218
00:05:42,640 --> 00:05:43,840
也就是51753

219
00:05:43,840 --> 00:05:44,440
注意

220
00:05:44,440 --> 00:05:46,240
也就是说绑个短口号不是必须的

221
00:05:46,240 --> 00:05:47,640
它的系统会自动给它分配一个断口号

222
00:05:47,640 --> 00:05:49,680
好 那么SERVER我们这里马上就可以看到

223
00:05:49,680 --> 00:05:51,140
SERVER收到了一个消息

224
00:05:51,140 --> 00:05:51,640
就是Hello

225
00:05:51,640 --> 00:05:54,840
来自于我们本机51753这个端

226
00:05:54,840 --> 00:05:57,440
好 那么就证明我们这个Client

227
00:05:57,440 --> 00:05:59,600
已经通过UDP把数据发到了这个SERVER

228
00:05:59,600 --> 00:06:01,540
那么接下来我们让这个SERVER

229
00:06:01,540 --> 00:06:02,940
给这个Client来回一条消息

230
00:06:02,940 --> 00:06:04,780
好 那么给它回消息的话

231
00:06:04,780 --> 00:06:06,340
我们在这里就可以这样来做

232
00:06:06,340 --> 00:06:08,640
好 所以说我们在SERVER的这个Message当中

233
00:06:08,640 --> 00:06:09,640
因为我们可以看到

234
00:06:09,640 --> 00:06:10,940
我们可以通过这个RemoteInfer

235
00:06:10,940 --> 00:06:12,540
来得到客户端的一些信息

236
00:06:12,540 --> 00:06:14,100
来留下的地址和它断口号

237
00:06:14,100 --> 00:06:15,180
所以说这个时候

238
00:06:15,180 --> 00:06:17,420
我们就可以在这里去sir.sind

239
00:06:17,420 --> 00:06:18,420
也就是在这里

240
00:06:18,420 --> 00:06:19,220
不管你sir也好

241
00:06:19,220 --> 00:06:20,140
还是client也罢

242
00:06:20,140 --> 00:06:21,020
他们都是什么呢

243
00:06:21,020 --> 00:06:23,140
他们是不是都是通过dgram.socket

244
00:06:23,140 --> 00:06:24,740
是创建的这个socket通信对象

245
00:06:24,740 --> 00:06:26,620
我们只是给他起了一个不同的名字

246
00:06:26,620 --> 00:06:27,460
来加以区分

247
00:06:27,460 --> 00:06:29,180
但他们的本质是都是socket

248
00:06:29,180 --> 00:06:30,940
所以说这个sir也可以去sind

249
00:06:30,940 --> 00:06:31,940
因为他也是socket

250
00:06:31,940 --> 00:06:33,340
那么接下来我们在这里

251
00:06:33,340 --> 00:06:34,980
我们来给他回应一个word

252
00:06:34,980 --> 00:06:36,820
那么把这个word发给谁呢

253
00:06:36,820 --> 00:06:38,980
我们要发给remoteinfer的port

254
00:06:38,980 --> 00:06:40,300
因为我们不知道

255
00:06:40,300 --> 00:06:41,620
这个客户端端号是什么

256
00:06:41,620 --> 00:06:43,620
但是也就是说我们不知道

257
00:06:43,620 --> 00:06:45,140
假如说你在这把定了一个

258
00:06:45,140 --> 00:06:46,880
假如说你在这儿帮给你一个八千

259
00:06:46,880 --> 00:06:48,100
那么这种情况下

260
00:06:48,100 --> 00:06:49,440
对于sir来说它是不知道的

261
00:06:49,440 --> 00:06:50,860
所以说我们可以通过这个remoteinfer

262
00:06:50,860 --> 00:06:53,280
来得到当前给我发消息的这个端

263
00:06:53,280 --> 00:06:54,700
它的端口和它的地址是什么

264
00:06:54,700 --> 00:06:56,960
然后接下来就是它的一个address

265
00:06:56,960 --> 00:06:58,040
那么这样的话

266
00:06:58,040 --> 00:06:59,620
我们就把这个消息去发给

267
00:06:59,620 --> 00:07:02,120
就是给我发消息的这个端了

268
00:07:02,120 --> 00:07:03,100
那这是client

269
00:07:03,100 --> 00:07:05,000
然后接下来我们改好以后

270
00:07:05,000 --> 00:07:06,780
然后回到我们的命令行当中

271
00:07:06,780 --> 00:07:08,620
那么这个时候我们把两边分别去断开

272
00:07:08,620 --> 00:07:09,880
断开以后

273
00:07:09,880 --> 00:07:11,820
然后这个时候我们再来启动sir

274
00:07:11,820 --> 00:07:14,280
完了我们再来启动client

275
00:07:14,280 --> 00:07:15,860
此时我们就可以看到

276
00:07:15,860 --> 00:07:17,300
Client启动成功以后

277
00:07:17,300 --> 00:07:18,500
向客户端发一条消息

278
00:07:18,500 --> 00:07:20,320
客户端收到来自了客户端的hello

279
00:07:20,320 --> 00:07:22,800
然后服务端回应一条消息

280
00:07:22,800 --> 00:07:24,860
然后Client就收到了这个Word

281
00:07:24,860 --> 00:07:27,020
来自于3000的这个所谓的服务端

282
00:07:27,020 --> 00:07:28,960
所以他们大概就是这样的

283
00:07:28,960 --> 00:07:29,880
那么在这里的话

284
00:07:29,880 --> 00:07:30,880
我们就可以通过这个示例

285
00:07:30,880 --> 00:07:33,640
就了解到这个Socket它本身

286
00:07:33,640 --> 00:07:36,400
就是你创建得到这个Socket对象

287
00:07:36,400 --> 00:07:37,800
它既可以是客户端

288
00:07:37,800 --> 00:07:38,880
还可以是服务端

289
00:07:38,880 --> 00:07:39,800
这个无所谓

290
00:07:39,800 --> 00:07:41,560
关键是我们这两个端

291
00:07:41,560 --> 00:07:43,580
是能进行这个所谓的数据通信

292
00:07:43,580 --> 00:07:44,600
而且在这里呢

293
00:07:44,600 --> 00:07:45,660
也体现了他们的其中一点

294
00:07:45,660 --> 00:07:46,240
就是什么呢

295
00:07:46,240 --> 00:07:47,200
他们在通信的时候

296
00:07:47,200 --> 00:07:49,000
他们不需要建立连接

297
00:07:49,000 --> 00:07:50,240
例如这个client

298
00:07:50,240 --> 00:07:51,260
要给这个sir发消息

299
00:07:51,260 --> 00:07:52,540
你可以直接在这里

300
00:07:52,540 --> 00:07:53,980
发送消息就可以了

301
00:07:53,980 --> 00:07:55,180
当然这里一定要注意的一点

302
00:07:55,180 --> 00:07:57,320
就是我们这个UDT

303
00:07:57,320 --> 00:07:58,620
所创建的这个sirket的通信对象

304
00:07:58,620 --> 00:08:00,440
它这个by的端口号

305
00:08:00,440 --> 00:08:01,500
不是必须的

306
00:08:01,500 --> 00:08:02,380
当然说如果说

307
00:08:02,380 --> 00:08:03,360
你要固定这个端口号的话

308
00:08:03,360 --> 00:08:04,400
那你就需要自己来绑定

309
00:08:04,400 --> 00:08:05,580
如果说不需要固定

310
00:08:05,580 --> 00:08:06,300
那么你就不用信

311
00:08:06,300 --> 00:08:07,640
那么不管这个端口号

312
00:08:07,640 --> 00:08:09,220
你就可以直接在这里去发消息

313
00:08:09,220 --> 00:08:10,500
那如果说假如说

314
00:08:10,500 --> 00:08:11,400
你这个sirket

315
00:08:11,400 --> 00:08:12,860
你固定了端口号

316
00:08:12,860 --> 00:08:14,260
假如说你在这固定一个八千

317
00:08:14,260 --> 00:08:15,700
当然这个应该是Client

318
00:08:15,700 --> 00:08:17,360
那么当你绑这个段号以后

319
00:08:17,360 --> 00:08:18,260
注意你这个时候

320
00:08:18,260 --> 00:08:19,140
你就不能在这里

321
00:08:19,140 --> 00:08:20,180
直接去Sign这个消息了

322
00:08:20,180 --> 00:08:21,640
那么你应该在哪里呢

323
00:08:21,640 --> 00:08:23,920
你应该在这个Socket的Listening

324
00:08:23,920 --> 00:08:25,580
成功以后才能去发送消息

325
00:08:25,580 --> 00:08:27,340
所以说在这里注意一下

326
00:08:27,340 --> 00:08:29,860
就是说如果你没有绑定

327
00:08:29,860 --> 00:08:30,820
手动绑的段号

328
00:08:30,820 --> 00:08:31,820
那么操作系统会给你

329
00:08:31,820 --> 00:08:32,640
分配一个随机的段号

330
00:08:32,640 --> 00:08:33,880
那么这个Sign的方法

331
00:08:33,880 --> 00:08:34,960
你可以直接在这里写

332
00:08:34,960 --> 00:08:35,640
直接去发消息

333
00:08:35,640 --> 00:08:37,720
但是假如说你绑定了段号

334
00:08:37,720 --> 00:08:39,020
那么此时你要发消息

335
00:08:39,020 --> 00:08:40,620
那么你就一定要确保

336
00:08:40,620 --> 00:08:41,900
这个发送消息的代码

337
00:08:41,900 --> 00:08:42,760
要写到哪里呢

338
00:08:42,760 --> 00:08:45,800
要写到这个Socket listening成功以后

339
00:08:45,800 --> 00:08:46,580
要写到这里

340
00:08:46,580 --> 00:08:49,820
因此时才确保你绑定这个手动绑定这个单口号

341
00:08:49,820 --> 00:08:51,680
就是绑定并且就是监听成功了

342
00:08:51,680 --> 00:08:53,440
所以说你在这里务必要这么来做

343
00:08:53,440 --> 00:08:54,360
那么这样的话

344
00:08:54,360 --> 00:08:54,920
你就可以看到

345
00:08:54,920 --> 00:08:56,060
我们就分别设置了

346
00:08:56,060 --> 00:08:57,540
一个监听了3000的Socket

347
00:08:57,540 --> 00:08:59,180
和一个监听了8000的Socket

348
00:08:59,180 --> 00:09:00,760
那么这两端是可以进行通信的

349
00:09:00,760 --> 00:09:01,760
当然在这里的话

350
00:09:01,760 --> 00:09:03,760
他们也没有什么就是Server和Client一说

351
00:09:03,760 --> 00:09:06,140
只不过从你这个角度来看

352
00:09:06,140 --> 00:09:07,780
从你这个就是通信角度来看

353
00:09:07,780 --> 00:09:09,480
两端的其实是等价的

354
00:09:09,480 --> 00:09:10,460
你既可以是狗短

355
00:09:10,460 --> 00:09:11,340
还可以是服短

356
00:09:11,340 --> 00:09:13,800
因为双方是互相通信的一种方式

357
00:09:13,800 --> 00:09:15,900
当然这样你简单改了这个段号以后

358
00:09:15,900 --> 00:09:17,560
然后这个时候你再回到你的命令和当中

359
00:09:17,560 --> 00:09:19,100
然后这个时候你再来看一下

360
00:09:19,100 --> 00:09:20,120
我们再来启动这个sir

361
00:09:20,120 --> 00:09:22,480
我们再来启动这个client

362
00:09:22,480 --> 00:09:23,720
然后此时你就可以看到

363
00:09:23,720 --> 00:09:25,420
我们科目端是不是就占用这个8000

364
00:09:25,420 --> 00:09:27,260
或者说我们这个UDP这个通信端

365
00:09:27,260 --> 00:09:29,060
然后对方发了一条消息

366
00:09:29,060 --> 00:09:30,700
那么对方就收到了这个来自8000的消息

367
00:09:30,700 --> 00:09:31,600
然后回了一个world

368
00:09:31,600 --> 00:09:33,820
那这个world就来自于这个3000的这个段号的消息

369
00:09:33,820 --> 00:09:34,840
所以说

370
00:09:34,840 --> 00:09:41,080
这个就是我们UDP它的单拨的一种简单的实现

