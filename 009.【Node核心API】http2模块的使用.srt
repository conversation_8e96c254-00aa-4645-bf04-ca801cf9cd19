1
00:00:00,000 --> 00:00:04,380
刚才我们学习了如何通过http模块去创建一个我们的http的服务

2
00:00:04,380 --> 00:00:05,120
大家也很熟悉

3
00:00:05,120 --> 00:00:06,500
那么接下来这样的一个模块

4
00:00:06,500 --> 00:00:08,260
可能大家就稍微有点陌生了

5
00:00:08,260 --> 00:00:09,640
平时可能很少有同学接触过

6
00:00:09,640 --> 00:00:12,320
那么它其实就是我们的http2

7
00:00:12,320 --> 00:00:16,660
那么http2对应的就是我们的http2.0的版本

8
00:00:16,660 --> 00:00:18,120
我们来看一下它的介绍

9
00:00:18,120 --> 00:00:22,200
htt2模块提供了我们http2.0协议的一个实现

10
00:00:22,200 --> 00:00:26,780
那么使用方法也是把我们的http2在那个模块给引入

11
00:00:26,780 --> 00:00:29,980
那么引入之后就可以去创建我们的一个service了

12
00:00:29,980 --> 00:00:36,980
好 那么这里的话呢 我呢就使用我们的http2.0这样一个模块 我们来创建一个http服务

13
00:00:36,980 --> 00:00:42,980
好 那么这里的代码呢 我已经给同学们来 已经准备好了

14
00:00:42,980 --> 00:00:47,980
来看一下这一段代码 首先呢 我们引入了http2.0的一个模块

15
00:00:47,980 --> 00:00:50,980
那么它呢就是支持我们http2.0的http2.0

16
00:00:50,980 --> 00:00:56,980
因为呢在我们学习loads早期的时候 我们是不是一直经常 可能有同学会想到一个问题

17
00:00:56,980 --> 00:00:57,820
这个问题

18
00:00:57,820 --> 00:00:58,820
咱们HTTP

19
00:00:58,820 --> 00:01:00,980
肉的监视创建一个HTTP服务器之后

20
00:01:00,980 --> 00:01:03,400
我们给咱们浏览器提供的协议版本是多少

21
00:01:03,400 --> 00:01:08,000
那么我们目前市面上主流的HTTP协议

22
00:01:08,000 --> 00:01:08,960
一般要么是1.1

23
00:01:08,960 --> 00:01:09,780
要么就是2.0

24
00:01:09,780 --> 00:01:11,120
那么其实大多数的

25
00:01:11,120 --> 00:01:12,320
占点都是1.1的协议

26
00:01:12,320 --> 00:01:14,020
那么我们刚才

27
00:01:14,020 --> 00:01:15,380
占了一个localhost 3000

28
00:01:15,380 --> 00:01:16,640
也就是使用HTTP模块

29
00:01:16,640 --> 00:01:17,660
创建了HTTP的服务器

30
00:01:17,660 --> 00:01:18,460
那么它的协议是什么

31
00:01:18,460 --> 00:01:19,940
其实它的协议是1.1

32
00:01:19,940 --> 00:01:21,080
那么我们的协议版本

33
00:01:21,080 --> 00:01:22,980
其实取决于我们的服务端

34
00:01:22,980 --> 00:01:24,580
它所提供的HTTP的一个模块

35
00:01:24,580 --> 00:01:28,140
是什么 我们刚才的http.js require的是http

36
00:01:28,140 --> 00:01:30,940
所以它提供的是1.1版本的一个协议

37
00:01:30,940 --> 00:01:37,000
那么我们http2模块就会去给我们提供一个2.0的版本的一个协议

38
00:01:37,000 --> 00:01:40,180
那么2.0和1.1有什么区别呢 咱们后面的课程会去介绍

39
00:01:40,180 --> 00:01:42,940
我们先来看一下如何去创建一个我们2.0的一个服务

40
00:01:42,940 --> 00:01:46,580
这里引入FS模块 大家注意这样一行代码

41
00:01:46,580 --> 00:01:51,820
我们抗设一个sever 等于什么呢http2.createsecuresever

42
00:01:51,820 --> 00:01:53,840
创建了一个加密的服务

43
00:01:53,840 --> 00:01:55,340
什么意思呢

44
00:01:55,340 --> 00:01:56,560
我这里谈到加密

45
00:01:56,560 --> 00:01:59,340
第一反应就想到HTPS

46
00:01:59,340 --> 00:02:03,640
它就是使用咱们的一个SS或者TLS去加密的一个协议

47
00:02:03,640 --> 00:02:06,440
那么我们的HTTP2为什么需要去加密呢

48
00:02:06,440 --> 00:02:10,040
其实这里也是HTTP2模块所强制的

49
00:02:10,040 --> 00:02:11,380
什么意思呢

50
00:02:11,380 --> 00:02:13,800
HTTP2.0的协议

51
00:02:13,800 --> 00:02:16,120
它一般来讲的话是只支持HTTP2.0

52
00:02:16,120 --> 00:02:19,480
所以这里希望同学们去注意

53
00:02:19,480 --> 00:02:22,480
虽然说http2.0和https没有什么关系

54
00:02:22,480 --> 00:02:30,480
但是http2.0一般情况下只支持https

55
00:02:30,480 --> 00:02:36,480
但是也能支持http协议

56
00:02:36,480 --> 00:02:38,480
好那么这句话可能有点闹

57
00:02:38,480 --> 00:02:40,480
我来稍微给同学们来解释一下

58
00:02:40,480 --> 00:02:45,480
首先呢https它是不是基于我们的http了呀

59
00:02:45,480 --> 00:02:46,420
那么S是什么

60
00:02:46,420 --> 00:02:49,580
S它只是指我们的SSL

61
00:02:49,580 --> 00:02:50,820
所以我们这里可以得到一个公式

62
00:02:50,820 --> 00:02:55,320
HTTP加上SSL等于HTTP

63
00:02:55,320 --> 00:02:58,760
所以我们的HTTP2.0

64
00:02:58,760 --> 00:03:00,320
其实是属于HTTP这样一个范畴的

65
00:03:00,320 --> 00:03:01,780
它相当于是HTTP的一个升级

66
00:03:01,780 --> 00:03:05,240
那么HTTP2.0其实和SSL没有什么关系

67
00:03:05,240 --> 00:03:06,780
但是一般来讲

68
00:03:06,780 --> 00:03:08,440
如果说你的HTTP升级到了2.0

69
00:03:08,440 --> 00:03:09,820
你就一定要加上SSL

70
00:03:09,820 --> 00:03:11,780
不然的话没有什么意义

71
00:03:11,780 --> 00:03:12,900
如果说你非要

72
00:03:12,900 --> 00:03:15,240
非要使用HTTP的协议

73
00:03:15,240 --> 00:03:16,400
然后把他升级2.0

74
00:03:16,400 --> 00:03:19,140
没有去使用SSL加密

75
00:03:19,140 --> 00:03:20,080
当然也是可以的

76
00:03:20,080 --> 00:03:22,080
但是一般情况下

77
00:03:22,080 --> 00:03:25,340
我们都是需要去使用SGDPS和SGDP2.0

78
00:03:25,340 --> 00:03:27,140
他们是结合起来去使用的

79
00:03:27,140 --> 00:03:29,880
既然我们需要给协议加密

80
00:03:29,880 --> 00:03:31,640
所以我们就需要提供一个公钥和私钥

81
00:03:31,640 --> 00:03:32,680
那么公钥和私钥的概念

82
00:03:32,680 --> 00:03:34,200
咱们后面也会去介绍

83
00:03:34,200 --> 00:03:35,240
公钥

84
00:03:35,240 --> 00:03:38,120
私钥

85
00:03:38,120 --> 00:03:39,420
好

86
00:03:39,420 --> 00:03:41,340
那么这里就是我们提供的公钥和私钥

87
00:03:41,340 --> 00:03:42,100
那么公钥和私钥

88
00:03:42,100 --> 00:03:42,580
一般来讲

89
00:03:42,580 --> 00:03:43,900
你需要去第三方的一个

90
00:03:43,900 --> 00:03:47,040
证书机构去认证 但是我这里就直接使用我们本地生成的

91
00:03:47,040 --> 00:03:49,900
我们本地是如何去生成咱们的公钥和试药呢

92
00:03:49,900 --> 00:03:53,900
那么它的生成方式 我们直接把它给放到了我们的文档中

93
00:03:53,900 --> 00:03:55,900
如果说同学们是使用麦克的话

94
00:03:55,900 --> 00:03:58,900
直接执行在一行命令就可以去生成我们的一个公钥和试药

95
00:03:58,900 --> 00:04:00,900
所以我就不去给同学们去演示了 因为我这里已经安装好了

96
00:04:00,900 --> 00:04:03,900
那么如果说是Windows系统的同学们可能自己需要去百度搜索一下

97
00:04:03,900 --> 00:04:05,900
其实安装本地试书非常的简单

98
00:04:05,900 --> 00:04:07,900
我就不对试书这块做过多的介绍

99
00:04:07,900 --> 00:04:10,900
大家可以看到了我们的本地 我的本地已经安装了

100
00:04:10,900 --> 00:04:12,600
在那两个公钥和私钥

101
00:04:12,600 --> 00:04:16,720
好 当我们配置完公钥和私钥以及它启动服务之后

102
00:04:16,720 --> 00:04:20,380
那么这里呢 我们的服务是不是依然去listen

103
00:04:20,380 --> 00:04:22,340
也就是监听一个我们8443这样的一个端口

104
00:04:22,340 --> 00:04:24,000
但是我们如何响应我们的请求呢

105
00:04:24,000 --> 00:04:25,720
那么这里呢就稍微有一点不一样了

106
00:04:25,720 --> 00:04:28,600
因为我们的http2.0呢 它呢 速度会非常的快

107
00:04:28,600 --> 00:04:31,520
而且呢也支持我们的数据的一个分段传输

108
00:04:31,520 --> 00:04:33,460
所以说呢 我们的数据呢可以

109
00:04:33,460 --> 00:04:37,940
它相当于是咱们的Several也是继承于我们的一个event这样一个对象

110
00:04:37,940 --> 00:04:39,340
然后呢 它监听Dream这样一个事件

111
00:04:39,340 --> 00:04:40,780
当stream出发的时候呢

112
00:04:40,780 --> 00:04:42,340
它就可以给我们去Respond

113
00:04:42,340 --> 00:04:43,640
给我们返回一个

114
00:04:43,640 --> 00:04:46,440
咱们的Respond式的header

115
00:04:46,440 --> 00:04:46,880
比如说呢

116
00:04:46,880 --> 00:04:48,660
我们给它定义为我们的HTML

117
00:04:48,660 --> 00:04:49,440
Status的200

118
00:04:49,440 --> 00:04:50,580
然后在stream的end里面

119
00:04:50,580 --> 00:04:51,580
也就是在我们的流的最后

120
00:04:51,580 --> 00:04:52,440
给了它一个

121
00:04:52,440 --> 00:04:53,720
好了word这样一个字幕创

122
00:04:53,720 --> 00:04:54,280
HT标签

123
00:04:54,280 --> 00:04:54,620
好

124
00:04:54,620 --> 00:04:55,480
那么我们就要测试一下

125
00:04:55,480 --> 00:04:56,660
我们的8413这样一个端口

126
00:04:56,660 --> 00:04:58,600
好

127
00:04:58,600 --> 00:04:58,880
首先呢

128
00:04:58,880 --> 00:04:59,440
我们来启动一下

129
00:04:59,440 --> 00:05:00,360
我们的这样一个服务

130
00:05:00,360 --> 00:05:01,540
我们来执行一下load

131
00:05:01,540 --> 00:05:04,000
http2.js

132
00:05:04,000 --> 00:05:04,900
好

133
00:05:04,900 --> 00:05:05,740
我们来一起访问一下

134
00:05:05,740 --> 00:05:06,620
8413这样一个端口

135
00:05:09,340 --> 00:05:18,940
好 大家可以看到 我们此时的页面中的已经出现了一个Hello World

136
00:05:18,940 --> 00:05:21,540
其实呢说明我们的H1已经对吧 已经返回成功了

137
00:05:21,540 --> 00:05:23,540
那么我们来看一下它的网络请求是什么样的

138
00:05:23,540 --> 00:05:32,580
好 大家可以看一下 我们的Request URL有什么特点

139
00:05:32,580 --> 00:05:35,640
它里面是不是有个HTPS localhost8413

140
00:05:35,640 --> 00:05:38,540
是不是说明我们的HTPS协议已经生效了呀 对吧

141
00:05:38,540 --> 00:05:43,140
所以说为什么说我们刚才提到了你使用HTTP200就一定要使用HTTPs

142
00:05:43,140 --> 00:05:47,000
而且大家可以看到我们的代码中也没有提到任何跟HTTPs相关的模块

143
00:05:47,000 --> 00:05:47,820
原因是什么呢

144
00:05:47,820 --> 00:05:50,780
因为HTTP200它默认就是支持HTTPs了

145
00:05:50,780 --> 00:05:52,340
那么如果说你不做一些其他的配置呢

146
00:05:52,340 --> 00:05:54,100
你只要启动服务都是HTTPs了

147
00:05:54,100 --> 00:05:57,180
那么这里为什么浏览器会出现一个不安全呢

148
00:05:57,180 --> 00:05:57,940
对吧

149
00:05:57,940 --> 00:05:58,920
原因是什么呢

150
00:05:58,920 --> 00:06:00,500
因为在我们的谷歌浏览器内部

151
00:06:00,500 --> 00:06:02,700
它会有一些它所信任的一些远程证书

152
00:06:02,700 --> 00:06:05,780
那么在那些证书都需要到一些第三方的机构去进行一个认证

153
00:06:05,780 --> 00:06:06,920
因为我们是本地证书

154
00:06:06,920 --> 00:06:08,680
我们的本地证书肯定没有经过一个认证

155
00:06:08,680 --> 00:06:11,180
所以说这里的浏览器里面就出现了一个不安全的标志

156
00:06:11,180 --> 00:06:14,180
所以如果说同学们去访问一些网站的时候

157
00:06:14,180 --> 00:06:16,540
如果说它的左上角出现了一个不安全的按钮

158
00:06:16,540 --> 00:06:19,640
说明了这个网站的证书可能会有一些问题

159
00:06:19,640 --> 00:06:21,400
或者说你遇到一些中间人这样的一些攻击

160
00:06:21,400 --> 00:06:24,780
这里此时就需要引起大家的一个注意了

161
00:06:24,780 --> 00:06:29,720
那么这里就是我们https去创建服务的一个过程

162
00:06:29,720 --> 00:06:32,260
我们来简单的回顾一下我们这节课的一个内容

163
00:06:32,260 --> 00:06:36,460
我们这节课主要就是去介绍了我们http2.0这样的一个模块

164
00:06:36,460 --> 00:06:40,000
那么我们HTTP2其实它和我们的HTTP是匹配的

165
00:06:40,000 --> 00:06:41,780
因为它相对就是我们HTTP的一个升级版

166
00:06:41,780 --> 00:06:45,120
但是当你去创建一个HTTP2.0的一个服务的时候

167
00:06:45,120 --> 00:06:48,240
你是不是需要去提供咱们的公钥和私钥

168
00:06:48,240 --> 00:06:49,320
当你提供公钥和私钥之后

169
00:06:49,320 --> 00:06:51,580
它默认是不是就帮你开启了HTTPs的协议

170
00:06:51,580 --> 00:06:53,500
帮你对你的通行过程进行的加密

171
00:06:53,500 --> 00:06:56,080
所以说当你去访问咱们的巴士市场端口的时候

172
00:06:56,080 --> 00:06:57,680
它默认就是HTTPs

173
00:06:57,680 --> 00:07:00,060
那么这里就是我们这节课的内容

