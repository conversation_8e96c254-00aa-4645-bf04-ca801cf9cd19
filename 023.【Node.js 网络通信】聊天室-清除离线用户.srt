1
00:00:00,000 --> 00:00:05,520
我们实现了基本的登录群聊和私聊这三个功能之后

2
00:00:05,520 --> 00:00:07,640
然后接下来我们对这个小案例呢

3
00:00:07,640 --> 00:00:08,620
做一个简单的优化

4
00:00:08,620 --> 00:00:11,060
我们现在呢在这呢遇到一个小问题

5
00:00:11,060 --> 00:00:11,760
就是说

6
00:00:11,760 --> 00:00:14,440
例如我在这里输入nodeclant

7
00:00:14,440 --> 00:00:15,780
例如我在这登录一下

8
00:00:15,780 --> 00:00:16,980
它的名字叫做Jack

9
00:00:16,980 --> 00:00:18,480
当天登录用户是1

10
00:00:18,480 --> 00:00:20,240
然后接下来我们让这个Jack呢

11
00:00:20,240 --> 00:00:21,700
强行退出给这个客户端

12
00:00:21,700 --> 00:00:22,920
我们在这里Ctrl C

13
00:00:22,920 --> 00:00:24,060
然后就可以退出

14
00:00:24,060 --> 00:00:25,560
退出以后呢我们再进来

15
00:00:25,560 --> 00:00:26,860
我们再来输入一个Mac

16
00:00:26,860 --> 00:00:27,800
退车

17
00:00:27,800 --> 00:00:29,360
我们发现当天登录用户是2

18
00:00:29,360 --> 00:00:31,180
那么实际上我们发现

19
00:00:31,180 --> 00:00:32,920
就是说当我们退出以后

20
00:00:32,920 --> 00:00:34,060
然后服务端并不知道

21
00:00:34,060 --> 00:00:35,680
这个客户端已经退出了

22
00:00:35,680 --> 00:00:36,760
然后也没有呢

23
00:00:36,760 --> 00:00:37,880
就是说把那个客户端

24
00:00:37,880 --> 00:00:40,020
从我们服务端当中的

25
00:00:40,020 --> 00:00:41,840
这个users里面去给它移除掉

26
00:00:41,840 --> 00:00:43,640
所以说你现在看到

27
00:00:43,640 --> 00:00:44,260
这个用户就是2

28
00:00:44,260 --> 00:00:45,920
然后你会发现

29
00:00:45,920 --> 00:00:47,560
我们如果说你再来注册这个架客

30
00:00:47,560 --> 00:00:48,300
你注册不上

31
00:00:48,300 --> 00:00:50,120
因为它架客还在里面

32
00:00:50,120 --> 00:00:51,280
只不过就是说实际上

33
00:00:51,280 --> 00:00:52,180
这个架客这个用户呢

34
00:00:52,180 --> 00:00:54,020
早就就是说离开了已经

35
00:00:54,020 --> 00:00:55,220
所以说接下来

36
00:00:55,220 --> 00:00:56,460
我们要把这个功能来给他

37
00:00:56,460 --> 00:00:57,400
或者说把这个问题

38
00:00:57,400 --> 00:00:58,180
来给他处理一下

39
00:00:58,180 --> 00:01:00,280
就是说当用户离开

40
00:01:00,280 --> 00:01:01,840
就是这个客户端之后

41
00:01:01,840 --> 00:01:02,980
那我们在服务端

42
00:01:02,980 --> 00:01:04,780
要把他这个用户的这个

43
00:01:04,780 --> 00:01:06,040
就是通信端

44
00:01:06,040 --> 00:01:07,900
从这个user里面就把它移除掉

45
00:01:07,900 --> 00:01:09,260
好那这里我怎么知道

46
00:01:09,260 --> 00:01:10,260
用户什么时候离开呢

47
00:01:10,260 --> 00:01:12,020
所以说我们可以在客户端这里

48
00:01:12,020 --> 00:01:14,200
来监听他的一个事件

49
00:01:14,200 --> 00:01:15,200
叫做end

50
00:01:15,200 --> 00:01:16,900
就是说当用户离开的时候

51
00:01:16,900 --> 00:01:19,220
那么会触发这个end的事件

52
00:01:19,220 --> 00:01:19,420
好

53
00:01:19,420 --> 00:01:22,420
那么在他里面

54
00:01:22,420 --> 00:01:23,720
我们这个时候可以来测试一下

55
00:01:23,720 --> 00:01:26,120
有用户离开了

56
00:01:26,120 --> 00:01:27,900
好那这个时候我们来看一下

57
00:01:27,900 --> 00:01:30,340
我们在这里先进来

58
00:01:30,340 --> 00:01:31,860
然后直接Ctrl C打断

59
00:01:31,860 --> 00:01:33,620
我们是不是可以看到这里就输出了

60
00:01:33,620 --> 00:01:34,380
用户离开了

61
00:01:34,380 --> 00:01:35,980
那这样的话

62
00:01:35,980 --> 00:01:38,120
踢下来既然有用户离开了

63
00:01:38,120 --> 00:01:39,720
那接下来这个就非常好处理了

64
00:01:39,720 --> 00:01:41,040
我们家只需要就是说

65
00:01:41,040 --> 00:01:44,480
找到这个clientFocket在users当中的这个所影像

66
00:01:44,480 --> 00:01:47,460
然后把它从users里面的去移除掉就可以了

67
00:01:47,460 --> 00:01:50,740
所以说这个时候我们家就可以去user

68
00:01:50,740 --> 00:01:53,360
我们找到这个index

69
00:01:53,360 --> 00:01:55,420
users.findindex

70
00:01:55,420 --> 00:01:57,140
那这个就是

71
00:01:57,140 --> 00:02:04,180
User的NicName等于ClientSocket的NicName

72
00:02:04,180 --> 00:02:04,880
我们找到它

73
00:02:04,880 --> 00:02:05,640
好了

74
00:02:05,640 --> 00:02:06,840
找到它以后

75
00:02:06,840 --> 00:02:09,320
然后这个时候我们让这个Users点

76
00:02:09,320 --> 00:02:12,620
就是如果Index不等于-1

77
00:02:12,620 --> 00:02:14,760
就是说我们找到这个用户了

78
00:02:14,760 --> 00:02:16,740
那么我们在这就让Users点

79
00:02:16,740 --> 00:02:19,640
Users点Supplies to index 1

80
00:02:19,640 --> 00:02:21,580
就是说从这个Index所引开始

81
00:02:21,580 --> 00:02:22,640
往后删多少呢

82
00:02:22,640 --> 00:02:23,100
删一个

83
00:02:23,100 --> 00:02:25,080
这样的话就把这个用户

84
00:02:25,080 --> 00:02:27,360
从users里面去给它移除掉了

85
00:02:27,360 --> 00:02:28,600
好了

86
00:02:28,600 --> 00:02:29,840
移除掉以后

87
00:02:29,840 --> 00:02:31,600
然后接下来我们再回到

88
00:02:31,600 --> 00:02:33,600
这个控制条大中

89
00:02:33,600 --> 00:02:35,260
这个时候我们就可以来测试一下

90
00:02:35,260 --> 00:02:36,540
我们来测试一下

91
00:02:36,540 --> 00:02:37,720
好了

92
00:02:37,720 --> 00:02:38,880
我们再来nodeClient

93
00:02:38,880 --> 00:02:40,100
然后我们再来输入

94
00:02:40,100 --> 00:02:41,240
当然我们输入手都无所谓

95
00:02:41,240 --> 00:02:42,360
我们再来直接

96
00:02:42,360 --> 00:02:43,720
我们输入一个Jack

97
00:02:43,720 --> 00:02:44,760
我们Ctrl-C

98
00:02:44,760 --> 00:02:45,420
好了

99
00:02:45,420 --> 00:02:46,500
那到底有没有移除掉呢

100
00:02:46,500 --> 00:02:47,700
我们再来登录

101
00:02:47,700 --> 00:02:48,540
我们再输入Jack

102
00:02:48,540 --> 00:02:49,320
我们看到

103
00:02:49,320 --> 00:02:50,540
是又登录成功列

104
00:02:50,540 --> 00:02:51,740
所以就是这样的

105
00:02:51,740 --> 00:02:53,080
好

106
00:02:53,080 --> 00:02:54,740
那就证明我们这个小问题

107
00:02:54,740 --> 00:02:55,620
就给他处理好了啊

108
00:02:55,620 --> 00:02:56,320
我们在这儿离开

109
00:02:56,320 --> 00:02:57,480
那离开以后

110
00:02:57,480 --> 00:02:58,280
那Jack按理说

111
00:02:58,280 --> 00:02:59,140
就又应该可以

112
00:02:59,140 --> 00:03:00,060
再来注册这个Jack了

113
00:03:00,060 --> 00:03:00,760
我们再来看一下

114
00:03:00,760 --> 00:03:01,180
是不是Jack

115
00:03:01,180 --> 00:03:02,940
是不是当年大家的用户是1啊

116
00:03:02,940 --> 00:03:04,300
所以就是这样的啊

117
00:03:04,300 --> 00:03:05,300
那这个小问题呢

118
00:03:05,300 --> 00:03:06,860
我们家里就给他处理完了啊

119
00:03:06,860 --> 00:03:07,160
处理完了

