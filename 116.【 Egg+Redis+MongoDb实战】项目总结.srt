1
00:00:00,000 --> 00:00:03,460
这一节可能我们简单的对咱们整个项目的来做一个总结和回顾

2
00:00:03,460 --> 00:00:06,140
首先我们来看一下我们首先学习这个项目一个初始的学习目标

3
00:00:06,140 --> 00:00:07,740
我们需要了解Cookie和Session的关系

4
00:00:07,740 --> 00:00:08,820
以及全面了解登录流程

5
00:00:08,820 --> 00:00:10,620
包括了Reddit在我们登录流程中的一个作用

6
00:00:10,620 --> 00:00:12,060
首先Cookie和Session的关系

7
00:00:12,060 --> 00:00:15,060
那么在完成项目之后的相信同学们都会有一个简单的了解了

8
00:00:15,060 --> 00:00:19,320
那么Cookie比如说它是咱们客户端和服务端去通信的一个条量

9
00:00:19,320 --> 00:00:19,700
对吧

10
00:00:19,700 --> 00:00:20,600
那么Session是什么呢

11
00:00:20,600 --> 00:00:23,760
Session其实它是为了补充Cookie它的一个大小限制

12
00:00:23,760 --> 00:00:25,260
比如说Cookie大小限制只有4K

13
00:00:25,260 --> 00:00:26,960
但是Session我们可以

14
00:00:30,000 --> 00:00:32,660
好那么这里呢我们来对整个项目的来做一个简单的总结

15
00:00:32,660 --> 00:00:35,060
首先呢我们项目开始之前咱们定的学习目标是什么呀

16
00:00:35,060 --> 00:00:36,760
了解CNC全面了解登录流程

17
00:00:36,760 --> 00:00:38,160
以及呢了解登录

18
00:00:38,160 --> 00:00:40,440
了解Redis在登录流程中的一个作用

19
00:00:40,440 --> 00:00:41,660
那么相信大家学完之后

20
00:00:41,660 --> 00:00:43,820
CNC的一个关系大家应该有一个错误的了解

21
00:00:43,820 --> 00:00:46,100
那么CNC呢也就是为了解决HTTP

22
00:00:46,100 --> 00:00:47,860
无状态的一个缺点

23
00:00:47,860 --> 00:00:49,680
你比如说扣端和扶端是不是通过CNC

24
00:00:49,680 --> 00:00:51,100
可以进行一些交流保存一些信息

25
00:00:51,100 --> 00:00:52,400
那么CNC是做什么用的呢

26
00:00:52,400 --> 00:00:54,260
CNC就是为了补充CNC的一个缺点

27
00:00:54,260 --> 00:00:55,500
Cookie的缺点是什么呢

28
00:00:55,500 --> 00:00:56,880
它的数据存储量非常的小

29
00:00:56,880 --> 00:00:57,320
只有4K

30
00:00:57,320 --> 00:00:58,320
那么通过我们的Session

31
00:00:58,320 --> 00:00:59,680
是不是可以把我们的数据存储到

32
00:00:59,680 --> 00:01:00,520
咱们的一个服务端

33
00:01:00,520 --> 00:01:02,140
但是大家需要注意的是什么呢

34
00:01:02,140 --> 00:01:02,980
没有Cookie就没有Session

35
00:01:02,980 --> 00:01:04,900
其实Session它是基于Cookie去实现的

36
00:01:04,900 --> 00:01:07,900
那么我们是不是也全面的了解了一个登录流程

37
00:01:07,900 --> 00:01:09,680
那么我们就来看一下我们的登录流程是怎么样的

38
00:01:09,680 --> 00:01:13,780
这里我们还是拿咱们之前画的这样一张图来看

39
00:01:13,780 --> 00:01:15,200
首先我们登录转发三大块

40
00:01:15,200 --> 00:01:16,340
第一个就是咱们的一个注册

41
00:01:16,340 --> 00:01:17,100
注册完成之后登录

42
00:01:17,100 --> 00:01:19,000
登录之后咱们是不是需要去保持我们的登录态

43
00:01:19,000 --> 00:01:21,040
好我们的信用户首先第一次访问页面

44
00:01:21,040 --> 00:01:22,060
然后需要进行注册

45
00:01:22,060 --> 00:01:22,860
注册完成之后呢

46
00:01:22,860 --> 00:01:24,400
将我们的数据存储到一个mongodb里面

47
00:01:24,400 --> 00:01:25,200
也就是数据库

48
00:01:25,200 --> 00:01:26,760
当然用mysoco也是可以的

49
00:01:26,760 --> 00:01:28,100
好我们存储完成之后

50
00:01:28,100 --> 00:01:30,080
好用户呢

51
00:01:30,080 --> 00:01:31,060
接下来呢要进行登录了

52
00:01:31,060 --> 00:01:31,860
登录了

53
00:01:31,860 --> 00:01:33,760
币了是不是要输入用户名币吗

54
00:01:33,760 --> 00:01:34,840
好输入之后进行交易

55
00:01:34,840 --> 00:01:35,320
交易完成

56
00:01:35,320 --> 00:01:36,360
生成一个token

57
00:01:36,360 --> 00:01:37,880
token呢其实就代表我们的一个用户信息

58
00:01:37,880 --> 00:01:40,040
也是token其实就是意味着你是谁

59
00:01:40,040 --> 00:01:40,340
对吧

60
00:01:40,340 --> 00:01:42,980
好那么我们通过这样一个token呢

61
00:01:42,980 --> 00:01:44,340
和咱们的一个扣端进行交互

62
00:01:44,340 --> 00:01:48,180
然后基于在一个token将一些信息存储到我们的radis服务器

63
00:01:48,180 --> 00:01:49,960
比如说我们在一个项目中存储的是什么呀

64
00:01:49,960 --> 00:01:52,260
通过token存储的是什么呢

65
00:01:52,260 --> 00:01:53,340
我们的登录信息对吧

66
00:01:53,340 --> 00:01:54,000
login为2

67
00:01:54,000 --> 00:01:56,560
包括了还有一些xpire一些过期时间

68
00:01:56,560 --> 00:01:57,780
好这里呢就是登录的过程

69
00:01:57,780 --> 00:01:59,720
包括了我们还会有一个保持登录的过程

70
00:01:59,720 --> 00:02:00,520
也就是第三大块

71
00:02:00,520 --> 00:02:02,700
好那我们当我们当当我们再次访问页面的时候

72
00:02:02,700 --> 00:02:04,420
我们呢是不是会携带一个cookie

73
00:02:04,420 --> 00:02:05,900
那么cookie呢就是我们啊

74
00:02:05,900 --> 00:02:07,880
登录过程中所生成的那个token

75
00:02:07,880 --> 00:02:09,960
我们把这个token给携带到服务端

76
00:02:09,960 --> 00:02:11,160
那么服务端拿了token之后

77
00:02:11,160 --> 00:02:13,160
是不是需要去Redis数据库里面去查询

78
00:02:13,160 --> 00:02:14,080
占一个token对象

79
00:02:14,080 --> 00:02:15,000
它的login值是不是为2

80
00:02:15,000 --> 00:02:15,960
那么如果说为2

81
00:02:15,960 --> 00:02:17,980
说明你已经登录了

82
00:02:17,980 --> 00:02:18,600
可以进入页面

83
00:02:18,600 --> 00:02:19,560
而且可以正常的访问

84
00:02:19,560 --> 00:02:20,840
那么这里其实就是咱们

85
00:02:20,840 --> 00:02:21,820
这一个项目中的一个登录流程

86
00:02:21,820 --> 00:02:23,220
最后我们来看一下

87
00:02:23,220 --> 00:02:25,540
Redis在整个登录流程中的一个作用

88
00:02:25,540 --> 00:02:27,400
我们为什么要选Redis去存储数据

89
00:02:27,400 --> 00:02:28,400
因为咱们刚才讲到了

90
00:02:28,400 --> 00:02:29,980
Session其实是在服务团去存储一些东西

91
00:02:29,980 --> 00:02:31,360
那么有的同学可能会有一个疑问

92
00:02:31,360 --> 00:02:32,500
我为什么不用Monger存

93
00:02:32,500 --> 00:02:33,280
或者不用MySQL存

94
00:02:33,280 --> 00:02:34,520
咱们为什么非要去选择Redis

95
00:02:34,520 --> 00:02:36,000
这里原因是什么呢

96
00:02:36,000 --> 00:02:38,080
因为用户数据和浏览器的交互非常频繁

97
00:02:38,080 --> 00:02:38,840
为什么呢

98
00:02:38,840 --> 00:02:40,300
比如说你去使用淘宝的时候

99
00:02:40,300 --> 00:02:41,580
你是不是点击一次商品

100
00:02:41,580 --> 00:02:42,200
咱们的页面就要刷新

101
00:02:42,200 --> 00:02:43,020
那么你页面刷新

102
00:02:43,020 --> 00:02:45,140
这个页面是不是就需要去读取你的用户信息

103
00:02:45,140 --> 00:02:45,920
它需要知道里面登录

104
00:02:45,920 --> 00:02:46,740
对吧

105
00:02:46,740 --> 00:02:48,420
那么我们往往去购买一个商品

106
00:02:48,420 --> 00:02:49,220
比如说你购买一个手机

107
00:02:49,220 --> 00:02:50,000
你是不是要货币三家

108
00:02:50,000 --> 00:02:51,060
你需要去看很多的店

109
00:02:51,060 --> 00:02:52,960
那么你的页面就会经常去刷新

110
00:02:52,960 --> 00:02:55,360
那么如果说经常去刷新

111
00:02:55,360 --> 00:02:57,620
那么我们的数据库是不是要频繁的去读写

112
00:02:57,620 --> 00:02:59,560
那么像传统的硬盘型数据库

113
00:02:59,560 --> 00:03:00,880
比如说Mongo或者Mysoco

114
00:03:00,880 --> 00:03:02,800
就会遇到一些性能问题

115
00:03:02,800 --> 00:03:04,500
但是Redis它的特点是什么

116
00:03:04,500 --> 00:03:05,580
是不是内存型快

117
00:03:05,580 --> 00:03:08,040
所以说我们选择使用Redis

118
00:03:08,040 --> 00:03:10,020
来完成咱们用户信息的一个存储

119
00:03:10,020 --> 00:03:11,040
使用Session这一种技术

120
00:03:11,040 --> 00:03:13,600
那么同学们可能此时还会有一个疑问

121
00:03:13,600 --> 00:03:14,340
什么呢

122
00:03:14,340 --> 00:03:16,040
你比如说我们用户量非常大的情况下

123
00:03:16,040 --> 00:03:16,540
你像微信

124
00:03:16,540 --> 00:03:18,500
是不是在中国就有十几亿的用户

125
00:03:18,500 --> 00:03:20,460
那么他们的内存够用吗

126
00:03:20,460 --> 00:03:21,580
因为我们一台电脑

127
00:03:21,580 --> 00:03:22,540
你的硬盘可能有几T

128
00:03:22,540 --> 00:03:24,640
但是内存可能只有8G或者16G

129
00:03:24,640 --> 00:03:26,420
其实这里同学们不需要去担心

130
00:03:26,420 --> 00:03:28,260
因为Redis也可以去使用集群的技术

131
00:03:28,260 --> 00:03:29,500
你比如说一台机器是16G

132
00:03:29,500 --> 00:03:30,460
那么两台是32G

133
00:03:30,460 --> 00:03:32,360
那么利用集群你可以加无数的机器

134
00:03:32,360 --> 00:03:33,300
都是有可能的

135
00:03:33,300 --> 00:03:33,700
好

136
00:03:33,700 --> 00:03:35,800
那么这里就是咱们这一系列的课程

