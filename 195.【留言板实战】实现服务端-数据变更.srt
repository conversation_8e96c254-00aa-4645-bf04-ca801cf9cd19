1
00:00:00,000 --> 00:00:04,720
完成了查询的业务逻辑之后

2
00:00:04,720 --> 00:00:05,800
还剩下一个功能点

3
00:00:05,800 --> 00:00:06,740
就是变更操作

4
00:00:06,740 --> 00:00:08,080
我们在添加留言信息的时候

5
00:00:08,080 --> 00:00:09,800
会涉及到数据的变更

6
00:00:09,800 --> 00:00:11,160
这就是我们用到了mutation

7
00:00:11,160 --> 00:00:12,740
所以说接下来我们来实现一下

8
00:00:12,740 --> 00:00:14,800
服务端的变更操作的处理逻辑

9
00:00:14,800 --> 00:00:16,380
其实核心就两部分

10
00:00:16,380 --> 00:00:18,500
一个就是准备mutation类型

11
00:00:18,500 --> 00:00:19,800
以及输入类型

12
00:00:19,800 --> 00:00:21,720
然后把对应的resolver函数

13
00:00:21,720 --> 00:00:22,380
来处理一下

14
00:00:22,380 --> 00:00:22,980
这就可以

15
00:00:22,980 --> 00:00:25,180
接下来我们首先来准备一下

16
00:00:25,180 --> 00:00:26,000
相关的类型

17
00:00:26,000 --> 00:00:27,880
在这我们要定义一个mutation

18
00:00:27,880 --> 00:00:28,780
就是type

19
00:00:28,780 --> 00:00:29,940
Mutation

20
00:00:29,940 --> 00:00:31,860
这呢我们起个名字

21
00:00:31,860 --> 00:00:33,020
Create

22
00:00:33,020 --> 00:00:33,900
然后叫comment

23
00:00:33,900 --> 00:00:36,100
后面呢我们需要提供参数

24
00:00:36,100 --> 00:00:37,520
commentInput

25
00:00:37,520 --> 00:00:39,060
这呢我们要使用的是输入类型

26
00:00:39,060 --> 00:00:42,340
然后呢在上面单独的去定义一下

27
00:00:42,340 --> 00:00:43,620
这个commentInput

28
00:00:43,620 --> 00:00:45,200
那这里边我们要用到关键字

29
00:00:45,200 --> 00:00:45,760
就是input

30
00:00:45,760 --> 00:00:47,620
那这个字段的话呢

31
00:00:47,620 --> 00:00:48,080
我们需要两个

32
00:00:48,080 --> 00:00:48,720
一个是拥户名

33
00:00:48,720 --> 00:00:49,300
拥战内幕

34
00:00:49,300 --> 00:00:51,240
还有一个就是留言的信息

35
00:00:51,240 --> 00:00:51,780
就是content

36
00:00:51,780 --> 00:00:53,340
这是spin

37
00:00:53,340 --> 00:00:55,560
好那这个显示的结果的话

38
00:00:55,560 --> 00:00:57,100
我们还是给它指定成comment

39
00:00:57,100 --> 00:00:58,660
这个类型我们之前是定义过的

40
00:00:58,660 --> 00:01:02,740
这是关于Mutation相关的类型定义

41
00:01:02,740 --> 00:01:04,100
然后的话我们要处理的

42
00:01:04,100 --> 00:01:06,060
就是这里边的resolver函数了

43
00:01:06,060 --> 00:01:07,700
需要单独准备一个类型Mutation

44
00:01:07,700 --> 00:01:11,100
然后里边提供一个字段

45
00:01:11,100 --> 00:01:12,600
这个字段要和上面匹配

46
00:01:12,600 --> 00:01:14,220
这是一个函数

47
00:01:14,220 --> 00:01:17,320
这里边要做什么事呢

48
00:01:17,320 --> 00:01:18,660
注意在这背注一下

49
00:01:18,660 --> 00:01:21,020
这里边首先要接收客户端

50
00:01:21,020 --> 00:01:22,360
传记的数据

51
00:01:22,360 --> 00:01:26,700
然后把数据保存在文件中

52
00:01:26,700 --> 00:01:28,840
因为我们这个留言的列表数据

53
00:01:28,840 --> 00:01:30,140
存储在这个文件中

54
00:01:30,140 --> 00:01:31,160
这文件中的数据

55
00:01:31,160 --> 00:01:31,160
就是这个data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data

56
00:01:31,160 --> 00:02:01,160
就是这个data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.data.d

57
00:02:01,160 --> 00:02:02,400
获取了comment

58
00:02:02,400 --> 00:02:04,000
然后调用set data

59
00:02:04,000 --> 00:02:05,940
把这个数据直接传过去就可以

60
00:02:05,940 --> 00:02:08,940
这是x当中的comment

61
00:02:08,940 --> 00:02:12,240
input当中的user name

62
00:02:12,240 --> 00:02:13,360
然后另外一个

63
00:02:13,360 --> 00:02:16,340
是这里边的comment input中的content

64
00:02:16,340 --> 00:02:18,760
这是我们需要的数据

65
00:02:18,760 --> 00:02:20,200
由于这个是ebo的操作

66
00:02:20,200 --> 00:02:22,140
所以说我们这应该用atink

67
00:02:22,140 --> 00:02:24,820
然后这里边的返回值

68
00:02:24,820 --> 00:02:26,720
我们通过await方式来获取

69
00:02:26,720 --> 00:02:29,000
好

70
00:02:29,000 --> 00:02:30,640
这个返回的结果是什么呢

71
00:02:30,640 --> 00:02:31,260
我们再来看一下

72
00:02:31,260 --> 00:02:34,400
实际上就是写入文件成功之后

73
00:02:34,400 --> 00:02:35,260
会返回一个success

74
00:02:35,260 --> 00:02:36,280
所以说我们这儿呢

75
00:02:36,280 --> 00:02:36,660
做一个判断

76
00:02:36,660 --> 00:02:38,880
就是如果ID

77
00:02:38,880 --> 00:02:40,160
它这个值呢

78
00:02:40,160 --> 00:02:40,580
是success

79
00:02:40,580 --> 00:02:41,740
这就证明呢

80
00:02:41,740 --> 00:02:42,700
这个添加成功

81
00:02:42,700 --> 00:02:44,900
添加成功之后

82
00:02:44,900 --> 00:02:45,800
我们直接返回点东西

83
00:02:45,800 --> 00:02:47,380
我们就把这个UDERNAME呢

84
00:02:47,380 --> 00:02:47,940
和这个content

85
00:02:47,940 --> 00:02:48,740
都原封不动的

86
00:02:48,740 --> 00:02:49,320
给返回去

87
00:02:49,320 --> 00:02:51,020
是这个

88
00:02:51,020 --> 00:02:52,240
好然后呢

89
00:02:52,240 --> 00:02:53,260
还有一个是content

90
00:02:53,260 --> 00:02:55,760
这个呢

91
00:02:55,760 --> 00:02:56,780
改过来content

92
00:02:56,780 --> 00:02:57,680
好这是

93
00:02:57,680 --> 00:02:58,960
关于Midation中的

94
00:02:58,960 --> 00:02:59,480
业务处理

95
00:02:59,480 --> 00:03:00,260
就这么多

96
00:03:00,260 --> 00:03:03,140
好那接下来我们就启动服务

97
00:03:03,140 --> 00:03:05,640
Node Index

98
00:03:05,640 --> 00:03:07,940
好启动之后我们再来测试一下

99
00:03:07,940 --> 00:03:10,160
具体的话我们在这里单独的再开一个标签

100
00:03:10,160 --> 00:03:13,160
然后通过Mutation来做测试

101
00:03:13,160 --> 00:03:15,740
起个名字叫Create Comment

102
00:03:15,740 --> 00:03:17,100
然后我们定义变量

103
00:03:17,100 --> 00:03:20,560
这叫Comment Input

104
00:03:20,560 --> 00:03:22,260
后边是输入类型

105
00:03:22,260 --> 00:03:24,440
Comment Input

106
00:03:24,440 --> 00:03:27,640
然后下面这个字单的名字这个是一样的

107
00:03:27,640 --> 00:03:29,980
然后这我们需要这个变量

108
00:03:29,980 --> 00:03:31,500
然后放这

109
00:03:31,500 --> 00:03:33,960
然后后边是在多伦福的这个名字

110
00:03:33,960 --> 00:03:35,460
然后操作成功之后

111
00:03:35,460 --> 00:03:36,840
我们直接把这个UserName和Content

112
00:03:36,840 --> 00:03:38,400
再查询回来

113
00:03:38,400 --> 00:03:40,700
好 这是变更的具体的动作

114
00:03:40,700 --> 00:03:43,640
然后我们需要提供一下对应的数据

115
00:03:43,640 --> 00:03:45,160
这个位置

116
00:03:45,160 --> 00:03:47,420
首先我们要提供的是

117
00:03:47,420 --> 00:03:49,080
Comment Input

118
00:03:49,080 --> 00:03:50,820
然后后边是具体的数据

119
00:03:50,820 --> 00:03:53,400
分别是UserName

120
00:03:53,400 --> 00:03:55,220
这栏加个名字

121
00:03:55,220 --> 00:03:57,580
然后是内容

122
00:03:57,580 --> 00:03:58,720
就是留言的信息

123
00:03:58,720 --> 00:04:00,360
这里边我们就写Hello

124
00:04:00,360 --> 00:04:00,980
好

125
00:04:00,980 --> 00:04:01,900
数据有了

126
00:04:01,900 --> 00:04:02,880
然后我们执行

127
00:04:02,880 --> 00:04:04,880
会发现这个动作已经成功了

128
00:04:04,880 --> 00:04:06,300
因为返回来我们需要的数据了

129
00:04:06,300 --> 00:04:08,580
就是传入的数据和返回来的是一样的

130
00:04:08,580 --> 00:04:09,760
然后的话我们看后台的文件

131
00:04:09,760 --> 00:04:10,500
有没有协助成功

132
00:04:10,500 --> 00:04:12,260
这个文件隔日画一下

133
00:04:12,260 --> 00:04:14,740
我们会发现上面多了一套数据

134
00:04:14,740 --> 00:04:15,680
这样的话就证明

135
00:04:15,680 --> 00:04:17,260
我们刚才的服务端的

136
00:04:17,260 --> 00:04:19,800
变更的相关的业务逻辑是没问题了

137
00:04:19,800 --> 00:04:20,860
好

138
00:04:20,860 --> 00:04:21,640
到此为止的话

139
00:04:21,640 --> 00:04:22,540
关于服务端的实现

140
00:04:22,540 --> 00:04:24,080
我们就已经完成了

141
00:04:24,080 --> 00:04:25,340
包括两部分功能

142
00:04:25,340 --> 00:04:26,940
一部分是查询相关的业务

143
00:04:26,940 --> 00:04:28,760
另外一部分是变更相关的要务

144
00:04:28,760 --> 00:04:30,700
其实核心概括来说的话

145
00:04:30,700 --> 00:04:31,380
就是两部分

146
00:04:31,380 --> 00:04:32,980
一个就是处理这个类型的定义

147
00:04:32,980 --> 00:04:34,720
另外一部分就是处理这个数据的解析

148
00:04:34,720 --> 00:04:37,220
这是关于服务端相关的实现

149
00:04:37,220 --> 00:04:38,820
主要就这么多

