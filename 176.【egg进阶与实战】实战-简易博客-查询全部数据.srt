1
00:00:00,000 --> 00:00:02,500
那么这节课我们就来看一下怎么样去查询数据

2
00:00:02,500 --> 00:00:07,000
那么查询数据我们刚才是不是讲过来

3
00:00:07,000 --> 00:00:08,240
查询封为几种

4
00:00:08,240 --> 00:00:09,820
查询是不是封为两种

5
00:00:09,820 --> 00:00:12,360
查询封为两种

6
00:00:12,360 --> 00:00:14,220
那么第一种的话呢就是查询全部的数据

7
00:00:14,220 --> 00:00:16,180
第二种呢就是查询某一条数据

8
00:00:16,180 --> 00:00:18,520
那么我们先来看一下怎么样去查询全部的数据

9
00:00:18,520 --> 00:00:21,220
那么查询全部的数据是不是通过get posts

10
00:00:21,220 --> 00:00:22,060
对吧

11
00:00:22,060 --> 00:00:22,800
那么get

12
00:00:22,800 --> 00:00:25,180
get posts为什么它可以查询全部的数据

13
00:00:25,180 --> 00:00:26,800
是不是因为我们也没有传ID

14
00:00:26,800 --> 00:00:28,520
所以我们就直接来

15
00:00:28,520 --> 00:00:30,520
进入index这样一个方法

16
00:00:30,520 --> 00:00:34,160
asyncindex

17
00:00:34,160 --> 00:00:36,000
好

18
00:00:36,000 --> 00:00:37,260
那么我们首先来思考一下

19
00:00:37,260 --> 00:00:39,580
怎么样去完成index

20
00:00:39,580 --> 00:00:45,640
那么我们需不需要从我们的request去接受参数

21
00:00:45,640 --> 00:00:46,820
不需要吧

22
00:00:46,820 --> 00:00:47,960
不需要从玻璃接受参数吧

23
00:00:47,960 --> 00:00:49,200
而且它是一个什么请求

24
00:00:49,200 --> 00:00:51,580
我们可以毫无疑问的知道它是个get的请求

25
00:00:51,580 --> 00:00:53,960
因为不需要创立参数

26
00:00:53,960 --> 00:00:56,360
所以我们直接去get就可以了

27
00:00:56,360 --> 00:00:57,240
那么get的话

28
00:00:57,240 --> 00:01:00,260
所以说我们这里的操作部署就只有一步

29
00:01:00,260 --> 00:01:00,720
是什么呢

30
00:01:00,720 --> 00:01:01,660
咱们直接就是查询数据

31
00:01:01,660 --> 00:01:02,460
对吧

32
00:01:02,460 --> 00:01:03,340
那么数据查到之后呢

33
00:01:03,340 --> 00:01:04,420
返回给用户就可以了

34
00:01:04,420 --> 00:01:04,640
好

35
00:01:04,640 --> 00:01:06,680
那么我们查询数据怎么样去查呢

36
00:01:06,680 --> 00:01:09,700
查询数据怎么样去查

37
00:01:09,700 --> 00:01:14,460
我们刚才是不是讲过来

38
00:01:14,460 --> 00:01:15,960
我们增山改查都是来源于哪里啊

39
00:01:15,960 --> 00:01:16,360
它的操作

40
00:01:16,360 --> 00:01:18,320
是不是来源于我们model啊

41
00:01:18,320 --> 00:01:19,380
这里不是注释吗

42
00:01:19,380 --> 00:01:21,220
业务里面会经常用这个类的方法

43
00:01:21,220 --> 00:01:22,040
进行增山改查

44
00:01:22,040 --> 00:01:22,980
所以说呢

45
00:01:22,980 --> 00:01:23,980
我们直接

46
00:01:23,980 --> 00:01:26,380
waitcontest.model

47
00:01:26,380 --> 00:01:28,680
点什么是不是posed

48
00:01:28,680 --> 00:01:30,480
然后呢 调用它的什么方法

49
00:01:30,480 --> 00:01:33,040
find吧 其实在mongos里面呢 它有一个范的方法

50
00:01:33,040 --> 00:01:35,860
这里呢 我们就不去介绍 有兴趣可以去看我们的

51
00:01:35,860 --> 00:01:37,900
那么其实很简单嘛

52
00:01:37,900 --> 00:01:40,200
posed 占一个类 那么在一个类里面 它有一个范的方法

53
00:01:40,200 --> 00:01:42,760
那么我们去查询的时候是不是需要去传一些查询条件

54
00:01:42,760 --> 00:01:45,060
因为我们现在呢 是不是去查询所有的数据

55
00:01:45,060 --> 00:01:46,340
那么你传一个空对象就可以了

56
00:01:46,340 --> 00:01:47,380
所以说呢 这里呢

57
00:01:47,380 --> 00:01:51,720
我们是不是现在需要通过一个rebounce去接收啊 对吧

58
00:01:51,720 --> 00:01:56,360
我们此时呢通过一个rebounce去接受咱们查询到这个数据

59
00:01:56,360 --> 00:01:57,480
那么为什么要wait呢

60
00:01:57,480 --> 00:01:58,580
是不是讲过呀

61
00:01:58,580 --> 00:02:00,300
咱们对module咱们对mongoose的操作

62
00:02:00,300 --> 00:02:01,480
一般来讲它都是一步的

63
00:02:01,480 --> 00:02:02,020
好

64
00:02:02,020 --> 00:02:04,140
那当我们去查询完结果之后

65
00:02:04,140 --> 00:02:06,720
是不是需要去调用什么呀

66
00:02:06,720 --> 00:02:09,100
是不是调用咱们的一个success方法

67
00:02:09,100 --> 00:02:11,440
响应给我们的一个用户

68
00:02:11,440 --> 00:02:12,940
咱们把read传递出去

69
00:02:12,940 --> 00:02:13,280
好

70
00:02:13,280 --> 00:02:14,940
那么我们来看一下咱们的结果是什么

71
00:02:14,940 --> 00:02:16,640
这里呢

72
00:02:16,640 --> 00:02:17,680
我们需不需要通过postsman

73
00:02:17,680 --> 00:02:18,980
是不是不需要啊

74
00:02:18,980 --> 00:02:19,820
我们直接通过浏览器

75
00:02:19,820 --> 00:02:20,440
是不是方便一点

76
00:02:20,440 --> 00:02:21,860
因为它是一个什么请求

77
00:02:21,860 --> 00:02:22,980
因为它是一个get的请求

78
00:02:22,980 --> 00:02:25,860
我们来访问API posts

79
00:02:25,860 --> 00:02:26,780
大家可以看到

80
00:02:26,780 --> 00:02:29,000
此时我们的结果是什么样的

81
00:02:29,000 --> 00:02:30,520
message OK code 0

82
00:02:30,520 --> 00:02:31,440
下面的data里面

83
00:02:31,440 --> 00:02:32,520
是不是我们所返回的一个数据

84
00:02:32,520 --> 00:02:33,840
那么data里面的数据是谁

85
00:02:33,840 --> 00:02:35,160
data里面的数据

86
00:02:35,160 --> 00:02:36,840
是不是就是咱们的res

87
00:02:36,840 --> 00:02:38,260
也就是咱们posts所犯的

88
00:02:38,260 --> 00:02:38,780
出来的数据

89
00:02:38,780 --> 00:02:40,320
如果说不相信的同学

90
00:02:40,320 --> 00:02:41,180
也没关系

91
00:02:41,180 --> 00:02:42,780
我们把它给打印出来就可以了

92
00:02:42,780 --> 00:02:44,140
就是这么的

93
00:02:44,140 --> 00:02:44,760
神奇

94
00:02:44,760 --> 00:02:46,240
其实大家不要觉得辅导很难

95
00:02:46,240 --> 00:02:47,300
其实就这么点东西

96
00:02:47,300 --> 00:02:48,820
不一定比咱们前端辅装

97
00:02:48,820 --> 00:02:49,360
辅导的东西

98
00:02:49,360 --> 00:02:50,020
如果说大家搞出了

99
00:02:50,020 --> 00:02:51,040
反而比前端简单

100
00:02:51,040 --> 00:02:52,580
而且很省心

101
00:02:52,580 --> 00:02:55,140
当然学到后面也会有一些难度

102
00:02:55,140 --> 00:02:56,160
那么我们再

103
00:02:56,160 --> 00:02:58,220
访问一下

104
00:02:58,220 --> 00:03:00,520
大家可以看到我们的read是什么呀

105
00:03:00,520 --> 00:03:01,800
是不是一个数字

106
00:03:01,800 --> 00:03:04,100
那么为什么我们的数据结构长这样一个样子

107
00:03:04,100 --> 00:03:05,120
大家可以看到我们的base

108
00:03:05,120 --> 00:03:06,660
当然是个says里面是不是就是一个date

109
00:03:06,660 --> 00:03:08,460
那么我们date是不是就是这样的一个

110
00:03:08,460 --> 00:03:09,740
就是这样的一个数字

111
00:03:09,740 --> 00:03:10,760
所以说我们可以得到

112
00:03:10,760 --> 00:03:12,040
我们可以得到

113
00:03:12,040 --> 00:03:13,320
现在我们这样的一个数据结构

114
00:03:13,320 --> 00:03:15,360
其实我们的查询就是这么的

115
00:03:15,360 --> 00:03:15,880
简单

116
00:03:15,880 --> 00:03:16,380
好

117
00:03:16,380 --> 00:03:18,180
那么我们刚才是不是已经完成了我们

118
00:03:19,200 --> 00:03:21,500
我们查询查询里面的什么功能呢

119
00:03:21,500 --> 00:03:23,300
查询全部的数据

120
00:03:23,300 --> 00:03:26,120
我们这里完成的是

121
00:03:26,120 --> 00:03:28,160
查询全部数据

122
00:03:28,160 --> 00:03:30,460
好 那么我们首先来总结一下我们这节课的内容

123
00:03:30,460 --> 00:03:33,280
我们这节课其实讲解讲解的是我们查询所有的数据

124
00:03:33,280 --> 00:03:35,080
所以说我们只需要定义一个index方法

125
00:03:35,080 --> 00:03:37,380
那么index方法对应的是谁呢是不是我们的

126
00:03:37,380 --> 00:03:38,140
posts

127
00:03:38,140 --> 00:03:39,940
所以说呢只是不需要查任何参数

128
00:03:39,940 --> 00:03:41,480
那么我们一定是查询所有的数据

129
00:03:41,480 --> 00:03:42,240
怎么去查呢

130
00:03:42,240 --> 00:03:43,520
我们通过model里面的

131
00:03:43,520 --> 00:03:45,820
是不是咱们posts这样一个model然后调用它的find方法

132
00:03:45,820 --> 00:03:47,880
因为我们查询所有的数据所以说不需要

133
00:03:48,120 --> 00:03:49,660
查询条件有空对象就可以了

134
00:03:49,660 --> 00:03:51,440
那么我们查完之后去调用一个

135
00:03:51,440 --> 00:03:52,220
success

136
00:03:52,220 --> 00:03:54,780
那么这里大家有没有注意到我们是不是漏了一点什么呀

137
00:03:54,780 --> 00:03:55,800
是不是漏了一个

138
00:03:55,800 --> 00:03:56,820
trycatch

139
00:03:56,820 --> 00:03:58,100
那么我们的把它补上

140
00:03:58,100 --> 00:04:02,460
其实我们这里去加它是为了防止发生一些

141
00:04:02,460 --> 00:04:02,960
意外

142
00:04:02,960 --> 00:04:07,060
所以说为了保险起降我们所有的一些咱们数据库的一些操作都要去加上咱们一个trycatch

143
00:04:07,060 --> 00:04:07,840
好

144
00:04:07,840 --> 00:04:08,600
catch一下

145
00:04:08,600 --> 00:04:11,160
this

146
00:04:11,160 --> 00:04:12,440
await

147
00:04:12,440 --> 00:04:13,980
this.4

148
00:04:13,980 --> 00:04:15,260
咱们把error给传进去

149
00:04:15,260 --> 00:04:15,760
好

150
00:04:15,760 --> 00:04:17,040
那么这里呢就是我们这几个的内容

151
00:04:17,300 --> 00:04:19,600
下期可能我们来看一下怎么样去查询我们的单个数据

