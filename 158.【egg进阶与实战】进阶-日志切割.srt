1
00:00:00,000 --> 00:00:02,420
好,那么其实关于日志呢,还一点内容没有讲完

2
00:00:02,420 --> 00:00:04,860
那么我们需要考一种情况,什么情况呢

3
00:00:04,860 --> 00:00:06,180
我们的日志

4
00:00:06,180 --> 00:00:08,400
默认它有一种切割方式

5
00:00:08,400 --> 00:00:09,980
是什么呀,每日的零点

6
00:00:09,980 --> 00:00:11,800
会去切割我们的文件,为什么要切割

7
00:00:11,800 --> 00:00:12,600
同学们思考一下

8
00:00:12,600 --> 00:00:15,880
是不是我们的日志文件会日复一日,年复一年

9
00:00:15,880 --> 00:00:18,220
它会越来越大,那么有一天它会充满你的硬盘

10
00:00:18,220 --> 00:00:19,940
怎么办,你一个文件可能非常大

11
00:00:19,940 --> 00:00:21,760
你到时候可能把它打开啊,这样是会影响性能

12
00:00:21,760 --> 00:00:23,920
包括你去查询也不是很好查,你很有可能

13
00:00:23,920 --> 00:00:25,560
一个文件有几千万条数据,怎么办

14
00:00:25,560 --> 00:00:27,540
那么呢,你就需要去对它进行切割

15
00:00:27,540 --> 00:00:29,260
那么其实在1GG里面

16
00:00:29,260 --> 00:00:31,080
它默认已经帮我们做好了

17
00:00:31,080 --> 00:00:31,880
切割其实大家可以看到

18
00:00:31,880 --> 00:00:33,480
我们在本地开发环境

19
00:00:33,480 --> 00:00:33,840
我们的log

20
00:00:33,840 --> 00:00:34,840
是不是它分为

21
00:00:34,840 --> 00:00:35,660
比如说common arrow

22
00:00:35,660 --> 00:00:36,840
这里有一个common arrow

23
00:00:36,840 --> 00:00:37,840
6月29

24
00:00:37,840 --> 00:00:38,920
然后还会有一个

25
00:00:38,920 --> 00:00:39,960
 arrow.log

26
00:00:39,960 --> 00:00:40,820
7月3号

27
00:00:40,820 --> 00:00:41,740
那么为什么没有7月4号

28
00:00:41,740 --> 00:00:42,640
咱们今天是7月4号

29
00:00:42,640 --> 00:00:43,260
为什么没有

30
00:00:43,260 --> 00:00:44,900
是因为我们今天还没到零点

31
00:00:44,900 --> 00:00:46,820
所以说它还没有帮我们去切割

32
00:00:46,820 --> 00:00:48,000
那么这里其实1G默认

33
00:00:48,000 --> 00:00:48,820
帮我们以天位单位

34
00:00:48,820 --> 00:00:49,520
去已经做好了

35
00:00:49,520 --> 00:00:50,620
切割了一个操作

36
00:00:50,620 --> 00:00:52,800
但是你如果说

37
00:00:52,800 --> 00:00:53,480
我不想按日

38
00:00:53,480 --> 00:00:54,320
我想按大小去切

39
00:00:54,320 --> 00:00:54,980
当然也可以

40
00:00:54,980 --> 00:00:56,540
那么你可能需要去做一些配置

41
00:00:56,540 --> 00:00:57,980
那么我怎么样去配呢

42
00:00:57,980 --> 00:00:59,680
同样的在config里面

43
00:00:59,680 --> 00:01:00,980
那么config不用我去再介绍了吧

44
00:01:00,980 --> 00:01:03,480
它里面提供一个log rotator这样的支端

45
00:01:03,480 --> 00:01:06,280
里面有个属性叫做files rotate by size

46
00:01:06,280 --> 00:01:07,540
by size什么意思是不按照大小

47
00:01:07,540 --> 00:01:08,780
那么这里呢你会去配置一下

48
00:01:08,780 --> 00:01:09,600
按照什么样大小呢

49
00:01:09,600 --> 00:01:12,880
mix file size 2×1024×1024×1024

50
00:01:12,880 --> 00:01:13,660
也就是两机的意思

51
00:01:13,660 --> 00:01:15,920
那么这里呢你就可以通过大小去对待进行一个切割

52
00:01:15,920 --> 00:01:18,960
那么同样的你除了可以按照文件的大小进行切割呢

53
00:01:18,960 --> 00:01:20,260
你还可以去按照它的什么样

54
00:01:20,260 --> 00:01:22,100
是不是也可以去按照时间呢

55
00:01:22,100 --> 00:01:23,600
你比如说我不想按天我想按小时

56
00:01:23,600 --> 00:01:25,220
我们的服务非常频繁访问

57
00:01:25,220 --> 00:01:27,060
我想的每时每刻去关注它

58
00:01:27,060 --> 00:01:29,320
当然你可以去按照小时去拆分

59
00:01:29,320 --> 00:01:31,180
那么同样的我们去配置一个

60
00:01:31,180 --> 00:01:32,180
咱们confine里面有一个

61
00:01:32,180 --> 00:01:33,920
这里就改为了

62
00:01:33,920 --> 00:01:35,960
fears rotate by hour

63
00:01:35,960 --> 00:01:37,240
by hour是什么按小时

64
00:01:37,240 --> 00:01:39,280
那么有没有百分钟呢

65
00:01:39,280 --> 00:01:40,260
我也不知道

66
00:01:40,260 --> 00:01:41,160
大家可以去看一下文章

67
00:01:41,160 --> 00:01:41,940
对吧

68
00:01:41,940 --> 00:01:42,180
好

69
00:01:42,180 --> 00:01:45,480
这里就是我们去进行日子切割的一个方式

70
00:01:45,480 --> 00:01:46,100
包括呢

71
00:01:46,100 --> 00:01:47,780
这里还有一点血统面去注意

72
00:01:47,780 --> 00:01:48,980
我们的日子的存储

73
00:01:48,980 --> 00:01:50,260
其实为了考虑它的性能

74
00:01:50,260 --> 00:01:50,960
当然不是

75
00:01:50,960 --> 00:01:52,080
不是说你打印一条

76
00:01:52,080 --> 00:01:52,500
它就存一条

77
00:01:52,500 --> 00:01:55,020
其实它会每间隔一秒存一次

78
00:01:55,020 --> 00:01:55,460
什么意思

79
00:01:55,460 --> 00:01:56,360
也就是说

80
00:01:56,360 --> 00:01:57,140
比如说你一秒钟之内

81
00:01:57,140 --> 00:01:57,980
是不是咱们去打印了

82
00:01:57,980 --> 00:01:58,880
很debug很多日子

83
00:01:58,880 --> 00:01:59,140
但是呢

84
00:01:59,140 --> 00:02:00,440
它不会同时的去写入

85
00:02:00,440 --> 00:02:00,860
它呢

86
00:02:00,860 --> 00:02:02,200
会比如说把它给缓存起来

87
00:02:02,200 --> 00:02:03,080
是不是有点类似于我们的reddit

88
00:02:03,080 --> 00:02:03,520
这样呢

89
00:02:03,520 --> 00:02:03,940
会防止

90
00:02:03,940 --> 00:02:05,200
是不是可以防止我们一瞬间

91
00:02:05,200 --> 00:02:05,740
对吧

92
00:02:05,740 --> 00:02:06,120
读写

93
00:02:06,120 --> 00:02:07,280
是不是太频繁了

94
00:02:07,280 --> 00:02:07,420
好

95
00:02:07,420 --> 00:02:07,820
这里呢

96
00:02:07,820 --> 00:02:08,620
就是我们这几个内容

97
00:02:08,620 --> 00:02:09,220
我们来

98
00:02:09,220 --> 00:02:10,460
简单的来总结一下

99
00:02:10,460 --> 00:02:11,640
我们刚才是不是讲到了

100
00:02:11,640 --> 00:02:13,380
咱们的一个日子的切割

101
00:02:13,380 --> 00:02:14,180
那么日子切割

102
00:02:14,180 --> 00:02:15,040
它可以分为哪些方式

103
00:02:15,040 --> 00:02:16,220
是不是可以通过大小了

104
00:02:16,220 --> 00:02:17,720
也可以通过我们的一个时间

105
00:02:17,720 --> 00:02:18,280
那么时间呢

106
00:02:18,280 --> 00:02:18,900
你可以按日

107
00:02:18,900 --> 00:02:20,660
也呢也可以按小时

108
00:02:20,660 --> 00:02:22,140
看你自己的爱好

109
00:02:22,140 --> 00:02:23,000
那么同样呢

110
00:02:23,000 --> 00:02:23,840
这里是不是还会有一个

111
00:02:23,840 --> 00:02:25,780
日字是不是还会有一个新能问题啊

112
00:02:25,780 --> 00:02:26,080
对吧

113
00:02:26,080 --> 00:02:27,540
那么它呢默认

114
00:02:27,540 --> 00:02:28,240
一秒

115
00:02:28,240 --> 00:02:29,160
是不是一秒写入一次

116
00:02:29,160 --> 00:02:29,740
对吧

117
00:02:29,740 --> 00:02:30,140
好

118
00:02:30,140 --> 00:02:31,660
这里呢就是我们日字这块的内容

