1
00:00:00,000 --> 00:00:02,300
好 我们再来看一个例子 怎么样去实现缓存

2
00:00:02,300 --> 00:00:04,600
假设我们现在有这样一个场景

3
00:00:04,600 --> 00:00:05,880
比如说我们

4
00:00:05,880 --> 00:00:06,920
需要做一个网站

5
00:00:06,920 --> 00:00:07,940
去实时的去

6
00:00:07,940 --> 00:00:09,480
绑合到咱们学生成绩的一个

7
00:00:09,480 --> 00:00:10,240
排名

8
00:00:10,240 --> 00:00:12,540
但是我们学生的成绩他

9
00:00:12,540 --> 00:00:14,840
肯定不可能说你每一分每一秒都在发生变化吧

10
00:00:14,840 --> 00:00:18,180
你可能一天更新一次或者你有可能一学期才更新一次

11
00:00:18,180 --> 00:00:19,720
那么我们就可以利用redis

12
00:00:19,720 --> 00:00:21,000
去对他进行缓存

13
00:00:21,000 --> 00:00:22,020
我们没有必要

14
00:00:22,020 --> 00:00:24,060
每次用户请求都去重新计算一次吧

15
00:00:24,060 --> 00:00:25,080
那么我们就来看一下

16
00:00:25,080 --> 00:00:26,360
比如说我们去设置一个

17
00:00:26,360 --> 00:00:27,400
两个小时的缓存

18
00:00:27,400 --> 00:00:29,180
在两个小时之内就不用去重新计算

19
00:00:29,440 --> 00:00:30,980
那么我们怎么样去实现这个需求呢

20
00:00:30,980 --> 00:00:32,260
我们直接来看一下

21
00:00:32,260 --> 00:00:41,720
首先我们现在是不是要去实现缓存

22
00:00:41,720 --> 00:00:43,780
那么这里呢我们先去定义一个k

23
00:00:43,780 --> 00:00:48,120
定义一个k

24
00:00:48,120 --> 00:00:50,440
叫什么呢就是咱们缓存所存储的

25
00:00:50,440 --> 00:00:53,760
咱们学生排名的字乱叫做什么呢叫做咱们就叫catch

26
00:00:53,760 --> 00:00:58,120
catch rock

27
00:00:59,440 --> 00:01:03,400
好首先我们这段定义了 第一步做什么

28
00:01:03,400 --> 00:01:08,660
我们来分析一下步骤 第一步是先去获取成绩啊

29
00:01:08,660 --> 00:01:13,000
如果没有没有学生的成绩 我们就重新计算

30
00:01:13,000 --> 00:01:19,400
然后呢是不是要缓存 那么如果有了

31
00:01:19,400 --> 00:01:22,480
直接读取吧

32
00:01:22,480 --> 00:01:28,120
不需要计算 好 那我们就来看一下 比如说我们去挖一个rank等于client点

33
00:01:29,440 --> 00:01:32,120
getget什么是不是catch rank呀

34
00:01:32,120 --> 00:01:37,640
那咱们这不需要去写判断if rank

35
00:01:37,640 --> 00:01:44,040
那也就是也就是说如果说我们能够如果说我们去有

36
00:01:44,040 --> 00:01:49,400
直接返回吧所以呢这点逻辑我们不用去写我们重点来看

37
00:01:49,400 --> 00:01:50,680
没有成绩情况

38
00:01:50,680 --> 00:01:54,280
那么没有成绩的情况首先比如说我们去挖一个多的rank

39
00:01:54,280 --> 00:01:56,580
我们是不是需要去计算排名呢比如说

40
00:01:56,840 --> 00:01:59,400
我们现在有一个计算的方法 此时会返回台名

41
00:01:59,400 --> 00:02:00,680
我们返回之后

42
00:02:00,680 --> 00:02:02,720
是不是需要去存储

43
00:02:02,720 --> 00:02:04,520
set

44
00:02:04,520 --> 00:02:05,800
所以我们去client点

45
00:02:05,800 --> 00:02:07,080
set他

46
00:02:07,080 --> 00:02:10,920
然后把我们计算出来的$rank把它给存储进去

47
00:02:10,920 --> 00:02:14,760
好

48
00:02:14,760 --> 00:02:17,840
那么存储进去之后 我们是不是还要给他设计过期时间

49
00:02:17,840 --> 00:02:18,600
对吧

50
00:02:18,600 --> 00:02:20,640
client点Expile

51
00:02:20,640 --> 00:02:22,440
Expile谁呢

52
00:02:22,440 --> 00:02:25,520
是不是我们的catch rank呀

53
00:02:25,760 --> 00:02:27,800
给他一个时间2小时7200秒

54
00:02:27,800 --> 00:02:29,080
那么到这里就结束了吗

55
00:02:29,080 --> 00:02:31,640
大家看一下这段代码有没有问题

56
00:02:31,640 --> 00:02:32,680
我们实现缓存逻辑

57
00:02:32,680 --> 00:02:34,460
好像是不够吧 为什么

58
00:02:34,460 --> 00:02:36,760
是不是加乎失误啊

59
00:02:36,760 --> 00:02:38,560
因为如果说我们在send这里出错了

60
00:02:38,560 --> 00:02:40,360
那么我们的学生成绩

61
00:02:40,360 --> 00:02:42,920
是不是expire会失败啊

62
00:02:42,920 --> 00:02:45,480
所以呢我们需要去进行

63
00:02:45,480 --> 00:02:47,520
失误的一个包装

64
00:02:47,520 --> 00:02:51,620
exec

65
00:02:51,620 --> 00:02:54,440
好 这里就是我们去实现缓存的一段

66
00:02:54,680 --> 00:02:56,060
逻辑其实非常简单

67
00:02:56,060 --> 00:03:00,060
相比我们之前举的例子已经算是很简单了吧

68
00:03:00,060 --> 00:03:02,360
我们来回顾一下是怎么去分析了

69
00:03:02,360 --> 00:03:04,440
首先我们肯定需要去先获取一下

70
00:03:04,440 --> 00:03:06,060
看一下它到底存不存在

71
00:03:06,060 --> 00:03:07,280
如果说存在我们就直接返回

72
00:03:07,280 --> 00:03:08,280
如果说不存在

73
00:03:08,280 --> 00:03:10,200
那么我们就来看一下

74
00:03:10,200 --> 00:03:11,460
不存在我们是不是要重新计算

75
00:03:11,460 --> 00:03:12,040
计算之后呢

76
00:03:12,040 --> 00:03:12,700
通过一个事物

77
00:03:12,700 --> 00:03:13,460
然后去存储它

78
00:03:13,460 --> 00:03:14,620
给它一个过期时间两小时

79
00:03:14,620 --> 00:03:17,140
这里呢就是我们去实现缓存的这样一段逻辑

80
00:03:17,140 --> 00:03:17,440
好

81
00:03:17,440 --> 00:03:19,600
我们来先把视频停一下

