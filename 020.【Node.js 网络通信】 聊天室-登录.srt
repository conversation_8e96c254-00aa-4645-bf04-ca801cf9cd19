1
00:00:00,000 --> 00:00:03,260
好 接下来我们这一节课呢

2
00:00:03,260 --> 00:00:05,680
就根据我们刚才设计的这个数据格式

3
00:00:05,680 --> 00:00:08,400
来进行我们的这个功能开发

4
00:00:08,400 --> 00:00:11,040
那么我们首先呢从这个用户登录开始

5
00:00:11,040 --> 00:00:12,720
我们这里的话呢

6
00:00:12,720 --> 00:00:14,900
我们的需求就是客户端输入昵称

7
00:00:14,900 --> 00:00:15,960
发送到服务端

8
00:00:15,960 --> 00:00:18,980
就是用户第一次进入我们这个聊天室

9
00:00:18,980 --> 00:00:20,080
要让他提示一下

10
00:00:20,080 --> 00:00:21,160
让他输入这个昵称

11
00:00:21,160 --> 00:00:22,540
好 服务端收到数据以后

12
00:00:22,540 --> 00:00:23,680
然后教训他是否重复

13
00:00:23,680 --> 00:00:24,740
如果已重复

14
00:00:24,740 --> 00:00:26,140
就告诉客户端重复了

15
00:00:26,140 --> 00:00:27,360
如果可以使用

16
00:00:27,360 --> 00:00:28,360
则把这个用户昵称

17
00:00:28,360 --> 00:00:30,080
包括它这个通信的socket

18
00:00:30,080 --> 00:00:31,720
存储到我们这个容器当中

19
00:00:31,720 --> 00:00:33,360
就是用于这个后续的数理

20
00:00:33,360 --> 00:00:37,460
然后接下来我们回到我们的编辑器当中

21
00:00:37,460 --> 00:00:39,660
找到我们的这个客户端

22
00:00:39,660 --> 00:00:43,000
首先我们为了方便的处理

23
00:00:43,000 --> 00:00:45,540
那这个时候我们在TCT chatroom当中

24
00:00:45,540 --> 00:00:46,900
我们在这新建一个文件

25
00:00:46,900 --> 00:00:49,460
这个文件叫做tats.js

26
00:00:49,460 --> 00:00:51,800
然后这个tats就是类型

27
00:00:51,800 --> 00:00:54,200
因为我们这个客户端和服务端

28
00:00:54,200 --> 00:00:55,260
在进行通信的时候

29
00:00:55,260 --> 00:00:57,460
他们的很多类型都是一样的

30
00:00:57,460 --> 00:00:59,320
如果说我们自己去写这个字符串太麻烦

31
00:00:59,320 --> 00:01:01,440
所以说我们在这里用一个对象

32
00:01:01,440 --> 00:01:03,360
把他们这个类型的去给它存储起来

33
00:01:03,360 --> 00:01:06,880
Login的消息类型我们给它设置为0

34
00:01:06,880 --> 00:01:10,020
然后是这个叫Broadcast我们给它设置为1

35
00:01:10,020 --> 00:01:12,560
然后P2P呢我们给它设置为2

36
00:01:12,560 --> 00:01:16,560
也就是说这个呢就是我们这个消息类型的一个定义

37
00:01:16,560 --> 00:01:18,940
然后接下来我们客户端和服务端

38
00:01:18,940 --> 00:01:20,920
所有用到的这些消息的类型

39
00:01:20,920 --> 00:01:22,760
他们去进行发送和判断的时候

40
00:01:22,760 --> 00:01:24,800
我们就不再去直接手写字符串了

41
00:01:24,800 --> 00:01:26,160
我们都来使用Type里面

42
00:01:26,160 --> 00:01:27,860
我们定义好的这些数据类型

43
00:01:27,860 --> 00:01:30,920
然后接下来我们来到我们的客户端

44
00:01:30,920 --> 00:01:32,880
在我们客户端这里

45
00:01:32,880 --> 00:01:34,920
首先我们在这里呢

46
00:01:34,920 --> 00:01:36,540
来给大家进行一个处理

47
00:01:36,540 --> 00:01:38,600
首先我们在客户端这儿

48
00:01:38,600 --> 00:01:40,260
我们来定义一个

49
00:01:40,260 --> 00:01:42,160
变量

50
00:01:42,160 --> 00:01:43,580
let nickname

51
00:01:43,580 --> 00:01:46,960
也就是说首先这个默认情况下

52
00:01:46,960 --> 00:01:48,840
客户端在这儿是没有昵称的

53
00:01:48,840 --> 00:01:50,640
当客户端与我们的服务端

54
00:01:50,640 --> 00:01:52,640
建立连接成功以后

55
00:01:52,640 --> 00:01:54,440
成功以后然后接下来

56
00:01:54,440 --> 00:01:56,480
我们这个时候叫一上来

57
00:01:56,480 --> 00:01:57,640
我们叫提示啊

58
00:01:57,640 --> 00:01:58,600
提示这个用户

59
00:01:58,600 --> 00:01:59,900
让他来输入这个昵称

60
00:01:59,900 --> 00:02:01,140
所以接下来

61
00:02:01,140 --> 00:02:02,800
我们就不在这直接发这个数据了

62
00:02:02,800 --> 00:02:04,180
我们在这里直接就是

63
00:02:04,180 --> 00:02:07,160
processstdout.write

64
00:02:07,160 --> 00:02:10,040
请输入昵称

65
00:02:10,040 --> 00:02:11,340
我们在这里提示用户

66
00:02:11,340 --> 00:02:13,180
让他输入这个昵称

67
00:02:13,180 --> 00:02:16,120
当然这个我们可以把它放到上面

68
00:02:16,120 --> 00:02:16,640
放到这里

69
00:02:16,640 --> 00:02:18,440
当用户输入这个昵称的时候呢

70
00:02:18,440 --> 00:02:20,000
那么就会出入这个data时间

71
00:02:20,000 --> 00:02:21,040
好

72
00:02:21,040 --> 00:02:22,800
那在这里我们就接收到了

73
00:02:22,800 --> 00:02:23,620
用户输入的这个数据

74
00:02:23,620 --> 00:02:25,820
然后接下来我们就做一个判断

75
00:02:25,820 --> 00:02:26,640
如果啊

76
00:02:26,640 --> 00:02:27,660
废一下

77
00:02:27,660 --> 00:02:29,240
那么这个就表示

78
00:02:29,240 --> 00:02:30,700
如果用户没有昵称

79
00:02:30,700 --> 00:02:32,240
就是说他现在还没有昵称的

80
00:02:32,240 --> 00:02:33,380
没有昵称的话

81
00:02:33,380 --> 00:02:34,540
那么这个时候呢

82
00:02:34,540 --> 00:02:36,100
当然我们在这儿

83
00:02:36,100 --> 00:02:37,160
以上就是提示他

84
00:02:37,160 --> 00:02:37,740
请输入昵称

85
00:02:37,740 --> 00:02:38,620
好

86
00:02:38,620 --> 00:02:40,600
然后接下来没有昵称的话

87
00:02:40,600 --> 00:02:42,180
那就表示当前这个消息呢

88
00:02:42,180 --> 00:02:44,340
是用户发送的这个昵称的消息

89
00:02:44,340 --> 00:02:45,880
所以说这个时候呢

90
00:02:45,880 --> 00:02:46,860
我们在这儿就可以去

91
00:02:46,860 --> 00:02:47,540
这样

92
00:02:47,540 --> 00:02:49,700
client.rat

93
00:02:49,700 --> 00:02:51,060
rat一个什么呢

94
00:02:51,060 --> 00:02:52,960
我们按照我们刚才的这个需求

95
00:02:52,960 --> 00:02:53,860
我们刚才这个要求

96
00:02:53,860 --> 00:02:54,980
我们来看一下

97
00:02:54,980 --> 00:02:57,000
我们要发送一个这样的数据格式

98
00:02:57,000 --> 00:02:57,780
Type是login

99
00:02:57,780 --> 00:03:00,380
Nicname就是当前用户输入的这个消息

100
00:03:00,380 --> 00:03:01,520
所以说这个时候呢

101
00:03:01,520 --> 00:03:02,800
我们在这儿就要去注意

102
00:03:02,800 --> 00:03:04,960
我们不能在这儿直接发送这个对象

103
00:03:04,960 --> 00:03:06,400
因为这个write的方法呢

104
00:03:06,400 --> 00:03:07,480
它要么发送二进制

105
00:03:07,480 --> 00:03:08,400
要么发送字符串

106
00:03:08,400 --> 00:03:10,040
对象是没办法发送的

107
00:03:10,040 --> 00:03:11,600
所以说我们在这里要把这个对象呢

108
00:03:11,600 --> 00:03:12,760
去给它转成字符串

109
00:03:12,760 --> 00:03:13,800
再来发送

110
00:03:13,800 --> 00:03:15,060
所以说我们在这儿应该是

111
00:03:15,060 --> 00:03:17,060
json.stream5

112
00:03:17,060 --> 00:03:18,320
然后传一个对象

113
00:03:18,320 --> 00:03:21,320
然后这个type呢就是login

114
00:03:21,320 --> 00:03:21,960
当然注意

115
00:03:21,960 --> 00:03:23,560
我们不要在这里直接手写这个login

116
00:03:23,560 --> 00:03:25,600
因为我们后面改的话会非常的麻烦

117
00:03:25,600 --> 00:03:29,400
大家有注意到我在这里定义的这个types这个类型

118
00:03:29,400 --> 00:03:31,300
所以说加来我们不在这这样来写

119
00:03:31,300 --> 00:03:34,440
我们这个时候直接加载一下我们的types

120
00:03:34,440 --> 00:03:37,140
把这个types给它拿过来

121
00:03:37,140 --> 00:03:37,880
拿过来以后

122
00:03:37,880 --> 00:03:39,960
那么这个时候我们家就变成了types.login

123
00:03:39,960 --> 00:03:41,960
实际上大家都知道

124
00:03:41,960 --> 00:03:43,920
这个types.login它的指示多少呢

125
00:03:43,920 --> 00:03:44,460
它就只是0

126
00:03:44,460 --> 00:03:46,440
这就是因为我不在这

127
00:03:46,440 --> 00:03:48,160
我不去直接写0123

128
00:03:48,160 --> 00:03:51,140
因为0123我们这个代码的阅读和这个维护

129
00:03:51,140 --> 00:03:52,120
就是不好

130
00:03:52,120 --> 00:03:54,580
那如果说我们把它加上一个名字

131
00:03:54,580 --> 00:03:55,800
说吧就是给这个0

132
00:03:55,800 --> 00:03:56,680
给这个1给这个2

133
00:03:56,680 --> 00:03:57,840
就是起了不同的名字

134
00:03:57,840 --> 00:03:59,100
0的名字就是Lobby

135
00:03:59,100 --> 00:04:00,480
那这样的话

136
00:04:00,480 --> 00:04:01,780
我们就使用一下就很方便

137
00:04:01,780 --> 00:04:02,880
好

138
00:04:02,880 --> 00:04:04,740
然后接下来Nikname

139
00:04:04,740 --> 00:04:07,220
就是我本次要登录所使用的昵称

140
00:04:07,220 --> 00:04:08,580
那么就是谁呢

141
00:04:08,580 --> 00:04:09,140
就是这个data

142
00:04:09,140 --> 00:04:10,360
那么这样的话

143
00:04:10,360 --> 00:04:13,340
此时我们就把这个消息

144
00:04:13,340 --> 00:04:14,580
发送给到我们这个服务端

145
00:04:14,580 --> 00:04:17,260
那接下来我们在这就可以来测试一下

146
00:04:17,260 --> 00:04:17,960
我们来测试一下

147
00:04:17,960 --> 00:04:19,780
然后对于我们这个服务端来讲

148
00:04:19,780 --> 00:04:21,400
当然我们服务端也得先处理一下

149
00:04:21,400 --> 00:04:22,400
要不然也不太好使用

150
00:04:22,400 --> 00:04:24,300
所以说我们在这把这个逻辑

151
00:04:24,300 --> 00:04:26,440
注意我们刚才都只是用于测试

152
00:04:26,440 --> 00:04:28,100
所以这个时候我在这就把这个逻辑

153
00:04:28,100 --> 00:04:30,460
都给他注意都直接删除掉

154
00:04:30,460 --> 00:04:31,320
我们在这不需要

155
00:04:31,320 --> 00:04:33,580
我们服务端在这里要做什么

156
00:04:33,580 --> 00:04:34,600
包括这里也不需要

157
00:04:34,600 --> 00:04:36,020
我们先不存

158
00:04:36,020 --> 00:04:38,460
因为我们有了用户这个概念

159
00:04:38,460 --> 00:04:40,760
同时我们让他也换一个名字叫users

160
00:04:40,760 --> 00:04:41,980
这样看起来更直观一些

161
00:04:41,980 --> 00:04:44,680
然后接下来服务端收到这个data以后

162
00:04:44,680 --> 00:04:45,940
服务端就要根据什么

163
00:04:45,940 --> 00:04:49,400
那客户端有不同的这个消息的类型

164
00:04:49,400 --> 00:04:50,400
有login

165
00:04:50,400 --> 00:04:51,400
有broadcast

166
00:04:51,400 --> 00:04:52,400
还有什么p2p

167
00:04:52,400 --> 00:04:54,400
所以说服务端要判断

168
00:04:54,400 --> 00:04:55,400
判断这个不同的type

169
00:04:55,400 --> 00:04:57,400
所以说接下来我们在这里呢

170
00:04:57,400 --> 00:05:00,400
就要去把这个types

171
00:05:00,400 --> 00:05:01,400
去给它加载进来

172
00:05:01,400 --> 00:05:02,400
把它加载进来以后

173
00:05:02,400 --> 00:05:05,400
然后服务端在这就可以去匹配判断了

174
00:05:05,400 --> 00:05:06,400
怎么判断呢

175
00:05:06,400 --> 00:05:07,400
这里也非常的简单

176
00:05:07,400 --> 00:05:10,400
首先我们把这个data去toolstream

177
00:05:10,400 --> 00:05:13,400
然后顺便trim一下

178
00:05:13,400 --> 00:05:15,400
因为我们都知道它是一个对象

179
00:05:15,400 --> 00:05:17,400
所以说我们在这一次

180
00:05:19,400 --> 00:05:22,680
好 这里去把它转成一个对象

181
00:05:22,680 --> 00:05:24,180
好 转这个对象以后呢

182
00:05:24,180 --> 00:05:25,940
我们这样来匹配

183
00:05:25,940 --> 00:05:26,720
匹配什么呢

184
00:05:26,720 --> 00:05:27,380
Data的Type

185
00:05:27,380 --> 00:05:28,620
就是这个数据的类型

186
00:05:28,620 --> 00:05:30,520
好 那么这个时候我们大家就可以去

187
00:05:30,520 --> 00:05:32,580
CaseTapes.Lobin

188
00:05:32,580 --> 00:05:33,960
Brick

189
00:05:33,960 --> 00:05:37,520
CaseTapes.Broadcast

190
00:05:37,520 --> 00:05:38,560
Brick

191
00:05:38,560 --> 00:05:41,140
好 然后CaseTapes.

192
00:05:41,140 --> 00:05:43,600
然后是它的这个叫做P2P

193
00:05:43,600 --> 00:05:44,860
我们可以来看一下

194
00:05:44,860 --> 00:05:45,980
我们这里定义一个P2P

195
00:05:45,980 --> 00:05:47,140
就是Point2Point

196
00:05:47,140 --> 00:05:47,800
就是资料

197
00:05:47,800 --> 00:05:48,540
Brick

198
00:05:48,540 --> 00:05:49,760
default

199
00:05:49,760 --> 00:05:54,560
那么接下来

200
00:05:54,560 --> 00:05:56,460
这个时候我们这个代码格式呢就很清晰

201
00:05:56,460 --> 00:05:59,100
就是说如果客户端发送的是登录的消息

202
00:05:59,100 --> 00:06:00,300
我大致做登录处理

203
00:06:00,300 --> 00:06:02,380
如果是Birdcast的我就做群聊的处理

204
00:06:02,380 --> 00:06:03,860
P2P我就做私聊的处理

205
00:06:03,860 --> 00:06:05,140
就是非常清晰

206
00:06:05,140 --> 00:06:08,320
那接下来我们对这个登录来做一个简单的处理

207
00:06:08,320 --> 00:06:09,560
那么这个登录的处理呢

208
00:06:09,560 --> 00:06:11,000
这个逻辑呢也非常的简单

209
00:06:11,000 --> 00:06:14,460
那这里的话我们就要来判断了

210
00:06:14,460 --> 00:06:15,440
所以说如果

211
00:06:15,440 --> 00:06:16,660
如果什么呢

212
00:06:16,660 --> 00:06:18,380
如果这个users

213
00:06:18,380 --> 00:06:21,560
那Uders是个数组

214
00:06:21,560 --> 00:06:23,200
数组里面我们将来要让它存什么呢

215
00:06:23,200 --> 00:06:24,500
这个数组里面要让它存

216
00:06:24,500 --> 00:06:26,440
就是一个一个的socket

217
00:06:26,440 --> 00:06:29,460
也就是一个一个的客户端

218
00:06:29,460 --> 00:06:32,500
所以说我们在这就是这样来判断

219
00:06:32,500 --> 00:06:33,220
item

220
00:06:33,220 --> 00:06:34,320
item的

221
00:06:34,320 --> 00:06:35,240
nickname

222
00:06:35,240 --> 00:06:36,100
item nickname

223
00:06:36,100 --> 00:06:37,280
等于什么呢

224
00:06:37,280 --> 00:06:40,580
等于我们当前这个客户端提交的这个nickname

225
00:06:40,580 --> 00:06:41,760
好

226
00:06:41,760 --> 00:06:43,040
那这一段的含义就是说

227
00:06:43,040 --> 00:06:44,000
如果能find的

228
00:06:44,000 --> 00:06:45,340
就是说如果能找到

229
00:06:45,340 --> 00:06:46,240
如果能找到

230
00:06:46,240 --> 00:06:48,080
就是当前这个客户端注册的

231
00:06:48,080 --> 00:06:50,540
这个倪称的这个优道像

232
00:06:50,540 --> 00:06:51,160
那就证明什么呢

233
00:06:51,160 --> 00:06:53,120
证明这个倪称已经重复了

234
00:06:53,120 --> 00:06:54,500
证明他已经重复了

235
00:06:54,500 --> 00:06:56,420
如果说已经重复了

236
00:06:56,420 --> 00:06:57,320
那么这个时候我们就return

237
00:06:57,320 --> 00:06:58,700
阻止代码后续执行

238
00:06:58,700 --> 00:06:59,200
然后什么招呢

239
00:06:59,200 --> 00:07:00,060
然后给啊

240
00:07:00,060 --> 00:07:03,800
我们通过这个clientSocket.root

241
00:07:03,800 --> 00:07:05,720
同样的我们再来给他回一个消息

242
00:07:05,720 --> 00:07:06,460
那么回消息的话

243
00:07:06,460 --> 00:07:09,420
当然也是json.screenify

244
00:07:09,420 --> 00:07:10,280
我们给他闯一个对象

245
00:07:10,280 --> 00:07:11,640
当然对象没法直接发

246
00:07:11,640 --> 00:07:13,220
所以说要把它转成字符串

247
00:07:13,220 --> 00:07:13,800
那么这里的话

248
00:07:13,800 --> 00:07:15,560
同样的这个tap

249
00:07:15,560 --> 00:07:17,840
此时我给你想的消息的类型是什么呢

250
00:07:17,840 --> 00:07:19,400
还是test.lobbing

251
00:07:19,400 --> 00:07:20,240
好

252
00:07:20,240 --> 00:07:22,620
然后我们可以给他一个message

253
00:07:22,620 --> 00:07:24,800
这个就是说完了

254
00:07:24,800 --> 00:07:25,460
就是这个叫

255
00:07:25,460 --> 00:07:27,520
就是该用户已存在

256
00:07:27,520 --> 00:07:28,880
就是该逆称已重复

257
00:07:28,880 --> 00:07:31,660
逆称已重复

258
00:07:31,660 --> 00:07:32,880
就是逆称已经有了

259
00:07:32,880 --> 00:07:33,320
好了

260
00:07:33,320 --> 00:07:34,560
那这个

261
00:07:34,560 --> 00:07:35,640
我们在这

262
00:07:35,640 --> 00:07:36,500
这个消息呢

263
00:07:36,500 --> 00:07:37,760
就给他去提示回来了

264
00:07:37,760 --> 00:07:38,320
提示回来了

265
00:07:38,320 --> 00:07:39,420
那如果说

266
00:07:39,420 --> 00:07:40,940
我们在这找不到

267
00:07:40,940 --> 00:07:42,460
就是说这个users里面

268
00:07:42,460 --> 00:07:43,140
没有这个用户

269
00:07:43,140 --> 00:07:44,300
没有这个用户的话

270
00:07:44,300 --> 00:07:45,340
那我们在这里就要去

271
00:07:45,340 --> 00:07:46,220
client forget

272
00:07:46,220 --> 00:07:48,520
把当前这个Socket的客户端

273
00:07:48,520 --> 00:07:49,760
反正它是个对象

274
00:07:49,760 --> 00:07:51,700
我们待会就给它添加一个自定义成员

275
00:07:51,700 --> 00:07:52,200
就是Nikname

276
00:07:52,200 --> 00:07:53,320
等于什么呢

277
00:07:53,320 --> 00:07:54,840
等于Data里面的这个Nikname

278
00:07:54,840 --> 00:07:57,060
然后添加完以后呢

279
00:07:57,060 --> 00:07:58,200
加来我们让这个Users

280
00:07:58,200 --> 00:07:59,240
Push

281
00:07:59,240 --> 00:08:00,480
Push什么呢

282
00:08:00,480 --> 00:08:01,480
Push这个叫ClientSocket

283
00:08:01,480 --> 00:08:03,620
我们把这个当前的客户端

284
00:08:03,620 --> 00:08:05,640
添加到这个Users数字里面了

285
00:08:05,640 --> 00:08:06,400
添加完以后

286
00:08:06,400 --> 00:08:07,860
我们还要干一件事

287
00:08:07,860 --> 00:08:09,480
你得告诉当前这个

288
00:08:09,480 --> 00:08:10,540
请就登陆的这个客户端

289
00:08:10,540 --> 00:08:12,720
告诉他你本次这个昵称可以使用

290
00:08:12,720 --> 00:08:14,040
然后是不是登陆成功列

291
00:08:14,040 --> 00:08:15,400
当然我们这儿呢

292
00:08:15,400 --> 00:08:16,000
别忘了一件事

293
00:08:16,000 --> 00:08:17,520
success

294
00:08:17,520 --> 00:08:18,460
vtrue

295
00:08:18,460 --> 00:08:19,500
vforce

296
00:08:19,500 --> 00:08:20,760
表示这个登录失败

297
00:08:20,760 --> 00:08:22,200
好 然后这个时候呢

298
00:08:22,200 --> 00:08:25,020
我们在这就去clientsocket.write

299
00:08:25,020 --> 00:08:26,240
write什么呢

300
00:08:26,240 --> 00:08:27,740
同样的json.screen file

301
00:08:27,740 --> 00:08:29,800
好 那么我们在这里的话呢

302
00:08:29,800 --> 00:08:33,500
首先这个type还是这个types.lobbing

303
00:08:33,500 --> 00:08:35,060
好 然后success

304
00:08:35,060 --> 00:08:37,160
那么此时就是true

305
00:08:37,160 --> 00:08:38,560
表示成功了

306
00:08:38,560 --> 00:08:42,440
好 这个message就是这个叫登录成功

307
00:08:42,440 --> 00:08:43,720
好 然后同样的

308
00:08:43,720 --> 00:08:44,660
我们再给他发一个东西

309
00:08:44,660 --> 00:08:45,400
我们再给他发一个

310
00:08:45,400 --> 00:08:47,500
就是说当前有多少个用户在线

311
00:08:47,500 --> 00:08:48,680
那我们这里可以去

312
00:08:48,680 --> 00:08:51,280
其实就是这个

313
00:08:51,280 --> 00:08:52,640
users的length

314
00:08:52,640 --> 00:08:54,200
就是它的长度

315
00:08:54,200 --> 00:08:55,920
好那这样写完以后

316
00:08:55,920 --> 00:08:57,360
就表示服务端打消息的去

317
00:08:57,360 --> 00:08:57,960
发过来了

318
00:08:57,960 --> 00:08:58,560
发过来了

319
00:08:58,560 --> 00:08:59,640
发过来以后

320
00:08:59,640 --> 00:09:00,700
这个时候我们可以在客户端

321
00:09:00,700 --> 00:09:01,720
我们只是在这呢

322
00:09:01,720 --> 00:09:02,920
去get into stream

323
00:09:02,920 --> 00:09:04,100
去输出来一下

324
00:09:04,100 --> 00:09:04,840
当前服务端

325
00:09:04,840 --> 00:09:05,860
就是发了什么消息

326
00:09:05,860 --> 00:09:07,660
那客户端要做什么处理

327
00:09:07,660 --> 00:09:08,780
那就是下一步的事了

328
00:09:08,780 --> 00:09:10,040
然后将来这个时候

329
00:09:10,040 --> 00:09:11,740
我们可以来测试一下

330
00:09:11,740 --> 00:09:12,700
测试一下

331
00:09:12,700 --> 00:09:13,700
然后打开控制台

332
00:09:13,700 --> 00:09:14,900
好

333
00:09:14,900 --> 00:09:15,900
左边是我们的服务端

334
00:09:15,900 --> 00:09:16,900
右边是客户端

335
00:09:16,900 --> 00:09:19,100
我在这里使用这个node

336
00:09:19,100 --> 00:09:20,700
然后这个glant

337
00:09:20,700 --> 00:09:21,900
我们来执行一下啊

338
00:09:21,900 --> 00:09:23,900
那这里提示就是说请输入你称

339
00:09:23,900 --> 00:09:25,900
这里怎么直接输出hello呢

340
00:09:25,900 --> 00:09:26,900
我来看一下啊

341
00:09:26,900 --> 00:09:28,900
请输入你称

342
00:09:28,900 --> 00:09:30,900
然后加了这个data

343
00:09:30,900 --> 00:09:34,400
我们这里是这样的啊

344
00:09:34,400 --> 00:09:36,400
我们服务端在这发一个消息过来

345
00:09:36,400 --> 00:09:38,400
然后客户端在这输出了服务端的消息啊

346
00:09:38,400 --> 00:09:39,900
我们这句话实际上在这已经不需要了啊

347
00:09:39,900 --> 00:09:40,900
把他删掉

348
00:09:40,900 --> 00:09:42,900
然后我们再来啊

349
00:09:43,900 --> 00:09:44,940
好 请输入捏称

350
00:09:44,940 --> 00:09:45,820
我们取个例子啊

351
00:09:45,820 --> 00:09:46,780
他输入Jack回车

352
00:09:46,780 --> 00:09:47,860
我们在这可以看到

353
00:09:47,860 --> 00:09:49,060
type 0 success为true

354
00:09:49,060 --> 00:09:50,100
message登陆成功

355
00:09:50,100 --> 00:09:51,660
some users 1

356
00:09:51,660 --> 00:09:53,700
那是不是这个用户在这就登陆成功了呀

357
00:09:53,700 --> 00:09:56,580
好 这的话我们再来开启一个客户端

358
00:09:56,580 --> 00:09:57,780
node client

359
00:09:57,780 --> 00:10:00,020
那这个时候我们让他再输入Jack回车

360
00:10:00,020 --> 00:10:01,620
然后在这我们就可以看到

361
00:10:01,620 --> 00:10:02,940
type是0 success是false

362
00:10:02,940 --> 00:10:04,180
message捏称以重复

363
00:10:04,180 --> 00:10:05,500
那这个时候我们

364
00:10:05,500 --> 00:10:07,700
在客户端直接输出这个

365
00:10:07,700 --> 00:10:09,220
服务端想要的原生的数据

366
00:10:09,220 --> 00:10:10,100
是不是不太合适

367
00:10:10,100 --> 00:10:12,180
我们客户端是不是最好判断一下

368
00:10:12,180 --> 00:10:15,080
如果这个type是这个login

369
00:10:15,080 --> 00:10:17,000
也就是说这个type为0的这个想象消息

370
00:10:17,000 --> 00:10:18,280
然后并且这个success

371
00:10:18,280 --> 00:10:19,220
例如是true的时候怎么着

372
00:10:19,220 --> 00:10:20,240
是false的时候怎么着

373
00:10:20,240 --> 00:10:20,940
对吧

374
00:10:20,940 --> 00:10:22,000
如果说这个false了

375
00:10:22,000 --> 00:10:23,040
那我们就再提示用户

376
00:10:23,040 --> 00:10:23,680
请输入你程

377
00:10:23,680 --> 00:10:25,320
如果说true成功了

378
00:10:25,320 --> 00:10:26,300
那我们就提示用户

379
00:10:26,300 --> 00:10:28,460
例如欢迎进入聊天室

380
00:10:28,460 --> 00:10:30,180
当前在线用户有多少多少个

381
00:10:30,180 --> 00:10:31,160
好

382
00:10:31,160 --> 00:10:33,380
然后接下来我们再就可以快速来实现一下

383
00:10:33,380 --> 00:10:34,780
这个就表示我们客户端

384
00:10:34,780 --> 00:10:36,400
收到了服务端的消息

385
00:10:36,400 --> 00:10:37,560
然后接下来同样的

386
00:10:37,560 --> 00:10:39,200
我们让这个date it to stream

387
00:10:39,200 --> 00:10:41,120
我们让它trim一下

388
00:10:41,120 --> 00:10:46,280
然后接下来我们把它去json.parse一下

389
00:10:46,280 --> 00:10:48,680
然后接下来我们在这 switch

390
00:10:48,680 --> 00:10:50,820
好,date.type

391
00:10:50,820 --> 00:10:53,640
那么这个时候我们还是一样的

392
00:10:53,640 --> 00:10:57,140
分别是case.taps.login

393
00:10:57,140 --> 00:11:03,640
word.type.taps.wordcast

394
00:11:03,640 --> 00:11:07,020
case.taps.p2p

395
00:11:07,020 --> 00:11:12,020
CaseRogerDefault

396
00:11:12,020 --> 00:11:15,720
当然我们可以在这里的输出

397
00:11:15,720 --> 00:11:17,100
这个叫未知的

398
00:11:17,100 --> 00:11:19,360
未知的消息类型

399
00:11:19,360 --> 00:11:20,660
就是说我无法识别

400
00:11:20,660 --> 00:11:21,880
然后接下来

401
00:11:21,880 --> 00:11:23,500
当我们匹配到这个Login的时候

402
00:11:23,500 --> 00:11:24,720
然后我们在这里

403
00:11:24,720 --> 00:11:26,540
我们在这里就可以去进行判断

404
00:11:26,540 --> 00:11:27,420
判断什么呢

405
00:11:27,420 --> 00:11:28,760
如果DateSuccess

406
00:11:28,760 --> 00:11:31,220
是不是表示这个登录成功率

407
00:11:31,220 --> 00:11:33,340
如果说它登录成功

408
00:11:33,340 --> 00:11:35,040
当然我们也可以先飞一下

409
00:11:35,040 --> 00:11:35,780
我们先飞一下

410
00:11:35,780 --> 00:11:37,380
飞侠表示就是说他没有

411
00:11:37,380 --> 00:11:38,120
都登录失败了

412
00:11:38,120 --> 00:11:39,940
那这个时候我们记得去return

413
00:11:39,940 --> 00:11:40,900
我们先这样

414
00:11:40,900 --> 00:11:41,740
先提示一下

415
00:11:41,740 --> 00:11:43,740
这个叫登录失败

416
00:11:43,740 --> 00:11:45,500
失败的消息是什么呢

417
00:11:45,500 --> 00:11:47,500
我们可以这样让他拼接上

418
00:11:47,500 --> 00:11:49,260
辅端响的那个消息

419
00:11:49,260 --> 00:11:51,100
也就是data里面的这个message

420
00:11:51,100 --> 00:11:51,940
完了以后呢

421
00:11:51,940 --> 00:11:53,120
我们要再提示用户

422
00:11:53,120 --> 00:11:56,560
process.stdout.rut

423
00:11:56,560 --> 00:11:59,380
请输入秘程

424
00:11:59,380 --> 00:12:02,200
就是说你再次的去进行这个注册

425
00:12:02,200 --> 00:12:02,880
好了

426
00:12:02,880 --> 00:12:04,600
然后没有问题之后

427
00:12:04,600 --> 00:12:05,300
然后就是off

428
00:12:05,300 --> 00:12:08,520
比如说这个是登录失败的这个处理

429
00:12:08,520 --> 00:12:10,580
下面这个就是登录成功的一个处理

430
00:12:10,580 --> 00:12:11,720
登录成功以后

431
00:12:11,720 --> 00:12:12,900
我们在这儿要干嘛

432
00:12:12,900 --> 00:12:15,080
所以说我们在这儿直接提示一下

433
00:12:15,080 --> 00:12:17,100
这个叫登录成功

434
00:12:17,100 --> 00:12:19,720
当前在线用户

435
00:12:19,720 --> 00:12:20,760
有多少个呢

436
00:12:20,760 --> 00:12:22,740
当前在线用户有多少呢

437
00:12:22,740 --> 00:12:24,220
我们大家可以拼上这个

438
00:12:24,220 --> 00:12:25,100
当然反映号

439
00:12:25,100 --> 00:12:28,720
反映号

440
00:12:28,720 --> 00:12:33,280
然后这个就是date.sum users

441
00:12:33,280 --> 00:12:34,180
date.sum users

442
00:12:34,180 --> 00:12:35,920
当中成功以后

443
00:12:35,920 --> 00:12:37,040
当然我们别忘了一件事

444
00:12:37,040 --> 00:12:37,840
那么此时的话

445
00:12:37,840 --> 00:12:39,620
是不是表示客户端的用户有昵称链

446
00:12:39,620 --> 00:12:41,040
有了昵称以后

447
00:12:41,040 --> 00:12:41,520
那么这个时候

448
00:12:41,520 --> 00:12:42,620
我们就把这个nickname

449
00:12:42,620 --> 00:12:43,380
去给它付一个值

450
00:12:43,380 --> 00:12:44,580
付好值以后

451
00:12:44,580 --> 00:12:45,840
然后下一步用户输入的数据

452
00:12:45,840 --> 00:12:46,760
由于它有了昵称

453
00:12:46,760 --> 00:12:48,800
那么就变成这个聊天的数据了

454
00:12:48,800 --> 00:12:50,820
而不再是这个昵称的消息

455
00:12:50,820 --> 00:12:51,740
所以说这个时候

456
00:12:51,740 --> 00:12:52,500
我们别忘了一件事

457
00:12:52,500 --> 00:12:53,560
然后让这个nickname

458
00:12:53,560 --> 00:12:55,060
我们让它有值

459
00:12:55,060 --> 00:12:56,400
有值就可以了

460
00:12:56,400 --> 00:12:57,200
当然

461
00:12:57,200 --> 00:12:58,920
我们可以这样

462
00:12:58,920 --> 00:13:00,820
当然我们在这并不知道

463
00:13:00,820 --> 00:13:01,440
它昵称是什么

464
00:13:01,440 --> 00:13:02,440
我们可以让服务端

465
00:13:02,440 --> 00:13:04,000
去给它响应回来啊

466
00:13:04,000 --> 00:13:04,400
nickname

467
00:13:04,400 --> 00:13:06,300
date the nickname

468
00:13:06,300 --> 00:13:07,040
好了

469
00:13:07,040 --> 00:13:08,560
然后这个date the nickname

470
00:13:08,560 --> 00:13:10,320
这样的话表示这个客户端

471
00:13:10,320 --> 00:13:11,320
就有了尼称了啊

472
00:13:11,320 --> 00:13:12,180
下一步输入的消息呢

473
00:13:12,180 --> 00:13:13,860
就都是这个聊天的消息了

474
00:13:13,860 --> 00:13:14,020
好了

475
00:13:14,020 --> 00:13:15,120
然后接下来这个时候呢

476
00:13:15,120 --> 00:13:16,480
我们再来测试一下啊

477
00:13:16,480 --> 00:13:17,300
然后打开这个控制台

478
00:13:17,300 --> 00:13:20,080
这个时候我们再来看一下啊

479
00:13:20,080 --> 00:13:21,400
我们把客户端都先关掉

480
00:13:21,400 --> 00:13:23,980
我们再都进来啊

481
00:13:23,980 --> 00:13:24,860
请输入尼称啊

482
00:13:24,860 --> 00:13:25,880
来比如这个叫Jack

483
00:13:25,880 --> 00:13:26,680
登陆成功

484
00:13:26,680 --> 00:13:27,640
当前价钱用户为一

485
00:13:27,640 --> 00:13:28,880
好我们这里来一个啊

486
00:13:28,880 --> 00:13:29,300
请输入尼称

487
00:13:29,300 --> 00:13:30,680
我们再来输入这个Jack啊

488
00:13:30,680 --> 00:13:30,960
Jack

489
00:13:30,960 --> 00:13:31,600
好了

490
00:13:31,600 --> 00:13:31,960
登陆失败

491
00:13:31,960 --> 00:13:32,860
你一乘以重复

492
00:13:32,860 --> 00:13:33,920
你驾驾输出驾驾

493
00:13:33,920 --> 00:13:34,700
是不是不行啊

494
00:13:34,700 --> 00:13:34,900
好

495
00:13:34,900 --> 00:13:35,860
所以你在这换一个啊

496
00:13:35,860 --> 00:13:37,160
比如我带着来一个Mac

497
00:13:37,160 --> 00:13:38,300
东东成功

498
00:13:38,300 --> 00:13:40,160
当前载性用户为二

499
00:13:40,160 --> 00:13:41,320
那将来你再输入的消息

500
00:13:41,320 --> 00:13:42,460
比如输入123一回车

501
00:13:42,460 --> 00:13:43,860
那么这里什么反应也没有

502
00:13:43,860 --> 00:13:45,060
因为此时的消息呢

503
00:13:45,060 --> 00:13:46,520
应该是让它变成这个

504
00:13:46,520 --> 00:13:47,760
就是聊天的消息

505
00:13:47,760 --> 00:13:49,260
就是说我们把123

506
00:13:49,260 --> 00:13:51,900
去发到一个就是其他的客户办法

507
00:13:51,900 --> 00:13:53,260
然后可能在这就会输出

508
00:13:53,260 --> 00:13:55,560
输出这样一个就是Mac123

509
00:13:55,560 --> 00:13:55,760
对吧

510
00:13:55,760 --> 00:13:56,760
就表示Mac说了一句话

511
00:13:56,760 --> 00:13:58,860
123就是这个意思

512
00:13:58,860 --> 00:13:59,660
好

513
00:13:59,660 --> 00:14:00,660
那么此时啊

514
00:14:00,660 --> 00:14:01,100
此时的话

515
00:14:01,100 --> 00:14:02,560
我们这个就是聊天室

516
00:14:02,560 --> 00:14:04,880
它的这个登录的功能

517
00:14:04,880 --> 00:14:06,620
在这我们就给它完成了

518
00:14:06,620 --> 00:14:07,420
就说白了

519
00:14:07,420 --> 00:14:08,920
让它注册一个昵称

520
00:14:08,920 --> 00:14:09,680
就是不重复

