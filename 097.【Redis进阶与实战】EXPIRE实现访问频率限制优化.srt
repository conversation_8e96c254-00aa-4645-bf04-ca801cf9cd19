1
00:00:00,000 --> 00:00:00,820
好我们继续

2
00:00:00,820 --> 00:00:04,400
刚才是不是我们通过散列去实现了一个什么呀

3
00:00:04,400 --> 00:00:06,220
是不是实现了我们的访问频率的限制

4
00:00:06,220 --> 00:00:08,440
其实呢他还有一个比较严重的问题

5
00:00:08,440 --> 00:00:09,680
也是一个比较大的漏洞

6
00:00:09,680 --> 00:00:11,720
你比如说如果说我们一个用户

7
00:00:11,720 --> 00:00:14,700
在一分钟的第一次访问的一个博客

8
00:00:14,700 --> 00:00:17,060
那么他在第59秒访问的9次

9
00:00:17,060 --> 00:00:21,100
他呢又在第二分钟的第一秒访问的10次

10
00:00:21,100 --> 00:00:24,180
那么我们实际上在两秒钟之内是不是访问了19次

11
00:00:24,180 --> 00:00:26,560
大家可以思考一下这个地方

12
00:00:26,560 --> 00:00:28,720
是不是这么回事

13
00:00:28,720 --> 00:00:30,440
那么我们怎么样去解决这样一个问题

14
00:00:30,440 --> 00:00:32,880
这样是不是我们的控制也会有问题

15
00:00:32,880 --> 00:00:34,520
你明明希望的是一分钟

16
00:00:34,520 --> 00:00:36,200
只能限制他访问十次

17
00:00:36,200 --> 00:00:37,680
但是他这样的一个漏洞

18
00:00:37,680 --> 00:00:39,180
让他两秒钟之内

19
00:00:39,180 --> 00:00:40,100
实际上访问了十九次

20
00:00:40,100 --> 00:00:40,580
怎么办

21
00:00:40,580 --> 00:00:41,780
我们是不是又要去改造

22
00:00:41,780 --> 00:00:43,020
那么我们来看一下

23
00:00:43,020 --> 00:00:44,080
可以怎么样去改造

24
00:00:44,080 --> 00:00:45,360
可以去避免这样一个问题

25
00:00:45,360 --> 00:00:47,680
实际上我们可以把我们的闪电结构

26
00:00:47,680 --> 00:00:48,840
改造成为列表

27
00:00:48,840 --> 00:00:50,540
怎么样去改呢

28
00:00:50,540 --> 00:00:51,880
你比如说我们同样的

29
00:00:51,880 --> 00:00:52,920
新建我们刚才在一个字板

30
00:00:52,920 --> 00:00:54,880
比如说我们每一个用户

31
00:00:54,880 --> 00:00:55,900
存在一个这样的列表字板

32
00:00:55,900 --> 00:00:57,960
然后每次访问的时候

33
00:00:57,960 --> 00:00:58,980
就在列表中存入

34
00:00:58,980 --> 00:01:01,140
存入我们当前的一个时间戳

35
00:01:01,140 --> 00:01:03,240
如果说列表小于10

36
00:01:03,240 --> 00:01:04,380
你可以不断的去存入

37
00:01:04,380 --> 00:01:05,920
但是一旦当你的列表

38
00:01:05,920 --> 00:01:08,100
大于了10或者等于10的时候

39
00:01:08,100 --> 00:01:10,080
我们需要把咱们列表里面的

40
00:01:10,080 --> 00:01:11,800
第一个时间戳和现在的时间去做对比

41
00:01:11,800 --> 00:01:13,440
如果说小于60秒

42
00:01:13,440 --> 00:01:14,300
则拒绝访问

43
00:01:14,300 --> 00:01:15,440
小于60秒是什么意思

44
00:01:15,440 --> 00:01:16,960
小于60秒是不是就是说

45
00:01:16,960 --> 00:01:18,280
你在60秒之内

46
00:01:18,280 --> 00:01:19,700
一分钟之内访问了超过10次了

47
00:01:19,700 --> 00:01:21,160
那么如果说大于60秒

48
00:01:21,160 --> 00:01:22,820
你是不是就可以继续往里面去存

49
00:01:22,820 --> 00:01:24,020
也就是说你可以去访问了

50
00:01:24,020 --> 00:01:24,620
好

51
00:01:24,620 --> 00:01:25,420
那么我们就来看一下

52
00:01:25,420 --> 00:01:27,400
我们可以怎么样去改造

53
00:01:27,400 --> 00:01:34,960
好

54
00:01:34,960 --> 00:01:36,800
首先

55
00:01:36,800 --> 00:01:38,260
我们刚才讲到了

56
00:01:38,260 --> 00:01:39,600
我们是不是需要去判断

57
00:01:39,600 --> 00:01:41,140
我们一个列表的一个长度

58
00:01:41,140 --> 00:01:44,980
列表的长度代表什么

59
00:01:44,980 --> 00:01:47,040
列表的长度是不是代表你的访问次数

60
00:01:47,040 --> 00:01:49,520
好

61
00:01:49,520 --> 00:01:50,480
我们首先来看一下

62
00:01:55,880 --> 00:02:00,480
List Lens 等于client点 咱们怎么样去获取一个列表的长度啊 什么命率是不是

63
00:02:00,480 --> 00:02:01,840
 l l e n 还要认

64
00:02:01,840 --> 00:02:03,720
获取什么呢

65
00:02:03,720 --> 00:02:05,180
write imit

66
00:02:05,180 --> 00:02:06,760
说了

67
00:02:06,760 --> 00:02:10,280
ip 好 这里呢 我们是不是就可以获取了我们

68
00:02:10,280 --> 00:02:12,840
获取了我们的一个列表的长度

69
00:02:12,840 --> 00:02:18,440
那么我们获取到长度之后是不是还会去写一个判断呢 什么判断呢 什么判断

70
00:02:18,440 --> 00:02:23,680
首先我们是不是要判断小于10的情况 小于10我们可以不管吗 直接往里面去push

71
00:02:23,680 --> 00:02:24,220
 为什么

72
00:02:24,600 --> 00:02:32,020
因为你访问次数都小于10那我们当然就可以访问了我们难解的情况是不是你超过10次之后我们就要去看一下你一分钟之内是不是访问了14对吧

73
00:02:32,020 --> 00:02:33,300
所以说呢如果说我们他

74
00:02:33,300 --> 00:02:35,600
小于10你什么都不用做

75
00:02:35,600 --> 00:02:39,200
你直接去干什么了你直接往里面去进行l push

76
00:02:39,200 --> 00:02:45,840
push他对吧也就是push到你你的你自己锁定ip的在一个字段里面去push的是什么呢

77
00:02:45,840 --> 00:02:50,460
是不是咱们push的时候在你的字段里面是不是要存储一个什么时间戳吧

78
00:02:50,460 --> 00:02:53,520
存储时间戳代表了你访问的时间

79
00:02:54,300 --> 00:02:57,500
比如说我们怎么去获取时间呢 我们可以通过lue date

80
00:02:57,500 --> 00:03:02,020
lue date 是不是我们s浏览器里面他的一个时间对象啊 怎么去获取时间戳

81
00:03:02,020 --> 00:03:04,860
 通过value off 对吧 好 这里呢 我们就

82
00:03:04,860 --> 00:03:11,320
在每次访问的时候都会去存储一个时间戳 以列表的形式 那么当当我们的长度大于10的时候怎么办

83
00:03:11,320 --> 00:03:17,300
当我们的长度大于10的时候 我们是不是需要去将我们列表中最开始的时间拿出来和我们现在的时间对比

84
00:03:17,300 --> 00:03:23,620
对吧 对比你到底有没有超过60秒 我们就来看一下 首先我们来看一下 怎么去获取

85
00:03:23,980 --> 00:03:26,040
你第一次访问的时间

86
00:03:26,040 --> 00:03:27,720
client点

87
00:03:27,720 --> 00:03:29,580
比如说我们要在

88
00:03:29,580 --> 00:03:30,520
我们要在

89
00:03:30,520 --> 00:03:31,740
write

90
00:03:31,740 --> 00:03:32,640
不好意思

91
00:03:32,640 --> 00:03:32,980
write

92
00:03:32,980 --> 00:03:34,040
我们要在write

93
00:03:34,040 --> 00:03:34,880
limit

94
00:03:34,880 --> 00:03:37,440
你自己对应的IP在一个列表里面寻找你

95
00:03:37,440 --> 00:03:38,980
第一次访问的时间怎么样去获取

96
00:03:38,980 --> 00:03:40,440
是不是可以通过我们的

97
00:03:40,440 --> 00:03:41,220
lindex

98
00:03:41,220 --> 00:03:44,480
也就是通过index去访问你列表中的某一个值

99
00:03:44,480 --> 00:03:45,080
好

100
00:03:45,080 --> 00:03:46,840
访问他

101
00:03:46,840 --> 00:03:48,020
那么怎么样去获取第一个值呢

102
00:03:48,020 --> 00:03:49,120
是不是-1

103
00:03:49,120 --> 00:03:49,680
为什么是-1

104
00:03:49,680 --> 00:03:50,380
2不是0

105
00:03:50,380 --> 00:03:51,520
因为我们是lpush

106
00:03:51,520 --> 00:03:52,520
如果你是rpush

107
00:03:52,520 --> 00:03:53,940
就可以通过0去访问吧

108
00:03:53,940 --> 00:03:55,940
这里想清楚的同学可以再

109
00:03:55,940 --> 00:03:56,940
思考一下

110
00:03:56,940 --> 00:03:59,940
好这里我们是不是就获取了咱们第一次访问时间

111
00:03:59,940 --> 00:04:01,940
获取了时间之后怎么办

112
00:04:01,940 --> 00:04:03,940
我们是不是在这里需要去做一个判断了

113
00:04:03,940 --> 00:04:05,940
和我们当前时间做对比吧

114
00:04:05,940 --> 00:04:07,940
你比如说我们用

115
00:04:07,940 --> 00:04:11,940
newbit.valueof

116
00:04:11,940 --> 00:04:13,940
是不是减去我们的time

117
00:04:13,940 --> 00:04:17,940
也就是说我们用当前时间

118
00:04:17,940 --> 00:04:19,940
当前时间

119
00:04:19,940 --> 00:04:21,940
减去

120
00:04:21,940 --> 00:04:25,420
第一次访问的时间

121
00:04:25,420 --> 00:04:28,920
和谁对比

122
00:04:28,920 --> 00:04:30,860
是不是小于60秒

123
00:04:30,860 --> 00:04:35,900
对吧

124
00:04:35,900 --> 00:04:37,160
不过大家要注意

125
00:04:37,160 --> 00:04:38,260
这里第一次访问的时间

126
00:04:38,260 --> 00:04:38,960
实际上并不是

127
00:04:38,960 --> 00:04:40,220
你比如说你昨天访问一次

128
00:04:40,220 --> 00:04:41,340
然后今天访问一次

129
00:04:41,340 --> 00:04:42,380
那么你这个第一次访问的时间

130
00:04:42,380 --> 00:04:43,440
是昨天的那个时间吗

131
00:04:43,440 --> 00:04:44,260
实际上不是的

132
00:04:44,260 --> 00:04:46,120
是不是我们应该是咱们的

133
00:04:46,120 --> 00:04:48,520
咱们这个时间应该是

134
00:04:48,520 --> 00:04:50,920
实际上第一次访问的时间

135
00:04:50,920 --> 00:04:59,360
我们准确的来说是不是应该是我们的right limit ip 这样一个列表里面最早的时间

136
00:04:59,360 --> 00:05:04,480
我们如果说它小于60秒 假如说假如他们相减小于60

137
00:05:04,480 --> 00:05:08,840
小廖时说明什么问题 是不是说明你60秒钟之内已经访问超过10次了

138
00:05:08,840 --> 00:05:10,380
 所以这个时候就不能让你访问吗

139
00:05:10,380 --> 00:05:12,160
client.x

140
00:05:12,160 --> 00:05:14,980
不能让你访问吗

141
00:05:18,320 --> 00:05:21,140
好 接下来我们来看一下 如果说可以访问怎么办

142
00:05:21,140 --> 00:05:23,700
如果说可以访问怎么办

143
00:05:23,700 --> 00:05:27,540
也就是他大于60情况 说明你的10次访问都是带

144
00:05:27,540 --> 00:05:30,600
大于60秒钟时间访问的吧 所以肯定可以让你去访问的

145
00:05:30,600 --> 00:05:33,940
那么我们每次访问的时候是不是同样的 我们也需要去进行lpush

146
00:05:33,940 --> 00:05:35,980
同样的去执行他吧

147
00:05:35,980 --> 00:05:39,560
执行他 好 我们lpush之后

148
00:05:39,560 --> 00:05:40,600
还需要做什么

149
00:05:40,600 --> 00:05:42,640
这样就结束了吗 同学看一下

150
00:05:42,640 --> 00:05:44,180
这样的代码

151
00:05:44,180 --> 00:05:46,480
可行吗 可以实现我们的需求吗

152
00:05:47,500 --> 00:05:48,520
不可以吧 大家发现没有

153
00:05:48,520 --> 00:05:50,580
大家发现没有

154
00:05:50,580 --> 00:05:52,360
如果说我每分钟访问一次

155
00:05:52,360 --> 00:05:53,140
可以吧

156
00:05:53,140 --> 00:05:56,200
但是你每分钟访问一次 我们的l push 他是不是很快就会超过10

157
00:05:56,200 --> 00:06:00,820
那你是不是又会像刚才一样 永远不能再访问了呀 和咱们刚才的一个过去时间不会被酸出一样

158
00:06:00,820 --> 00:06:01,580
你比如说

159
00:06:01,580 --> 00:06:03,620
你比如说我们每分钟访问一次

160
00:06:03,620 --> 00:06:05,160
每分钟访问一次的话

161
00:06:05,160 --> 00:06:09,520
我们是不是会永远走s 你比如说你访问了10次的时候 你永远会走s

162
00:06:09,520 --> 00:06:11,300
ls里面他是不是永远会小于

163
00:06:11,300 --> 00:06:14,120
永远不会小于60 那么他永远会去

164
00:06:14,120 --> 00:06:16,680
push 那么会push越来多越来多越来多

165
00:06:17,460 --> 00:06:18,540
那怎么办呢

166
00:06:18,540 --> 00:06:21,080
我们是不是需要去保证我们的

167
00:06:21,080 --> 00:06:22,580
L push在一个列表

168
00:06:22,580 --> 00:06:24,940
我们的write limit IP在一个列表它的长度

169
00:06:24,940 --> 00:06:26,080
永远不能够大于10了

170
00:06:26,080 --> 00:06:27,500
所以说我们通过一个命令

171
00:06:27,500 --> 00:06:28,120
怎么了

172
00:06:28,120 --> 00:06:29,620
L trim 同学们记得吗

173
00:06:29,620 --> 00:06:30,780
我们是不是每次

174
00:06:30,780 --> 00:06:32,300
你比如说你访问10次

175
00:06:32,300 --> 00:06:33,220
第11次访问的时候

176
00:06:33,220 --> 00:06:34,540
你需要把第11次给push进去

177
00:06:34,540 --> 00:06:36,080
然后呢 把第1次给拴掉吧

178
00:06:36,080 --> 00:06:36,620
好

179
00:06:36,620 --> 00:06:39,720
write limit

180
00:06:39,720 --> 00:06:42,160
多了 IP

181
00:06:42,160 --> 00:06:44,220
什么呢

182
00:06:44,220 --> 00:06:45,340
是不是0到9

183
00:06:45,340 --> 00:06:47,300
你永远需要去保证

184
00:06:47,300 --> 00:06:48,360
你的列表长度为10

185
00:06:48,360 --> 00:06:49,420
而且保留最新的

186
00:06:49,420 --> 00:06:50,980
把最成就的数据给删除掉

187
00:06:50,980 --> 00:06:52,580
这里就是我们通过去列表

188
00:06:52,580 --> 00:06:53,460
去改进我们的一个

189
00:06:53,460 --> 00:06:56,940
实现我们的访问量限制的一个过程

190
00:06:56,940 --> 00:06:58,960
那么我们来重新回顾一下

191
00:06:58,960 --> 00:07:00,960
首先我们是不是会用一个列表去存储

192
00:07:00,960 --> 00:07:01,880
我们每次访问

193
00:07:01,880 --> 00:07:02,440
存储什么

194
00:07:02,440 --> 00:07:03,240
是不是存储时间戳

195
00:07:03,240 --> 00:07:05,500
那么时间戳存储完之后

196
00:07:05,500 --> 00:07:06,440
首先会有一个判断

197
00:07:06,440 --> 00:07:07,420
如果说小于10

198
00:07:07,420 --> 00:07:08,780
小于10是不是可以随便访问

199
00:07:08,780 --> 00:07:09,480
那么如果大于10

200
00:07:09,480 --> 00:07:10,900
我们是不是要把当前时间

201
00:07:10,900 --> 00:07:13,080
和我们列表里面最大时间去

202
00:07:13,080 --> 00:07:14,060
做一个比较

203
00:07:14,060 --> 00:07:15,360
如果说他们时间差小于60

204
00:07:15,360 --> 00:07:17,000
说明你在60秒钟之内

205
00:07:17,000 --> 00:07:18,040
是不是访问超过了十次

206
00:07:18,040 --> 00:07:18,880
所以你不能访问

207
00:07:18,880 --> 00:07:20,560
同样的

208
00:07:20,560 --> 00:07:21,380
如果说大于六十

209
00:07:21,380 --> 00:07:23,000
你就可以去访问吧

210
00:07:23,000 --> 00:07:23,780
而且我们需要去

211
00:07:23,780 --> 00:07:24,660
在Reddit里面去布袭

212
00:07:24,660 --> 00:07:25,320
一个新的时间戳

213
00:07:25,320 --> 00:07:26,520
然后把旧的给酸掉

214
00:07:26,520 --> 00:07:26,900
好

215
00:07:26,900 --> 00:07:28,840
这里就是我们这节课的内容

