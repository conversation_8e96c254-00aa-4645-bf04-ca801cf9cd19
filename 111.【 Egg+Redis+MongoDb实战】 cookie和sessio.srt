1
00:00:00,000 --> 00:00:29,980
请不吝点赞 订阅 转发 打赏支持明镜与点点栏目

2
00:00:30,000 --> 00:00:59,980
请不吝点赞 订阅 转发 打赏支持明镜与点栏目

3
00:01:00,000 --> 00:01:03,760
有些犹豫 那么我们再来问一个问题 它们都是米吗 你肯定非常非常肯定告诉我

4
00:01:03,760 --> 00:01:04,360
 它们都是的

5
00:01:04,360 --> 00:01:07,420
那么这里呢就一个结论 没有米就没有粥 也就是

6
00:01:07,420 --> 00:01:09,220
说没有cookie那就没有session

7
00:01:09,220 --> 00:01:12,540
它们呢都是米 说明了cookie和session它们其实都依赖于谁啊

8
00:01:12,540 --> 00:01:14,080
米和粥是不是都依赖于米

9
00:01:14,080 --> 00:01:18,180
那么cookie和session它们都依赖于cookie 但是它们不是同一种东西 对吧

10
00:01:18,180 --> 00:01:21,760
米粥呢可以喝的 那么米呢不能直接吃 你需要去做一些烹饪 对吧

11
00:01:21,760 --> 00:01:25,600
所以我们得出个结论 没有cookie就没有session 但是它们不是同一种东西

12
00:01:25,600 --> 00:01:27,140
好 可能到这里呢 同学们呢

13
00:01:27,400 --> 00:01:27,920
不是理解了

14
00:01:27,920 --> 00:01:31,240
Session是基于Cookie 但是它们不是同种东西 那么Session到底是什么

15
00:01:31,240 --> 00:01:32,260
可能你还是没有清楚

16
00:01:32,260 --> 00:01:35,600
那么没有关系 我们在讲解Session之前 可能呢 我们需要去温析一下Cookie

17
00:01:35,600 --> 00:01:39,680
 包括咱们需要了解一下Cookie 是如何维持咱们的一个登录的一个过程

18
00:01:39,680 --> 00:01:42,760
那么慢慢的来理解 同学们不要着急 首先我们来看一下Cookie的交互过程

19
00:01:42,760 --> 00:01:45,840
首先呢 浏览器第一次访问服务端

20
00:01:45,840 --> 00:01:48,640
此时 此时呢 咱们呢 在客户端的没有Cookie

21
00:01:48,640 --> 00:01:53,260
当服务端收到请求之后 通过一个Response 携带一个SetCookie的字段 带给客户端

22
00:01:53,520 --> 00:01:58,120
然后客户端收到这样一个response之后 通过setcookie的内容 将cookie存储在浏览器的本地

23
00:01:58,120 --> 00:02:01,200
那么当客户端再次发起请求的时候 就会将cookie带给服务端

24
00:02:01,200 --> 00:02:02,800
这里呢 其实就是cookie的交互过程

25
00:02:02,800 --> 00:02:06,900
好 这里呢 我能通过代码给同学们来演示一下 我们来看一下设置cookie的一个小例子

26
00:02:06,900 --> 00:02:12,120
比如说我们在这里添加了一个test 这样一个路由 然后我们在这个 这样一个路由里面去添加了一个cookie

27
00:02:12,120 --> 00:02:17,000
那么cookie的name呢 叫做username 它的value是jack 就是这么简单 一行代码的逻辑

28
00:02:17,000 --> 00:02:17,680
 好 我们来看一下效果

29
00:02:20,280 --> 00:02:24,620
大家可以看到我们的application cookie里面有了这样的username value jack

30
00:02:24,620 --> 00:02:27,440
大家可以注意下面有这样一个东西username.si 它是做什么的呢

31
00:02:27,440 --> 00:02:31,900
其实它是对咱们上面的一条cookie它的签名防止其他人对咱们的一个cookie做一个篡改

32
00:02:31,900 --> 00:02:35,980
好 我们来看一下咱们的请求过程是不是符合我们刚才所介绍的1234步

33
00:02:35,980 --> 00:02:40,320
我们清空来刷新好 大家可以看到 首先我们来看哪里啊

34
00:02:40,320 --> 00:02:45,360
是不是response 因为response是哪里来的 是咱们后端来的 那么后端一定会有一个setcookie这个字段

35
00:02:45,360 --> 00:02:45,880
 大家可以看到

36
00:02:45,880 --> 00:02:48,860
setcookie username等于jack 对吧

37
00:02:49,100 --> 00:02:53,800
所以说呢 后端指定我们客户端 你需要去设置一个cookiecookie的name是username

38
00:02:53,800 --> 00:02:54,540
 value是jack

39
00:02:54,540 --> 00:02:56,160
好 我们再来刷新一下

40
00:02:56,160 --> 00:03:00,340
此时呢 我们的cookie是不是已经设置成空了呀 所以说我们再次刷新的时候

41
00:03:00,340 --> 00:03:03,480
我们的request里面就会带上咱们在一个cookie之段

42
00:03:03,480 --> 00:03:04,600
大家可以看到

43
00:03:04,600 --> 00:03:11,900
username等于jack username.sig等于啊 这样一串token 好 那么我们客户端已经携带的cookie是不是传到了咱们的服务端

44
00:03:11,900 --> 00:03:17,420
那么我们的服务端就可以拿到咱们的这样一串cookie 我们重点关注其实是username等于jack

45
00:03:17,420 --> 00:03:19,100
 这里呢 才是我们所需要的数据

46
00:03:19,100 --> 00:03:20,900
好

47
00:03:20,900 --> 00:03:22,580
我们了解了Cookie的交互过程之后

48
00:03:22,580 --> 00:03:24,820
那么我们来看一下Cookie到底是如何去维持登录的

49
00:03:24,820 --> 00:03:25,740
什么叫维持登录呢

50
00:03:25,740 --> 00:03:27,420
也就是咱们不停的去刷新这样一个页面

51
00:03:27,420 --> 00:03:28,340
那么我们如何

52
00:03:28,340 --> 00:03:30,380
服务端是如何知道我们登录过

53
00:03:30,380 --> 00:03:30,740
对吧

54
00:03:30,740 --> 00:03:31,300
好

55
00:03:31,300 --> 00:03:32,080
那么我们来看一下

56
00:03:32,080 --> 00:03:33,000
首先呢

57
00:03:33,000 --> 00:03:34,040
咱们在登录的时候

58
00:03:34,040 --> 00:03:35,940
是不是需要去输入用户名和密码

59
00:03:35,940 --> 00:03:37,940
那么通过request将它们发送给服务端

60
00:03:37,940 --> 00:03:39,540
那么发送给服务端之后

61
00:03:39,540 --> 00:03:41,480
咱们服务器是不是需要在数据库里面去查询

62
00:03:41,480 --> 00:03:43,420
你刚才传递过来的用户名和密码

63
00:03:43,420 --> 00:03:45,580
和我数据库所存储的用户名密码能不能对应上

64
00:03:45,580 --> 00:03:47,100
好 那么如果能够对应上

65
00:03:47,100 --> 00:03:48,500
此时

66
00:03:48,500 --> 00:03:50,300
服务端就知道了

67
00:03:50,300 --> 00:03:51,220
哎 你已经进行登录了

68
00:03:51,220 --> 00:03:52,900
那么服务端呢 接下来做件什么事情呢

69
00:03:52,900 --> 00:03:55,580
服务端将会在Response中去set一个cookie

70
00:03:55,580 --> 00:03:58,300
也就是意味着服务端要告诉浏览器

71
00:03:58,300 --> 00:03:59,700
此时呢 你要在本地去存储一个cookie

72
00:03:59,700 --> 00:04:00,780
那么cookie的指示是什么呢

73
00:04:00,780 --> 00:04:01,740
是username等于jack

74
00:04:01,740 --> 00:04:03,300
也就是你刚才所登录的用户名

75
00:04:03,300 --> 00:04:04,900
对吧 假如你的用户名是jack

76
00:04:04,900 --> 00:04:07,020
服务端呢 将在Response中去setcookie

77
00:04:07,020 --> 00:04:08,180
为username等于jack

78
00:04:09,380 --> 00:04:15,880
好 此时呢 服端进行了设的cookie 那么浏览器的本地是不是就储存一个cookie在下一次的request里面会带上

79
00:04:15,880 --> 00:04:20,280
那么浏览器在下一次请求的时候将携带username等于cookie这一段内容给服端

80
00:04:20,280 --> 00:04:26,620
好 那么将username等于cookie这一段内容带给服端 那么服端拿到username等于jack之后

81
00:04:26,620 --> 00:04:30,340
那么username等于jack从哪来 是不是从cookie里面来 服端当然可以拿到 对吧

82
00:04:30,340 --> 00:04:31,680
 好 拿到之后去数据库去查

83
00:04:31,680 --> 00:04:35,720
发现我们的username在一个资料里面有Jack这样一个人

84
00:04:35,720 --> 00:04:37,940
那么胡端就会认为你刚才已经登陆过了

85
00:04:37,940 --> 00:04:40,640
那么这样一个页面就允许你去访问

86
00:04:40,640 --> 00:04:42,680
这其实就是我们Cookie去维持登陆的一个过程

87
00:04:42,680 --> 00:04:45,840
那么接下来我们来用我们的一个最终完成的结果

88
00:04:45,840 --> 00:04:47,660
咱们来验证一下是不是刚才的一个过程

89
00:04:47,660 --> 00:04:50,200
首先我们是不是需要去登陆

90
00:04:50,200 --> 00:04:51,720
比如说ABC123456

91
00:04:51,720 --> 00:04:52,820
这里的时候提前给注册好了

92
00:04:52,820 --> 00:04:55,680
那么此时我点击登陆

93
00:04:55,680 --> 00:04:59,160
那么这个时候是不是会咱们会发送一个request

94
00:04:59,160 --> 00:05:00,120
然后去数据库里面对比

95
00:05:00,120 --> 00:05:01,100
那么如果说对比成功

96
00:05:01,100 --> 00:05:02,460
我们的用户密码是正确的

97
00:05:02,460 --> 00:05:02,920
那么

98
00:05:02,920 --> 00:05:04,520
服务端的razebounce里面是不是应该有设置cookie

99
00:05:04,520 --> 00:05:04,940
我们来看一下

100
00:05:04,940 --> 00:05:05,620
电影登录

101
00:05:05,620 --> 00:05:07,460
我们来看一下login在一个接口

102
00:05:07,460 --> 00:05:09,300
我们来看一下razebounce里面有没有设置cookie

103
00:05:09,300 --> 00:05:10,780
在一个这段大家可以看到是不是有设置cookie

104
00:05:10,780 --> 00:05:12,100
然后呢去set的一个

105
00:05:12,100 --> 00:05:14,160
eggs s e s s

106
00:05:14,160 --> 00:05:16,700
其实这里呢就是咱们用于维持登录的一个cookie

107
00:05:16,700 --> 00:05:17,200
好

108
00:05:17,200 --> 00:05:19,100
我们再来看下一个接口

109
00:05:19,100 --> 00:05:21,700
当服务端返回一个razebounce去setcookie

110
00:05:21,700 --> 00:05:22,940
此时呢咱们做了一个重定项

111
00:05:22,940 --> 00:05:23,700
重定项哪里呢

112
00:05:23,700 --> 00:05:25,580
其实重定项到了咱们的一个首页

113
00:05:25,580 --> 00:05:27,240
对吧 因为咱们刚才进登录是哪个页面了

114
00:05:27,240 --> 00:05:28,520
login 只是咱们到了首页

115
00:05:28,520 --> 00:05:30,380
根目录对吧 我们来看一下

116
00:05:30,380 --> 00:05:32,380
我们的request里面有没有携带在一个cookie

117
00:05:32,380 --> 00:05:35,280
大家可以看到我们的requestheaders里面

118
00:05:35,280 --> 00:05:37,020
也就携带在一个cookie 携带在什么呢

119
00:05:37,020 --> 00:05:38,820
egg s e s s

120
00:05:38,820 --> 00:05:40,180
好 我们携带cookie之后

121
00:05:40,180 --> 00:05:41,880
服务端看到了这一个token

122
00:05:41,880 --> 00:05:43,680
他就知道 这个用户是你

123
00:05:43,680 --> 00:05:45,480
那么当然了 因为这里它是egg

124
00:05:45,480 --> 00:05:46,840
里面去封装一个逻辑

125
00:05:46,840 --> 00:05:48,340
肯定没有我们刚才分析的那么简单

126
00:05:48,340 --> 00:05:49,980
只是说 我们刚才为什么分析那么简单

127
00:05:49,980 --> 00:05:51,420
其实为了帮助同学们去理解

128
00:05:51,420 --> 00:05:52,760
如果说你理解了这样一个过程

129
00:05:52,760 --> 00:05:54,520
那么就可以理解Cookie登陆的一个过程

130
00:05:54,520 --> 00:05:55,380
接下来我们来看一下Sation

131
00:05:55,380 --> 00:05:56,880
首先Sation的交互过程

132
00:05:56,880 --> 00:05:57,840
其实和我们Cookie的一致

133
00:05:57,840 --> 00:05:58,780
因为咱们前面讲到了

134
00:05:58,780 --> 00:05:59,880
Sation其实它这种技术

135
00:05:59,880 --> 00:06:01,120
就是基于Cookie去实现的

136
00:06:01,120 --> 00:06:02,020
好 我们来看一下

137
00:06:02,020 --> 00:06:04,720
Sation是如何维持咱们登陆的一个状态

138
00:06:04,720 --> 00:06:06,440
好 其实首先我们要搞清楚一点

139
00:06:06,440 --> 00:06:09,260
如果仅仅只需要维持登陆

140
00:06:09,260 --> 00:06:10,300
根本就不需要Sation

141
00:06:10,300 --> 00:06:12,000
使用上面的方法完全够用

142
00:06:12,000 --> 00:06:13,000
上面的方法是什么呢

143
00:06:13,000 --> 00:06:14,180
也就是使用Cookie去维持登陆

144
00:06:14,180 --> 00:06:15,340
比如说我们去塞一个Cookie

145
00:06:15,340 --> 00:06:16,060
username等于Jack

146
00:06:16,060 --> 00:06:16,980
然后去数据库里面查

147
00:06:21,420 --> 00:06:22,440
根本就不需要session

148
00:06:22,440 --> 00:06:24,500
什么意思呢 如果说我们除了登录之外

149
00:06:24,500 --> 00:06:25,780
还是要去做一些其他事情

150
00:06:25,780 --> 00:06:27,300
那么你仅仅通过cookie

151
00:06:27,300 --> 00:06:28,580
就会是登录 那么可能就

152
00:06:28,580 --> 00:06:30,640
不够用了 那么我们来设想一种场景

153
00:06:30,640 --> 00:06:33,200
假如说你的公司有一百个系统

154
00:06:33,200 --> 00:06:35,500
然后你的用户名是我是老板

155
00:06:35,500 --> 00:06:37,540
我希望只注册一次账号

156
00:06:37,540 --> 00:06:38,820
就能涉及所有的权限

157
00:06:38,820 --> 00:06:40,100
登录所有的系统

158
00:06:40,100 --> 00:06:41,380
但是有的人

159
00:06:41,380 --> 00:06:43,180
注册一次账号只能进入某些系统

160
00:06:43,180 --> 00:06:45,480
因为我们可能有些管理层或者说有些员工

161
00:06:45,480 --> 00:06:47,780
大家权限肯定不一样 能够进入系统也不一样

162
00:06:47,780 --> 00:06:48,820
那么如果说

163
00:06:49,060 --> 00:06:52,060
你仅仅去使用cookie去存储 也就是咱们上面技术去存储

164
00:06:52,060 --> 00:06:53,960
咱们服务端和客户端需要通信的技术

165
00:06:53,960 --> 00:06:57,560
你比如说 我们如果说通过cookie去判断哪些系统你可以进 不可以进

166
00:06:57,560 --> 00:06:59,260
咱们就可以去设计这样一个字幕创

167
00:06:59,260 --> 00:07:00,960
user name等于我是老板

168
00:07:00,960 --> 00:07:05,060
然后通过一个连字符 系统A等于Q 系统B等于Q 系统C等于Q

169
00:07:05,060 --> 00:07:08,460
那么如果说有成千上百个这样的系统 咱们是不是要添加非常多的这样一些字段

170
00:07:08,460 --> 00:07:11,060
比如说系统D等于什么force 哪些系统是Q 对吧

171
00:07:11,060 --> 00:07:14,260
这样就代表我们哪些系统可以进 还是没有进 不能进

172
00:07:14,260 --> 00:07:17,100
但是你可能今天设计系统权限

173
00:07:17,100 --> 00:07:18,280
明天还会涉及到

174
00:07:18,280 --> 00:07:20,740
我们为每个用户还需要去添加菜单权限

175
00:07:20,740 --> 00:07:22,180
这样其实是永无止境的

176
00:07:22,180 --> 00:07:24,420
那么我们的Cookie里面塞的内容会越来越多越来越多

177
00:07:24,420 --> 00:07:27,000
但是Cookie它有一个特点是什么

178
00:07:27,000 --> 00:07:29,040
它最多只能够存储4K的数据

179
00:07:29,040 --> 00:07:30,140
那么你总有一天会存满

180
00:07:30,140 --> 00:07:31,860
显然这种方案是不成立的

181
00:07:31,860 --> 00:07:33,580
我们的Cookie其实存储的空间非常小

182
00:07:33,580 --> 00:07:35,000
我们只能存储一些比较核心的数据

183
00:07:35,000 --> 00:07:36,800
但是如果说你的存储空间不够怎么办

184
00:07:36,800 --> 00:07:40,060
那么我们再来去举个例子

185
00:07:40,060 --> 00:07:42,500
假如我们的Cookie能够存储对象

186
00:07:42,500 --> 00:07:45,120
而且不受cookie4k大小于限制

187
00:07:45,120 --> 00:07:47,160
并且还能够实现浏览器和浮端通信

188
00:07:47,160 --> 00:07:48,040
大概多好啊

189
00:07:48,040 --> 00:07:49,120
是一件非常美妙的事情

190
00:07:49,120 --> 00:07:49,420
对吧

191
00:07:49,420 --> 00:07:52,400
你比如说我们如果说能够将cookie以这样一种形式存在

192
00:07:52,400 --> 00:07:53,300
比如说我们json

193
00:07:53,300 --> 00:07:55,080
json里面json大家都很熟悉吧

194
00:07:55,080 --> 00:07:57,880
我们有一个k叫做我是老板

195
00:07:57,880 --> 00:07:58,500
也就是你的用户名

196
00:07:58,500 --> 00:07:59,380
然后后面跟着一个对象

197
00:07:59,380 --> 00:08:02,080
里面通过一个对象去存储

198
00:08:02,080 --> 00:08:04,220
你和浮端去交互了一些内容

199
00:08:04,220 --> 00:08:05,320
这样你是不是可以无限的扩展

200
00:08:05,320 --> 00:08:06,740
而且不受cookie大小的限制

201
00:08:06,740 --> 00:08:08,260
那么为了解决这样一个问题

202
00:08:08,260 --> 00:08:09,300
于是cookie就诞生了

203
00:08:09,300 --> 00:08:12,080
那么我们来看一下cookie的一个交互过程

204
00:08:12,080 --> 00:08:14,320
首先用户输入用户名和密码

205
00:08:14,320 --> 00:08:15,740
那么通过request发送服务端

206
00:08:15,740 --> 00:08:17,940
然后服务端去数据库去查询用户名和密码

207
00:08:17,940 --> 00:08:18,380
到底对不对

208
00:08:18,380 --> 00:08:19,820
这里和咱们cookie是一致的

209
00:08:19,820 --> 00:08:20,600
如果是正确

210
00:08:20,600 --> 00:08:23,260
比如说服务端查询到你的用户名是jack

211
00:08:23,260 --> 00:08:24,980
然后服务端将在rebox中是在cookie

212
00:08:24,980 --> 00:08:26,220
username等于jack

213
00:08:26,220 --> 00:08:28,580
这里其实和咱们前面cookie的过程是一模一样的

214
00:08:28,580 --> 00:08:30,480
接下来我们来看不同的地方在这里

215
00:08:30,480 --> 00:08:31,220
那么同时

216
00:08:31,220 --> 00:08:32,860
好 这里打错了

217
00:08:32,860 --> 00:08:36,200
那么同时服务端将在

218
00:08:36,200 --> 00:08:42,440
服务端将在数据库中存储一些以Jack为Key的对象或者数据

219
00:08:42,440 --> 00:08:43,440
什么意思呢

220
00:08:43,440 --> 00:08:45,960
你比如说我们刚才是不是通过

221
00:08:45,960 --> 00:08:48,300
通过user name Jack传递到服务端

222
00:08:48,300 --> 00:08:50,320
那么服务端设置了一个cookie username等于Jack

223
00:08:50,320 --> 00:08:52,100
那么同时服务端也会在我们的数据库中

224
00:08:52,100 --> 00:08:53,320
那么数据库包含哪些呢

225
00:08:53,320 --> 00:08:55,860
咱们在一个项目中实际上使用的是mongodb

226
00:08:55,860 --> 00:08:57,340
当然你使用mysoco

227
00:08:57,340 --> 00:08:58,200
那么也是可以的

228
00:08:58,200 --> 00:08:59,680
同时在数据库里面去存储一些

229
00:08:59,680 --> 00:09:01,360
以Jack为Key的对象或者数据

230
00:09:01,360 --> 00:09:02,140
存储什么数据呢

231
00:09:02,140 --> 00:09:04,680
比如说目前我登陆一个网站

232
00:09:04,680 --> 00:09:06,000
我是不是会添加一些购物车

233
00:09:06,000 --> 00:09:07,780
或者说我需要去有一些权限

234
00:09:07,780 --> 00:09:09,240
或者说我有一些登录状态

235
00:09:09,240 --> 00:09:10,480
比如说我多长时间过期

236
00:09:10,480 --> 00:09:11,100
对吧

237
00:09:11,100 --> 00:09:12,220
我此时登录了没有

238
00:09:12,220 --> 00:09:13,160
有没有登录

239
00:09:13,160 --> 00:09:13,900
这里呢

240
00:09:13,900 --> 00:09:15,360
其实我们可以把这些数据

241
00:09:15,360 --> 00:09:16,920
把它单独的去存在数据库里面

242
00:09:16,920 --> 00:09:17,960
当用户去登录的时候

243
00:09:17,960 --> 00:09:19,300
咱们只需要使用到咱们的username

244
00:09:19,300 --> 00:09:19,760
也就是jack

245
00:09:19,760 --> 00:09:20,860
是不是可以查询到这些信息

246
00:09:20,860 --> 00:09:21,980
然后返回到我们客户端

247
00:09:21,980 --> 00:09:22,380
对吧

248
00:09:22,380 --> 00:09:24,260
那么当客户端再次访问的时候

249
00:09:24,260 --> 00:09:25,940
将携带username等于jack

250
00:09:25,940 --> 00:09:26,460
这样一个cookie

251
00:09:26,460 --> 00:09:28,840
那么服务端就会使用jack去查询

252
00:09:28,840 --> 00:09:30,940
也就是咱们之前所存储的一些数据

253
00:09:30,940 --> 00:09:34,260
此时我们服务器根据查询到的信息

254
00:09:34,260 --> 00:09:35,740
返回不同的页面到客户端

255
00:09:35,740 --> 00:09:37,020
其实在那一整个过程

256
00:09:37,020 --> 00:09:38,580
就是咱们Session所做的一个事情

257
00:09:38,580 --> 00:09:39,660
那么我们总结为一句话

258
00:09:39,660 --> 00:09:42,880
总结为一句话是什么呢

259
00:09:42,880 --> 00:09:47,900
Cookie是将用户数据存储在本地

260
00:09:47,900 --> 00:09:56,020
Session是将用户相关的数据存储到服务端

261
00:09:56,020 --> 00:09:57,860
其实它们根本的区别是什么呢

262
00:09:57,860 --> 00:09:59,240
Cookie将数据存在本地

263
00:09:59,240 --> 00:10:01,240
C型将用户的数据存储到复端

264
00:10:01,240 --> 00:10:02,740
它们核心沟通的桥梁是什么

265
00:10:02,740 --> 00:10:03,740
其实就是userlin

266
00:10:03,740 --> 00:10:06,200
好 那么这里其实就是Cookie和C型的区别

267
00:10:06,200 --> 00:10:08,360
那么接下来我们将来看一下咱们

268
00:10:08,360 --> 00:10:10,320
如何去结合Redis和Monkodb

269
00:10:10,320 --> 00:10:12,320
去实现咱们这样一套C型机制

270
00:10:12,320 --> 00:10:14,200
好 这里就是我们这节奏内容

