1
00:00:00,000 --> 00:00:02,480
熟悉了Polo Server的这个开发流程之后呢

2
00:00:02,480 --> 00:00:04,040
我们接下来来详细的探讨一下

3
00:00:04,040 --> 00:00:06,580
就是GraphQL当中最先要定义的它的规则

4
00:00:06,580 --> 00:00:07,800
其实还有很多的细节

5
00:00:07,800 --> 00:00:09,060
那具体呢我们来看一下

6
00:00:09,060 --> 00:00:10,320
所以这个对象来说呢

7
00:00:10,320 --> 00:00:12,460
我们之前用到的是Type Query

8
00:00:12,460 --> 00:00:13,640
就定义了一个查询的类型

9
00:00:13,640 --> 00:00:14,380
那除此之外呢

10
00:00:14,380 --> 00:00:15,860
其实我们可以定义更多的类型

11
00:00:15,860 --> 00:00:17,060
接下来这个类型当中呢

12
00:00:17,060 --> 00:00:18,020
也可以有更多的字段

13
00:00:18,020 --> 00:00:19,460
其实呢就GraphQL呢

14
00:00:19,460 --> 00:00:21,240
它提供了一套完善的类型系统

15
00:00:21,240 --> 00:00:22,300
那这些类型呢

16
00:00:22,300 --> 00:00:23,260
就规定了服务端

17
00:00:23,260 --> 00:00:25,840
可以向客户端提供哪种格式的数据

18
00:00:25,840 --> 00:00:28,040
所以说这是有严格规定的

19
00:00:28,040 --> 00:00:29,700
接下来我们通过这样一个具体的场景

20
00:00:29,700 --> 00:00:30,500
来演示一下

21
00:00:30,500 --> 00:00:32,220
增加复杂一点的对象类型的定义

22
00:00:32,220 --> 00:00:33,780
具体是这样的

23
00:00:33,780 --> 00:00:34,900
我们提供一个学生类型

24
00:00:34,900 --> 00:00:36,540
学生类型当中有这个姓名

25
00:00:36,540 --> 00:00:38,280
还有它的各科成绩

26
00:00:38,280 --> 00:00:39,220
就是分数

27
00:00:39,220 --> 00:00:42,280
这个每一科的成绩又是一个新的类型

28
00:00:42,280 --> 00:00:43,280
就是课程

29
00:00:43,280 --> 00:00:45,020
课程的话有课程的名称

30
00:00:45,020 --> 00:00:46,480
还有课程的三科的分数

31
00:00:46,480 --> 00:00:48,700
接下来我们就演示一下这个场景

32
00:00:48,700 --> 00:00:49,840
具体我们这样来做

33
00:00:49,840 --> 00:00:52,360
我们就是参考最开始的简单案例

34
00:00:52,360 --> 00:00:53,820
就一个hello2的ind来做

35
00:00:53,820 --> 00:00:54,720
在这个基础之上

36
00:00:54,720 --> 00:00:55,680
我们再定义新的类型

37
00:00:55,680 --> 00:00:57,100
比如说我们先定义一个

38
00:00:57,100 --> 00:00:57,980
标准它

39
00:00:57,980 --> 00:01:00,340
这里边有s name

40
00:01:00,340 --> 00:01:01,260
就是学生的名称

41
00:01:01,260 --> 00:01:02,320
姓名

42
00:01:02,320 --> 00:01:03,160
然后这个类型的话

43
00:01:03,160 --> 00:01:03,660
我们是子名

44
00:01:03,660 --> 00:01:04,920
然后的话还有分数

45
00:01:04,920 --> 00:01:05,780
主意这个分数

46
00:01:05,780 --> 00:01:07,720
是很多学科的分数

47
00:01:07,720 --> 00:01:09,180
所以说应该是一个数组

48
00:01:09,180 --> 00:01:10,120
那在quarker当中

49
00:01:10,120 --> 00:01:10,540
表示数组

50
00:01:10,540 --> 00:01:11,420
其实用的也是放括号

51
00:01:11,420 --> 00:01:12,780
但是这个数组中放的

52
00:01:12,780 --> 00:01:13,800
其实是另外一个对象

53
00:01:13,800 --> 00:01:14,900
就是cost

54
00:01:14,900 --> 00:01:15,820
课程对象

55
00:01:15,820 --> 00:01:16,720
所以说课程

56
00:01:16,720 --> 00:01:17,880
也需要一个类型来援助

57
00:01:17,880 --> 00:01:18,540
在这

58
00:01:18,540 --> 00:01:19,180
这样

59
00:01:19,180 --> 00:01:19,820
cost

60
00:01:19,820 --> 00:01:21,980
然后这个cost的话

61
00:01:21,980 --> 00:01:22,540
有这个finim

62
00:01:22,540 --> 00:01:24,440
表示课程的名称

63
00:01:24,440 --> 00:01:25,840
也是Spin类型的

64
00:01:25,840 --> 00:01:27,040
还有它的单核的分数

65
00:01:27,040 --> 00:01:28,880
就每一个核目的分数

66
00:01:28,880 --> 00:01:29,620
分数的话

67
00:01:29,620 --> 00:01:31,080
我们用的是float类型的

68
00:01:31,080 --> 00:01:32,440
因为它在graphq2当中

69
00:01:32,440 --> 00:01:33,760
提供了一些内置的类型

70
00:01:33,760 --> 00:01:35,280
除了Spin和float之外

71
00:01:35,280 --> 00:01:36,000
还有一些别的类型

72
00:01:36,000 --> 00:01:37,160
我们后面会单独说

73
00:01:37,160 --> 00:01:38,240
这我们遇到一个的话

74
00:01:38,240 --> 00:01:38,860
先记录

75
00:01:38,860 --> 00:01:39,780
这就可以了

76
00:01:39,780 --> 00:01:41,600
好 这是关于两种类型

77
00:01:41,600 --> 00:01:43,640
建议好类型之后

78
00:01:43,640 --> 00:01:45,280
其实我们随着业务的复版

79
00:01:45,280 --> 00:01:46,420
这里边类型会越来越多

80
00:01:46,420 --> 00:01:48,000
所以说我们一般会加一点注视

81
00:01:48,000 --> 00:01:49,880
注视的话怎么加呢

82
00:01:49,880 --> 00:01:51,000
在这里边有点特别

83
00:01:51,000 --> 00:01:52,440
它的注册用的是警号

84
00:01:52,440 --> 00:01:54,800
这个表示课程类型

85
00:01:54,800 --> 00:01:56,180
然后下面这个

86
00:01:56,180 --> 00:01:59,360
这个表示是学生类型

87
00:01:59,360 --> 00:02:01,380
然后最后我们也加上注册

88
00:02:01,380 --> 00:02:04,620
这个叫查询类型

89
00:02:04,620 --> 00:02:06,240
其实我们定义的这三个类型

90
00:02:06,240 --> 00:02:07,240
本质上没有什么区别

91
00:02:07,240 --> 00:02:09,000
唯一有区别的就是query

92
00:02:09,000 --> 00:02:10,560
它是查询的入口

93
00:02:10,560 --> 00:02:11,100
除此之外

94
00:02:11,100 --> 00:02:12,640
它们其实本质上都是一样的

95
00:02:12,640 --> 00:02:14,140
这是关于类型的一些特性

96
00:02:14,140 --> 00:02:14,800
我们要注意

97
00:02:14,800 --> 00:02:16,180
这种注册的方式我们要记录

98
00:02:16,180 --> 00:02:18,020
到此为止

99
00:02:18,020 --> 00:02:19,020
关于类型我们都提供好了

100
00:02:19,020 --> 00:02:20,240
接下来我们需要提供一下

101
00:02:20,240 --> 00:02:21,100
数据的解析

102
00:02:21,100 --> 00:02:22,400
数据的解析的话

103
00:02:22,400 --> 00:02:24,400
我们应该在查询类型当中

104
00:02:24,400 --> 00:02:25,360
添加学生的信息

105
00:02:25,360 --> 00:02:26,800
比如说我们提供名字叫STO

106
00:02:26,800 --> 00:02:27,980
它后面遵用的类型

107
00:02:27,980 --> 00:02:28,720
就是我们自定义的

108
00:02:28,720 --> 00:02:29,500
S6.t

109
00:02:29,500 --> 00:02:32,640
这STO也需要通过Resolve

110
00:02:32,640 --> 00:02:33,360
来做解析

111
00:02:33,360 --> 00:02:35,180
这加上STO

112
00:02:35,180 --> 00:02:36,120
后面是一个函数

113
00:02:36,120 --> 00:02:39,460
函数当中用来提供

114
00:02:39,460 --> 00:02:40,720
学生相关的数据

115
00:02:40,720 --> 00:02:42,440
就是这里提供

116
00:02:42,440 --> 00:02:45,240
学生相关的数据

117
00:02:45,240 --> 00:02:46,160
这数据的格式

118
00:02:46,160 --> 00:02:46,700
应该是什么样子

119
00:02:46,700 --> 00:02:48,240
我们直接去追上一个对象

120
00:02:48,240 --> 00:02:49,280
这个对象里面

121
00:02:49,280 --> 00:02:49,860
必须包含

122
00:02:49,860 --> 00:02:51,420
相关这个属性

123
00:02:51,420 --> 00:02:52,720
我们这定义的有两个

124
00:02:52,720 --> 00:02:53,660
所以说我们这返回的

125
00:02:53,660 --> 00:02:54,640
就是SNAME

126
00:02:54,640 --> 00:02:55,860
后边这个值呢

127
00:02:55,860 --> 00:02:56,520
我们就叫张三

128
00:02:56,520 --> 00:02:58,400
然后这分数的话

129
00:02:58,400 --> 00:02:58,900
怎么处理呢

130
00:02:58,900 --> 00:03:00,540
分数的话实际上是一个数字

131
00:03:00,540 --> 00:03:01,900
我们先把这个名字加上

132
00:03:01,900 --> 00:03:02,360
SCOAT

133
00:03:02,360 --> 00:03:03,340
后边是数字

134
00:03:03,340 --> 00:03:03,920
这个数字呢

135
00:03:03,920 --> 00:03:05,060
应该通过Data的方式

136
00:03:05,060 --> 00:03:06,060
我们在这定义一个

137
00:03:06,060 --> 00:03:07,340
Data等于数字

138
00:03:07,340 --> 00:03:08,760
来提供一下

139
00:03:08,760 --> 00:03:09,540
那这里边的数据

140
00:03:09,540 --> 00:03:10,480
其实是课程的数据

141
00:03:10,480 --> 00:03:11,420
那课程的数据呢

142
00:03:11,420 --> 00:03:12,460
也要符合上这定义的规则

143
00:03:12,460 --> 00:03:13,780
所以说我们这里边呢

144
00:03:13,780 --> 00:03:15,140
每一个对象应该有SNAME

145
00:03:15,140 --> 00:03:17,000
比如说这里边有数学

146
00:03:17,000 --> 00:03:19,420
这是数据这个小课

147
00:03:19,420 --> 00:03:20,460
这个分数呢

148
00:03:20,460 --> 00:03:21,720
比如说考了100分

149
00:03:21,720 --> 00:03:23,160
然后呢再来一个

150
00:03:23,160 --> 00:03:25,840
这呢是CNAME

151
00:03:25,840 --> 00:03:27,740
这个呢是英语

152
00:03:27,740 --> 00:03:30,780
英语的话呢

153
00:03:30,780 --> 00:03:31,600
我们看给它一个分数

154
00:03:31,600 --> 00:03:32,300
比如说99

155
00:03:32,300 --> 00:03:33,400
这样的话呢

156
00:03:33,400 --> 00:03:34,900
我们就提供了两条数据

157
00:03:34,900 --> 00:03:35,440
这数据呢

158
00:03:35,440 --> 00:03:36,620
通过Rhythm的方式来返回

159
00:03:36,620 --> 00:03:37,720
到此为止呢

160
00:03:37,720 --> 00:03:38,480
我们这个类型呢

161
00:03:38,480 --> 00:03:39,060
就定义好了

162
00:03:39,060 --> 00:03:40,340
数据也解析好了

163
00:03:40,340 --> 00:03:41,140
那接下来呢

164
00:03:41,140 --> 00:03:41,940
我们来运行

165
00:03:41,940 --> 00:03:44,600
这里边要运行的是02

166
00:03:44,600 --> 00:03:47,240
回车

167
00:03:47,240 --> 00:03:48,540
这里边是running

168
00:03:48,540 --> 00:03:50,240
那就表示运行成功了

169
00:03:50,240 --> 00:03:51,600
然后的话我们来查询一下

170
00:03:51,600 --> 00:03:53,340
STU里边的数据

171
00:03:53,340 --> 00:03:54,080
那查询的方式

172
00:03:54,080 --> 00:03:55,820
我们还是通过URD值的方式

173
00:03:55,820 --> 00:03:57,120
最后的话我们用的还是4000

174
00:03:57,120 --> 00:03:58,100
回车

175
00:03:58,100 --> 00:04:00,500
注意这里边要加上路径的

176
00:04:00,500 --> 00:04:01,560
我们的路径是5RQR

177
00:04:01,560 --> 00:04:02,040
回车

178
00:04:02,040 --> 00:04:03,480
那这个位置

179
00:04:03,480 --> 00:04:04,360
查询参数

180
00:04:04,360 --> 00:04:05,680
我们直接是STU

181
00:04:05,680 --> 00:04:07,340
由于这STU是一个对象

182
00:04:07,340 --> 00:04:08,340
所以说后边你要指定

183
00:04:08,340 --> 00:04:10,120
查每个字段

184
00:04:10,120 --> 00:04:11,320
这应该是SNIM

185
00:04:11,320 --> 00:04:12,560
然后我们点查询

186
00:04:12,560 --> 00:04:13,240
有了

187
00:04:13,240 --> 00:04:14,580
然后还有一个分数

188
00:04:14,580 --> 00:04:16,640
分数的话是Scot

189
00:04:16,640 --> 00:04:18,380
但是Scot它是一个数组

190
00:04:18,380 --> 00:04:19,540
所以说数组当中的对象

191
00:04:19,540 --> 00:04:20,680
应该查询的一些信息

192
00:04:20,680 --> 00:04:22,320
比如说我们只查课程的名称

193
00:04:22,320 --> 00:04:24,000
我们就是Chenium

194
00:04:24,000 --> 00:04:25,140
然后再点查询

195
00:04:25,140 --> 00:04:26,700
这里查出来的就是每一课

196
00:04:26,700 --> 00:04:28,520
它的课程的名称

197
00:04:28,520 --> 00:04:30,180
如果你想查询成绩的话

198
00:04:30,180 --> 00:04:31,140
那就再加上Scot

199
00:04:31,140 --> 00:04:32,460
好 查询

200
00:04:32,460 --> 00:04:33,760
这样的话所有的信息就出来了

201
00:04:33,760 --> 00:04:35,280
所以说我们可以精确的

202
00:04:35,280 --> 00:04:36,500
来获取我们需要的数据

203
00:04:36,500 --> 00:04:38,220
这是相对复杂的一个场景

204
00:04:38,220 --> 00:04:39,860
这是关于类型定义

205
00:04:39,860 --> 00:04:40,720
我们需要注意的地方

206
00:04:40,720 --> 00:04:43,080
然后我们看这里边的一些注意事项

207
00:04:43,080 --> 00:04:44,720
就是这个花括号中

208
00:04:44,720 --> 00:04:46,760
就是对这个对象当中的

209
00:04:46,760 --> 00:04:48,120
字段信息的描述

210
00:04:48,120 --> 00:04:50,000
这个类型有几个字段

211
00:04:50,000 --> 00:04:51,600
然后这个属性名

212
00:04:51,600 --> 00:04:52,780
这个是自定义的

213
00:04:52,780 --> 00:04:53,480
属性的类型

214
00:04:53,480 --> 00:04:54,800
这个是内置的

215
00:04:54,800 --> 00:04:56,100
详细的这个知识的类型

216
00:04:56,100 --> 00:04:59,460
我们在后面会单独的来说

217
00:04:59,460 --> 00:05:00,440
其实这个类型的话

218
00:05:00,440 --> 00:05:01,480
我们也可以看一下官网

219
00:05:01,480 --> 00:05:03,520
在这为止

220
00:05:03,520 --> 00:05:06,000
有一个叫标亮类型

221
00:05:06,000 --> 00:05:06,760
点开

222
00:05:06,760 --> 00:05:07,800
其实它知识的类型

223
00:05:07,800 --> 00:05:08,260
有这么几个

224
00:05:08,260 --> 00:05:10,040
我们后面会系统的在说一说

225
00:05:10,040 --> 00:05:11,420
这先有一个整体的认识

226
00:05:11,420 --> 00:05:12,080
好了

227
00:05:12,080 --> 00:05:12,920
这是关于这个类型

228
00:05:12,920 --> 00:05:13,880
然后的话

229
00:05:13,880 --> 00:05:14,860
我们再往下

230
00:05:14,860 --> 00:05:16,900
就是属性名后边的类型

231
00:05:16,900 --> 00:05:17,840
它是标量类型

232
00:05:17,840 --> 00:05:19,080
其实就是我们刚才所看的

233
00:05:19,080 --> 00:05:19,580
那个类质类型

234
00:05:19,580 --> 00:05:20,640
我们称之为类质类型

235
00:05:20,640 --> 00:05:22,320
应该是更好理解一些

236
00:05:22,320 --> 00:05:24,500
最后还有一个知识点

237
00:05:24,500 --> 00:05:25,760
就是Growthr当中

238
00:05:25,760 --> 00:05:26,860
使用这个警号进行注视

239
00:05:26,860 --> 00:05:28,920
这是关于对象类型定义

240
00:05:28,920 --> 00:05:30,100
我们需要注意的一些细节

241
00:05:30,100 --> 00:05:31,480
好了

242
00:05:31,480 --> 00:05:32,520
关于这个对象类型的定义

243
00:05:32,520 --> 00:05:33,800
我们就先说到这里

