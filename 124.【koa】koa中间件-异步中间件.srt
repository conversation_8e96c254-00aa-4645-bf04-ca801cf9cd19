1
00:00:00,000 --> 00:00:01,700
好这节课我们就来看一下

2
00:00:01,700 --> 00:00:02,600
一步中间键

3
00:00:02,600 --> 00:00:04,600
那么什么是一步中间键呢

4
00:00:04,600 --> 00:00:06,060
因为我们所有的操作

5
00:00:06,060 --> 00:00:07,580
咱们刚才讲的one two three的例子

6
00:00:07,580 --> 00:00:08,200
是不是都是同步的

7
00:00:08,200 --> 00:00:09,620
那么其实在我们实际业务中

8
00:00:09,620 --> 00:00:10,620
有很多一步的场景

9
00:00:10,620 --> 00:00:12,040
那么如果说有一步的操作

10
00:00:12,040 --> 00:00:12,560
我们怎么样去办

11
00:00:12,560 --> 00:00:13,100
怎么样去做呢

12
00:00:13,100 --> 00:00:14,680
我们如何保证我们中间键的一个指令顺序

13
00:00:14,680 --> 00:00:18,080
那么我们这里直接来看一个例子

14
00:00:18,080 --> 00:00:20,420
我这里建了一个async.js

15
00:00:20,420 --> 00:00:23,520
其实还是和刚才咱们的one two three是一样的

16
00:00:23,520 --> 00:00:26,820
还是和咱们的one two three是一样的

17
00:00:26,820 --> 00:00:27,920
那么我们这里来做一个修改

18
00:00:27,920 --> 00:00:29,080
什么修改呢

19
00:00:30,000 --> 00:00:30,960
我们来给他一个定试器

20
00:00:30,960 --> 00:00:32,140
比如说我们在3里面

21
00:00:32,140 --> 00:00:35,800
我们把它给改成一步的

22
00:00:35,800 --> 00:00:36,640
那么我们的执行顺序

23
00:00:36,640 --> 00:00:37,320
是不是会变坏啊

24
00:00:37,320 --> 00:00:37,980
同学们

25
00:00:37,980 --> 00:00:39,380
首先我们来执行

26
00:00:39,380 --> 00:00:39,920
好

27
00:00:39,920 --> 00:00:40,240
访问一下

28
00:00:40,240 --> 00:00:40,620
都是放的

29
00:00:40,620 --> 00:00:40,820
好

30
00:00:40,820 --> 00:00:41,520
我们来看一下打印顺序

31
00:00:41,520 --> 00:00:44,420
是不是1,2,3

32
00:00:44,420 --> 00:00:46,180
2,1,3

33
00:00:46,180 --> 00:00:47,860
是不是刚才咱们

34
00:00:47,860 --> 00:00:48,980
一步的这些行代码

35
00:00:48,980 --> 00:00:50,200
它的执行顺序有一些问题

36
00:00:50,200 --> 00:00:52,320
那么我们怎么样去做呢

37
00:00:52,320 --> 00:00:54,000
其实它很简单

38
00:00:54,000 --> 00:00:56,000
其实很简单

39
00:00:56,000 --> 00:00:58,420
刚才我们是不是讲到了

40
00:00:58,420 --> 00:01:00,180
我们是不是讲过

41
00:01:00,180 --> 00:01:01,680
讲过什么呀

42
00:01:01,680 --> 00:01:02,800
Core他是不是支持

43
00:01:02,800 --> 00:01:08,060
支持他什么

44
00:01:08,060 --> 00:01:12,600
是不是esc里面的async和wait呀

45
00:01:12,600 --> 00:01:18,200
async和wait async

46
00:01:18,200 --> 00:01:18,640
好

47
00:01:18,640 --> 00:01:20,200
那么我们怎么去使用他呢

48
00:01:20,200 --> 00:01:21,560
比如说

49
00:01:21,560 --> 00:01:26,600
我们是不是直接async方形

50
00:01:26,600 --> 00:01:29,640
这里就是我们所需要执行的方法

51
00:01:29,640 --> 00:01:31,520
ctx next

52
00:01:31,520 --> 00:01:34,040
然后我们await3

53
00:01:34,040 --> 00:01:35,800
我们是不是需要去await

54
00:01:35,800 --> 00:01:37,320
那么这样一个定时器这样一个方法

55
00:01:37,320 --> 00:01:38,600
比如说我们去打印一个3

56
00:01:38,600 --> 00:01:40,360
那么这样我们直接await他可不可以

57
00:01:40,360 --> 00:01:43,240
直接await他await他可不可以

58
00:01:43,240 --> 00:01:46,280
不可以吧为什么

59
00:01:46,280 --> 00:01:48,200
因为await需要

60
00:01:48,200 --> 00:01:49,160
是不是需要

61
00:01:49,160 --> 00:01:50,800
是不是需要promise

62
00:01:50,800 --> 00:01:52,040
你不是promise你怎么去await他

63
00:01:52,040 --> 00:01:54,200
对吧他怎么知道await是一个同步还是一本蛋

64
00:01:54,200 --> 00:01:57,120
所以我们需要去使用Promise去封装一下

65
00:01:57,120 --> 00:01:58,300
怎么样去封装一个Promise

66
00:01:58,300 --> 00:02:01,180
比如说我们去挖一个

67
00:02:01,180 --> 00:02:03,040
比如说我们去是不是Log3呢

68
00:02:03,040 --> 00:02:07,340
等于一个newPromise

69
00:02:07,340 --> 00:02:10,300
Promise怎么写上句话记得吗

70
00:02:10,300 --> 00:02:12,660
是不是一个resolve一个什么reget

71
00:02:12,660 --> 00:02:15,180
然后我们是不是需要在

72
00:02:15,180 --> 00:02:16,660
一个定时器里面

73
00:02:16,660 --> 00:02:19,400
也就是一段时间之后

74
00:02:19,400 --> 00:02:23,180
去干嘛呀

75
00:02:23,180 --> 00:02:24,080
是不是打赢我们的

76
00:02:24,080 --> 00:02:26,140
3啊

77
00:02:26,140 --> 00:02:27,360
我们来copy吧

78
00:02:27,360 --> 00:02:30,980
那么执行完之后

79
00:02:30,980 --> 00:02:32,720
我们是不是要把它给resource掉

80
00:02:32,720 --> 00:02:33,740
对吧

81
00:02:33,740 --> 00:02:34,140
好

82
00:02:34,140 --> 00:02:35,180
那么这里呢

83
00:02:35,180 --> 00:02:37,840
我们就来去wait一下它

84
00:02:37,840 --> 00:02:41,560
wait是不是我们的log

85
00:02:41,560 --> 00:02:42,980
log3呢

86
00:02:42,980 --> 00:02:43,580
对吧

87
00:02:43,580 --> 00:02:48,980
好

88
00:02:48,980 --> 00:02:50,440
那么我们来看一下运行结果是什么

89
00:02:50,440 --> 00:02:55,240
好 三千

90
00:02:55,240 --> 00:02:56,440
大家可以看到

91
00:02:56,440 --> 00:02:57,860
其实我们访问的结果

92
00:02:57,860 --> 00:02:59,820
还是123213

93
00:02:59,820 --> 00:03:00,600
说明什么问题

94
00:03:00,600 --> 00:03:01,960
是不是说明我们的

95
00:03:01,960 --> 00:03:03,100
async await没有生效

96
00:03:03,100 --> 00:03:04,640
为什么没有生效

97
00:03:04,640 --> 00:03:06,300
而且大家可以看到

98
00:03:06,300 --> 00:03:06,780
我们的3

99
00:03:06,780 --> 00:03:08,080
它确实是两秒钟之后

100
00:03:08,080 --> 00:03:09,280
才蹦出来的

101
00:03:09,280 --> 00:03:11,620
不行 我们再访问一次看一下

102
00:03:11,620 --> 00:03:12,380
刷新

103
00:03:12,380 --> 00:03:13,720
大家可以看到

104
00:03:13,720 --> 00:03:14,960
3是不是最后才跳出来

105
00:03:14,960 --> 00:03:15,320
说明了

106
00:03:15,320 --> 00:03:16,540
其实咱们的async await

107
00:03:16,540 --> 00:03:17,500
其实这里是生效

108
00:03:17,500 --> 00:03:18,700
但是为什么

109
00:03:18,700 --> 00:03:22,480
但是为什么我们的执行顺序不对

110
00:03:22,480 --> 00:03:25,980
是不是我们的sync wait使用错了

111
00:03:25,980 --> 00:03:26,420
还是什么原因

112
00:03:26,420 --> 00:03:27,760
还是call这个框架它有问题

113
00:03:27,760 --> 00:03:29,360
咱们试考一下

114
00:03:29,360 --> 00:03:31,720
其实原因出在哪里呢

115
00:03:31,720 --> 00:03:34,600
比如说咱们app.use3的时候

116
00:03:34,600 --> 00:03:36,140
use3的时候

117
00:03:36,140 --> 00:03:37,400
大家可以注意

118
00:03:37,400 --> 00:03:39,040
咱们2这样一个方法执行的时候

119
00:03:39,040 --> 00:03:39,900
它的一个next是谁

120
00:03:39,900 --> 00:03:42,240
它的next是不是指咱们的3

121
00:03:42,240 --> 00:03:44,460
但是如果说你一个sync的函数

122
00:03:44,460 --> 00:03:45,360
你直接这样去调用它

123
00:03:45,360 --> 00:03:47,460
咱们2是不是不会等待它

124
00:03:47,460 --> 00:03:53,500
所以我们需要把2也改为我们的async函数

125
00:03:53,500 --> 00:03:59,080
比如说async方形

126
00:03:59,080 --> 00:04:01,540
好 当天

127
00:04:01,540 --> 00:04:06,440
ctx next 我们去await它

128
00:04:06,440 --> 00:04:08,780
好 那么我们再来执行一下看一下

129
00:04:08,780 --> 00:04:15,000
好 大家可以看到我们现在的函数执行顺序

130
00:04:15,000 --> 00:04:17,040
是不是变成了12312

131
00:04:17,040 --> 00:04:18,580
好像还是一种问题 为什么

132
00:04:18,580 --> 00:04:20,120
是不是因为我们的

133
00:04:20,120 --> 00:04:21,400
to此时他是一步的

134
00:04:21,400 --> 00:04:24,480
所以在咱们one的next里面去执行他的时候是不是也要去

135
00:04:24,480 --> 00:04:30,100
是不是也要去await他呀

136
00:04:30,100 --> 00:04:34,960
所以此时我们是不是要把one也改成咱们的一个一步函数

137
00:04:34,960 --> 00:04:36,000
as

138
00:04:36,000 --> 00:04:38,300
ync

139
00:04:38,300 --> 00:04:39,320
方形

140
00:04:39,320 --> 00:04:40,600
ctx

141
00:04:40,600 --> 00:04:41,880
next

142
00:04:45,000 --> 00:04:48,420
好 那么我们再来执行一下 看一下我们的一个结果是什么

143
00:04:48,420 --> 00:04:55,180
one two three three one 大家可以看到 现在我们的整体顺序是不是就已经正常了呀

144
00:04:55,180 --> 00:04:58,260
那么所以呢 这里希望大家注意的一点是什么呢

145
00:04:58,260 --> 00:05:04,650
我们一旦 比如说我们的中间键 假如说我们的三 它是一个异步的 我们一定要注意

146
00:05:04,650 --> 00:05:08,740
 two 我们也要把它改成sync weight 包括one 这是在口网里面去实现异步的一种

147
00:05:08,740 --> 00:05:11,440
稍微的有一点麻烦 但是它却非常的好使

148
00:05:11,440 --> 00:05:14,760
好 那么我们就来总结一下这一刻的内容

149
00:05:15,000 --> 00:05:20,680
我们是不是刚才所讲解了在cora中间键里面的e步中间键了

150
00:05:20,680 --> 00:05:27,680
e步中间键我们需要使用什么是不是async和wait

151
00:05:27,680 --> 00:05:29,000
我们需要注意的一点是什么

152
00:05:29,000 --> 00:05:37,020
需要特别注意的是什么是不是app在use的时候是不是需要吧

153
00:05:37,020 --> 00:05:44,420
其他的中间键都改为什么async和

154
00:05:44,420 --> 00:05:50,820
结合weight的模式 也就是说你只要有一个中间键是eable的 那么它上面的中间键你全部都要改为async/weight这种模式

155
00:05:50,820 --> 00:05:52,360
是不是和咱们的next有关了

156
00:05:52,360 --> 00:05:56,960
希望同学们能好好的去体会一下这块 可能有些同学刚开始学习这块是很难去体会

157
00:05:56,960 --> 00:05:58,500
 你可能需要去结合async/weight

158
00:05:58,500 --> 00:06:03,360
去看一下他的文档 以及呢 再来回顾一下咱们之前的next他是做什么用的

159
00:06:03,360 --> 00:06:05,920
好好的去理解一下 好 这点就是我们这节课的内容

