1
00:00:00,000 --> 00:00:02,440
好 接下来我们来看一下我们的闪念

2
00:00:02,440 --> 00:00:05,360
怎么样去完成我们刚才文章的一个例子

3
00:00:05,360 --> 00:00:06,660
之前我们是不是讲到了

4
00:00:06,660 --> 00:00:08,520
在文章通过注册存储

5
00:00:08,520 --> 00:00:10,920
它呢会造成两个问题

6
00:00:10,920 --> 00:00:12,300
你比如说会产生静态

7
00:00:12,300 --> 00:00:14,560
你两个扣端同时去操作会发生冲突

8
00:00:14,560 --> 00:00:16,100
最终呢只有一个属性被修改

9
00:00:16,100 --> 00:00:17,920
因为呢它没有原子化的操作

10
00:00:17,920 --> 00:00:19,520
第二个呢就是你的每次

11
00:00:19,520 --> 00:00:22,600
修改或者读取都需要去进行反去的话

12
00:00:22,600 --> 00:00:23,240
消耗性能

13
00:00:23,240 --> 00:00:25,040
这个我们之前是不是讲过了

14
00:00:25,040 --> 00:00:26,100
那么我们来看一下

15
00:00:26,100 --> 00:00:29,100
为什么你两个扣端同时操作会发生冲突

16
00:00:29,100 --> 00:00:31,140
这里呢

17
00:00:31,140 --> 00:00:33,200
我能来写一段代码给同学们来演示一下

18
00:00:33,200 --> 00:00:34,380
比如说data等于

19
00:00:34,380 --> 00:00:37,560
假如说咱们的data有两个属性

20
00:00:37,560 --> 00:00:39,600
a1和b2

21
00:00:39,600 --> 00:00:40,880
好

22
00:00:40,880 --> 00:00:43,380
那么如果说此时我们呢

23
00:00:43,380 --> 00:00:46,000
a做了一个操作

24
00:00:46,000 --> 00:00:50,600
将data.a改成了10

25
00:00:50,600 --> 00:00:55,920
那么b呢也做了一个操作

26
00:00:55,920 --> 00:00:58,580
将我们的data.b改成了20

27
00:00:58,580 --> 00:01:00,840
那么不管A和B什么时候发生

28
00:01:00,840 --> 00:01:01,640
那么我们的data

29
00:01:01,640 --> 00:01:03,160
它的A和B是不是都被修改了

30
00:01:03,160 --> 00:01:03,960
但是假如说

31
00:01:03,960 --> 00:01:05,100
但是假如

32
00:01:05,100 --> 00:01:09,600
假如我们的data

33
00:01:09,600 --> 00:01:10,820
在修改的时候

34
00:01:10,820 --> 00:01:13,600
它被反训练化了

35
00:01:13,600 --> 00:01:14,320
那么此时

36
00:01:14,320 --> 00:01:15,760
你比如说我们是一个自不穿的

37
00:01:15,760 --> 00:01:19,440
自不穿的A和1B2

38
00:01:19,440 --> 00:01:22,460
那么你假如说A

39
00:01:22,460 --> 00:01:24,840
假如说A

40
00:01:24,840 --> 00:01:27,000
A修改完之后

41
00:01:27,000 --> 00:01:28,160
是不是变成这样了

42
00:01:28,160 --> 00:01:28,600
对吧

43
00:01:28,600 --> 00:01:30,460
那么B修改完之后呢

44
00:01:30,460 --> 00:01:33,080
是不是A还是一样

45
00:01:33,080 --> 00:01:34,280
B呢

46
00:01:34,280 --> 00:01:35,540
20

47
00:01:35,540 --> 00:01:37,520
那么此时A和B

48
00:01:37,520 --> 00:01:40,180
他们不管随先修改或者随后修改

49
00:01:40,180 --> 00:01:41,540
是不是咱们最终只能改

50
00:01:41,540 --> 00:01:43,160
修改A或者B的一个属性呢

51
00:01:43,160 --> 00:01:44,940
这样就会造成咱们刚才所产生的问题

52
00:01:44,940 --> 00:01:45,780
会产生静态

53
00:01:45,780 --> 00:01:48,160
这里也是通过字幕创去存储

54
00:01:48,160 --> 00:01:50,040
我们的文章会存在的一些问题

55
00:01:50,040 --> 00:01:52,300
那我们就来看一下

56
00:01:52,300 --> 00:01:53,940
不管闪链怎么样去改造

57
00:01:53,940 --> 00:01:55,780
而且这里也会提出一个新需求

58
00:01:55,780 --> 00:01:57,180
一般文章它都会有缩裂名

59
00:01:57,180 --> 00:01:59,740
比如说我们的文章叫this is a good post

60
00:01:59,740 --> 00:02:02,560
他的说列名就可以为this is a great post

61
00:02:02,560 --> 00:02:05,120
那么说列名其实可以用作我们生成文章的地址

62
00:02:05,120 --> 00:02:06,400
或者会有一些其他的用处

63
00:02:06,400 --> 00:02:08,760
好 这里我们就直接来看一下怎么样去改造

64
00:02:08,760 --> 00:02:11,720
首先我们用散烈去改造的时候

65
00:02:11,720 --> 00:02:13,380
我们首先定义我们的见词

66
00:02:13,380 --> 00:02:14,800
是不是我们的内名加上id

67
00:02:14,800 --> 00:02:16,060
还记得刚才小车的例子吗

68
00:02:16,060 --> 00:02:16,800
内名加上id

69
00:02:16,800 --> 00:02:17,860
然后呢这段

70
00:02:17,860 --> 00:02:19,500
二维 咱们是二维对象吧

71
00:02:19,500 --> 00:02:22,020
这段这里呢就是咱们的属性title other time content

72
00:02:22,020 --> 00:02:24,640
对吧 好 那我们直接进入代码的一个编写

73
00:02:27,180 --> 00:02:35,420
好 首先我们同样的 是不是假设有一个redis操作

74
00:02:35,420 --> 00:02:41,660
操作load的一个工具啊 操作load的工具呢 这里呢 老师呢 在后面会详细给大家去介绍

75
00:02:41,660 --> 00:02:42,200
 这里我们先

76
00:02:42,200 --> 00:02:48,340
大家不要担心 很简单的redis 比如说我们去引入一个redis 然后呢 去留了一个咱们的client

77
00:02:48,340 --> 00:02:52,140
 也就是redis的实例 然后呢 传入一些配置 这里呢

78
00:02:52,140 --> 00:02:54,660
我们假装传入

79
00:02:54,660 --> 00:02:56,320
假装传入 好

80
00:02:56,320 --> 00:03:05,520
首先首先我们老规矩 老规矩 我们的post是不是有个自证id 对吧 什么呢

81
00:03:05,520 --> 00:03:12,320
 client.icl 什么呢 post 咱们他的属性名呢还是叫做post count

82
00:03:12,320 --> 00:03:21,940
好 然后然后我们去定义一个缩链名 首先 比如说咱们挖一个多的是那个等于咱们缩链名就叫好了我的不要搞那么复杂

83
00:03:21,940 --> 00:03:24,160
好了我的好说那名定义之后

84
00:03:24,160 --> 00:03:25,340
我们的文章是不是还有title啊

85
00:03:25,340 --> 00:03:26,480
比如说title等于

86
00:03:26,480 --> 00:03:30,660
哈喽我的

87
00:03:30,660 --> 00:03:34,920
好title是不是还有内容啊

88
00:03:34,920 --> 00:03:36,100
那么内容这里呢

89
00:03:36,100 --> 00:03:37,560
我们就随意叉叉下写

90
00:03:37,560 --> 00:03:40,680
然后阅读量多了view0

91
00:03:40,680 --> 00:03:45,080
好那么我们一些变量已经定义了

92
00:03:45,080 --> 00:03:46,240
定义之后干嘛

93
00:03:46,240 --> 00:03:47,400
我们首先是不是要给我们

94
00:03:47,400 --> 00:03:48,460
我们再生成一篇文章的时候

95
00:03:48,460 --> 00:03:50,900
生成文章

96
00:03:50,900 --> 00:03:52,300
之前

97
00:03:52,300 --> 00:03:54,180
我们是不是要去

98
00:03:54,180 --> 00:03:55,340
教验

99
00:03:55,340 --> 00:03:56,760
缩略名

100
00:03:56,760 --> 00:03:58,180
是否可用啊

101
00:03:58,180 --> 00:03:59,380
为什么呀

102
00:03:59,380 --> 00:04:00,840
因为我们的缩略名是不是不能重复

103
00:04:00,840 --> 00:04:02,380
因为缩略名可能到时候会用于我们的

104
00:04:02,380 --> 00:04:03,700
生成我们的文章访问的地址

105
00:04:03,700 --> 00:04:05,960
咱们两篇文章肯定需要有两个地址吧

106
00:04:05,960 --> 00:04:06,180
对吧

107
00:04:06,180 --> 00:04:06,400
好

108
00:04:06,400 --> 00:04:07,720
这里我们用到一个

109
00:04:07,720 --> 00:04:08,980
比如说我们椅子

110
00:04:08,980 --> 00:04:11,240
代表了我们的缩略名是不是可用

111
00:04:11,240 --> 00:04:12,340
这里用到一个

112
00:04:12,340 --> 00:04:13,520
同学们可能之前没接受过

113
00:04:13,520 --> 00:04:14,660
一个新的一票叫做hset

114
00:04:14,660 --> 00:04:16,260
h是不是咱们超过操作闪脸的

115
00:04:16,260 --> 00:04:17,580
hash set

116
00:04:17,580 --> 00:04:18,800
hset什么呢

117
00:04:18,800 --> 00:04:19,220
nx

118
00:04:19,220 --> 00:04:21,060
nx是什么意思呢

119
00:04:21,060 --> 00:04:23,060
nx是什么意思呢

120
00:04:23,060 --> 00:04:23,660
它很简单

121
00:04:23,660 --> 00:04:24,920
属性存在

122
00:04:24,920 --> 00:04:27,120
则修改失败

123
00:04:27,120 --> 00:04:28,880
反之成功

124
00:04:28,880 --> 00:04:29,420
很简单

125
00:04:29,420 --> 00:04:30,500
如果说你去set的时候

126
00:04:30,500 --> 00:04:31,600
它这样一个属性存在

127
00:04:31,600 --> 00:04:32,680
那么你就不能修改

128
00:04:32,680 --> 00:04:33,420
如果它不存在

129
00:04:33,420 --> 00:04:34,160
就可以创建一个新的

130
00:04:34,160 --> 00:04:35,360
这点就是hset的nx

131
00:04:35,360 --> 00:04:36,060
它的一个作用

132
00:04:36,060 --> 00:04:40,220
那么我们去set的时候

133
00:04:40,220 --> 00:04:41,000
赛来什么呢

134
00:04:41,000 --> 00:04:41,540
赛来什么

135
00:04:41,540 --> 00:04:42,840
比如说我们现在

136
00:04:42,840 --> 00:04:44,240
我们现在需要去

137
00:04:44,240 --> 00:04:45,440
需要去

138
00:04:45,440 --> 00:04:47,320
我们用关系数据库去存储

139
00:04:47,320 --> 00:04:48,280
比如说我们用

140
00:04:48,280 --> 00:04:57,580
关系型数据库会额外建一张表来存储

141
00:04:57,580 --> 00:05:06,680
因为在咱们关系数据库里面会额外的用一张表来存储文章的id和咱们的缩猎名之间的映射关系

142
00:05:06,680 --> 00:05:11,340
为什么因为可能后期设计到搜索人家去搜索文章的缩猎名直接可以收到咱们的文章的列表

143
00:05:11,340 --> 00:05:13,260
但是文章列表是通过id去搜索了

144
00:05:13,260 --> 00:05:15,340
这是关系数据库它的一个设计原则

145
00:05:15,340 --> 00:05:17,680
可能后面同学们去写一些辅断代码可以去理解

146
00:05:17,680 --> 00:05:19,220
那么咱们在redis里面同样的

147
00:05:19,220 --> 00:05:20,320
也需要通过一个

148
00:05:20,320 --> 00:05:22,200
比如说我们专门去创建一个

149
00:05:22,200 --> 00:05:24,860
属性去存咱们文章的

150
00:05:24,860 --> 00:05:26,820
id和我们缩链名之间的关系

151
00:05:26,820 --> 00:05:28,240
比如说我们也通过闪链

152
00:05:28,240 --> 00:05:31,820
闪链内名叫做什么呢

153
00:05:31,820 --> 00:05:34,520
slug.toid

154
00:05:34,520 --> 00:05:36,800
专门存储

155
00:05:36,800 --> 00:05:38,560
文章的id和缩链名

156
00:05:38,560 --> 00:05:40,500
他们之间的关系

157
00:05:40,500 --> 00:05:41,540
虽然在redis里面

158
00:05:41,540 --> 00:05:42,780
我们也去额外的创造了一个

159
00:05:42,780 --> 00:05:43,720
闪链专门去存储

160
00:05:43,720 --> 00:05:44,940
维护他们之间的关系

161
00:05:44,940 --> 00:05:45,960
比如说我们走先

162
00:05:45,960 --> 00:05:49,080
h set是不是存储闪电

163
00:05:49,080 --> 00:05:51,340
nx的目的就是它存在着失败

164
00:05:51,340 --> 00:05:53,940
那么我们首先slug.qid

165
00:05:53,940 --> 00:05:55,100
第二个呢

166
00:05:55,100 --> 00:05:56,020
是不是咱们的属性名

167
00:05:56,020 --> 00:05:56,840
属性名是什么呢

168
00:05:56,840 --> 00:05:59,720
属性名是这里的属性名就存咱们的slug

169
00:05:59,720 --> 00:06:01,460
也就是咱们输入的缩链名

170
00:06:01,460 --> 00:06:02,720
然后呢再传入我们文章的id

171
00:06:02,720 --> 00:06:04,120
这里呢

172
00:06:04,120 --> 00:06:05,260
此时我们的缩链名

173
00:06:05,260 --> 00:06:08,060
此时我们的缩链名和id就

174
00:06:08,060 --> 00:06:10,340
就能一一对应了吧

175
00:06:10,340 --> 00:06:10,860
对吧

176
00:06:10,860 --> 00:06:11,180
好

177
00:06:11,180 --> 00:06:13,300
那么它的返回值

178
00:06:13,300 --> 00:06:14,340
它呢其实有一个返回值

179
00:06:14,340 --> 00:06:15,200
如果说你存储

180
00:06:15,200 --> 00:06:17,700
如果说咱们的存储失败

181
00:06:17,700 --> 00:06:18,560
也就是说我们缩链名存在

182
00:06:18,560 --> 00:06:19,160
它就返回0

183
00:06:19,160 --> 00:06:22,660
所以如果我们的意思是那个

184
00:06:22,660 --> 00:06:25,060
等于等于说明我们存储失败了

185
00:06:25,060 --> 00:06:26,720
此时咱们是不是就需要去退出

186
00:06:26,720 --> 00:06:29,440
exit会提示什么呢

187
00:06:29,440 --> 00:06:31,680
缩链名已存在

188
00:06:31,680 --> 00:06:34,020
好

189
00:06:34,020 --> 00:06:35,960
else是什么

190
00:06:35,960 --> 00:06:37,660
else就是咱们可以存储

191
00:06:37,660 --> 00:06:38,440
存储成功的一个逻辑

192
00:06:38,440 --> 00:06:40,920
这里我们就通过client.hset

193
00:06:40,920 --> 00:06:43,100
那么咱们

194
00:06:43,100 --> 00:06:44,780
这里就是咱们

195
00:06:44,780 --> 00:06:48,820
散烈存储逻辑怎么样去存怎么去存

196
00:06:48,820 --> 00:06:53,300
类名怎么去命名是不是咱们的首先类加上我们的

197
00:06:53,300 --> 00:06:58,780
是不是透多了类名加上咱们id对吧然后呢属性名比如说title

198
00:06:58,780 --> 00:07:00,080
多了

199
00:07:00,080 --> 00:07:07,020
多了title也就是咱们输入的名称这是value这个是属性名咱们的多了万六是咱们的

200
00:07:07,020 --> 00:07:10,420
属性值title比如说content然后呢

201
00:07:10,420 --> 00:07:13,780
多了content然后views

202
00:07:14,780 --> 00:07:15,880
说了

203
00:07:15,880 --> 00:07:19,960
好 这里呢我们的数据就已经存储成功了

204
00:07:19,960 --> 00:07:22,860
好 那么我们存储成功之后是不是要去读取啊

205
00:07:22,860 --> 00:07:25,700
刚才这里呢是咱们的存储

206
00:07:25,700 --> 00:07:27,920
存储逻辑

207
00:07:27,920 --> 00:07:30,140
好 咱们存储完之后是不是要读取

208
00:07:30,140 --> 00:07:31,540
怎么样读取

209
00:07:31,540 --> 00:07:32,800
怎么样去读取

210
00:07:32,800 --> 00:07:35,160
读取第一步是不是要获取ID啊

211
00:07:35,160 --> 00:07:36,060
刚才是不是讲过

212
00:07:36,060 --> 00:07:37,420
你去读取文章

213
00:07:37,420 --> 00:07:38,720
你首先知道文章ID是什么吧

214
00:07:38,720 --> 00:07:39,320
获取ID

215
00:07:39,320 --> 00:07:41,180
那么怎么去获取ID呢

216
00:07:41,180 --> 00:07:42,660
怎么需要获取ID

217
00:07:42,660 --> 00:07:43,300
哪里存的ID

218
00:07:43,300 --> 00:07:47,260
是不是我们的slug to id专门有一个缩猎名和我们id之间的印尸了呀

219
00:07:47,260 --> 00:07:50,120
所以呢咱们去挖一个post id等于

220
00:07:50,120 --> 00:07:54,140
等于什么是不是hclient.hget

221
00:07:54,140 --> 00:07:57,260
什么类名slug.to.id

222
00:07:57,260 --> 00:08:00,380
然后传入我们的什么

223
00:08:00,380 --> 00:08:01,820
传入我们的什么

224
00:08:01,820 --> 00:08:08,760
是不是传入我们所需要输入的咱们的slug

225
00:08:08,760 --> 00:08:10,520
咱们通过缩猎名去查询id

226
00:08:10,520 --> 00:08:11,740
那么查询到之后

227
00:08:13,900 --> 00:08:16,080
查询到之后 这里是不是还会有一个判断呢

228
00:08:16,080 --> 00:08:22,720
首先 如果说你的id不存在 是不是咱们需要去退出啊 点exit

229
00:08:22,720 --> 00:08:27,660
提示 文章不存在 好 那么如果成功呢 else

230
00:08:27,660 --> 00:08:33,320
如果成功呢 成功 我们就可以到我们的闪电里面去读取了吧 比如说我们去定义

231
00:08:33,320 --> 00:08:35,160
 我们的文章对象等于

232
00:08:35,160 --> 00:08:39,540
client点 怎么读取 怎么读取 是不是i取get什么

233
00:08:40,620 --> 00:08:43,060
是get war呀 因为我们要把文章所有的属性全部给读到

234
00:08:43,060 --> 00:08:44,340
get war

235
00:08:44,340 --> 00:08:47,780
post post 是不是我们的id呀

236
00:08:47,780 --> 00:08:49,840
多了 post

237
00:08:49,840 --> 00:08:53,420
多了 post id

238
00:08:53,420 --> 00:08:54,700
然后呢

239
00:08:54,700 --> 00:08:59,420
没了吧 因为我们是get war 只需要传内名就可以了 我们属性名不用传 这里呢

240
00:08:59,420 --> 00:09:01,360
 他会接受一个毁掉 为什么呀 之前

241
00:09:01,360 --> 00:09:04,340
之前我们去get war的时候是不是他会在命令行里面一条一条的打印出来

242
00:09:04,340 --> 00:09:08,400
 但是我们需要把它给转成对象嘛 实际上我们在nodejs redis cool 他会去封装

243
00:09:08,820 --> 00:09:12,920
后面的我们会去讲到 比如说我们把这样一个data给打印出来 就会把咱们文章所有的属性

244
00:09:12,920 --> 00:09:15,660
 比如说title content wills 都会挂在咱们的data上面

245
00:09:15,660 --> 00:09:17,960
这里是nodejs 帮我们自动去处理的这样一个包

246
00:09:17,960 --> 00:09:20,100
后面我们会讲 这里是读取的逻辑

247
00:09:20,100 --> 00:09:24,840
那么读取之后我们是不是还有一个需求啊 是什么 是不是修改 我们还会修改缩裂名吧

248
00:09:24,840 --> 00:09:26,940
假如说我们需要去修改缩裂名

249
00:09:26,940 --> 00:09:32,210
说缩裂名怎么去感觉 比如说我们去挖一个new的slug等于比如说我们瞎写

250
00:09:32,210 --> 00:09:33,220
 搞一个新的

251
00:09:34,180 --> 00:09:38,280
那么搞一个新的 首先我们是不是还是需要去判断一下 你这个缩链名可不可用啊

252
00:09:38,280 --> 00:09:38,540
 比如说

253
00:09:38,540 --> 00:09:42,500
z 是不是等于我们的client.hznx

254
00:09:42,500 --> 00:09:49,100
第一个参数是不是是那个.q.id 第二个呢 第二个

255
00:09:49,100 --> 00:09:52,940
是不是我们的newslog 然后呢 然后

256
00:09:52,940 --> 00:09:58,500
然后是不是我们的id呀 比如说 我们要去修改id为42的文章的缩链名 此时如果说

257
00:10:00,300 --> 00:10:06,180
如果说我们他不存在不存在 走 我们是不是要去提示啊 我们是不是要提示

258
00:10:06,180 --> 00:10:07,460
缩链名

259
00:10:07,460 --> 00:10:09,520
已经存在了

260
00:10:09,520 --> 00:10:12,080
那么

261
00:10:12,080 --> 00:10:15,400
那么如果不存在了 是不是就可以存 是不是可以存对吧

262
00:10:15,400 --> 00:10:19,760
好 怎么要去存呢 是不是client.hset

263
00:10:19,760 --> 00:10:23,080
在什么post42吧

264
00:10:23,080 --> 00:10:24,620
slug

265
00:10:24,620 --> 00:10:28,460
他有一个缩链名属性 是不是要存储为咱们的6

266
00:10:28,980 --> 00:10:35,420
是那个对吧 好 那么此时是不是还需要做一件什么事情 是不是我们要把旧的旧的咱们是那个tud的索引给拴掉啊

267
00:10:35,420 --> 00:10:38,280
 对吧 那么这里呢 我们需要去获取一下旧的 比如说 old

268
00:10:38,280 --> 00:10:42,380
是那个等于什么等于什么 是不是client.hget

269
00:10:42,380 --> 00:10:44,860
host

270
00:10:44,860 --> 00:10:47,640
是啊 是那个呀

271
00:10:47,640 --> 00:10:52,740
那么我们获取是不是应该在之前了 同学们 因为咱们要set咱们先

272
00:10:52,740 --> 00:10:55,020
先获取

273
00:10:55,020 --> 00:10:58,640
旧的 然后呢 负值

274
00:10:58,980 --> 00:11:03,700
新的 然后是不是要栓掉 把以前旧的给栓掉 咱们client.hd

275
00:11:03,700 --> 00:11:07,600
栓谁 栓谁 是不是栓咱们的映射关系啊

276
00:11:07,600 --> 00:11:11,360
是不是 因为刚才我们在闪电里面把它已经重新给复制了 所以不用去栓

277
00:11:11,360 --> 00:11:13,820
 我们只需要把slug2id的 旧的栓的名给栓掉

278
00:11:13,820 --> 00:11:16,540
slug.2.id

279
00:11:16,540 --> 00:11:18,700
然后呢 是不是我们的

280
00:11:18,700 --> 00:11:21,500
oldslug 栓掉就可以了

281
00:11:21,500 --> 00:11:26,180
好 这里呢 就是我们这样一段 通过散电去重构我们文章 这样一个需求的

282
00:11:26,740 --> 00:11:31,260
逻辑 这里老师来带同学们来回顾一下 首先我们的存储 我们的存储是不是通过

283
00:11:31,260 --> 00:11:35,900
通过 而去赛的nx这样一个api去确认你这样一个缩列名到底存不存在

284
00:11:35,900 --> 00:11:39,840
而且呢 我们需要专门用一个slug去id去存储文章id和缩列名之间的关系

285
00:11:39,840 --> 00:11:41,200
 为什么 因为我们会做搜索

286
00:11:41,200 --> 00:11:45,800
那所以说需要对缩列名和id之间建立一个联系 如果说存在 存在咱们就退出

287
00:11:45,800 --> 00:11:47,900
 如果不存在 我们就去创建我们的文章

288
00:11:47,900 --> 00:11:51,620
通过闪念 咱们的闪电类名pose加上id 然后呢去存储

289
00:11:51,620 --> 00:11:55,480
他的一些信息 这里呢 我可能写错了 我们是

290
00:11:56,420 --> 00:12:02,420
i.hmset 因为我们是多重的属性 对吧 我们是这个多次多个属性 那么读取了

291
00:12:02,420 --> 00:12:07,900
读取是我们直接通过咱们的slug to id 我们缩链名和和咱们文章的印象关系去群寻找

292
00:12:07,900 --> 00:12:08,060
 id

293
00:12:08,060 --> 00:12:13,310
找不到文章不存在 如果找到了咱们通过hget war把它给读取到 读取到之后

294
00:12:13,310 --> 00:12:15,080
 是不是漏的s他会去封装 对吧

295
00:12:15,080 --> 00:12:17,780
漏的会封装 这里后面的内容会介绍

296
00:12:17,780 --> 00:12:21,420
好 封装之后咱们是不是反对象就可以去读取了 咱们修改缩链名呢

297
00:12:21,960 --> 00:12:23,160
比如说你要有新的数量名

298
00:12:23,160 --> 00:12:23,960
我们首先看一下

299
00:12:23,960 --> 00:12:24,700
这个数量能不能用

300
00:12:24,700 --> 00:12:25,820
好 现在翻译可用

301
00:12:25,820 --> 00:12:27,560
可用的话我们是不是要把旧的给找到

302
00:12:27,560 --> 00:12:28,120
旧的给找到

303
00:12:28,120 --> 00:12:29,120
然后去复制新的

304
00:12:29,120 --> 00:12:32,220
最后把旧的在slugkyd的映射表里面给删掉

305
00:12:32,220 --> 00:12:34,060
好 这里就是我们文章

306
00:12:34,060 --> 00:12:37,120
文章通过闪电去重构的一个内容

307
00:12:37,120 --> 00:12:39,600
我们先把视频停一下

