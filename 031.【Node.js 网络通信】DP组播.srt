1
00:00:00,000 --> 00:00:04,020
接下来我们来看一下如何在Node中来实现UDP组播

2
00:00:04,020 --> 00:00:06,060
那么首先从服务端这里

3
00:00:06,060 --> 00:00:07,640
服务端这里发送组播数据

4
00:00:07,640 --> 00:00:09,660
只需要在IP地址这里呢

5
00:00:09,660 --> 00:00:12,940
去为指定的组播IP来发送消息就可以了

6
00:00:12,940 --> 00:00:14,960
不需要再做其他任何额外的设置

7
00:00:14,960 --> 00:00:17,380
然后对于客户端来讲

8
00:00:17,380 --> 00:00:19,620
我们可以看到只需要在客户端当中

9
00:00:19,620 --> 00:00:21,320
在客户端真听成功以后

10
00:00:21,320 --> 00:00:24,220
如果说你需要来接收这个组播数据

11
00:00:24,220 --> 00:00:25,240
那么在这里呢

12
00:00:25,240 --> 00:00:27,600
就需要调用这个AdMembership这个方法

13
00:00:27,600 --> 00:00:29,540
来加入指定的IP组播组

14
00:00:29,540 --> 00:00:30,500
那么这样的话

15
00:00:30,500 --> 00:00:31,580
你此时这个客户端

16
00:00:31,580 --> 00:00:33,300
就会收到这个组博数据

17
00:00:33,300 --> 00:00:35,400
那么同样的

18
00:00:35,400 --> 00:00:36,800
如果说你这个服务端

19
00:00:36,800 --> 00:00:38,520
自己也来加入这个组博组当中

20
00:00:38,520 --> 00:00:40,540
那么服务端本身也是可以收到

21
00:00:40,540 --> 00:00:41,940
自己发送的这个组博数据的

22
00:00:41,940 --> 00:00:43,320
都是可以的

23
00:00:43,320 --> 00:00:44,080
那么接下来

24
00:00:44,080 --> 00:00:45,240
我们就把这个代码

25
00:00:45,240 --> 00:00:46,940
在我们这个来实现

26
00:00:46,940 --> 00:00:47,860
去给大家演示一下

27
00:00:47,860 --> 00:00:50,140
回到我们的便利器当中

28
00:00:50,140 --> 00:00:51,140
回到便利器当中

29
00:00:51,140 --> 00:00:53,260
首先我们来到SURR这里

30
00:00:53,260 --> 00:00:54,600
来到SURR这里以后

31
00:00:54,600 --> 00:00:55,660
所以我们这个呢

32
00:00:55,660 --> 00:00:56,520
还是我们刚才这个

33
00:00:56,520 --> 00:00:58,240
最简单的数据代码

34
00:00:58,240 --> 00:01:00,720
好 那么我们只需要在这个sora中

35
00:01:00,720 --> 00:01:01,900
当他listening

36
00:01:01,900 --> 00:01:03,920
也就是坚定启动成功以后

37
00:01:03,920 --> 00:01:05,560
然后接下来我们让他去发消息

38
00:01:05,560 --> 00:01:06,640
好 那么这个发消息

39
00:01:06,640 --> 00:01:07,740
我们可以还是同样的

40
00:01:07,740 --> 00:01:09,660
例如我们让他每两秒就来发一条消息

41
00:01:09,660 --> 00:01:12,280
好 那么此时我们就在这里去sora.stand

42
00:01:12,280 --> 00:01:14,280
好 那么我们还是来发送一个hello

43
00:01:14,280 --> 00:01:16,100
好 然后在里面

44
00:01:16,100 --> 00:01:17,960
我们要发送到端口号

45
00:01:17,960 --> 00:01:20,160
例如我们还是来指定为一个8000

46
00:01:20,160 --> 00:01:21,220
好 完了以后

47
00:01:21,220 --> 00:01:21,980
那么这个地址

48
00:01:21,980 --> 00:01:24,680
注意 我们这里要使用一个主播地址

49
00:01:24,680 --> 00:01:25,640
例如我们这里

50
00:01:25,640 --> 00:01:28,060
以我们在案例当中

51
00:01:28,060 --> 00:01:29,180
以这个地址为例

52
00:01:29,180 --> 00:01:30,580
那么这个是就是一个

53
00:01:30,580 --> 00:01:31,740
可以使用的祖博地址

54
00:01:31,740 --> 00:01:33,300
224.0.1.100

55
00:01:33,300 --> 00:01:34,100
好

56
00:01:34,100 --> 00:01:35,680
我们把这个地址给他放到这里

57
00:01:35,680 --> 00:01:36,940
那么现在的话呢

58
00:01:36,940 --> 00:01:38,420
就是相当于我每隔两秒

59
00:01:38,420 --> 00:01:41,080
就向这个祖博发送了一条数据

60
00:01:41,080 --> 00:01:41,680
就发一个hello

61
00:01:41,680 --> 00:01:43,680
那么服务端这里写好以后

62
00:01:43,680 --> 00:01:45,440
然后接下来回到这个client

63
00:01:45,440 --> 00:01:46,580
来到这个客户端这里

64
00:01:46,580 --> 00:01:47,840
那么客户端这里的话

65
00:01:47,840 --> 00:01:51,100
他只需要在收到这条消息的时候

66
00:01:51,100 --> 00:01:53,240
注意我们不需要让他去发消息了

67
00:01:53,240 --> 00:01:55,060
我们只需要让他收到这个消息的时候

68
00:01:55,060 --> 00:01:56,260
在这里去输出一下

69
00:01:56,260 --> 00:01:57,680
当然不要忘了一件事

70
00:01:57,680 --> 00:02:00,060
我们要在他的这个listening事件当中

71
00:02:00,060 --> 00:02:00,960
我们在这里

72
00:02:00,960 --> 00:02:04,080
然后让他来加入这个组播组

73
00:02:04,080 --> 00:02:07,120
那么这个方法就是client add member

74
00:02:07,120 --> 00:02:09,760
那么这个地址呢

75
00:02:09,760 --> 00:02:11,700
就是我们刚才所看到的这个地址

76
00:02:11,700 --> 00:02:12,440
那么这样的话呢

77
00:02:12,440 --> 00:02:14,060
就把它加入了这个组播组

78
00:02:14,060 --> 00:02:16,220
也就是这个地址组播组当中了

79
00:02:16,220 --> 00:02:17,820
那么写完以后

80
00:02:17,820 --> 00:02:19,260
然后接下来回到这个命令行当中

81
00:02:19,260 --> 00:02:20,820
我们来把这个脚本来测试一下

82
00:02:20,820 --> 00:02:21,980
打开命令行

83
00:02:21,980 --> 00:02:23,000
打开命令行

84
00:02:23,000 --> 00:02:23,880
首先我们

85
00:02:23,880 --> 00:02:27,280
来到我们的UDP主播当中

86
00:02:27,280 --> 00:02:29,860
首先我们在这里启动这个SOR

87
00:02:29,860 --> 00:02:32,020
SOR启动成功以后

88
00:02:32,020 --> 00:02:32,820
然后接下来

89
00:02:32,820 --> 00:02:36,460
我们CT到进入我们的UDP主播这个目录

90
00:02:36,460 --> 00:02:37,940
然后接下来我们来执行Client

91
00:02:37,940 --> 00:02:41,340
我们看一下他能否收到这个主播消息

92
00:02:41,340 --> 00:02:42,820
然后这个时候我们确实看到

93
00:02:42,820 --> 00:02:44,200
每隔两秒我们就收到了

94
00:02:44,200 --> 00:02:46,660
来自于这个3000端口的主播员

95
00:02:46,660 --> 00:02:48,920
所发送过来的这个Hello这条消息数据

96
00:02:48,920 --> 00:02:50,280
那么此时就证明我们在这里

97
00:02:50,280 --> 00:02:51,860
主播就正常的收到了

98
00:02:51,860 --> 00:02:53,260
那么同样的

99
00:02:53,260 --> 00:02:53,900
我们为了更好的

100
00:02:53,900 --> 00:02:55,140
再进一步进行测试

101
00:02:55,140 --> 00:02:57,140
我们来到我们刚才的

102
00:02:57,140 --> 00:02:58,680
这个虚拟机环境当中

103
00:02:58,680 --> 00:02:59,460
我们在这里的话

104
00:02:59,460 --> 00:03:01,140
我们也让它来进入我们这个

105
00:03:01,140 --> 00:03:02,680
来执行这个UDP主播

106
00:03:02,680 --> 00:03:03,680
中的这个Client

107
00:03:03,680 --> 00:03:04,780
也就是说我们看一下

108
00:03:04,780 --> 00:03:06,700
同处于一个网段当中

109
00:03:06,700 --> 00:03:07,560
那么这个机器

110
00:03:07,560 --> 00:03:08,600
它是否能收到

111
00:03:08,600 --> 00:03:09,900
就是主播数据呢

112
00:03:09,900 --> 00:03:11,400
我们在这里执行

113
00:03:11,400 --> 00:03:12,120
执行成功以后

114
00:03:12,120 --> 00:03:12,700
我们可以看到

115
00:03:12,700 --> 00:03:13,660
这里也是一样的

116
00:03:13,660 --> 00:03:14,200
每隔两面

117
00:03:14,200 --> 00:03:15,760
就可以收到来自于

118
00:03:15,760 --> 00:03:16,580
这个IPD之为

119
00:03:16,580 --> 00:03:19,020
**************.3000端口的

120
00:03:19,020 --> 00:03:19,580
这个服务

121
00:03:19,580 --> 00:03:21,420
所发过来的主播数据

122
00:03:21,420 --> 00:03:23,980
所以说这个就证明

123
00:03:23,980 --> 00:03:24,820
我们这个祖博

124
00:03:24,820 --> 00:03:26,840
开了一个简单的实现

125
00:03:26,840 --> 00:03:27,560
在这就OK了

126
00:03:27,560 --> 00:03:28,840
那么重点的话呢

127
00:03:28,840 --> 00:03:29,540
就是首先

128
00:03:29,540 --> 00:03:31,260
你发送的这条数据

129
00:03:31,260 --> 00:03:32,820
首先是祖博地址

130
00:03:32,820 --> 00:03:34,260
地址要写祖博地址

131
00:03:34,260 --> 00:03:35,680
那么其他想收到

132
00:03:35,680 --> 00:03:37,240
这个祖博数据的客户端

133
00:03:37,240 --> 00:03:38,320
都需要通过

134
00:03:38,320 --> 00:03:40,220
在Listening启动成功以后

135
00:03:40,220 --> 00:03:41,400
来调用这个就是

136
00:03:41,400 --> 00:03:42,960
AdMembership这个方法

137
00:03:42,960 --> 00:03:44,940
来加入指定的祖博组

138
00:03:44,940 --> 00:03:46,120
当然如果说

139
00:03:46,120 --> 00:03:47,260
你想退出这个祖博组

140
00:03:47,260 --> 00:03:48,580
它也是可以的

141
00:03:48,580 --> 00:03:49,540
那么有兴趣的同学呢

142
00:03:49,540 --> 00:03:50,660
可以去查阅它的API

143
00:03:50,660 --> 00:03:51,900
去自行测试

144
00:03:51,900 --> 00:03:53,540
那么关于这个UDP

145
00:03:53,540 --> 00:03:54,820
主播的一个简单实现

146
00:03:54,820 --> 00:03:55,860
我们就说到这里

