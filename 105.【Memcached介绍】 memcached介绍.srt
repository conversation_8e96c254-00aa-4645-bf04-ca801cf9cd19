1
00:00:00,000 --> 00:00:03,420
好,朋友们大家好,这节课我们就来看一下我们的第三章的内容,Memcast

2
00:00:03,420 --> 00:00:09,280
其实我们之前是不是讲过,Memcast它和Redis是一样的,都是我们的内存型数据库,而且是非关系型数据库

3
00:00:09,280 --> 00:00:13,260
那么我们呢,其实重点要放在Redis上面,为什么呀

4
00:00:13,260 --> 00:00:17,620
因为Memcast它本质上是一个简洁的KValue存储系统,什么意思呢

5
00:00:17,620 --> 00:00:20,800
说明在Memcast里面它只有自不创造一种类型

6
00:00:20,800 --> 00:00:24,220
它没有什么集合、闪列,也没有我们的列宾

7
00:00:24,220 --> 00:00:28,460
那么我们Redis为什么方便,是不是正是因为它有集合、有闪列

8
00:00:28,460 --> 00:00:29,980
我们可以去求一些交集

9
00:00:29,980 --> 00:00:30,740
求一些并解

10
00:00:30,740 --> 00:00:31,680
而且可以去排序

11
00:00:31,680 --> 00:00:33,120
那么Memcast相比Redis

12
00:00:33,120 --> 00:00:34,540
它的功能其实会弱很多

13
00:00:34,540 --> 00:00:36,420
所以同学们只需要去了解一下

14
00:00:36,420 --> 00:00:38,160
Memcast到底是什么就可以了

15
00:00:38,160 --> 00:00:39,420
因为Memcast它的出生

16
00:00:39,420 --> 00:00:40,120
是不是与Redis早

17
00:00:40,120 --> 00:00:42,440
但是Redis已经可以说是完全超越了它

18
00:00:42,440 --> 00:00:44,120
好

19
00:00:44,120 --> 00:00:45,980
我们来看一下Memcast它的安装

20
00:00:45,980 --> 00:00:47,800
如果说同学们你在Mac系统里面

21
00:00:47,800 --> 00:00:48,740
只需一行命令即可

22
00:00:48,740 --> 00:00:50,260
Brain Install Memcast

23
00:00:50,260 --> 00:00:52,200
如果说你是Windows系统的话

24
00:00:52,200 --> 00:00:53,700
同学们可以自己去看一下

25
00:00:53,700 --> 00:00:54,720
怎么去安装都很简单

26
00:00:54,720 --> 00:00:56,260
我们之前是不是安装过Redis

27
00:00:56,260 --> 00:00:57,260
那么你会安装Redis

28
00:00:57,260 --> 00:00:58,800
相信你一定会安装咱们的Memocache

29
00:00:58,800 --> 00:01:00,820
那么接下来我们来看一下API

30
00:01:00,820 --> 00:01:01,820
其实这里呢

31
00:01:01,820 --> 00:01:02,740
只是带同学了解一下

32
00:01:02,740 --> 00:01:06,040
这里呢我就不去具体的去敲咱们的一个命令

33
00:01:06,040 --> 00:01:07,120
其实在Memocache里面

34
00:01:07,120 --> 00:01:08,900
它核心的API其实只有几个非常简单

35
00:01:08,900 --> 00:01:10,260
它可以说比Redis简单多了

36
00:01:10,260 --> 00:01:10,400
为什么

37
00:01:10,400 --> 00:01:11,980
因为它刚才我们是不是讲过

38
00:01:11,980 --> 00:01:13,020
它只有一种数据结构

39
00:01:13,020 --> 00:01:14,180
也就是我们的KV

40
00:01:14,180 --> 00:01:15,480
也就是我们的自创内型

41
00:01:15,480 --> 00:01:16,260
好

42
00:01:16,260 --> 00:01:17,640
那么我们就来看一下它到底

43
00:01:17,640 --> 00:01:20,160
它到底有哪些API

44
00:01:20,160 --> 00:01:20,780
比如说SET

45
00:01:20,780 --> 00:01:21,780
SET是不是我们存储数据

46
00:01:21,780 --> 00:01:24,360
那么SET命令将用于将Value的

47
00:01:24,360 --> 00:01:26,480
将Value的值存储在指定的K中

48
00:01:26,480 --> 00:01:28,020
其实就是ky6的存储

49
00:01:28,020 --> 00:01:30,360
那么如果说咱们set的k已经存在

50
00:01:30,360 --> 00:01:31,700
那么这个命令就可以去更新

51
00:01:31,700 --> 00:01:32,520
该可以原来的数据

52
00:01:32,520 --> 00:01:33,340
说白了什么意思啊

53
00:01:33,340 --> 00:01:35,080
set它既可以去从我们的新数据

54
00:01:35,080 --> 00:01:36,140
也可以去修改我们的旧数据

55
00:01:36,140 --> 00:01:36,420
对吧

56
00:01:36,420 --> 00:01:37,360
那么我们来看一下

57
00:01:37,360 --> 00:01:39,200
去set的时候咱们参数有哪些不一样

58
00:01:39,200 --> 00:01:40,760
比如说我们去set一个k

59
00:01:40,760 --> 00:01:41,720
k是我们的件子吧

60
00:01:41,720 --> 00:01:42,920
包括还有一个属性flug

61
00:01:42,920 --> 00:01:43,820
flug是什么呢

62
00:01:43,820 --> 00:01:45,800
它可以包括件子对的整形参数

63
00:01:45,800 --> 00:01:47,420
客户机使用它存储

64
00:01:47,420 --> 00:01:48,800
关于件子对的额外信息

65
00:01:48,800 --> 00:01:49,720
其实一般是没有了

66
00:01:49,720 --> 00:01:50,380
我们一般会去

67
00:01:50,380 --> 00:01:52,740
一般会去给个0就可以了

68
00:01:52,740 --> 00:01:54,300
比如说我们去下面set的一个

69
00:01:54,300 --> 00:02:00,580
设定一个K 然后呢咱们flux是不是0了 第三个参数是900 那么这个900是什么这个900是不是

70
00:02:00,580 --> 00:02:07,180
XX time 这里呢其实就是过期时间 那么在redis里面过期时间是什么 是不是expire啊

71
00:02:07,180 --> 00:02:09,200
 其实这里稍微慢慢看起来和redis里面有一些区别

72
00:02:09,200 --> 00:02:11,920
在这里呢 他叫做XP time

73
00:02:11,920 --> 00:02:12,820
好

74
00:02:12,820 --> 00:02:14,380
900也就是说呢

75
00:02:14,380 --> 00:02:19,140
也就是说900秒之后过期900秒多少时间呢 900秒也就是好像是15分钟左右

76
00:02:19,140 --> 00:02:19,400
 对吧

77
00:02:19,700 --> 00:02:23,280
9呢,9是什么?9是不是byte,在缓存中存储的字节数

78
00:02:23,280 --> 00:02:26,860
我们可以数一下,我们去存储的时候,man卡写这样一个字不串

79
00:02:26,860 --> 00:02:28,660
比如说我们去存储

80
00:02:28,660 --> 00:02:31,220
man卡写这样一个字不串,它长度是不是9啊

81
00:02:31,220 --> 00:02:34,540
123456789,所以呢,咱们闯入一个byte 9

82
00:02:34,540 --> 00:02:38,380
这里呢,其实和咱们的reddit的set是不是非常的类似啊,只是说它的一个

83
00:02:38,380 --> 00:02:42,480
expire,这样一个参数,是不是稍微有点不一样

84
00:02:42,480 --> 00:02:44,280
好,我们再来看一下第二个命令,add

85
00:02:44,280 --> 00:02:47,340
add是什么呢,咱们刚才是不是讲的set

86
00:02:47,860 --> 00:02:49,900
sat 接下来我们来看一下add

87
00:02:49,900 --> 00:02:53,740
在mapcast中 add命名将用于

88
00:02:53,740 --> 00:02:56,060
将value的值存储在指定的

89
00:02:56,060 --> 00:02:57,080
k中

90
00:02:57,080 --> 00:03:01,940
什么意思 我们sat是不是也可以将咱们的value存在指定的k中啊 那么add是做什么的呢

91
00:03:01,940 --> 00:03:09,100
大家可以看一下解释 如果说add的k已经存在 则不会更新数据 之前的值将仍然保持相同

92
00:03:09,100 --> 00:03:10,640
 并且能将获得响应

93
00:03:10,640 --> 00:03:12,180
什么意思

94
00:03:12,180 --> 00:03:13,200
什么意思

95
00:03:13,200 --> 00:03:17,820
说明add在一条命令是不是只能够存心值啊 诚学们

96
00:03:17,860 --> 00:03:19,660
只能够存心值吧

97
00:03:19,660 --> 00:03:20,420
为什么呀

98
00:03:20,420 --> 00:03:21,860
因为如果说你的k已经存在

99
00:03:21,860 --> 00:03:22,560
则不会更新数据

100
00:03:22,560 --> 00:03:24,600
也就是说你的k存在就会更新失败

101
00:03:24,600 --> 00:03:27,200
你只有k不存在才能够去实行吧

102
00:03:27,200 --> 00:03:27,640
对吧

103
00:03:27,640 --> 00:03:28,140
说明

104
00:03:28,140 --> 00:03:31,840
只能存心值

105
00:03:31,840 --> 00:03:33,980
没有修改功能吧

106
00:03:33,980 --> 00:03:35,360
好

107
00:03:35,360 --> 00:03:36,680
那我们来看一下第三个命令

108
00:03:36,680 --> 00:03:37,320
replace

109
00:03:37,320 --> 00:03:39,340
replace咱们看它的字面意思是不是修改

110
00:03:39,340 --> 00:03:41,480
那么我们来看一下它解释

111
00:03:41,480 --> 00:03:42,960
我们之前是不是讲过set

112
00:03:42,960 --> 00:03:43,640
set这样一个命令

113
00:03:43,640 --> 00:03:44,820
它是不是也可以去修改就职

114
00:03:44,820 --> 00:03:45,620
那么我们的replace

115
00:03:45,620 --> 00:03:46,680
它到底是做什么用的呢

116
00:03:46,680 --> 00:03:48,060
首先它肯定是替换

117
00:03:48,060 --> 00:03:49,420
那么大家可以注意

118
00:03:49,420 --> 00:03:50,720
大家可以注意这样一个条件

119
00:03:50,720 --> 00:03:53,220
如果k不存在则替换失败

120
00:03:53,220 --> 00:03:55,460
如果k不存在则替换失败

121
00:03:55,460 --> 00:03:56,540
什么意思

122
00:03:56,540 --> 00:03:59,260
它是不是和咱们的add是反的呀

123
00:03:59,260 --> 00:04:00,740
add如果存在就会失败

124
00:04:00,740 --> 00:04:01,660
那么replace呢

125
00:04:01,660 --> 00:04:03,360
它是不存在则会失败

126
00:04:03,360 --> 00:04:04,360
你替换替换

127
00:04:04,360 --> 00:04:05,240
如果说你一个字不存在

128
00:04:05,240 --> 00:04:06,040
拿什么去替换呢

129
00:04:06,040 --> 00:04:06,260
对吧

130
00:04:06,260 --> 00:04:07,300
这里呢就是咱们的replace

131
00:04:07,300 --> 00:04:08,340
它和咱们set的一个区别

132
00:04:08,340 --> 00:04:09,880
我们再来看一下第四个命令的pand

133
00:04:09,880 --> 00:04:11,500
pand其实就很好理解

134
00:04:11,500 --> 00:04:13,940
它呢用于像已存在的k

135
00:04:13,940 --> 00:04:16,300
value值后面追加数据

136
00:04:16,300 --> 00:04:18,260
是不是就和咱们的html的append一样啊

137
00:04:18,260 --> 00:04:20,000
在咱们的一个dome节点后面去添加一些数据

138
00:04:20,000 --> 00:04:21,040
你比如说你去存储了一些自传

139
00:04:21,040 --> 00:04:22,400
我在自传后面再去添加一些

140
00:04:22,400 --> 00:04:23,660
咱们就可以用append

141
00:04:23,660 --> 00:04:25,460
第五个命令呢是什么

142
00:04:25,460 --> 00:04:26,940
prepend

143
00:04:26,940 --> 00:04:27,660
prepend

144
00:04:27,660 --> 00:04:28,440
更好理解

145
00:04:28,440 --> 00:04:31,000
在前面推荐数据和咱们append是相反的

146
00:04:31,000 --> 00:04:31,360
好

147
00:04:31,360 --> 00:04:32,840
这里呢就是咱们mancard写的内容

148
00:04:32,840 --> 00:04:33,360
其实很简单

149
00:04:33,360 --> 00:04:33,960
我们这节课呢

150
00:04:33,960 --> 00:04:35,360
主要就是介绍一下什么是mancard

151
00:04:35,360 --> 00:04:37,500
而且呢去介绍一下他的一些核心的api

152
00:04:37,500 --> 00:04:40,060
其实咱们mancard写他的内容是非常少的

153
00:04:40,060 --> 00:04:41,840
所以呢我们这里来总结一下

154
00:04:41,840 --> 00:04:44,240
咱们刚才所讲解Memkash的那些内容

155
00:04:44,240 --> 00:04:50,600
这里是不是第三章

156
00:04:50,600 --> 00:04:51,640
内容是比较少的

157
00:04:51,640 --> 00:04:54,200
因为Memkash确实我们了解够了

158
00:04:54,200 --> 00:04:58,000
首先我们刚才是不是讲解了Memkash的一个介绍

159
00:04:58,000 --> 00:04:59,680
我们核心是什么

160
00:04:59,680 --> 00:05:01,800
它是什么类型的Memkash

161
00:05:01,800 --> 00:05:04,040
是不是只有

162
00:05:04,040 --> 00:05:05,840
KValue

163
00:05:05,840 --> 00:05:07,640
一种类型吧

164
00:05:07,640 --> 00:05:09,280
而且呢

165
00:05:09,280 --> 00:05:10,200
而且什么

166
00:05:10,200 --> 00:05:11,240
是不是已经

167
00:05:11,840 --> 00:05:15,940
已经被radis超越吧

168
00:05:15,940 --> 00:05:16,880
其实我们了解就可以了

169
00:05:16,880 --> 00:05:18,300
我们的核心战场还在radis上面

170
00:05:18,300 --> 00:05:19,800
而且呢

171
00:05:19,800 --> 00:05:20,900
我们还讲了什么

172
00:05:20,900 --> 00:05:22,640
他的一些核心API有哪些啊

173
00:05:22,640 --> 00:05:24,080
首先第一个set

174
00:05:24,080 --> 00:05:25,580
set它的特性是什么

175
00:05:25,580 --> 00:05:30,100
有什么特性是不是可以负值啊

176
00:05:30,100 --> 00:05:32,080
而且呢新旧值都可以吧

177
00:05:32,080 --> 00:05:33,640
也就是说既可以负新值又可以改旧值

178
00:05:33,640 --> 00:05:36,600
我们还学了什么是不是add

179
00:05:36,600 --> 00:05:38,600
add是什么意思

180
00:05:38,600 --> 00:05:41,500
是不是只能负新值

181
00:05:41,500 --> 00:05:47,820
对吧 你不能改旧值吧 replace呢 是不是和add相反 只能改旧值吧 为什么呀

182
00:05:47,820 --> 00:05:49,820
 因为你个旨都不存在 你拿什么去替换它 对吧

183
00:05:49,820 --> 00:05:53,310
比如说现在已旧换新的活动 你连旧的都没有 你拿什么换新的 对吧

184
00:05:53,310 --> 00:05:56,180
 好 我们再来看一下 刚才是不是还学习一个

185
00:05:56,180 --> 00:05:58,100
pend 这一个命令 是不是追价

186
00:05:58,100 --> 00:06:04,100
追价 很简单 这里就不去介绍 包括呢 相反的还学的什么 propend 吧

187
00:06:04,100 --> 00:06:07,820
pend 是什么意思 是不是在前面追价啊 向前追价

188
00:06:09,060 --> 00:06:11,160
好 这里就是man.com写汤全部的内容

189
00:06:11,160 --> 00:06:13,320
好 这里就是我们这几份的内容

