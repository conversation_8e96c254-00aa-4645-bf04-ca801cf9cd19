1
00:00:00,000 --> 00:00:01,880
好 这一节课呢我们就来看一下

2
00:00:01,880 --> 00:00:04,880
我们1GG它里面的一个异常捕获是怎么样的

3
00:00:04,880 --> 00:00:06,520
那么什么是异常捕获呢 其实就是错误处理

4
00:00:06,520 --> 00:00:09,620
那么错误处理呢其实对我们的项目来讲是非常重要的

5
00:00:09,620 --> 00:00:12,580
我们并不是说我们写好了API写好了接口我们的任务就完成了

6
00:00:12,580 --> 00:00:15,020
那么这里呢是初级和中级工程师去做的事情

7
00:00:15,020 --> 00:00:18,600
那么如果说我们想去未来进阶层成为一名前端的一个资深工程师

8
00:00:18,600 --> 00:00:20,580
包括移民 潜当专家

9
00:00:20,580 --> 00:00:21,620
那么你一定要去

10
00:00:21,620 --> 00:00:22,520
第一步就是什么呢

11
00:00:22,520 --> 00:00:23,640
把错误处理给做好

12
00:00:23,640 --> 00:00:26,240
因为它能保障你项目的一个稳定性

13
00:00:26,240 --> 00:00:28,380
那么错误处理呢

14
00:00:28,380 --> 00:00:30,260
它最常用的一个方法是什么呢

15
00:00:30,260 --> 00:00:31,100
就是通过TryCatch

16
00:00:31,100 --> 00:00:33,280
那么TryCatch什么情况下会使用呢

17
00:00:33,280 --> 00:00:35,060
我们所有的地方都需要去用TryCatch

18
00:00:35,060 --> 00:00:36,640
这也是一些新手

19
00:00:36,640 --> 00:00:38,200
包括一些刚入行的潜当工程师

20
00:00:38,200 --> 00:00:39,520
他很难去理解的一个问题

21
00:00:39,520 --> 00:00:40,300
其实这里呢

22
00:00:40,300 --> 00:00:41,940
我们可以这么去做

23
00:00:41,940 --> 00:00:43,400
你只要觉得你的代码有风险

24
00:00:43,400 --> 00:00:44,740
你就可以使用TryCatch

25
00:00:44,740 --> 00:00:46,540
这样就可以一定程度上面

26
00:00:46,540 --> 00:00:47,760
来保障你项目的一个稳定性

27
00:00:47,760 --> 00:00:49,840
而且在一些核心代码层面上面去通过try开启

28
00:00:49,840 --> 00:00:51,000
那么这里呢我就不去

29
00:00:51,000 --> 00:00:53,340
拒绝介绍有场景

30
00:00:53,340 --> 00:00:54,480
因为这里呢可能跟一个经验有关

31
00:00:54,480 --> 00:00:55,520
同学们项目做多了之后

32
00:00:55,520 --> 00:00:57,060
自然就能够理解

33
00:00:57,060 --> 00:00:58,720
但是大家只需要了解一点

34
00:00:58,720 --> 00:00:59,700
try开启它非常重要

35
00:00:59,700 --> 00:01:02,440
那么我们就来看一下try开启它的基本使用

36
00:01:02,440 --> 00:01:04,660
首先那么比如说我们还是

37
00:01:04,660 --> 00:01:07,220
还是拿我们的home control来说

38
00:01:07,220 --> 00:01:09,200
假如我们在这里呢

39
00:01:09,200 --> 00:01:14,040
首先我们来try然后来开启

40
00:01:14,040 --> 00:01:20,180
看起来一个r 我们首先来看一下它的一个基本使用是怎么样的

41
00:01:20,180 --> 00:01:24,280
那么我们的try里面是不是执行业务逻辑 那么看起来就是做咱们的一个

42
00:01:24,280 --> 00:01:26,080
凑处理

43
00:01:26,080 --> 00:01:29,400
那么凑处理可以怎么去处理了

44
00:01:29,400 --> 00:01:31,960
那么你这里是不是可以把它

45
00:01:31,960 --> 00:01:37,340
写入我们1GG的日志里面的就是调用log.r 对吧

46
00:01:37,340 --> 00:01:38,880
然后呢 还有什么方式

47
00:01:38,880 --> 00:01:42,460
你是不是把它直接给打击出来 再咱们控制来去看一下 这里呢 它是一种比较什么呀

48
00:01:42,460 --> 00:01:43,480
 是比较低级的一个方式

49
00:01:44,040 --> 00:01:47,710
好 这里呢 为了方便同学们去学习 我们只用concert.nof 咱们就先不去

50
00:01:47,710 --> 00:01:48,640
 不去写日志

51
00:01:48,640 --> 00:01:54,280
首先我们这里呢 来try 这里我们去执行一段错误的代码 比如说我们去执行犯错误

52
00:01:54,280 --> 00:01:57,600
这样一个方法 那么我们是不是没有这样一个方法 所以一定这里会报错

53
00:01:57,600 --> 00:02:00,940
好 这里呢 我们就来看一下咱们空材会报什么错npm

54
00:02:00,940 --> 00:02:04,000
npm rundv

55
00:02:04,000 --> 00:02:10,400
好 刷新走 大家看到我们的空材是不是什么什么都没有发生 为什么呀

56
00:02:10,400 --> 00:02:11,680
因为呢

57
00:02:12,720 --> 00:02:14,760
这个地方它是不是一定会发生错误

58
00:02:14,760 --> 00:02:15,540
但是我们catch

59
00:02:15,540 --> 00:02:16,820
是不是已经到catch了

60
00:02:16,820 --> 00:02:18,100
但是catch没有执行任何代码

61
00:02:18,100 --> 00:02:20,400
所以你再控制来看不到任何东西

62
00:02:20,400 --> 00:02:21,420
比如说我们把它给挡印出来

63
00:02:21,420 --> 00:02:22,700
我们再来试一下

64
00:02:22,700 --> 00:02:24,500
这里

65
00:02:24,500 --> 00:02:25,520
好

66
00:02:25,520 --> 00:02:26,800
这里呢大家是不是就可以看到

67
00:02:26,800 --> 00:02:29,100
fine 错误 is not defined

68
00:02:29,100 --> 00:02:29,880
对吧

69
00:02:29,880 --> 00:02:30,640
那么其实这里呢

70
00:02:30,640 --> 00:02:31,920
稍微有一点难去分辨

71
00:02:31,920 --> 00:02:33,200
它到底是系统发生的错误

72
00:02:33,200 --> 00:02:34,740
还是我们concel.nog打印出来错误

73
00:02:34,740 --> 00:02:35,240
对吧

74
00:02:35,240 --> 00:02:35,760
这里呢

75
00:02:35,760 --> 00:02:37,800
我们来通过concel.info来试一下

76
00:02:37,800 --> 00:02:39,080
它有没有一个颜色

77
00:02:39,080 --> 00:02:41,140
其实是没有的

78
00:02:41,140 --> 00:02:42,940
那么这里其实不是很好区分

79
00:02:42,940 --> 00:02:45,500
那么我们就来看一下我们系统原生它的一个错误它是怎么样的

80
00:02:45,500 --> 00:02:48,180
比如说如果说我们没有看起是不是也会发生错误吧

81
00:02:48,180 --> 00:02:49,580
假如我们把所有的代码全部给注释

82
00:02:49,580 --> 00:02:51,120
全部注释

83
00:02:51,120 --> 00:02:52,140
我们的指指键

84
00:02:52,140 --> 00:02:53,680
咱们是代码什么呀

85
00:02:53,680 --> 00:02:54,200
这叫什么

86
00:02:54,200 --> 00:02:55,740
指键诺奔

87
00:02:55,740 --> 00:02:57,260
我们来看一下裸奔的效果是什么

88
00:02:57,260 --> 00:02:59,820
好大家可以看到

89
00:02:59,820 --> 00:03:01,360
我们的界面

90
00:03:01,360 --> 00:03:03,160
是不是报了一个错呀

91
00:03:03,160 --> 00:03:04,940
nodejs

92
00:03:04,940 --> 00:03:07,760
nodejs

93
00:03:07,760 --> 00:03:08,780
referenceError

94
00:03:08,780 --> 00:03:09,820
放错误 is not defined

95
00:03:09,820 --> 00:03:10,580
而且呢

96
00:03:10,840 --> 00:03:13,240
 strongest

97
00:03:40,840 --> 00:03:42,740
那么假如说我们现在这么去写

98
00:03:42,740 --> 00:03:44,740
setImmediate

99
00:03:44,740 --> 00:03:45,840
我们通过set

100
00:03:45,840 --> 00:03:49,440
咱们通过了一个异步的方法去调用我们的

101
00:03:49,440 --> 00:04:01,900
那么假如我们在一个异步的环境里面去调用我们的一个方法

102
00:04:01,900 --> 00:04:04,600
那现在大家觉得会不会报错

103
00:04:04,600 --> 00:04:05,900
大家觉得会不会报错

104
00:04:05,900 --> 00:04:08,040
好我们来刷新一下

105
00:04:11,360 --> 00:04:13,400
好 大家可以看到我们页面是不是标红了呀

106
00:04:13,400 --> 00:04:14,680
那么标红意味着什么呀

107
00:04:14,680 --> 00:04:16,720
是不是百分之百你的js内部出了问题

108
00:04:16,720 --> 00:04:18,780
那么此时你的js现成就非常的危险

109
00:04:18,780 --> 00:04:22,360
对吧 它非常有可能就会就此挂掉 再也无法提供http服务了

110
00:04:22,360 --> 00:04:23,640
那么这是什么原因呢

111
00:04:23,640 --> 00:04:24,660
这是什么原因

112
00:04:24,660 --> 00:04:26,200
那么我们的try catch

113
00:04:26,200 --> 00:04:27,740
在try里面是不是只能去

114
00:04:27,740 --> 00:04:30,040
catch他当前的一个什么呀

115
00:04:30,040 --> 00:04:31,320
当前这一轮的一个

116
00:04:31,320 --> 00:04:33,120
这一轮事件

117
00:04:33,120 --> 00:04:36,440
那么设定media是不是在下一轮呢 我们之前是不是学习过我们js的一个事件循环

118
00:04:36,440 --> 00:04:37,980
那么try

119
00:04:37,980 --> 00:04:40,020
只能

120
00:04:40,800 --> 00:04:43,560
捕捉到当前的这一轮

121
00:04:43,560 --> 00:04:46,180
那么下一轮呢它是无法给捕捉到的

122
00:04:46,180 --> 00:04:47,380
所以呢这里呢犯错误

123
00:04:47,380 --> 00:04:48,920
它呢就没有捕捉到

124
00:04:48,920 --> 00:04:50,300
那么我们的介绍是不是就直接标红了

125
00:04:50,300 --> 00:04:51,800
那么标红就非常不好

126
00:04:51,800 --> 00:04:53,040
所以这里呢

127
00:04:53,040 --> 00:04:55,420
在我们的1GG里面它提供的一个方法

128
00:04:55,420 --> 00:04:56,140
专门去

129
00:04:56,140 --> 00:04:58,460
专门去帮助我们解决一个问题

130
00:04:58,460 --> 00:04:58,880
叫做呢

131
00:04:58,880 --> 00:05:00,920
run in background

132
00:05:00,920 --> 00:05:03,560
好

133
00:05:03,560 --> 00:05:05,720
那么这里它为什么叫做这样的一个API呢

134
00:05:05,720 --> 00:05:07,000
为什么叫做这样的API

135
00:05:07,000 --> 00:05:07,880
run in background

136
00:05:07,880 --> 00:05:10,140
它那个意思是不是有点类似于咱们在后台运行呢

137
00:05:10,140 --> 00:05:10,460
对吧

138
00:05:10,460 --> 00:05:12,320
那么其实比如说我们有一种场景

139
00:05:12,320 --> 00:05:14,180
需要去使用异步去调用某一个方法的时候

140
00:05:14,180 --> 00:05:16,280
是不是也是希望它去不要去阻塑我们的主线程

141
00:05:16,280 --> 00:05:16,880
对吧

142
00:05:16,880 --> 00:05:18,640
那么其实也有一点类似我们希望

143
00:05:18,640 --> 00:05:23,660
希望你在后台运行

144
00:05:23,660 --> 00:05:25,740
所以呢在一机器里面它就取得这样一个名字

145
00:05:25,740 --> 00:05:26,520
那么我们就来试一下

146
00:05:26,520 --> 00:05:29,340
它到底有没有什么很神奇的效果

147
00:05:29,340 --> 00:05:30,100
好

148
00:05:30,100 --> 00:05:31,800
你们来接受一个Sync的函数

149
00:05:31,800 --> 00:05:34,900
我们去执行它

150
00:05:40,460 --> 00:05:45,200
好 走理

151
00:05:45,200 --> 00:05:47,380
好 此时大家是不是可以看到

152
00:05:47,380 --> 00:05:49,680
我们错误是不是已经被我们的什么呀

153
00:05:49,680 --> 00:05:52,520
是不是被我们的圈开启给捕捉到了呀

154
00:05:52,520 --> 00:05:53,920
对吧 那么这里

155
00:05:53,920 --> 00:05:55,720
说明什么问题呢

156
00:05:55,720 --> 00:05:56,140
我们

157
00:05:56,140 --> 00:05:59,200
你如果说需要去在后台去执行一些逻辑

158
00:05:59,200 --> 00:06:02,380
那么呢 你就可以去使用咱们的一个软引byground

159
00:06:02,380 --> 00:06:03,560
这样一个API去解决这样一个问题

160
00:06:03,560 --> 00:06:05,720
那么它呢是一GG原生所提供的

161
00:06:05,720 --> 00:06:08,280
好 那么这里呢就是我们

162
00:06:08,280 --> 00:06:10,040
错误处理这里的一点内容

163
00:06:10,040 --> 00:06:12,220
而且为了保证异常可追踪

164
00:06:12,220 --> 00:06:14,500
你必须保证所有抛出的异常都是error类型

165
00:06:14,500 --> 00:06:16,640
因为只有error类型才会带上堆战信息定位的问题

166
00:06:16,640 --> 00:06:17,800
那么什么是堆战信息

167
00:06:17,800 --> 00:06:19,660
可能有的同学们不是很理解什么是堆战信息

168
00:06:19,660 --> 00:06:20,420
那么这里呢

169
00:06:20,420 --> 00:06:21,460
我就来给同学们去演示一下

170
00:06:21,460 --> 00:06:22,380
什么是堆战信息

171
00:06:22,380 --> 00:06:24,600
其实我们刚才是不是执行犯错误的时候

172
00:06:24,600 --> 00:06:26,060
那么控制还是报了一个错

173
00:06:26,060 --> 00:06:29,160
那么其实这一串它就是一个堆战信息

174
00:06:29,160 --> 00:06:31,500
你比如说我们去把它点一下

175
00:06:31,500 --> 00:06:34,580
那是不是可以定位到我们发生错误的一些代码

176
00:06:34,580 --> 00:06:36,620
咱们是不是可以直接定位到我们的当前的代码

177
00:06:36,620 --> 00:06:36,920
对吧

178
00:06:36,920 --> 00:06:37,840
那么其实这里呢

179
00:06:37,840 --> 00:06:40,240
它就是堆战信息去帮助我们去查错的

180
00:06:40,240 --> 00:06:41,500
那么如果说

181
00:06:41,500 --> 00:06:42,600
那么其实这里呢

182
00:06:42,600 --> 00:06:43,400
我们也可以去手动的

183
00:06:43,400 --> 00:06:45,460
比如说我们去调用

184
00:06:45,460 --> 00:06:46,300
Straw

185
00:06:46,300 --> 00:06:50,880
比如说我们去Straw一个

186
00:06:50,880 --> 00:06:52,500
Error

187
00:06:52,500 --> 00:06:55,820
我是错误

188
00:06:55,820 --> 00:06:56,660
其实大家可以看到

189
00:06:56,660 --> 00:06:57,420
这种情况下

190
00:06:57,420 --> 00:06:58,520
它有没有堆战信息

191
00:06:58,520 --> 00:07:00,820
好

192
00:07:00,820 --> 00:07:01,400
大家可以看到

193
00:07:01,400 --> 00:07:02,160
其实这种情况下

194
00:07:02,160 --> 00:07:03,280
它是不是也有堆战信息啊

195
00:07:03,280 --> 00:07:03,960
为什么呀

196
00:07:03,960 --> 00:07:04,780
因为我们刚才讲

197
00:07:04,780 --> 00:07:05,380
里面是不是讲的

198
00:07:05,380 --> 00:07:06,300
为了保证

199
00:07:06,300 --> 00:07:07,620
一场可追踪

200
00:07:07,620 --> 00:07:09,460
那么你必须所有抛出的一场都是error类型

201
00:07:09,460 --> 00:07:11,440
我们刚才是不是已经抛了一个error类型了

202
00:07:11,440 --> 00:07:13,160
那么假如我们不去抛error类型怎么办

203
00:07:13,160 --> 00:07:14,800
我们就srow我是错误

204
00:07:14,800 --> 00:07:15,660
那我们再来看一下

205
00:07:15,660 --> 00:07:17,920
大家可以看到

206
00:07:17,920 --> 00:07:20,500
我们的cash里面是不是把我是错误给打开了

207
00:07:20,500 --> 00:07:21,220
但是呢

208
00:07:21,220 --> 00:07:22,460
它是不是没有对战信息啊

209
00:07:22,460 --> 00:07:23,640
所以很难去定位问题

210
00:07:23,640 --> 00:07:24,480
那么这里呢

211
00:07:24,480 --> 00:07:25,460
同学们就需要去注意

212
00:07:25,460 --> 00:07:26,680
我们去抛错的时候

213
00:07:26,680 --> 00:07:27,400
一定要去

214
00:07:27,400 --> 00:07:29,520
是不是srow一个什么的error对象啊

215
00:07:29,520 --> 00:07:34,480
一定要抛出error对象

216
00:07:34,480 --> 00:07:35,160
这是非常重要

217
00:07:35,160 --> 00:07:37,800
好 那么呢我们就来总结一下我们这些个内容

218
00:07:37,800 --> 00:07:40,780
咱们刚才讲的是不是凑处理

219
00:07:40,780 --> 00:07:44,840
那么凑处理咱们使用什么方法

220
00:07:44,840 --> 00:07:49,240
是不是通过TryCatch来进行我们的一个凑处理

221
00:07:49,240 --> 00:07:50,700
那么TryCatch它有一种

222
00:07:50,700 --> 00:07:52,580
它有一个问题是什么呀

223
00:07:52,580 --> 00:07:53,540
它是不是只能

224
00:07:53,540 --> 00:07:58,080
只能Catch是不是当前

225
00:07:58,080 --> 00:08:01,680
当前是件循环的

226
00:08:01,680 --> 00:08:02,700
那么下一轮怎么办

227
00:08:02,700 --> 00:08:05,020
咱们是不是可以通过Contest的电

228
00:08:05,020 --> 00:08:11,020
RunningBikeGround

229
00:08:11,020 --> 00:08:15,020
好 那么我们需要注意什么问题呢

230
00:08:15,020 --> 00:08:20,020
是不是咱们去trycast一定要抛出error类型了

231
00:08:20,020 --> 00:08:24,020
一定要抛出error类型

232
00:08:24,020 --> 00:08:27,020
好 那么这里就是我们这节目的内容

233
00:08:27,020 --> 00:08:28,080
我们的内容是

