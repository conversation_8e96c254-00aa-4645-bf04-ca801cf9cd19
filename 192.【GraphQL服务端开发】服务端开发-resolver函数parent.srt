1
00:00:00,000 --> 00:00:04,680
前面我们主要熟悉了

2
00:00:04,680 --> 00:00:05,560
GuardFq2当中的

3
00:00:05,560 --> 00:00:06,660
一系列的内置类型

4
00:00:06,660 --> 00:00:07,900
与内置类型相关的

5
00:00:07,900 --> 00:00:09,200
就是对应的数据解析函数

6
00:00:09,200 --> 00:00:09,740
Resolver

7
00:00:09,740 --> 00:00:11,200
Resolver函数的话

8
00:00:11,200 --> 00:00:12,060
它其实有四个参数

9
00:00:12,060 --> 00:00:13,520
但是我们之前只用到了其中一个

10
00:00:13,520 --> 00:00:14,060
就是X

11
00:00:14,060 --> 00:00:16,360
它表示这个客户端的查询参数

12
00:00:16,360 --> 00:00:17,540
除此之外

13
00:00:17,540 --> 00:00:18,160
另外三个参数

14
00:00:18,160 --> 00:00:19,540
我们也需要再认识一下

15
00:00:19,540 --> 00:00:20,600
首先我们看第一个

16
00:00:20,600 --> 00:00:20,960
Parrot

17
00:00:20,960 --> 00:00:23,120
它表示的是上一级对象

18
00:00:23,120 --> 00:00:24,140
既然是上一级

19
00:00:24,140 --> 00:00:25,180
肯定就有下一级

20
00:00:25,180 --> 00:00:26,220
这里边就涉及到

21
00:00:26,220 --> 00:00:26,760
一个是根节点

22
00:00:26,760 --> 00:00:27,320
一个是子节点

23
00:00:27,320 --> 00:00:28,700
我们之前做的这个查询

24
00:00:28,700 --> 00:00:30,320
基本上都属于跟节点的查询

25
00:00:30,320 --> 00:00:32,000
跟节点就是query当中

26
00:00:32,000 --> 00:00:33,180
直接查询的字段

27
00:00:33,180 --> 00:00:34,040
就是跟节点

28
00:00:34,040 --> 00:00:35,040
什么是子节点

29
00:00:35,040 --> 00:00:35,880
子节点

30
00:00:35,880 --> 00:00:36,760
比如说我们之前定义的

31
00:00:36,760 --> 00:00:37,700
student的类型

32
00:00:37,700 --> 00:00:39,140
它当中的姓名

33
00:00:39,140 --> 00:00:40,080
年龄这些字段

34
00:00:40,080 --> 00:00:41,860
我们都称为子节点

35
00:00:41,860 --> 00:00:43,360
子节点当中

36
00:00:43,360 --> 00:00:44,560
才有对应的parent

37
00:00:44,560 --> 00:00:45,960
但是跟节点当中

38
00:00:45,960 --> 00:00:47,180
parent实际上它是空直

39
00:00:47,180 --> 00:00:48,600
后边我们通过例子的方式

40
00:00:48,600 --> 00:00:49,040
来多演示

41
00:00:49,040 --> 00:00:52,020
接下来我们看content的范数

42
00:00:52,020 --> 00:00:52,860
这是第三个

43
00:00:52,860 --> 00:00:54,340
它的主要的应用场景

44
00:00:54,340 --> 00:00:56,840
是用于获取数据源当中的数据

45
00:00:56,840 --> 00:00:57,500
所谓的数据源

46
00:00:57,500 --> 00:00:58,640
就是我们获取数据的地方

47
00:00:58,640 --> 00:00:59,360
包括数据库

48
00:00:59,360 --> 00:01:00,720
也包括文件中读取数据

49
00:01:00,720 --> 00:01:03,060
也包括调用第三方的接口

50
00:01:03,060 --> 00:01:05,740
我们主要把操作数据员的对象

51
00:01:05,740 --> 00:01:06,480
提供给context

52
00:01:06,480 --> 00:01:08,500
然后我们可以在所有的解析器

53
00:01:08,500 --> 00:01:09,580
也就是resolver函数当中

54
00:01:09,580 --> 00:01:10,400
获取到这个对象

55
00:01:10,400 --> 00:01:11,860
从而可以更加方便的

56
00:01:11,860 --> 00:01:13,600
去操作数据员当中的数据

57
00:01:13,600 --> 00:01:14,340
这是它的作用

58
00:01:14,340 --> 00:01:15,840
最后一个参数

59
00:01:15,840 --> 00:01:16,880
infra这个我们用的不多

60
00:01:16,880 --> 00:01:19,080
它主要用来保存查询参数

61
00:01:19,080 --> 00:01:20,980
当中的字段相关的信息

62
00:01:20,980 --> 00:01:23,600
以及服务端的类型定义相关的信息

63
00:01:23,600 --> 00:01:25,140
这次gamer指的就是

64
00:01:25,140 --> 00:01:26,540
类型的定义相关信息

65
00:01:26,540 --> 00:01:27,520
这个我们用的不多

66
00:01:27,520 --> 00:01:28,420
了解一下就可以

67
00:01:28,420 --> 00:01:30,340
我们的中心在前三个参数

68
00:01:30,340 --> 00:01:31,980
有一点需要注意的

69
00:01:31,980 --> 00:01:33,920
就是这四个参数是有顺序的

70
00:01:33,920 --> 00:01:35,080
顺序要对得上

71
00:01:35,080 --> 00:01:36,840
不过这个名字由于是行参

72
00:01:36,840 --> 00:01:38,320
所以说可以自定义

73
00:01:38,320 --> 00:01:40,560
这是关于这四个参数的基本情况

74
00:01:40,560 --> 00:01:42,260
我们先有一个初步的认识

75
00:01:42,260 --> 00:01:44,140
接下来我们从parent参数开始

76
00:01:44,140 --> 00:01:45,440
看它的作用是什么

77
00:01:45,440 --> 00:01:47,380
具体我们通过一个例子来演示

78
00:01:47,380 --> 00:01:49,440
首先我们定义一个student类型

79
00:01:49,440 --> 00:01:50,960
然后给它提供一个里头版函数

80
00:01:50,960 --> 00:01:53,620
但是我们在这对student当中的

81
00:01:53,620 --> 00:01:54,400
它的子节点

82
00:01:54,400 --> 00:01:56,320
就是它的子属性

83
00:01:56,320 --> 00:01:57,620
也提供了兹刀函数

84
00:01:57,620 --> 00:01:58,680
就是这个sName

85
00:01:58,680 --> 00:02:00,580
这里边可以获取到parent

86
00:02:00,580 --> 00:02:02,500
可以通过parent来获取一些数据

87
00:02:02,500 --> 00:02:04,100
那接下来我们就具体演示一下

88
00:02:04,100 --> 00:02:06,600
首先我们定义一个学生类型

89
00:02:06,600 --> 00:02:07,920
student

90
00:02:07,920 --> 00:02:09,700
然后这儿我们提供一个sName

91
00:02:09,700 --> 00:02:11,840
这是student类型的

92
00:02:11,840 --> 00:02:12,800
然后再一个age

93
00:02:12,800 --> 00:02:14,240
这个我们用int

94
00:02:14,240 --> 00:02:16,120
然后的话我们再提供一个favor

95
00:02:16,120 --> 00:02:17,180
这个表示爱好

96
00:02:17,180 --> 00:02:18,280
还是student

97
00:02:18,280 --> 00:02:19,660
在这查询类质

98
00:02:19,660 --> 00:02:20,660
我们再提供一个sto

99
00:02:20,660 --> 00:02:22,340
要查询的类型是student

100
00:02:22,340 --> 00:02:25,840
最后我们提供这个sto

101
00:02:25,840 --> 00:02:27,360
它对应的gador

102
00:02:27,360 --> 00:02:30,280
在这的话

103
00:02:30,280 --> 00:02:31,460
我们直接就反映一个对象

104
00:02:31,460 --> 00:02:33,100
先提供点数据

105
00:02:33,100 --> 00:02:34,520
sname 第四

106
00:02:34,520 --> 00:02:36,020
然后是年龄

107
00:02:36,020 --> 00:02:36,860
12岁

108
00:02:36,860 --> 00:02:38,440
最后是favor 爱好

109
00:02:38,440 --> 00:02:39,860
比如说他喜欢游泳

110
00:02:39,860 --> 00:02:40,300
随名

111
00:02:40,300 --> 00:02:42,480
然后的话我们启动服务

112
00:02:42,480 --> 00:02:43,840
先查询一下数据

113
00:02:43,840 --> 00:02:44,280
这呢

114
00:02:44,280 --> 00:02:44,480
node

115
00:02:44,480 --> 00:02:45,260
mod

116
00:02:45,260 --> 00:02:46,480
防分的是08

117
00:02:46,480 --> 00:02:48,820
好 启动成功之后

118
00:02:48,820 --> 00:02:49,460
我们做一个查询

119
00:02:49,460 --> 00:02:51,160
在这我们要查询的是谁呢

120
00:02:51,160 --> 00:02:52,580
sto

121
00:02:52,580 --> 00:02:53,400
它当中的

122
00:02:53,400 --> 00:02:54,280
sname

123
00:02:54,280 --> 00:02:55,400
还有年龄

124
00:02:55,400 --> 00:02:56,480
最后这个是Favor

125
00:02:56,480 --> 00:02:57,760
然后点查询

126
00:02:57,760 --> 00:02:58,860
数据都获取到了

127
00:02:58,860 --> 00:03:01,060
但是由于我们查询的STO

128
00:03:01,060 --> 00:03:01,700
它属于根节点

129
00:03:01,700 --> 00:03:02,900
所以说在这里边

130
00:03:02,900 --> 00:03:04,540
Parent实际上是不存在的

131
00:03:04,540 --> 00:03:06,140
你可以把它打印一下

132
00:03:06,140 --> 00:03:08,420
然后你再查询

133
00:03:08,420 --> 00:03:10,700
然后看打印

134
00:03:10,700 --> 00:03:11,520
这里是AndPan

135
00:03:11,520 --> 00:03:13,080
所以说对于根节点来说

136
00:03:13,080 --> 00:03:14,540
Parent其实它没有用

137
00:03:14,540 --> 00:03:16,060
什么时候有用呢

138
00:03:16,060 --> 00:03:17,080
如果说我们在

139
00:03:17,080 --> 00:03:18,580
StealTent当中的

140
00:03:18,580 --> 00:03:20,060
子节点当中

141
00:03:20,060 --> 00:03:20,820
定义Risover函数

142
00:03:20,820 --> 00:03:21,700
也就是说我们这样来做

143
00:03:21,700 --> 00:03:23,640
在这提供一个StealTent类型

144
00:03:23,640 --> 00:03:24,200
然后

145
00:03:24,200 --> 00:03:26,820
里边再添加一个resolver函数

146
00:03:26,820 --> 00:03:27,860
那就是对应的sname

147
00:03:27,860 --> 00:03:30,960
这样来做

148
00:03:30,960 --> 00:03:32,400
然后这儿我们提供一个parent

149
00:03:32,400 --> 00:03:36,980
然后这里边我们直接去written一个值

150
00:03:36,980 --> 00:03:39,040
written什么呢

151
00:03:39,040 --> 00:03:39,980
换一个名字张三

152
00:03:39,980 --> 00:03:43,320
并且把这个parent给它打印出来

153
00:03:43,320 --> 00:03:44,360
好

154
00:03:44,360 --> 00:03:45,360
然后我们再次查询

155
00:03:45,360 --> 00:03:46,540
走

156
00:03:46,540 --> 00:03:47,380
会发现

157
00:03:47,380 --> 00:03:49,020
这个sname它变成了张三

158
00:03:49,020 --> 00:03:50,020
并且你看后台

159
00:03:50,020 --> 00:03:51,200
这里边打印出数据了

160
00:03:51,200 --> 00:03:52,240
你看这个数据是谁

161
00:03:52,240 --> 00:03:54,000
实际上就是这个

162
00:03:54,000 --> 00:03:54,880
负极的对象

163
00:03:54,880 --> 00:03:57,540
所以说此时sname它就是子节点

164
00:03:57,540 --> 00:04:00,600
那上面的sto它就是根节点

165
00:04:00,600 --> 00:04:02,800
这是有这个负子关系的

166
00:04:02,800 --> 00:04:05,080
所以说从子节点当中的parent参数

167
00:04:05,080 --> 00:04:07,300
就可以得到负极的对象

168
00:04:07,300 --> 00:04:09,160
并且还有一个细节是什么

169
00:04:09,160 --> 00:04:12,040
如果说在子节点的resolver函数中

170
00:04:12,040 --> 00:04:12,880
返回了一个具体的值

171
00:04:12,880 --> 00:04:14,560
那么其实最终是以特别准的

172
00:04:14,560 --> 00:04:16,180
那它负节点的值就会被覆盖掉

173
00:04:16,180 --> 00:04:19,120
因为我们现在拿到的是张三这个数据

174
00:04:19,120 --> 00:04:20,660
这是关于parent它的作用

175
00:04:20,660 --> 00:04:22,920
其实还有一个细节是什么

176
00:04:22,920 --> 00:04:24,780
就是如果说我们这个字弹

177
00:04:24,780 --> 00:04:27,040
它不提供对应的resolver函数

178
00:04:27,040 --> 00:04:28,840
那它为什么也能够得到值呢

179
00:04:28,840 --> 00:04:30,220
那是因为默认的话

180
00:04:30,220 --> 00:04:33,740
这个graphqr会为它生成一个resolver函数

181
00:04:33,740 --> 00:04:36,020
它生成的resolver函数大体上是这样的

182
00:04:36,020 --> 00:04:37,100
这里边是这样返回的

183
00:04:37,100 --> 00:04:39,260
written parent当中的什么呢

184
00:04:39,260 --> 00:04:39,820
sname

185
00:04:39,820 --> 00:04:41,860
所以这个sname其实就是

186
00:04:41,860 --> 00:04:43,620
负节点当中传过来的那个数据

187
00:04:43,620 --> 00:04:44,580
其实还是理次

188
00:04:44,580 --> 00:04:45,680
然后你再查准

189
00:04:45,680 --> 00:04:47,360
会发现这里就变成了理次

190
00:04:47,360 --> 00:04:49,500
所以说它默认的resolver函数

191
00:04:49,500 --> 00:04:52,240
它里边的返回值实际上就类似这样的

192
00:04:52,240 --> 00:04:54,140
这就是parent它的作用

193
00:04:54,140 --> 00:04:57,820
那为了更加体现出parent它的实际意义呢

194
00:04:57,820 --> 00:04:58,740
我们可以再扩展一点

195
00:04:58,740 --> 00:05:00,980
在这呢我们给这个favor

196
00:05:00,980 --> 00:05:02,360
再加一个resolver函数

197
00:05:02,360 --> 00:05:03,980
我们这样做

198
00:05:03,980 --> 00:05:06,420
在这做什么事呢

199
00:05:06,420 --> 00:05:07,160
我们可以做一个判断

200
00:05:07,160 --> 00:05:10,800
就是如果parent它当中的这个favor

201
00:05:10,800 --> 00:05:12,500
如果这个值是谁呢

202
00:05:12,500 --> 00:05:13,120
是随名

203
00:05:13,120 --> 00:05:15,700
我就给它返回一个中文的游泳

204
00:05:15,700 --> 00:05:16,880
written

205
00:05:16,880 --> 00:05:18,240
游泳

206
00:05:18,240 --> 00:05:19,420
否则的话呢

207
00:05:19,420 --> 00:05:20,500
我还是返回原来的数据

208
00:05:20,500 --> 00:05:22,100
这就是rated

209
00:05:22,100 --> 00:05:23,560
原来的数据就是parent

210
00:05:23,560 --> 00:05:25,380
然后favor

211
00:05:25,380 --> 00:05:26,400
这样来做

212
00:05:26,400 --> 00:05:28,180
这的话就有一些具体的场景了

213
00:05:28,180 --> 00:05:29,160
这是实际的一个需求

214
00:05:29,160 --> 00:05:31,440
然后的话我们再去查询

215
00:05:31,440 --> 00:05:33,420
这里边就变成了中文的游泳

216
00:05:33,420 --> 00:05:35,620
如果你的爱好不是游泳

217
00:05:35,620 --> 00:05:36,640
比如说你爱好写代码

218
00:05:36,640 --> 00:05:37,020
口顶

219
00:05:37,020 --> 00:05:38,660
然后你再去查询

220
00:05:38,660 --> 00:05:40,320
这就是原始的信息

221
00:05:40,320 --> 00:05:42,700
这就是parent它的一个具体的作用

222
00:05:42,700 --> 00:05:45,260
就是通过负节点传过来的数据

223
00:05:45,260 --> 00:05:47,500
可以做一些加工处理

224
00:05:47,500 --> 00:05:49,660
这是关于第一个参数parent

225
00:05:49,660 --> 00:05:50,860
我们主要就这么多

226
00:05:50,860 --> 00:05:52,000
最后的话

227
00:05:52,000 --> 00:05:52,760
我们需要注意的是什么

228
00:05:52,760 --> 00:05:53,840
就是resolver函数

229
00:05:53,840 --> 00:05:54,780
它的第一个参数parent

230
00:05:54,780 --> 00:05:57,580
可以获取负级的对象中的数据

231
00:05:57,580 --> 00:05:59,540
并且对于自弹类型来说

232
00:05:59,540 --> 00:06:00,340
如果是标量类型

233
00:06:00,340 --> 00:06:02,300
我们不给它提供resolver函数的话

234
00:06:02,300 --> 00:06:04,180
它会默认生成一个resolver函数

235
00:06:04,180 --> 00:06:05,680
默认生成的resolver函数

236
00:06:05,680 --> 00:06:08,020
它返回的其实是负级对象的自弹数据

237
00:06:08,020 --> 00:06:08,920
就类似于这样来写

238
00:06:08,920 --> 00:06:11,020
这是关于resolver函数

239
00:06:11,020 --> 00:06:13,160
以及parent参数的作用

240
00:06:13,160 --> 00:06:13,980
我们就说到这里

