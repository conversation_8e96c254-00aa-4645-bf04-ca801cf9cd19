1
00:00:00,000 --> 00:00:02,760
前面我们对AuPoloServer有了一个初步的认识

2
00:00:02,760 --> 00:00:04,480
那接下来我们来详细的分析一下

3
00:00:04,480 --> 00:00:06,200
如何对AuPoloServer做实际的开发

4
00:00:06,200 --> 00:00:07,680
但是在再半层面当中

5
00:00:07,680 --> 00:00:09,520
我们会用到一个ES6的语法特性

6
00:00:09,520 --> 00:00:10,400
叫标签模板

7
00:00:10,400 --> 00:00:12,580
那接下来我们提前来铺件一下这个语法点

8
00:00:12,580 --> 00:00:14,200
那具体我们这样来说

9
00:00:14,200 --> 00:00:16,240
在这我们创建一个文件01

10
00:00:16,240 --> 00:00:18,160
注意这是ES6的语法

11
00:00:18,160 --> 00:00:21,360
这语法的名称我们就叫标签模板

12
00:00:21,360 --> 00:00:24,520
标签模板的话有什么用呢

13
00:00:24,520 --> 00:00:26,100
实际上就是在调用函数的时候

14
00:00:26,100 --> 00:00:28,080
可以用另外一种方式做参数的传递

15
00:00:28,080 --> 00:00:29,500
所以说我们先用传统的方式

16
00:00:29,500 --> 00:00:30,440
做一个函数交用

17
00:00:30,440 --> 00:00:31,080
做对比

18
00:00:31,080 --> 00:00:33,140
这我们定义一个函数

19
00:00:33,140 --> 00:00:35,120
里边打印点信息

20
00:00:35,120 --> 00:00:36,440
比如说我们就把这个形态

21
00:00:36,440 --> 00:00:37,080
给它打印出来

22
00:00:37,080 --> 00:00:39,020
然后下边做函数的交用

23
00:00:39,020 --> 00:00:41,000
这传入一个叛数

24
00:00:41,000 --> 00:00:41,680
Hello

25
00:00:41,680 --> 00:00:43,260
这没什么好说的

26
00:00:43,260 --> 00:00:44,420
就是做一个函数定义

27
00:00:44,420 --> 00:00:45,100
和函数交用

28
00:00:45,100 --> 00:00:47,020
然后我们做执行

29
00:00:47,020 --> 00:00:48,620
执行的时候

30
00:00:48,620 --> 00:00:49,400
我们再打开一个中端

31
00:00:49,400 --> 00:00:51,080
然后我们运行

32
00:00:51,080 --> 00:00:51,960
就是node

33
00:00:51,960 --> 00:00:54,640
这结果就打印出来了

34
00:00:54,640 --> 00:00:56,440
这是我们传统的函数交用方式

35
00:00:56,440 --> 00:00:57,700
其实我们可以换一种形式

36
00:00:57,700 --> 00:00:59,940
就是fo加上这个翻译号

37
00:00:59,940 --> 00:01:02,220
里边我们可以给它传入参数

38
00:01:02,220 --> 00:01:03,580
比如说我们传入你好

39
00:01:03,580 --> 00:01:05,580
然后我们再去执行

40
00:01:05,580 --> 00:01:07,040
这个结果也能够出来

41
00:01:07,040 --> 00:01:08,760
也就是说这里边调用函数的时候

42
00:01:08,760 --> 00:01:10,220
这个参数也传给了param

43
00:01:10,220 --> 00:01:11,880
但是打印的形式有点区别

44
00:01:11,880 --> 00:01:14,240
此时打印出来的实际上是一个数组

45
00:01:14,240 --> 00:01:15,940
它当中包含了这个参数的内容

46
00:01:15,940 --> 00:01:17,020
所以说我们把上面这个数掉

47
00:01:17,020 --> 00:01:18,640
然后这个位置我们加上方括号

48
00:01:18,640 --> 00:01:19,820
0

49
00:01:19,820 --> 00:01:21,000
然后我们再运行

50
00:01:21,000 --> 00:01:23,400
这个结果就出来了

51
00:01:23,400 --> 00:01:25,040
就没有了这个数组的方括号了

52
00:01:25,040 --> 00:01:27,320
所以说采用这个模板字符号的方式

53
00:01:27,320 --> 00:01:28,880
也可以进行参数的传递

54
00:01:28,880 --> 00:01:31,160
从这个功能层面上来说

55
00:01:31,160 --> 00:01:31,980
其实是类似的

56
00:01:31,980 --> 00:01:32,960
但是这种方式

57
00:01:32,960 --> 00:01:33,780
其实有一个好处

58
00:01:33,780 --> 00:01:35,020
什么好处呢

59
00:01:35,020 --> 00:01:35,320
就是在这

60
00:01:35,320 --> 00:01:37,600
你可以直接

61
00:01:37,600 --> 00:01:38,800
在这个模板字幕式当中

62
00:01:38,800 --> 00:01:40,660
写入更复杂的内容格式

63
00:01:40,660 --> 00:01:42,320
比如说我们在这写成这个DMV

64
00:01:42,320 --> 00:01:43,100
你好

65
00:01:43,100 --> 00:01:44,020
你再来一个

66
00:01:44,020 --> 00:01:44,600
Hello

67
00:01:44,600 --> 00:01:46,200
然后我们再去进行

68
00:01:46,200 --> 00:01:46,620
看一下

69
00:01:46,620 --> 00:01:49,420
这样的结果也能够传递进去

70
00:01:49,420 --> 00:01:50,440
并且我们这里边

71
00:01:50,440 --> 00:01:51,120
这个参数的形式

72
00:01:51,120 --> 00:01:52,140
是有格式的

73
00:01:52,140 --> 00:01:53,580
就是可读性更好

74
00:01:53,580 --> 00:01:55,240
这是关于这个标签模板

75
00:01:55,240 --> 00:01:56,280
它的用法

76
00:01:56,280 --> 00:01:58,080
那背后的结论是什么呢

77
00:01:58,080 --> 00:01:58,920
我们在这背注一下

78
00:01:58,920 --> 00:02:01,180
就是标签模板

79
00:02:01,180 --> 00:02:02,380
它的作用呢

80
00:02:02,380 --> 00:02:07,400
就是可以作为函数要用的参数

81
00:02:07,400 --> 00:02:09,080
所以说就这一个作用

82
00:02:09,080 --> 00:02:10,700
那这是关于标签模板

83
00:02:10,700 --> 00:02:12,780
我们要有一个整体的认识

84
00:02:12,780 --> 00:02:15,560
后边呢我们在写这个GraphQL语法的时候呢

85
00:02:15,560 --> 00:02:16,020
会用到它

86
00:02:16,020 --> 00:02:17,560
就是做类型定义的时候

87
00:02:17,560 --> 00:02:19,740
我们会以这种形式来做参数的传递

88
00:02:19,740 --> 00:02:21,480
好 这是关于标签模板

89
00:02:21,480 --> 00:02:21,980
我们就说到这里

