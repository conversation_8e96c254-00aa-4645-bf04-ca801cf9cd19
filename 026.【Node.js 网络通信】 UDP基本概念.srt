1
00:00:00,660 --> 00:00:02,980
接下来我们就要学习我们课程中的第三章节

2
00:00:02,980 --> 00:00:04,100
构建UDP服务

3
00:00:04,100 --> 00:00:06,520
那么本章节当中所设计的内容

4
00:00:06,520 --> 00:00:09,540
第一就是关于UDP的一些概念性的介绍

5
00:00:09,540 --> 00:00:10,640
例如什么是UDP

6
00:00:10,640 --> 00:00:13,360
UDP和TCP之间的一个区别等相关的概念

7
00:00:13,360 --> 00:00:15,240
那么第二呢就是我们要学习到

8
00:00:15,240 --> 00:00:17,020
在Node中所提供的一个核心模块

9
00:00:17,020 --> 00:00:17,660
就是Dgram

10
00:00:17,660 --> 00:00:19,900
那么这个模块它相关的一些API

11
00:00:19,900 --> 00:00:21,860
那么我们有了这个核心模块

12
00:00:21,860 --> 00:00:23,680
然后我们接下来就可以使用这个核心模块

13
00:00:23,680 --> 00:00:26,880
来分别实现UDP的单波广播和主播

14
00:00:26,880 --> 00:00:29,880
那么首先让我们先来了解一下

15
00:00:29,880 --> 00:00:32,060
对UDP的一个相关概念的介绍

16
00:00:32,060 --> 00:00:33,420
那什么是UDP呢

17
00:00:33,420 --> 00:00:36,400
UDP的群称叫做User DataGram Protocol

18
00:00:36,400 --> 00:00:37,600
简称的就是UDP

19
00:00:37,600 --> 00:00:40,860
它翻译过来的意思就是用户数据报协议

20
00:00:40,860 --> 00:00:42,400
它和TCP是一样的

21
00:00:42,400 --> 00:00:43,400
也位于网络传输桶

22
00:00:43,400 --> 00:00:45,660
主要就是用来传输我们的数据班

23
00:00:45,660 --> 00:00:48,680
UDP最大的一个特点就是无连接

24
00:00:48,680 --> 00:00:50,080
这个所谓的无连接

25
00:00:50,080 --> 00:00:52,080
就是说UDP之间的相互通信

26
00:00:52,080 --> 00:00:53,600
是不需要接近连接的

27
00:00:53,600 --> 00:00:55,780
例如我A端要给B端来发送数据

28
00:00:55,780 --> 00:00:57,380
你直接去发就可以了

29
00:00:57,380 --> 00:00:58,760
它和我们的TCP相比

30
00:00:58,760 --> 00:01:00,400
就是TCP需要建立连接

31
00:01:00,400 --> 00:01:01,920
然后才能去进行这个数据的收发

32
00:01:01,920 --> 00:01:03,080
而UDP不需要

33
00:01:03,080 --> 00:01:05,800
所以说正是由于它这个无连接的一个特点

34
00:01:05,800 --> 00:01:07,740
所以UDP的传输速度非常的快

35
00:01:07,740 --> 00:01:10,320
当然UDP也有一些所谓的缺点

36
00:01:10,320 --> 00:01:12,620
例如它的数据传输并不可靠

37
00:01:12,620 --> 00:01:16,340
例如它不提供对数据包的一个分组组装

38
00:01:16,340 --> 00:01:18,660
并且不能对我们的数据包进行排序

39
00:01:18,660 --> 00:01:21,820
也就是说UDP的数据包可能会有持续的问题

40
00:01:21,820 --> 00:01:25,780
所以说我们UDP它数据传输是不可靠的

41
00:01:25,780 --> 00:01:28,340
那如果说你想让你的UDP数据包

42
00:01:28,340 --> 00:01:29,900
就是保证所谓的可靠性

43
00:01:29,900 --> 00:01:30,880
那么这一点的话

44
00:01:30,880 --> 00:01:32,240
你需要通过你的应用层

45
00:01:32,240 --> 00:01:33,480
来自己来处理

46
00:01:33,480 --> 00:01:36,200
那么其次对于我们UDP来说

47
00:01:36,200 --> 00:01:39,000
它不仅仅支持一对一通信

48
00:01:39,000 --> 00:01:40,820
而且它还支持一对多通信

49
00:01:40,820 --> 00:01:42,420
那么这里我所说的一对多

50
00:01:42,420 --> 00:01:44,680
指的是我从一个服务端

51
00:01:44,680 --> 00:01:46,800
留下N个客户端来发送数据

52
00:01:46,800 --> 00:01:47,820
那如果是TCP的话

53
00:01:47,820 --> 00:01:48,940
那你需要去一个

54
00:01:48,940 --> 00:01:50,940
就是说假如说你得有10个客户端

55
00:01:50,940 --> 00:01:53,280
每个客户端接收相同的数据

56
00:01:53,280 --> 00:01:54,020
那么对于TCP

57
00:01:54,020 --> 00:01:55,040
你要一份一份发

58
00:01:55,040 --> 00:01:55,960
也就是你要发10次

59
00:01:55,960 --> 00:01:57,220
而对UDP来讲

60
00:01:57,220 --> 00:01:57,920
它支持一对多

61
00:01:57,920 --> 00:01:58,980
那么对于这个东西

62
00:01:58,980 --> 00:02:00,180
我们可以只发一次

63
00:02:00,180 --> 00:02:02,300
然后把这个数据去给它广播数据

64
00:02:02,300 --> 00:02:04,500
所以说UDP它支持一对多通信

65
00:02:04,500 --> 00:02:07,320
那么很多关键的互联网应用

66
00:02:07,320 --> 00:02:08,720
也都使用到了UDP

67
00:02:08,720 --> 00:02:11,920
例如我们知名的DNS域名系统服务

68
00:02:11,920 --> 00:02:13,660
TFTP简单的念传书协议

69
00:02:13,660 --> 00:02:15,760
DHCP动态主力设置协议

70
00:02:15,760 --> 00:02:16,480
对于这些

71
00:02:16,480 --> 00:02:17,540
他们都使用到了UDP

72
00:02:17,540 --> 00:02:20,160
那么从这个就是应用

73
00:02:20,160 --> 00:02:21,920
真实的业务应用场景来说

74
00:02:21,920 --> 00:02:22,980
UDP适用于这种

75
00:02:22,980 --> 00:02:24,660
就是对速度要求比较高

76
00:02:24,660 --> 00:02:26,820
但是对数据的质量要求不严谨

77
00:02:26,820 --> 00:02:27,520
也就是说

78
00:02:27,520 --> 00:02:54,120
不强制依赖这个所谓的

