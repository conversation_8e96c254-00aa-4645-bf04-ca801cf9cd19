1
00:00:00,000 --> 00:00:01,880
好 这节课我们就要看一下中间键

2
00:00:01,880 --> 00:00:03,880
那么中间键其实在core里面

3
00:00:03,880 --> 00:00:05,580
它呢也是非常常见的

4
00:00:05,580 --> 00:00:07,660
我们之前是不是也写过core的中间键

5
00:00:07,660 --> 00:00:09,720
它呢是基于洋葱模型的

6
00:00:09,720 --> 00:00:12,120
那么呢 我们每次编写一个中间键

7
00:00:12,120 --> 00:00:14,960
是不是就相当于在洋葱的外面又给包了一层

8
00:00:14,960 --> 00:00:17,700
那么我们先来回顾一下core的中间键是怎么样写的

9
00:00:17,700 --> 00:00:19,380
首先呢 是不是一个方形

10
00:00:19,380 --> 00:00:21,740
接受两个参数 一个context和next

11
00:00:21,740 --> 00:00:23,800
那么next代表什么 同学们还记不记得

12
00:00:23,800 --> 00:00:25,480
next是不是代表

13
00:00:25,480 --> 00:00:29,040
代表app use过之后的下一个函数

14
00:00:29,040 --> 00:00:33,260
好

15
00:00:33,260 --> 00:00:33,940
那么这里呢

16
00:00:33,940 --> 00:00:34,400
就是core

17
00:00:34,400 --> 00:00:35,420
它有中间的写法

18
00:00:35,420 --> 00:00:36,540
那么我们就来看一下

19
00:00:36,540 --> 00:00:38,680
在我们的1G里面

20
00:00:38,680 --> 00:00:39,120
怎么样去写

21
00:00:39,120 --> 00:00:39,640
中间键

22
00:00:39,640 --> 00:00:40,500
其实我们刚才

23
00:00:40,500 --> 00:00:41,220
是不是也写过一个

24
00:00:41,220 --> 00:00:41,920
夹杂日字的

25
00:00:41,920 --> 00:00:43,640
其实在中间键里面

26
00:00:43,640 --> 00:00:44,940
我们刚才其实也写过了

27
00:00:44,940 --> 00:00:45,380
我们呢

28
00:00:45,380 --> 00:00:45,880
中间键

29
00:00:45,880 --> 00:00:46,900
在core里面

30
00:00:46,900 --> 00:00:47,260
它呢

31
00:00:47,260 --> 00:00:48,940
是需要去export

32
00:00:48,940 --> 00:00:49,860
一个普通的方形

33
00:00:49,860 --> 00:00:50,200
它呢

34
00:00:50,200 --> 00:00:50,940
需要接受两参数

35
00:00:50,940 --> 00:00:51,860
一个是options

36
00:00:51,860 --> 00:00:52,940
options是什么呀

37
00:00:52,940 --> 00:00:53,620
是不是我们中间

38
00:00:53,620 --> 00:00:54,520
初始化了一些配置

39
00:00:54,520 --> 00:00:55,980
app呢

40
00:00:55,980 --> 00:00:57,500
也就是咱们的application的实例

41
00:00:57,500 --> 00:00:58,980
其实大家可以看到

42
00:00:58,980 --> 00:00:59,700
我们呢

43
00:00:59,700 --> 00:01:01,400
在一击进里面

44
00:01:01,400 --> 00:01:02,300
是不是首先呢

45
00:01:02,300 --> 00:01:03,520
你需要export的一个方形

46
00:01:03,520 --> 00:01:05,000
然后再去return一个方形

47
00:01:05,000 --> 00:01:06,520
return出来的就是咱们在core里面

48
00:01:06,520 --> 00:01:07,940
去写的中间间的一个样子

49
00:01:07,940 --> 00:01:08,580
那么这里呢

50
00:01:08,580 --> 00:01:09,440
在我们的一个

51
00:01:09,440 --> 00:01:11,080
视力代码里面实现的是一个glape

52
00:01:11,080 --> 00:01:12,940
那么我们重点是看怎么样去写

53
00:01:12,940 --> 00:01:13,820
那么glape这里呢

54
00:01:13,820 --> 00:01:15,380
我就不去做过多的一个介绍

55
00:01:15,380 --> 00:01:17,680
那么如果说我们要去使用中间间

56
00:01:17,680 --> 00:01:19,060
首先我们在middleboard里面

57
00:01:19,060 --> 00:01:20,680
是不是需要去传入我们中间间的名称

58
00:01:20,680 --> 00:01:21,560
而且呢

59
00:01:21,560 --> 00:01:22,540
如果说你需要去配置的话

60
00:01:22,540 --> 00:01:23,780
你可以直接在

61
00:01:23,780 --> 00:01:25,540
你可以直接在你

62
00:01:25,540 --> 00:01:27,220
我们的config里面去配

63
00:01:27,220 --> 00:01:28,100
那么我们就来看一下

64
00:01:28,100 --> 00:01:29,020
我们刚才写的log

65
00:01:29,020 --> 00:01:30,400
如果说我们想对它做一些定制化

66
00:01:30,400 --> 00:01:30,840
怎么样去做

67
00:01:30,840 --> 00:01:36,220
好

68
00:01:36,220 --> 00:01:37,260
大家其实现在可以看到

69
00:01:37,260 --> 00:01:38,560
我们如果说需要写中间键

70
00:01:38,560 --> 00:01:40,900
是不是在app的midware里面

71
00:01:40,900 --> 00:01:41,300
对吧

72
00:01:41,300 --> 00:01:42,040
也就是这里

73
00:01:42,040 --> 00:01:43,680
我们来看一下

74
00:01:43,680 --> 00:01:45,320
我们之前所写的一个

75
00:01:45,320 --> 00:01:46,580
打印日志的一个中间键

76
00:01:46,580 --> 00:01:47,840
首先是不是传出一个方形

77
00:01:47,840 --> 00:01:48,600
然后retain一个

78
00:01:48,600 --> 00:01:49,680
类似call的中间键

79
00:01:49,680 --> 00:01:50,560
那么option是

80
00:01:50,560 --> 00:01:51,520
我们刚才是不是没有接收

81
00:01:51,520 --> 00:01:52,400
那么我们现在

82
00:01:52,400 --> 00:01:55,200
我们现在来配这个option

83
00:01:55,200 --> 00:01:56,300
看它到底是怎么用的

84
00:01:57,220 --> 00:02:06,100
那么我们呢就在这里去把它给输出一下

85
00:02:06,100 --> 00:02:09,580
比如说我们需要去传入一个content吧

86
00:02:09,580 --> 00:02:12,260
如果说没有的话我们就给它默认值

87
00:02:12,260 --> 00:02:15,920
好那么我们怎么样去配呢

88
00:02:15,920 --> 00:02:17,400
是不是在config里面呢

89
00:02:17,400 --> 00:02:18,700
config

90
00:02:18,700 --> 00:02:24,720
好那么这里我们是不是需要去配置config.log

91
00:02:27,220 --> 00:02:29,700
等于什么呢

92
00:02:29,700 --> 00:02:31,220
是不是等于content

93
00:02:31,220 --> 00:02:35,180
从配置来的日子

94
00:02:35,180 --> 00:02:37,180
好

95
00:02:37,180 --> 00:02:37,820
那这里呢

96
00:02:37,820 --> 00:02:38,440
我们就来试一下

97
00:02:38,440 --> 00:02:40,020
我们之前是不是在local里面

98
00:02:40,020 --> 00:02:41,500
把咱们的meetball给拴掉吧

99
00:02:41,500 --> 00:02:43,120
那么我们先把它给干掉

100
00:02:43,120 --> 00:02:45,220
我们来重启一下咱们的服务

101
00:02:45,220 --> 00:02:49,740
好

102
00:02:49,740 --> 00:02:50,100
整理

103
00:02:50,100 --> 00:02:51,960
大家是不是可以看到

104
00:02:51,960 --> 00:02:53,320
从配置来的日子

105
00:02:53,320 --> 00:02:54,720
那么我们的option是不是一个对象

106
00:02:54,720 --> 00:02:55,920
content从配置来的日子

107
00:02:55,920 --> 00:02:57,460
那么这里呢说明什么问题

108
00:02:57,460 --> 00:02:59,320
是不是我们在写中间键的时候

109
00:02:59,320 --> 00:03:01,200
是不是可以通过我们的一个配置

110
00:03:01,200 --> 00:03:02,980
包括你从你config传递过来之后

111
00:03:02,980 --> 00:03:05,400
我们是不是就可以去多样化我们的一个中间键了

112
00:03:05,400 --> 00:03:06,820
好

113
00:03:06,820 --> 00:03:09,000
这里呢就是中间键里面的一个配置

114
00:03:09,000 --> 00:03:10,680
那么如果说

115
00:03:10,680 --> 00:03:13,720
那么如果说你需要去

116
00:03:13,720 --> 00:03:15,320
我们刚才是不是在应用里面去

117
00:03:15,320 --> 00:03:17,720
我们刚才是不是在应用里面去添加了中间键

118
00:03:17,720 --> 00:03:18,140
对吧

119
00:03:18,140 --> 00:03:20,120
因为你在APP里面去添加是在你的应用中

120
00:03:20,120 --> 00:03:22,440
但是如果说你想在框架上面去添加中间键怎么办

121
00:03:22,440 --> 00:03:24,460
那么这里呢我们可以通过什么呢

122
00:03:25,460 --> 00:03:27,460
我们可以通过APP.conf一个

123
00:03:27,460 --> 00:03:28,860
那么APP.conf一个是什么呀

124
00:03:28,860 --> 00:03:31,860
是不是我们全局的配置文件都会挂载在APP这样一个对象下面

125
00:03:31,860 --> 00:03:33,560
你可以用到他的这样一个方法

126
00:03:33,560 --> 00:03:34,560
CoreMidwall

127
00:03:34,560 --> 00:03:37,260
那么CoreMidwall你就可以访问到我们框架

128
00:03:37,260 --> 00:03:38,860
它内核里面的一个中间的对象

129
00:03:38,860 --> 00:03:40,460
你可以在里面去进行一些数据的操作

130
00:03:40,460 --> 00:03:41,560
你比如说你去UNCF的

131
00:03:41,560 --> 00:03:43,860
也就是在前面去添加了一个report这样一个中间键

132
00:03:43,860 --> 00:03:44,460
那么呢

133
00:03:44,460 --> 00:03:46,360
这样就可以在我们的框架层面去生效

134
00:03:46,360 --> 00:03:48,560
那么我们为什么要在框架里面去添加工件键呢

135
00:03:48,560 --> 00:03:50,860
是不是因为和我们的执行顺序有关系

136
00:03:50,860 --> 00:03:51,560
对吧

137
00:03:51,560 --> 00:03:53,660
我们的应用是不是大于

138
00:03:53,660 --> 00:03:56,660
框架,框架是大于插件,他们有这样一个执行顺序

139
00:03:56,660 --> 00:04:08,660
是不是可以通过APP的config对象去给框架添加中间件了,对吧

140
00:04:08,660 --> 00:04:11,660
我们为什么不在应用层面去添加,是因为

141
00:04:11,660 --> 00:04:19,660
可能插件,是不是可能中间件执行顺序有一些要求

142
00:04:20,660 --> 00:04:23,220
那么如果说我们的中间间是不是都会在全局生效

143
00:04:23,220 --> 00:04:24,240
因为刚才我们讲到了

144
00:04:24,240 --> 00:04:26,780
我们每天见一个中间间就相当于在洋葱上面给添加了一层

145
00:04:26,780 --> 00:04:28,160
你每一次请求都会经过它

146
00:04:28,160 --> 00:04:30,700
但是如果说你只想针对单个路由生效怎么办

147
00:04:30,700 --> 00:04:32,180
其实是不是和我们Core类似

148
00:04:32,180 --> 00:04:34,260
我们Core其实也有类似这样的功能

149
00:04:34,260 --> 00:04:36,940
你比如说你在app.root你去get请求一个

150
00:04:36,940 --> 00:04:38,140
在那路由的时候

151
00:04:38,140 --> 00:04:41,600
你可以呢把我们的中间间当作为第二个参数给传递进去

152
00:04:41,600 --> 00:04:42,360
你比如说

153
00:04:42,360 --> 00:04:44,720
gzip等于app.midwall

154
00:04:44,720 --> 00:04:48,440
那么同样的我们中间间在一个对象其实也挂在app下面

155
00:04:50,660 --> 00:04:57,600
那么我们可以通过APP的Midwall属性拿到我们的中间键这样一个机制的对象

156
00:04:57,600 --> 00:05:01,340
然后你传给咱们的是不是Rooter的第二个参数就可以了

157
00:05:01,340 --> 00:05:03,480
那么这里就可以基于这样一个路由单独的去生效

158
00:05:03,480 --> 00:05:04,780
这里我就不给同学们去演示

159
00:05:04,780 --> 00:05:05,720
好

160
00:05:05,720 --> 00:05:09,800
那么除了我们需要单独去挂载在你某一个路由下面

161
00:05:09,800 --> 00:05:12,280
我们在框架里面其实会默认带一些中间键

162
00:05:12,280 --> 00:05:13,860
你比如说像BodyParser

163
00:05:13,860 --> 00:05:17,100
我们之前是不是在Core里面讲过一个怎么样去解析Body的例子

164
00:05:17,100 --> 00:05:19,300
其实在咱们的1GG里面

165
00:05:19,300 --> 00:05:20,300
它是自带这样的一些

166
00:05:20,300 --> 00:05:21,300
我们经常用的一些中间

167
00:05:21,300 --> 00:05:22,260
它会给集成进去

168
00:05:22,260 --> 00:05:23,360
你比如说BodyParser

169
00:05:23,360 --> 00:05:24,000
也就是解析Body

170
00:05:24,000 --> 00:05:25,800
那么如果说我们想修改它配置怎么办

171
00:05:25,800 --> 00:05:28,700
其实和我们刚才去配置咱们的log

172
00:05:28,700 --> 00:05:29,400
是不是一样的呀

173
00:05:29,400 --> 00:05:31,480
你比如说我们需要配置咱们的日志

174
00:05:31,480 --> 00:05:31,860
对吧

175
00:05:31,860 --> 00:05:32,720
我们需要传递一个content

176
00:05:32,720 --> 00:05:34,700
那么我们的BodyParser实际上是一样的

177
00:05:34,700 --> 00:05:36,340
你只需要在config里面

178
00:05:36,340 --> 00:05:37,640
去建一个BodyParser这样的属性

179
00:05:37,640 --> 00:05:40,020
然后去传给传递它一些配置就可以了

180
00:05:40,020 --> 00:05:42,440
那么这里可能还会有个需求是什么呢

181
00:05:42,440 --> 00:05:44,380
还会有些需求

182
00:05:44,380 --> 00:05:46,220
因为我们现在我们的一G是继续Core的

183
00:05:46,220 --> 00:05:47,900
但是如果说你有一些Core项目的中间

184
00:05:47,900 --> 00:05:48,820
你需要移植过来怎么办

185
00:05:48,820 --> 00:05:53,200
你比如说我们在Core里面有一个Compers这样的中间键

186
00:05:53,200 --> 00:05:58,400
那么如果说我们想去引渡到咱们的一G里面去使用

187
00:05:58,400 --> 00:05:58,760
怎么办

188
00:05:58,760 --> 00:06:00,140
其实呢我们可以直接

189
00:06:00,140 --> 00:06:02,700
你比如说我们比如说大罪看这里

190
00:06:02,700 --> 00:06:06,120
我们呢在APP里面是不是建立一个midware

191
00:06:06,120 --> 00:06:07,060
Compers.js

192
00:06:07,060 --> 00:06:08,380
那么呢其实我们可以直接

193
00:06:08,380 --> 00:06:10,260
我们可以直接require

194
00:06:10,260 --> 00:06:12,720
require一个Core-Compers

195
00:06:12,720 --> 00:06:14,340
然后呢把它给导出就可以了

196
00:06:14,340 --> 00:06:15,800
那么这样为什么可以呢

197
00:06:15,800 --> 00:06:17,700
其实同学们可以这样理解

198
00:06:17,700 --> 00:06:19,660
那么我们的Core Compass

199
00:06:19,660 --> 00:06:21,180
它其实是已经遵守了

200
00:06:21,180 --> 00:06:22,300
ETG这样的一个协议

201
00:06:22,300 --> 00:06:23,240
你比如说它可能

202
00:06:23,240 --> 00:06:25,460
它的中间的编写

203
00:06:25,460 --> 00:06:26,520
其实本身就是

204
00:06:26,520 --> 00:06:27,340
什么呢

205
00:06:27,340 --> 00:06:29,420
本身就是遵循了

206
00:06:29,420 --> 00:06:31,020
ETG它所需要的一个约定

207
00:06:31,020 --> 00:06:31,380
你比如说

208
00:06:31,380 --> 00:06:33,260
它本身就是Options和什么呢

209
00:06:33,260 --> 00:06:34,360
是不是和一个APP啊

210
00:06:34,360 --> 00:06:36,320
然后再去Return咱们的一个

211
00:06:36,320 --> 00:06:38,280
是不是Context和Next的一个函数

212
00:06:38,280 --> 00:06:38,760
说明什么

213
00:06:38,760 --> 00:06:40,680
是不是Core-Press它已经坚容了

214
00:06:40,680 --> 00:06:41,080
这一点

215
00:06:41,080 --> 00:06:42,300
所以就可以直接去引入

216
00:06:42,300 --> 00:06:43,720
然后把它给导出

217
00:06:44,480 --> 00:06:47,040
然后你在config里面去对它进行一些配置

218
00:06:47,040 --> 00:06:49,100
那么如果说还有一种情况

219
00:06:49,100 --> 00:06:52,160
如果我们使用到了Core中间键不符合咱们的一些参数条件怎么办

220
00:06:52,160 --> 00:06:54,980
也就是他没有比如说没有通过一个方性去return方性

221
00:06:54,980 --> 00:06:57,280
那么我们就可以是不是可以手动去包装啊

222
00:06:57,280 --> 00:06:59,600
你比如说我们直接呢module.export

223
00:06:59,600 --> 00:07:00,880
我们是不是自己手动的通过

224
00:07:00,880 --> 00:07:01,640
Option

225
00:07:01,640 --> 00:07:04,460
和App把它给包装一次啊对吧

226
00:07:04,460 --> 00:07:08,040
那么这里呢就是引入Core的一个中间键内容

227
00:07:08,040 --> 00:07:08,800
我们来看一下

228
00:07:08,800 --> 00:07:13,420
其实在咱们的中间键里面呢他还会有一些通用的配置你比如说

229
00:07:13,720 --> 00:07:16,660
你比如说有这样三个属性

230
00:07:16,660 --> 00:07:17,300
xenable

231
00:07:17,300 --> 00:07:18,140
enable很好理解

232
00:07:18,140 --> 00:07:19,240
是不是可以控制我们的中间键

233
00:07:19,240 --> 00:07:20,120
它是否了开启

234
00:07:20,120 --> 00:07:21,320
march和eaglore呢

235
00:07:21,320 --> 00:07:21,840
什么意思

236
00:07:21,840 --> 00:07:24,020
其实就可以去判断

237
00:07:24,020 --> 00:07:26,720
你哪些路由会使这个中间键生效

238
00:07:26,720 --> 00:07:27,860
哪些路由不会使它生效

239
00:07:27,860 --> 00:07:29,200
我们刚才是不是有一个方法

240
00:07:29,200 --> 00:07:29,620
是什么呢

241
00:07:29,620 --> 00:07:30,900
我们刚才有个什么方法

242
00:07:30,900 --> 00:07:32,060
是不是这样的router里面

243
00:07:32,060 --> 00:07:33,120
去传入这样一个中间键

244
00:07:33,120 --> 00:07:34,380
你就可以去做了单个路由生效

245
00:07:34,380 --> 00:07:35,240
但是呢

246
00:07:35,240 --> 00:07:37,000
往往有些情况是什么样子呢

247
00:07:37,000 --> 00:07:38,980
我们项目中可能有几十个路由

248
00:07:38,980 --> 00:07:39,660
你比如说

249
00:07:39,660 --> 00:07:40,780
你想以user开头的

250
00:07:40,780 --> 00:07:42,220
去经过一些和用户相当的中间键

251
00:07:42,220 --> 00:07:43,460
你又想呢

252
00:07:43,460 --> 00:07:44,680
比如说你订单开头的路由

253
00:07:44,680 --> 00:07:46,780
你走一些订单相关的中间间

254
00:07:46,780 --> 00:07:47,740
那么如果在一种情况

255
00:07:47,740 --> 00:07:49,120
你每一个路由去添加是不是很麻烦

256
00:07:49,120 --> 00:07:50,160
当然你就可以用到

257
00:07:50,160 --> 00:07:51,720
March和Eagle这样两个属性去做

258
00:07:51,720 --> 00:07:53,100
你比如说我们在

259
00:07:53,100 --> 00:07:55,660
我们G-Zapor这样一个中间间里面

260
00:07:55,660 --> 00:07:56,740
去加入一个March这样的属性

261
00:07:56,740 --> 00:07:57,940
我们是不是只有在经过

262
00:07:57,940 --> 00:07:59,040
Static这样一个路由的时候

263
00:07:59,040 --> 00:07:59,720
我们才会去

264
00:07:59,720 --> 00:08:01,800
走到G-Zapor这样一个中间间呢

265
00:08:01,800 --> 00:08:02,080
对吧

266
00:08:02,080 --> 00:08:02,580
可以说呢

267
00:08:02,580 --> 00:08:04,500
非常的方便

268
00:08:04,500 --> 00:08:05,540
那么March和Eagle

269
00:08:05,540 --> 00:08:08,120
它支持多种类型的配置方式

270
00:08:08,120 --> 00:08:09,620
那么March是不是顾名思义

271
00:08:09,620 --> 00:08:10,780
就是我匹配哪一个路由

272
00:08:10,780 --> 00:08:11,780
那么Eagle也很简单

273
00:08:11,780 --> 00:08:13,220
就是我忽略哪个路由

274
00:08:13,220 --> 00:08:13,980
当然可以接受

275
00:08:13,980 --> 00:08:14,480
支付串

276
00:08:14,480 --> 00:08:16,540
那么支付串是不是就是像static这样的

277
00:08:16,540 --> 00:08:17,220
那么正值呢

278
00:08:17,220 --> 00:08:18,560
正值同学们都学过吧

279
00:08:18,560 --> 00:08:19,760
那么这里呢我就不去多介绍了

280
00:08:19,760 --> 00:08:21,340
包括了你还可以去传入一个函数

281
00:08:21,340 --> 00:08:22,080
什么意思

282
00:08:22,080 --> 00:08:23,560
你比如说

283
00:08:23,560 --> 00:08:26,660
我们有时候是不是需要在请求里面去判断我们一些

284
00:08:26,660 --> 00:08:28,020
用户信息啊

285
00:08:28,020 --> 00:08:29,780
你比如说如果说我们的user agent

286
00:08:29,780 --> 00:08:31,480
你来自iphone或者ipad的对吧

287
00:08:31,480 --> 00:08:32,400
在那种条件

288
00:08:32,400 --> 00:08:34,500
你单独的去写支付串或者正值是匹配不到的

289
00:08:34,500 --> 00:08:35,960
因为你要根据咱们的request去判断

290
00:08:35,960 --> 00:08:37,800
所以说呢这个时候你就可以

291
00:08:37,800 --> 00:08:38,860
可以

292
00:08:38,860 --> 00:08:41,560
可以利用咱们的一个方形去解决这样一个问题

293
00:08:41,560 --> 00:08:44,400
好 这里呢就是我们中间键相关的那种

294
00:08:44,400 --> 00:08:48,780
我们呢一起来给同学们来回顾一下

295
00:08:48,780 --> 00:08:50,760
中间键

296
00:08:50,760 --> 00:09:00,220
好 我们刚才所讲解的中间键

297
00:09:00,220 --> 00:09:03,000
那么中间键它的一个基本雏形是什么样的

298
00:09:03,000 --> 00:09:04,280
同学们 是不是一个

299
00:09:04,280 --> 00:09:06,860
UPS和一个APP

300
00:09:06,860 --> 00:09:12,340
app 然后呢 咱们是不是需要再retain一个方形 它介绍什么是不是context和next呀

301
00:09:12,340 --> 00:09:24,020
好 那么这里呢就是在一击里面中间建它的一个基本形态 那么呢 我们还可以通过options和app是不是去传绿一些参数啊对吧

302
00:09:24,020 --> 00:09:33,340
那么options它来源于哪里 是不是来源于咱们的config的配置文件呢 那么app呢是不是app型的一个实例

303
00:09:34,260 --> 00:09:36,560
那我们除了讲解他怎么样去配置之外

304
00:09:36,560 --> 00:09:37,580
我们是不是

305
00:09:37,580 --> 00:09:40,660
我们是不是还讲到了咱们

306
00:09:40,660 --> 00:09:43,220
咱们怎么样去

307
00:09:43,220 --> 00:09:44,500
比如说我们刚才这样一种

308
00:09:44,500 --> 00:09:46,540
我们可以在哪些地方去添加中间键了

309
00:09:46,540 --> 00:09:48,080
是不是可以在应用

310
00:09:48,080 --> 00:09:50,140
和咱们的框架层面都可以去添加中间键

311
00:09:50,140 --> 00:09:57,300
那么在应用中怎么样去添加是不是APP里面

312
00:09:57,300 --> 00:09:59,860
Midwall这样的一个文件夹

313
00:09:59,860 --> 00:10:01,900
那么如果说你需要在框架层面去添加怎么办

314
00:10:01,900 --> 00:10:04,220
咱们的APP下面是不是有一个

315
00:10:04,260 --> 00:10:05,280
康贝克这样的对象

316
00:10:05,280 --> 00:10:06,780
他那里面是不是有一个call

317
00:10:06,780 --> 00:10:08,860
middlewall

318
00:10:08,860 --> 00:10:09,500
他那是一个什么

319
00:10:09,500 --> 00:10:10,080
是不是一个宿主

320
00:10:10,080 --> 00:10:10,840
然后呢

321
00:10:10,840 --> 00:10:11,920
你是不是可以在里面去

322
00:10:11,920 --> 00:10:13,280
做一些事情呢

323
00:10:13,280 --> 00:10:14,120
你可以在里面去

324
00:10:14,120 --> 00:10:15,920
进行宿主的一些操作

325
00:10:15,920 --> 00:10:16,320
然后呢

326
00:10:16,320 --> 00:10:17,480
去添加你想要的中间键

327
00:10:17,480 --> 00:10:18,520
那么如果说

328
00:10:18,520 --> 00:10:20,480
那么如果说我们想

329
00:10:20,480 --> 00:10:21,960
引入call的中间键怎么办

330
00:10:21,960 --> 00:10:25,240
怎么办

331
00:10:25,240 --> 00:10:26,980
引入call的中间键

332
00:10:26,980 --> 00:10:28,060
我们是不是可以

333
00:10:28,060 --> 00:10:29,960
自己包装啊

334
00:10:29,960 --> 00:10:32,320
或者呢

335
00:10:32,320 --> 00:10:33,740
它本身符合规范吧

336
00:10:33,740 --> 00:10:41,340
那么我们除了在括号中引入括号的中间键

337
00:10:41,340 --> 00:10:44,080
那么我们是不是还会咱们中间键是不是还有一些通用的配置

338
00:10:44,080 --> 00:10:45,980
那么通用的配置它有哪些

339
00:10:45,980 --> 00:10:47,360
是不是首先第一个enable

340
00:10:47,360 --> 00:10:50,360
enable是不是代表我们的中间键是不是可用的

341
00:10:50,360 --> 00:10:53,200
然后呢是不是还会有match匹配到哪一个路由

342
00:10:53,200 --> 00:10:55,820
那么和match对应呢是不是还有一个罗

343
00:10:55,820 --> 00:10:58,420
对吧好这里呢就是我们这节课的内容

