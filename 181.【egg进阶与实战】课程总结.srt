1
00:00:00,000 --> 00:00:03,560
好 那么到这里了其实我们就已经结束了web应用开发框架的所有内容

2
00:00:03,560 --> 00:00:05,900
也结束了和同学们一起欢乐的学习时光

3
00:00:05,900 --> 00:00:08,000
我们就来看一下咱们学了哪些内容

4
00:00:08,000 --> 00:00:09,120
首先咱们是不是学了Core1

5
00:00:09,120 --> 00:00:11,680
那么Core1它这个介绍很牛逼

6
00:00:11,680 --> 00:00:13,840
基于NodeGIS平台的下一代web开发框架

7
00:00:13,840 --> 00:00:14,860
其实它确实很屌

8
00:00:14,860 --> 00:00:15,920
那么它的核心是什么

9
00:00:15,920 --> 00:00:19,920
核心是不是就是咱们的一个洋葱模型先进先出

10
00:00:19,920 --> 00:00:22,860
那么每一层壳就是它的一个中间件

11
00:00:22,860 --> 00:00:25,500
那么讲完了咱们的一个洋葱模型和Core1

12
00:00:25,500 --> 00:00:27,100
咱们是不是后来又讲了1GG

13
00:00:27,100 --> 00:00:28,800
那么1GG呢其实它是由阿里巴巴

14
00:00:28,800 --> 00:00:30,340
开源的一套基于扣拓来用框架

15
00:00:30,340 --> 00:00:32,520
那么我们是不是介绍了什么是1GG

16
00:00:32,520 --> 00:00:34,140
那么1GG其实呢

17
00:00:34,140 --> 00:00:36,300
它的核心是什么呀

18
00:00:36,300 --> 00:00:38,220
是不是核心就是渐进式开发框架

19
00:00:38,220 --> 00:00:39,580
那么什么是渐进式呢

20
00:00:39,580 --> 00:00:40,760
是不是它可以通过插件

21
00:00:40,760 --> 00:00:42,880
去扩展它的一些内置对象

22
00:00:42,880 --> 00:00:44,100
比如说application对吧

23
00:00:44,100 --> 00:00:45,000
比如说context

24
00:00:45,000 --> 00:00:45,820
那么这样呢

25
00:00:45,820 --> 00:00:47,520
我们通过插件一层一层的去沉淀

26
00:00:47,520 --> 00:00:50,140
沉淀可以沉淀我们成为我们自己的一个框架

27
00:00:50,140 --> 00:00:51,920
那么这里呢就是它的渐进式的一个应用

28
00:00:51,920 --> 00:00:53,460
包括后面我们是不是也讲了一些

29
00:00:53,460 --> 00:00:55,160
关于1GG它的一些进阶的内容

30
00:00:55,160 --> 00:00:56,280
比如说我们怎么样去调试

31
00:00:56,280 --> 00:00:57,460
我们怎么样去收集日志

32
00:00:57,460 --> 00:00:59,220
包括了多进程之间的一些通信

33
00:00:59,220 --> 00:01:00,180
然后呢还有一些

34
00:01:00,180 --> 00:01:01,380
我们去怎么样去做

35
00:01:01,380 --> 00:01:03,060
比如说TRACKET去补回一些异常

36
00:01:03,060 --> 00:01:04,940
还有呢我们去写一些多实力的插件

37
00:01:04,940 --> 00:01:06,860
包括我们在多进程里面

38
00:01:06,860 --> 00:01:09,540
我们的agint是不是也会有许多的一些使用方式

39
00:01:09,540 --> 00:01:10,740
我们这里呢也讲了一种

40
00:01:10,740 --> 00:01:11,540
比较拿那种方式

41
00:01:11,540 --> 00:01:12,460
可能有些同学们没有听懂

42
00:01:12,460 --> 00:01:13,220
不过这里没有关系

43
00:01:13,220 --> 00:01:14,500
在后面的开发过程中

44
00:01:14,500 --> 00:01:15,260
或者说在工作过程中

45
00:01:15,260 --> 00:01:16,500
可以去慢慢的去理解

46
00:01:16,500 --> 00:01:17,280
好那么呢

47
00:01:17,280 --> 00:01:18,980
我们最后是不是还做了一个项目

48
00:01:18,980 --> 00:01:20,180
做了一个简单的一个博客

49
00:01:20,180 --> 00:01:21,700
就是说咱们的项目非常的小

50
00:01:21,700 --> 00:01:23,420
但是我们是不是完整的实现了增商改查

51
00:01:23,420 --> 00:01:24,820
那么也是特别重要的是什么呢

52
00:01:24,820 --> 00:01:25,860
我们是不是也抽象了service

53
00:01:25,860 --> 00:01:26,420
对吧

54
00:01:26,420 --> 00:01:28,760
然后呢也去进行了一些错误的处理

55
00:01:28,760 --> 00:01:29,380
包括呢

56
00:01:29,380 --> 00:01:30,660
返回值的处理

57
00:01:30,660 --> 00:01:32,060
包括也教会的同学们怎么样去

58
00:01:32,060 --> 00:01:34,140
怎么抽象一个通用的一个

59
00:01:34,140 --> 00:01:35,700
controller这个类

60
00:01:35,700 --> 00:01:37,220
那么这里呢

61
00:01:37,220 --> 00:01:38,120
我们讲了cova

62
00:01:38,120 --> 00:01:39,580
讲了egg也做了一个项目

63
00:01:39,580 --> 00:01:40,520
那么接下来

64
00:01:40,520 --> 00:01:41,840
我们来看一下

65
00:01:41,840 --> 00:01:43,280
未来我们的一个学习方向是什么

66
00:01:43,280 --> 00:01:44,280
其实大家觉得

67
00:01:44,280 --> 00:01:46,820
大家不要觉得我们增商改查非常的简单

68
00:01:46,820 --> 00:01:48,320
其实我们刚才写了一个博客

69
00:01:48,320 --> 00:01:48,940
也确实简单

70
00:01:48,940 --> 00:01:51,180
其实简单是因为有egg整个框架给我们做支撑

71
00:01:51,180 --> 00:01:54,880
因为ECG它的封装使我们去写业务非常简单

72
00:01:54,880 --> 00:01:57,880
但是后面我们依然依然需要持续的去学习

73
00:01:57,880 --> 00:01:58,300
为什么呢

74
00:01:58,300 --> 00:02:00,240
因为我们刚才是不是只讲到了什么呀

75
00:02:00,240 --> 00:02:01,860
是不是只讲了咱们对文章的一个正常改参

76
00:02:01,860 --> 00:02:02,980
我们有没有涉及到登录问题

77
00:02:02,980 --> 00:02:03,340
对吧

78
00:02:03,340 --> 00:02:05,540
你比如说我们能不能够去接入微信的登录接口

79
00:02:05,540 --> 00:02:06,660
比如说能不能去接入QQ

80
00:02:06,660 --> 00:02:07,840
或者说能不能接入微博

81
00:02:07,840 --> 00:02:08,980
这里是一个登录

82
00:02:08,980 --> 00:02:10,100
包括你还需要去健全

83
00:02:10,100 --> 00:02:12,820
你需不需要考虑哪些用户可以访问这些文章

84
00:02:12,820 --> 00:02:13,560
哪些用户不可以

85
00:02:13,560 --> 00:02:15,060
包括你是不是还需要有一个用户组

86
00:02:15,060 --> 00:02:16,140
好

87
00:02:16,140 --> 00:02:17,800
这里其实业务层面呢

88
00:02:17,800 --> 00:02:19,520
我们是不是也只是冰山一角

89
00:02:19,520 --> 00:02:20,260
包括服务部署

90
00:02:20,260 --> 00:02:22,200
我们刚才是在哪个环境去开发的

91
00:02:22,200 --> 00:02:23,120
是不是只在DV环境

92
00:02:23,120 --> 00:02:24,000
我们有没有讲过

93
00:02:24,000 --> 00:02:24,840
咱们怎么去部署

94
00:02:24,840 --> 00:02:25,480
是不是也没有

95
00:02:25,480 --> 00:02:27,300
好 这里是服务部署

96
00:02:27,300 --> 00:02:29,000
那么我们部署完成之后

97
00:02:29,000 --> 00:02:30,900
是不是还需要去做一些监控预警呢

98
00:02:30,900 --> 00:02:31,280
比如说

99
00:02:31,280 --> 00:02:33,280
咱们搜集到一些error怎么办

100
00:02:33,280 --> 00:02:34,660
你能不能够去人工的肉眼

101
00:02:34,660 --> 00:02:35,320
每天盯着电脑

102
00:02:35,320 --> 00:02:35,780
不可以吧

103
00:02:35,780 --> 00:02:37,020
那么有没有一些自动化的方式

104
00:02:37,020 --> 00:02:37,720
去告诉我们

105
00:02:37,720 --> 00:02:38,760
你的服务现在有些问题

106
00:02:38,760 --> 00:02:40,580
包括我们还需要去做一个

107
00:02:40,580 --> 00:02:41,580
日子分析系统

108
00:02:41,580 --> 00:02:42,600
比如说我们去上传一些

109
00:02:42,600 --> 00:02:43,380
咱们重要的参数

110
00:02:43,380 --> 00:02:44,580
看一下我们复习的性能

111
00:02:44,580 --> 00:02:45,840
那么做完性能之后

112
00:02:45,840 --> 00:02:47,060
可能你的用户量越来越大

113
00:02:47,060 --> 00:02:48,440
我们还需要去做一些数据库的优化

114
00:02:48,440 --> 00:02:50,180
我们提升我们的一个数据库的查询速度

115
00:02:50,180 --> 00:02:51,180
那么查询完成之后

116
00:02:51,180 --> 00:02:52,680
我们的用户是不是更大了呀

117
00:02:52,680 --> 00:02:53,500
那么更大之后怎么办

118
00:02:53,500 --> 00:02:55,680
我们可能会涉及到一个集群的问题

119
00:02:55,680 --> 00:02:57,360
包括我们需要去用多进程

120
00:02:57,360 --> 00:02:58,640
去压到我们复习的性能

121
00:02:58,640 --> 00:03:00,540
那么我们数据量过亿之后

122
00:03:00,540 --> 00:03:03,000
是不是要去做一些大数据的一些处理和分析

123
00:03:03,000 --> 00:03:04,960
实际上服务端的内容是非常之多的

124
00:03:04,960 --> 00:03:05,740
也是非常之广的

125
00:03:05,740 --> 00:03:08,360
希望同学们能够去持续的学习

126
00:03:08,360 --> 00:03:08,740
好

127
00:03:08,740 --> 00:03:10,600
那么如果所有同学们有任何的问题

128
00:03:10,600 --> 00:03:12,300
也可以去博学谷的一个评论区

129
00:03:12,300 --> 00:03:13,400
也可以去向我提问

130
00:03:13,400 --> 00:03:15,820
我看到了第一时间会向同学们去回复

131
00:03:15,820 --> 00:03:18,440
那么最后谢谢同学们的一个观看

