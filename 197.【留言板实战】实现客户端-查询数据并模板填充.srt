1
00:00:00,000 --> 00:00:04,960
好了我们获取到玩5K2数据之后

2
00:00:04,960 --> 00:00:05,780
下一步我们要做的

3
00:00:05,780 --> 00:00:07,460
就是把这个数据展示到这个页面中

4
00:00:07,460 --> 00:00:08,820
把这假数据给它替换掉

5
00:00:08,820 --> 00:00:10,400
具体我们这样来做

6
00:00:10,400 --> 00:00:12,380
在这有两条假数据

7
00:00:12,380 --> 00:00:13,620
其实我们剩一条就可以了

8
00:00:13,620 --> 00:00:14,140
删掉一条

9
00:00:14,140 --> 00:00:16,280
然后我们把这换成动态的数据

10
00:00:16,280 --> 00:00:19,020
那就通过VACO这个指令来做

11
00:00:19,020 --> 00:00:22,140
在这我们要提供便利的操作

12
00:00:22,140 --> 00:00:22,740
atom

13
00:00:22,740 --> 00:00:24,000
然后把这个所印给它加上

14
00:00:24,000 --> 00:00:25,140
然后这印

15
00:00:25,140 --> 00:00:27,040
从印份中取出来list

16
00:00:27,040 --> 00:00:29,000
然后把这个key给它加上

17
00:00:29,000 --> 00:00:30,620
通过冒号

18
00:00:30,620 --> 00:00:31,700
Key

19
00:00:31,700 --> 00:00:32,580
后边这个值呢

20
00:00:32,580 --> 00:00:33,100
我们用的indame

21
00:00:33,100 --> 00:00:34,760
好

22
00:00:34,760 --> 00:00:36,020
这是边地的动作

23
00:00:36,020 --> 00:00:37,340
然后呢

24
00:00:37,340 --> 00:00:38,620
我们获取里边的数据

25
00:00:38,620 --> 00:00:39,180
这数据呢

26
00:00:39,180 --> 00:00:40,440
有这个时间

27
00:00:40,440 --> 00:00:41,380
在这呢

28
00:00:41,380 --> 00:00:41,920
把这个替掉

29
00:00:41,920 --> 00:00:43,740
通过插指表达字

30
00:00:43,740 --> 00:00:45,480
然后这里边通过atom

31
00:00:45,480 --> 00:00:46,460
取出来这个data

32
00:00:46,460 --> 00:00:47,520
然后这呢

33
00:00:47,520 --> 00:00:48,060
还有姓名

34
00:00:48,060 --> 00:00:50,420
通过atom

35
00:00:50,420 --> 00:00:51,400
然后取出来这个

36
00:00:51,400 --> 00:00:52,240
UderName

37
00:00:52,240 --> 00:00:53,280
然后呢

38
00:00:53,280 --> 00:00:54,240
后边是具体的内容

39
00:00:54,240 --> 00:00:56,160
把这个整体替掉

40
00:00:56,160 --> 00:00:58,560
通过atom

41
00:00:59,000 --> 00:01:00,280
取出来的内容

42
00:01:00,280 --> 00:01:02,620
这是模板的填充

43
00:01:02,620 --> 00:01:04,720
然后我们看页面的效果

44
00:01:04,720 --> 00:01:05,720
这样的话我们就得到了

45
00:01:05,720 --> 00:01:06,800
动态的数据

46
00:01:06,800 --> 00:01:07,960
现在有三条数据

47
00:01:07,960 --> 00:01:09,500
所以说我们这儿展示出

48
00:01:09,500 --> 00:01:10,300
对应的三条数据

49
00:01:10,300 --> 00:01:11,000
这就没问题

50
00:01:11,000 --> 00:01:12,800
这是数据的填充

51
00:01:12,800 --> 00:01:14,820
如果说我们要获取更多的数据

52
00:01:14,820 --> 00:01:15,940
这个事就比较方便了

53
00:01:15,940 --> 00:01:18,780
我们只需要修改查询的规则就可以了

54
00:01:18,780 --> 00:01:20,180
比如说我们这儿再查询出

55
00:01:20,180 --> 00:01:21,320
有情链接

56
00:01:21,320 --> 00:01:22,540
那就是link

57
00:01:22,540 --> 00:01:24,660
这里边有l name

58
00:01:24,660 --> 00:01:26,520
还有就是l url

59
00:01:26,520 --> 00:01:27,140
然后保存

60
00:01:27,140 --> 00:01:28,780
我们看这儿又发了一个请求

61
00:01:28,780 --> 00:01:29,640
然后点开之后

62
00:01:29,640 --> 00:01:30,440
我们会发现

63
00:01:30,440 --> 00:01:32,520
这里边又多了一个数据

64
00:01:32,520 --> 00:01:33,160
就是link

65
00:01:33,160 --> 00:01:34,240
所以说接下来

66
00:01:34,240 --> 00:01:35,460
我们就可以把这有情链接

67
00:01:35,460 --> 00:01:36,220
拼交到这个下面

68
00:01:36,220 --> 00:01:38,520
那具体我们这样来做

69
00:01:38,520 --> 00:01:39,960
找到这位置有一个link

70
00:01:39,960 --> 00:01:41,400
在这我们加上一标签

71
00:01:41,400 --> 00:01:42,360
就可以做便利了

72
00:01:42,360 --> 00:01:43,900
在这通过微段后

73
00:01:43,900 --> 00:01:45,320
还是通过这个指令

74
00:01:45,320 --> 00:01:46,020
做便利

75
00:01:46,020 --> 00:01:48,380
我们需要给它添上这个所以

76
00:01:48,380 --> 00:01:51,140
用于这个key

77
00:01:51,140 --> 00:01:54,400
然后这通过这个infer中的link

78
00:01:54,400 --> 00:01:55,820
然后得到数据

79
00:01:55,820 --> 00:01:57,380
这的话我们要填充一下

80
00:01:57,380 --> 00:01:58,800
首先我们要加上t

81
00:01:58,800 --> 00:02:01,200
这我们用injax

82
00:02:01,200 --> 00:02:02,760
然后这个地址的话

83
00:02:02,760 --> 00:02:05,400
我们要加上atom中的l

84
00:02:05,400 --> 00:02:07,220
但是这是动态的数据

85
00:02:07,220 --> 00:02:09,880
所以说要通过绑定的方式来处理

86
00:02:09,880 --> 00:02:11,640
然后a标签当中的内容

87
00:02:11,640 --> 00:02:12,680
这个也要动态的填充

88
00:02:12,680 --> 00:02:15,980
这是atom中的l内容

89
00:02:15,980 --> 00:02:18,260
这是关于有情链接

90
00:02:18,260 --> 00:02:19,280
然后我们看

91
00:02:19,280 --> 00:02:19,920
因为的效果

92
00:02:19,920 --> 00:02:22,100
下面就多了三个有情链接

93
00:02:22,100 --> 00:02:23,560
这是第二个功能件

94
00:02:23,560 --> 00:02:25,380
如果说在这个基础值上

95
00:02:25,380 --> 00:02:26,360
又增加了一个新的功能

96
00:02:26,360 --> 00:02:27,380
要显示天气的信息

97
00:02:27,380 --> 00:02:29,420
这个时候我们把它显示到右侧这个位置

98
00:02:29,420 --> 00:02:31,340
这个数据我们同样的需要

99
00:02:31,340 --> 00:02:33,100
再加点查询的逻辑

100
00:02:33,100 --> 00:02:34,800
在这我们要查询的是天气

101
00:02:34,800 --> 00:02:37,300
这里边有两个字段

102
00:02:37,300 --> 00:02:38,600
一个就是

103
00:02:38,600 --> 00:02:39,560
WEN

104
00:02:39,560 --> 00:02:40,640
这是天气

105
00:02:40,640 --> 00:02:42,320
另外一个的话就是温度

106
00:02:42,320 --> 00:02:43,060
Temperature

107
00:02:43,060 --> 00:02:44,320
这是减写的

108
00:02:44,320 --> 00:02:44,860
然后保存

109
00:02:44,860 --> 00:02:46,200
好我们再看一下

110
00:02:46,200 --> 00:02:46,940
此次查询

111
00:02:46,940 --> 00:02:48,640
看这又多了一份数据

112
00:02:48,640 --> 00:02:49,380
就是天气

113
00:02:49,380 --> 00:02:51,360
然后我们把天气的数据

114
00:02:51,360 --> 00:02:52,380
在上面给它取出来

115
00:02:52,380 --> 00:02:53,320
在这个位置

116
00:02:53,320 --> 00:02:56,380
这呢我们就加一个div

117
00:02:56,380 --> 00:02:57,340
然后呢

118
00:02:57,340 --> 00:02:59,080
这个是天气

119
00:02:59,080 --> 00:03:00,180
后边这个值

120
00:03:00,180 --> 00:03:00,940
我们这样获取

121
00:03:00,940 --> 00:03:02,180
通过导法

122
00:03:02,180 --> 00:03:03,680
先得到这个weather

123
00:03:03,680 --> 00:03:05,080
然后呢

124
00:03:05,080 --> 00:03:07,740
再得到里边的这个wea

125
00:03:07,740 --> 00:03:08,960
然后考一份

126
00:03:08,960 --> 00:03:10,160
那下面这个呢是温度

127
00:03:10,160 --> 00:03:12,100
温度的话呢

128
00:03:12,100 --> 00:03:12,800
这个是temp

129
00:03:12,800 --> 00:03:14,420
好保存

130
00:03:14,420 --> 00:03:15,780
然后呢我们会发现

131
00:03:15,780 --> 00:03:17,120
这里边有了这个天气的信息

132
00:03:17,120 --> 00:03:18,400
好这就是

133
00:03:18,400 --> 00:03:20,080
关于查询数据的对界

134
00:03:20,080 --> 00:03:21,340
所以说呢

135
00:03:21,340 --> 00:03:22,740
我们随着这个业务量的复杂

136
00:03:22,740 --> 00:03:24,500
这里边我们查询的数据

137
00:03:24,500 --> 00:03:25,540
可能会变得越来越多

138
00:03:25,540 --> 00:03:27,360
但是对于GraphQ2的接口来说

139
00:03:27,360 --> 00:03:28,860
它这里边需要改动的

140
00:03:28,860 --> 00:03:30,460
仅仅是查询的规则

141
00:03:30,460 --> 00:03:31,960
然后拿到数据之后

142
00:03:31,960 --> 00:03:33,340
剩下的模板的填充

143
00:03:33,340 --> 00:03:34,700
这个相对就比较简单了

144
00:03:34,700 --> 00:03:36,320
这也是基于GraphQ2

145
00:03:36,320 --> 00:03:37,580
它开发模式的一个特点

146
00:03:37,580 --> 00:03:39,160
相比我们传统的

147
00:03:39,160 --> 00:03:40,480
Restful那种UI2来说

148
00:03:40,480 --> 00:03:41,980
我们这里边要省很多的劲

149
00:03:41,980 --> 00:03:43,140
因为我们不再需要

150
00:03:43,140 --> 00:03:44,140
去调用更多接口

151
00:03:44,140 --> 00:03:45,380
我们的接口只有这一个

152
00:03:45,380 --> 00:03:48,060
这是关于查询数据的对接

153
00:03:48,060 --> 00:03:49,300
主要就这么多

154
00:03:49,300 --> 00:03:50,920
好 到此为止

155
00:03:50,920 --> 00:03:52,960
关于查询的全部的功能

156
00:03:52,960 --> 00:03:53,820
我们就做完了

157
00:03:53,820 --> 00:03:56,640
这是关于客户端的具体的实现

158
00:03:56,640 --> 00:03:57,540
我们就先说到这里

