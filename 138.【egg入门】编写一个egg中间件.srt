1
00:00:00,000 --> 00:00:04,120
好 这节课我们来看一下怎么样在1GG里面去编写一个中间键

2
00:00:04,120 --> 00:00:06,820
那么中间键和Core里面的中间键的概念是非常的类似的

3
00:00:06,820 --> 00:00:08,980
甚至你可以认为它就是Core里面的中间键

4
00:00:08,980 --> 00:00:12,980
那么我们首先来看一下 我们先去编写一个中间键怎么去写

5
00:00:12,980 --> 00:00:16,060
比如说你在APP下面去创建一个Midwall这样的文件夹

6
00:00:16,060 --> 00:00:17,140
它也是咱们的一个约定

7
00:00:17,140 --> 00:00:20,760
然后你创建一个log.ts 说明我们是要写一个关于日志的log的中间键

8
00:00:20,760 --> 00:00:24,060
首先你去export的一个方形 它需要传入两个参数

9
00:00:24,060 --> 00:00:26,700
第一个是options 第二个是APP 它其实也是约定

10
00:00:26,700 --> 00:00:29,840
它的作用是什么呀

11
00:00:29,840 --> 00:00:33,340
你比如说我们的中间间是不是是不是一个方形去接受一个context和lux

12
00:00:33,340 --> 00:00:36,640
那么为什么他的外层还会包装一个方形去形成个币包呢

13
00:00:36,640 --> 00:00:38,960
其实option和app他也是一阶级的约定

14
00:00:38,960 --> 00:00:41,060
为了什么呀是不是为了

15
00:00:41,060 --> 00:00:42,460
为了你呢

16
00:00:42,460 --> 00:00:45,960
他有两个目的一个你可以去定制化一些配置对吧

17
00:00:45,960 --> 00:00:51,700
而且呢你可以去传入是不是他传入app这样一个对象

18
00:00:51,700 --> 00:00:54,140
你有可能在中间间里面你是不是要需要去使用

19
00:00:54,140 --> 00:00:55,620
你比如说app上面去挂载的一些方法

20
00:00:55,620 --> 00:00:57,240
你如果说需要去使用这样是不是很方便

21
00:00:57,240 --> 00:00:59,220
这点也是一阶级他这种去做的

22
00:00:59,840 --> 00:01:04,690
好 那么我们只要去写中间键 按照这种模式就可以了 写一个方形 然后再拟一个方形

23
00:01:04,690 --> 00:01:10,320
 第一个方形的传入opsy和app 然后里面呢 会返回一个币包 这里呢 就是币包了就是咱们库里面中间键的一个写法

24
00:01:10,320 --> 00:01:17,900
那么呢 我们除了

25
00:01:17,900 --> 00:01:21,300
我们除了去写中间键以外 是不是还需要在咱们的配置里面去注册呀

26
00:01:21,300 --> 00:01:25,470
咱们只需要在config的mitwork里面去传一个log 就代表我们已经引入了 那么其实这里呢

27
00:01:25,470 --> 00:01:27,200
 去编写了有两步 是不是写一个

28
00:01:27,920 --> 00:01:29,960
中间键了 第二个是不是就是咱们需要在配置里面

29
00:01:29,960 --> 00:01:33,560
引入啊 好 那么我们首先来看一下怎么去

30
00:01:33,560 --> 00:01:35,340
写个中间键

31
00:01:35,340 --> 00:01:41,740
那我们写中间键是不是需要在app的medewall这样一个文件夹下面

32
00:01:41,740 --> 00:01:44,300
好 那么我们就来创建一下

33
00:01:44,300 --> 00:01:47,640
medewall

34
00:01:47,640 --> 00:01:51,220
然后呢去创建一个log.js

35
00:01:51,220 --> 00:01:54,800
首先我们是不是需要module.export对吧

36
00:01:54,800 --> 00:01:57,880
module.export

37
00:01:57,920 --> 00:02:03,680
等于什么呢 咱们是不是要导出一个方形 第一个参数是option 第二个是app

38
00:02:03,680 --> 00:02:05,160
 然后我们再去retend一个

39
00:02:05,160 --> 00:02:09,960
再次retend一个方形了 async 他呢其实就跟咱们夸中间的写法是一样的

40
00:02:09,960 --> 00:02:17,200
这是咱们刚才去写了一个步骤 是吧 咱们现在完成的是第一步 写一个中间

41
00:02:17,200 --> 00:02:22,080
好 再来返回一个 比如说我们返回一个

42
00:02:23,600 --> 00:02:24,080
i think

43
00:02:24,080 --> 00:02:25,880
方形 log

44
00:02:25,880 --> 00:02:27,680
是不是接受两参数啊

45
00:02:27,680 --> 00:02:28,520
cord中间大家记得吧

46
00:02:28,520 --> 00:02:29,720
ctx next

47
00:02:29,720 --> 00:02:30,380
好

48
00:02:30,380 --> 00:02:31,260
那么我们就搞简单一点

49
00:02:31,260 --> 00:02:32,240
比如说我们去答应一个

50
00:02:32,240 --> 00:02:36,140
我是日志中间键

51
00:02:36,140 --> 00:02:37,360
好

52
00:02:37,360 --> 00:02:38,300
我们这里的只是演示

53
00:02:38,300 --> 00:02:39,560
简单一点

54
00:02:39,560 --> 00:02:41,000
然后再去执行咱们的一个

55
00:02:41,000 --> 00:02:42,140
 next

56
00:02:42,140 --> 00:02:44,480
好

57
00:02:44,480 --> 00:02:46,940
这样是不是就完成了我们的一个中间键了

58
00:02:46,940 --> 00:02:48,660
接下来是不是要写第二步是什么呀

59
00:02:48,660 --> 00:02:50,420
是不是需要去配置

60
00:02:50,420 --> 00:02:53,060
在咱们的config.default里面去配置

61
00:02:53,340 --> 00:02:54,340
是不是在meadware里面

62
00:02:54,340 --> 00:02:57,940
对吧 两步 这里呢 完成的是咱们的第二步

63
00:02:57,940 --> 00:03:01,780
好 闯入一个log 他传入的是什么呀 同学们

64
00:03:01,780 --> 00:03:05,620
同学们注意 他传入的是不是就是咱们刚才所创建的这样的一个文件名呢

65
00:03:05,620 --> 00:03:08,700
好 那么我们就来看一下 重新执行

66
00:03:08,700 --> 00:03:11,340
好 那我们随便访问一个一面 走理

67
00:03:11,340 --> 00:03:14,340
大家可以看到咱们中间键是不是已经生效了

68
00:03:14,340 --> 00:03:16,780
我是日子中间键 我们没访问一次 他是不是就打印一次啊

69
00:03:16,780 --> 00:03:18,820
是不是说明咱们的meadware生效了

70
00:03:18,820 --> 00:03:22,220
好 这里呢 就是咱们去编写 中间键的一个过程 我们一起来

71
00:03:22,900 --> 00:03:23,340
回顾一下

72
00:03:23,340 --> 00:03:28,760
编写一个中间键

73
00:03:28,760 --> 00:03:31,340
那么编写一个中间键是不是分为两步啊

74
00:03:31,340 --> 00:03:33,400
第一步是什么

75
00:03:33,400 --> 00:03:35,600
第一步是不是在

76
00:03:35,600 --> 00:03:37,400
APP的

77
00:03:37,400 --> 00:03:41,740
Vidware下面去创建一个你所需要插件的名称呢

78
00:03:41,740 --> 00:03:43,460
这里呢是创建中间键

79
00:03:43,460 --> 00:03:44,100
然后呢

80
00:03:44,100 --> 00:03:49,700
config.effort.js里面

81
00:03:49,700 --> 00:03:51,900
我们是不是需要去注册啊

82
00:03:51,900 --> 00:03:53,920
那么创建的时候咱们需要注意哪两个问题

83
00:03:53,920 --> 00:03:54,580
刚才是不是讲到了

84
00:03:54,580 --> 00:03:57,740
是不是需要写一个

85
00:03:57,740 --> 00:04:00,780
币包啊

86
00:04:00,780 --> 00:04:01,700
return

87
00:04:01,700 --> 00:04:07,340
return一个方形

88
00:04:07,340 --> 00:04:08,740
那么这个return的方形

89
00:04:08,740 --> 00:04:09,960
是不是和咱们call的中间间的写法

90
00:04:09,960 --> 00:04:10,580
是不是一模一样

91
00:04:10,580 --> 00:04:11,320
好

92
00:04:11,320 --> 00:04:13,060
那么这里呢就是我们这节课的内容

