1
00:00:00,000 --> 00:00:01,880
那么前面我们讲过

2
00:00:01,880 --> 00:00:04,400
如果想要搭建一个HTTPS的安全服务器

3
00:00:04,400 --> 00:00:06,720
我们需要得到CA机构颁发的认证证书

4
00:00:06,720 --> 00:00:07,660
但关键问题在于

5
00:00:07,660 --> 00:00:09,660
我现在不知道去哪里能找一个CA机构

6
00:00:09,660 --> 00:00:10,840
给我的服务器颁发证书

7
00:00:10,840 --> 00:00:12,100
来为我的服务器做一个认证

8
00:00:12,100 --> 00:00:13,220
那么找不到怎么办呢

9
00:00:13,220 --> 00:00:15,000
好我们就可以在自己的服务器里

10
00:00:15,000 --> 00:00:16,280
模拟一个CA机构

11
00:00:16,280 --> 00:00:18,220
然后自己给自己颁发一个证书

12
00:00:18,220 --> 00:00:19,280
在服务器中我们模拟CA

13
00:00:19,280 --> 00:00:20,420
生成一个本地的密要对

14
00:00:20,420 --> 00:00:22,340
这个密要对其实就是证书

15
00:00:22,340 --> 00:00:23,860
好那么这里给大家准备了一条命令

16
00:00:23,860 --> 00:00:25,260
那这条命令具体的哪个参数呢

17
00:00:25,260 --> 00:00:26,880
其实你不需要具体理解

18
00:00:26,880 --> 00:00:28,500
那当然如果你感兴趣可以自行搜索

19
00:00:28,500 --> 00:00:29,880
因为呢我们后面其实也是不用的

20
00:00:29,880 --> 00:00:30,880
毕竟它是一个模拟的

21
00:00:30,880 --> 00:00:32,780
我这里呢只给大家呢做一个简单的讲解

22
00:00:32,780 --> 00:00:35,140
好从这里这个K这里开始往前的这一部分

23
00:00:35,140 --> 00:00:37,460
这一部分是在模拟一个CA机构

24
00:00:37,460 --> 00:00:38,540
那么后面的这一部分呢

25
00:00:38,540 --> 00:00:40,340
是通过前面模拟的这个CA机构呢

26
00:00:40,340 --> 00:00:43,860
来生成一个有效期为365天的一个证书

27
00:00:43,860 --> 00:00:45,280
好那么这条命令呢

28
00:00:45,280 --> 00:00:47,860
我们需要拿到我们的服务器当中去执行一下

29
00:00:47,860 --> 00:00:51,240
我们就能够通过模拟CA的方式来去生成一个名号队

30
00:00:51,240 --> 00:00:53,060
也就是颁发一个证书

31
00:00:53,060 --> 00:00:54,140
OK打开我们的服务器

32
00:00:54,140 --> 00:00:56,360
那么这里我给大家准备了一个服务器

33
00:00:56,360 --> 00:00:57,320
已经连接上了

34
00:00:57,320 --> 00:00:57,680
强烈一下

35
00:00:57,680 --> 00:00:59,380
我这里使用的是U12

36
00:00:59,380 --> 00:01:00,840
就是Linux的一个操作系统

37
00:01:00,840 --> 00:01:02,940
因为我们的服务器大部分采用的都是Linux

38
00:01:02,940 --> 00:01:06,020
很少有使用Mac操作系统或者是Windows系统作为服务器的

39
00:01:06,020 --> 00:01:08,480
那如果你使用的是Windows或者是Mac

40
00:01:08,480 --> 00:01:09,720
那么如果是Mac的话

41
00:01:09,720 --> 00:01:11,580
你像刚才一样去执行那个命令就可以了

42
00:01:11,580 --> 00:01:12,640
但如果你是Windows

43
00:01:12,640 --> 00:01:14,940
那么你必须的安装一个OpenSSL

44
00:01:14,940 --> 00:01:17,240
这样的一个软件才是可以执行那个命令的

45
00:01:17,240 --> 00:01:18,760
具体的使用方法我就不再介绍了

46
00:01:18,760 --> 00:01:21,180
那么我们以使用最广泛的Linux作为服务器

47
00:01:21,180 --> 00:01:22,520
来去执行这个命令

48
00:01:22,520 --> 00:01:22,820
好

49
00:01:22,820 --> 00:01:23,340
连接上之后呢

50
00:01:23,340 --> 00:01:25,480
我直接把命令行复制过来

51
00:01:25,480 --> 00:01:28,540
然后在我们的服务器当中去执行

52
00:01:28,540 --> 00:01:29,780
OK

53
00:01:29,780 --> 00:01:30,480
那么此时呢

54
00:01:30,480 --> 00:01:31,460
他就会有一个提示啊

55
00:01:31,460 --> 00:01:32,500
问我的这个名字

56
00:01:32,500 --> 00:01:33,260
国家地区啊

57
00:01:33,260 --> 00:01:34,060
那我呢就随便写

58
00:01:34,060 --> 00:01:34,620
这里的内容呢

59
00:01:34,620 --> 00:01:35,280
就随便写一个就可以了

60
00:01:35,280 --> 00:01:35,900
那么再问呢

61
00:01:35,900 --> 00:01:36,960
我也是CN啊

62
00:01:36,960 --> 00:01:38,280
问我在哪个城市呢

63
00:01:38,280 --> 00:01:39,120
我就是北京

64
00:01:39,120 --> 00:01:40,060
那么问我的组织呢

65
00:01:40,060 --> 00:01:40,980
我就随便来一个吧

66
00:01:40,980 --> 00:01:41,560
XL是吧

67
00:01:41,560 --> 00:01:42,020
XL

68
00:01:42,020 --> 00:01:42,660
这个呢无所谓

69
00:01:42,660 --> 00:01:43,320
你随便填就可以了

70
00:01:43,320 --> 00:01:43,680
但是呢

71
00:01:43,680 --> 00:01:44,560
这里有个叫common name

72
00:01:44,560 --> 00:01:45,820
这个呢是比较重要的

73
00:01:45,820 --> 00:01:46,200
如果

74
00:01:46,200 --> 00:01:47,020
注意看最底下

75
00:01:47,020 --> 00:01:48,700
如果你是真实的

76
00:01:48,700 --> 00:01:49,840
不是模拟CA的

77
00:01:49,840 --> 00:01:50,940
我们现在是模拟CA的

78
00:01:50,940 --> 00:01:51,420
你可以随便写

79
00:01:51,420 --> 00:01:53,040
如果是真实的CA机构的话

80
00:01:53,040 --> 00:01:55,660
那这个名字是要和你的域名对应起来的

81
00:01:55,660 --> 00:01:58,340
后面我们在申请国际认证的CA证书的时候

82
00:01:58,340 --> 00:01:59,560
我们就会对大家呢

83
00:01:59,560 --> 00:02:00,520
去额外的讲解一下

84
00:02:00,520 --> 00:02:00,820
好

85
00:02:00,820 --> 00:02:01,420
那么当然在这里呢

86
00:02:01,420 --> 00:02:02,140
我就随意写一个

87
00:02:02,140 --> 00:02:03,020
就QQ.com就可以了

88
00:02:03,020 --> 00:02:03,460
随便写

89
00:02:03,460 --> 00:02:03,720
随便写

90
00:02:03,720 --> 00:02:04,260
叫什么都可以

91
00:02:04,260 --> 00:02:04,840
好

92
00:02:04,840 --> 00:02:05,360
那么再来问啊

93
00:02:05,360 --> 00:02:06,100
说你的邮箱地址是什么

94
00:02:06,100 --> 00:02:07,560
那QQ.com就随便写的

95
00:02:07,560 --> 00:02:08,660
好

96
00:02:08,660 --> 00:02:09,060
那么此时呢

97
00:02:09,060 --> 00:02:10,260
我们就能够LS看一眼

98
00:02:10,260 --> 00:02:11,300
好

99
00:02:11,300 --> 00:02:12,760
我们呢在我们的服务器当中呢

100
00:02:12,760 --> 00:02:16,220
就生成了两个一个叫CERT.CRT这样的证书

101
00:02:16,220 --> 00:02:18,840
同时我们的模拟机构叫RSA下号线Provit.K

102
00:02:18,840 --> 00:02:20,240
这是声明的两个文件

103
00:02:20,240 --> 00:02:21,880
这个呢其实也就是密要对的

104
00:02:21,880 --> 00:02:25,180
好那这就是在我们的本地生成我们的CERT证书

