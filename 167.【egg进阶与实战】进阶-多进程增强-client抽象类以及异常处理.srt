1
00:00:00,520 --> 00:00:04,600
好 这里呢我们就来看一下具体怎么样去编码 因为我们刚才是不是已经介绍了一个核心思想

2
00:00:04,600 --> 00:00:07,670
那么编码这块呢可能说稍微有点难度 如果说能够听懂的同学当然更好

3
00:00:07,670 --> 00:00:09,720
 听不懂的其实也没关系 因为这一块的确实是

4
00:00:09,720 --> 00:00:10,760
非常有难度

5
00:00:10,760 --> 00:00:12,280
好 那么我们来看一下

6
00:00:12,280 --> 00:00:15,360
首先呢我们将客户端接口抽象为下面两大类 那么什么是客户端

7
00:00:15,360 --> 00:00:17,400
客户端是不是就是我们的

8
00:00:17,400 --> 00:00:23,560
我们的一机机的应用 对吧 好 那么抽象为下面两大类 那么这里其实我们需要重点关注什么呀

9
00:00:23,560 --> 00:00:29,700
首先我刚才跟同学去解释了什么是客户端 那么这里呢 需要再给同学解释一下什么是抽象

10
00:00:30,000 --> 00:00:31,240
那么如果说学过家吧

11
00:00:31,240 --> 00:00:32,900
或者说学过一些强类型语言的同学

12
00:00:32,900 --> 00:00:34,760
可能会对抽象有一些了解

13
00:00:34,760 --> 00:00:36,120
如果没有了也没关系

14
00:00:36,120 --> 00:00:36,920
那么比如说

15
00:00:36,920 --> 00:00:39,320
比如说在我们的强弹编程领域里面

16
00:00:39,320 --> 00:00:40,120
是不是有一个类的概念

17
00:00:40,120 --> 00:00:40,860
对吧

18
00:00:40,860 --> 00:00:42,920
比如说我们去实现一个人在那个类

19
00:00:42,920 --> 00:00:44,220
那么人他一般会有什么方法

20
00:00:44,220 --> 00:00:45,020
或者说有哪些属性

21
00:00:45,020 --> 00:00:46,560
那么人是不是一般

22
00:00:46,560 --> 00:00:48,100
你比如说我们会有些年龄

23
00:00:48,100 --> 00:00:48,760
年龄四八岁

24
00:00:48,760 --> 00:00:49,160
对吧

25
00:00:49,160 --> 00:00:50,740
然后呢会有一些姓名

26
00:00:50,740 --> 00:00:51,580
姓名比如说小红

27
00:00:51,580 --> 00:00:52,740
然后呢会有身份证号码

28
00:00:52,740 --> 00:00:53,700
包括了还有一些

29
00:00:53,700 --> 00:00:55,760
比如说你有五官对吧

30
00:00:55,760 --> 00:00:57,520
眼睛鼻子耳朵什么等等的

31
00:00:57,520 --> 00:00:58,340
都是人的一些属性

32
00:00:58,340 --> 00:01:00,180
那么这里呢是类的一个概念

33
00:01:00,180 --> 00:01:01,660
那么什么是抽象的一个概念呢

34
00:01:01,660 --> 00:01:02,820
抽象其实就是

35
00:01:02,820 --> 00:01:04,060
我去给你

36
00:01:04,060 --> 00:01:04,700
我去告诉你

37
00:01:04,700 --> 00:01:05,580
你去给我创造一个人

38
00:01:05,580 --> 00:01:07,440
那么我会告诉你人有哪些属性

39
00:01:07,440 --> 00:01:08,160
比如说人呢

40
00:01:08,160 --> 00:01:09,180
有眼睛

41
00:01:09,180 --> 00:01:09,540
有鼻子

42
00:01:09,540 --> 00:01:09,820
有嘴巴

43
00:01:09,820 --> 00:01:10,220
有耳朵

44
00:01:10,220 --> 00:01:11,320
但是眼睛长什么样

45
00:01:11,320 --> 00:01:12,060
鼻子长什么样

46
00:01:12,060 --> 00:01:13,000
有几只眼睛

47
00:01:13,000 --> 00:01:13,720
有几个耳朵

48
00:01:13,720 --> 00:01:15,040
那么其实都是你自己去定义的

49
00:01:15,040 --> 00:01:15,760
这里呢就是抽象

50
00:01:15,760 --> 00:01:16,800
抽象是什么呀

51
00:01:16,800 --> 00:01:18,100
其实就是对类的一个描述

52
00:01:18,100 --> 00:01:18,920
我会告诉你

53
00:01:18,920 --> 00:01:20,060
你需要去创点一个什么样的类

54
00:01:20,060 --> 00:01:20,900
所以呢同学们

55
00:01:20,900 --> 00:01:22,660
需要去记住的一点是什么呢

56
00:01:22,660 --> 00:01:25,480
抽象是类的描述

57
00:01:25,480 --> 00:01:26,360
其实说白了一点

58
00:01:26,360 --> 00:01:27,120
简单一点就是

59
00:01:27,120 --> 00:01:28,500
是不是对咱们一个类的约束

60
00:01:28,500 --> 00:01:29,860
告诉你怎么去创建类

61
00:01:29,860 --> 00:01:30,280
好

62
00:01:30,280 --> 00:01:31,500
那么接下来我们就来看一下

63
00:01:31,500 --> 00:01:33,860
这也是对客户端接口的一个规范

64
00:01:33,860 --> 00:01:35,720
对于符合规范的客户端

65
00:01:35,720 --> 00:01:36,900
我们自动的将其包装为

66
00:01:36,900 --> 00:01:37,880
led follow模式

67
00:01:37,880 --> 00:01:38,640
也就是说

68
00:01:38,640 --> 00:01:40,540
你如果说创建了下面的这样一种类

69
00:01:40,540 --> 00:01:41,980
你创建了这样一个类

70
00:01:41,980 --> 00:01:42,720
那么这样一个类

71
00:01:42,720 --> 00:01:43,960
它需要有哪些方法

72
00:01:43,960 --> 00:01:44,680
它给你规定了

73
00:01:44,680 --> 00:01:45,700
也就是刚才

74
00:01:45,700 --> 00:01:46,800
这样的一个客户端

75
00:01:46,800 --> 00:01:47,820
它规定你什么呢

76
00:01:47,820 --> 00:01:49,500
规定你必须实现一个订阅发布

77
00:01:49,500 --> 00:01:50,160
和一个调用类

78
00:01:50,160 --> 00:01:51,600
那么订阅发布的方法是什么呀

79
00:01:51,600 --> 00:01:52,840
subscribe和publish

80
00:01:52,840 --> 00:01:53,900
也就是说你这样一个类

81
00:01:53,900 --> 00:01:56,020
你必须有subscribe和publish

82
00:01:56,020 --> 00:01:56,700
这样的两个方法

83
00:01:56,700 --> 00:01:59,340
那么subscribe在我们的前段的认知领域里面

84
00:01:59,340 --> 00:01:59,800
它是做什么呢

85
00:01:59,800 --> 00:02:00,640
是不是做发布订阅了

86
00:02:00,640 --> 00:02:01,140
所以说呢

87
00:02:01,140 --> 00:02:02,980
你只需要去实现你自己的发布订阅就可以了

88
00:02:02,980 --> 00:02:03,700
那么你怎么实现

89
00:02:03,700 --> 00:02:04,000
它不管

90
00:02:04,000 --> 00:02:04,880
你比如说实现一个人

91
00:02:04,880 --> 00:02:06,160
我告诉你你要有眼睛

92
00:02:06,160 --> 00:02:06,820
那么你是红眼睛

93
00:02:06,820 --> 00:02:07,080
蓝眼睛

94
00:02:07,080 --> 00:02:07,400
黄眼睛

95
00:02:07,400 --> 00:02:07,740
白眼睛

96
00:02:07,740 --> 00:02:08,460
那没有人管你

97
00:02:08,460 --> 00:02:09,480
你知道有眼睛就OK了

98
00:02:09,480 --> 00:02:10,380
包括调用类

99
00:02:10,380 --> 00:02:11,780
调用来的其实就是获取我们数据了

100
00:02:11,780 --> 00:02:12,960
那么你只要有就可以了

101
00:02:12,960 --> 00:02:14,380
它并不要求你怎么样去调用

102
00:02:14,380 --> 00:02:15,100
或者说怎么样去获取

103
00:02:15,100 --> 00:02:17,060
那么我们就来看一下咱们一个扣断实例

104
00:02:17,060 --> 00:02:17,300
好

105
00:02:17,300 --> 00:02:17,980
那么这里呢

106
00:02:17,980 --> 00:02:18,260
首先呢

107
00:02:18,260 --> 00:02:21,620
我们会通过class base去引入一个叫做sdk base

108
00:02:21,620 --> 00:02:22,720
那么它是做什么的呢

109
00:02:22,720 --> 00:02:24,120
我们来看一下它的一个github

110
00:02:24,120 --> 00:02:25,240
它的一个文档

111
00:02:25,240 --> 00:02:26,100
这里呢

112
00:02:26,100 --> 00:02:26,480
是我呢

113
00:02:26,480 --> 00:02:28,940
从1G它那个官网找到了在那个sdk叫做base

114
00:02:28,940 --> 00:02:30,760
那么为什么说这节课的内容难

115
00:02:30,760 --> 00:02:31,820
难的一个原因是什么呢

116
00:02:31,820 --> 00:02:32,280
其实就是

117
00:02:32,280 --> 00:02:33,820
其实资料不是很好找

118
00:02:33,820 --> 00:02:35,720
我们可以看到它那个官方的一个介绍是什么

119
00:02:35,720 --> 00:02:36,880
大家可以看到

120
00:02:36,880 --> 00:02:37,720
就一句话

121
00:02:37,720 --> 00:02:40,800
base class for SDK with some common useful functions

122
00:02:40,800 --> 00:02:42,760
是不是说的挺白说

123
00:02:42,760 --> 00:02:44,080
他说他提供了一些有用的方形

124
00:02:44,080 --> 00:02:44,560
有什么用呢

125
00:02:44,560 --> 00:02:46,620
其实对我来说没任何意义

126
00:02:46,620 --> 00:02:46,820
好

127
00:02:46,820 --> 00:02:47,800
那么他的例子呢

128
00:02:47,800 --> 00:02:49,420
其实写的也非常的简陋

129
00:02:49,420 --> 00:02:50,240
什么都看不出来

130
00:02:50,240 --> 00:02:50,980
那么我没关系

131
00:02:50,980 --> 00:02:52,060
我们直接来看咱们的讲义

132
00:02:52,060 --> 00:02:53,300
其实这段

133
00:02:53,300 --> 00:02:54,400
其实这段例子的

134
00:02:54,400 --> 00:02:55,340
他的一个想法

135
00:02:55,340 --> 00:02:56,780
其实也是老师我自己呢

136
00:02:56,780 --> 00:02:58,360
去根据

137
00:02:58,360 --> 00:02:59,260
根据他的一些官方

138
00:02:59,260 --> 00:03:00,120
包括我的经验去猜测

139
00:03:00,120 --> 00:03:00,380
好

140
00:03:00,380 --> 00:03:00,880
我们来看一下

141
00:03:00,880 --> 00:03:02,680
首先我们一个class

142
00:03:02,680 --> 00:03:04,480
client extends base

143
00:03:04,480 --> 00:03:05,040
我们呢

144
00:03:05,040 --> 00:03:05,640
是继承base

145
00:03:05,640 --> 00:03:06,880
然后他会有一些自己的方法

146
00:03:06,880 --> 00:03:07,180
对吧

147
00:03:07,180 --> 00:03:08,100
会计成一下贝斯的方法

148
00:03:08,100 --> 00:03:09,020
首先我们来看一下

149
00:03:09,020 --> 00:03:10,480
我们刚才是不是讲到了

150
00:03:10,480 --> 00:03:12,440
咱们在一个抽象类需要去实现什么呀

151
00:03:12,440 --> 00:03:14,400
是不是需要去实现subscribe和publish

152
00:03:14,400 --> 00:03:15,120
所以呢

153
00:03:15,120 --> 00:03:16,980
我们这一个客户端的类

154
00:03:16,980 --> 00:03:19,620
是不是实现了subscribe和publish

155
00:03:19,620 --> 00:03:20,120
但是呢

156
00:03:20,120 --> 00:03:20,760
具体怎么样去实现

157
00:03:20,760 --> 00:03:21,560
这里的例子里面没有写

158
00:03:21,560 --> 00:03:21,980
因为这里呢

159
00:03:21,980 --> 00:03:22,640
会交给我们自己

160
00:03:22,640 --> 00:03:23,280
然后呢

161
00:03:23,280 --> 00:03:24,640
这里还会有一个方法是什么呢

162
00:03:24,640 --> 00:03:25,680
sync get data

163
00:03:25,680 --> 00:03:27,020
其实这里它有一个注释

164
00:03:27,020 --> 00:03:28,380
获取数据invoke

165
00:03:28,380 --> 00:03:28,840
什么意思

166
00:03:28,840 --> 00:03:30,260
是不是get data

167
00:03:30,260 --> 00:03:33,060
其实就是我们所实现的一个调用类啊

168
00:03:33,060 --> 00:03:33,340
对吧

169
00:03:33,340 --> 00:03:35,220
也就是刚才这里所介绍的

170
00:03:35,220 --> 00:03:36,240
我们也需要去同样的

171
00:03:36,240 --> 00:03:38,280
不除了订阅发布还需要实现个调用类

172
00:03:38,280 --> 00:03:38,800
那么

173
00:03:38,800 --> 00:03:40,600
就是我们的调用类

174
00:03:40,600 --> 00:03:43,400
他其实不管你怎么样去获取数据的你只要去实现他就可以了

175
00:03:43,400 --> 00:03:45,460
那么大家其实还需要注意点是什么呢

176
00:03:45,460 --> 00:03:47,760
subscribe publish

177
00:03:47,760 --> 00:03:50,840
和我们的给 data就是都是我们刚才抽象对类的一个规定

178
00:03:50,840 --> 00:03:52,620
那么这里还会有一句话是什么

179
00:03:52,620 --> 00:03:53,900
this and ready to

180
00:03:53,900 --> 00:03:55,700
调用了this and ready

181
00:03:55,700 --> 00:03:57,740
那么ready这样一个方法从哪里来是从base来

182
00:03:57,740 --> 00:03:58,520
对吧

183
00:03:58,520 --> 00:04:00,300
那么他做了些什么事情或者说有什么意义啊

184
00:04:00,300 --> 00:04:01,580
其实就是告诉我们

185
00:04:01,580 --> 00:04:03,380
在抽实化成功以后记得ready

186
00:04:03,380 --> 00:04:04,140
其实呢

187
00:04:04,140 --> 00:04:05,680
这里的目的是什么

188
00:04:05,940 --> 00:04:13,880
你传入了Q就代表了你告诉他你告诉了这样一个实力啊我们已经初始化成功了也就是说了你什么时候啊你的实力准备好了你就可以去调理事的Ready

189
00:04:13,880 --> 00:04:18,840
就可以代表你一个实力初始化创建成功了其实这个方法是sdk贝斯所提供的

190
00:04:18,840 --> 00:04:23,920
那么这里呢就是我们客户端这样的一个实力我们来看一下一场处理是怎么去做的

191
00:04:23,920 --> 00:04:30,260
那么如果说Leader如果死掉会触发新的一轮端口争夺那么争夺到端口的那个实力呢被推行为新的Leader

192
00:04:30,260 --> 00:04:35,120
其实是不是和我们刚才的LeaderFollow那种现场模式类似啊

193
00:04:35,380 --> 00:04:38,870
首先有个leader 然后还会有我们的功能 包括还有一些闲杂人的 对吧

194
00:04:38,870 --> 00:04:41,020
 那么如果说出现了一把鼠 就会有闲杂人去抢

195
00:04:41,020 --> 00:04:42,540
其实和我们这里是类似的

196
00:04:42,540 --> 00:04:44,860
那么为了保证我们leader和follow之间的通道健康

197
00:04:44,860 --> 00:04:46,380
他们的通道是什么 是不是socket

198
00:04:46,380 --> 00:04:48,440
需要来引入定时的心跳检测机制

199
00:04:48,440 --> 00:04:52,280
如果follow在固定时间没有发送心跳包 那么leader和follow就会主动断开

200
00:04:52,280 --> 00:04:53,560
然后follow重新出始化

201
00:04:53,560 --> 00:04:54,320
什么意思

202
00:04:54,320 --> 00:04:56,620
其实就是和我们 比如说

203
00:04:56,620 --> 00:04:58,680
我们去 假设我们去抢救某一个人

204
00:04:58,680 --> 00:05:01,740
看这个人还有没有活着 我们是不是看他的心跳还有没有 如果有 这个人就活着

205
00:05:02,000 --> 00:05:04,820
其实这里呢,涉及到我们sok的里面的思想,比如说我们有一个定时器

206
00:05:04,820 --> 00:05:06,100
我每过五秒

207
00:05:06,100 --> 00:05:10,700
去给你发一个请求,看你还在不在,如果说你有响应说明你是不是还活着,如果没有响应说明

208
00:05:10,700 --> 00:05:15,060
咱们的一个佛罗是不是已经死了,我们需要去重新创建一个新的,这里呢就是咱们异常处理它的一个

209
00:05:15,060 --> 00:05:15,820
心跳的一个过程

210
00:05:15,820 --> 00:05:20,180
那么呢,我们来总结一下这几个内容

211
00:05:20,180 --> 00:05:24,280
那么我们刚才是不是其实

212
00:05:24,280 --> 00:05:27,860
在讲怎么样去编码,首先呢我们会有一个什么,是不是客户端的

213
00:05:27,860 --> 00:05:28,620
抽象类

214
00:05:29,140 --> 00:05:32,720
好 那个客户的抽象类需要去实现哪个方法 是不是需要去实现一个

215
00:05:32,720 --> 00:05:35,800
发布订阅 发布订阅

216
00:05:35,800 --> 00:05:37,580
还有一个什么 是不是

217
00:05:37,580 --> 00:05:39,120
调用类啊

218
00:05:39,120 --> 00:05:41,940
那么调用类是做什么 是不是读取

219
00:05:41,940 --> 00:05:42,960
数据的

220
00:05:42,960 --> 00:05:46,810
好 那么发布订阅就是我们的publish和subscribe 其实他不管你怎么样去实现

221
00:05:46,810 --> 00:05:48,080
 他只是对咱们一个

222
00:05:48,080 --> 00:05:51,420
对什么 是不是只是对类的一个抽象

223
00:05:51,420 --> 00:05:52,440
只是

224
00:05:52,440 --> 00:05:55,500
对类的抽象

225
00:05:57,040 --> 00:05:58,960
随便怎么实现

226
00:05:58,960 --> 00:06:02,560
然后我们刚才是不是还会讲了一些异常的处理

227
00:06:02,560 --> 00:06:04,620
那么有两种情况

228
00:06:04,620 --> 00:06:06,160
首先那leader是不是会死掉

229
00:06:06,160 --> 00:06:07,460
那么leader死掉怎么办

230
00:06:07,460 --> 00:06:08,580
是不是follow

231
00:06:08,580 --> 00:06:11,300
follow会抢

232
00:06:11,300 --> 00:06:11,640
对吧

233
00:06:11,640 --> 00:06:12,120
谁抢到了

234
00:06:12,120 --> 00:06:13,020
谁就是新的leader

235
00:06:13,020 --> 00:06:15,520
那么我们怎么样去检测follow有没有死呢

236
00:06:15,520 --> 00:06:17,840
是不是此时会引入一种什么心跳机制

237
00:06:17,840 --> 00:06:21,240
那么心跳机制呢

238
00:06:21,240 --> 00:06:22,480
其实也是我们自己去实现的

239
00:06:22,480 --> 00:06:24,020
因为我们在socket的领域里面

240
00:06:24,020 --> 00:06:24,920
心跳是非常多的

241
00:06:24,920 --> 00:06:26,720
如果有兴趣的同学们也可以去查一下

242
00:06:26,720 --> 00:06:29,280
什麼事心跳好這裡就是我們這集的內容

