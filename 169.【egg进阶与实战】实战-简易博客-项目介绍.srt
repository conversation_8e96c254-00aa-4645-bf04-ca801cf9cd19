1
00:00:00,000 --> 00:00:03,000
好 那么我们接下来进行咱们同学们最喜欢的一个实战环节

2
00:00:03,000 --> 00:00:04,260
我们来做一个简易的博客

3
00:00:04,260 --> 00:00:05,220
首先来看一下学习目标

4
00:00:05,220 --> 00:00:08,600
使用1GG加上蒙古DB实现一个简易博客的增三改参

5
00:00:08,600 --> 00:00:09,520
那么什么是蒙古DB

6
00:00:09,520 --> 00:00:11,540
它是不是和Redis一样是一个非关系型数据库

7
00:00:11,540 --> 00:00:12,320
那么它们的区别是什么

8
00:00:12,320 --> 00:00:13,100
Redis是内存型

9
00:00:13,100 --> 00:00:14,540
那么蒙古DB是咱们的一个存储型

10
00:00:14,540 --> 00:00:15,440
它是存在硬盘中的

11
00:00:15,440 --> 00:00:16,320
好 框架选型

12
00:00:16,320 --> 00:00:17,460
1GG加上蒙古斯

13
00:00:17,460 --> 00:00:18,240
那么什么是蒙古斯

14
00:00:18,240 --> 00:00:19,940
蒙古斯其实它是对蒙古DB的一个封装实现了

15
00:00:19,940 --> 00:00:20,940
我们对操作的一些简化

16
00:00:20,940 --> 00:00:22,540
那么后面我们会具体的介绍他

17
00:00:22,540 --> 00:00:24,040
好 那么在学习开始之前呢

18
00:00:24,040 --> 00:00:25,340
你需要去做一些准备工作

19
00:00:25,340 --> 00:00:27,240
首先呢你需要去安装一个mongodb的可视化工具

20
00:00:27,240 --> 00:00:28,240
然后后来还有postment

21
00:00:28,240 --> 00:00:29,640
包括了咱们去安装一下mongodb

22
00:00:29,640 --> 00:00:30,640
好 那么在学习开始之前呢

23
00:00:30,640 --> 00:00:31,640
先来给同学去演示一下

24
00:00:31,640 --> 00:00:32,240
我们在哪个项目

25
00:00:32,240 --> 00:00:33,140
他到底是怎么回事

26
00:00:33,140 --> 00:00:36,140
其实非常简单

27
00:00:36,140 --> 00:00:37,740
比如说我们去做一个博客

28
00:00:37,740 --> 00:00:39,040
是不是需要去设计一些接口

29
00:00:39,040 --> 00:00:39,940
那么我们的文章

30
00:00:39,940 --> 00:00:40,740
比如说我们的博客

31
00:00:40,740 --> 00:00:41,940
最核心的是什么是不是文章啊

32
00:00:41,940 --> 00:00:43,300
所以呢我们去设计了这两个

33
00:00:43,300 --> 00:00:44,640
接口叫做api posts

34
00:00:44,640 --> 00:00:46,700
那么它是符合了resifu咱们的一个规范

35
00:00:46,700 --> 00:00:48,180
那么resifu是不是前面也介绍过

36
00:00:48,180 --> 00:00:50,380
它是对我们接生数据开发的一种规范

37
00:00:50,380 --> 00:00:51,660
大家可以去搜一下

38
00:00:51,660 --> 00:00:52,500
如果说有不了解的同学

39
00:00:52,500 --> 00:00:55,000
那么我们首先第一个参数

40
00:00:55,000 --> 00:00:58,160
第一个API是不是代表我们是去返回的是一个接生

41
00:00:58,160 --> 00:01:00,420
那么post代表我们的类型

42
00:01:00,420 --> 00:01:02,800
说明咱们的这个接口是去对文章做一些操作

43
00:01:02,800 --> 00:01:04,100
因为我们的博客核心就是文章

44
00:01:04,100 --> 00:01:05,800
所以说我们的接口就去围绕

45
00:01:05,800 --> 00:01:08,320
API post这样的一个API咱们去做一些文章

46
00:01:08,320 --> 00:01:09,260
首先咱们来看一下

47
00:01:09,260 --> 00:01:10,720
请求这里

48
00:01:10,720 --> 00:01:12,200
比如说我们去访问API post

49
00:01:12,200 --> 00:01:13,240
现在是什么请求

50
00:01:13,240 --> 00:01:16,700
那么这里我们是不是可以读取我们所有文章列表

51
00:01:16,700 --> 00:01:18,140
那么我们文章奉有哪些字段呢

52
00:01:18,140 --> 00:01:19,500
其实很简单

53
00:01:19,500 --> 00:01:20,300
只有三个字段

54
00:01:20,300 --> 00:01:21,920
哪三个呢

55
00:01:21,920 --> 00:01:22,380
一个是ID

56
00:01:22,380 --> 00:01:24,500
ID就是我们文章的一个序号

57
00:01:24,500 --> 00:01:25,020
Index

58
00:01:25,020 --> 00:01:26,940
那么title是代表咱们的文章的标题

59
00:01:26,940 --> 00:01:28,580
content就是我们的一个文章内容

60
00:01:28,580 --> 00:01:29,560
那么这里呢

61
00:01:29,560 --> 00:01:31,740
我们只去设计了这样几个简单的字段

62
00:01:31,740 --> 00:01:32,880
因为我们的重点是什么呀

63
00:01:32,880 --> 00:01:33,740
是实现增山改查

64
00:01:33,740 --> 00:01:34,140
所以说呢

65
00:01:34,140 --> 00:01:35,360
字段不再多而待于精

66
00:01:35,360 --> 00:01:36,820
那么这里呢

67
00:01:36,820 --> 00:01:39,400
是获取文章列表的一个方法

68
00:01:39,400 --> 00:01:41,140
我们是不是实现增山改查

69
00:01:41,140 --> 00:01:41,900
这里实现的是什么

70
00:01:41,900 --> 00:01:42,480
是不是查呀

71
00:01:42,480 --> 00:01:43,500
其实我们还可以去改它

72
00:01:43,500 --> 00:01:44,460
那么怎么样去改呢

73
00:01:44,460 --> 00:01:46,560
比如说我们想去修改

74
00:01:46,560 --> 00:01:47,940
帅起来我自己都怕

75
00:01:47,940 --> 00:01:48,340
对吧

76
00:01:48,340 --> 00:01:49,240
这条数据

77
00:01:49,240 --> 00:01:50,480
我们是不是把它ID拿到

78
00:01:50,480 --> 00:01:51,460
然后去进行什么呀

79
00:01:51,460 --> 00:01:52,580
如果说修改它

80
00:01:52,580 --> 00:01:53,220
是不是put操作

81
00:01:53,220 --> 00:01:53,560
好

82
00:01:53,560 --> 00:01:54,780
那么我们就来进行一下put操作

83
00:01:54,780 --> 00:01:57,100
那么这里呢

84
00:01:57,100 --> 00:01:58,100
我们怎么样去模拟操作呢

85
00:01:58,100 --> 00:01:59,660
其实是使用我们的一个poster们

86
00:01:59,660 --> 00:02:00,140
比如说呢

87
00:02:00,140 --> 00:02:01,220
我们把它ID给复制过来

88
00:02:01,220 --> 00:02:02,240
然后去执行一个什么操作

89
00:02:02,240 --> 00:02:02,760
是不是put

90
00:02:02,760 --> 00:02:03,480
好

91
00:02:03,480 --> 00:02:05,400
然后我们需要改它的什么字段

92
00:02:05,400 --> 00:02:08,420
比如说我们要改它的content

93
00:02:08,420 --> 00:02:08,820
对吧

94
00:02:08,820 --> 00:02:09,820
好

95
00:02:09,820 --> 00:02:11,400
我们把它content给改掉

96
00:02:11,400 --> 00:02:13,400
改成什么呢

97
00:02:13,400 --> 00:02:15,400
帅起来

98
00:02:15,400 --> 00:02:17,400
也怕好

99
00:02:17,400 --> 00:02:19,400
那么我们来操作一下剩的

100
00:02:19,400 --> 00:02:23,400
problem passing json

101
00:02:23,400 --> 00:02:25,400
这里呢可能是我们的json发生错误

102
00:02:25,400 --> 00:02:27,400
我来重新发送一下好大家可以看到

103
00:02:27,400 --> 00:02:29,400
我们是不是返回咱们的更新成功

104
00:02:29,400 --> 00:02:31,400
那我们来看一下到底是怎么回事

105
00:02:31,400 --> 00:02:33,400
刷新走里大家可以看到我们的content

106
00:02:33,400 --> 00:02:35,400
是不是也改成了咱们的帅起来你也怕

107
00:02:35,400 --> 00:02:37,400
对吧好那么其实我们这个项目

108
00:02:37,400 --> 00:02:39,400
它的作用就是我们去实现我们的一个

109
00:02:39,400 --> 00:02:41,400
对咱们API post这样的一个接口

110
00:02:41,400 --> 00:02:42,700
我们刚才做的是一个什么操作

111
00:02:42,700 --> 00:02:43,400
是不是更新的操作

112
00:02:43,400 --> 00:02:45,260
当然了你也可以去做一些删除了

113
00:02:45,260 --> 00:02:46,260
比如说我们来演示一下删除

114
00:02:46,260 --> 00:02:47,000
delete

115
00:02:47,000 --> 00:02:48,100
好咱们直接去圣的

116
00:02:48,100 --> 00:02:49,560
大家可以看到返回的是success

117
00:02:49,560 --> 00:02:50,900
那么我们同样的来刷新一下

118
00:02:50,900 --> 00:02:52,140
可以看到了我们第一条数据

119
00:02:52,140 --> 00:02:53,040
其实已经也没有了

120
00:02:53,040 --> 00:02:54,060
对吧

121
00:02:54,060 --> 00:02:55,000
我们刚才算起来

122
00:02:55,000 --> 00:02:56,460
你也怕这条数据是不是已经消失了

123
00:02:56,460 --> 00:02:57,960
所以说我们的核心就是利用1GG

124
00:02:57,960 --> 00:02:58,880
加上我们的一个mongodb

125
00:02:58,880 --> 00:03:00,420
去实现这样一套的真伤改查

126
00:03:00,420 --> 00:03:02,300
这里就是我们这一整套课程

127
00:03:02,300 --> 00:03:03,640
它的一个内容

128
00:03:03,640 --> 00:03:05,440
好

129
00:03:05,440 --> 00:03:06,080
那么呢

130
00:03:06,080 --> 00:03:07,640
这里呢可能你需要去做一些准备工作

131
00:03:07,640 --> 00:03:08,520
我们刚才是不是已经讲到了

132
00:03:08,520 --> 00:03:10,220
比如说我们需要去准备一个什么呀

133
00:03:10,220 --> 00:03:11,940
刚才我们是不是已经演示了Postment

134
00:03:11,940 --> 00:03:14,280
那么蒙哥DB呢这里呢你可能需要去自己安装一下

135
00:03:14,280 --> 00:03:16,600
那么怎么安装我这里就不去过多的介绍了

136
00:03:16,600 --> 00:03:17,720
我们来看一下蒙哥DB可造工具

137
00:03:17,720 --> 00:03:20,160
那么可造工具其实之前咱们是不是讲过蒙哥DB

138
00:03:20,160 --> 00:03:21,280
告诉同学们去安装啊

139
00:03:21,280 --> 00:03:23,700
那么这里老师使用的是呢一款叫做

140
00:03:23,700 --> 00:03:28,280
叫做Studio 3T这样的一个软件

141
00:03:28,280 --> 00:03:29,960
Studio 3T

142
00:03:29,960 --> 00:03:31,300
那么同学们可以去自己下载一个

143
00:03:31,300 --> 00:03:32,360
或者说呢使用一个别的也可以

144
00:03:32,360 --> 00:03:37,700
大家可以看到我们刚才存储的数据是不是在咱们的数据库里面呢

145
00:03:37,700 --> 00:03:39,180
这里呢就是咱们一个数据库的可刷工具

146
00:03:39,180 --> 00:03:41,220
我们可以看到在post里面是不是有四条数据

147
00:03:41,220 --> 00:03:41,800
它有哪些字段

148
00:03:41,800 --> 00:03:44,600
这边id title content

149
00:03:44,600 --> 00:03:45,760
包括了一个下方向v

150
00:03:45,760 --> 00:03:47,320
它那是蒙古地币自己生成的一个字段

151
00:03:47,320 --> 00:03:49,240
那么我们再来看一下我们的

152
00:03:49,240 --> 00:03:50,720
我们的界面

153
00:03:50,720 --> 00:03:51,760
我们的浏览器

154
00:03:51,760 --> 00:03:54,500
这里呢是不是也有四条数据

155
00:03:54,500 --> 00:03:55,980
所以呢和咱们的数据库是对应的一个关系

156
00:03:55,980 --> 00:03:56,500
好

157
00:03:56,500 --> 00:03:59,080
那么这里呢希望同学们下去呢自己去准备一下环境

158
00:03:59,080 --> 00:04:00,920
那么下节课呢我们就来先来介绍一下蒙古时

159
00:04:00,920 --> 00:04:02,920
然后咱们来进入咱们真正的一个项目的实战开发

160
00:04:02,920 --> 00:04:04,920
好那这里呢就是我们这几个内容

