1
00:00:00,000 --> 00:00:03,080
好 这节课我们来从零开始完成咱们整个登录流程

2
00:00:03,080 --> 00:00:05,880
首先咱们第一步是不是在安装我们的一个项目环境呢

3
00:00:05,880 --> 00:00:10,240
首先咱们进入我们的一个项目目录 也就是redis-login 大家可以发现它是一个空洞目录

4
00:00:10,240 --> 00:00:11,780
我们进入redis-login

5
00:00:11,780 --> 00:00:21,240
好 我们进入目录之后 那么我们的一GG是如何去进行初始化的呢

6
00:00:21,240 --> 00:00:26,620
只需要一条命令 npm init-type-simple 这样呢我们就可以去初始化我们的一个项目

7
00:00:26,620 --> 00:00:29,700
好 那么让它去安装 我们来介绍一下咱们项目的依赖

8
00:00:30,000 --> 00:00:32,480
首先我们来看一下pocket.json 咱们的项目依赖核心

9
00:00:32,480 --> 00:00:37,880
1GG-mongos 那么mongos是什么呢 它其实是去操作我们的mongodb数据库的一个

10
00:00:37,880 --> 00:00:45,540
库 那么redis呢 同样的也是去操作redis的一个库 那么1GG-session 那么我们也是通过将session存储了redis里面的一个库

11
00:00:45,540 --> 00:00:48,180
那么1GG-viewlandjacks 我们的模板引擎

12
00:00:48,180 --> 00:00:53,880
那么这一块呢 其实有稍微有点依赖于咱们前面一GG所讲解的内容 那么如果说听不懂的同学可能需要去看一下前面的视频

13
00:00:54,360 --> 00:00:59,220
好 那么这里呢 我呢 就不去具体的介绍咱们安装过程了 这里呢 我直接把package.json呢

14
00:00:59,220 --> 00:01:00,940
 直接给粘贴过来 咱们直接去安装依赖就可以了

15
00:01:00,940 --> 00:01:04,020
因为我们呢 需要去主要是讲解核心内容 对吧

16
00:01:04,020 --> 00:01:10,160
好 我们的项目已经出发成功了 这里呢 我进入package.json 咱们直接把依赖给替换

17
00:01:10,160 --> 00:01:15,700
 让他自动去安装就可以了 我们把内容稍微改一下 叫做release-robin

18
00:01:15,700 --> 00:01:22,920
好 我们的通过npm来安装一下 那么在安装的过程中呢 我呢给同学们来

19
00:01:22,920 --> 00:01:23,800
 简单的来

20
00:01:24,360 --> 00:01:26,660
介绍一下啊1GG-Session在那个库

21
00:01:26,660 --> 00:01:32,300
我们其实只需要去了解他那么mongos和redis其实使用呢非常简单同学们直接等一下看我操作就可以了

22
00:01:32,300 --> 00:01:36,640
那么1GG-Session这里可能需要跟同学们去啊稍微讲解一下我们来看一下

23
00:01:36,640 --> 00:01:41,000
1GG-Session

24
00:01:41,000 --> 00:01:48,420
其实这里呢他这个库的作用是什么呢其实就是为了让我们的Session能够存储到redis里面也就是存储到咱们外部的一个数据库里面

25
00:01:49,440 --> 00:01:50,980
好好在那些配置呢

26
00:01:50,980 --> 00:01:53,800
比如说我们要在1GG的插件里面去配置

27
00:01:53,800 --> 00:01:55,080
包括了我们要通过它的

28
00:01:55,080 --> 00:01:57,000
包括我们的一个

29
00:01:57,000 --> 00:01:58,140
config也要去进行配置

30
00:01:58,140 --> 00:01:59,160
那么这里呢其实是不需要的

31
00:01:59,160 --> 00:01:59,940
我们重点看这里

32
00:01:59,940 --> 00:02:01,480
我们如何去使用它

33
00:02:01,480 --> 00:02:02,500
其实也很简单

34
00:02:02,500 --> 00:02:03,260
比如说

35
00:02:03,260 --> 00:02:06,080
好那么我们在APP里面去定一个session store

36
00:02:06,080 --> 00:02:06,600
这样一个属性

37
00:02:06,600 --> 00:02:08,380
其实这里是插件所规定的

38
00:02:08,380 --> 00:02:09,400
我们这么去写就可以了

39
00:02:09,400 --> 00:02:10,680
重点其实是这样三个方法

40
00:02:10,680 --> 00:02:13,000
get set和destroy

41
00:02:13,000 --> 00:02:14,280
那我看起来是不是有点

42
00:02:14,280 --> 00:02:15,300
一二四五里面proxy

43
00:02:15,300 --> 00:02:16,840
也就是咱们数据劫持的一个感觉

44
00:02:16,840 --> 00:02:18,120
那么其实它的原理就是这样的

45
00:02:18,120 --> 00:02:19,400
比如说我们get的方法

46
00:02:19,400 --> 00:02:20,520
我们怎么理解呢

47
00:02:20,520 --> 00:02:21,160
简单点来讲

48
00:02:21,160 --> 00:02:22,760
我们只要去调用了

49
00:02:22,760 --> 00:02:24,500
也就是说我们只要去读取了我们的Session

50
00:02:24,500 --> 00:02:25,940
也就是调用咱们的get方法

51
00:02:25,940 --> 00:02:27,200
它就会执行里面的一个逻辑

52
00:02:27,200 --> 00:02:28,680
比如说我们去获取Session

53
00:02:28,680 --> 00:02:29,400
Session在哪里呢

54
00:02:29,400 --> 00:02:30,380
咱们通过之前的演示

55
00:02:30,380 --> 00:02:31,160
可以发现我们的Session

56
00:02:31,160 --> 00:02:32,940
是不是存储在外部的数据库里面

57
00:02:32,940 --> 00:02:34,100
也就是咱们的Redis数据库里面

58
00:02:34,100 --> 00:02:35,520
那么如果说你调用get方法

59
00:02:35,520 --> 00:02:36,380
也就是getSession的时候

60
00:02:36,380 --> 00:02:38,140
其实它的内部就会去调用

61
00:02:38,140 --> 00:02:39,560
app.redis.get

62
00:02:39,560 --> 00:02:41,000
那么这里其实就是node.js

63
00:02:41,000 --> 00:02:41,980
去操作redis的api

64
00:02:41,980 --> 00:02:42,960
咱们前面课程是不是也讲过

65
00:02:42,960 --> 00:02:44,920
这里我呢就就不去做过多的一个叙述了

66
00:02:44,920 --> 00:02:47,360
那么radis这样一个实例其实挂在app这样方法里

67
00:02:47,360 --> 00:02:49,360
我们调用get之后实际上会去调用

68
00:02:49,360 --> 00:02:52,860
调用radis的getk也就是去获取radis这样一个k里面的一个数据

69
00:02:52,860 --> 00:02:54,660
比如说我举个例子

70
00:02:54,660 --> 00:02:58,560
咱们呢radis里面是不是有这样几个k

71
00:02:58,560 --> 00:03:01,060
这样几个k呢也就是这样几个token都代表咱们一个用户信息

72
00:03:01,060 --> 00:03:03,560
那么里面会有一些东西比如说login这个对象对吧

73
00:03:03,560 --> 00:03:03,960
好

74
00:03:03,960 --> 00:03:05,860
那么如果说

75
00:03:05,860 --> 00:03:08,660
如果说我们去getc信的时候传入了这样一个k

76
00:03:08,660 --> 00:03:09,660
那么k就是我们刚才说

77
00:03:09,660 --> 00:03:12,160
看到一个token我们去获取那个token的时候

78
00:03:12,160 --> 00:03:14,460
那么redis就会去查询咱们的一个对象

79
00:03:14,460 --> 00:03:16,240
里面有一些我们的登录信息

80
00:03:16,240 --> 00:03:17,160
包括一些过期时间等等

81
00:03:17,160 --> 00:03:18,360
我们拿到数据之后

82
00:03:18,360 --> 00:03:20,840
就会返回给咱们服务端的一个session

83
00:03:20,840 --> 00:03:22,680
这里就是get的过程

84
00:03:22,680 --> 00:03:24,220
那么set其实是一样的

85
00:03:24,220 --> 00:03:26,580
比如说我们在1GG里面去set的一个session

86
00:03:26,580 --> 00:03:28,060
那么同样会调用

87
00:03:28,060 --> 00:03:29,540
这样一个方法里面的逻辑

88
00:03:29,540 --> 00:03:30,600
比如说我们去set的时候

89
00:03:30,600 --> 00:03:31,740
会给一些过期时间

90
00:03:31,740 --> 00:03:34,080
然后在redis里面也会去存储

91
00:03:34,080 --> 00:03:35,160
我们刚才说set的值

92
00:03:35,160 --> 00:03:36,460
包括我们销毁的时候

93
00:03:36,460 --> 00:03:38,300
同样也会在redis里面去进行销毁

94
00:03:38,300 --> 00:03:40,000
说白了就是咱们服务器的session

95
00:03:40,000 --> 00:03:41,560
会和咱们的数据库进行一个同步

96
00:03:41,560 --> 00:03:43,840
那么APP那么一GG-Session这样一个库

97
00:03:43,840 --> 00:03:44,560
它的一个核心作用

98
00:03:44,560 --> 00:03:47,000
其实就是对咱们我们的一个服务器的一个Session

99
00:03:47,000 --> 00:03:48,860
和咱们的数据库进行一个同步

100
00:03:48,860 --> 00:03:51,200
好 那么呢 我们来继续咱们

101
00:03:51,200 --> 00:03:53,260
咱们的编码

102
00:03:53,260 --> 00:03:55,700
好 那么我们安装完delight呢

103
00:03:55,700 --> 00:03:57,800
可能呢 需要对咱们刚才引入了一些插件呢

104
00:03:57,800 --> 00:03:59,000
需要去进行一些配置

105
00:03:59,000 --> 00:04:01,360
那么我们来配置一下咱们的一个插件

106
00:04:01,360 --> 00:04:04,300
好 这里呢 我直接把配置给粘贴过来

107
00:04:04,300 --> 00:04:07,440
因为配置的这里呢不是我们的一个重点

108
00:04:07,440 --> 00:04:09,600
好 这里呢 我直接把代码给扩过来

109
00:04:09,600 --> 00:04:15,180
好 我们在plugin里面其实对咱们的一个模板包括我们的数据库进行了一些配置

110
00:04:15,180 --> 00:04:18,900
那么我们配置完plugin之后呢 还要进入咱们一个config里面

111
00:04:18,900 --> 00:04:20,460
也要去进行一些插件的配置

112
00:04:20,460 --> 00:04:22,380
好 这里呢 我也直接给括括

113
00:04:22,380 --> 00:04:23,720
解决时间

114
00:04:23,720 --> 00:04:25,720
好 我们来看一下重点配置的哪个内容

115
00:04:25,720 --> 00:04:27,720
其实重点呢 咱们进行了一个模板的配置 对吧

116
00:04:27,720 --> 00:04:31,180
点html文件 我们把它给解析成lunch样式这样一个模板

117
00:04:31,180 --> 00:04:33,120
包括session的一个配置

118
00:04:33,120 --> 00:04:35,020
那么session的配置是什么呢

119
00:04:35,020 --> 00:04:37,480
两个 一个叫做incrept

120
00:04:37,480 --> 00:04:39,460
另一个是zine的 其实它们两个代表什么意思呢

121
00:04:39,460 --> 00:04:44,580
一个是加密 一个是进行一个签名 其实这里我呢都把它给自己搞成fors

122
00:04:44,580 --> 00:04:48,670
因为我们这样一个项目里面不需要 接下来我们来看一下radis的配置

123
00:04:48,670 --> 00:04:49,960
 那么radis的配置呢其实

124
00:04:49,960 --> 00:04:54,060
也非常简单 对吧 我们配置什么呢

125
00:04:54,060 --> 00:04:57,900
给他一个端口 6379 这是我们radis服务的一个默认端口 包括了一个密码

126
00:04:57,900 --> 00:04:58,920
 包括我们选择哪一个库

127
00:04:58,920 --> 00:05:02,760
mongos也是一样的 我们的一个连接地址是什么 那么连接的一个数据库的名称是什么

128
00:05:02,760 --> 00:05:04,800
 对吧 包括一些选项 这里呢移密都没有

129
00:05:05,320 --> 00:05:10,230
好 那么其实这里就完成了咱们项目的一个配置 接下来我们就开始去编写我们的页面

130
00:05:10,230 --> 00:05:12,040
 好 那么在编写之前咱们先把项目给乱起来

131
00:05:12,040 --> 00:05:21,600
好 到这里大家看到嗨11G说明了我们项目其实已经成功了跑起来了 那么首先我们首先来编写一个咱们的首页

132
00:05:21,600 --> 00:05:26,200
就比如说咱们的一个类似咱们的一个淘宝网的首页 对吧 你在登录之后才能够去查询商品

133
00:05:26,200 --> 00:05:28,300
 所以我们来编写一个需要登录的首页

134
00:05:28,300 --> 00:05:33,710
那么首页这里呢 我们呢 可能呢 首先呢 需要去定一个什么呀 是不是需要去定一个模板

135
00:05:33,710 --> 00:05:34,880
 对吧 好 我们呢 建立一个

136
00:05:35,320 --> 00:05:36,320
view在那个文件夹

137
00:05:36,320 --> 00:05:38,720
那么在view里面

138
00:05:38,720 --> 00:05:39,920
咱们需要去创建一个

139
00:05:39,920 --> 00:05:42,720
比如说我这里叫做home.html

140
00:05:42,720 --> 00:05:44,920
咱们呢

141
00:05:44,920 --> 00:05:46,020
手页这里的代码呢

142
00:05:46,020 --> 00:05:46,820
我直接给括过来

143
00:05:46,820 --> 00:05:50,920
也就是和咱们刚才所演示的一个例子的一个demo是一样的

144
00:05:50,920 --> 00:05:51,520
好

145
00:05:51,520 --> 00:05:51,920
节时间

146
00:05:51,920 --> 00:05:53,520
所以说我们直接把页面给copy过来

147
00:05:53,520 --> 00:05:56,720
那么我们在control里面是不是要去渲染我们刚才在那个页面呢

148
00:05:56,720 --> 00:05:57,420
对吧

149
00:05:57,420 --> 00:05:57,620
好

150
00:05:57,620 --> 00:05:59,220
那么我们就来编写一下我们的控制器

151
00:05:59,220 --> 00:06:01,320
控制器呢也非常简单

152
00:06:01,320 --> 00:06:01,820
好

153
00:06:01,820 --> 00:06:02,120
这里呢

154
00:06:02,120 --> 00:06:03,320
咱们先把其他的逻辑给

155
00:06:03,320 --> 00:06:04,120
酸掉

156
00:06:04,420 --> 00:06:05,860
因为这里呢涉及到我们后面的逻辑

157
00:06:05,860 --> 00:06:08,720
好那么此时其实我们的控制器做的事情非常简单

158
00:06:08,720 --> 00:06:09,920
他只做一件事情

159
00:06:09,920 --> 00:06:11,920
我们的路由访问到咱们的页面的时候

160
00:06:11,920 --> 00:06:14,720
我们的控制器是不是需要去认得我们home.htmi就是它对吧

161
00:06:14,720 --> 00:06:15,920
好那么此时呢我们来看一下效果

162
00:06:15,920 --> 00:06:19,720
好我是一个需要登录的页面

163
00:06:19,720 --> 00:06:20,820
好我们来点一下这个页面

164
00:06:20,820 --> 00:06:21,920
大家看到录的放的为什么呀

165
00:06:21,920 --> 00:06:23,720
因为我们还没有编写上一个路由对吧

166
00:06:23,720 --> 00:06:25,020
好没关系我们继续

167
00:06:25,020 --> 00:06:27,720
这里呢已经完成了我们

168
00:06:27,720 --> 00:06:28,620
首页的一个编写

169
00:06:28,620 --> 00:06:30,820
那么接下来呢我们呢就把所有的页面先给编写完成

170
00:06:30,820 --> 00:06:32,220
那么还是哪些页面没有编写呢

171
00:06:32,420 --> 00:06:35,760
我们的首页有了 是不是需要一个登录页面 需要一个注册页面呢 对吧

172
00:06:35,760 --> 00:06:38,380
 我们还需要两个页面 所以我们在模板里面呢 把它给定义完

173
00:06:38,380 --> 00:06:41,900
比如说我们去创建一个login.html

174
00:06:41,900 --> 00:06:48,040
接下来再来创建一个sign.html 好 那么login页面的一个

175
00:06:48,040 --> 00:06:52,380
html呢 我呢 也直接给扣过来 因为写它没意义 你学不到什么东西 对吧

176
00:06:52,380 --> 00:06:54,940
再呢 我们也是一样 咱们直接把它给扣过来

177
00:06:54,940 --> 00:06:57,120
我们来看一下

178
00:06:57,120 --> 00:06:59,300
咱们在一个页面里面需要注意的内容

179
00:06:59,820 --> 00:07:03,980
我们重点看咱们的一个form 表现 action action 是不是咱们一个提交的地址

180
00:07:03,980 --> 00:07:05,880
我们的API设计为什么呢

181
00:07:05,880 --> 00:07:09,080
API login 这里代表我们登录了一个接口

182
00:07:09,080 --> 00:07:10,980
登录接口地址

183
00:07:10,980 --> 00:07:15,280
这里注释呢 我们把它写到上面

184
00:07:15,280 --> 00:07:17,980
这里呢 它代表我们的登录接口地址

185
00:07:17,980 --> 00:07:19,760
API login method 方法是post

186
00:07:19,760 --> 00:07:22,220
当我们点击登录的时候呢 就可以去调用咱们的一个API login

187
00:07:22,220 --> 00:07:24,620
包括了把我们的username和password给传递到辅端

188
00:07:24,620 --> 00:07:25,780
那么注册其实是一样的

189
00:07:25,780 --> 00:07:28,420
我们去访问API join 方法也是post

190
00:07:28,420 --> 00:07:30,420
然后自乱呢是username和posword

191
00:07:30,420 --> 00:07:33,660
好那么呢我们的页面别写完成之后咱们是不是要给他添加路由啊

192
00:07:33,660 --> 00:07:37,060
好那么我们就把就给这两个两个页面的去添加一下他们的路由

193
00:07:37,060 --> 00:07:39,680
好那么他们的路由呢我呢

194
00:07:39,680 --> 00:07:45,180
添加一下router.get

195
00:07:45,180 --> 00:07:46,180
为什么是get

196
00:07:46,180 --> 00:07:47,780
因为我们其实此时访问是html

197
00:07:47,780 --> 00:07:48,820
我们还没有进行登录对吧

198
00:07:48,820 --> 00:07:51,100
这当我们真正登录的时候才是访问是啊

199
00:07:51,100 --> 00:07:52,980
才是访问这post对吧

200
00:07:52,980 --> 00:07:54,160
好我们呢首先get

201
00:07:54,160 --> 00:07:54,960
咱们登录的是login

202
00:07:54,960 --> 00:07:56,660
我们的

203
00:07:57,720 --> 00:08:04,790
sign 我们还是get 咱们的sign 是我们的一个注册 好 那么呢 是不是他们也需要对应自己的一个控制器啊

204
00:08:04,790 --> 00:08:04,980
 对吧

205
00:08:04,980 --> 00:08:11,240
好 所以这里呢 我来新建一个文件 叫做login.js 那么login.js的就代表我们整个的一个登录逻辑

206
00:08:11,240 --> 00:08:15,960
好 我们来把home给扩拐 咱们来改一下 比如说我们叫做login

207
00:08:15,960 --> 00:08:20,280
controller 好 我们此时是不是需要去定义两个方法 哪两个方法

208
00:08:20,280 --> 00:08:22,200
是不是需要去

209
00:08:23,520 --> 00:08:27,840
定义访问咱们login和战也就是咱们登录注册两个页面的一个两个方法

210
00:08:27,840 --> 00:08:31,840
 对吧 他们的分别对应一个渲染页面的方法 好 我们来看一下 如何去编写

211
00:08:31,840 --> 00:08:32,980
好

212
00:08:32,980 --> 00:08:38,080
这里呢 比如说我取一个名称叫做

213
00:08:38,080 --> 00:08:44,150
login 还取天马耀 这里呢 代表访问我们的登录页面 再来定一个方法 a

214
00:08:44,150 --> 00:08:44,640
 sync

215
00:08:44,640 --> 00:08:48,320
s i7 来取天马耀 这里呢 也代表访问我们的一个

216
00:08:48,320 --> 00:08:53,500
注册页面 那么其实我们直接去调用什么呢 我们是不是直接去调用伦的方法就可以

217
00:08:53,520 --> 00:08:55,760
这里其实是Longjax这样一个插件提供给我们的方法

218
00:08:55,760 --> 00:09:00,920
我们的login html去render我们的login就可以了

219
00:09:00,920 --> 00:09:04,020
那么我们的Zine去访问我们的一个Zine

220
00:09:04,020 --> 00:09:06,020
此时就已经完成了我们登录注册的逻辑

221
00:09:06,020 --> 00:09:07,020
我们只需要在root里面

222
00:09:07,020 --> 00:09:12,420
我们只需要在root里面去把咱们的控制器给添上

223
00:09:12,420 --> 00:09:18,720
比如说我们这里去定义controller.login.login html

224
00:09:18,720 --> 00:09:19,920
在Zine里面去定义

225
00:09:23,520 --> 00:09:27,560
那么我们来看一下效果

226
00:09:27,560 --> 00:09:30,860
我们来访问一下

227
00:09:30,860 --> 00:09:33,100
什么原因

228
00:09:33,100 --> 00:09:36,160
好 我们来重启一下我们的一个服务

229
00:09:36,160 --> 00:09:46,000
好 这里可以发现我们的页面其实已经生效

230
00:09:46,000 --> 00:09:48,760
sg 注册 对吧

231
00:09:48,760 --> 00:09:50,840
好 那我们所有的页面其实已经完成了

232
00:09:50,840 --> 00:09:51,900
那么接下来我们要完成什么内容

233
00:09:51,900 --> 00:09:54,800
是不是咱们要去编写登录和注册的一个接口

234
00:09:54,800 --> 00:09:55,300
好

235
00:09:55,300 --> 00:09:57,380
那么下一节可能我们就来编写登录和注册的接口

