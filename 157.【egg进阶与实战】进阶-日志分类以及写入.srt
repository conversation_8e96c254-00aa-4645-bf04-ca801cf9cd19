1
00:00:00,000 --> 00:00:03,560
info 我们有info 有问 有error 但是为什么没有debug

2
00:00:03,560 --> 00:00:05,160
为什么没有debug 同学们

3
00:00:05,160 --> 00:00:09,440
其实原因 等一下我会去讲 为什么没有debug

4
00:00:09,440 --> 00:00:15,320
好 那么其实这里呢 还需要注意一点什么呢 我们刚才是不是讲过 我们刚才是不是讲过

5
00:00:15,320 --> 00:00:16,400
 我们的日子分类是不是有一个

6
00:00:16,400 --> 00:00:21,960
是不是有个errorlog 那么errorlog是什么意思 是不是我们所有的错误都会收集到我们的errorlog里面

7
00:00:21,960 --> 00:00:27,200
那么刚才我们在一个error 他在哪里 他在哪里 是不是在我们的egg weblog里面

8
00:00:27,200 --> 00:00:28,320
 他是不是收集我们的一些

9
00:00:28,680 --> 00:00:33,600
应用的日子说明什么问题 我们的应用日子里面会收集我们的info 握你和aero

10
00:00:33,600 --> 00:00:38,320
那么aero信息呢 他是不是会在copy一条 会复制一条到我们哪里去 到我们common

11
00:00:38,320 --> 00:00:39,920
 arrow 他呢还会有一条大家可以看到

12
00:00:39,920 --> 00:00:44,040
是不是contact中的应该这里其实是aero 我刚才写错了 那么其实这里大家可以看到

13
00:00:44,040 --> 00:00:46,920
 是不是我们的错误信息 你不仅在咱们的日子文件里面有

14
00:00:46,920 --> 00:00:50,840
包括他还会去新建一个aero.log 把它给复制过去 为什么呀 其实这样呢

15
00:00:50,840 --> 00:00:51,720
 可以方便我们去查错

16
00:00:51,720 --> 00:00:53,720
那我刚才我们通过

17
00:00:53,720 --> 00:00:55,240
研究是不是发现

18
00:00:55,900 --> 00:00:57,180
我们哪一条日子没有出现呢

19
00:00:57,180 --> 00:00:59,220
是不是debug 它终究没出现 为什么呀

20
00:00:59,220 --> 00:01:01,280
其实这里有一个原因是什么呢

21
00:01:01,280 --> 00:01:04,600
因为我们的文件日子 我们刚才讲到了 它是不是分为四个级别

22
00:01:04,600 --> 00:01:08,700
那么其实它默认只会输出info及以下的 及以上的日子到文件中 为什么呀

23
00:01:08,700 --> 00:01:13,300
因为我们刚才是不是讲到了 要代表错误 握影代表警告 那么info代表一些重要信息

24
00:01:13,300 --> 00:01:15,360
 那么debug 我们是不是会随意打赢一些信息

25
00:01:15,360 --> 00:01:18,180
但是你如果说把所有的信息都给存起来 这样是不是会非常

26
00:01:18,180 --> 00:01:23,390
占用我们的一个内存 所以说默认只会输出info及以上的 咱们的级别日子到文件中

27
00:01:23,390 --> 00:01:24,580
 那么如果说你需要去打赢debug

28
00:01:25,900 --> 00:01:27,700
然后我们去配置一下把他的内部改为一半

29
00:01:27,700 --> 00:01:31,020
那么如果说你需要关闭日子你能把它改为浪就可以了

30
00:01:31,020 --> 00:01:34,600
好这里呢是我们级别的一个介绍我们的先来总结一下这几个内容

31
00:01:34,600 --> 00:01:37,940
我们这节课是不是主要讲了咱们的一个日子啊

32
00:01:37,940 --> 00:01:40,500
那么日子他是不是会有个存储路径

33
00:01:40,500 --> 00:01:41,780
那么存储路径

34
00:01:41,780 --> 00:01:46,640
咱们的开发环境是不是默认的对吧那么生产环境呢需要你指定吧

35
00:01:46,640 --> 00:01:53,540
为什么要指定说你需要去把它存到别的地方对吧

36
00:01:53,800 --> 00:01:55,820
那么呢我们的日词是不是又可以去分类呀

37
00:01:55,820 --> 00:01:57,940
那么日词分类有几种呢

38
00:01:57,940 --> 00:01:59,960
我们重要的是不是就是一个error

39
00:01:59,960 --> 00:02:01,160
警告

40
00:02:01,160 --> 00:02:02,060
info

41
00:02:02,060 --> 00:02:02,780
debug

42
00:02:02,780 --> 00:02:03,080
对吧

43
00:02:03,080 --> 00:02:03,740
error是错误

44
00:02:03,740 --> 00:02:05,300
walling呢是什么一些警告

45
00:02:05,300 --> 00:02:05,920
比如说我们的一个

46
00:02:05,920 --> 00:02:07,280
我们去写一个组件的时候没有加k

47
00:02:07,280 --> 00:02:07,560
对吧

48
00:02:07,560 --> 00:02:08,680
info呢是一些重要的信息

49
00:02:08,680 --> 00:02:10,040
那么debug呢是一些你啊

50
00:02:10,040 --> 00:02:11,780
想调色的时候一些比较随意的信息

51
00:02:11,780 --> 00:02:13,280
那么还一个浪呢就是没有

52
00:02:13,280 --> 00:02:14,640
就是没什么意思

53
00:02:14,640 --> 00:02:15,060
好

54
00:02:15,060 --> 00:02:17,240
那么我们讲到了日词分类以及级别

55
00:02:17,240 --> 00:02:18,100
后面是不是

56
00:02:18,100 --> 00:02:20,800
啊又讲到了我们怎么样去输出日资啊

57
00:02:20,800 --> 00:02:22,020
是不是在咱们的contest这边

58
00:02:22,020 --> 00:02:23,080
load下面呢

59
00:02:23,080 --> 00:02:23,360
对吧

60
00:02:23,360 --> 00:02:25,780
那么同样的你不仅contest下面有log

61
00:02:25,780 --> 00:02:28,340
你app下面其实也有log这样的方法

62
00:02:28,340 --> 00:02:30,180
可以让我们去存储日志

63
00:02:30,180 --> 00:02:31,900
好 这里呢就是我们这节奏的内容

