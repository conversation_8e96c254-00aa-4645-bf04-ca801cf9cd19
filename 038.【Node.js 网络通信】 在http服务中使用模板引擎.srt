1
00:00:00,000 --> 00:00:02,260
那么我们在这里的话

2
00:00:02,260 --> 00:00:03,180
我们所演示的

3
00:00:03,180 --> 00:00:05,460
是只是单纯的对这个字符串

4
00:00:05,460 --> 00:00:07,300
使用木板引擎来进行解析替换

5
00:00:07,300 --> 00:00:08,600
然后接下来我们就把

6
00:00:08,600 --> 00:00:09,700
我们解析到的这个结果

7
00:00:09,700 --> 00:00:12,100
看一下如何和我们的web服务

8
00:00:12,100 --> 00:00:13,560
也就是说这个HTTP服务

9
00:00:13,560 --> 00:00:14,400
是结合到一起

10
00:00:14,400 --> 00:00:16,120
就是把我们渲染的这个结果

11
00:00:16,120 --> 00:00:18,080
是不是给它发送到我们的音名当中啊

12
00:00:18,080 --> 00:00:19,840
那么首先我们在这里

13
00:00:19,840 --> 00:00:21,140
要把这一段注意

14
00:00:21,140 --> 00:00:22,640
把这一段ATML内容注意

15
00:00:22,640 --> 00:00:24,740
我们不让它写到我们的js文件当中了

16
00:00:24,740 --> 00:00:25,480
我们在这里

17
00:00:25,480 --> 00:00:26,720
我们把它单独的提取到了

18
00:00:26,720 --> 00:00:28,900
一个叫做index2.ATML音名当中

19
00:00:28,900 --> 00:00:29,820
我们把它放到这里

20
00:00:29,820 --> 00:00:31,260
那么放过来以后

21
00:00:31,260 --> 00:00:33,580
然后接下来我们又创建了一个06

22
00:00:33,580 --> 00:00:35,480
叫做http-template.js

23
00:00:35,480 --> 00:00:37,500
然后接下来我们在这个大版当中

24
00:00:37,500 --> 00:00:40,440
我们让他当他请求我们的首页跟路径的时候

25
00:00:40,440 --> 00:00:42,840
我们让他渲染index2.admr这个文件

26
00:00:42,840 --> 00:00:44,460
当然此时我们要

27
00:00:44,460 --> 00:00:45,300
是要把里面的数据

28
00:00:45,300 --> 00:00:46,700
是给它变成一个动态的数据

29
00:00:46,700 --> 00:00:48,820
所以说这个时候我们在这就可以这样来做

30
00:00:48,820 --> 00:00:51,560
首先我们要拿到template

31
00:00:51,560 --> 00:00:54,440
就是我们这个叫atemplate这个木板引擎

32
00:00:54,440 --> 00:00:55,480
拿到以后

33
00:00:55,480 --> 00:00:56,840
然后接下来我们可以在这里这样

34
00:00:56,840 --> 00:00:59,420
我们可以单独的拿到这个url

35
00:00:59,420 --> 00:01:00,540
另外我们让他

36
00:01:00,540 --> 00:01:02,260
当他请求的是干的时候

37
00:01:02,260 --> 00:01:04,360
那么这个时候我们可以在这里通过

38
00:01:04,360 --> 00:01:05,580
FS

39
00:01:05,580 --> 00:01:07,740
我们把这个文件给他读出来

40
00:01:07,740 --> 00:01:08,600
read file

41
00:01:08,600 --> 00:01:09,820
好 我们在这里读取

42
00:01:09,820 --> 00:01:11,960
我们这个叫做index2.atmar文件

43
00:01:11,960 --> 00:01:14,020
好 然后是arrow和data

44
00:01:14,020 --> 00:01:16,100
好 如果arrow

45
00:01:16,100 --> 00:01:18,140
那么就表示我们这个文件在这里读取失败了

46
00:01:18,140 --> 00:01:19,680
我们在这里呢先简单一点

47
00:01:19,680 --> 00:01:22,680
好 我们先给他读入arrow

48
00:01:22,680 --> 00:01:23,760
好了 完了之后

49
00:01:23,760 --> 00:01:25,340
然后接下来我们是不是要去注意

50
00:01:25,340 --> 00:01:26,840
是不是要模板引擎解析替了呀

51
00:01:26,840 --> 00:01:27,860
也就是这个时候注意

52
00:01:27,860 --> 00:01:28,320
我们

53
00:01:28,320 --> 00:01:31,120
把质量代码给他拿过来

54
00:01:31,120 --> 00:01:31,860
也就是说

55
00:01:31,860 --> 00:01:32,780
此时我们在这里

56
00:01:32,780 --> 00:01:33,720
Response the end的时候

57
00:01:33,720 --> 00:01:34,620
就应该end这个data

58
00:01:34,620 --> 00:01:36,020
但是你这个data是什么呀

59
00:01:36,020 --> 00:01:37,640
就是未经处理的data呀

60
00:01:37,640 --> 00:01:38,100
所以说我们接下来

61
00:01:38,100 --> 00:01:39,580
我们要发送这个data之前

62
00:01:39,580 --> 00:01:41,340
就是把这个文件中的内容

63
00:01:41,340 --> 00:01:42,140
也就是这个字符串

64
00:01:42,140 --> 00:01:43,260
是给它进行一个解析替换

65
00:01:43,260 --> 00:01:44,600
然后就是再来发给这段期

66
00:01:44,600 --> 00:01:45,640
所以说现在的话

67
00:01:45,640 --> 00:01:46,260
我们在这里呢

68
00:01:46,260 --> 00:01:47,180
就应该这样来做

69
00:01:47,180 --> 00:01:48,260
我们接下来在这就

70
00:01:48,260 --> 00:01:48,980
TimePay的DN

71
00:01:48,980 --> 00:01:49,920
Render一下

72
00:01:49,920 --> 00:01:51,260
好那么data就是什么呢

73
00:01:51,260 --> 00:01:52,380
是不是就是我们的数据源

74
00:01:52,380 --> 00:01:53,460
但是这里一定要注意

75
00:01:53,460 --> 00:01:54,940
data这个Render方法呢

76
00:01:54,940 --> 00:01:55,560
接受的第一个参数

77
00:01:55,560 --> 00:01:56,480
必须是一个字符串

78
00:01:56,480 --> 00:01:57,400
而你这个data呢

79
00:01:57,400 --> 00:01:58,220
实际上默认情况下

80
00:01:58,220 --> 00:01:58,860
它是一个buffer

81
00:01:58,860 --> 00:01:59,860
是一个二精致数据

82
00:01:59,860 --> 00:02:00,500
所以说我们在这里

83
00:02:00,500 --> 00:02:02,220
我们要让它to3一下

84
00:02:02,220 --> 00:02:03,260
好了转完以后

85
00:02:03,260 --> 00:02:04,480
然后接下来我们给它一个数据源

86
00:02:04,480 --> 00:02:05,320
那么这个数据源

87
00:02:05,320 --> 00:02:07,460
我们可以把我们刚才的这个数据

88
00:02:07,460 --> 00:02:08,580
我们给它拿过来

89
00:02:08,580 --> 00:02:10,200
好也就是这份数据

90
00:02:10,200 --> 00:02:11,500
只不过我们现在是把它的

91
00:02:11,500 --> 00:02:12,340
模板的字符串

92
00:02:12,340 --> 00:02:13,600
是给它单独的放到了

93
00:02:13,600 --> 00:02:15,040
一个atmr页面当中了

94
00:02:15,040 --> 00:02:15,600
文件当中了

95
00:02:15,600 --> 00:02:15,960
对吧

96
00:02:15,960 --> 00:02:16,560
好了

97
00:02:16,560 --> 00:02:17,700
然后这样拿过来以后

98
00:02:17,700 --> 00:02:18,860
此时我们在这里来

99
00:02:18,860 --> 00:02:20,160
接收一下这个结果

100
00:02:20,160 --> 00:02:22,060
好 这就是我们的ATMRSTR

101
00:02:22,060 --> 00:02:23,140
好 接受过来以后

102
00:02:23,140 --> 00:02:24,640
最后我们在这里Response的ind

103
00:02:24,640 --> 00:02:25,180
就是什么呢

104
00:02:25,180 --> 00:02:26,580
我们ATMRSTR就是它

105
00:02:26,580 --> 00:02:29,160
当然不要忘了把我们这个TextBoundPrint

106
00:02:29,160 --> 00:02:30,800
换成我们的TextBoundATMR

107
00:02:30,800 --> 00:02:31,940
好 那这样的话

108
00:02:31,940 --> 00:02:33,400
我们在这就是就把这段信息

109
00:02:33,400 --> 00:02:34,460
在这是给它处理好了呀

110
00:02:34,460 --> 00:02:35,620
好 总体流程就是

111
00:02:35,620 --> 00:02:36,820
当请求进来的时候

112
00:02:36,820 --> 00:02:38,540
我们在这里通过读文件的方式

113
00:02:38,540 --> 00:02:40,340
是读取到这个文件中的

114
00:02:40,340 --> 00:02:41,600
所以我们现在把它称之为

115
00:02:41,600 --> 00:02:42,680
模板字符串

116
00:02:42,680 --> 00:02:45,660
也就是未经处理的字符串

117
00:02:45,660 --> 00:02:46,540
好了 读到它以后

118
00:02:46,540 --> 00:02:47,580
然后接下来我们调用

119
00:02:47,580 --> 00:02:49,280
Template的这个模板的render方法

120
00:02:49,280 --> 00:02:50,760
然后把什么呢

121
00:02:50,760 --> 00:02:52,520
是把我们读取到的这个字符串

122
00:02:52,520 --> 00:02:53,880
然后利用这个模板引擎

123
00:02:53,880 --> 00:02:55,520
是进行了一个解析替换呀

124
00:02:55,520 --> 00:02:57,000
那么模板引擎就会打什么呢

125
00:02:57,000 --> 00:02:58,280
是不是就把你这个数据中的

126
00:02:58,280 --> 00:03:00,280
替换到了你这个页面中

127
00:03:00,280 --> 00:03:01,360
所写的这个双大块

128
00:03:01,360 --> 00:03:02,320
替换到这里

129
00:03:02,320 --> 00:03:04,300
然后把你数据中的这个土豆子

130
00:03:04,300 --> 00:03:05,780
是通过这个一起循环的方式

131
00:03:05,780 --> 00:03:06,480
就是给替换到这里

132
00:03:06,480 --> 00:03:07,700
然后循环生成了多个力

133
00:03:07,700 --> 00:03:09,060
所以说在这里的话呢

134
00:03:09,060 --> 00:03:10,080
它就是这样一种方式

135
00:03:10,080 --> 00:03:10,820
好

136
00:03:10,820 --> 00:03:11,660
那么写好以后

137
00:03:11,660 --> 00:03:13,560
然后接下来回到我们的命令行当中

138
00:03:13,560 --> 00:03:14,520
好我们在命令行当中

139
00:03:14,520 --> 00:03:15,920
接下来我们加来node06

140
00:03:15,920 --> 00:03:17,180
就是来启动这个脚本

141
00:03:17,180 --> 00:03:17,920
好 回事

142
00:03:17,920 --> 00:03:18,740
我们现在看到

143
00:03:18,740 --> 00:03:19,760
这个服务正常启动成功

144
00:03:19,760 --> 00:03:20,520
成功以后

145
00:03:20,520 --> 00:03:21,900
回到我们的浏览器当中

146
00:03:21,900 --> 00:03:22,680
那么此时我们

147
00:03:22,680 --> 00:03:23,900
接下来我们来访问

148
00:03:23,900 --> 00:03:25,520
127.0.0.1

149
00:03:25,520 --> 00:03:26,540
123千

150
00:03:26,540 --> 00:03:26,940
回事

151
00:03:26,940 --> 00:03:28,440
那么大家此时就发现

152
00:03:28,440 --> 00:03:29,360
我们收到了一个

153
00:03:29,360 --> 00:03:29,780
Hello World

154
00:03:29,780 --> 00:03:30,960
吃饭睡觉打豆腐

155
00:03:30,960 --> 00:03:31,500
你会发现

156
00:03:31,500 --> 00:03:32,300
睡觉默认

157
00:03:32,300 --> 00:03:33,220
是不是就是打高的

158
00:03:33,220 --> 00:03:34,720
当然此时

159
00:03:34,720 --> 00:03:36,160
对于我们这个页面来说

160
00:03:36,160 --> 00:03:37,120
它现在这个数据呢

161
00:03:37,120 --> 00:03:37,980
其实就是动态的

162
00:03:37,980 --> 00:03:38,880
它不是写死的

163
00:03:38,880 --> 00:03:39,260
也就是说

164
00:03:39,260 --> 00:03:40,140
现在你这个Hello

165
00:03:40,140 --> 00:03:40,660
后面是什么

166
00:03:40,660 --> 00:03:41,840
包括你一起吐肚子

167
00:03:41,840 --> 00:03:42,780
虽然说这个力是什么

168
00:03:42,780 --> 00:03:45,400
是完全取决于我们的数据是什么呀

169
00:03:45,400 --> 00:03:46,840
例如我在这里来个

170
00:03:46,840 --> 00:03:49,980
我在这里来给他写一个黑马程序员

171
00:03:49,980 --> 00:03:51,360
那么回到浏览机当中

172
00:03:51,360 --> 00:03:52,720
你在这里刷新一下

173
00:03:52,720 --> 00:03:54,500
当然我们发现一件事

174
00:03:54,500 --> 00:03:55,960
就是我们要把我们的服务

175
00:03:55,960 --> 00:03:57,520
是不是要通过NodeMount

176
00:03:57,520 --> 00:03:58,620
来个集中起来

177
00:03:58,620 --> 00:03:59,760
因为只有这样的话

178
00:03:59,760 --> 00:04:01,600
我们才能就是看到这个最新的改变

179
00:04:01,600 --> 00:04:03,200
我们看到这里就是黑马程序员

180
00:04:03,200 --> 00:04:04,040
那么同样的

181
00:04:04,040 --> 00:04:05,460
例如我们把数据当中这个吃饭

182
00:04:05,460 --> 00:04:06,840
我们给他改成True

183
00:04:06,840 --> 00:04:07,620
那我们页面中

184
00:04:07,620 --> 00:04:08,620
是不是要根据你的数据

185
00:04:08,620 --> 00:04:10,440
就是来决定这个checkbox是选中与否

186
00:04:10,440 --> 00:04:11,800
那回到这个页面当中

187
00:04:11,800 --> 00:04:12,520
我们再去刷新一下

188
00:04:12,520 --> 00:04:13,460
就看到这个吃饭

189
00:04:13,460 --> 00:04:15,080
是不是就是默认被选中的

190
00:04:15,080 --> 00:04:17,040
这就是什么呢

191
00:04:17,040 --> 00:04:18,920
这就是我们在这一个

192
00:04:18,920 --> 00:04:20,060
就是Note中

193
00:04:20,060 --> 00:04:21,820
让我们这个模板引擎

194
00:04:21,820 --> 00:04:23,960
和我们的HTT服务

195
00:04:23,960 --> 00:04:26,020
结合渲染页面的一个过程

