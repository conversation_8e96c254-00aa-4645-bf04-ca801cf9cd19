1
00:00:00,000 --> 00:00:03,080
好 那么蒙古斯也讲完了 我们就正式进入咱们一个

2
00:00:03,080 --> 00:00:05,640
实战 我们去实现我们的一个简易博客

3
00:00:05,640 --> 00:00:08,960
那么呢 接下来内容呢 我们重点不要去关注讲义了

4
00:00:08,960 --> 00:00:13,060
大家要跟着老师去写代码就可以了 我们的一些咱们的讲义呢 其实都会写到我们的代码中

5
00:00:13,060 --> 00:00:16,380
以注释的形式存在 所以要同学们重点去关注老师的编辑器就可以了

6
00:00:16,380 --> 00:00:20,480
那么简易呢 先把它放在那边 好 那么首先我们项目开始之前 怎么样去

7
00:00:20,480 --> 00:00:24,440
出爽一个egg项目 大家记得吗 是不是npm init egg

8
00:00:24,440 --> 00:00:28,920
然后给了个参数 是不是对吧 type=simple 说明我们起了一个简单项目

9
00:00:28,920 --> 00:00:30,200
好 那么我们稍等一下

10
00:00:30,200 --> 00:00:35,400
那么在安装的过程中呢 其实可以跟同学们去简单的聊一下 那么一GG这样一个框架呢

11
00:00:35,400 --> 00:00:37,620
 其实我们开服供充其实是非常爽的

12
00:00:37,620 --> 00:00:38,900
而且写多了之后大家会发现

13
00:00:38,900 --> 00:00:40,180
其实我们开发服务端

14
00:00:40,180 --> 00:00:42,480
比开发前端会简单一点 为什么呀

15
00:00:42,480 --> 00:00:46,080
因为咱们服务器没有那么多的兼容性问题 比如说你去开发我们的前端

16
00:00:46,080 --> 00:00:48,880
 你经常去写CSS 写HML 写GS

17
00:00:48,880 --> 00:00:52,980
然后写完了之后呢 你还要去看咱们浏览器的一些兼容性 对吧 其实非常的难受

18
00:00:52,980 --> 00:00:54,300
但是咱们后端开发不一样

19
00:00:54,300 --> 00:00:55,900
也可以专注去写你的业务逻辑

20
00:00:55,900 --> 00:00:56,980
专注的去写你的纪约室

21
00:00:56,980 --> 00:00:58,300
在服务端是不存在一个

22
00:00:58,300 --> 00:00:59,560
兼容性问题

23
00:00:59,560 --> 00:01:01,560
那么安装呢

24
00:01:01,560 --> 00:01:02,280
好像稍微

25
00:01:02,280 --> 00:01:03,540
是不是有一点慢了

26
00:01:03,540 --> 00:01:05,020
那么其实

27
00:01:05,020 --> 00:01:06,000
177的舒适化

28
00:01:06,000 --> 00:01:07,480
可能就是稍微的慢

29
00:01:07,480 --> 00:01:09,020
那没事我们等一下

30
00:01:09,020 --> 00:01:12,160
我们呢已经安装完成了

31
00:01:12,160 --> 00:01:12,760
那么这里呢

32
00:01:12,760 --> 00:01:13,980
咱们project呢

33
00:01:13,980 --> 00:01:15,060
都使用它默认的

34
00:01:15,060 --> 00:01:15,920
好肯定要一下

35
00:01:15,920 --> 00:01:17,140
然后接下来干什么

36
00:01:17,140 --> 00:01:17,980
是不是安装依赖呀

37
00:01:17,980 --> 00:01:19,040
npm store

38
00:01:19,040 --> 00:01:20,200
我们来安装一下依赖

39
00:01:20,200 --> 00:01:21,480
好那么在安装依赖的时候呢

40
00:01:21,480 --> 00:01:24,900
那我们现在给大家介绍一个插件 我们呢其实需要一个插件叫做什么呢

41
00:01:24,900 --> 00:01:26,000
 叫做我们的一个

42
00:01:26,000 --> 00:01:31,320
controller 在config里面 我们怎么样去给咱们EGG的项目去配置插件呢

43
00:01:31,320 --> 00:01:37,920
是不是在他的一个plugin.js下面呢 那么其实我们 我们刚才是不是讲到了需要mongos

44
00:01:37,920 --> 00:01:38,280
 所以说呢

45
00:01:38,280 --> 00:01:40,920
我们需要需要使用到mongos这样一个插件

46
00:01:40,920 --> 00:01:45,120
好 那么他是怎么样去使用的呢 我们来看一下他的一个

47
00:01:45,120 --> 00:01:47,400
我们来搜索一下

48
00:01:48,280 --> 00:01:50,400
其实这样一个插件是一GG它官方原生提供的

49
00:01:50,400 --> 00:01:55,800
包括我们它呢不仅提供了像咱们mongodb的操作库其实也提供了什么样是mysoco其实它也提供了

50
00:01:55,800 --> 00:01:57,220
我们来看一下一GG-mongos

51
00:01:57,220 --> 00:02:02,460
那么其实这里呢它是一GG团队他们自己去开发了一个mongos他们自己写的插件

52
00:02:02,460 --> 00:02:04,100
当然同学们如果说有兴趣也可以自己去写一个

53
00:02:04,100 --> 00:02:06,600
那么怎么样去使用呢先安装一下对吧一GG-mongos

54
00:02:06,600 --> 00:02:10,640
然后配置一下enable改为2package去定义一下我们去使用它这样一个插件

55
00:02:10,640 --> 00:02:13,940
然后再在config default.js里面去配置一些信息

56
00:02:13,940 --> 00:02:15,260
好那么我们先来

57
00:02:15,260 --> 00:02:17,660
我们先来把它给fix一下

58
00:02:18,040 --> 00:02:21,620
因为如果说npm出现了这样一个提示 你最好是把它fax一下以免出现一些意外

59
00:02:21,620 --> 00:02:22,900
好 那么我们先来

60
00:02:22,900 --> 00:02:28,280
配置一下 首先在plugin里面 我们是不是需要去配一个mongus这样的一个

61
00:02:28,280 --> 00:02:29,820
属性

62
00:02:29,820 --> 00:02:37,500
然后把它的enable改为2 意思呢就是打开enable之后呢 咱们是不是要配置package

63
00:02:37,500 --> 00:02:40,560
package说明了咱们是不是去定义咱们引用的

64
00:02:40,560 --> 00:02:41,600
酷是哪个

65
00:02:41,600 --> 00:02:42,880
egg-mongus

66
00:02:42,880 --> 00:02:46,460
好 那么我们配置完成之后来安装一下 安装一下这一个酷

67
00:02:46,720 --> 00:02:48,280
怎么样安装呢,npm

68
00:02:48,280 --> 00:02:52,600
刚刚save号,那我们安装一下,那么在安装的过程中呢

69
00:02:52,600 --> 00:02:54,400
我们刚才是不是在配置中

70
00:02:54,400 --> 00:03:00,040
是不是引入插件,对吧,那么引入插件之后要做什么,是不是要去配置插件了,所以说呢我们要在

71
00:03:00,040 --> 00:03:03,360
config里面去配置一个

72
00:03:03,360 --> 00:03:08,480
咱们mongos它的初始化的配置,那么怎么样去配置呢,咱们同样的来看一下它的官网

73
00:03:08,480 --> 00:03:10,020
其实它的配置非常简单

74
00:03:10,020 --> 00:03:12,060
比如说,我们其实只需要

75
00:03:12,060 --> 00:03:16,160
配置两个属性就可以了,一个是url,url就是代表什么呀,是不是咱们连接的是哪一个

76
00:03:16,420 --> 00:03:17,480
咱们连接哪个数据库

77
00:03:17,480 --> 00:03:19,700
那么Options就是它的一些配置

78
00:03:19,700 --> 00:03:21,640
那么在这个项目里面我们暂时没有什么配置

79
00:03:21,640 --> 00:03:24,820
Plugin是因为我们刚才是不是给同学看的一些文档

80
00:03:24,820 --> 00:03:27,320
我们Mongos里面它是不是可以去写插件

81
00:03:27,320 --> 00:03:27,800
对吧

82
00:03:27,800 --> 00:03:30,160
那么这里就是它提供你去引入

83
00:03:30,160 --> 00:03:31,300
Mongos插件的一个入口

84
00:03:31,300 --> 00:03:32,780
那么我们就来简单的配置一下

85
00:03:32,780 --> 00:03:34,640
咱们首先把它给贴过来

86
00:03:34,640 --> 00:03:41,000
那么这里就是它的一个配置

87
00:03:41,000 --> 00:03:43,500
好我们的URL现在是联系的是什么呀

88
00:03:43,500 --> 00:03:45,260
Mongodb说明了它是哪个端口啊

89
00:03:45,260 --> 00:03:47,180
是不是2707端口

90
00:03:47,180 --> 00:03:51,020
好 那么后面这个字段是不是代表我们

91
00:03:51,020 --> 00:03:53,260
我们什么呀 是不是我们去选择了哪个数据库

92
00:03:53,260 --> 00:03:55,960
那么它是什么意思呢 其实这里可以给同学来看一下

93
00:03:55,960 --> 00:03:57,160
比如说这是我们的可笑工具

94
00:03:57,160 --> 00:04:00,360
我们之前呢posts其实是存在在example在那个库里面的

95
00:04:00,360 --> 00:04:04,260
为什么呀 因为我们的本地是不是可以同时存在很多的库

96
00:04:04,260 --> 00:04:06,760
对吧 包括我们的应用也可以连接很多的数据库

97
00:04:06,760 --> 00:04:08,360
这里是我们其中的一个叫做example

98
00:04:08,360 --> 00:04:10,260
那么这个项目呢 我们把它的名字给改一下

99
00:04:10,260 --> 00:04:12,460
叫做posts

100
00:04:12,460 --> 00:04:14,760
其实不是很准确 因为posts只是代表文章

101
00:04:14,760 --> 00:04:16,060
但是我们要做的是博客

102
00:04:16,060 --> 00:04:17,340
所以我们就叫做

103
00:04:17,340 --> 00:04:20,660
博客

104
00:04:20,660 --> 00:04:22,300
博客是不是叫做blog

105
00:04:22,300 --> 00:04:22,960
对吧

106
00:04:22,960 --> 00:04:24,780
那么我们就创建了一个blog

107
00:04:24,780 --> 00:04:25,820
这样一个库

108
00:04:25,820 --> 00:04:27,380
那么我们是不是

109
00:04:27,380 --> 00:04:29,660
会在我们的本地的数据库里面

110
00:04:29,660 --> 00:04:30,840
去添加一个叫做blog

111
00:04:30,840 --> 00:04:31,660
这样一个库

112
00:04:31,660 --> 00:04:32,300
对吧

113
00:04:32,300 --> 00:04:33,860
那么这里我们现在

114
00:04:33,860 --> 00:04:34,460
对蒙古史

115
00:04:34,460 --> 00:04:36,140
它的配置已经完成了

116
00:04:36,140 --> 00:04:37,780
其实刚才我们是不是已经完成了

117
00:04:37,780 --> 00:04:38,840
我们开发顺序里面的第一步

118
00:04:38,840 --> 00:04:39,620
安装环境

119
00:04:39,620 --> 00:04:40,780
以及配置我们的数据库插件

120
00:04:40,780 --> 00:04:41,960
那么我们就来回顾一下

121
00:04:41,960 --> 00:04:43,020
咱们这节课的第一步

122
00:04:43,020 --> 00:04:43,560
这样一个内容

123
00:04:43,560 --> 00:04:44,520
首先我们是不是安装

124
00:04:44,520 --> 00:04:45,220
通过什么呀

125
00:04:45,220 --> 00:04:46,280
npm init egg

126
00:04:46,280 --> 00:04:47,400
然后通过它一个simple

127
00:04:47,400 --> 00:04:48,040
在那个叫模板

128
00:04:48,040 --> 00:04:48,780
第二个呢

129
00:04:48,780 --> 00:04:49,680
安装我们的一个插件

130
00:04:49,680 --> 00:04:50,620
egg gmungus

131
00:04:50,620 --> 00:04:51,520
那么安装完插件之后

132
00:04:51,520 --> 00:04:53,180
是不是需要去配置我们的插件

133
00:04:53,180 --> 00:04:55,020
也就是去在咱们的项目中去引入它

134
00:04:55,020 --> 00:04:55,760
那么引入之后

135
00:04:55,760 --> 00:04:58,100
是不是还需要在config的defaultgs里面去

136
00:04:58,100 --> 00:05:00,080
配置初始化的一些咱们数据库的配置

137
00:05:00,080 --> 00:05:00,520
比如说呢

138
00:05:00,520 --> 00:05:01,820
你需要去选择哪一个数据库

139
00:05:01,820 --> 00:05:03,180
然后呢还给它去起一个名称

140
00:05:03,180 --> 00:05:04,020
叫做blog

141
00:05:04,020 --> 00:05:06,860
那么这里呢就是我们开发里面的第一步

142
00:05:06,860 --> 00:05:08,140
那么我们完成第一步之后

143
00:05:08,140 --> 00:05:08,980
同学们思考一下

144
00:05:08,980 --> 00:05:10,540
我们接下来该干嘛

145
00:05:10,540 --> 00:05:11,760
我们数据库配好了

146
00:05:11,760 --> 00:05:12,600
已经配置好了

147
00:05:12,600 --> 00:05:13,060
接下来干嘛

148
00:05:13,060 --> 00:05:15,100
是不是去设计我们的数据库字段呢

149
00:05:15,100 --> 00:05:15,700
对吧

150
00:05:15,700 --> 00:05:16,920
你比如说我们要去做一个博客

151
00:05:16,920 --> 00:05:18,420
那么我们来分析一下文章

152
00:05:18,420 --> 00:05:19,480
它需要有哪些字段

153
00:05:19,480 --> 00:05:20,680
那么这里呢就是我们

154
00:05:20,680 --> 00:05:22,280
下节课所需要讲解的内容

155
00:05:22,280 --> 00:05:22,560
好

156
00:05:22,560 --> 00:05:24,460
那么我们这节课的内容就先到这里

