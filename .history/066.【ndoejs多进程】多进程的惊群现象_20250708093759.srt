1
00:00:00,000 --> 00:00:02,820
好 刚才我们是不是已经讲解了cluster的一些API

2
00:00:02,820 --> 00:00:06,660
那么接下来我们来看一下cluster它这样的一个多进程的模型有什么优势

3
00:00:06,660 --> 00:00:09,480
包括了咱们漏了间是早期的一些漏了

4
00:00:09,480 --> 00:00:11,800
多进程模型它有哪些的缺点

5
00:00:11,800 --> 00:00:14,520
首先我们为什么要使用cluster

6
00:00:14,520 --> 00:00:16,900
咱们用tilt process.fork不行吗

7
00:00:16,900 --> 00:00:18,700
之前我们是不是已经学过了

8
00:00:18,700 --> 00:00:21,000
可以通过负质进程通过send和on去通信

9
00:00:21,000 --> 00:00:24,400
那么他们是不是是不是已经也实现了负质之间的通信

10
00:00:24,400 --> 00:00:26,000
那么为什么还用cluster

11
00:00:26,000 --> 00:00:28,660
其实cluster它不仅实现了多进程

12
00:00:28,660 --> 00:00:31,000
它还对负质进程之间的通信

13
00:00:31,000 --> 00:00:31,800
质进程的管理

14
00:00:31,800 --> 00:00:32,500
以及负载均衡

15
00:00:32,500 --> 00:00:33,580
它解决了这样一些问题

16
00:00:33,580 --> 00:00:34,880
你比如说

17
00:00:34,880 --> 00:00:35,860
什么是负载均衡

18
00:00:35,860 --> 00:00:37,240
负载均衡

19
00:00:37,240 --> 00:00:38,020
其实就是

20
00:00:38,020 --> 00:00:39,080
你比如说我们的服务器

21
00:00:39,080 --> 00:00:39,960
它来了很多的请求

22
00:00:39,960 --> 00:00:40,920
假如说一千万条

23
00:00:40,920 --> 00:00:41,340
一亿条

24
00:00:41,340 --> 00:00:42,580
你一台服务器肯定扛不住

25
00:00:42,580 --> 00:00:43,880
你可能需要很多服务器去扛

26
00:00:43,880 --> 00:00:45,480
那么这里就涉及到一个

27
00:00:45,480 --> 00:00:46,740
咱们消息分发的问题

28
00:00:46,740 --> 00:00:47,620
你怎么样去分配

29
00:00:47,620 --> 00:00:48,460
你这么多服务器

30
00:00:48,460 --> 00:00:50,240
你分为十台服务器一起去扛

31
00:00:50,240 --> 00:00:51,060
你每台服务器

32
00:00:51,060 --> 00:00:52,860
是不是会分配一百万条

33
00:00:52,860 --> 00:00:53,800
一千万条

34
00:00:53,800 --> 00:00:54,900
那么这样也有一个问题

35
00:00:54,900 --> 00:00:55,660
有的服务性能好

36
00:00:55,660 --> 00:00:56,740
有的服务性能差

37
00:00:56,740 --> 00:00:58,660
你不能够平均的去分

38
00:00:58,660 --> 00:01:00,840
所以这里就涉及到一个负载均衡的一个问题

39
00:01:00,840 --> 00:01:02,560
cluster它可以解决这个问题

40
00:01:02,560 --> 00:01:05,260
好既然说到cluster它很好

41
00:01:05,260 --> 00:01:07,300
我们现在看一下以前早期的模型有哪些缺点

42
00:01:07,300 --> 00:01:08,800
首先有一个比较经典的问题

43
00:01:08,800 --> 00:01:09,900
也是最早期多进程的模型

44
00:01:09,900 --> 00:01:11,740
它有一个经典的问题叫做

45
00:01:11,740 --> 00:01:12,860
金群

46
00:01:12,860 --> 00:01:14,160
金群是怎么一回事呢

47
00:01:14,160 --> 00:01:16,740
金群其实就是多个进程之间会竞争一个链接

48
00:01:16,740 --> 00:01:17,500
产生金群现象

49
00:01:17,500 --> 00:01:18,540
它的效率比较低

50
00:01:18,540 --> 00:01:20,340
而且它会造成一个问题是什么呢

51
00:01:20,340 --> 00:01:21,620
他无法控制一个新的链接

52
00:01:21,620 --> 00:01:22,440
由哪个竞争来处理

53
00:01:22,440 --> 00:01:23,820
所以导致walker竞争之间的负载

54
00:01:23,820 --> 00:01:24,460
非常的不均衡

55
00:01:24,460 --> 00:01:25,660
那么我们来看一下

56
00:01:25,660 --> 00:01:28,480
竞争竞争竞争是怎么样产生的

57
00:01:28,480 --> 00:01:29,740
首先

58
00:01:29,740 --> 00:01:32,000
我们的父子竞争是不是会有一个master

59
00:01:32,000 --> 00:01:33,160
然后会有很多的walker

60
00:01:33,160 --> 00:01:34,460
这那是子竞争

61
00:01:34,460 --> 00:01:35,440
好

62
00:01:35,440 --> 00:01:36,200
首先呢

63
00:01:36,200 --> 00:01:38,980
master他会监听一个端口3000

64
00:01:38,980 --> 00:01:40,340
也就是咱们的服务端

65
00:01:40,340 --> 00:01:42,320
会一个端口3000端口去响应扣端请求

66
00:01:42,320 --> 00:01:44,080
那么一台服务肯定不够

67
00:01:44,080 --> 00:01:45,140
或者说一个竞争肯定不扣

68
00:01:45,140 --> 00:01:47,220
所以他需要产生四个walker去帮助他处理

69
00:01:47,220 --> 00:01:48,960
那么这4个work怎么样去帮助他处理呢

70
00:01:48,960 --> 00:01:50,160
首先

71
00:01:50,160 --> 00:01:52,480
他们都会去listen了3000

72
00:01:52,480 --> 00:01:54,940
也就是说master首先fork四个进程

73
00:01:54,940 --> 00:01:55,540
首先fork

74
00:01:55,540 --> 00:01:58,800
然后这4个work都会去listen了3000端口

75
00:01:58,800 --> 00:02:00,220
也就是有一个关键字

76
00:02:00,220 --> 00:02:02,120
金群产生的关键字就是抢

77
00:02:02,120 --> 00:02:03,040
抢琴球

78
00:02:03,040 --> 00:02:03,880
说白了

79
00:02:03,880 --> 00:02:05,760
他们4个work都去listen了3000

80
00:02:05,760 --> 00:02:07,700
也就是说谁先抢到谁就去处理

81
00:02:07,700 --> 00:02:10,320
这就是金群现象产生的一个原因

82
00:02:10,320 --> 00:02:12,700
那么接下来呢

83
00:02:12,700 --> 00:02:13,880
我们就来通过代码来看一下

84
00:02:13,880 --> 00:02:14,900
到底什么是金群现象

85
00:02:17,220 --> 00:02:23,100
好 这里呢 老师见了一个suprise的文件夹 里面有个master和一个workmaster负责主进程的逻辑

86
00:02:23,100 --> 00:02:27,120
我可能负责主进程的逻辑 因为金群现象呢 可能呢 代码稍微有点复杂

87
00:02:27,120 --> 00:02:29,880
 所以说呢 老师这里就不去写了 带大家去看一遍 咱们的代码

88
00:02:29,880 --> 00:02:33,820
首先这里会通过require一个net模块 net是什么呀

89
00:02:33,820 --> 00:02:39,770
net它其实是最基础的网络模块 htb就是基于net去封装的 所以net它是进行最底层的

90
00:02:39,770 --> 00:02:40,340
 说可以的

91
00:02:40,340 --> 00:02:44,620
这里呢 可能设计了nodejs 它比较

92
00:02:45,720 --> 00:02:47,520
他可能需要大家去看一下漏的建设文档

93
00:02:47,520 --> 00:02:50,320
或者说去看一下前面的视频

94
00:02:50,320 --> 00:02:51,600
这里老师先不去做解释

95
00:02:51,600 --> 00:02:53,140
同样有兴趣可以自己去看文档

96
00:02:53,140 --> 00:02:55,200
因为这里介绍net模块的部分不算多

97
00:02:55,200 --> 00:02:56,220
好fork呢

98
00:02:56,220 --> 00:02:57,500
这里我们用chill processfork

99
00:02:57,500 --> 00:02:58,520
为什么呢为什么不用cluster

100
00:02:58,520 --> 00:03:01,080
因为我们要去看它的缺点

101
00:03:01,080 --> 00:03:02,360
cluster其实没有精群现象

102
00:03:02,360 --> 00:03:02,880
所以说呢

103
00:03:02,880 --> 00:03:03,900
我们来看一下chill process

104
00:03:03,900 --> 00:03:04,660
怎么样造成的精群

105
00:03:04,660 --> 00:03:06,720
精群

106
00:03:06,720 --> 00:03:10,560
好首先

107
00:03:10,560 --> 00:03:12,600
我们通过net模块去创建一个服务

108
00:03:12,600 --> 00:03:13,360
绑定到3000端口

109
00:03:13,360 --> 00:03:14,400
他会返回一个

110
00:03:14,900 --> 00:03:17,060
也就是一个咱们处理请求的一个函数

111
00:03:17,060 --> 00:03:20,900
返回一个callback

112
00:03:20,900 --> 00:03:23,460
好 咱们来通过货循环去fork四次

113
00:03:23,460 --> 00:03:24,500
fork是个worker

114
00:03:24,500 --> 00:03:26,260
worker就是咱们work.js的代码

115
00:03:26,260 --> 00:03:28,500
这里呢 我就没有去fork八份了

116
00:03:28,500 --> 00:03:30,420
咱们只fork四个 也就是咱们只起四个进程

117
00:03:30,420 --> 00:03:32,100
然后send 剩的是什么呀 之前是不是讲过

118
00:03:32,100 --> 00:03:34,900
fork出来的子进程会返回一个对象

119
00:03:34,900 --> 00:03:37,380
用send的方法往process 通过onmessage可以接收

120
00:03:37,380 --> 00:03:39,860
然后呢 把hander 就是把这样一个callback给传入

121
00:03:39,860 --> 00:03:41,380
好 咱们来看一下咱们的worker

122
00:03:41,380 --> 00:03:43,540
首先呢 还会去require一下net

123
00:03:43,540 --> 00:03:46,180
onMessage也就是去接收咱们直径程的

124
00:03:46,180 --> 00:03:48,620
也就是接收咱们Fork刚才圣的过来的消息对吧

125
00:03:48,620 --> 00:03:50,680
圣的刚才是不是传了两个参数

126
00:03:50,680 --> 00:03:51,460
一个对象一个handle

127
00:03:51,460 --> 00:03:55,620
handle的就是咱们通过let query server去创建了一个服务

128
00:03:55,620 --> 00:03:58,340
好那么咱们再用通过stat方法把这样一个

129
00:03:58,340 --> 00:04:01,900
把这样一个net创建的服务传入

130
00:04:01,900 --> 00:04:03,020
好我们先来看一下start

131
00:04:03,020 --> 00:04:04,360
server是不是就是它

132
00:04:04,360 --> 00:04:06,780
首先呢我们去调用一下listen

133
00:04:06,780 --> 00:04:08,400
监听一下然后呢创建一个链接

134
00:04:08,400 --> 00:04:10,660
connection连接它的套接字

135
00:04:11,420 --> 00:04:11,940
连接之后呢

136
00:04:11,940 --> 00:04:12,460
它又会

137
00:04:12,460 --> 00:04:14,240
有一个callback让我去处理

138
00:04:14,240 --> 00:04:16,300
大家注意这一个handle和这个handle它不是同一个东西

139
00:04:16,300 --> 00:04:17,580
大家可以仔细去看一下

140
00:04:17,580 --> 00:04:18,340
好

141
00:04:18,340 --> 00:04:19,880
重点看这里重点看这里

142
00:04:19,880 --> 00:04:21,660
首先呢我们会遛一个

143
00:04:21,660 --> 00:04:22,700
net的socket

144
00:04:22,700 --> 00:04:24,220
也就是说呢咱们新建议的套节字

145
00:04:24,220 --> 00:04:25,500
然后去把它的

146
00:04:25,500 --> 00:04:27,040
可读可写的属性改为q

147
00:04:27,040 --> 00:04:28,060
在end的时候

148
00:04:28,060 --> 00:04:29,340
返回咱们的rent

149
00:04:29,340 --> 00:04:30,620
rent是什么呀

150
00:04:30,620 --> 00:04:31,400
rent是不是咱们的

151
00:04:31,400 --> 00:04:34,720
结果啊其实返回的就是hellonodejshttb

152
00:04:34,720 --> 00:04:36,000
状态码把它改为200

153
00:04:36,000 --> 00:04:38,300
咱们httb是不是可以直接

154
00:04:38,300 --> 00:04:39,580
recreitend

155
00:04:39,580 --> 00:04:40,360
然后hellonodejs

156
00:04:40,360 --> 00:04:41,520
但是在net模块里面不可以

157
00:04:41,520 --> 00:04:42,400
因为它是最底层的模块

158
00:04:42,400 --> 00:04:43,420
我们需要自己去处理状态

159
00:04:43,420 --> 00:04:44,820
处理它的httb协议

160
00:04:44,820 --> 00:04:46,500
包括一些咱们的内容的长度

161
00:04:46,500 --> 00:04:48,300
这里就不去过多来去介绍了

162
00:04:48,300 --> 00:04:48,960
好

163
00:04:48,960 --> 00:04:49,820
那么这里咱们

164
00:04:49,820 --> 00:04:51,500
walker和master逻辑就已经完成了

165
00:04:51,500 --> 00:04:53,820
这里我通过一个data对象

166
00:04:53,820 --> 00:04:54,800
去存了一下pid

167
00:04:54,800 --> 00:04:56,700
然后每次咱们的walker

168
00:04:56,700 --> 00:04:57,860
去进行服务的时候

169
00:04:57,860 --> 00:04:58,800
就给它加1

170
00:04:58,800 --> 00:05:03,420
每次服务加1

171
00:05:03,420 --> 00:05:03,820
好

172
00:05:03,820 --> 00:05:05,940
这里就是咱们金群现象的一个代码编写

173
00:05:05,940 --> 00:05:08,460
这里再把每次加1的结果

174
00:05:08,460 --> 00:05:09,260
咱们把它统计出来

175
00:05:09,260 --> 00:05:09,460
好

176
00:05:09,460 --> 00:05:10,260
这里我们就来看一下

177
00:05:10,260 --> 00:05:12,260
金群现象到底是怎么样去发生的

178
00:05:12,260 --> 00:05:14,680
好

179
00:05:14,680 --> 00:05:15,860
固定一下

180
00:05:15,860 --> 00:05:16,880
咱们旗下服务

181
00:05:16,880 --> 00:05:17,740
load master.js

182
00:05:17,740 --> 00:05:18,660
是不是fork了四个进程

183
00:05:18,660 --> 00:05:19,680
咱们来访问一下

184
00:05:19,680 --> 00:05:21,120
Hello load.js

185
00:05:21,120 --> 00:05:21,560
对吧

186
00:05:21,560 --> 00:05:22,320
咱们来看一下

187
00:05:22,320 --> 00:05:23,320
gold connection

188
00:05:23,320 --> 00:05:24,960
onwalker pid 56115

189
00:05:24,960 --> 00:05:25,360
一次

190
00:05:25,360 --> 00:05:26,880
这里的其实看不出来

191
00:05:26,880 --> 00:05:28,120
所以说我们需要用一个神器

192
00:05:28,120 --> 00:05:28,920
ab

193
00:05:28,920 --> 00:05:30,340
ab是什么

194
00:05:30,340 --> 00:05:31,400
是不是咱们进行压策的一个工具

195
00:05:31,400 --> 00:05:32,540
咱们来访问一千次

196
00:05:32,540 --> 00:05:33,580
进行20个并发

197
00:05:33,580 --> 00:05:33,880
看一下

198
00:05:33,880 --> 00:05:34,920
好 走完了

199
00:05:34,920 --> 00:05:36,260
大家可以注意到

200
00:05:36,260 --> 00:05:38,140
大家可以注意到

201
00:05:38,140 --> 00:05:39,060
咱们是不是

202
00:05:39,060 --> 00:05:48,720
gott connection on walker pid 56112115113114也就说咱们的112113114115这四个pid是不是就是咱们四个子星层他们分别处理多少个请求

203
00:05:48,720 --> 00:05:52,560
一个207次295269239

204
00:05:52,560 --> 00:05:56,820
大家注意到他的一个规律是什么是不是他们四个星层之间处理的次数不一样啊

205
00:05:56,820 --> 00:05:59,300
这里是不是就是造成了咱们的负载不均衡

206
00:05:59,300 --> 00:06:06,060
是不是之前讲过这就是金群现象产生的原因他们都会去抢都会去抢都会去抢

207
00:06:06,060 --> 00:06:06,780
抢

208
00:06:08,400 --> 00:06:11,480
强请求,所以这里就会造成金群现象

209
00:06:11,480 --> 00:06:13,780
好,那咱们来回归一下

210
00:06:13,780 --> 00:06:18,640
我们最初的多进程模型

211
00:06:18,640 --> 00:06:23,760
他造成的问题就是金群,那么什么是金群呢?多个进程之间会竞争一个链接

212
00:06:23,760 --> 00:06:26,840
你比如说咱们Master是不是创建了一个3000端口

213
00:06:26,840 --> 00:06:28,360
然后Fork的4个子进程

214
00:06:28,360 --> 00:06:30,160
4个子进程都会去

215
00:06:30,160 --> 00:06:31,700
NIST去抢

216
00:06:31,700 --> 00:06:33,240
抢咱们扣张那些请求

217
00:06:33,240 --> 00:06:35,540
所以就造成了咱们的金群现象

218
00:06:35,800 --> 00:06:37,880
而且由于你是不是无法控制哪个连接

219
00:06:37,880 --> 00:06:38,820
由哪个竞争来处理

220
00:06:38,820 --> 00:06:40,200
为什么无法控制

221
00:06:40,200 --> 00:06:42,020
是不是因为咱们的Worker都在抢

222
00:06:42,020 --> 00:06:43,620
你也不知道谁会先抢到

223
00:06:43,620 --> 00:06:45,740
所以说造成负载非常的不均衡

224
00:06:45,740 --> 00:06:47,900
这里也就是著名的金群现象

225
00:06:47,900 --> 00:06:51,020
好 老师先把视频停一下

