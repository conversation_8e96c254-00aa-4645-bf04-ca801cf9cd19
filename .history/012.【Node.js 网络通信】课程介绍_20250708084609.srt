1
00:00:01,000 --> 00:00:04,160
欢迎大家来学习Node.js网络通信这门课程

2
00:00:04,160 --> 00:00:07,400
首先我们来对这门课程做一个简单介绍

3
00:00:07,400 --> 00:00:09,980
那我们每天使用互联网

4
00:00:09,980 --> 00:00:13,340
大家有没有想过我们互联网它是如何实现的

5
00:00:13,340 --> 00:00:17,000
说完了就是说我们在网络当中是如何进行通信的

6
00:00:17,000 --> 00:00:18,900
我们大家可以想象一下

7
00:00:18,900 --> 00:00:22,080
我们全世界有很多电脑很多电子设备

8
00:00:22,080 --> 00:00:23,260
包括我们现在的手机

9
00:00:23,260 --> 00:00:27,460
我们可以在上海的某一个电脑或者是手机发出一个信号

10
00:00:27,580 --> 00:00:28,860
我们就可以在美国

11
00:00:28,860 --> 00:00:30,000
例如洛杉矶的某个地方

12
00:00:30,000 --> 00:00:30,820
然后就收到了

13
00:00:30,820 --> 00:00:32,500
那么两者根本就不知道

14
00:00:32,500 --> 00:00:34,620
对方的现实当中具体的物理位置

15
00:00:34,620 --> 00:00:37,080
那这个到底是如何做到的

16
00:00:37,080 --> 00:00:39,540
那这个实际上是一件非常神奇的一个事情

17
00:00:39,540 --> 00:00:41,040
那我们这边课程

18
00:00:41,040 --> 00:00:42,280
就是教大家

19
00:00:42,280 --> 00:00:44,980
就是去理解网络通信的一个本质

20
00:00:44,980 --> 00:00:46,980
然后去掌握使用NodeGIS

21
00:00:46,980 --> 00:00:47,980
去进行网络通信

22
00:00:47,980 --> 00:00:50,460
那这里为什么使用我们

23
00:00:50,460 --> 00:00:52,380
基于NodeGIS的一个网络编程

24
00:00:52,380 --> 00:00:53,340
那这里呢

25
00:00:53,340 --> 00:00:54,340
我们再简单来说一下

26
00:00:54,340 --> 00:00:55,280
那首先

27
00:00:55,280 --> 00:00:57,920
node是一个面向网络而生的一个平台

28
00:00:57,920 --> 00:01:00,320
它本身呢是具有事件驱动

29
00:01:00,320 --> 00:01:03,080
无阻塞和单线程等相关的一些特性

30
00:01:03,080 --> 00:01:05,120
它具备非常良好的一个伸缩性

31
00:01:05,120 --> 00:01:06,840
使得它本身呢是非常轻量

32
00:01:06,840 --> 00:01:10,040
node本身呢非常适合在这种分布式网络中

33
00:01:10,040 --> 00:01:11,920
扮演去各种各样的角色

34
00:01:11,920 --> 00:01:13,540
那么同时node提供的API呢

35
00:01:13,540 --> 00:01:14,760
是非常的贴合网络

36
00:01:14,760 --> 00:01:17,840
非常适合用来构建灵活的网络服务

37
00:01:17,840 --> 00:01:19,640
那么我们可以利用node

38
00:01:19,640 --> 00:01:23,260
提供的这个API来构建我们这个网络服务器

39
00:01:23,260 --> 00:01:25,740
可以非常方便的构建这个网络服务

40
00:01:25,740 --> 00:01:26,420
那么说到这里

41
00:01:26,420 --> 00:01:27,120
我们可以来看一下

42
00:01:27,120 --> 00:01:28,700
我们传统的这个Web平台

43
00:01:28,700 --> 00:01:29,520
大多数呢

44
00:01:29,520 --> 00:01:30,960
都需要专门的Web服务器

45
00:01:30,960 --> 00:01:32,560
来作为这个Web容器

46
00:01:32,560 --> 00:01:34,720
例如我们这个Virium的ASP

47
00:01:34,720 --> 00:01:35,720
AS.NET等

48
00:01:35,720 --> 00:01:37,920
它们需要IIS来作为它们的服务器

49
00:01:37,920 --> 00:01:39,500
例如我们常见的PVP

50
00:01:39,500 --> 00:01:41,860
可能需要搭载Apaq或者Enginex

51
00:01:41,860 --> 00:01:43,100
来作为它的服务环境

52
00:01:43,100 --> 00:01:44,660
但是我们Note本身

53
00:01:44,660 --> 00:01:46,400
它只需要简单几行代码

54
00:01:46,400 --> 00:01:48,080
就能把这个服务去给它创建出来

55
00:01:48,080 --> 00:01:50,300
它不需要额外的这个

56
00:01:50,300 --> 00:01:51,460
例如Tomcat啊

57
00:01:51,460 --> 00:01:52,340
IIS啊

58
00:01:52,340 --> 00:01:53,060
Apaq啊

59
00:01:53,060 --> 00:01:54,620
就是作为这个额外的服务容器

60
00:01:54,620 --> 00:01:55,660
它本身就可以

61
00:01:55,660 --> 00:01:56,800
它本身就是说

62
00:01:56,800 --> 00:01:58,140
理解成既是一个平台

63
00:01:58,140 --> 00:01:59,720
然后也是一个外部容器

64
00:01:59,720 --> 00:02:00,920
所以Node本身呢

65
00:02:00,920 --> 00:02:01,460
非常强大

66
00:02:01,460 --> 00:02:02,480
好

67
00:02:02,480 --> 00:02:03,960
那我们NodeGS本身

68
00:02:03,960 --> 00:02:05,580
它为我们提供了

69
00:02:05,580 --> 00:02:05,940
Net

70
00:02:05,940 --> 00:02:06,820
Dgram

71
00:02:06,820 --> 00:02:07,700
HTTP

72
00:02:07,700 --> 00:02:09,540
HTTP-S等四个模块

73
00:02:09,540 --> 00:02:11,020
分别用于处理

74
00:02:11,020 --> 00:02:12,020
我们网络当中的

75
00:02:12,020 --> 00:02:12,720
TCP

76
00:02:12,720 --> 00:02:13,520
UDP

77
00:02:13,520 --> 00:02:14,280
HTTP

78
00:02:14,280 --> 00:02:15,320
HTTP-S

79
00:02:15,320 --> 00:02:16,900
那这些模块呢

80
00:02:16,900 --> 00:02:19,680
分别适用于我们的服务器端和客户端

81
00:02:19,680 --> 00:02:21,180
好

82
00:02:21,180 --> 00:02:22,160
了解了NodeGS

83
00:02:22,160 --> 00:02:23,320
就于网络编程

84
00:02:23,320 --> 00:02:25,180
那么我们在整个课程当中

85
00:02:25,180 --> 00:02:26,520
在这就会去学习

86
00:02:26,520 --> 00:02:27,440
使用到这四个模块

87
00:02:27,440 --> 00:02:28,880
看一下他们是如何

88
00:02:28,880 --> 00:02:30,920
来构建这个网络通信

89
00:02:30,920 --> 00:02:32,860
以及在什么样的场景下

90
00:02:32,860 --> 00:02:34,560
去使用什么样的协议

91
00:02:34,560 --> 00:02:35,740
或者说什么样的模块

92
00:02:35,740 --> 00:02:36,680
去进行处理

93
00:02:36,680 --> 00:02:39,300
那接下来我们来看一下

94
00:02:39,300 --> 00:02:41,420
我们整个课程的一个目录大纲

95
00:02:41,420 --> 00:02:42,860
那么第一点

96
00:02:42,860 --> 00:02:44,580
我们首先来给大家介绍一下

97
00:02:44,580 --> 00:02:46,520
关于Nodejs网络通信的一个概述

98
00:02:46,520 --> 00:02:47,940
那么在这一环节

99
00:02:47,940 --> 00:02:49,600
会大家讲到我们互联网当中

100
00:02:49,600 --> 00:02:51,480
相关的一些协议

101
00:02:51,480 --> 00:02:53,220
例如什么是Mac地址

102
00:02:53,220 --> 00:02:54,680
什么是IP地址

103
00:02:54,680 --> 00:02:55,320
什么是端口号

104
00:02:55,320 --> 00:02:57,280
什么是socket等等相关内容

105
00:02:57,280 --> 00:02:58,680
然后第二点

106
00:02:58,680 --> 00:03:00,280
就是我们来教大家

107
00:03:00,280 --> 00:03:01,860
如何去构建TCP服务

108
00:03:01,860 --> 00:03:02,820
那么这里的话

109
00:03:02,820 --> 00:03:03,600
我们会使用到

110
00:03:03,600 --> 00:03:05,100
NodeGS当中提供的Net模块

111
00:03:05,100 --> 00:03:06,940
来构建TCP服务

112
00:03:06,940 --> 00:03:07,920
然后再往下

113
00:03:07,920 --> 00:03:08,880
我们会学习到

114
00:03:08,880 --> 00:03:11,180
使用NodeGS来构建UDP服务

115
00:03:11,180 --> 00:03:11,820
那么这里的话

116
00:03:11,820 --> 00:03:12,540
我们会使用到

117
00:03:12,540 --> 00:03:13,600
它的Dgram这个模块

118
00:03:13,600 --> 00:03:16,500
那接下来我们会使用到

119
00:03:16,500 --> 00:03:19,140
在Node中来构建HTTP服务

120
00:03:19,140 --> 00:03:20,740
那么HTTP服务呢

121
00:03:20,740 --> 00:03:24,380
就可以非常方便的帮我们去处理这个浏览器和服务端的一个交互

122
00:03:24,380 --> 00:03:28,860
那么其次就是我们用Node来构建Web Socket的服务

123
00:03:28,860 --> 00:03:30,140
那么Web Socket呢

124
00:03:30,140 --> 00:03:30,960
Web Socket呢

125
00:03:30,960 --> 00:03:35,320
可以非常方便的用于我们浏览器和我们的服务端的这种双向通信

126
00:03:35,320 --> 00:03:37,360
就是我们常见的这种实时通信

127
00:03:37,360 --> 00:03:38,840
例如网页中的这个聊天

128
00:03:38,840 --> 00:03:41,460
就非常适合用这个东西来处理

129
00:03:41,460 --> 00:03:42,740
那么在课程的最后

130
00:03:42,740 --> 00:03:47,220
我们会给大家讲到如何使用Node GIS来构建HTTP S服务

131
00:03:47,220 --> 00:03:48,200
那么这个东西呢

132
00:03:48,200 --> 00:03:50,000
是主要是用来构建我们这种

133
00:03:50,000 --> 00:03:51,040
它基于http

134
00:03:51,040 --> 00:03:53,920
就是一种更安全的http数据传输

135
00:03:53,920 --> 00:03:55,760
那么我们在这门课程当中呢

136
00:03:55,760 --> 00:03:57,040
都会去给大家去设计的

137
00:03:57,040 --> 00:04:00,000
了解了我们的课程目录大纲之后

138
00:04:00,000 --> 00:04:01,020
然后接下来看一下

139
00:04:01,020 --> 00:04:02,980
我们课程的一个目标

140
00:04:02,980 --> 00:04:04,580
那么第一点呢

141
00:04:04,580 --> 00:04:06,100
我们会通过这门课程

142
00:04:06,100 --> 00:04:07,960
会去了解我们网络通信

143
00:04:07,960 --> 00:04:09,380
相关的一些基本的概念

144
00:04:09,380 --> 00:04:11,840
然后第二点就是我们就能掌握

145
00:04:11,840 --> 00:04:14,060
使用node.js来构建tcp服务

146
00:04:14,060 --> 00:04:15,780
然后同时掌握使用node

147
00:04:15,780 --> 00:04:17,420
来构建udp服务

148
00:04:17,420 --> 00:04:19,540
同时掌握使用Node来构建HTTP服务

149
00:04:19,540 --> 00:04:22,820
以及使用Node来掌握构建这种WebSoci的服务

150
00:04:22,820 --> 00:04:26,000
以及使用Node来构建HTTP-S服务

