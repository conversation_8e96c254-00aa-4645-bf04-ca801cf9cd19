1
00:00:00,000 --> 00:00:05,880
好 这节课呢 老师来教同学们一个在nodejs中去调试的方法

2
00:00:05,880 --> 00:00:10,080
因为咱们在写代码的时候 咱们在浏览器里面是不是他自带了一些调试工具啊

3
00:00:10,080 --> 00:00:10,240
 同学们

4
00:00:10,240 --> 00:00:15,880
比如说咱们f12或者comment+i会出来一个debug窗口console.log 对吧

5
00:00:15,880 --> 00:00:19,200
那在nodejs里面他有什么去调 他有什么调试方法呢

6
00:00:19,200 --> 00:00:22,780
可能有的同学会说 我使用console.log行不行

7
00:00:22,780 --> 00:00:24,840
那么我们就来看一下行不行

8
00:00:24,840 --> 00:00:28,420
我们为什么需要调试工具

9
00:00:30,000 --> 00:00:37,840
首先 先给大家演示一下我们怎么样通过console.log去调试 首先挖一个变量data等于空对象

10
00:00:37,840 --> 00:00:38,420
AAA-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-A-

11
00:00:38,420 --> 00:00:46,370
A A A B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B

12
00:00:46,370 --> 00:00:51,780
 B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B

13
00:00:51,780 --> 00:00:57,180
 B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B

14
00:00:57,180 --> 00:01:02,580
 B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B

15
00:01:02,580 --> 00:01:08,420
 B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B

16
00:01:08,420 --> 00:01:09,780
C方形CC看不到是什么

17
00:01:09,780 --> 00:01:11,500
这种打印方式好不好

18
00:01:11,500 --> 00:01:13,520
如果说有浏览器那样的断点调试

19
00:01:13,520 --> 00:01:14,620
是不是就完美了

20
00:01:14,620 --> 00:01:16,620
其实咱们在VSCode里面

21
00:01:16,620 --> 00:01:17,800
也有断点调试的方式

22
00:01:17,800 --> 00:01:20,080
这里也是咱们常用的编辑器

23
00:01:20,080 --> 00:01:21,200
不知道同学们用的是不是VSCode

24
00:01:21,200 --> 00:01:22,680
如果没有使用VSCode的同学

25
00:01:22,680 --> 00:01:24,000
老师建议同学们去下载一个

26
00:01:24,000 --> 00:01:24,900
它真的是非常好用

27
00:01:24,900 --> 00:01:27,140
它已经超过了市面上所有的编辑器

28
00:01:27,140 --> 00:01:28,820
比如说WebStorm Subline

29
00:01:28,820 --> 00:01:30,920
目前来看VSCode是最好的

30
00:01:30,920 --> 00:01:33,660
那么我们来看一下怎么样去debug

31
00:01:37,620 --> 00:01:41,840
首先在VSCode 它这里有一个小虫子 大家注意到 有一个小虫子

32
00:01:41,840 --> 00:01:46,860
我们只要 只要点击了它 断点就可以开始打

33
00:01:46,860 --> 00:01:49,220
首先我们把服务关一下

34
00:01:49,220 --> 00:01:54,860
好 这个时候什么也没发生 对吧 其实咱们在点击这一块区域的时候 它有一个小红点

35
00:01:54,860 --> 00:01:58,300
大家注意到没有 有一个小红点 它就是咱们需要打断点的代码

36
00:01:58,300 --> 00:02:02,220
假如说我们在这里 console.nogdata 这里打一个断点 走

37
00:02:03,900 --> 00:02:08,160
好 他说lunch program已经在运行 说明咱们刚才忘了关闭 先关掉 重新打

38
00:02:08,160 --> 00:02:12,420
好 大家看到没有 断点已经走到这里来了 已经到了第八行

39
00:02:12,420 --> 00:02:15,000
那么我们来看一下data是什么

40
00:02:15,000 --> 00:02:17,700
一个对象 ccc是一个方形

41
00:02:17,700 --> 00:02:23,420
它显示的是一个介绍函数 包括我们还可以看一下http 它的一个对象里面是什么

42
00:02:23,420 --> 00:02:26,700
所有一些圆形方法都在上面

43
00:02:26,700 --> 00:02:28,620
是不是方便了很多

44
00:02:28,620 --> 00:02:33,080
我们为什么需要它 可能有的同学说 我用concel.log不是很好吗

45
00:02:33,720 --> 00:02:35,200
那么如果说你想看HTTP

46
00:02:35,200 --> 00:02:36,440
老师打印出来给你看一下

47
00:02:36,440 --> 00:02:38,480
你就懂了我们为什么需要用断点调试工具

48
00:02:38,480 --> 00:02:41,320
好 那咱们把HTTP给打印出来看一下

49
00:02:41,320 --> 00:02:43,480
首先我来把它关掉

50
00:02:43,480 --> 00:02:44,520
这个按钮停止

51
00:02:44,520 --> 00:02:46,520
其实它是不是和浏览器的很像

52
00:02:46,520 --> 00:02:49,440
比如说这个箭头是不是咱们继续到下一段点

53
00:02:49,440 --> 00:02:51,000
比如说这里打一个

54
00:02:51,000 --> 00:02:52,040
是不是到下一段点

55
00:02:52,040 --> 00:02:53,280
这个跳过

56
00:02:53,280 --> 00:02:54,840
单步跳过

57
00:02:54,840 --> 00:02:55,720
F11

58
00:02:55,720 --> 00:02:56,520
下一步

59
00:02:56,520 --> 00:02:57,440
跳出

60
00:02:57,440 --> 00:02:58,320
重启 咱们重启

61
00:02:58,320 --> 00:02:59,400
对吧 又到第8号了

62
00:02:59,400 --> 00:03:02,040
其实和浏览器的断点调试是一模一样的

63
00:03:02,040 --> 00:03:03,320
好 先停一下

64
00:03:03,720 --> 00:03:09,060
那么我们就来通过concel.log的方式 看一下咱们http在控制塔里面打一串是什么

65
00:03:09,060 --> 00:03:14,360
大家看到没有 这样一串可读吗 是不是可读性很差

66
00:03:14,360 --> 00:03:21,360
所以说我们需要使用断点调试工具 vs code 它的这样一个工具就非常的好用

67
00:03:21,360 --> 00:03:29,160
那么假如说 假如我们现在调完了http 我们想去调试一下 这样一个刚才咱们的cluster

68
00:03:29,160 --> 00:03:31,040
 我们想看一下cpu 它有多少个核数

69
00:03:32,840 --> 00:03:35,260
是不是咱们可以在这里打上一个断点了

70
00:03:35,260 --> 00:03:40,080
好走 咱们看一下 走 好 没什么

71
00:03:40,080 --> 00:03:44,980
什么都没有出现 原因是什么呀 其实在VSCode里面 咱们需要去写一个配置文件

72
00:03:44,980 --> 00:03:47,020
叫做.VSCodeLaunch.js

73
00:03:47,020 --> 00:03:52,820
咱们看一下 它这里有一个属性 叫做Program Workspace 这个是你的工作目录

74
00:03:52,820 --> 00:03:56,920
也就是咱们断点调试的入口 刚才咱们是不是写了http.js

75
00:03:56,920 --> 00:04:01,000
但是咱们需要去调试httpcluster.js 所以呢 这里咱们改一下

76
00:04:01,560 --> 00:04:04,880
cluster.js 好 再来咱们再来打一个锻炼

77
00:04:04,880 --> 00:04:11,540
走 走 刚才没关掉 咱们关一下 重新来 走 大家看到没有 是不是已经过来了

78
00:04:11,540 --> 00:04:14,360
8个版 说明了我们cpu是8核

79
00:04:14,360 --> 00:04:18,200
包括你鼠标一上去的时候也可以看到 比如说你cast cluster

80
00:04:18,200 --> 00:04:22,040
它的一些属性上面有什么event fork方法 is master is worker属性

81
00:04:22,040 --> 00:04:25,360
对吧 那么包括 is master 它是q

82
00:04:25,360 --> 00:04:27,420
说明了是在咱们主线程里面去运行的

83
00:04:27,420 --> 00:04:30,480
这里就是在vscode里面去调试一个技巧

84
00:04:30,740 --> 00:04:33,280
那么lunch节省呢它有很多的一些属性

85
00:04:33,280 --> 00:04:35,580
包括配置的和ypack一样

86
00:04:35,580 --> 00:04:36,220
一直很复杂

87
00:04:36,220 --> 00:04:38,260
这里呢老师呢推荐同学们去看一下官方的文档

88
00:04:38,260 --> 00:04:41,060
这里呢就是VSCode它的一个文档debug

89
00:04:41,060 --> 00:04:42,680
这里呢老师还教同学们一个技巧

90
00:04:42,680 --> 00:04:43,500
如果说英文你看不懂

91
00:04:43,500 --> 00:04:45,200
在在谷歌浏览器下面

92
00:04:45,200 --> 00:04:47,280
你双击这里有一个翻译成中文

93
00:04:47,280 --> 00:04:48,900
这样去看可能会好一点

94
00:04:48,900 --> 00:04:51,200
如果说对VSCode的调试感兴趣的同学

95
00:04:51,200 --> 00:04:52,960
可以下去看一下VSCode它的一个文档

96
00:04:52,960 --> 00:04:56,520
好我们来回顾

97
00:04:56,520 --> 00:04:58,580
刚才是不是咱们讲的loads怎么样去调试

98
00:04:58,580 --> 00:04:59,540
地址呢在这里

99
00:04:59,540 --> 00:05:00,820
同学们可以自己去看一下

100
00:05:00,820 --> 00:05:02,240
为什么要用load.js

101
00:05:02,240 --> 00:05:03,880
我们为什么需要去用VSCode去调试

102
00:05:03,880 --> 00:05:04,820
是不是因为我们把

103
00:05:04,820 --> 00:05:06,700
这样打印出来的内容很恶心

104
00:05:06,700 --> 00:05:08,360
如果说有工具帮助我们去

105
00:05:08,360 --> 00:05:09,860
查看一些信息

106
00:05:09,860 --> 00:05:10,860
是不是会提升我们的效率

107
00:05:10,860 --> 00:05:12,740
这就是咱们需要去使用load.js

108
00:05:12,740 --> 00:05:13,580
调试

109
00:05:13,580 --> 00:05:14,720
一个原因

110
00:05:14,720 --> 00:05:16,260
好 这里就是这节课的内容

