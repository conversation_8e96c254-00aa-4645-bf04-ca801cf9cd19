1
00:00:00,000 --> 00:00:01,800
好 这节课我们来看一下

2
00:00:01,800 --> 00:00:02,820
过期时间

3
00:00:02,820 --> 00:00:04,200
提到过期时间

4
00:00:04,200 --> 00:00:05,300
大家是不是可以想到

5
00:00:05,300 --> 00:00:06,640
比如说我们浏览器里面的Cookie

6
00:00:06,640 --> 00:00:08,120
它是不是有过期时间

7
00:00:08,120 --> 00:00:09,960
包括了我们浏览器里面的缓存

8
00:00:09,960 --> 00:00:10,680
分为什么

9
00:00:10,680 --> 00:00:11,800
强缓存和协商缓存

10
00:00:11,800 --> 00:00:13,500
它里面也有过期时间的一个概念

11
00:00:13,500 --> 00:00:15,460
那么Redis里面它的过期时间是什么呢

12
00:00:15,460 --> 00:00:16,340
实际上呢

13
00:00:16,340 --> 00:00:18,460
在Redis里面它的过期时间

14
00:00:18,460 --> 00:00:20,160
和咱们的Cookie过期时间

15
00:00:20,160 --> 00:00:21,760
和我们的一个缓存过期时间有一点

16
00:00:21,760 --> 00:00:22,360
类似

17
00:00:22,360 --> 00:00:23,000
你比如说

18
00:00:23,000 --> 00:00:24,040
我们在实际的开发中

19
00:00:24,040 --> 00:00:25,600
经常会遇到一些有效的数据

20
00:00:25,600 --> 00:00:26,760
比如咱们的优惠活动

21
00:00:26,760 --> 00:00:27,660
缓存或者验证

22
00:00:30,000 --> 00:00:34,200
那么在redis里面是怎么样去做到的呢 实际上他需要使用到一个关键字叫做expire

23
00:00:34,200 --> 00:00:40,200
那么expire他的命令其实非常简单 首先执行expire命令 然后呢 这段后面那就是他的时间

24
00:00:40,200 --> 00:00:41,560
 比如说你需要去设定他多少

25
00:00:41,560 --> 00:00:44,200
多长时间过期 比如说 比如说我们

26
00:00:44,200 --> 00:00:45,280
我们去

27
00:00:45,280 --> 00:00:49,640
实现这样一个 假如说我们去set的一个session 咱们在浏览器里面是不是经常会去

28
00:00:49,640 --> 00:00:51,400
 比如说服务团会给我们去写入一些session

29
00:00:51,400 --> 00:00:52,400
那么我们来看一下

30
00:00:52,400 --> 00:00:53,640
如果说我们

31
00:00:53,640 --> 00:00:59,080
如果我们要去对这个session去做一些过去时间怎么样去做 比如说我们去set的一个session

32
00:00:59,080 --> 00:01:00,000
 id 我们随便写

33
00:01:00,000 --> 00:01:03,260
比如说我们Session的值为ABC

34
00:01:03,260 --> 00:01:07,140
一般我们的Session的值是一些加密信息来代表你是否登录

35
00:01:07,140 --> 00:01:10,040
好 我们现在去set一个Session这样的一个字段

36
00:01:10,040 --> 00:01:12,180
那么我们需要给它去设置过期时间

37
00:01:12,180 --> 00:01:13,920
你比如说我们现在设置值

38
00:01:13,920 --> 00:01:15,580
它是不是永久存在的

39
00:01:15,580 --> 00:01:16,760
在Reduce里面它不会被删除

40
00:01:16,760 --> 00:01:19,240
那么你如果说需要它一段时间之后过期

41
00:01:19,240 --> 00:01:21,160
也就是被删除

42
00:01:21,160 --> 00:01:23,240
那么我们可以用打Expile这样一个字段

43
00:01:23,240 --> 00:01:25,740
Expile Reduce所提供的方法

44
00:01:25,740 --> 00:01:28,760
Expile我们的Session999

45
00:01:28,760 --> 00:01:31,000
比如说我们设置5秒过期

46
00:01:31,000 --> 00:01:34,560
比如说我们现在就get session999

47
00:01:34,560 --> 00:01:35,220
它还在ABC

48
00:01:35,220 --> 00:01:37,660
现在大概已经超过了5秒

49
00:01:37,660 --> 00:01:41,540
我们再来看一下它还在不在get session

50
00:01:41,540 --> 00:01:43,020
999

51
00:01:43,020 --> 00:01:44,880
大家可以看到它已经消失了

52
00:01:44,880 --> 00:01:46,920
这里就是expile一个简单的使用

53
00:01:46,920 --> 00:01:49,320
那么我们再来看一下

54
00:01:49,320 --> 00:01:51,500
你如果说想知道一个键

55
00:01:51,500 --> 00:01:52,880
它还有多久的时间会被删除

56
00:01:52,880 --> 00:01:54,740
也可以使用TTL这样一个命令

57
00:01:54,740 --> 00:01:56,580
这个命令是做什么的呢

58
00:01:56,580 --> 00:01:58,640
你如果说你有时候你去存储一个session

59
00:01:58,640 --> 00:02:01,700
但是呢你想知道他还有多久过期你就可以用到这个命令

60
00:02:01,700 --> 00:02:02,500
我们来测试一下

61
00:02:02,500 --> 00:02:04,200
比如说我们再去set一下

62
00:02:04,200 --> 00:02:05,380
咱们的一个

63
00:02:05,380 --> 00:02:08,560
session999

64
00:02:08,560 --> 00:02:09,900
我们给他一个过期时间100秒

65
00:02:09,900 --> 00:02:11,360
好

66
00:02:11,360 --> 00:02:13,860
5 4 3 2 1

67
00:02:13,860 --> 00:02:15,320
好我们此时大概过了5秒

68
00:02:15,320 --> 00:02:16,800
我们来看一下

69
00:02:16,800 --> 00:02:18,860
他还有多久

70
00:02:18,860 --> 00:02:21,840
session999-1

71
00:02:21,840 --> 00:02:25,220
-1

72
00:02:25,220 --> 00:02:27,740
为什么是-1

73
00:02:27,740 --> 00:02:33,880
好 我再来看一下 比如说我们去set一个session888

74
00:02:33,880 --> 00:02:37,460
我们给他一万秒的过期时间 然后呢

75
00:02:37,460 --> 00:02:42,720
哦 我知道了 我们刚才是不是set的999100 但是没有给他expire 对吧

76
00:02:42,720 --> 00:02:49,240
所以我们重新来一下 比如说我们去set session999 过期时间了 给他一个值abc

77
00:02:49,240 --> 00:02:52,320
然后呢 此时我们是不是通过expire给他一个过期时间

78
00:02:53,340 --> 00:02:55,620
3信999过期时间了100秒

79
00:02:55,620 --> 00:02:59,340
好 我们此时通过TTL来查一下咱们的3信999它还有多久过期

80
00:02:59,340 --> 00:03:01,480
大家可以看到它会返回一个94

81
00:03:01,480 --> 00:03:04,280
也就是说说明我们的3信它还有94秒过期

82
00:03:04,280 --> 00:03:05,520
那么呢

83
00:03:05,520 --> 00:03:09,660
它的返回值TTL它一共有三种返回值

84
00:03:09,660 --> 00:03:11,420
一种呢就是它还有多久过期

85
00:03:11,420 --> 00:03:13,980
然后呢还有两种刚才大家可以看到我们是不是已经

86
00:03:13,980 --> 00:03:15,500
返回一个-1

87
00:03:15,500 --> 00:03:17,360
那么其实呢它还有一种返回值就是

88
00:03:17,360 --> 00:03:19,020
2

89
00:03:19,020 --> 00:03:21,240
那么什么情况下会返回-1或者

90
00:03:21,240 --> 00:03:23,680
当键不存在时

91
00:03:23,680 --> 00:03:25,120
TTL就会返回for

92
00:03:25,120 --> 00:03:26,740
那么如果说

93
00:03:26,740 --> 00:03:28,680
那么如果说

94
00:03:28,680 --> 00:03:31,600
你没有为这个键设置过期

95
00:03:31,600 --> 00:03:32,920
也就是说它是永久存在的

96
00:03:32,920 --> 00:03:33,900
它就会返回-1

97
00:03:33,900 --> 00:03:34,900
那么我们就来看一下

98
00:03:34,900 --> 00:03:36,840
首先我们去

99
00:03:36,840 --> 00:03:39,460
重新设置一个新的键

100
00:03:39,460 --> 00:03:40,620
比如说我们去set session

101
00:03:40,620 --> 00:03:47,280
我们来给它去设置一个expile

102
00:03:47,280 --> 00:03:51,100
它是不是马上就过期了

103
00:03:51,100 --> 00:03:53,420
对吧 那么我们来看一下TTL

104
00:03:53,420 --> 00:03:56,380
Session888 它会返回几 返回几

105
00:03:56,380 --> 00:04:01,980
是不是返回-2啊 因为如果说你已经过期了 说明键会被拴除 所以呢就会返回-2

106
00:04:01,980 --> 00:04:03,340
我们来看一下 是不是-2

107
00:04:03,340 --> 00:04:06,740
那么-1的情况 是不是 你没有给它设置过期时间 它就会返回-1

108
00:04:06,740 --> 00:04:08,620
你比如说 我们去SET的一个

109
00:04:08,620 --> 00:04:11,620
Session777

110
00:04:11,620 --> 00:04:13,660
没有给值 比如说再给个ABC

111
00:04:13,660 --> 00:04:17,660
好 我们没有给它设置过期时间吧 我们去TTL

112
00:04:17,660 --> 00:04:20,860
Session777

113
00:04:20,860 --> 00:04:21,800
大概看到反为-1

114
00:04:21,800 --> 00:04:22,580
好

115
00:04:22,580 --> 00:04:24,560
那么如果说还有一种情况

116
00:04:24,560 --> 00:04:26,240
你如果想取消键的过期时间

117
00:04:26,240 --> 00:04:28,560
你如果想取消它的过期时间

118
00:04:28,560 --> 00:04:28,980
你比如说

119
00:04:28,980 --> 00:04:31,120
我们刚才是不是给Session999设置了一个过期时间

120
00:04:31,120 --> 00:04:32,600
但是呢你现在突然反悔了

121
00:04:32,600 --> 00:04:33,260
我不想给它

122
00:04:33,260 --> 00:04:33,940
我不想让它过期

123
00:04:33,940 --> 00:04:34,360
怎么办

124
00:04:34,360 --> 00:04:35,780
这里呢我们可以用到

125
00:04:35,780 --> 00:04:36,820
Persist这一个命令

126
00:04:36,820 --> 00:04:38,040
Persist它的意思是什么

127
00:04:38,040 --> 00:04:39,040
是不是持久

128
00:04:39,040 --> 00:04:40,800
你比如说我们去Set了一个

129
00:04:40,800 --> 00:04:43,840
Session999

130
00:04:43,840 --> 00:04:44,600
我们给它1000秒

131
00:04:44,600 --> 00:04:45,880
我们来TTL看一下

132
00:04:45,880 --> 00:04:47,180
它还多久过期

133
00:04:47,180 --> 00:04:48,900
大家可以看到

134
00:04:48,900 --> 00:04:50,940
不好意思

135
00:04:50,940 --> 00:04:52,940
老是搞混

136
00:04:52,940 --> 00:04:55,420
老是把set和expile命令使用混

137
00:04:55,420 --> 00:04:57,080
比如说我们去

138
00:04:57,080 --> 00:04:58,380
3型999给它一个值

139
00:04:58,380 --> 00:04:58,900
abc

140
00:04:58,900 --> 00:05:00,180
然后我们再去expile

141
00:05:00,180 --> 00:05:04,880
expile3型999给它一个1000秒

142
00:05:04,880 --> 00:05:06,360
1000秒是不是十分多钟啊

143
00:05:06,360 --> 00:05:07,880
那么十分多钟之内它不会过期

144
00:05:07,880 --> 00:05:08,980
我们来看一下ttl

145
00:05:08,980 --> 00:05:10,000
它是什么

146
00:05:10,000 --> 00:05:11,060
还有993秒

147
00:05:11,060 --> 00:05:12,640
那么如果说我们想取消它的过期时间

148
00:05:12,640 --> 00:05:14,740
我们就可以利用perseize的这个命令

149
00:05:14,740 --> 00:05:15,740
我们去perseize

150
00:05:15,740 --> 00:05:20,860
我们去persease的咱们的session999

151
00:05:20,860 --> 00:05:23,420
我们再来看一下他还有多久过期

152
00:05:23,420 --> 00:05:26,740
大家可以看到返回-1

153
00:05:26,740 --> 00:05:27,520
-1是什么

154
00:05:27,520 --> 00:05:30,580
-1是不是没有设置过期时间说明他会永久存在

155
00:05:30,580 --> 00:05:32,120
好

156
00:05:32,120 --> 00:05:34,680
还有一种情况我们也可以去取消过期时间

157
00:05:34,680 --> 00:05:35,200
什么呢

158
00:05:35,200 --> 00:05:37,240
你除了persease的命令之外

159
00:05:37,240 --> 00:05:38,780
还可以你直接去set他

160
00:05:38,780 --> 00:05:40,820
你就可以为他清除过期时间什么意思

161
00:05:40,820 --> 00:05:41,860
什么意思

162
00:05:41,860 --> 00:05:42,880
比如说

163
00:05:42,880 --> 00:05:44,420
比如说我们现在

164
00:05:45,440 --> 00:05:47,920
还是为session999去给他一个过期时间

165
00:05:47,920 --> 00:05:48,700
1000秒

166
00:05:48,700 --> 00:05:50,840
我们去查一下

167
00:05:50,840 --> 00:05:54,880
他现在咱们的session999是不是在1000秒之后会过期

168
00:05:54,880 --> 00:05:57,480
那么我们直接去set他

169
00:05:57,480 --> 00:05:58,800
也就是说我们给他重新复制

170
00:05:58,800 --> 00:06:00,940
比如说我们去重新给他复制为ab

171
00:06:00,940 --> 00:06:02,280
那么我们再来查一下

172
00:06:02,280 --> 00:06:04,900
咱们的session999他的一个过期时间是什么

173
00:06:04,900 --> 00:06:06,180
大家可以看到

174
00:06:06,180 --> 00:06:10,420
复义说明了我们不仅可以通过perseize的去对他进行持久化

175
00:06:10,420 --> 00:06:13,080
你重新去set也可以取消他的一个过期时间

176
00:06:13,080 --> 00:06:14,740
那么这里呢

177
00:06:14,740 --> 00:06:15,820
希望大家有点需要注意

178
00:06:15,820 --> 00:06:16,660
需要注意

179
00:06:16,660 --> 00:06:22,300
我们只有set操作会影响它的过期时间

180
00:06:22,300 --> 00:06:22,740
怎么影响

181
00:06:22,740 --> 00:06:24,580
是不是会让它的过期时间取消

182
00:06:24,580 --> 00:06:25,420
也就是永久存在

183
00:06:25,420 --> 00:06:26,540
但是如果说你使用

184
00:06:26,540 --> 00:06:27,460
类似比如说imcr

185
00:06:27,460 --> 00:06:28,100
Airpush

186
00:06:28,100 --> 00:06:29,640
H3RM

187
00:06:29,640 --> 00:06:31,680
这样的方法去操作我们的数据

188
00:06:31,680 --> 00:06:33,920
它是不会影响过期时间的

189
00:06:33,920 --> 00:06:34,440
好

190
00:06:34,440 --> 00:06:36,280
而且

191
00:06:36,280 --> 00:06:38,320
expire的命运

192
00:06:38,320 --> 00:06:40,540
它的参数必须是整数

193
00:06:40,540 --> 00:06:41,180
什么意思

194
00:06:41,180 --> 00:06:41,900
也就是说你刚才

195
00:06:41,900 --> 00:06:42,820
我们是不是给session999

196
00:06:42,820 --> 00:06:44,160
去设置了过期时间为1000秒

197
00:06:44,160 --> 00:06:47,420
你如果说想给它1000.5秒

198
00:06:47,420 --> 00:06:48,320
是不是不可以

199
00:06:48,320 --> 00:06:49,540
它的最小单位就是1秒

200
00:06:49,540 --> 00:06:50,400
你不能给它一个半秒

201
00:06:50,400 --> 00:06:52,740
那么如果说你想更精确的去控制过期时间

202
00:06:52,740 --> 00:06:54,380
这里呢还有一个其他的命令

203
00:06:54,380 --> 00:06:56,660
叫做PXPIL

204
00:06:56,660 --> 00:06:57,820
它是做什么的呢

205
00:06:57,820 --> 00:07:00,380
它只是说把你的最小单位改为了毫秒

206
00:07:00,380 --> 00:07:02,660
你比如说你去PXPIL

207
00:07:02,660 --> 00:07:04,940
一个字段1000和XPIL

208
00:07:04,940 --> 00:07:06,280
1秒是一样的

209
00:07:06,280 --> 00:07:07,620
它们只是一个换算关系

210
00:07:07,620 --> 00:07:08,900
所以说呢

211
00:07:08,900 --> 00:07:12,000
所以说我们如果说想更精确的去控制

212
00:07:12,000 --> 00:07:13,240
就可以去利用PXPIL

213
00:07:13,240 --> 00:07:14,840
那么呢

214
00:07:14,840 --> 00:07:16,420
这里呢还有一点

215
00:07:16,420 --> 00:07:18,100
希望大家去注意

216
00:07:18,100 --> 00:07:19,680
你比如说我们用卧起

217
00:07:19,680 --> 00:07:21,400
大家还记得咱们事物中的卧起吗

218
00:07:21,400 --> 00:07:23,420
它是不是可以去监测我们一个字段

219
00:07:23,420 --> 00:07:24,800
但是如果说

220
00:07:24,800 --> 00:07:26,760
你监测一个拥有过期时间的键

221
00:07:26,760 --> 00:07:28,120
你比如说咱们通过卧起

222
00:07:28,120 --> 00:07:29,600
去监测了咱们的Session999

223
00:07:29,600 --> 00:07:31,060
那么如果说Session999

224
00:07:31,060 --> 00:07:33,320
它被设置过期时间为千

225
00:07:33,320 --> 00:07:34,140
那么它过期之后

226
00:07:34,140 --> 00:07:35,720
我们的卧起能够监测到它的变化吗

227
00:07:35,720 --> 00:07:36,960
其实是不可以的

228
00:07:36,960 --> 00:07:38,060
这里希望同学们去注意

229
00:07:38,060 --> 00:07:39,560
而且呢这里还有两个

230
00:07:39,560 --> 00:07:41,060
不太常用的一个命令

231
00:07:41,060 --> 00:07:47,560
Xpile和XpileAT和PXpileAT

232
00:07:47,560 --> 00:07:49,120
那么它是做什么用的呢

233
00:07:49,120 --> 00:07:50,540
其实我们刚才

234
00:07:50,540 --> 00:07:51,500
我们刚才

235
00:07:51,500 --> 00:07:52,820
比如说我们去设计一个Xpile

236
00:07:52,820 --> 00:07:53,880
设计999 1000

237
00:07:53,880 --> 00:07:54,980
那么这个1000

238
00:07:54,980 --> 00:07:57,140
它是相对什么时间过期

239
00:07:57,140 --> 00:07:58,120
是不是咱们执行

240
00:07:58,120 --> 00:07:58,860
是不是咱们相对

241
00:07:58,860 --> 00:08:00,080
咱们执行这条命令的时间

242
00:08:00,080 --> 00:08:01,040
去过期啊

243
00:08:01,040 --> 00:08:02,600
比如说咱们执行的这一瞬间

244
00:08:02,600 --> 00:08:03,820
1000秒之后

245
00:08:03,820 --> 00:08:04,560
这个资料就会过期

246
00:08:04,560 --> 00:08:06,320
但是我们还有另外一种方式

247
00:08:06,320 --> 00:08:07,340
可以去设计它的过期时间

248
00:08:07,340 --> 00:08:09,620
也就是我们的UNIX时间

249
00:08:09,620 --> 00:08:10,600
什么是UNIX时间

250
00:08:10,600 --> 00:08:11,560
同学们

251
00:08:11,560 --> 00:08:13,120
也就是咱们

252
00:08:13,120 --> 00:08:14,100
是不是时间戳呀

253
00:08:14,100 --> 00:08:16,180
时间戳对吧

254
00:08:16,180 --> 00:08:17,680
那么我们既然是怎么样去获取时间戳

255
00:08:17,680 --> 00:08:19,100
是不是new data.value

256
00:08:19,100 --> 00:08:20,060
好

257
00:08:20,060 --> 00:08:21,060
实际上呢

258
00:08:21,060 --> 00:08:23,580
你如果说想通过时间戳去设置它的过期时间

259
00:08:23,580 --> 00:08:24,640
你就直接可以通过

260
00:08:24,640 --> 00:08:25,780
XpireAT

261
00:08:25,780 --> 00:08:26,340
比如说一个字段

262
00:08:26,340 --> 00:08:27,840
后面呢就是咱们的unix时间戳

263
00:08:27,840 --> 00:08:29,520
咱们在js里面去操作时间的时候

264
00:08:29,520 --> 00:08:30,560
是不是经常会使用到时间戳

265
00:08:30,560 --> 00:08:33,560
那么时间戳其实它也是比较常见的一种用法

266
00:08:33,560 --> 00:08:35,460
也比如说我们浏览器去做协商缓存的时候

267
00:08:35,460 --> 00:08:37,120
需要用什么字段去对比

268
00:08:37,120 --> 00:08:39,140
是不是也是需要和复端的Xpire字段

269
00:08:39,140 --> 00:08:42,800
其实这块大家可以去结合咱们浏览器里面的强缓存和协商缓存

270
00:08:42,800 --> 00:08:45,920
去理解一下它unix时间它的一个应用场景是什么

271
00:08:45,920 --> 00:08:51,020
好 这里呢就是咱们这节课对expire这样一个命令的介绍

272
00:08:51,020 --> 00:08:55,300
我们一起来回顾一下我们的expire命令

273
00:08:55,300 --> 00:09:03,700
好 我们刚才我们刚才是不是讲解了过期时间

274
00:09:03,700 --> 00:09:05,680
那么过期时间它的命令是什么呀

275
00:09:05,680 --> 00:09:08,060
它最常见的命令是不是expire

276
00:09:08,060 --> 00:09:09,480
Xpile对应的是什么

277
00:09:09,480 --> 00:09:10,480
PXpile

278
00:09:10,480 --> 00:09:11,780
它们有什么区别

279
00:09:11,780 --> 00:09:12,620
是不是一个是秒

280
00:09:12,620 --> 00:09:13,320
一个是毫秒

281
00:09:13,320 --> 00:09:16,940
秒和毫秒

282
00:09:16,940 --> 00:09:21,360
那么我们设置过期时间的时候

283
00:09:21,360 --> 00:09:22,920
Xpile它是不是有返回值

284
00:09:22,920 --> 00:09:23,980
是不是有返回值

285
00:09:23,980 --> 00:09:26,900
正常的返回值

286
00:09:26,900 --> 00:09:27,260
是什么

287
00:09:27,260 --> 00:09:28,640
正常的返回值

288
00:09:28,640 --> 00:09:30,860
是不是OK

289
00:09:30,860 --> 00:09:31,920
返回值为OK

290
00:09:31,920 --> 00:09:33,920
那么如果说我们想去查询

291
00:09:33,920 --> 00:09:35,140
这个时间还多久过期

292
00:09:35,140 --> 00:09:36,560
是不是要用到TTL站一个命令

293
00:09:36,560 --> 00:09:38,040
那么TTL它有哪些返回值呢

294
00:09:38,040 --> 00:09:40,580
TTL它是不是会

295
00:09:40,580 --> 00:09:41,500
首先会返回你

296
00:09:41,500 --> 00:09:43,360
剩余多久过期

297
00:09:43,360 --> 00:09:44,580
然后呢还有-1

298
00:09:44,580 --> 00:09:45,460
-1是什么意思

299
00:09:45,460 --> 00:09:46,820
-1是不是说明

300
00:09:46,820 --> 00:09:48,720
它是永久存在的

301
00:09:48,720 --> 00:09:49,880
你没有给它设置过期时间

302
00:09:49,880 --> 00:09:51,340
永久存在

303
00:09:51,340 --> 00:09:51,780
-2呢

304
00:09:51,780 --> 00:09:52,720
-2是什么意思

305
00:09:52,720 --> 00:09:53,860
-2是不是已经过期

306
00:09:53,860 --> 00:09:55,520
或者你这个字段不存在

307
00:09:55,520 --> 00:10:01,060
那么还有一种

308
00:10:01,060 --> 00:10:02,220
咱们刚才还讲了什么

309
00:10:02,220 --> 00:10:04,780
是不是可以用unix时间呢

310
00:10:04,780 --> 00:10:07,940
咱们的expile和pxpile

311
00:10:07,940 --> 00:10:08,840
是不是用了相对时间

312
00:10:08,840 --> 00:10:09,980
那么我们的unix时间

313
00:10:09,980 --> 00:10:10,720
它的命令是什么

314
00:10:10,720 --> 00:10:13,180
它的一个命令

315
00:10:13,180 --> 00:10:13,820
是不是

316
00:10:13,820 --> 00:10:17,600
expile at和什么

317
00:10:17,600 --> 00:10:19,180
p expile

318
00:10:19,180 --> 00:10:21,720
at他们的区别是什么

319
00:10:21,720 --> 00:10:22,600
是不是也是一个秒

320
00:10:22,600 --> 00:10:24,340
和毫秒一个区别

321
00:10:24,340 --> 00:10:24,800
好

322
00:10:24,800 --> 00:10:26,100
这里就是我们过期时间

323
00:10:26,100 --> 00:10:26,940
这节课的内容

