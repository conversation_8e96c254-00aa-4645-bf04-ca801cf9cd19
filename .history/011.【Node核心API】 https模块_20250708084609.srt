1
00:00:00,000 --> 00:00:03,200
好 这节课我们就来看一下我们的HTPS模块

2
00:00:03,200 --> 00:00:08,320
那么HTPS呢 其实它和我们的HTPS一样 非常的枯燥无味

3
00:00:08,320 --> 00:00:10,920
而且我们看一下它是如何去create一个7的

4
00:00:10,920 --> 00:00:14,540
首先require一个HTPS 然后引入一个FS模块

5
00:00:14,540 --> 00:00:18,560
最后给它一个K 也就是我们的公钥和私钥

6
00:00:18,560 --> 00:00:20,760
然后再去进行create7绑定一个档口

7
00:00:20,760 --> 00:00:24,480
大家可以看到是不是和我们的HTPS二战一个模块去创建咱们的一个Sever

8
00:00:24,480 --> 00:00:25,840
基本上是一致的

9
00:00:25,840 --> 00:00:29,420
所以说我们HTPS咱们的重点就不放在我们的文档的一个阅读

10
00:00:29,420 --> 00:00:30,800
以及它的服务的一个创建

11
00:00:30,800 --> 00:00:31,500
我们就不去演示

12
00:00:31,500 --> 00:00:33,040
我们这几个重点在哪里呢

13
00:00:33,040 --> 00:00:35,000
我们这几个重点呢

14
00:00:35,000 --> 00:00:36,940
主要就是在于我们HTTP模块的

15
00:00:36,940 --> 00:00:39,280
HTTPS模块它的一个加密过程

16
00:00:39,280 --> 00:00:41,500
因为也是面试中会经常考到的一个点

17
00:00:41,500 --> 00:00:42,280
好 我们就来看一下

18
00:00:42,280 --> 00:00:43,720
我们都知道

19
00:00:43,720 --> 00:00:46,280
HTTP请求呢都是明文传输的

20
00:00:46,280 --> 00:00:47,140
那么所谓的明文

21
00:00:47,140 --> 00:00:48,700
指的就是没有经过加密的信息

22
00:00:48,700 --> 00:00:50,820
那如果HTTP请求被黑客拦截

23
00:00:50,820 --> 00:00:52,560
并且呢里面含有银行卡密码

24
00:00:52,560 --> 00:00:53,880
或者呢一些敏感数据的话呢

25
00:00:53,880 --> 00:00:54,640
就会非常的危险

26
00:00:54,640 --> 00:00:55,840
为了解决这样一个问题

27
00:00:55,840 --> 00:00:59,380
NetSpace公司制定了HTPS的协议

28
00:00:59,380 --> 00:01:01,720
那HTPS可以将数据加密传输

29
00:01:01,720 --> 00:01:04,140
那么也就是传输的是密文

30
00:01:04,140 --> 00:01:07,740
即使在黑客在传输过程中拦截的数据也没有办法去破解

31
00:01:07,740 --> 00:01:10,680
这里就保证了我们的一个网络通信的安全

32
00:01:10,680 --> 00:01:14,940
好 那它是如何去保证我们网络通信的安全的呢

33
00:01:14,940 --> 00:01:17,800
这里我们就首先得了解一下我们密码学的一个基础

34
00:01:17,800 --> 00:01:23,540
首先名文 名文指的就是我们没有被加密过的一个原始数据

35
00:01:23,540 --> 00:01:25,380
比如说我给你发送一条消息

36
00:01:25,380 --> 00:01:25,780
好了我的

37
00:01:25,780 --> 00:01:27,040
那么我们网络中传输了

38
00:01:27,040 --> 00:01:28,440
那么就是好了我的在那一个名文

39
00:01:28,440 --> 00:01:30,540
那么什么是密文呢

40
00:01:30,540 --> 00:01:32,620
密文就是名文被某种加密算法

41
00:01:32,620 --> 00:01:34,480
加密之后会变为一个密文

42
00:01:34,480 --> 00:01:35,920
那么我们简单的理解一下

43
00:01:35,920 --> 00:01:36,860
密文

44
00:01:36,860 --> 00:01:38,380
其实非常好理解

45
00:01:38,380 --> 00:01:40,160
比如说我跟你通信

46
00:01:40,160 --> 00:01:40,760
对吧

47
00:01:40,760 --> 00:01:42,700
我说我要发给你一个数字

48
00:01:42,700 --> 00:01:43,740
发给你一个100

49
00:01:43,740 --> 00:01:46,020
但是我们为了防止

50
00:01:46,020 --> 00:01:47,540
别人给我们进行一个窃听

51
00:01:47,540 --> 00:01:48,100
对吧

52
00:01:48,100 --> 00:01:49,540
我们私下我们就偷偷约定

53
00:01:49,540 --> 00:01:50,840
我们呢

54
00:01:50,840 --> 00:01:52,100
我只要跟你说一个数字

55
00:01:52,100 --> 00:01:53,000
你就把它给注意2

56
00:01:53,000 --> 00:01:55,000
这里就是我们相对说我们的密钥对吧

57
00:01:55,000 --> 00:01:56,000
所以说我就会告诉你

58
00:01:56,000 --> 00:01:59,000
我发给了你一个200对吧

59
00:01:59,000 --> 00:02:01,000
然后此时你就心理神会

60
00:02:01,000 --> 00:02:02,000
其实你要给我说的是100

61
00:02:02,000 --> 00:02:03,000
因为我们是不是私下约定过呀

62
00:02:03,000 --> 00:02:04,000
咱们呢

63
00:02:04,000 --> 00:02:05,000
密钥就是把这个数字除为2

64
00:02:05,000 --> 00:02:07,000
其实这里就是我们的密钥

65
00:02:07,000 --> 00:02:08,000
当然现实中的一个加密

66
00:02:08,000 --> 00:02:09,000
肯定比我这样一种加密方式

67
00:02:09,000 --> 00:02:10,000
会复杂非常非常多

68
00:02:10,000 --> 00:02:11,000
那么密钥呢

69
00:02:11,000 --> 00:02:12,000
我们刚才其实解释过了

70
00:02:12,000 --> 00:02:14,000
就是咱们之间的一个约定对吧

71
00:02:14,000 --> 00:02:15,000
也是一种在我们的计算机里面

72
00:02:15,000 --> 00:02:17,000
它就是一种算法

73
00:02:17,000 --> 00:02:18,000
好

74
00:02:18,000 --> 00:02:19,000
接下来我们来看一下

75
00:02:19,000 --> 00:02:20,000
对称加密和非对称加密

76
00:02:20,000 --> 00:02:20,760
和非对称加密

77
00:02:20,760 --> 00:02:22,300
那么对称加密和非对称加密

78
00:02:22,300 --> 00:02:23,320
是怎么回事呢

79
00:02:23,320 --> 00:02:23,840
我们来看一下

80
00:02:23,840 --> 00:02:26,140
那么对称加密的他有叫做适要加密

81
00:02:26,140 --> 00:02:28,960
即信息的发送方和接收方使用同一个密钥区

82
00:02:28,960 --> 00:02:30,240
加密和解密数据

83
00:02:30,240 --> 00:02:32,540
对称加密的特点是算法公开

84
00:02:32,540 --> 00:02:34,080
加密和解密速度快

85
00:02:34,080 --> 00:02:36,900
而且适用于对大数据量进行加密

86
00:02:36,900 --> 00:02:39,460
常见的对称加密算法就有这么几种

87
00:02:39,460 --> 00:02:41,240
好怎么理解对称加密

88
00:02:41,240 --> 00:02:43,800
我来举个例子

89
00:02:43,800 --> 00:02:47,400
假如说

90
00:02:47,400 --> 00:02:49,180
我们两个要进行一个

91
00:02:49,700 --> 00:02:50,700
通信对吧

92
00:02:50,700 --> 00:02:51,700
二行通信

93
00:02:51,700 --> 00:02:54,700
然后我们两边的都有一把钥匙

94
00:02:54,700 --> 00:02:56,700
那么假设我们的钥匙

95
00:02:56,700 --> 00:03:00,700
假设我们的钥匙都是一个三角形

96
00:03:00,700 --> 00:03:03,700
我使用三角形把信息发送给你

97
00:03:03,700 --> 00:03:06,700
你就使用咱们的同样的一把钥匙把它给解开

98
00:03:06,700 --> 00:03:09,700
然后再来回复给我一些信息

99
00:03:09,700 --> 00:03:11,700
那么我得到信息之后我又使用同一把钥匙

100
00:03:11,700 --> 00:03:13,700
咱们同样的钥匙把它给解开

101
00:03:13,700 --> 00:03:15,700
这里就是我们的一个对称加密

102
00:03:15,700 --> 00:03:17,700
那么既然了解了什么是对称加密

103
00:03:17,700 --> 00:03:19,700
我们就来了解一下什么是非对层加密

104
00:03:19,700 --> 00:03:23,700
咱们的加密是不是同样会两个人

105
00:03:23,700 --> 00:03:26,700
然后我使用一把钥匙

106
00:03:26,700 --> 00:03:28,700
我的钥匙是一个三角形

107
00:03:28,700 --> 00:03:30,700
你的钥匙是一个正方形

108
00:03:30,700 --> 00:03:32,700
我首先使用三角形

109
00:03:32,700 --> 00:03:34,700
把加密后的信息发送给你

110
00:03:34,700 --> 00:03:36,700
你使用你的一个正方形的钥匙

111
00:03:36,700 --> 00:03:39,700
把我的传输给你的一个密文给解开

112
00:03:39,700 --> 00:03:41,700
同样的

113
00:03:41,700 --> 00:03:46,700
你使用三角形对你的密文去进行

114
00:03:46,700 --> 00:03:49,380
你使用正方形对你的密文来进行加密

115
00:03:49,380 --> 00:03:52,080
然后我呢就使用我的三角形在那一个钥匙

116
00:03:52,080 --> 00:03:53,500
把你发出给我的信息给解开

117
00:03:53,500 --> 00:03:56,560
这也其实就是我们的一个非对称加密最简单的一个理解

118
00:03:56,560 --> 00:03:58,980
那么我们简单的总结一下就是对称加密就是

119
00:03:58,980 --> 00:04:01,240
我和你都使用同一把钥匙去进行加密和解密

120
00:04:01,240 --> 00:04:02,700
那么非对称加密就是

121
00:04:02,700 --> 00:04:05,360
我使用三角形加密你使用正方形解密

122
00:04:05,360 --> 00:04:07,940
那么你使用正方形加密我能使用三角形解密

123
00:04:07,940 --> 00:04:10,940
那么有的同学可能会问

124
00:04:10,940 --> 00:04:13,680
你使用三角形在我们非对称加密的情况下

125
00:04:13,680 --> 00:04:14,780
你使用三角形加密

126
00:04:14,780 --> 00:04:16,640
那么我同样用你的三角形的钥匙能够给解密吗

127
00:04:16,640 --> 00:04:17,780
其实是不可以的

128
00:04:17,780 --> 00:04:19,720
这里就叫做非对称加密

129
00:04:19,720 --> 00:04:20,480
对吧

130
00:04:20,480 --> 00:04:22,140
也就是同样的钥匙在非对称加密里面呢

131
00:04:22,140 --> 00:04:23,280
它只能加密或者解密

132
00:04:23,280 --> 00:04:25,540
它不能同时进行加密和解密

133
00:04:25,540 --> 00:04:26,520
好 这里就是非对称加密

134
00:04:26,520 --> 00:04:28,380
那么非对称加密它的一个特点是什么呢

135
00:04:28,380 --> 00:04:30,180
它就是安全性比较高

136
00:04:30,180 --> 00:04:32,720
但是它的效率是比较低的

137
00:04:32,720 --> 00:04:34,180
那它就和对称加密相反

138
00:04:34,180 --> 00:04:36,440
因为凡事我们都会有取舍对吧

139
00:04:36,440 --> 00:04:36,980
好

140
00:04:36,980 --> 00:04:39,120
那么我们了解了对称加密

141
00:04:39,120 --> 00:04:39,920
非对称加密

142
00:04:39,920 --> 00:04:40,720
它们的关系之后呢

143
00:04:40,720 --> 00:04:42,280
我们就来看一下hcps

144
00:04:42,280 --> 00:04:43,820
它的一个通信过程是什么样的

145
00:04:43,820 --> 00:04:45,860
首先我们的HTTP协议

146
00:04:45,860 --> 00:04:48,620
它分为HTTP和我们的SSL

147
00:04:48,620 --> 00:04:49,220
这样的两部分

148
00:04:49,220 --> 00:04:50,520
那么HTTP我们前面介绍过

149
00:04:50,520 --> 00:04:52,640
SSL就是我们的一种加密协议

150
00:04:52,640 --> 00:04:54,560
那么在HTTP-S数据传说过程中

151
00:04:54,560 --> 00:04:56,820
需要使用我们的SSL和我们的TLS

152
00:04:56,820 --> 00:04:58,940
对我们的数据进行加密和解密

153
00:04:58,940 --> 00:05:03,360
所以HTTP-S就是由我们的HTTP

154
00:05:03,360 --> 00:05:05,300
和我们的SSL一起合作进行完成的

155
00:05:05,300 --> 00:05:05,980
我们就来看一下

156
00:05:05,980 --> 00:05:08,720
那么HTTP-S为了兼顾安全和效率

157
00:05:08,720 --> 00:05:11,040
同时使用了对称加密和非对称加密

158
00:05:11,040 --> 00:05:40,080
如果我们用最通俗的语言

159
00:05:40,080 --> 00:05:40,840
解释一遍

160
00:05:40,840 --> 00:05:44,180
因为刚才我们是不是解释过了什么是对层加密和什么是非对层加密

161
00:05:44,180 --> 00:05:45,460
那么对层加密就是同一把钥匙

162
00:05:45,460 --> 00:05:48,520
非对层加密就是两把不同的钥匙分别进行加密和解密

163
00:05:48,520 --> 00:05:49,800
但是它们的效率有所区别

164
00:05:49,800 --> 00:05:52,120
对层加密也就是同一把钥匙效率很高

165
00:05:52,120 --> 00:05:54,160
那么你两把钥匙进行加密和解密是不是

166
00:05:54,160 --> 00:05:55,440
可想的速度会慢一点

167
00:05:55,440 --> 00:05:58,000
但是我们现在htps

168
00:05:58,000 --> 00:05:59,540
它是不是既要兼顾

169
00:05:59,540 --> 00:06:01,580
咱们的速度又要兼顾我们安全

170
00:06:01,580 --> 00:06:03,880
你当然两把钥匙是不是比一把钥匙安全

171
00:06:03,880 --> 00:06:05,160
但是两把钥匙它比较慢

172
00:06:05,160 --> 00:06:06,440
那么如何解决这个问题

173
00:06:06,440 --> 00:06:07,680
我们既要它安全要它快

174
00:06:07,680 --> 00:06:10,660
那么是如何解决这样一个问题呢

175
00:06:10,660 --> 00:06:12,180
假如说我和你之间进行通信

176
00:06:12,180 --> 00:06:13,820
那么数据通信的过程

177
00:06:13,820 --> 00:06:15,240
其实我们使用的是对称加密

178
00:06:15,240 --> 00:06:16,280
那么也就是说

179
00:06:16,280 --> 00:06:17,580
我这边使用三角形加密

180
00:06:17,580 --> 00:06:19,100
你也用三角形进行解密

181
00:06:19,100 --> 00:06:21,820
那么有的同学可能会问

182
00:06:21,820 --> 00:06:23,760
他们不还是对称加密吗

183
00:06:23,760 --> 00:06:25,000
那么非对称加密是做什么呢

184
00:06:25,000 --> 00:06:25,880
那么非对称加密

185
00:06:25,880 --> 00:06:28,020
其实就是针对于我们的他

186
00:06:28,020 --> 00:06:29,760
针对于我们的三角形的

187
00:06:29,760 --> 00:06:32,880
什么意思呢

188
00:06:32,880 --> 00:06:35,360
什么意思

189
00:06:35,360 --> 00:06:40,100
我们的三角形在那一把钥匙

190
00:06:40,100 --> 00:06:42,280
其实在网络传输中它也会暴露出来

191
00:06:42,280 --> 00:06:43,880
比如说我给你约定好了

192
00:06:43,880 --> 00:06:46,020
我们用三角形在那一把钥匙进行加密

193
00:06:46,020 --> 00:06:46,860
那么你传输过程中

194
00:06:46,860 --> 00:06:47,660
是不是这把钥匙

195
00:06:47,660 --> 00:06:48,880
是不是也有可能被黑客盗取

196
00:06:48,880 --> 00:06:49,360
对吧

197
00:06:49,360 --> 00:06:49,700
他一看

198
00:06:49,700 --> 00:06:51,160
原来你用三角形在那一把钥匙进行加密了

199
00:06:51,160 --> 00:06:52,280
我也用三角形进解密

200
00:06:52,280 --> 00:06:53,700
只是你是不是信息就暴露了

201
00:06:53,700 --> 00:06:54,240
所以呢

202
00:06:54,240 --> 00:06:56,220
搞HTP的这群人非常聪明

203
00:06:56,220 --> 00:06:57,120
他们想到一个办法

204
00:06:57,120 --> 00:07:00,400
他们对三角形在那一个钥匙

205
00:07:00,400 --> 00:07:04,820
对钥匙进行非对称

206
00:07:04,820 --> 00:07:08,740
我们在了解他们的加密过程之后

207
00:07:08,740 --> 00:07:10,340
我们接下来看一下数字证书

208
00:07:10,340 --> 00:07:13,380
而http不会对通信的双方进行身份的验证

209
00:07:13,380 --> 00:07:15,720
所以身份有可能被伪装造成安全问题

210
00:07:15,720 --> 00:07:17,060
所以为了解决这样一个问题

211
00:07:17,060 --> 00:07:18,100
产生了数字证书

212
00:07:18,100 --> 00:07:20,060
我们来看一下数字证书

213
00:07:20,060 --> 00:07:21,460
它存在的一个原因是什么

214
00:07:21,460 --> 00:07:22,820
咱们还是刚才的这样一个图

215
00:07:22,820 --> 00:07:26,040
假设这里是我们的客户端

216
00:07:26,040 --> 00:07:28,880
这里是我们的服务器

217
00:07:28,880 --> 00:07:30,800
我们两个要经理联系

218
00:07:30,800 --> 00:07:31,240
对吧

219
00:07:31,240 --> 00:07:31,880
经理联系

220
00:07:31,880 --> 00:07:32,960
对吧

221
00:07:32,960 --> 00:07:35,000
好 但是有个问题是什么

222
00:07:35,000 --> 00:07:37,840
那么一般是不是客户端先去访问我们的服务器啊

223
00:07:37,840 --> 00:07:42,060
但是你怎么知道你访问的服务器

224
00:07:42,060 --> 00:07:47,280
就是真实的服务器啊 对吧 什么意思

225
00:07:47,280 --> 00:07:50,060
比如说我此时访问淘宝网站

226
00:07:50,060 --> 00:07:53,900
好 那么呢 我这里呢 去窃取了你家的一个路由

227
00:07:53,900 --> 00:07:54,920
相当于是做了一个截词

228
00:07:54,920 --> 00:07:56,720
然后我返回一个假的淘宝网址给你

229
00:07:56,720 --> 00:07:57,840
然后呢 去做一些事情

230
00:07:57,840 --> 00:07:59,080
那么此时其实呢

231
00:07:59,080 --> 00:08:02,080
HTTP它并不能保证我们的服务器是真实的

232
00:08:02,080 --> 00:08:04,780
所以说就产生了数据证书这样一个东西

233
00:08:04,780 --> 00:08:05,740
那么服务器呢

234
00:08:05,740 --> 00:08:06,980
它如何解决这样一个问题呢

235
00:08:06,980 --> 00:08:08,800
它首先向一个大家都信任的第三方机构

236
00:08:08,800 --> 00:08:10,360
申请一个身份证书

237
00:08:10,360 --> 00:08:12,220
那么这里的第三方机构指谁呢

238
00:08:12,220 --> 00:08:14,500
就是指我们全球非常有名的一些专门

239
00:08:14,500 --> 00:08:16,140
去做我们数据证书认证的机构

240
00:08:16,140 --> 00:08:18,680
那么它给我们的网站去颁发证书的时候

241
00:08:18,680 --> 00:08:20,140
都会去审核网站的一些资料

242
00:08:20,140 --> 00:08:23,520
所以说一些恶意的网站是无法去拿到我们这里的证书的

243
00:08:23,520 --> 00:08:24,760
所以说大家可以这样理解

244
00:08:24,760 --> 00:08:26,080
拥有了证书的网站

245
00:08:26,080 --> 00:08:27,460
那么这样一个网站它有很大的几率

246
00:08:27,460 --> 00:08:28,380
一定是一个正规的网站

247
00:08:28,380 --> 00:08:29,620
当然不能够百分之百的保证

248
00:08:29,620 --> 00:08:30,160
对吧

249
00:08:30,160 --> 00:08:30,700
好

250
00:08:30,700 --> 00:08:33,540
那么客户端和服务键你连接的时候呢

251
00:08:33,540 --> 00:08:34,780
首先会去向服务器呢

252
00:08:34,780 --> 00:08:36,140
去请求服务器证书

253
00:08:36,140 --> 00:08:36,460
好

254
00:08:36,460 --> 00:08:38,420
那么服务器收到请求之后呢

255
00:08:38,420 --> 00:08:39,900
是不是就把证书发送给我们的客户端

256
00:08:39,900 --> 00:08:40,300
好

257
00:08:40,300 --> 00:08:41,640
那么我们客户端拿到证书之后

258
00:08:41,640 --> 00:08:45,220
然后再去教验你这个证书是不是真的

259
00:08:45,220 --> 00:08:45,760
好

260
00:08:45,760 --> 00:08:47,100
那么我们客户端只随随去教验

261
00:08:47,100 --> 00:08:48,000
是你去教验吗

262
00:08:48,000 --> 00:08:48,580
肯定不是的

263
00:08:48,580 --> 00:08:50,280
是我们的谷歌浏览器

264
00:08:50,280 --> 00:08:51,740
因为我们的浏览器厂商

265
00:08:51,740 --> 00:08:53,880
和这样的一些第三方机构都会有一些合作

266
00:08:53,880 --> 00:08:55,300
比如说我去开一家公司

267
00:08:55,300 --> 00:08:57,180
我跟谷歌的老板关系非常好

268
00:08:57,180 --> 00:08:58,960
我说我要搞一家数字证书的公司

269
00:08:58,960 --> 00:09:00,920
我会去给一些网站去颁发证书

270
00:09:00,920 --> 00:09:03,340
到时候你碰到是我的证书你就给我通过

271
00:09:03,340 --> 00:09:06,340
那么这样我的第三方的一个证书机构也就开启了

272
00:09:06,340 --> 00:09:08,820
我那也可以去向各大网站去收费

273
00:09:08,820 --> 00:09:12,460
其实这里就是第三方机构它存在了一个意义

274
00:09:12,460 --> 00:09:14,160
那么我们了解完数字证书之后

275
00:09:14,160 --> 00:09:15,560
我们还得去了解一个数字签名

276
00:09:15,560 --> 00:09:17,840
那么什么是数字签名呢

277
00:09:17,840 --> 00:09:20,660
HGDP它不会对数据的完整性进行浇验

278
00:09:20,660 --> 00:09:22,380
那么这样就会去造成通信的过程中

279
00:09:22,380 --> 00:09:23,800
数据被恶意创改

280
00:09:23,800 --> 00:09:25,720
那么什么意思呢

281
00:09:25,720 --> 00:09:27,860
我们前面通过数字证书

282
00:09:27,860 --> 00:09:29,040
是不是已经验证了我们服务器

283
00:09:29,040 --> 00:09:29,400
是真的

284
00:09:29,400 --> 00:09:31,140
但是还会有一种情况

285
00:09:31,140 --> 00:09:33,520
我们HTTPS在传输数据的过程中

286
00:09:33,520 --> 00:09:35,000
它可能不会修改你的证书

287
00:09:35,000 --> 00:09:36,580
但是你传输了

288
00:09:36,580 --> 00:09:38,820
咱们加密之后的一些数据

289
00:09:38,820 --> 00:09:40,040
是不是有可能被创改

290
00:09:40,040 --> 00:09:40,540
对吧

291
00:09:40,540 --> 00:09:41,880
所以说数字签名就解决了

292
00:09:41,880 --> 00:09:42,760
这样的一个创改的问题

293
00:09:42,760 --> 00:09:45,500
那么它第一个就是验证数据

294
00:09:45,500 --> 00:09:47,640
是否为意料中的对象所发出的

295
00:09:47,640 --> 00:09:49,520
第二个就是去教养我们数据的

296
00:09:49,520 --> 00:09:50,140
一个完整性

297
00:09:50,140 --> 00:09:53,400
那么它的一个原理其实就是主要基于两点

298
00:09:53,400 --> 00:09:55,500
第一个对需要发送的数据进行摘要

299
00:09:55,500 --> 00:09:57,820
比如说服务器返回给你一段信息

300
00:09:57,820 --> 00:09:59,560
那么返回之前就要对它进行一个摘要

301
00:09:59,560 --> 00:10:01,920
那么这里的摘要就和我们wipack的hashimap有点类似

302
00:10:01,920 --> 00:10:04,100
大家是不是都是用wipack打包

303
00:10:04,100 --> 00:10:06,780
比如说我们去修改了gs的一段代码

304
00:10:06,780 --> 00:10:08,460
那么修改完成之后我们wipack打包之后

305
00:10:08,460 --> 00:10:10,840
它的一个hashi的值是不是就会发生变化

306
00:10:10,840 --> 00:10:13,100
但是如果说你去进行重复的打包那个hashi

307
00:10:13,100 --> 00:10:14,500
它是不是一直都没有变化

308
00:10:14,500 --> 00:10:16,180
那么只有当你的代码发生修改之后

309
00:10:16,180 --> 00:10:17,900
wipack生成了hashi版本才会发生变化

310
00:10:17,900 --> 00:10:21,180
其实这里就和我们的数据摘要是一样的一个原理

311
00:10:21,180 --> 00:10:23,440
那么第二个呢就对摘要的信息进行签名

312
00:10:23,440 --> 00:10:24,800
其实签名呢就是对我们

313
00:10:24,800 --> 00:10:26,740
比如说我们的一段GS

314
00:10:26,740 --> 00:10:30,080
我们的一段GS是不是需要通过Wipack打包之后生成一个哈希

315
00:10:30,080 --> 00:10:33,680
那么这样一个哈希其实它传输的过程中是不是也是名文

316
00:10:33,680 --> 00:10:36,000
那么呢其实也要咱们的服务器呢

317
00:10:36,000 --> 00:10:38,440
它也会对我们的这样一个哈希也就是摘要信息进行一个签名

318
00:10:38,440 --> 00:10:40,140
那么签名其实就是加密

319
00:10:40,140 --> 00:10:44,320
那么这样呢通过摘要和签名就可以去确认我们的数据

320
00:10:44,320 --> 00:10:45,620
它有没有被创改

321
00:10:45,620 --> 00:10:48,440
好 接下来我们就来看一下整个他们的一个通信过程

322
00:10:48,440 --> 00:10:49,980
那么首先呢

323
00:10:49,980 --> 00:10:50,740
第一步

324
00:10:50,740 --> 00:10:52,280
客户端向服务端发起请求

325
00:10:52,280 --> 00:10:54,320
也就是我们浏览器是不是发起我们

326
00:10:54,320 --> 00:10:56,120
向服务器发起请求啊对吧

327
00:10:56,120 --> 00:10:58,420
首先呢客户端生成一个随机数21发送给我们的服务端

328
00:10:58,420 --> 00:11:00,720
那么告诉服务端自己支持哪些加密算法

329
00:11:00,720 --> 00:11:03,020
因为这里呢都是浏览器底层的东西我们不用去关注

330
00:11:03,020 --> 00:11:03,800
第二个

331
00:11:03,800 --> 00:11:06,100
服务器向客户端发送数字证书对吧

332
00:11:06,100 --> 00:11:06,860
好

333
00:11:06,860 --> 00:11:09,180
那么服务端收到请求之后发送一个数字证书

334
00:11:09,180 --> 00:11:11,220
然后呢它会生成一个随机数

335
00:11:11,220 --> 00:11:12,760
R

336
00:11:12,760 --> 00:11:14,120
也就是生成一个新的随机数

337
00:11:14,120 --> 00:11:15,720
那么同时从客户端

338
00:11:15,720 --> 00:11:17,520
支持的加密算法中选择一个

339
00:11:17,520 --> 00:11:18,700
大家都支持的加密算法

340
00:11:18,700 --> 00:11:19,460
最后呢

341
00:11:19,460 --> 00:11:21,020
服端就把他的数字证书

342
00:11:21,020 --> 00:11:22,100
以及随机数R

343
00:11:22,100 --> 00:11:24,780
以及呢我们去绘画的一些密要的生成算法

344
00:11:24,780 --> 00:11:26,040
一起来给到我们的客户端

345
00:11:26,040 --> 00:11:27,940
那么此时的第三步

346
00:11:27,940 --> 00:11:29,280
我们客户端是不是已经拿到了数字证书

347
00:11:29,280 --> 00:11:31,080
那么拿到数字证书之后呢

348
00:11:31,080 --> 00:11:33,160
首先需要去验证我们证书的可靠信

349
00:11:33,160 --> 00:11:34,360
那么证书的可靠信

350
00:11:34,360 --> 00:11:35,920
就是交给我们浏览区去验证的

351
00:11:35,920 --> 00:11:38,100
那么就要看我们的第三方机构和谷歌

352
00:11:38,100 --> 00:11:39,300
是否有合作

353
00:11:39,300 --> 00:11:41,400
那么验证完我们的证书之后

354
00:11:41,400 --> 00:11:44,580
接下来会获得我们证书里面的一个公钥

355
00:11:44,580 --> 00:11:47,380
以及我们绘画密钥生成的一个算法

356
00:11:47,380 --> 00:11:48,340
以及在那个随机数

357
00:11:48,340 --> 00:11:50,860
接下来我们的浏览器就会生成一个随机数23

358
00:11:50,860 --> 00:11:54,360
那么此时我们拿到了212223

359
00:11:54,360 --> 00:11:58,120
是不是就是我们进行加密的一个密钥

360
00:11:58,120 --> 00:11:59,740
也就是我们进行对称加密的一个密钥

361
00:11:59,740 --> 00:12:04,160
然后客户端使用服务端证书的公钥

362
00:12:04,160 --> 00:12:07,000
对我们刚才生成的在一个密钥进行一个加密

363
00:12:07,000 --> 00:12:08,000
发送给服务端

364
00:12:08,000 --> 00:12:11,360
这里就是我们进行对称加密的一个必要

365
00:12:11,360 --> 00:12:23,360
此处就是服务端和咱们的客户端进行数据传输的对称必要

366
00:12:23,360 --> 00:12:24,820
好

367
00:12:24,820 --> 00:12:29,240
那么此时我们的客户端是不是把我们的对称密要

368
00:12:29,240 --> 00:12:30,840
通过非对称的方式发送给了我们服务端

369
00:12:30,840 --> 00:12:32,680
服务端的此时用私要去解密

370
00:12:32,680 --> 00:12:34,680
我们客户端发送过来的随机数23

371
00:12:34,680 --> 00:12:35,520
好

372
00:12:35,520 --> 00:12:37,380
那么解密完成之后

373
00:12:37,380 --> 00:12:41,580
咱们服务端和扣端是不是就已经拿到了彼此的一个对称的必要

374
00:12:41,580 --> 00:12:43,380
他们就可以去进行一个链接

375
00:12:43,380 --> 00:12:46,380
然后去传输他们之间的一些数据

376
00:12:46,380 --> 00:12:49,880
好 那么这里其实就是htps它的整个通信的一个过程

377
00:12:49,880 --> 00:12:51,080
我们来简单的回归一下

378
00:12:51,080 --> 00:12:53,080
整个htps的通信过程

379
00:12:53,080 --> 00:12:55,780
其实就是htps加上ssl和tls

380
00:12:55,780 --> 00:12:57,880
也就是加密的一种协议

381
00:12:57,880 --> 00:12:59,080
那么他们是如何通信的呢

382
00:12:59,080 --> 00:13:01,780
其实是结合了我们的非对称加密和对称加密

383
00:13:01,780 --> 00:13:03,180
那么他们分别什么特点呢

384
00:13:03,180 --> 00:13:05,180
非对称加密安全性更高

385
00:13:05,180 --> 00:13:06,080
但是效率比较低

386
00:13:06,080 --> 00:13:07,280
对称加密安全性比较低

387
00:13:07,280 --> 00:13:07,900
但是效率很高

388
00:13:07,900 --> 00:13:11,100
所以说我们FTPs为了协调他们之间的关系

389
00:13:11,100 --> 00:13:12,740
也就是为了兼顾性能和安全

390
00:13:12,740 --> 00:13:15,220
就同时使用了非对称加密和对称加密

391
00:13:15,220 --> 00:13:17,520
那么我们的数据传输使用对称加密

392
00:13:17,520 --> 00:13:20,000
但是我们的密钥却使用了非对称加密

393
00:13:20,000 --> 00:13:22,760
那么接下来出现了数字证书

394
00:13:22,760 --> 00:13:23,780
数字证书解决了什么问题

395
00:13:23,780 --> 00:13:25,680
是不是解决了我们的服务器

396
00:13:25,680 --> 00:13:27,500
你到底是不是真的这样一个问题

397
00:13:27,500 --> 00:13:28,800
那么数字签名解决了什么问题

398
00:13:28,800 --> 00:13:31,400
我只是认证了你的服务器确实是真实的

399
00:13:31,400 --> 00:13:32,320
但是是不是也有可能

400
00:13:32,320 --> 00:13:33,500
我们中间传出一些消息

401
00:13:33,500 --> 00:13:34,520
也有可能被人创改

402
00:13:34,520 --> 00:13:39,580
所以说就利用了数字签名这样一种技术来校验我们数据到底有没有被人创改过

403
00:13:39,580 --> 00:13:41,620
那么我们为什么要防止数据被人创改呢

404
00:13:41,620 --> 00:13:44,840
比如说我给你转账100被人改为1000这样是不是就非常不好了

405
00:13:44,840 --> 00:13:46,440
那么如何去解决这样一个问题呢

406
00:13:46,440 --> 00:13:50,360
它的原理其实就是和我们的YPAC的一个生成版本号的一个哈希是非常相似的

407
00:13:50,360 --> 00:13:52,840
那么生成哈希之后还会去使用签名对它进行一个加密

408
00:13:52,840 --> 00:13:55,340
通过数字签名之后就可以解决我们数据创改的问题

409
00:13:55,340 --> 00:13:58,500
那么解决了我们服务器的真实性以及数据的真实性之后

410
00:13:58,500 --> 00:14:01,920
那么其实可以很大程度上保证了我们数据传输的一个安全

411
00:14:01,920 --> 00:14:05,900
那么这里就是我们整个HGPS它的一个内容

