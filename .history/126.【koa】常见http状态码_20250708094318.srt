1
00:00:00,000 --> 00:00:01,800
好 这节课我们就来看一下

2
00:00:01,800 --> 00:00:03,320
Core中它的一些错误处理

3
00:00:03,320 --> 00:00:04,780
那么在学习错误处理之前

4
00:00:04,780 --> 00:00:07,300
我们首先得了解一下HTTP里面一些常见的状态

5
00:00:07,300 --> 00:00:08,740
首先200

6
00:00:08,740 --> 00:00:10,640
200是不是我们非常常见的

7
00:00:10,640 --> 00:00:11,460
它代表请求成功

8
00:00:11,460 --> 00:00:12,920
那么我们来看一下什么是200

9
00:00:12,920 --> 00:00:15,220
比如说

10
00:00:15,220 --> 00:00:16,760
同学们最喜欢的淘宝网

11
00:00:16,760 --> 00:00:18,320
我们算一下是不是满屏的200

12
00:00:18,320 --> 00:00:19,280
200代表什么

13
00:00:19,280 --> 00:00:20,000
我们随便点一个

14
00:00:20,000 --> 00:00:22,240
200是不是代表OK啊

15
00:00:22,240 --> 00:00:23,640
是不是代表OK

16
00:00:23,640 --> 00:00:24,820
OK说明没毛病吧

17
00:00:24,820 --> 00:00:26,440
好 那么这里呢就是200

18
00:00:26,440 --> 00:00:27,900
那么还有一个就是304

19
00:00:27,900 --> 00:00:29,380
这是我们常见的

20
00:00:29,380 --> 00:00:30,780
304其实是代表缓存

21
00:00:30,780 --> 00:00:33,280
那我们来看一下什么是缓存

22
00:00:33,280 --> 00:00:36,100
好 我们来刷新

23
00:00:36,100 --> 00:00:39,780
大家可以看到

24
00:00:39,780 --> 00:00:41,060
304这样一个状态吗

25
00:00:41,060 --> 00:00:42,020
我们是不是只看到了

26
00:00:42,020 --> 00:00:44,120
很少啊 对吧

27
00:00:44,120 --> 00:00:44,820
只有两个

28
00:00:44,820 --> 00:00:45,740
大部分是200

29
00:00:45,740 --> 00:00:47,900
那么难道我们的页面没有做缓存吗

30
00:00:47,900 --> 00:00:48,920
其实不是这样的

31
00:00:48,920 --> 00:00:49,420
大家可以看到

32
00:00:49,420 --> 00:00:51,720
我们这里大块

33
00:00:51,720 --> 00:00:52,820
size

34
00:00:52,820 --> 00:00:53,900
它是不是有

35
00:00:53,900 --> 00:00:55,000
size在一个属性

36
00:00:55,000 --> 00:00:55,560
下面是不是都是

37
00:00:55,560 --> 00:00:56,580
什么frommemory card

38
00:00:56,580 --> 00:00:57,300
去disc card

39
00:00:57,300 --> 00:00:58,600
service worker

40
00:00:58,600 --> 00:00:59,360
什么是service worker

41
00:00:59,360 --> 00:01:01,600
ServiceWorker是不是咱们的PWA里面的内容

42
00:01:01,600 --> 00:01:02,140
对吧

43
00:01:02,140 --> 00:01:02,820
好

44
00:01:02,820 --> 00:01:03,260
那么

45
00:01:03,260 --> 00:01:05,680
为什么说我们的缓存

46
00:01:05,680 --> 00:01:07,180
它此时状态是200了

47
00:01:07,180 --> 00:01:08,960
不知道同学们还记不记得

48
00:01:08,960 --> 00:01:10,300
之前咱们前面讲过的内容

49
00:01:10,300 --> 00:01:11,400
我们浏览器的缓存

50
00:01:11,400 --> 00:01:12,660
是不是分为两种啊

51
00:01:12,660 --> 00:01:14,220
一种是强缓存

52
00:01:14,220 --> 00:01:15,040
一个是

53
00:01:15,040 --> 00:01:17,720
鞋商缓存

54
00:01:17,720 --> 00:01:18,580
那么刚才

55
00:01:18,580 --> 00:01:19,560
frommemory卡尔奇

56
00:01:19,560 --> 00:01:20,480
和咱们的地设卡尔奇

57
00:01:20,480 --> 00:01:21,160
是不是强缓存了

58
00:01:21,160 --> 00:01:22,640
强缓存它是不是返回的其实是200

59
00:01:22,640 --> 00:01:23,020
为什么

60
00:01:23,020 --> 00:01:24,720
因为它是从咱们浏览器本地读取的

61
00:01:24,720 --> 00:01:26,460
那么鞋商缓存才会返回304

62
00:01:26,460 --> 00:01:26,660
为什么

63
00:01:26,660 --> 00:01:27,620
是不是因为

64
00:01:27,620 --> 00:01:30,220
协商缓存是通过服务器对比文件

65
00:01:30,220 --> 00:01:31,100
才返回一个状态

66
00:01:31,100 --> 00:01:31,980
那么我们是不是

67
00:01:31,980 --> 00:01:33,680
比如说我们去访问一个资源的时候

68
00:01:33,680 --> 00:01:34,460
它是不是先走

69
00:01:34,460 --> 00:01:36,160
抢缓存再走协商缓存呢

70
00:01:36,160 --> 00:01:36,820
所以说

71
00:01:36,820 --> 00:01:40,300
咱们的淘宝网大部分都是200的缓存

72
00:01:40,300 --> 00:01:41,760
那么304呢其实是协商缓存

73
00:01:41,760 --> 00:01:43,800
比如说我们去给它强制刷信一下

74
00:01:43,800 --> 00:01:46,760
咱们选择一个清除缓存并硬性重新加载

75
00:01:46,760 --> 00:01:50,980
好大家可以看到我们再一次访问它非常的慢

76
00:01:50,980 --> 00:01:51,220
为什么

77
00:01:51,220 --> 00:01:53,400
因为我们来把缓存给清掉了

78
00:01:53,400 --> 00:01:54,620
好

79
00:01:54,620 --> 00:01:56,120
重新刷一下好像卡住了

80
00:01:56,120 --> 00:02:09,160
我来重新加载一下

81
00:02:09,160 --> 00:02:10,240
重新加载一下

82
00:02:10,240 --> 00:02:11,560
好

83
00:02:11,560 --> 00:02:12,460
那么我们再来看

84
00:02:12,460 --> 00:02:13,520
大家可以看到我们是不是

85
00:02:13,520 --> 00:02:15,340
是不是咱们的Memory Couch

86
00:02:15,340 --> 00:02:16,880
变少了很多呀

87
00:02:16,880 --> 00:02:18,560
但是有的同学就会问了

88
00:02:18,560 --> 00:02:20,060
老师你刚才不是明明清了缓存吗

89
00:02:20,060 --> 00:02:21,380
那么我们为什么还会

90
00:02:21,380 --> 00:02:22,280
还会Memory Couch

91
00:02:22,280 --> 00:02:23,400
为什么呀

92
00:02:23,400 --> 00:02:24,100
因为咱们

93
00:02:24,100 --> 00:02:26,440
因为咱们刚才强制刷新了

94
00:02:26,440 --> 00:02:27,240
是不是淘宝这样一个域名

95
00:02:27,240 --> 00:02:27,980
但是咱们的图片

96
00:02:27,980 --> 00:02:28,960
它是它的网址

97
00:02:28,960 --> 00:02:29,820
它是不是不属于淘宝

98
00:02:29,820 --> 00:02:31,400
比如说它是属于DataImage

99
00:02:31,400 --> 00:02:32,460
它是属于别的域名

100
00:02:32,460 --> 00:02:34,820
所以说它缓存不是缓存在

101
00:02:34,820 --> 00:02:36,100
淘宝在一个域名下面

102
00:02:36,100 --> 00:02:37,600
所以说它还会存在一些memory cards

103
00:02:37,600 --> 00:02:39,900
好 这里就是304这样一个状态

104
00:02:39,900 --> 00:02:41,260
那么404是什么呢

105
00:02:41,260 --> 00:02:44,020
404是不是我们客户端发生了一些错误

106
00:02:44,020 --> 00:02:46,060
它是属于客户端错误

107
00:02:46,060 --> 00:02:48,180
那么什么是客户端错误呢

108
00:02:48,180 --> 00:02:49,580
这里我给同学们去解释一下

109
00:02:49,580 --> 00:02:51,540
比如说404它是客户端错误

110
00:02:51,540 --> 00:02:52,020
500呢

111
00:02:52,020 --> 00:02:52,960
内部服务器错误

112
00:02:52,960 --> 00:02:54,100
它代表是服务器有问题

113
00:02:54,100 --> 00:02:55,660
那么什么是客户端错误呢

114
00:02:55,660 --> 00:02:56,260
你比如说

115
00:02:56,260 --> 00:02:57,580
我们举个例子

116
00:02:57,580 --> 00:02:58,540
你客户端的错误

117
00:02:58,540 --> 00:03:00,040
那么假如说你是一个学生

118
00:03:00,040 --> 00:03:01,020
然后你访问一个网址

119
00:03:01,020 --> 00:03:01,660
但是呢

120
00:03:01,660 --> 00:03:03,120
在一个地址之间老师才能访问

121
00:03:03,120 --> 00:03:03,540
此时

122
00:03:03,540 --> 00:03:04,720
页面你是不是进不去

123
00:03:04,720 --> 00:03:05,380
然后呢会报错

124
00:03:05,380 --> 00:03:06,600
那么此时是谁的问题

125
00:03:06,600 --> 00:03:07,620
是不是你客户的问题啊

126
00:03:07,620 --> 00:03:08,240
因为你是一个学生

127
00:03:08,240 --> 00:03:09,240
但是你去访问老师的页面

128
00:03:09,240 --> 00:03:10,040
你当然访问不了

129
00:03:10,040 --> 00:03:10,400
所以呢

130
00:03:10,400 --> 00:03:11,280
这里是客户端错误

131
00:03:11,280 --> 00:03:12,800
那么什么是服务器错误呢

132
00:03:12,800 --> 00:03:13,980
服务器错误

133
00:03:13,980 --> 00:03:14,700
是不是就是指

134
00:03:14,700 --> 00:03:15,660
咱们呢

135
00:03:15,660 --> 00:03:16,440
服装有问题啊

136
00:03:16,440 --> 00:03:17,040
你比如说都有js

137
00:03:17,040 --> 00:03:17,880
比如说call啊

138
00:03:17,880 --> 00:03:18,600
你的逻辑写的问题

139
00:03:18,600 --> 00:03:20,000
用户传入一个参数之后

140
00:03:20,000 --> 00:03:20,680
结果呢

141
00:03:20,680 --> 00:03:21,920
你那一段用逻辑会挂掉

142
00:03:21,920 --> 00:03:23,840
这里呢就会返回一个500

143
00:03:23,840 --> 00:03:25,200
这里呢就是咱们常见的

144
00:03:25,200 --> 00:03:25,760
四个状态

145
00:03:25,760 --> 00:03:26,760
然后呢

146
00:03:26,760 --> 00:03:27,660
其实我们HTP

147
00:03:27,660 --> 00:03:28,480
它的状态

148
00:03:28,480 --> 00:03:29,440
它是有一些规律的

149
00:03:29,440 --> 00:03:29,900
比如说

150
00:03:29,900 --> 00:03:30,860
如果说是一开头

151
00:03:30,860 --> 00:03:32,260
是不是代表我们的请求的

152
00:03:32,260 --> 00:03:33,700
请求的一个过程

153
00:03:33,700 --> 00:03:34,020
其实

154
00:03:34,020 --> 00:03:35,660
其实一开头的状态是很少的

155
00:03:35,660 --> 00:03:36,520
你比如说2345

156
00:03:36,520 --> 00:03:37,360
二是不代表成功啊

157
00:03:37,360 --> 00:03:37,780
比如说200

158
00:03:37,780 --> 00:03:38,600
3呢

159
00:03:38,600 --> 00:03:39,140
重定项

160
00:03:39,140 --> 00:03:40,720
4呢

161
00:03:40,720 --> 00:03:41,660
代表我们的扣端错误

162
00:03:41,660 --> 00:03:42,640
比如说常见的有401

163
00:03:42,640 --> 00:03:43,600
没有权限

164
00:03:43,600 --> 00:03:43,980
404

165
00:03:43,980 --> 00:03:44,780
你没有找到资源

166
00:03:44,780 --> 00:03:45,420
500呢

167
00:03:45,420 --> 00:03:46,060
就是咱们的

168
00:03:46,060 --> 00:03:46,960
咱们的辅端

169
00:03:46,960 --> 00:03:47,780
有一些错误

170
00:03:47,780 --> 00:03:48,040
好

171
00:03:48,040 --> 00:03:48,380
这里呢

172
00:03:48,380 --> 00:03:49,400
我们就来总结一下

173
00:03:49,400 --> 00:03:51,600
我们刚才所讲的内容

174
00:03:51,600 --> 00:03:53,660
我们刚才是不是讲到了

175
00:03:53,660 --> 00:03:54,660
code里面

176
00:03:54,660 --> 00:03:57,140
错误处理

177
00:03:57,140 --> 00:03:58,140
错误处理

178
00:03:58,140 --> 00:03:58,780
刚才是不是介绍了

179
00:03:58,780 --> 00:03:59,480
常见的

180
00:03:59,480 --> 00:04:02,740
常见的http

181
00:04:02,740 --> 00:04:05,040
状态码呀

182
00:04:05,040 --> 00:04:06,220
比如说最常见的是200

183
00:04:06,220 --> 00:04:06,560
对吧

184
00:04:06,560 --> 00:04:07,240
200代表什么ok

185
00:04:07,240 --> 00:04:08,280
然后呢

186
00:04:08,280 --> 00:04:08,660
304

187
00:04:08,660 --> 00:04:09,260
304

188
00:04:09,260 --> 00:04:10,020
是不是代表缓存

189
00:04:10,020 --> 00:04:12,340
它是什么呢

190
00:04:12,340 --> 00:04:12,860
协商缓存

191
00:04:12,860 --> 00:04:14,060
那么对应的

192
00:04:14,060 --> 00:04:15,620
是不是还有强缓存呢

193
00:04:15,620 --> 00:04:17,000
强缓存其实

194
00:04:17,000 --> 00:04:18,220
访问率是多少

195
00:04:18,220 --> 00:04:18,620
是不是200

196
00:04:18,620 --> 00:04:19,760
同时呢

197
00:04:19,760 --> 00:04:20,240
还有一些

198
00:04:20,240 --> 00:04:20,980
比如说像404

199
00:04:20,980 --> 00:04:21,540
401

200
00:04:21,540 --> 00:04:23,140
它代表的是什么错误

201
00:04:23,140 --> 00:04:24,700
是不是咱们的客户端错误

202
00:04:24,700 --> 00:04:26,060
刚才是不是举了一个例子

203
00:04:26,060 --> 00:04:26,840
比如学生和老师

204
00:04:26,840 --> 00:04:28,240
你学生去访问老师的页面

205
00:04:28,240 --> 00:04:28,760
当然访问不到

206
00:04:28,760 --> 00:04:29,480
这是你自己的问题

207
00:04:29,480 --> 00:04:29,840
所以说呢

208
00:04:29,840 --> 00:04:30,600
这是客户端错误

209
00:04:30,600 --> 00:04:31,340
比如说呢

210
00:04:31,340 --> 00:04:31,880
你去一些

211
00:04:31,880 --> 00:04:33,560
比如说你一些参数

212
00:04:33,560 --> 00:04:34,320
传错了

213
00:04:34,320 --> 00:04:34,620
这里呢

214
00:04:34,620 --> 00:04:35,440
也是客户端错误

215
00:04:35,440 --> 00:04:36,060
然后呢

216
00:04:36,060 --> 00:04:37,440
还有一个所对应的

217
00:04:37,440 --> 00:04:38,300
是不是500呀

218
00:04:38,300 --> 00:04:39,060
服气错误

219
00:04:39,060 --> 00:04:41,220
好 这里呢就是我们这几个内容

