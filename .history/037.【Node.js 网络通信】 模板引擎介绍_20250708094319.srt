1
00:00:00,000 --> 00:00:04,780
这节课我们来学习一下如何在路德中来使用木板引擎来处理动态页面

2
00:00:04,780 --> 00:00:08,360
那这里我们可以看到假设我们在这里有一份数据

3
00:00:08,360 --> 00:00:11,200
例如这个土肚子这个数组要展示到我们的页面当中

4
00:00:11,200 --> 00:00:13,100
那这个数组要展示到页面当中

5
00:00:13,100 --> 00:00:14,440
我们最简单的一种方式呢

6
00:00:14,440 --> 00:00:17,820
我们就是通过这个数组来生成一段HMR字符串

7
00:00:17,820 --> 00:00:20,780
然后把生成的这个结果去通过字符串替换的方式

8
00:00:20,780 --> 00:00:22,920
然后把它替换到我们的这个页面的字符串当中

9
00:00:22,920 --> 00:00:24,080
这是最简单的方式

10
00:00:24,080 --> 00:00:26,000
但是当我们这种需求

11
00:00:26,000 --> 00:00:28,180
或者说当我们这种数据呢越来越多的时候

12
00:00:28,180 --> 00:00:28,980
那么这个时候

13
00:00:28,980 --> 00:00:30,920
你再去手动的进行字符串替换

14
00:00:30,920 --> 00:00:32,040
那么就会变得非常的麻烦

15
00:00:32,040 --> 00:00:33,980
所以我们这个前任呢

16
00:00:33,980 --> 00:00:34,780
就将这种方式

17
00:00:34,780 --> 00:00:36,520
就是整合了一些规则之后

18
00:00:36,520 --> 00:00:37,280
然后开发了

19
00:00:37,280 --> 00:00:39,180
我们现在就是建的更多的这种

20
00:00:39,180 --> 00:00:40,840
所谓的叫做模板引擎

21
00:00:40,840 --> 00:00:41,620
好

22
00:00:41,620 --> 00:00:43,020
例如我们经常在网页中呢

23
00:00:43,020 --> 00:00:43,880
看到这样一些大码

24
00:00:43,880 --> 00:00:45,220
例如肩波二百分二

25
00:00:45,220 --> 00:00:46,300
或者是什么双大码

26
00:00:46,300 --> 00:00:48,700
或者说其他的一些奇奇怪怪的语法

27
00:00:48,700 --> 00:00:49,660
但无论如何

28
00:00:49,660 --> 00:00:51,440
他们的实际上都是有规律的

29
00:00:51,440 --> 00:00:52,780
因为他们的就都是

30
00:00:52,780 --> 00:00:54,500
实际上就都是我们这个字符串

31
00:00:54,500 --> 00:00:55,980
在进行解析替换的时候

32
00:00:55,980 --> 00:00:57,840
一个所谓的固定规则的标记

33
00:00:57,840 --> 00:00:59,220
因为又只有这样

34
00:00:59,220 --> 00:01:00,000
只有制定的规则

35
00:01:00,000 --> 00:01:00,940
只有有了规律

36
00:01:00,940 --> 00:01:02,960
那么才可以去通用的进行处理

37
00:01:02,960 --> 00:01:04,420
所以说什么是木板引擎呢

38
00:01:04,420 --> 00:01:05,600
木板引擎的本质呢

39
00:01:05,600 --> 00:01:07,080
就是在根据

40
00:01:07,080 --> 00:01:09,620
或者说去识别这些特殊的语法标记

41
00:01:09,620 --> 00:01:10,980
然后把这些语法标记呢

42
00:01:10,980 --> 00:01:12,080
去进行解析替换

43
00:01:12,080 --> 00:01:13,160
说白了

44
00:01:13,160 --> 00:01:14,780
也就是一种就是更高级的一种

45
00:01:14,780 --> 00:01:17,020
就是字符串解析替换的一种方式

46
00:01:17,020 --> 00:01:18,160
那么这样的话呢

47
00:01:18,160 --> 00:01:20,320
我们就可以应对这种就是复杂的

48
00:01:20,320 --> 00:01:21,600
例如单位有很多份数据

49
00:01:21,600 --> 00:01:23,000
要显示到这个页面当中

50
00:01:23,000 --> 00:01:24,560
那这个时候有了这个木板引擎

51
00:01:24,560 --> 00:01:25,560
我们就会让这个工作呢

52
00:01:25,560 --> 00:01:26,440
变得非常轻松了

53
00:01:26,440 --> 00:01:27,880
好 那么在Node中

54
00:01:27,880 --> 00:01:30,040
注意 有很多优秀的木板引擎呢

55
00:01:30,040 --> 00:01:30,960
可以让我们来选择

56
00:01:30,960 --> 00:01:31,920
他们的功能呢

57
00:01:31,920 --> 00:01:33,120
大部分的都是一样的

58
00:01:33,120 --> 00:01:34,720
但是呢 各自有各自的特点

59
00:01:34,720 --> 00:01:36,120
好 另外我们常见的

60
00:01:36,120 --> 00:01:37,240
大概就是这些

61
00:01:37,240 --> 00:01:37,920
什么Maha

62
00:01:37,920 --> 00:01:38,680
还有什么Nanjax

63
00:01:38,680 --> 00:01:39,240
Handlebars

64
00:01:39,240 --> 00:01:40,240
EGS等等

65
00:01:40,240 --> 00:01:41,520
好 那么我们这里的话呢

66
00:01:41,520 --> 00:01:43,680
就以AddTemplate这个木板引擎为例

67
00:01:43,680 --> 00:01:44,600
我们来看一下

68
00:01:44,600 --> 00:01:46,360
这个木板引擎的一个具体的用法

69
00:01:46,360 --> 00:01:48,000
好 那么这个时候呢

70
00:01:48,000 --> 00:01:49,040
回到我们的浏览器当中

71
00:01:49,040 --> 00:01:50,560
好 在浏览器当中

72
00:01:50,560 --> 00:01:51,940
我们现在打开的这个页面

73
00:01:51,940 --> 00:01:52,960
就是Art Template

74
00:01:52,960 --> 00:01:55,000
木板引擎的Github的官方仓库

75
00:01:55,000 --> 00:01:55,600
好

76
00:01:55,600 --> 00:01:56,320
那么打开它以后

77
00:01:56,320 --> 00:01:57,280
注意它这里呢

78
00:01:57,280 --> 00:01:58,880
就简单的列出了一些

79
00:01:58,880 --> 00:02:00,320
它的一个仓库的介绍

80
00:02:00,320 --> 00:02:01,480
它的一些英文文档

81
00:02:01,480 --> 00:02:02,960
和中文文档等相关的信息

82
00:02:02,960 --> 00:02:04,080
包括它的一些特性

83
00:02:04,080 --> 00:02:05,120
那么这里的话

84
00:02:05,120 --> 00:02:06,520
我们也可以去单独的打开

85
00:02:06,520 --> 00:02:08,040
它这里提供的一个官方的网址

86
00:02:08,040 --> 00:02:09,440
就是它做了一个独立的官网

87
00:02:09,440 --> 00:02:10,680
我们打开它之后

88
00:02:10,680 --> 00:02:11,960
然后你会来到这样一个页面

89
00:02:11,960 --> 00:02:13,120
当然这个页面

90
00:02:13,120 --> 00:02:14,240
你默认打开它的时候呢

91
00:02:14,240 --> 00:02:15,480
它是显示的是这样的

92
00:02:15,480 --> 00:02:16,600
当然它默认是英文

93
00:02:16,600 --> 00:02:17,840
如果说你要看中文的话

94
00:02:20,560 --> 00:02:21,480
中文状态

95
00:02:21,480 --> 00:02:22,540
好那么切换好以后

96
00:02:22,540 --> 00:02:24,700
然后既然你可以点击这个文档进来

97
00:02:24,700 --> 00:02:25,360
好那么进来以后

98
00:02:25,360 --> 00:02:27,400
然后它就会去对这个模板引擎

99
00:02:27,400 --> 00:02:28,800
就是方方面面的一个介绍

100
00:02:28,800 --> 00:02:29,560
例如它说

101
00:02:29,560 --> 00:02:31,860
它是一个简约超快的模板引擎

102
00:02:31,860 --> 00:02:33,180
可以同时在浏览器

103
00:02:33,180 --> 00:02:35,160
或者是node架子中来进行使用

104
00:02:35,160 --> 00:02:36,260
而且这是它的特性

105
00:02:36,260 --> 00:02:37,340
支持这个字模板

106
00:02:37,340 --> 00:02:38,620
而且它本身的非常小

107
00:02:38,620 --> 00:02:39,700
只有6KB大小

108
00:02:39,700 --> 00:02:40,400
好那这个东西

109
00:02:40,400 --> 00:02:41,220
大家下来之后呢

110
00:02:41,220 --> 00:02:42,540
就是说可以去把这个文档

111
00:02:42,540 --> 00:02:43,860
去大概的去给它过一遍

112
00:02:43,860 --> 00:02:45,000
那我们这里的话呢

113
00:02:45,000 --> 00:02:46,600
我们可以来快速的来体验一下

114
00:02:46,600 --> 00:02:47,660
这是它的语法

115
00:02:47,660 --> 00:02:49,320
体验它的方式的第一步

116
00:02:49,320 --> 00:02:50,320
你要先来安装

117
00:02:50,320 --> 00:02:51,820
安装方式的

118
00:02:51,820 --> 00:02:53,260
就是它官方给出的这条命令

119
00:02:53,260 --> 00:02:54,520
就是NPM install a template

120
00:02:54,520 --> 00:02:55,860
当然后面这个刚刚sale

121
00:02:55,860 --> 00:02:56,760
加不加其实无所谓

122
00:02:56,760 --> 00:02:58,740
不加默认就是刚刚sale

123
00:02:58,740 --> 00:02:59,860
那我这里的话呢

124
00:02:59,860 --> 00:03:00,900
已经把它装到了

125
00:03:00,900 --> 00:03:02,400
咱们的这个设计项目当中

126
00:03:02,400 --> 00:03:03,080
已经装好了

127
00:03:03,080 --> 00:03:04,520
那么装好之后怎么去用呢

128
00:03:04,520 --> 00:03:05,900
注意接下来我们到达它的语法

129
00:03:05,900 --> 00:03:07,900
那么它在语法当中

130
00:03:07,900 --> 00:03:10,120
就为我列出了这些常用的语法

131
00:03:10,120 --> 00:03:11,880
例如当你想输出一段内容的时候

132
00:03:11,880 --> 00:03:13,540
当你想输出这个变量的时候

133
00:03:13,540 --> 00:03:15,420
那么它提供两种语法

134
00:03:15,420 --> 00:03:16,160
一种是标准语法

135
00:03:16,160 --> 00:03:17,040
一种是原始语法

136
00:03:17,040 --> 00:03:18,060
相对来讲

137
00:03:18,060 --> 00:03:20,000
注意这个没有什么标准不标准

138
00:03:20,000 --> 00:03:21,520
这就是它的这个简化语法

139
00:03:21,520 --> 00:03:23,140
我们在这里更推荐大家

140
00:03:23,140 --> 00:03:24,580
使用这种就是简化语法

141
00:03:24,580 --> 00:03:26,220
简化语法就是更简化了而已

142
00:03:26,220 --> 00:03:27,860
也就是说它这两种语法都支持

143
00:03:27,860 --> 00:03:30,760
包括它也支持这个就是原文输出

144
00:03:30,760 --> 00:03:32,140
例如如果你是一段

145
00:03:32,140 --> 00:03:32,980
Etimal字符传

146
00:03:32,980 --> 00:03:34,760
那么它也能直接进行原文输出

147
00:03:34,760 --> 00:03:37,500
包括它也支持母版中的条件判断

148
00:03:37,500 --> 00:03:39,120
包括母版中的循环

149
00:03:39,120 --> 00:03:40,160
母版中的循环

150
00:03:40,160 --> 00:03:41,720
母版中的这个变量

151
00:03:41,720 --> 00:03:42,840
变量设定

152
00:03:42,840 --> 00:03:43,580
母版继承

153
00:03:43,580 --> 00:03:44,740
子母版过滤器

154
00:03:44,740 --> 00:03:47,360
这个就是他大概所支持的这些语法

155
00:03:47,360 --> 00:03:49,080
接下来我们就可以在案例当中

156
00:03:49,080 --> 00:03:50,740
来简单的来试一下

157
00:03:50,740 --> 00:03:52,260
回到我们的编例器当中

158
00:03:52,260 --> 00:03:55,540
我在这里新建了一个就是05-template-engine.js

159
00:03:55,540 --> 00:03:57,260
到这里我已经把这个包给它装好了

160
00:03:57,260 --> 00:03:59,660
装好以后我们接下来在这里就可以去cost

161
00:03:59,660 --> 00:04:01,080
然后complete

162
00:04:01,080 --> 00:04:02,220
我们引包

163
00:04:02,220 --> 00:04:04,180
把这个包来给它引进来

164
00:04:04,180 --> 00:04:04,920
引进来以后注意

165
00:04:04,920 --> 00:04:06,480
它有一个方法叫做render

166
00:04:06,480 --> 00:04:08,040
那么这个render方法呢

167
00:04:08,040 --> 00:04:09,080
首先它接受一个参数

168
00:04:09,080 --> 00:04:10,280
那么这个参数呢

169
00:04:10,280 --> 00:04:11,620
注意我们先来个简单的

170
00:04:11,620 --> 00:04:12,380
留hello

171
00:04:12,380 --> 00:04:13,780
然后接下来我们在这里

172
00:04:13,780 --> 00:04:16,280
我们使用它的模板语法来进行一个替换

173
00:04:16,280 --> 00:04:17,500
例如我们在这里呢

174
00:04:17,500 --> 00:04:18,580
让它来一个message

175
00:04:18,580 --> 00:04:19,240
好了

176
00:04:19,240 --> 00:04:21,680
然后接下来我们在第二个参数给一个什么呢

177
00:04:21,680 --> 00:04:24,300
你注意就给一个普通的DS对象就可以了

178
00:04:24,300 --> 00:04:25,200
那么在这个对象当中

179
00:04:25,200 --> 00:04:28,360
我们要提供这个模板当中所使用到的一些数据

180
00:04:28,360 --> 00:04:30,320
例如在这里就需要来使用数据中的message

181
00:04:30,320 --> 00:04:32,160
那我们就需要在这个第二个参数对象中

182
00:04:32,160 --> 00:04:33,580
来提供一个message

183
00:04:33,580 --> 00:04:34,680
例如我们在这儿来个message

184
00:04:34,680 --> 00:04:36,740
那么这样的话

185
00:04:36,740 --> 00:04:37,780
我们在这里就写好了

186
00:04:37,780 --> 00:04:38,580
那么写好以后注意

187
00:04:38,580 --> 00:04:40,220
这个方法的作用就是什么呢

188
00:04:40,220 --> 00:04:43,220
就是将这个字符串当中的双大括号中

189
00:04:43,220 --> 00:04:44,920
这个message替换为

190
00:04:44,920 --> 00:04:46,180
我们数据对象中的

191
00:04:46,180 --> 00:04:47,580
这个message对应的值

192
00:04:47,580 --> 00:04:48,760
也就是说将来的结果

193
00:04:48,760 --> 00:04:50,020
这个就不在乎这个大文化了

194
00:04:50,020 --> 00:04:50,480
而是什么呢

195
00:04:50,480 --> 00:04:51,980
而是hello这个world

196
00:04:51,980 --> 00:04:52,520
好

197
00:04:52,520 --> 00:04:53,520
那这个时候我们接下来

198
00:04:53,520 --> 00:04:55,120
接收一下这个结果

199
00:04:55,120 --> 00:04:55,980
我们来看一下

200
00:04:55,980 --> 00:04:57,060
好

201
00:04:57,060 --> 00:04:57,880
我们在这输出

202
00:04:57,880 --> 00:04:59,380
然后接下来回到我们的命令行当中

203
00:04:59,380 --> 00:05:00,180
好

204
00:05:00,180 --> 00:05:02,160
我们在这里直接node05

205
00:05:02,160 --> 00:05:02,780
回车

206
00:05:02,780 --> 00:05:02,960
好

207
00:05:02,960 --> 00:05:03,520
我们在这里看到

208
00:05:03,520 --> 00:05:05,280
此时输出的结果就是helloworld

209
00:05:05,280 --> 00:05:05,720
好

210
00:05:05,720 --> 00:05:06,520
那么同样的这个语法

211
00:05:06,520 --> 00:05:07,180
它比较简单

212
00:05:07,180 --> 00:05:07,820
我们在这里可以给它

213
00:05:07,820 --> 00:05:09,420
稍微的来升级一下

214
00:05:09,420 --> 00:05:10,020
好

215
00:05:10,020 --> 00:05:11,780
我们在这里把上面的这个给它注一下

216
00:05:11,780 --> 00:05:12,440
他做掉以后

217
00:05:12,440 --> 00:05:12,980
然后接下来注意

218
00:05:12,980 --> 00:05:14,420
我们可以把它去掉一部分

219
00:05:14,420 --> 00:05:16,060
当然我们让他

220
00:05:16,060 --> 00:05:16,720
让这个字符串

221
00:05:16,720 --> 00:05:17,740
稍微复杂一些

222
00:05:17,740 --> 00:05:18,700
我们让他换个行

223
00:05:18,700 --> 00:05:20,800
例如我们在这里

224
00:05:20,800 --> 00:05:21,780
我们在这里来个什么呢

225
00:05:21,780 --> 00:05:22,120
就是例的

226
00:05:22,120 --> 00:05:22,920
我们在这里来个

227
00:05:22,920 --> 00:05:23,720
ATMARO部分

228
00:05:23,720 --> 00:05:25,500
例如我们来个这样的字符串

229
00:05:25,500 --> 00:05:26,700
这个字符串很明显

230
00:05:26,700 --> 00:05:26,900
注意

231
00:05:26,900 --> 00:05:27,320
它就是

232
00:05:27,320 --> 00:05:28,800
从本质上来讲

233
00:05:28,800 --> 00:05:29,400
它就是字符串

234
00:05:29,400 --> 00:05:29,940
对不对

235
00:05:29,940 --> 00:05:31,280
什么时候它才是ATMARO呢

236
00:05:31,280 --> 00:05:32,520
我们知道它是ATMARO格式

237
00:05:32,520 --> 00:05:33,620
它就到了浏览器

238
00:05:33,620 --> 00:05:35,660
浏览器是不是才会把它当作ATMARO格式

239
00:05:35,660 --> 00:05:36,120
进行渲染

240
00:05:36,120 --> 00:05:37,660
但是在我们这个NodeGIS中

241
00:05:37,660 --> 00:05:38,060
这是什么呀

242
00:05:38,060 --> 00:05:38,800
这是字符穿

243
00:05:38,800 --> 00:05:40,620
所以说一定要明白这个道理

244
00:05:40,620 --> 00:05:41,800
那么接下来我们注意

245
00:05:41,800 --> 00:05:42,780
那既然它是字符穿的话

246
00:05:42,780 --> 00:05:43,620
那它同样的

247
00:05:43,620 --> 00:05:44,960
是不是也可以来使用我们的数据

248
00:05:44,960 --> 00:05:46,300
例如我在这里

249
00:05:46,300 --> 00:05:47,440
hello双大括号

250
00:05:47,440 --> 00:05:48,100
好

251
00:05:48,100 --> 00:05:48,920
我来个message

252
00:05:48,920 --> 00:05:50,140
好那么这样写好以后

253
00:05:50,140 --> 00:05:50,500
注意

254
00:05:50,500 --> 00:05:51,760
这个时候我们再回到

255
00:05:51,760 --> 00:05:53,100
我们这个就是命令和当中

256
00:05:53,100 --> 00:05:55,180
此时我们再来执行

257
00:05:55,180 --> 00:05:56,340
那么现在我们就可以看到

258
00:05:56,340 --> 00:05:57,040
输出的结果

259
00:05:57,040 --> 00:05:58,720
还是注意字符穿

260
00:05:58,720 --> 00:05:59,640
那么这个时候

261
00:05:59,640 --> 00:06:00,520
我们看到这个HE当中

262
00:06:00,520 --> 00:06:01,860
是不是也是hello的

263
00:06:01,860 --> 00:06:02,840
也就是此时

264
00:06:02,840 --> 00:06:04,320
它已经不再是这个什么

265
00:06:04,320 --> 00:06:05,380
双大括号message了

266
00:06:05,380 --> 00:06:06,540
而是已经被什么呢

267
00:06:06,540 --> 00:06:07,540
是已经被模板引擎

268
00:06:07,540 --> 00:06:08,920
是把这个字符串当中

269
00:06:08,920 --> 00:06:10,080
是自己所能认识的

270
00:06:10,080 --> 00:06:10,660
这个刷大括号

271
00:06:10,660 --> 00:06:11,500
是给它替换了

272
00:06:11,500 --> 00:06:13,140
那么它绑的是message

273
00:06:13,140 --> 00:06:13,700
那么这个message

274
00:06:13,700 --> 00:06:14,360
是把数据中

275
00:06:14,360 --> 00:06:15,420
是找到数据中的message

276
00:06:15,420 --> 00:06:16,200
然后把这个值

277
00:06:16,200 --> 00:06:17,060
是给它替换到这个位置

278
00:06:17,060 --> 00:06:17,940
当然最终结果

279
00:06:17,940 --> 00:06:19,340
是不包含刷大括号的

280
00:06:19,340 --> 00:06:19,720
这就是链

281
00:06:19,720 --> 00:06:21,700
那接下来我们再来衍生一下

282
00:06:21,700 --> 00:06:22,900
例如我们把刚才

283
00:06:22,900 --> 00:06:23,360
这个toduz

284
00:06:23,360 --> 00:06:24,240
我们给它拿过来

285
00:06:24,240 --> 00:06:24,980
好

286
00:06:24,980 --> 00:06:25,820
那么这个就是

287
00:06:25,820 --> 00:06:27,720
例如它是对象的方式

288
00:06:27,720 --> 00:06:28,480
吃饭

289
00:06:28,480 --> 00:06:29,800
Completed

290
00:06:29,800 --> 00:06:31,640
这个就表示

291
00:06:31,640 --> 00:06:33,580
它这个任务是否已完成

292
00:06:33,580 --> 00:06:34,380
简单

293
00:06:34,380 --> 00:06:35,240
例如我们来三个

294
00:06:35,240 --> 00:06:40,220
那么这样提供好这份数据以后

295
00:06:40,220 --> 00:06:41,600
假如说我要把它确认到

296
00:06:41,600 --> 00:06:43,920
页面中的一个ULLI列表当中

297
00:06:43,920 --> 00:06:45,660
那这个时候我就需要这样写

298
00:06:45,660 --> 00:06:47,320
ULL然后LLI

299
00:06:47,320 --> 00:06:48,940
那么我们在LLI外面

300
00:06:48,940 --> 00:06:50,100
那这个时候我们可以用一下

301
00:06:50,100 --> 00:06:51,260
这个简单语法

302
00:06:51,260 --> 00:06:53,280
简单语法就是一起

303
00:06:53,280 --> 00:06:54,160
一起什么呢

304
00:06:54,160 --> 00:06:55,440
一起我们这个土肚子

305
00:06:55,440 --> 00:06:57,100
就是我们数据中的土肚子

306
00:06:57,100 --> 00:06:59,100
中间就是你的循环体

307
00:06:59,100 --> 00:07:00,840
这个就表示结束循环

308
00:07:00,840 --> 00:07:02,200
然后每循环一次

309
00:07:02,200 --> 00:07:04,020
我们是不是要输出他的这个title

310
00:07:04,020 --> 00:07:05,500
那么它这个title的输出

311
00:07:05,500 --> 00:07:06,540
我们可以再去通过一个叫

312
00:07:06,540 --> 00:07:08,020
$Value这样一个变量

313
00:07:08,020 --> 00:07:08,920
注意这是一个固定的

314
00:07:08,920 --> 00:07:09,940
在循环体当中

315
00:07:09,940 --> 00:07:11,620
$Value就表示我们的循环下

316
00:07:11,620 --> 00:07:12,540
好了

317
00:07:12,540 --> 00:07:14,820
然后接下来我们可以调出这个title

318
00:07:14,820 --> 00:07:16,620
当然后面这个completed表示

319
00:07:16,620 --> 00:07:17,900
就是它是否已完成的意思

320
00:07:17,900 --> 00:07:19,040
那么是否已完成的话

321
00:07:19,040 --> 00:07:19,840
我们可以在这里这样

322
00:07:19,840 --> 00:07:21,540
我们可以给它提供一个

323
00:07:21,540 --> 00:07:22,840
叫做checkbox

324
00:07:22,840 --> 00:07:24,700
我们这里放个checkbox

325
00:07:24,700 --> 00:07:27,320
那么checkbox是否选中

326
00:07:27,320 --> 00:07:28,280
那也就是说

327
00:07:28,280 --> 00:07:29,260
如果checkbox选中了

328
00:07:29,260 --> 00:07:30,360
就是表示这个容易完成的

329
00:07:30,360 --> 00:07:31,380
那在我们数据当中

330
00:07:31,380 --> 00:07:32,460
这completed是不是就表示

331
00:07:32,460 --> 00:07:33,260
它的完成状态

332
00:07:33,260 --> 00:07:33,860
那么现在的话

333
00:07:33,860 --> 00:07:34,960
我们就可以在这里这样

334
00:07:34,960 --> 00:07:36,940
我们就可以在这里双大块

335
00:07:36,940 --> 00:07:38,380
我们问一下

336
00:07:38,380 --> 00:07:39,520
BowlerValue

337
00:07:39,520 --> 00:07:41,100
跟Completed

338
00:07:41,100 --> 00:07:42,100
我们在这里问一下

339
00:07:42,100 --> 00:07:42,900
也就是说

340
00:07:42,900 --> 00:07:44,540
如果它Completed完成了

341
00:07:44,540 --> 00:07:45,500
那么我们就在这里

342
00:07:45,500 --> 00:07:47,280
就来个Teked

343
00:07:47,280 --> 00:07:49,020
否则就是一个什么呢

344
00:07:49,020 --> 00:07:49,800
是不是空字符串

345
00:07:49,800 --> 00:07:50,720
那这样的话

346
00:07:50,720 --> 00:07:51,720
我们就根据这个数据

347
00:07:51,720 --> 00:07:52,960
是在这去显示了

348
00:07:52,960 --> 00:07:54,120
这个显示了一个checkbox

349
00:07:54,120 --> 00:07:55,280
然后checkbox选中与否

350
00:07:55,280 --> 00:07:56,420
是取决于我们数据当中的

351
00:07:56,420 --> 00:07:57,720
Completed是True或者是False

352
00:07:57,720 --> 00:07:58,720
所以就是这样

353
00:07:58,720 --> 00:07:59,820
那么写好以后

354
00:07:59,820 --> 00:08:02,280
然后接下来回到我们这个命令行当中

355
00:08:02,280 --> 00:08:03,140
在命令行当中

356
00:08:03,140 --> 00:08:05,320
此时我们再来执行一下这个脚本

357
00:08:05,320 --> 00:08:06,100
执行完毕之后

358
00:08:06,100 --> 00:08:07,920
我们此时就可以看到

359
00:08:07,920 --> 00:08:08,960
在我们这个音频当中

360
00:08:08,960 --> 00:08:09,340
首先

361
00:08:09,340 --> 00:08:10,800
hello后面这个message

362
00:08:10,800 --> 00:08:11,920
是被变替换成了vote

363
00:08:11,920 --> 00:08:13,660
然后url中这个lead

364
00:08:13,660 --> 00:08:14,460
大家此时发现

365
00:08:14,460 --> 00:08:15,360
它已经不再是

366
00:08:15,360 --> 00:08:17,300
我们当时写的这个什么义起了

367
00:08:17,300 --> 00:08:18,920
是不是已经被便利出了

368
00:08:18,920 --> 00:08:19,880
根据我们的数据

369
00:08:19,880 --> 00:08:20,880
便利出了一个列表

370
00:08:20,880 --> 00:08:21,720
那这个列表当中

371
00:08:21,720 --> 00:08:22,720
是不是就由我们这个

372
00:08:22,720 --> 00:08:23,240
这三个数据

373
00:08:23,240 --> 00:08:23,920
吃饭睡觉

374
00:08:23,920 --> 00:08:24,500
打托动

375
00:08:24,500 --> 00:08:25,580
那么这三个input

376
00:08:25,580 --> 00:08:27,320
他们哪个input选中了呢

377
00:08:27,320 --> 00:08:28,120
就是说如果已完成

378
00:08:28,120 --> 00:08:28,700
就让它被选中

379
00:08:28,700 --> 00:08:29,820
那么是不是只有第二个

380
00:08:29,820 --> 00:08:30,860
这个睡觉的completion为true

381
00:08:30,860 --> 00:08:32,220
是不是还有这个checky的这个选项

382
00:08:32,220 --> 00:08:33,260
而其他的都没有

383
00:08:33,260 --> 00:08:34,120
因为他们没有完成

384
00:08:34,120 --> 00:08:35,840
所以就是这样的

385
00:08:35,840 --> 00:08:36,820
那这样的话

386
00:08:36,820 --> 00:08:37,360
我们就知道

387
00:08:37,360 --> 00:08:38,600
这个就是我们这个

388
00:08:38,600 --> 00:08:39,300
模板引擎的一个

389
00:08:39,300 --> 00:08:40,800
最基本的用法

390
00:08:40,800 --> 00:08:42,680
那有了它以后

391
00:08:42,680 --> 00:08:43,900
那接下来关键问题就是

392
00:08:43,900 --> 00:08:45,840
我们如何把这个页面

393
00:08:45,840 --> 00:08:46,700
就是给它渲

394
00:08:46,700 --> 00:08:47,700
或者说把这个页面

395
00:08:47,700 --> 00:08:49,620
输出到我们的网页当中

396
00:08:49,620 --> 00:08:50,440
也就是说

397
00:08:50,440 --> 00:08:51,720
我们要让这段代码

398
00:08:51,720 --> 00:08:54,280
是结合到我们的http服务当中

399
00:08:54,280 --> 00:08:54,680
对吧

400
00:08:54,680 --> 00:08:55,820
当然无论怎样

401
00:08:55,820 --> 00:08:56,920
我们待会就知道了一个质地

402
00:08:56,920 --> 00:08:59,120
就是模板引擎用来干嘛的

403
00:08:59,120 --> 00:09:01,400
是不是用来做字符串解析替换的呀

