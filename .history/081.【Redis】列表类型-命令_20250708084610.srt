1
00:00:00,000 --> 00:00:05,580
好 接下来我们来看一下怎么向我们的列表里面去添加或者操作它的元素

2
00:00:05,580 --> 00:00:07,980
 我们来看一下它的命令

3
00:00:07,980 --> 00:00:13,310
可量一下 首先像列表两端 刚才是不是讲到了列表它是一个双向列表

4
00:00:13,310 --> 00:00:15,000
 那么我们怎么向两端添加元素呢

5
00:00:15,000 --> 00:00:21,000
比如说有一个命令叫做l push 向左边 左边添加元素可以是什么呢 就是咱们数字的名称

6
00:00:21,000 --> 00:00:21,480
 比如说咱们去

7
00:00:21,480 --> 00:00:23,680
push一个lumbers

8
00:00:24,780 --> 00:00:28,120
1,好,说明成功了,我们添加了一个元素

9
00:00:28,120 --> 00:00:30,620
那么呢,此时我们是不是还可以向右边去曝醒啊

10
00:00:30,620 --> 00:00:32,560
比如说R曝醒

11
00:00:32,560 --> 00:00:38,260
蓝

12
00:00:38,260 --> 00:00:39,960
波尔斯2

13
00:00:39,960 --> 00:00:42,800
也就是说我们向左边曝醒了个1,向右边曝醒了个2

14
00:00:42,800 --> 00:00:43,800
那么这个时候

15
00:00:43,800 --> 00:00:46,360
我们的列表长什么样子啊

16
00:00:46,360 --> 00:00:52,780
向左边曝醒了个1,向右边曝醒了个2,咱们的列表呢,现在就是1和2

17
00:00:52,780 --> 00:00:53,380
好

18
00:00:54,580 --> 00:00:56,800
我们来看一下从列表两端弹出元素

19
00:00:56,800 --> 00:01:01,020
弹出元素是不是就是类似于咱们GS里面的POP

20
00:01:01,020 --> 00:01:02,840
或者是UNSHIFT

21
00:01:02,840 --> 00:01:04,180
好 我们来看一下

22
00:01:04,180 --> 00:01:05,440
L POP

23
00:01:05,440 --> 00:01:07,280
是不是从左边弹出

24
00:01:07,280 --> 00:01:08,520
R POP呢 从右边弹出

25
00:01:08,520 --> 00:01:11,200
比如说我们去

26
00:01:11,200 --> 00:01:13,100
好 等一下我们再来演示这样一个弹出

27
00:01:13,100 --> 00:01:13,900
因为呢 我们

28
00:01:13,900 --> 00:01:15,820
我们下面呢 需要有一个命令

29
00:01:15,820 --> 00:01:17,420
是获取列表的片段

30
00:01:17,420 --> 00:01:20,140
这里呢 我们需要把整个咱们的列表给打印出来

31
00:01:20,140 --> 00:01:21,340
打印出来呢 咱们才去演示

32
00:01:21,340 --> 00:01:22,200
弹出可能好

33
00:01:22,200 --> 00:01:22,740
好一点

34
00:01:22,740 --> 00:01:23,380
来看一下

35
00:01:23,380 --> 00:01:25,220
先来看一下获取列表中元素的个数

36
00:01:25,220 --> 00:01:25,220
LNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN

37
00:01:25,220 --> 00:01:30,760
啊呦还要嫩还要一恩咱们来看一下还要一恩南宝是不是二啊应该是

38
00:01:30,760 --> 00:01:39,060
哦不好意思这里了我写错了啊曝西安博士写错了重新曝习一下啊

39
00:01:39,060 --> 00:01:40,480
曝西

40
00:01:40,480 --> 00:01:41,900
男

41
00:01:41,900 --> 00:01:46,660
好我们再来看一下他的长度

42
00:01:46,660 --> 00:01:53,120
还要要一恩南宝大靠没有长度为二因为他此时他的列表是一和二

43
00:01:54,140 --> 00:01:55,420
那我们来多铺西点

44
00:01:55,420 --> 00:01:57,220
3

45
00:01:57,220 --> 00:02:00,800
是好这里的我们铺起了1234到一个列表里面去

46
00:02:00,800 --> 00:02:02,840
我们来看一下怎么样去获取列表片段

47
00:02:02,840 --> 00:02:04,900
那个命令叫做lrange

48
00:02:04,900 --> 00:02:07,200
lrange

49
00:02:07,200 --> 00:02:09,760
numbers什么呢

50
00:02:09,760 --> 00:02:10,780
start

51
00:02:10,780 --> 00:02:12,580
开始的索引和结束的索引

52
00:02:12,580 --> 00:02:16,160
比如说我们获取0到2获取三个元素123

53
00:02:16,160 --> 00:02:17,700
对吧

54
00:02:17,700 --> 00:02:24,100
那么这里有一个小技巧我们的输入lrange0和-1可以获取列表中所有元素是不是有点类似于我们的GS里面一个smiles

55
00:02:24,140 --> 00:02:29,140
-1什么意思 从左边的-1 是不是就是右边的最后一个呀 同学们 那我们来看一下

56
00:02:29,140 --> 00:02:33,140
-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1

57
00:02:33,140 --> 00:02:34,740
0-1 1 2 3 4

58
00:02:34,740 --> 00:02:39,200
对吧好 再来看一下我们的pop pop 这样一个方法

59
00:02:39,200 --> 00:02:41,860
我们来lpop numbers

60
00:02:41,860 --> 00:02:44,140
lpop是什么 是不是从左边弹出

61
00:02:44,140 --> 00:02:45,900
比如说我们来弹出一下

62
00:02:45,900 --> 00:02:47,680
好 走 咱们再来看一下

63
00:02:47,680 --> 00:02:50,440
是不是只剩下 应该只剩下234了 因为1被弹出了

64
00:02:50,440 --> 00:02:53,780
好 234 再来看一下双处列表中指定的值

65
00:02:53,780 --> 00:02:54,940
它是什么呢

66
00:02:54,940 --> 00:02:58,760
l21m 那这里有人可能会疑惑 我为什么不是

67
00:02:58,760 --> 00:03:00,020
l21l

68
00:03:00,820 --> 00:03:04,660
对吧 其实LREM它有一个作用是什么呢 你比如说我们去酸除

69
00:03:04,660 --> 00:03:06,460
Numbers中的

70
00:03:06,460 --> 00:03:09,780
Count 酸出几个 酸出一个 value是几

71
00:03:09,780 --> 00:03:11,060
从左边开始数

72
00:03:11,060 --> 00:03:12,860
LREM是从左边开始数

73
00:03:12,860 --> 00:03:16,180
数谁的数Numbers 数几个数1 数一个 算谁的算2

74
00:03:16,180 --> 00:03:19,500
好 算了一个 咱们把从左边开始数把2给酸掉了

75
00:03:19,500 --> 00:03:20,540
我们再来看一下

76
00:03:20,540 --> 00:03:25,660
现在咱们的列表排剩哪些内容 是不是只剩下34呢 因为我们把2给酸掉了

77
00:03:25,660 --> 00:03:30,520
那么其实LREM它是一个批量酸除的操作 所以不能够叫做LDEL

78
00:03:31,340 --> 00:03:33,900
那么咱们是不是有个参数叫做抗台刚才我们

79
00:03:33,900 --> 00:03:35,180
数了是一吧

80
00:03:35,180 --> 00:03:37,980
那么其实看他大概看到看了大于零时

81
00:03:37,980 --> 00:03:40,800
从LREM呢会从左边开始双处钱

82
00:03:40,800 --> 00:03:44,640
也就是说你输入唯一就有双处一个数二呢就说了双处两个

83
00:03:44,640 --> 00:03:45,920
那么如果说

84
00:03:45,920 --> 00:03:49,500
你数灵就是把所有的等于当前那个值的元素都给酸掉

85
00:03:49,500 --> 00:03:50,780
那么如果说小于0

86
00:03:50,780 --> 00:03:52,780
就会把LREM变成

87
00:03:52,780 --> 00:03:54,620
RM变成从右边双处

88
00:03:54,620 --> 00:03:56,160
好那么这里呢就是

89
00:03:56,160 --> 00:03:58,220
双处在一个命令

90
00:03:58,220 --> 00:04:00,260
到这里了我们来

91
00:04:01,580 --> 00:04:03,120
总结一下刚才我们所讲的哪些

92
00:04:03,120 --> 00:04:04,660
命令

93
00:04:04,660 --> 00:04:09,020
命令

94
00:04:09,020 --> 00:04:11,060
首先我们是不是讲到了

95
00:04:11,060 --> 00:04:12,080
怎么样去

96
00:04:12,080 --> 00:04:13,100
添加

97
00:04:13,100 --> 00:04:14,640
添加是什么

98
00:04:14,640 --> 00:04:16,180
l push和r

99
00:04:16,180 --> 00:04:17,720
分别是左边和右边

100
00:04:17,720 --> 00:04:20,540
那么添加之后我们是不是又讲了怎么获取长度呀

101
00:04:20,540 --> 00:04:21,560
ll1n

102
00:04:21,560 --> 00:04:23,600
后来呢是不是又讲了怎么去

103
00:04:23,600 --> 00:04:26,680
获取范围的列表是什么

104
00:04:26,680 --> 00:04:27,180
是不是

105
00:04:27,180 --> 00:04:29,240
哎呦

106
00:04:30,820 --> 00:04:31,940
l run 几啊

107
00:04:31,940 --> 00:04:37,480
好 那么获取范围之后 我们是不是又学习了一个酸除 哎呀 哎呀 为什么不是dl

108
00:04:37,480 --> 00:04:37,840
 因为

109
00:04:37,840 --> 00:04:42,040
他可以批量

110
00:04:42,040 --> 00:04:46,440
酸除 他接受一个 count 对吧 那么抗的等于就是全酸 抗的为正数呢

111
00:04:46,440 --> 00:04:48,800
 就是酸除几个为复数呢 就是从他相反的方向去

112
00:04:48,800 --> 00:04:52,720
酸除 好 其实呢 这几个命令呢 都比较简单 这里呢 就是这几个内容

