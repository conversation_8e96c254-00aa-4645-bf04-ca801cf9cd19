1
00:00:00,760 --> 00:00:02,340
好,这里我们就来看一下

2
00:00:02,340 --> 00:00:04,580
nodejs它怎么样去创建多进程

3
00:00:04,580 --> 00:00:06,480
以及它的多线程是怎么回事

4
00:00:06,480 --> 00:00:09,400
首先,伴随着10.5.0的发布

5
00:00:09,400 --> 00:00:11,880
nodejs新增了对多线程的实验性支持

6
00:00:11,880 --> 00:00:14,160
也就是work3s模块

7
00:00:14,160 --> 00:00:16,920
这里是老师查到了一篇文章

8
00:00:16,920 --> 00:00:18,920
我们来看一下

9
00:00:18,920 --> 00:00:21,820
好

10
00:00:21,820 --> 00:00:25,240
nodejs增多线程work3s突大

11
00:00:25,240 --> 00:00:29,360
首先work3s特性也就是多线程的特性

12
00:00:29,360 --> 00:00:32,420
是在2018年6月20日的V10.5版面引入了

13
00:00:32,420 --> 00:00:33,800
什么时候引入了2018年

14
00:00:33,800 --> 00:00:34,460
说明一个什么问题

15
00:00:34,460 --> 00:00:37,120
是不是说明LogS的多线程它是比较新的

16
00:00:37,120 --> 00:00:39,420
然后呢目前该模块属于

17
00:00:39,420 --> 00:00:40,420
这样一个阶段

18
00:00:40,420 --> 00:00:41,260
改动比较大

19
00:00:41,260 --> 00:00:42,080
不建议用于生产环境

20
00:00:42,080 --> 00:00:43,940
说明了咱们的LogS的多线程

21
00:00:43,940 --> 00:00:45,100
它刚诞生不久

22
00:00:45,100 --> 00:00:46,260
而且现在也不是很稳定

23
00:00:46,260 --> 00:00:47,380
不建议大家用于生产环境

24
00:00:47,380 --> 00:00:48,880
所以呢老师这里就不去

25
00:00:48,880 --> 00:00:50,700
介绍怎么样去创建多线程

26
00:00:50,700 --> 00:00:52,260
同学们也可以暂时不用去关注

27
00:00:52,260 --> 00:00:54,640
可以等LogS它多线程版本稳定之后

28
00:00:54,640 --> 00:00:55,240
我们再去研究

29
00:00:55,240 --> 00:00:57,800
好那么我们就来看一下

30
00:00:57,800 --> 00:01:00,380
在nodejs里面去怎么样去创建一个多进程

31
00:01:00,380 --> 00:01:02,500
首先

32
00:01:02,500 --> 00:01:07,940
首先这里呢

33
00:01:07,940 --> 00:01:11,480
我们老师呢写了一个http的服务器

34
00:01:11,480 --> 00:01:14,080
首先requirehttp create server

35
00:01:14,080 --> 00:01:16,900
在read里面呢直接去按着一个hello

36
00:01:16,900 --> 00:01:18,120
然后监听8000端口

37
00:01:18,120 --> 00:01:19,060
我们来run起来看一下

38
00:01:19,060 --> 00:01:22,900
其实刚才已经run了

39
00:01:22,900 --> 00:01:26,280
这里呢我们叫8000端口写的一个服务

40
00:01:26,280 --> 00:01:27,640
他当你访问的时候

41
00:01:27,640 --> 00:01:28,520
他直接会访问一个

42
00:01:28,520 --> 00:01:29,600
好喽

43
00:01:29,600 --> 00:01:30,660
那么我们来看一下

44
00:01:30,660 --> 00:01:31,540
效果

45
00:01:31,540 --> 00:01:34,520
localhost

46
00:01:34,520 --> 00:01:35,820
8000

47
00:01:35,820 --> 00:01:36,360
好喽

48
00:01:36,360 --> 00:01:38,040
这里呢

49
00:01:38,040 --> 00:01:38,740
是咱们通过

50
00:01:38,740 --> 00:01:41,340
单线程的方式

51
00:01:41,340 --> 00:01:42,340
去取得服务

52
00:01:42,340 --> 00:01:43,320
那么我们来看一下

53
00:01:43,320 --> 00:01:45,520
到底是不是怎么回事

54
00:01:45,520 --> 00:01:46,800
是不是我们现在

55
00:01:46,800 --> 00:01:47,220
弄的GIS

56
00:01:47,220 --> 00:01:48,700
它只有一个进程了

57
00:01:48,700 --> 00:01:49,660
然后呢

58
00:01:49,660 --> 00:01:50,360
有多个线程去

59
00:01:50,360 --> 00:01:52,580
处理这些服务之间的关系

60
00:01:52,580 --> 00:01:53,780
比如说GIS线程

61
00:01:53,780 --> 00:01:54,640
拉机回收

62
00:01:54,640 --> 00:01:55,720
刚才咱们是不是讲过

63
00:01:55,720 --> 00:01:56,840
好

64
00:01:56,840 --> 00:01:59,140
那么我们来看一下怎么样用多进程的方式去

65
00:01:59,140 --> 00:02:02,480
创建在一个http的服务

66
00:02:02,480 --> 00:02:03,240
首先

67
00:02:03,240 --> 00:02:06,560
这里呢 我们呢 需要用到class的模块

68
00:02:06,560 --> 00:02:09,120
class的模块 这里呢 老师先去

69
00:02:09,120 --> 00:02:09,640
写

70
00:02:09,640 --> 00:02:11,680
告诉大家怎么样去创建多进程

71
00:02:11,680 --> 00:02:14,760
稍后呢 我会去介绍他那些API 所以同学们不要着急

72
00:02:14,760 --> 00:02:16,300
我们先来看一下

73
00:02:16,300 --> 00:02:17,840
多进程他到底是怎么回事

74
00:02:17,840 --> 00:02:20,140
require代码很简单 同学们一定可以看懂

75
00:02:20,140 --> 00:02:23,200
首先咱们的requirecluster 他呢是nodejs的类置模块

76
00:02:23,200 --> 00:02:26,540
nodejs类置模块

77
00:02:26,840 --> 00:02:30,940
然后还需要咱们需要去引入HTTP的服务

78
00:02:30,940 --> 00:02:33,240
为什么呀

79
00:02:33,240 --> 00:02:38,100
是不是我们需要去进行HTTP的请求依赖于HTTP模块呀

80
00:02:38,100 --> 00:02:40,920
好这里呢我们既然启动多进程

81
00:02:40,920 --> 00:02:44,000
多进程它依赖于什么是不是依赖于咱们CPU有多少核

82
00:02:44,000 --> 00:02:46,560
有多少个核咱们现在是不是很流行8核

83
00:02:46,560 --> 00:02:50,140
4核8核16核大部分电脑现在都是CPU都是8核

84
00:02:50,140 --> 00:02:51,680
那么怎么样去获取

85
00:02:51,680 --> 00:02:53,200
获取咱们的CPU的

86
00:02:53,200 --> 00:02:56,020
核心数呢这里呢肉件是它类质的一个模块

87
00:02:56,020 --> 00:02:57,760
首先咱们CPU

88
00:02:57,760 --> 00:03:02,880
CPU的数量等于Require

89
00:03:02,880 --> 00:03:05,020
Require什么呢

90
00:03:05,020 --> 00:03:05,600
OS模块

91
00:03:05,600 --> 00:03:08,320
在Dollygs它的OS模块下面有一个CPU方法

92
00:03:08,320 --> 00:03:09,340
可以

93
00:03:09,340 --> 00:03:11,380
Lens可以获取了咱们CPU有多少个

94
00:03:11,380 --> 00:03:13,340
多少个核

95
00:03:13,340 --> 00:03:14,340
CPU核数

96
00:03:14,340 --> 00:03:22,760
那么这里呢

97
00:03:22,760 --> 00:03:24,420
接下来怎么去写

98
00:03:24,420 --> 00:03:26,480
首先我们启动多进程的时候

99
00:03:26,480 --> 00:03:29,440
cluster它的一个基本原理是什么呀

100
00:03:29,440 --> 00:03:32,000
基本原理老师跟大家先简单讲一下

101
00:03:32,000 --> 00:03:34,120
基本原理就是主线程

102
00:03:34,120 --> 00:03:36,440
去fork

103
00:03:36,440 --> 00:03:37,660
直线程

104
00:03:37,660 --> 00:03:40,940
然后管理它们

105
00:03:40,940 --> 00:03:42,000
那么fork是做什么的呀

106
00:03:42,000 --> 00:03:44,840
fork其实就是创建直线程的一个方法在cluster里面

107
00:03:44,840 --> 00:03:46,900
这里咱们先写个判断

108
00:03:46,900 --> 00:03:47,720
if

109
00:03:47,720 --> 00:03:50,180
cluster点

110
00:03:50,180 --> 00:03:51,180
一只什么呢

111
00:03:51,180 --> 00:03:51,580
一只master

112
00:03:51,580 --> 00:03:52,320
什么意思

113
00:03:52,320 --> 00:03:53,960
它的意思就是如果

114
00:03:53,960 --> 00:03:56,300
如果你是主线程

115
00:03:56,300 --> 00:03:57,360
因为刚才讲到了

116
00:03:57,360 --> 00:03:58,700
就是主线程去fork

117
00:03:58,700 --> 00:04:00,000
只线程然后去管理他们

118
00:04:00,000 --> 00:04:00,800
也就是说

119
00:04:00,800 --> 00:04:02,140
如果是主线程

120
00:04:02,140 --> 00:04:03,660
如果这一段介绍

121
00:04:03,660 --> 00:04:04,740
他在主线程里面去执行

122
00:04:04,740 --> 00:04:05,280
我们怎么样去

123
00:04:05,280 --> 00:04:06,400
我们怎么办

124
00:04:06,400 --> 00:04:08,580
cluster.fork

125
00:04:08,580 --> 00:04:10,400
咱们这个时候只需要去fork创建

126
00:04:10,400 --> 00:04:12,440
一个只线程就可以了

127
00:04:12,440 --> 00:04:13,580
else

128
00:04:13,580 --> 00:04:14,400
else什么意思

129
00:04:14,400 --> 00:04:15,420
else是不是就是

130
00:04:15,420 --> 00:04:16,900
只线程会执行他啊

131
00:04:16,900 --> 00:04:17,620
只线程走下面

132
00:04:23,960 --> 00:04:25,760
好 紫线程走下面

133
00:04:25,760 --> 00:04:29,840
走下面干嘛呢 是不是创建http服务 咱们把这一段粘过来

134
00:04:29,840 --> 00:04:35,480
好 这里了 我们就已经完成了

135
00:04:35,480 --> 00:04:37,020
通过cluster去创建一个

136
00:04:37,020 --> 00:04:39,060
多性成的load.js的模型

137
00:04:39,060 --> 00:04:41,360
但是不觉得很简单 其实就是这么简单

138
00:04:41,360 --> 00:04:43,160
我们来运行一下看一下

139
00:04:43,160 --> 00:04:47,520
load

140
00:04:47,520 --> 00:04:49,300
所以看一下ls

141
00:04:49,300 --> 00:04:51,860
loadhttp

142
00:04:51,860 --> 00:04:53,400
cluster.js

143
00:04:53,400 --> 00:04:55,440
好咱们的服务已经在8000端口启动了

144
00:04:55,440 --> 00:04:57,120
那么呢我们来访问一下

145
00:04:57,120 --> 00:05:00,820
是不是哈喽啊同学们那咱们再来活动监视器去看一下他有多少个进程

146
00:05:00,820 --> 00:05:04,720
两个进程

147
00:05:04,720 --> 00:05:09,160
大家看到没有两个进程其实这里呢代码稍微有点问题

148
00:05:09,160 --> 00:05:10,160
大家注意到没有

149
00:05:10,160 --> 00:05:12,860
是不是我们没有通过cpu的核数去

150
00:05:12,860 --> 00:05:15,380
for个子进程呢所以这里我们去写一个判断

151
00:05:15,380 --> 00:05:17,100
不对写个for循环for

152
00:05:17,100 --> 00:05:20,580
y=0

153
00:05:20,580 --> 00:05:23,020
i小于小于什么是不是cpu的核数

154
00:05:23,400 --> 00:05:33,620
然后呢执行cluster.fork开启直线程

155
00:05:33,620 --> 00:05:38,280
哎呦算了开启直径程

156
00:05:38,280 --> 00:05:42,980
好咱们再来看一下

157
00:05:42,980 --> 00:05:43,960
重启启动

158
00:05:43,960 --> 00:05:48,380
好大家看到没有

159
00:05:48,380 --> 00:05:51,840
721345678说明咱们是8核的

160
00:05:51,840 --> 00:05:56,840
对吧大家看到没有活动监视器进程名称露的露的露的露的咱们来访问一下可以吧

161
00:05:56,840 --> 00:05:58,840
好这里呢

162
00:05:58,840 --> 00:06:00,840
咱们来总结一下刚才内容

163
00:06:00,840 --> 00:06:07,840
首先在那个js里面它的多线程是不是伴随着10.5的发布它是不是很新啊什么时候

164
00:06:07,840 --> 00:06:11,840
2018年现在是哪一年2019年它刚出的新特性所以说咱们先不用去关注

165
00:06:11,840 --> 00:06:14,840
那怎么创建多性层是不是利用cluster

166
00:06:14,840 --> 00:06:19,840
cluster呢老师后面会去详细的讲刚才是不是我们讲到了怎么样去通过cluster去创建一个多性层

167
00:06:19,840 --> 00:06:22,400
首先咱们引入

168
00:06:22,400 --> 00:06:23,400
引入之后呢

169
00:06:23,400 --> 00:06:25,020
获取咱们CPU的核数

170
00:06:25,020 --> 00:06:25,960
咱们去判断

171
00:06:25,960 --> 00:06:27,400
如果说你是

172
00:06:27,400 --> 00:06:29,220
你是主进程

173
00:06:29,220 --> 00:06:30,880
咱们就去通过你的CPU核数去判断

174
00:06:30,880 --> 00:06:32,440
然后呢去fork它

175
00:06:32,440 --> 00:06:33,920
如果说你不是的

176
00:06:33,920 --> 00:06:35,280
咱们就去创造HTTP服务

177
00:06:35,280 --> 00:06:36,900
监听8000关口

178
00:06:36,900 --> 00:06:38,860
好这里呢

179
00:06:38,860 --> 00:06:40,820
cluster.onexceed

180
00:06:40,820 --> 00:06:42,940
就是去他cluster下面会去监听

181
00:06:42,940 --> 00:06:44,640
咱们只进程它的退出事件

182
00:06:44,640 --> 00:06:45,240
如果说退出

183
00:06:45,240 --> 00:06:46,920
就会打印出来你哪一个进程挂掉了

184
00:06:46,920 --> 00:06:48,620
它是为了防止咱们的进程出错

185
00:06:48,620 --> 00:06:50,220
这里后面也会去讲到

186
00:06:50,220 --> 00:06:52,760
好 这里就是这一节课的内容

