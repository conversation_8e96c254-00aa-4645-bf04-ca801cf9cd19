1
00:00:00,000 --> 00:00:01,680
好 我们来看一下定时任务

2
00:00:01,680 --> 00:00:04,000
会有许多的场景需要执行一些定时任务

3
00:00:04,000 --> 00:00:06,480
比如说我们定时的去上报应用的状态

4
00:00:06,480 --> 00:00:08,880
你比如说我们在服务器去部署了一个

5
00:00:08,880 --> 00:00:09,960
咱们的一个程序

6
00:00:09,960 --> 00:00:12,020
你是不是要不时的去问一下

7
00:00:12,020 --> 00:00:13,420
你还好不好在不在啊

8
00:00:13,420 --> 00:00:14,020
你运行的怎么样

9
00:00:14,020 --> 00:00:14,720
对吧

10
00:00:14,720 --> 00:00:15,560
第二个呢

11
00:00:15,560 --> 00:00:17,980
定时的从远程接口去更新本地的缓存

12
00:00:17,980 --> 00:00:18,800
为什么呀

13
00:00:18,800 --> 00:00:20,780
我们之前是不是讲过Reddit缓存了

14
00:00:20,780 --> 00:00:22,900
那么我们客户端去访问服务器的时候

15
00:00:22,900 --> 00:00:23,480
那么有一些

16
00:00:23,480 --> 00:00:25,140
频繁访问的文件

17
00:00:25,140 --> 00:00:26,740
我们是不是需要把它给缓存起来

18
00:00:26,740 --> 00:00:27,840
但是呢你又需要去更新

19
00:00:27,840 --> 00:00:29,900
那么我们定时的去更新这叫缓存

20
00:00:29,900 --> 00:00:31,440
就是一种方式

21
00:00:31,440 --> 00:00:32,780
那么第三个

22
00:00:32,780 --> 00:00:34,220
定时的对文件进行切割

23
00:00:34,220 --> 00:00:34,920
临时文件的删除

24
00:00:34,920 --> 00:00:35,700
什么意思

25
00:00:35,700 --> 00:00:37,000
比如说我们在服务器上面

26
00:00:37,000 --> 00:00:38,320
是不是会存储一些日子文件

27
00:00:38,320 --> 00:00:40,180
那么日子文件它会越来越来越来越来越大

28
00:00:40,180 --> 00:00:41,500
那么你肯定需要保持它的一个度

29
00:00:41,500 --> 00:00:42,460
所以你需要收拾的

30
00:00:42,460 --> 00:00:44,260
把它一些过期的日子给删掉

31
00:00:44,260 --> 00:00:46,460
这里就是定时任务的一个应用场景

32
00:00:46,460 --> 00:00:48,200
那么EGG的框架提供了一套机制

33
00:00:48,200 --> 00:00:49,800
让我们的定时任务的编写和维护

34
00:00:49,800 --> 00:00:50,560
更加优雅

35
00:00:50,560 --> 00:00:50,860
好

36
00:00:50,860 --> 00:00:51,520
那么我们就来看一下

37
00:00:51,520 --> 00:00:52,840
怎么样去写一个定时任务

38
00:00:52,840 --> 00:00:56,640
那么所有的定时动物它都统一放在APP的Cedule目标下面

39
00:00:56,640 --> 00:00:57,840
那么在一个单词是什么意思呢

40
00:00:57,840 --> 00:00:58,340
调度

41
00:00:58,340 --> 00:00:59,840
那么在RECT里面

42
00:00:59,840 --> 00:01:02,840
如果说有兴趣的同学可以去看一下它那个Favor的原码

43
00:01:02,840 --> 00:01:04,340
里面呢也有一个这样的关键字

44
00:01:04,340 --> 00:01:05,140
现在比较的火

45
00:01:05,140 --> 00:01:06,140
叫做调度

46
00:01:06,140 --> 00:01:06,940
好

47
00:01:06,940 --> 00:01:08,340
那么我们这里就来写一个

48
00:01:08,340 --> 00:01:10,340
我们这个定时动物来看一下

49
00:01:10,340 --> 00:01:13,640
我们是不是要创进一个什么呀

50
00:01:13,640 --> 00:01:15,140
Cedule文件夹

51
00:01:15,140 --> 00:01:17,840
然后呢我们在里面去比如说我们去写一个js

52
00:01:17,840 --> 00:01:20,040
我们呢定时的去打印一个log出来

53
00:01:22,340 --> 00:01:24,640
好 首先呢 在我们的

54
00:01:24,640 --> 00:01:27,720
首先呢 在我们的一GG上面 有一个subscribing

55
00:01:27,720 --> 00:01:28,740
在那个类

56
00:01:28,740 --> 00:01:34,120
有一个subscribing在那个类 那么有的东西可能会问

57
00:01:34,120 --> 00:01:36,940
为什么不是 schedule在那个类 而是subscribing

58
00:01:36,940 --> 00:01:38,720
你比如说我们的控制器 它是controller

59
00:01:38,720 --> 00:01:41,800
对吧 我们的service 它就是service 但是为什么我们的定时任务

60
00:01:41,800 --> 00:01:43,340
它是subscribing

61
00:01:43,340 --> 00:01:45,380
这里呢 希望同学们去注意

62
00:01:45,380 --> 00:01:46,400
它呢 不是

63
00:01:46,400 --> 00:01:49,740
是 schedule 那么这里呢 为什么呢 其实我也不知道

64
00:01:49,740 --> 00:01:52,300
是吧 好 我们来看一下

65
00:01:52,340 --> 00:01:55,900
怎么去写 比如说我们就写了一个logsubscription

66
00:01:55,900 --> 00:01:58,540
那么这里呢其实也叫做定书的我 我们来去继承一下它

67
00:01:58,540 --> 00:02:02,240
好 首先呢 我们需要去定一个静态方法

68
00:02:02,240 --> 00:02:06,740
static schedule get schedule

69
00:02:06,740 --> 00:02:10,940
然后呢 我们需要给它两个属性 一个是叫做interwar

70
00:02:10,940 --> 00:02:14,040
那么interwar是不是很好理解啊 你的一个间隔时间是多少

71
00:02:14,040 --> 00:02:16,040
好 我来看一下语法是不是有点问题

72
00:02:16,040 --> 00:02:18,540
get schedule 咱们需要去retend一个对象

73
00:02:18,540 --> 00:02:20,840
retend一个对象

74
00:02:21,840 --> 00:02:24,200
比如说我们去输入一个参数inter

75
00:02:24,200 --> 00:02:27,040
那么这里呢其实这样一个方法是什么呀

76
00:02:27,040 --> 00:02:31,540
这样一个进展方法就是咱们去配置定时任务的

77
00:02:31,540 --> 00:02:33,960
你比如说我们是不是需要去设计一些间隔时间的

78
00:02:33,960 --> 00:02:36,100
你定时任务肯定需要告诉咱们的一个任务嘛

79
00:02:36,100 --> 00:02:36,860
你多少时间执行次

80
00:02:36,860 --> 00:02:39,300
比如说我们去定义三秒钟执行一次

81
00:02:39,300 --> 00:02:41,420
那么它其实还有一个参数叫做type

82
00:02:41,420 --> 00:02:43,360
那么我们type其实它有两种

83
00:02:43,360 --> 00:02:46,460
type一个是叫做word一个是walker

84
00:02:46,460 --> 00:02:46,960
什么意思

85
00:02:46,960 --> 00:02:50,440
我们的任务启动的时候是不是会令我们CPU的多核

86
00:02:50,440 --> 00:02:52,500
那么呢会启动很多的walkers去帮助我们去调度

87
00:02:52,500 --> 00:02:54,000
那么如果说你传入walk的话

88
00:02:54,000 --> 00:02:54,560
就呢

89
00:02:54,560 --> 00:02:57,120
所有的walkers都会去帮助你去进行这样一个定时任务

90
00:02:57,120 --> 00:02:58,240
那么如果说你传入walkers

91
00:02:58,240 --> 00:02:59,340
那么呢如果说你是bacher

92
00:02:59,340 --> 00:03:02,000
他只会选择其中的一个walkers去执行我们的定时任务

93
00:03:02,000 --> 00:03:02,380
什么意思

94
00:03:02,380 --> 00:03:04,080
你如果说你的任务非常繁重

95
00:03:04,080 --> 00:03:04,800
需要大量的计算

96
00:03:04,800 --> 00:03:05,440
你可以选择walk

97
00:03:05,440 --> 00:03:06,840
但是如果说你的任务比较轻量

98
00:03:06,840 --> 00:03:08,240
你可能你可以选择walkers

99
00:03:08,240 --> 00:03:10,820
好这里呢我们就选择walkers吧

100
00:03:10,820 --> 00:03:11,860
因为我们只是打个log

101
00:03:11,860 --> 00:03:15,640
好然后这里呢我们呢还需要去定义一个方法

102
00:03:15,640 --> 00:03:17,300
我们定义了sync

103
00:03:17,300 --> 00:03:19,080
subscribe

104
00:03:19,080 --> 00:03:22,720
subscribe 叫做subscribe

105
00:03:22,720 --> 00:03:27,860
好 这里呢就是我们定时 定时的 定时需要执行的一个任务 比如说我们去打印一个

106
00:03:27,860 --> 00:03:32,580
我是定时任务 好 那么我们来run一下

107
00:03:32,580 --> 00:03:38,500
好 这里呢 同学们来拆一下 我们run起来之后再一个定时任务有没有执行

108
00:03:38,500 --> 00:03:45,780
我们是不是忘记了 忘记了把它给导出啊 好 现在我们导出了 那么同学们觉得他会不会执行

109
00:03:45,780 --> 00:03:47,740
好 我们就来看一下

110
00:03:47,740 --> 00:03:52,160
好 报错了 看一下到底是什么原因

111
00:03:52,160 --> 00:03:56,680
Must have statute and task

112
00:03:56,680 --> 00:03:58,720
Logscribing

113
00:03:58,720 --> 00:04:02,080
好 我看一下我们是不是有哪个字段写错了啊 大家稍等一下

114
00:04:02,080 --> 00:04:04,500
好 我把它直接给张过来看一下

115
00:04:04,500 --> 00:04:07,600
我直接把它给张过来

116
00:04:07,600 --> 00:04:09,120
比如说我去定义上面

117
00:04:09,120 --> 00:04:11,620
好 我们来重启一下

118
00:04:16,240 --> 00:04:21,240
大家可以看到 我们的定时任务是不是已经开始执行了呀 对吧 它呢 每个商标就会打印出一个字幕串出来

119
00:04:21,240 --> 00:04:24,440
我是定时任务说明一个什么问题 是不是说明 我们的定时任务已经在执行了

120
00:04:24,440 --> 00:04:26,480
那么有的同学可能会有疑问 什么疑问呢

121
00:04:26,480 --> 00:04:27,500
我们的

122
00:04:27,500 --> 00:04:30,320
log.js 他有没有在任何地方被引用啊

123
00:04:30,320 --> 00:04:34,680
没有吧 那么我们的任务为什么能执行 其实这里呢 就是一击击你帮我们已经封装好了

124
00:04:34,680 --> 00:04:38,640
他只要检测到你有了 这样的一个是这样的一个文件夹 然后里面的js

125
00:04:38,640 --> 00:04:41,080
 他都会去执行 然后去他执行一些定时任务

126
00:04:41,320 --> 00:04:44,400
那么如果说不信的同学 我们可以再建一个件事 比如说我们叫做log2.js

127
00:04:44,400 --> 00:04:48,480
他是不是会执行里面所有的js呢 那我们就来看一下 比如说 这里呢

128
00:04:48,480 --> 00:04:52,080
 我是定商务2 我们每个2秒执行一次 好 我们来看一下

129
00:04:52,080 --> 00:05:00,780
logsubscribing 我们把它的类给改一下

130
00:05:00,780 --> 00:05:03,600
好 我们来重启一下

131
00:05:07,940 --> 00:05:11,020
好 其实大家这里是不是可以看到 我们是不是同时执行了两个定时任务

132
00:05:11,020 --> 00:05:13,060
 对吧 每隔两秒的执行一个

133
00:05:13,060 --> 00:05:16,640
执行一个他 然后呢 每个三秒执行一个他 所以说我们只需要

134
00:05:16,640 --> 00:05:21,760
我们是不是只需要在我们的 schedule 在那个文件夹里面去添加件事 他就会自动的去执行我们的一个定时任务

135
00:05:21,760 --> 00:05:23,560
那我们刚才讲了

136
00:05:23,560 --> 00:05:27,400
讲了去执行定时任务的其中一种语法 那么其实呢 还会有另外一种写法

137
00:05:27,400 --> 00:05:30,460
 怎么写呢 我们直接摸点export 咱们呢 去导出一个

138
00:05:30,460 --> 00:05:31,740
直接去导出一个对象

139
00:05:32,260 --> 00:05:37,000
然后在里面去定义两个属性 一个是一个是在那个k 那么我们去对他进行一些配置

140
00:05:37,000 --> 00:05:40,400
 然后下面就是叫做一个task的字段 那么我们就来看一下到底是怎么回事

141
00:05:40,400 --> 00:05:48,290
好 我们来把间隔改为上面 这里呢 是不添加了一个是在那个属性 然后有一个async

142
00:05:48,290 --> 00:05:49,880
 task 我是定时任务

143
00:05:49,880 --> 00:05:54,120
这里咱们把type改为walker 那么其实和刚才我们的效果一段时间来讲应该是一样的

144
00:05:54,120 --> 00:05:57,480
好 我们来重新批动一下来看一下 是不是怎么回事

145
00:06:00,580 --> 00:06:03,960
好大家看到是不是已经执行了和我们刚才是一样的效果

146
00:06:03,960 --> 00:06:06,760
所以说到这里的定时任务的他其实有两种语法去书写

147
00:06:06,760 --> 00:06:10,600
你可以爱你的爱好随便选择哪种对吧

148
00:06:10,600 --> 00:06:12,040
好那么他能接受一些参数

149
00:06:12,040 --> 00:06:13,980
我们刚才是不是已经介绍过来一个interval

150
00:06:13,980 --> 00:06:14,800
是不是我们的时间呢

151
00:06:14,800 --> 00:06:17,060
包括了一个type那么type呢

152
00:06:17,060 --> 00:06:18,280
其实我呢也已经解释过了

153
00:06:18,280 --> 00:06:20,920
好那么我们再来总结一下我们的一个定时任务

154
00:06:20,920 --> 00:06:26,820
定时任务那么定时务它的作用是什么呀

155
00:06:26,820 --> 00:06:27,320
它的作用

156
00:06:29,440 --> 00:06:33,020
我们是不是可以去比如说我们去定期的是不是是不是去更新缓存的

157
00:06:33,020 --> 00:06:34,820
对吧然后呢

158
00:06:34,820 --> 00:06:36,360
是不是可以定期的

159
00:06:36,360 --> 00:06:43,000
删除日资啊包括了你还可以去定期的做一些啊其他的一些事情比如说你去查询服务器的状态对吧

160
00:06:43,000 --> 00:06:51,960
好那么这也是他作用那么我们怎么去书写了书写是不是两种方式一种通过class吧一种的通过他们导出一个

161
00:06:51,960 --> 00:06:57,600
导出一个对象了对吧他两种方式那么他呢是不是还会接受一些

162
00:06:58,620 --> 00:07:01,120
参数咱们最常用的其实有两个一个英特朗

163
00:07:01,120 --> 00:07:03,820
他那个间隔时间还有呢一个是太普咱们一个类型

164
00:07:03,820 --> 00:07:06,100
而定时动物他呢还会有一些更复杂的用法

165
00:07:06,100 --> 00:07:09,100
那么希望同学们可以自己去看一下他里面的一个文档

166
00:07:09,100 --> 00:07:11,100
好那么这里呢就是我们这节奏的内容

