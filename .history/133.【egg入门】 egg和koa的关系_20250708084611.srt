1
00:00:00,000 --> 00:00:02,900
好 这节课我们就来看一下1GG它与Core的关系

2
00:00:02,900 --> 00:00:06,120
刚才我们是不是讲到了1GG它是不是基于Core去进行一个二次开发

3
00:00:06,120 --> 00:00:08,420
那么Core它是一个非常优秀的框架

4
00:00:08,420 --> 00:00:10,420
然而对企业级应用来说它还比较基础

5
00:00:10,420 --> 00:00:14,120
因为我们之前去使用的时候Core是不是还需要自己去引用很多的一些中间件

6
00:00:14,120 --> 00:00:17,260
那么1GG它选择了Core作为其基础框架

7
00:00:17,260 --> 00:00:20,040
在它的模型基础上进一步对它进行了一些增强

8
00:00:20,040 --> 00:00:21,840
比如说我们举一个例子我们可以去扩展

9
00:00:21,840 --> 00:00:26,040
像咱们的你比如说我们在APP的extend

10
00:00:26,040 --> 00:00:27,300
extend是什么意思是不是扩展

11
00:00:27,300 --> 00:00:29,080
下面呢写一个context.js

12
00:00:29,080 --> 00:00:30,900
我们去扩展一个什么功能呢

13
00:00:30,900 --> 00:00:32,180
比如说我们去扩展了一个

14
00:00:32,180 --> 00:00:33,420
EaseALS

15
00:00:33,420 --> 00:00:34,700
也就是说我们可以去判断

16
00:00:34,700 --> 00:00:35,520
你当前的客户端

17
00:00:35,520 --> 00:00:36,980
是不是ALS客户端

18
00:00:36,980 --> 00:00:37,960
那么我们呢

19
00:00:37,960 --> 00:00:39,340
其实可以去把这个方法

20
00:00:39,340 --> 00:00:40,380
在一级记里面

21
00:00:40,380 --> 00:00:41,500
咱们可以直接挂载到

22
00:00:41,500 --> 00:00:43,720
context这样一个对象下面

23
00:00:43,720 --> 00:00:45,260
那么context来源于哪里

24
00:00:45,260 --> 00:00:46,760
context是不是来源于

25
00:00:46,760 --> 00:00:47,060
Qua

26
00:00:47,060 --> 00:00:49,100
那么Qua我们可不可以直接去扩展context

27
00:00:49,100 --> 00:00:50,700
好像是不可以吧

28
00:00:50,700 --> 00:00:51,880
你除非去自己引用一些

29
00:00:51,880 --> 00:00:53,080
中间键写些逼的我

30
00:00:53,080 --> 00:00:55,160
那么在一级记里面

31
00:00:55,160 --> 00:00:56,580
你可以通过它的约定的方式

32
00:00:56,580 --> 00:00:57,820
你比如说在APP这样一个目录下面

33
00:00:57,820 --> 00:00:59,060
extend是它所约定的

34
00:00:59,080 --> 00:01:03,880
然后有个context.gs你在下面写的所有的方法都会在EGG里面默认的给挂着

35
00:01:03,880 --> 00:01:05,320
context这样一个对象下面去

36
00:01:05,320 --> 00:01:06,800
这里呢只是给同学去了解一下

37
00:01:06,800 --> 00:01:09,460
在后面的课程呢我们会具体去讲怎么样去对它进行去扩展

38
00:01:09,460 --> 00:01:10,380
而且

39
00:01:10,380 --> 00:01:14,500
EGG它不仅提供了扩展功能还提供了一些插件的功能

40
00:01:14,500 --> 00:01:17,380
你比如说在Core中我们经常会引入一些中间件

41
00:01:17,380 --> 00:01:21,780
那么你比如说我们之前是不是讲到了通过Core的玻璃来解析咱们的一些请求

42
00:01:21,780 --> 00:01:24,160
那么EGG呢它提供了一些更强大的插件机制

43
00:01:24,160 --> 00:01:25,180
什么意思呢

44
00:01:25,180 --> 00:01:27,120
我们的Core是不是有中间建Midwall

45
00:01:27,120 --> 00:01:30,060
那么其实1GG它额外提供了两种扩展的方式

46
00:01:30,060 --> 00:01:32,180
它除了中间键还有一个叫做一个Send

47
00:01:32,180 --> 00:01:33,860
一个Send是不是叫做扩展呢

48
00:01:33,860 --> 00:01:35,660
包括config它会有一些自己的配置

49
00:01:35,660 --> 00:01:38,400
那么这里呢我们是不是可以得出一个结论

50
00:01:38,400 --> 00:01:39,340
是什么呀

51
00:01:39,340 --> 00:01:43,400
是不是我们1GG它相比Core它这个扩展能力更强啊

52
00:01:43,400 --> 00:01:46,220
好那么我们就来总结一下刚才所讲解的内容

53
00:01:46,220 --> 00:01:54,200
我们刚才是不是讲到了1GG与Core的关系

54
00:01:54,200 --> 00:02:00,060
那么1GG是不是继承至Core

55
00:02:00,060 --> 00:02:02,480
但是呢1GG是不是可以继续Core去扩展

56
00:02:02,480 --> 00:02:09,100
那么它

57
00:02:09,100 --> 00:02:10,980
那么它可以怎么样去

58
00:02:10,980 --> 00:02:13,560
怎么样去扩展呢

59
00:02:13,560 --> 00:02:15,500
是不是除了我们的一个中间建的概念

60
00:02:15,500 --> 00:02:17,640
它是不是还有自己的一个叫做extended的概念

61
00:02:17,640 --> 00:02:18,460
你比如说可以在

62
00:02:18,460 --> 00:02:21,280
是不是在咱们的context对象上面去挂在一些属性

63
00:02:21,280 --> 00:02:27,520
或者方法

64
00:02:27,520 --> 00:02:28,420
除了extend

65
00:02:28,420 --> 00:02:30,540
是不是还有一个叫做什么呀

66
00:02:30,540 --> 00:02:33,120
也就是它会去扩展一些配置文件

67
00:02:33,120 --> 00:02:33,500
好

68
00:02:33,500 --> 00:02:35,520
这里就是咱们这节课的内容

