1
00:00:00,000 --> 00:00:01,560
好 这节目我们就来看一下插件

2
00:00:01,560 --> 00:00:03,320
那么插件我们之前有没有使用过

3
00:00:03,320 --> 00:00:05,600
同学们回忆一下 我们有使用过插件吗

4
00:00:05,600 --> 00:00:07,400
其实有吧

5
00:00:07,400 --> 00:00:08,320
其实大家可以看一下

6
00:00:08,320 --> 00:00:11,420
我们APP里面是不是config有一个plugin.js

7
00:00:11,420 --> 00:00:12,640
我们是不是引入过一个

8
00:00:12,640 --> 00:00:14,460
1gzview-lunchex

9
00:00:14,460 --> 00:00:15,060
它是做什么呢

10
00:00:15,060 --> 00:00:16,900
是不是进行我们模板渲染的

11
00:00:16,900 --> 00:00:18,460
那么我们引入在一个插件之后

12
00:00:18,460 --> 00:00:21,080
在我们的GS里面是不是可以直接调用run的方法

13
00:00:21,080 --> 00:00:22,620
那么没有插件之前我们有没有run的方法

14
00:00:22,620 --> 00:00:23,320
没有吧

15
00:00:23,320 --> 00:00:25,680
这里呢就是我们使用插件的一个场景

16
00:00:25,680 --> 00:00:27,680
那么我们来看一下它的介绍

17
00:00:27,680 --> 00:00:29,740
插件机制是我们框架里的特色

18
00:00:29,740 --> 00:00:32,820
它不但可以保证框架核心足够精简稳定高效

19
00:00:32,820 --> 00:00:34,820
还可以根据业务逻辑的复用生态圈的形成

20
00:00:34,820 --> 00:00:35,540
说明一个什么问题

21
00:00:35,540 --> 00:00:37,260
说明插件它非常的重要

22
00:00:37,260 --> 00:00:40,180
那么在括号中其实已经有了中间件的机制

23
00:00:40,180 --> 00:00:41,360
我们为什么还需要插件呢

24
00:00:41,360 --> 00:00:43,000
其实这样一个问题

25
00:00:43,000 --> 00:00:44,700
我们刚才的模板是不是可以解决这样一个问题

26
00:00:44,700 --> 00:00:46,800
就可以给同学们去回答这样一个问题

27
00:00:46,800 --> 00:00:48,480
我们的模板去渲染的时候

28
00:00:48,480 --> 00:00:49,500
中间件能不能解决这样一个问题

29
00:00:49,500 --> 00:00:50,640
不可以吧

30
00:00:50,640 --> 00:00:51,020
而且

31
00:00:51,020 --> 00:00:52,740
而且呢

32
00:00:52,740 --> 00:00:55,700
中间件和应用之间它们有什么关系或者区别呢

33
00:00:55,700 --> 00:00:56,940
那么我们就一起来看一下

34
00:00:56,940 --> 00:00:59,880
这里是我从官方里面

35
00:00:59,880 --> 00:01:00,980
他去栽录了一段话

36
00:01:00,980 --> 00:01:02,680
使用COA中间键的过程中

37
00:01:02,680 --> 00:01:03,800
发现了下面的一些问题

38
00:01:03,800 --> 00:01:05,580
也就是一级集团队

39
00:01:05,580 --> 00:01:07,580
他们前期早期是使用COA去做项目的

40
00:01:07,580 --> 00:01:08,620
那么在做项目的过程中

41
00:01:08,620 --> 00:01:09,160
他发现了一些问题

42
00:01:09,160 --> 00:01:09,820
第一个

43
00:01:09,820 --> 00:01:12,080
中间键的加载其实是有先后顺序的

44
00:01:12,080 --> 00:01:13,940
但是中间键自身却无法管理这种顺序

45
00:01:13,940 --> 00:01:16,420
大家还记得洋葱模型吗

46
00:01:16,420 --> 00:01:17,540
我们是不是每天叫一个中间键

47
00:01:17,540 --> 00:01:18,680
就相当于在咱们洋葱的最外层

48
00:01:18,680 --> 00:01:19,220
添加了一个壳

49
00:01:19,220 --> 00:01:20,720
那么这些壳之间的顺序

50
00:01:20,720 --> 00:01:21,820
是不是无法去改变

51
00:01:21,820 --> 00:01:23,200
那么我们加载中间件

52
00:01:23,200 --> 00:01:24,020
其实需要去管理

53
00:01:24,020 --> 00:01:24,480
这样一个顺序

54
00:01:24,480 --> 00:01:25,160
如果说顺序不对

55
00:01:25,160 --> 00:01:26,300
我们的结果可能会出错

56
00:01:26,300 --> 00:01:28,040
第二个

57
00:01:28,040 --> 00:01:29,580
中间的定位是什么呀

58
00:01:29,580 --> 00:01:30,220
我们的洋出模型

59
00:01:30,220 --> 00:01:31,480
是不是request进去

60
00:01:31,480 --> 00:01:32,640
response的出来呀

61
00:01:32,640 --> 00:01:33,620
它是不是核心是

62
00:01:33,620 --> 00:01:34,520
做咱们的用户请求

63
00:01:34,520 --> 00:01:35,440
但是如果说

64
00:01:35,440 --> 00:01:36,300
你有一些其他的事情

65
00:01:36,300 --> 00:01:36,900
比如说健全

66
00:01:36,900 --> 00:01:37,840
安全检查

67
00:01:37,840 --> 00:01:38,660
日志等等

68
00:01:38,660 --> 00:01:39,100
对吧

69
00:01:39,100 --> 00:01:40,260
而且还有很多功能

70
00:01:40,260 --> 00:01:41,080
是和请求无关的

71
00:01:41,080 --> 00:01:41,900
包括像定时任务

72
00:01:41,900 --> 00:01:42,420
消息订阅

73
00:01:42,420 --> 00:01:43,040
后台逻辑等等

74
00:01:43,040 --> 00:01:43,680
我们的口法

75
00:01:43,680 --> 00:01:44,780
是不是只提供了

76
00:01:44,780 --> 00:01:45,740
咱们对请求和

77
00:01:45,740 --> 00:01:46,960
是不是和响应的一个封装

78
00:01:46,960 --> 00:01:49,100
那么对这些和请求无关的

79
00:01:49,100 --> 00:01:50,460
是不是没有提供一个很好的机制

80
00:01:50,460 --> 00:01:52,020
而且呢

81
00:01:52,020 --> 00:01:52,620
有一些功能

82
00:01:52,620 --> 00:01:54,740
它包含一些非常复杂的初始化逻辑

83
00:01:54,740 --> 00:01:56,660
需要在咱们应用启动的时候完成

84
00:01:56,660 --> 00:01:57,520
你比如说

85
00:01:57,520 --> 00:01:58,740
我们之前是不是写了个椅子LS

86
00:01:58,740 --> 00:02:00,840
在context下面去扩展了一个方法

87
00:02:00,840 --> 00:02:01,460
对吧

88
00:02:01,460 --> 00:02:03,000
包括我们前面在讲什么

89
00:02:03,000 --> 00:02:03,520
是不是讲

90
00:02:03,520 --> 00:02:05,080
我们的一个键进式应用

91
00:02:05,080 --> 00:02:05,500
对吧

92
00:02:05,500 --> 00:02:07,360
我们是不是需要一层一层的去封装

93
00:02:07,360 --> 00:02:07,920
这里呢

94
00:02:07,920 --> 00:02:11,500
都是我们中间键之外的一些功能

95
00:02:11,500 --> 00:02:12,860
那么什么是

96
00:02:12,860 --> 00:02:14,140
那么到底什么是一个差件呢

97
00:02:14,140 --> 00:02:15,920
其实我们可以总结一下

98
00:02:15,920 --> 00:02:19,660
它呢包含了service中间键框架配置等等

99
00:02:19,660 --> 00:02:23,020
但是它没有独立的root和controller也没有了plugin.js

100
00:02:23,020 --> 00:02:24,640
那么我们直观一点来讲

101
00:02:24,640 --> 00:02:26,620
比如说我们现在的app

102
00:02:26,620 --> 00:02:29,680
我们现在的比如说我们chapter2下面是不是有一些文件夹

103
00:02:29,680 --> 00:02:31,980
那么这一整个它都可以当成

104
00:02:31,980 --> 00:02:34,240
我们呢可以把这一整块都当成一个插件

105
00:02:34,240 --> 00:02:35,100
但是它里面

106
00:02:35,100 --> 00:02:38,920
但是大家注意它里面是没有controller和咱们的一个root

107
00:02:38,920 --> 00:02:41,260
也就是说如果说我们可以再去理解

108
00:02:41,260 --> 00:02:44,400
我们把controller和我们的一个root给化掉

109
00:02:44,400 --> 00:02:45,700
你就可以理解为剩下的内容

110
00:02:45,700 --> 00:02:47,620
我们可以把它作为一个插件去使用

111
00:02:47,620 --> 00:02:49,040
那么为什么插件里面

112
00:02:49,040 --> 00:02:52,360
我们一般不写router和咱们的一个controller呢

113
00:02:52,360 --> 00:02:54,700
因为我们的插件是不是不处理具体的用逻辑

114
00:02:54,700 --> 00:02:56,720
你比如说你需要一个模板的render功能

115
00:02:56,720 --> 00:02:58,420
你是不是不需要在里面去写router和controller

116
00:02:58,420 --> 00:02:58,800
对吧

117
00:02:58,800 --> 00:03:00,900
因为这里会需要我们在应用程序处理

118
00:03:00,900 --> 00:03:02,620
好

119
00:03:02,620 --> 00:03:03,940
我们来看一下怎么样去使用插件

120
00:03:03,940 --> 00:03:05,300
那么其实使用插件

121
00:03:05,300 --> 00:03:07,660
我们之前是不是在使用咱们的一个

122
00:03:07,660 --> 00:03:09,860
lanjax模板的时候就已经演示过了呀

123
00:03:09,860 --> 00:03:11,080
比如说我们先去npr名store

124
00:03:11,080 --> 00:03:12,040
比如说我们现在去

125
00:03:12,040 --> 00:03:13,400
咱们的奖页里面是买收口

126
00:03:13,400 --> 00:03:15,600
然后我们在plugin.js里面去把它给引入

127
00:03:15,600 --> 00:03:18,240
然后在config里面去做一些相关的配置

128
00:03:18,240 --> 00:03:19,280
那么这里呢

129
00:03:19,280 --> 00:03:21,740
我来带同学们直接回顾一下我们的什么呀

130
00:03:21,740 --> 00:03:23,220
是不是回顾一下我们的模板是怎么样去做的

131
00:03:23,220 --> 00:03:26,660
首先我们是不是在config里面有一个plugin.js

132
00:03:26,660 --> 00:03:27,420
咱们引入什么呀

133
00:03:27,420 --> 00:03:29,180
是不是引入一个lanjacks这样的一个对象

134
00:03:29,180 --> 00:03:30,540
然后enable告诉呢

135
00:03:30,540 --> 00:03:31,740
告诉我们的一个配置

136
00:03:31,740 --> 00:03:32,840
说我们要使用这样一个插件

137
00:03:32,840 --> 00:03:34,780
然后呢package指定我们要使用的是哪一个包

138
00:03:34,780 --> 00:03:36,480
然后在config里面

139
00:03:36,480 --> 00:03:39,060
我们在config里面是不是有一个lanjacks这样的字段

140
00:03:39,060 --> 00:03:39,760
对它去做一些

141
00:03:39,760 --> 00:03:41,040
比如说我们有一个view这样的字段

142
00:03:41,040 --> 00:03:42,600
是不是对它去做一些什么呀

143
00:03:42,600 --> 00:03:43,900
做一些配置

144
00:03:43,900 --> 00:03:45,500
那么这里呢就是我们

145
00:03:45,500 --> 00:03:47,300
我们去

146
00:03:47,300 --> 00:03:49,820
使用插件的一个过程

147
00:03:49,820 --> 00:03:51,360
包括我们怎么去引入呢

148
00:03:51,360 --> 00:03:53,060
其实引入这里是分为两种方式的

149
00:03:53,060 --> 00:03:54,540
一种呢你可以通过NPM方式引入

150
00:03:54,540 --> 00:03:56,180
这里呢也是最常见的引入方式

151
00:03:56,180 --> 00:03:57,840
咱们的LongJack是通过什么方式引入

152
00:03:57,840 --> 00:03:58,640
是不是也就是NPM呢

153
00:03:58,640 --> 00:03:59,440
我们通过NPM也是多

154
00:03:59,440 --> 00:03:59,920
对吧

155
00:03:59,920 --> 00:04:01,400
然后呢在config里面去配置

156
00:04:01,400 --> 00:04:02,280
然后还有一种是什么

157
00:04:02,280 --> 00:04:03,000
第二种方式

158
00:04:03,000 --> 00:04:04,720
就是呢我们可以在本地写一个插件

159
00:04:04,720 --> 00:04:07,320
然后通过咱们一个Path

160
00:04:07,320 --> 00:04:09,740
去指定我们插件的一个目录

161
00:04:09,740 --> 00:04:11,600
这样呢我们也可以在本地去引入插件

162
00:04:11,600 --> 00:04:13,360
那么在本地里面去引入

163
00:04:13,360 --> 00:04:14,880
我们下一节课会去讲

164
00:04:14,880 --> 00:04:15,780
那么这里呢

165
00:04:15,780 --> 00:04:16,420
我们来总结一下

166
00:04:16,420 --> 00:04:18,060
刚才所讲解的一个内容

167
00:04:18,060 --> 00:04:21,140
我们刚才是不是讲解了咱们插件了

168
00:04:21,140 --> 00:04:22,800
那么插件它到底是什么呀

169
00:04:22,800 --> 00:04:25,940
插件是不是解决了中间件

170
00:04:25,940 --> 00:04:26,940
解决

171
00:04:26,940 --> 00:04:28,560
解决不掉的问题

172
00:04:28,560 --> 00:04:30,480
解决了中间件

173
00:04:30,480 --> 00:04:33,380
做不到的事情

174
00:04:33,380 --> 00:04:33,800
好

175
00:04:33,800 --> 00:04:35,900
那么中间件它哪些事情做不到呢

176
00:04:35,900 --> 00:04:37,360
你比如说我们的洋葱模型

177
00:04:37,360 --> 00:04:39,940
是不是很难调整顺序

178
00:04:39,940 --> 00:04:45,300
而且呢我们是不是还需要封装一些和请求之外的逻辑

179
00:04:45,300 --> 00:04:53,340
那么我们的插件它长什么样子呢

180
00:04:53,340 --> 00:04:54,240
是不是除了没有

181
00:04:54,240 --> 00:04:56,160
是不是除了没有root和我们的controller

182
00:04:56,160 --> 00:04:58,860
他大部分和我们的一个1GG的应用是非常类似的对吧

183
00:04:58,860 --> 00:05:04,560
除了没有我们的一个root和controller

184
00:05:04,560 --> 00:05:09,360
是不是和我们的和1GGAPP很相似啊

185
00:05:09,360 --> 00:05:13,040
那么呢他有几种引入方式的顺便两种

186
00:05:13,040 --> 00:05:14,500
第一种是什么npm

187
00:05:14,500 --> 00:05:15,960
第二种呢你通过pass自己去指定

188
00:05:15,960 --> 00:05:18,200
那么下节课我们就来看一下怎么样在本地去写一个插件

189
00:05:18,200 --> 00:05:20,080
好这里呢就是我们这节课

