1
00:00:00,000 --> 00:00:01,860
好 刚才咱们是不是讲到了

2
00:00:01,860 --> 00:00:03,120
事件循环它的一个重要性

3
00:00:03,120 --> 00:00:04,720
那么这节课老师就来带同学们去

4
00:00:04,720 --> 00:00:06,320
学习到底什么是事件循环

5
00:00:06,320 --> 00:00:07,960
这里呢 老师先会介绍一下

6
00:00:07,960 --> 00:00:08,940
浏览器中事件循环的概念

7
00:00:08,940 --> 00:00:10,600
以及漏览器中事件循环的概念

8
00:00:10,600 --> 00:00:12,100
然后咱们得出事件循环

9
00:00:12,100 --> 00:00:13,200
它的一个本质到底是什么

10
00:00:13,200 --> 00:00:14,760
好 那我们先来看一下

11
00:00:14,760 --> 00:00:15,680
浏览器中的事件循环

12
00:00:15,680 --> 00:00:18,060
这里呢 是老师从他的官方网站

13
00:00:18,060 --> 00:00:18,840
去摘抄了一段

14
00:00:18,840 --> 00:00:20,540
对事件循环的一个介绍

15
00:00:20,540 --> 00:00:22,020
咱们先来看浏览器中

16
00:00:22,020 --> 00:00:23,380
为了协调事件

17
00:00:23,380 --> 00:00:24,280
用户交互

18
00:00:24,280 --> 00:00:24,900
脚本

19
00:00:24,900 --> 00:00:25,440
渲染

20
00:00:25,440 --> 00:00:26,200
网络等

21
00:00:26,200 --> 00:00:27,380
用户代理必须使用

22
00:00:27,380 --> 00:00:27,880
事件选完了

23
00:00:27,880 --> 00:00:29,100
重点是什么

24
00:00:29,100 --> 00:00:30,140
重点是不是协调

25
00:00:30,140 --> 00:00:32,040
我们先来看一下

26
00:00:32,040 --> 00:00:32,620
什么是事件

27
00:00:32,620 --> 00:00:33,280
事件

28
00:00:33,280 --> 00:00:34,500
实际上就是

29
00:00:34,500 --> 00:00:35,600
咱们浏览器中的

30
00:00:35,600 --> 00:00:36,280
PostMessage

31
00:00:36,280 --> 00:00:37,260
MutationObserver

32
00:00:37,260 --> 00:00:38,180
PostMessage

33
00:00:38,180 --> 00:00:39,000
是不是用于我们

34
00:00:39,000 --> 00:00:40,400
多个页面之间通信的

35
00:00:40,400 --> 00:00:41,700
MutationObserver呢

36
00:00:41,700 --> 00:00:43,500
它是对DOM去监听一些事件

37
00:00:43,500 --> 00:00:45,040
可能同学们没使用过

38
00:00:45,040 --> 00:00:45,740
这里呢

39
00:00:45,740 --> 00:00:46,800
同学们可以下去之后

40
00:00:46,800 --> 00:00:47,700
去查一下

41
00:00:47,700 --> 00:00:48,340
这个API

42
00:00:48,340 --> 00:00:49,220
用户交互

43
00:00:49,220 --> 00:00:49,780
是不是就是咱们

44
00:00:49,780 --> 00:00:50,720
既然是绑定的一些点击事件

45
00:00:50,720 --> 00:00:53,500
比如说clink,unscore等等

46
00:00:53,500 --> 00:00:55,920
渲染就是页面的一些渲染

47
00:00:55,920 --> 00:00:58,340
比如说咱们一个页面加载的时候

48
00:00:58,340 --> 00:01:00,160
是不是首先要去render它的一个dome tree

49
00:01:00,160 --> 00:01:01,860
然后再去解析它的css

50
00:01:01,860 --> 00:01:03,660
然后再去把整个页面给渲染出来

51
00:01:03,660 --> 00:01:04,920
脚本,脚本就是什么

52
00:01:04,920 --> 00:01:05,660
DS的一个执行

53
00:01:05,660 --> 00:01:07,220
那么什么是世界循环呢

54
00:01:07,220 --> 00:01:08,680
根据它的解释

55
00:01:08,680 --> 00:01:10,620
就是为了协调它们之间的关系

56
00:01:10,620 --> 00:01:11,940
需要使用世界循环

57
00:01:11,940 --> 00:01:13,760
那么这里是浏览器中世界循环的概念

58
00:01:13,760 --> 00:01:15,580
可能同学们听到这里呢

59
00:01:15,580 --> 00:01:16,880
还有点模糊,没有关系

60
00:01:16,880 --> 00:01:19,200
后面老师会带着同学们一步一步的清晰

61
00:01:19,200 --> 00:01:20,280
到底什么是世界循环

62
00:01:20,280 --> 00:01:23,640
好 那我们再来看下乐的阶线中世界循环的一个官方的解释是什么

63
00:01:23,640 --> 00:01:27,020
这里是一段英文 老师就不去带着同学们去看了

64
00:01:27,020 --> 00:01:28,420
我们来看一下中文

65
00:01:28,420 --> 00:01:30,920
世界循环允许乐的阶线执行非主次IO操作

66
00:01:30,920 --> 00:01:32,620
尽管JavaScript是单线程的

67
00:01:32,620 --> 00:01:34,680
通过尽可能将操作卸载到系统内核

68
00:01:34,680 --> 00:01:36,340
由于大多数现在内核都是多线程的

69
00:01:36,340 --> 00:01:38,620
因此它们可以处理在后台执行的多个操作

70
00:01:38,620 --> 00:01:40,040
当其中一个操作完成时

71
00:01:40,040 --> 00:01:43,340
内核会告诉乐的阶线一遍可以将相应的回调

72
00:01:43,340 --> 00:01:44,960
添加到轮熏队列中以最终执行

73
00:01:44,960 --> 00:01:46,300
是不是感觉

74
00:01:46,300 --> 00:01:48,380
漏的经验是解释得更加会是难懂

75
00:01:48,380 --> 00:01:49,160
更加模糊啊

76
00:01:49,160 --> 00:01:49,620
是不是有点懵

77
00:01:49,620 --> 00:01:50,720
其实没关系

78
00:01:50,720 --> 00:01:52,080
我们重点看最后一句话

79
00:01:52,080 --> 00:01:54,180
以便可以将相应的回调

80
00:01:54,180 --> 00:01:55,400
添加到轮循队列中

81
00:01:55,400 --> 00:01:56,420
以最终执行

82
00:01:56,420 --> 00:01:57,700
哦

83
00:01:57,700 --> 00:01:58,740
那么我们是不是这里能够

84
00:01:58,740 --> 00:01:59,700
稍微理解一点了

85
00:01:59,700 --> 00:02:00,100
是这样循环

86
00:02:00,100 --> 00:02:01,360
是不是就是把咱们的回调

87
00:02:01,360 --> 00:02:02,740
去添加到轮循队列里面去

88
00:02:02,740 --> 00:02:03,620
一个一个执行

89
00:02:03,620 --> 00:02:05,200
这里面是一个模糊的概念

90
00:02:05,200 --> 00:02:06,020
好

91
00:02:06,020 --> 00:02:06,580
那么我们来看一下

92
00:02:06,580 --> 00:02:08,600
把哪些回调添加到轮循队列中呢

93
00:02:08,600 --> 00:02:09,120
首先世界

94
00:02:09,120 --> 00:02:10,480
在漏的经验室里面

95
00:02:10,480 --> 00:02:11,820
是不是有个event admit对象

96
00:02:11,820 --> 00:02:13,320
同学们应该有使用过

97
00:02:13,320 --> 00:02:13,980
对吧

98
00:02:13,980 --> 00:02:15,420
然后呢一些非读式的IO

99
00:02:15,420 --> 00:02:17,060
网络请求文件读写等等

100
00:02:17,060 --> 00:02:18,900
他呢很多都是接收一些callback

101
00:02:18,900 --> 00:02:19,480
也就是回调

102
00:02:19,480 --> 00:02:21,340
脚本呢GS的执行

103
00:02:21,340 --> 00:02:23,420
那么在nolyGS事件循环中的概念

104
00:02:23,420 --> 00:02:23,940
就是呢

105
00:02:23,940 --> 00:02:25,760
以轮循的形式去执行他们

106
00:02:25,760 --> 00:02:26,280
好

107
00:02:26,280 --> 00:02:28,160
那我们就可以去

108
00:02:28,160 --> 00:02:29,480
总结一下事件循环

109
00:02:29,480 --> 00:02:30,220
它的个本质是什么

110
00:02:30,220 --> 00:02:31,920
是不是在浏览器

111
00:02:31,920 --> 00:02:33,340
或者nolyGS环境中

112
00:02:33,340 --> 00:02:36,800
运行时对GS脚本的调度方式啊

113
00:02:36,800 --> 00:02:37,200
同学们

114
00:02:37,200 --> 00:02:38,800
什么是运行时

115
00:02:38,800 --> 00:02:41,240
是不是我们在浏览器中就是浏览器环境

116
00:02:41,240 --> 00:02:42,460
在node中就是node的环境

117
00:02:42,460 --> 00:02:43,900
分别对应他们自己的环境

118
00:02:43,900 --> 00:02:46,480
但是他们是不是都始终保持了一个概念

119
00:02:46,480 --> 00:02:46,920
叫做什么

120
00:02:46,920 --> 00:02:50,280
nodejs里面是添加到轮群对面中去执行

121
00:02:50,280 --> 00:02:51,500
那么浏览器呢

122
00:02:51,500 --> 00:02:53,340
为了协调这些他们自己的关系

123
00:02:53,340 --> 00:02:54,440
实际上浏览器怎么样去协调

124
00:02:54,440 --> 00:02:57,160
是不是也有可能是添加到某一个对面里面去

125
00:02:57,160 --> 00:02:58,300
然后去一个一个的执行他们

126
00:02:58,300 --> 00:02:58,620
对吧

127
00:02:58,620 --> 00:02:59,580
好

128
00:02:59,580 --> 00:03:00,500
讲到这里了

129
00:03:00,500 --> 00:03:02,460
可能同学们还是没有清楚世界循环的一个概念

130
00:03:02,460 --> 00:03:03,260
没有关系

131
00:03:03,260 --> 00:03:05,640
咱们通过代码看一下世界循环

132
00:03:05,640 --> 00:03:06,880
帮助同学们去理解一下

133
00:03:06,880 --> 00:03:09,420
这里呢是一段代码

134
00:03:09,420 --> 00:03:11,280
老师呢在编辑系里面打开给同学们看一下

135
00:03:11,280 --> 00:03:14,520
首先

136
00:03:14,520 --> 00:03:16,940
我们会有一个setTimeout里面执行

137
00:03:16,940 --> 00:03:18,200
打印出来一个

138
00:03:18,200 --> 00:03:20,100
然后呢promise

139
00:03:20,100 --> 00:03:21,180
它也会直接点证

140
00:03:21,180 --> 00:03:21,980
promise是一步的

141
00:03:21,980 --> 00:03:22,420
对吧

142
00:03:22,420 --> 00:03:23,220
我们之前都学过

143
00:03:23,220 --> 00:03:23,740
所以呢

144
00:03:23,740 --> 00:03:24,660
也会打印个promise

145
00:03:24,660 --> 00:03:26,180
然后呢会在主线程上面去执行一个妹

146
00:03:26,180 --> 00:03:28,240
这里同学们来看一下

147
00:03:28,240 --> 00:03:29,180
它的一个执行顺序是什么

148
00:03:29,180 --> 00:03:30,880
setTimeout它是一步的

149
00:03:30,880 --> 00:03:32,160
promise它也是一步的

150
00:03:32,160 --> 00:03:33,740
那么我们的setTimeout先执行

151
00:03:33,740 --> 00:03:34,740
还是promise先执行呢

152
00:03:34,740 --> 00:03:36,680
如果说按照理论上常理上面来说

153
00:03:36,680 --> 00:03:37,940
是不是应该settimeout先执行了

154
00:03:37,940 --> 00:03:38,460
对吧

155
00:03:38,460 --> 00:03:39,600
因为它是在前面的一个一步

156
00:03:39,600 --> 00:03:40,820
promise它是后面的一步

157
00:03:40,820 --> 00:03:42,840
那么我们来看一下它这个预行结果是什么

158
00:03:42,840 --> 00:03:47,840
大家是不是可以看到

159
00:03:47,840 --> 00:03:49,600
我们的主线程先执行的是main

160
00:03:49,600 --> 00:03:50,700
它是同步的

161
00:03:50,700 --> 00:03:51,580
然后呢执行promise

162
00:03:51,580 --> 00:03:52,780
然后呢再去执行settimeout

163
00:03:52,780 --> 00:03:54,940
这里同学们是不是很奇怪呀

164
00:03:54,940 --> 00:03:58,320
明明咱们settimeout和promise都是一步的

165
00:03:58,320 --> 00:04:00,620
为什么promise它的在settimeout的前面

166
00:04:00,620 --> 00:04:01,280
实际上呢

167
00:04:01,280 --> 00:04:02,400
这里就跟世界循环

168
00:04:02,400 --> 00:04:03,500
有一些关系

169
00:04:03,500 --> 00:04:05,100
刚才我们是不是提到了

170
00:04:05,100 --> 00:04:07,100
什么是事件循环

171
00:04:07,100 --> 00:04:08,840
是不是我们在运行建设脚本的时候

172
00:04:08,840 --> 00:04:10,400
那么环境对它一个调度方式

173
00:04:10,400 --> 00:04:11,120
就叫做事件循环

174
00:04:11,120 --> 00:04:12,360
那么在浏览器里面

175
00:04:12,360 --> 00:04:13,400
到底是怎么样去调度它们的

176
00:04:13,400 --> 00:04:13,720
为什么

177
00:04:13,720 --> 00:04:16,080
Promise在前面

178
00:04:16,080 --> 00:04:17,020
在后面

179
00:04:17,020 --> 00:04:17,840
其实这里就涉及了

180
00:04:17,840 --> 00:04:19,780
涉及事件循环它的一个过程

181
00:04:19,780 --> 00:04:20,400
所以呢

182
00:04:20,400 --> 00:04:21,760
在下节课的内容

183
00:04:21,760 --> 00:04:23,360
老师会去讲解它的原因是什么

184
00:04:23,360 --> 00:04:25,100
那么我们来回顾一下

185
00:04:25,100 --> 00:04:26,260
刚才我们所讲过的内容

186
00:04:26,260 --> 00:04:28,340
我们是不是去

187
00:04:28,340 --> 00:04:30,380
了解一下官方网站

188
00:04:30,380 --> 00:04:31,440
浏览器中事件循环的一个概念

189
00:04:31,440 --> 00:04:32,120
然后呢

190
00:04:32,120 --> 00:04:33,780
漏的GS中世界循环一个概念

191
00:04:33,780 --> 00:04:34,640
那么在浏览器中

192
00:04:34,640 --> 00:04:35,860
是不是

193
00:04:35,860 --> 00:04:37,500
他的一个解释是为了去协调

194
00:04:37,500 --> 00:04:39,640
咱们浏览器的一些交互

195
00:04:39,640 --> 00:04:41,100
那么在漏的GS中

196
00:04:41,100 --> 00:04:43,060
它的核心是什么

197
00:04:43,060 --> 00:04:45,360
是不是将毁调添加到人群对链里面去

198
00:04:45,360 --> 00:04:47,340
只是说他们的方式不一样

199
00:04:47,340 --> 00:04:49,780
但是他们的核心本质是一样的

200
00:04:49,780 --> 00:04:52,700
对GS脚本的调度方式

201
00:04:52,700 --> 00:04:54,980
就是世界循环的一个本质

202
00:04:54,980 --> 00:04:56,900
通过这样一端代码

203
00:04:56,900 --> 00:04:57,800
我们是不是可以看出来

204
00:04:57,800 --> 00:04:58,720
好

205
00:04:58,720 --> 00:05:01,120
这里就是这一节的内容

