1
00:00:00,000 --> 00:00:03,860
好,这节课呢,我就来给大家演示一下,我们怎么样去编写一个help扩展

2
00:00:03,860 --> 00:00:08,300
那么help扩展是什么,其实它就是我们平时在项目中经常使用到的一些工具方法

3
00:00:08,300 --> 00:00:13,440
你比如说,假如说我们在项目中会经常去处理一些时间,把咱们的时间戳,转为咱们的某种固定的格式

4
00:00:13,440 --> 00:00:16,380
那么这样呢,我们是不是通常会建一个什么UTL无尽夹

5
00:00:16,380 --> 00:00:19,700
那么在扩网里面呢,它提供了一个专门的help这样的一个js

6
00:00:19,700 --> 00:00:24,140
它可以让我们在模板引擎,以及在咱们的context的对象上面都可以获取到这样一个help对象

7
00:00:24,140 --> 00:00:26,500
那么它到底是怎么回事,我们一起就来看一下

8
00:00:26,500 --> 00:00:28,660
我们先来复制这样一个代码非常简单

9
00:00:28,660 --> 00:00:30,480
我们这里呢模拟去获取一个时间

10
00:00:30,480 --> 00:00:33,220
比如说他呢假装我们返回一个我是处理后的时间

11
00:00:33,220 --> 00:00:34,360
中间的逻辑呢我给省略了

12
00:00:34,360 --> 00:00:35,580
我们重点看一下怎么去写扩展

13
00:00:35,580 --> 00:00:38,540
好首先呢我们在

14
00:00:38,540 --> 00:00:41,920
APP的extend里面

15
00:00:41,920 --> 00:00:43,760
这里呢我们需要去建一个新的文件夹

16
00:00:43,760 --> 00:00:45,680
叫做extend

17
00:00:45,680 --> 00:00:47,680
然后呢创建

18
00:00:47,680 --> 00:00:50,120
help.js

19
00:00:50,120 --> 00:00:52,100
好那么extend呢其实就代表

20
00:00:52,100 --> 00:00:54,100
是不是我们的扩展呢扩展里面有个help.js

21
00:00:54,100 --> 00:00:55,260
也就代表咱们一个帮助

22
00:00:55,260 --> 00:00:58,460
有点类似于咱们的UTR的计时

23
00:00:58,460 --> 00:01:04,740
这里其实我们已经完成了咱们插件的一个编系

24
00:01:04,740 --> 00:01:07,540
那么我们在我们的模板里面其实就可以访问到它了

25
00:01:07,540 --> 00:01:09,300
比如说我们去用咱们的模板

26
00:01:09,300 --> 00:01:11,220
比如说我们去访问

27
00:01:11,220 --> 00:01:13,920
方法是不是叫get data

28
00:01:13,920 --> 00:01:15,720
好执行一下它

29
00:01:15,720 --> 00:01:16,820
那么我们就来尝试一下

30
00:01:16,820 --> 00:01:17,540
看到底有没有生效

31
00:01:17,540 --> 00:01:20,500
手里大概看到是不是生效了

32
00:01:20,500 --> 00:01:21,280
我是处理后的时间

33
00:01:21,280 --> 00:01:24,560
那么我们只要在extend里面去创建一个HEP这样的js

34
00:01:24,560 --> 00:01:27,400
那么我们的模板引擎它会自动注入黑App这样一个方法

35
00:01:27,400 --> 00:01:30,180
然后你可以去调用黑App里面的一些方法

36
00:01:30,180 --> 00:01:32,120
而且你不仅可以在模板里面去调

37
00:01:32,120 --> 00:01:34,900
你当然也可以在context对象上去拿掉拿到

38
00:01:34,900 --> 00:01:35,460
你比如说

39
00:01:35,460 --> 00:01:37,800
比如说咱们访问首页的时候

40
00:01:37,800 --> 00:01:38,820
是不是会访问一个hi1G

41
00:01:38,820 --> 00:01:40,700
那我们来看一下context能不能访问到他

42
00:01:40,700 --> 00:01:45,460
比如说我们去context.help.getdata

43
00:01:45,460 --> 00:01:48,140
好

44
00:01:48,140 --> 00:01:48,940
那么我们来看一下

45
00:01:48,940 --> 00:01:50,620
在咱们context里面能不能够

46
00:01:50,620 --> 00:01:52,620
能不能够拿到

47
00:01:52,620 --> 00:01:52,940
好

48
00:01:52,940 --> 00:01:53,620
我们来访问一下首页

49
00:01:53,620 --> 00:01:59,760
好大家可以看到我们是不是不仅可以在模板里面拿到也而且呢在context在一个对象上面是不是也可以去访问到咱们的help

50
00:01:59,760 --> 00:02:01,560
好这里呢就是我们

51
00:02:01,560 --> 00:02:03,100
我们通过

52
00:02:03,100 --> 00:02:07,960
这里呢就是我们通过help去写一个扩展那么呢我们来

53
00:02:07,960 --> 00:02:09,740
总结一下

54
00:02:09,740 --> 00:02:13,340
写一个

55
00:02:13,340 --> 00:02:14,860
help

56
00:02:14,860 --> 00:02:15,900
扩展

57
00:02:15,900 --> 00:02:21,020
我们help扩展在哪里写了是不是app的

58
00:02:21,780 --> 00:02:26,720
extend 下面的 hair.js 然后呢 你可以在两个地方调用

59
00:02:26,720 --> 00:02:29,240
两个地方调用

60
00:02:29,240 --> 00:02:34,340
一个是什么呀 一个是不是咱们的模板 另外一个呢 是咱们的context对象上面可以去找到我们一个

61
00:02:34,340 --> 00:02:37,140
 hair对象 好 这里呢 就是我们这一节课的内容

