1
00:00:00,000 --> 00:00:02,500
好 接下来我们来看一下fork方法

2
00:00:02,500 --> 00:00:05,120
那么什么是fork呢

3
00:00:05,120 --> 00:00:07,880
fork它会直接创建一个直进程执行漏的脚本

4
00:00:07,880 --> 00:00:11,680
那么与spoint方法不同的是什么呢

5
00:00:11,680 --> 00:00:13,860
你比如说你fork一个tude.js

6
00:00:13,860 --> 00:00:15,460
其实和spoint是什么

7
00:00:15,460 --> 00:00:17,720
是不是也是去执行一个漏的命令

8
00:00:17,720 --> 00:00:20,180
spoint漏的然后执行tude.js

9
00:00:20,180 --> 00:00:22,020
其实和fork去的gs是一样的

10
00:00:22,020 --> 00:00:23,340
它都是去执行一段漏的脚本

11
00:00:23,340 --> 00:00:24,820
但是与spoint不同的是

12
00:00:24,820 --> 00:00:25,860
fork它会在

13
00:00:25,860 --> 00:00:29,060
负质进程之间去建立一个通信的管道

14
00:00:30,000 --> 00:00:31,260
它呢叫做type

15
00:00:31,260 --> 00:00:32,540
用于进程之间的通信

16
00:00:32,540 --> 00:00:34,480
也就是父子进程之间的通信

17
00:00:34,480 --> 00:00:36,160
它呢也是IPC通信的基础

18
00:00:36,160 --> 00:00:39,580
那么呢cluster其实也是基于一个IPC通信

19
00:00:39,580 --> 00:00:43,240
只是呢它对fork在一种方式做了一个封装

20
00:00:43,240 --> 00:00:45,280
所以说我们为什么要学processchill

21
00:00:45,280 --> 00:00:47,760
cluster其实就是基于它的fork进行的封装

22
00:00:47,760 --> 00:00:50,520
好那么我们就来看一下fork怎么样去使用

23
00:00:50,520 --> 00:00:53,320
以及呢父子进程之间是怎么样去通信的

24
00:00:53,320 --> 00:00:58,280
首先这里呢老师创建了一个fork文件夹

25
00:00:58,280 --> 00:00:59,460
下面有一个chill的有一个link

26
00:00:59,460 --> 00:01:06,200
首先呢,我们先来引入

27
00:01:06,200 --> 00:01:10,400
what,chill process等于require

28
00:01:10,400 --> 00:01:13,040
什么呀,chill process

29
00:01:13,040 --> 00:01:15,300
咱们来把pass模块引入一下

30
00:01:15,300 --> 00:01:17,160
pass等于require

31
00:01:17,160 --> 00:01:19,420
pass

32
00:01:19,420 --> 00:01:22,660
好,首先,咱们刚才介绍的什么

33
00:01:22,660 --> 00:01:23,760
fork方法

34
00:01:23,760 --> 00:01:26,700
fork谁呀

35
00:01:26,700 --> 00:01:28,660
是不是要fork咱们的chill的连接

36
00:01:28,660 --> 00:01:30,140
这也是咱们要在命下面

37
00:01:30,140 --> 00:01:34,760
命里面去创建一个紫禁城

38
00:01:34,760 --> 00:01:38,880
臭好去佛可

39
00:01:38,880 --> 00:01:40,560
怕是点

40
00:01:40,560 --> 00:01:42,380
蕊少为什么怕点瘦吧

41
00:01:42,380 --> 00:01:47,840
是不是因为在咱们的你可是环境下面他的路径可能会发生变化

42
00:01:47,840 --> 00:01:50,860
咱们为了稳定性咱们通过怕点瘦去返回一个绝对路径

43
00:01:50,860 --> 00:01:53,620
这样是不是稳定性好一点防止咱们的代码出错

44
00:01:53,620 --> 00:01:56,260
这是非常重要的稳定性

45
00:01:57,080 --> 00:01:58,580
也推荐同学们经常去使用

46
00:01:58,580 --> 00:01:59,680
past.resort

47
00:01:59,680 --> 00:01:59,920
好

48
00:01:59,920 --> 00:02:02,040
咱们fork之后怎么去做呢

49
00:02:02,040 --> 00:02:03,620
刚才讲到是不是会创建一个

50
00:02:03,620 --> 00:02:05,780
pipe和紫禁城之间去通信

51
00:02:05,780 --> 00:02:06,520
首先

52
00:02:06,520 --> 00:02:09,620
chill process.fork

53
00:02:09,620 --> 00:02:10,340
他呢

54
00:02:10,340 --> 00:02:12,420
会接收一个对象叫做

55
00:02:12,420 --> 00:02:13,080
chill的

56
00:02:13,080 --> 00:02:13,920
他会返回对象

57
00:02:13,920 --> 00:02:14,720
然后对象呢

58
00:02:14,720 --> 00:02:16,240
可以去监听message

59
00:02:16,240 --> 00:02:17,720
message就是紫禁城返回的

60
00:02:17,720 --> 00:02:18,920
一些数据

61
00:02:18,920 --> 00:02:20,520
甚至就是给紫禁城去发送

62
00:02:20,520 --> 00:02:22,340
所以这里我们可以得出

63
00:02:22,340 --> 00:02:23,080
得出什么呢

64
00:02:23,080 --> 00:02:24,840
fork

65
00:02:24,840 --> 00:02:27,040
之后会返回

66
00:02:27,080 --> 00:02:30,100
一个对象用于通信

67
00:02:30,100 --> 00:02:31,100
你也可以把它理解为

68
00:02:31,100 --> 00:02:32,560
Pipe的一个通道

69
00:02:32,560 --> 00:02:34,320
所以咱们需要用一个变量去接收

70
00:02:34,320 --> 00:02:35,740
哇Q的等约

71
00:02:35,740 --> 00:02:36,300
Q process

72
00:02:36,300 --> 00:02:37,580
这是不是就是咱们的fog之后

73
00:02:37,580 --> 00:02:38,180
返回一个对象

74
00:02:38,180 --> 00:02:40,020
首先通过Q的点

75
00:02:40,020 --> 00:02:41,860
按

76
00:02:41,860 --> 00:02:44,780
Message

77
00:02:44,780 --> 00:02:51,300
这里呢是他咱们约定的一种协议

78
00:02:51,300 --> 00:02:52,320
你必须去按Message

79
00:02:52,320 --> 00:02:55,080
这里呢就没有过多的去解释

80
00:02:55,080 --> 00:02:55,820
Console.log

81
00:02:55,820 --> 00:02:57,980
咱们去打印

82
00:02:57,980 --> 00:03:01,620
负集

83
00:03:01,620 --> 00:03:02,760
负集

84
00:03:02,760 --> 00:03:03,800
好

85
00:03:03,800 --> 00:03:04,300
父亲

86
00:03:04,300 --> 00:03:07,820
接收到数据

87
00:03:07,820 --> 00:03:09,620
data

88
00:03:09,620 --> 00:03:13,120
好

89
00:03:13,120 --> 00:03:13,940
这里呢

90
00:03:13,940 --> 00:03:16,260
是咱们父亲程序监听子进程的一个

91
00:03:16,260 --> 00:03:20,700
main去监听

92
00:03:20,700 --> 00:03:22,620
就得的消息

93
00:03:22,620 --> 00:03:23,440
好

94
00:03:23,440 --> 00:03:25,800
那么咱们监听是不是也要给子集

95
00:03:25,800 --> 00:03:27,600
去发送了什么发送了 剩的

96
00:03:27,600 --> 00:03:29,120
咱们直接传入一个

97
00:03:29,120 --> 00:03:31,440
自创

98
00:03:31,440 --> 00:03:34,760
叫什么呢 爸爸给你发

99
00:03:34,760 --> 00:03:35,780
消息了

100
00:03:35,780 --> 00:03:38,340
儿子爸爸给你发消息了

101
00:03:38,340 --> 00:03:38,860
好

102
00:03:38,860 --> 00:03:42,960
咱们再到子集里面去接收一下

103
00:03:42,960 --> 00:03:45,760
那么子进程怎么样去接收

104
00:03:45,760 --> 00:03:49,600
他其实是通过 process 代表什么 咱们之前讲过 是不是当前在一个进程

105
00:03:49,600 --> 00:03:52,420
process.message

106
00:03:55,800 --> 00:03:57,960
好我们来看一下data

107
00:03:57,960 --> 00:04:01,080
console.log

108
00:04:01,080 --> 00:04:05,160
儿子接收到的消息

109
00:04:05,160 --> 00:04:06,680
data

110
00:04:06,680 --> 00:04:09,580
咱们再来给父亲是不是要发送

111
00:04:09,580 --> 00:04:14,020
给父亲发送

112
00:04:14,020 --> 00:04:16,720
process.send

113
00:04:16,720 --> 00:04:18,020
怎么了

114
00:04:18,020 --> 00:04:19,200
爸爸你好

115
00:04:19,200 --> 00:04:22,680
好那咱们来运行一下

116
00:04:22,680 --> 00:04:24,420
看一下咱们的父亲是不是已经去

117
00:04:24,940 --> 00:04:25,960
message去接收

118
00:04:25,960 --> 00:04:27,240
甚至给儿子发消息

119
00:04:27,240 --> 00:04:28,260
那么儿子呢

120
00:04:28,260 --> 00:04:29,540
也会监听父亲

121
00:04:29,540 --> 00:04:30,300
发过来的消息

122
00:04:30,300 --> 00:04:31,080
然后呢还会

123
00:04:31,080 --> 00:04:32,620
给父亲发消息

124
00:04:32,620 --> 00:04:33,640
爸爸你好

125
00:04:33,640 --> 00:04:35,420
那咱们来执行一下看一下

126
00:04:35,420 --> 00:04:38,500
IOS

127
00:04:38,500 --> 00:04:40,300
load

128
00:04:40,300 --> 00:04:41,580
fork

129
00:04:41,580 --> 00:04:42,600
chill

130
00:04:42,600 --> 00:04:44,140
main.js

131
00:04:44,140 --> 00:04:44,640
好

132
00:04:44,640 --> 00:04:46,940
首先儿子接收到消息

133
00:04:46,940 --> 00:04:51,300
什么意思是不是咱们的子集chill的在一个进程收到消息了

134
00:04:51,300 --> 00:04:53,340
儿子爸爸给你发消息的也就是呢

135
00:04:53,600 --> 00:04:55,580
子集接收到负集他发过来的消息

136
00:04:55,580 --> 00:04:57,400
然后呢负集也收到了

137
00:04:57,400 --> 00:04:58,920
子集发过来的消息

138
00:04:58,920 --> 00:05:00,480
负集接收到消息

139
00:05:00,480 --> 00:05:01,140
爸爸你好

140
00:05:01,140 --> 00:05:02,800
是不是咱们就已经完成了

141
00:05:02,800 --> 00:05:04,480
是不是就已经完成了

142
00:05:04,480 --> 00:05:05,360
我们父子之间的通信

143
00:05:05,360 --> 00:05:06,580
那么父亲呢

144
00:05:06,580 --> 00:05:07,400
他既能接收数据

145
00:05:07,400 --> 00:05:08,700
又能给儿子发送数据

146
00:05:08,700 --> 00:05:09,420
那么儿子呢

147
00:05:09,420 --> 00:05:11,400
既能接收父亲的消息

148
00:05:11,400 --> 00:05:12,660
又能给父亲发消息

149
00:05:12,660 --> 00:05:13,620
是不是咱们就已经完成了

150
00:05:13,620 --> 00:05:14,780
双方的一个通信

151
00:05:14,780 --> 00:05:17,520
好这里呢

152
00:05:17,520 --> 00:05:18,460
我们就来总结一下

153
00:05:18,460 --> 00:05:19,280
Fork呢

154
00:05:19,280 --> 00:05:21,060
他其实是执行

155
00:05:21,060 --> 00:05:21,760
露的脚本

156
00:05:21,760 --> 00:05:22,600
和Spoin

157
00:05:22,600 --> 00:05:24,180
类似但是它的区别是什么呀

158
00:05:24,180 --> 00:05:25,880
fork是不是可以创建一个管道

159
00:05:25,880 --> 00:05:28,100
用于咱们父子之间的通信

160
00:05:28,100 --> 00:05:30,000
它呢也是咱们cluster的一个基础

161
00:05:30,000 --> 00:05:31,660
希望同学们好好理解一下

162
00:05:31,660 --> 00:05:33,100
好 这里那就是这节课的内容

