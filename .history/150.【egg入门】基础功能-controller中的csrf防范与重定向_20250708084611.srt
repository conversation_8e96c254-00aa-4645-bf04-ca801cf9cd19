1
00:00:00,000 --> 00:00:05,300
好 我们刚才是不是讲到了适合在咱们的controller里面去获取和解析我们的一些用户请求啊

2
00:00:05,300 --> 00:00:08,160
那么我们来看一下怎么样去获取咱们的一个request参数

3
00:00:08,160 --> 00:00:11,140
那么query获取地长参数我们之前是不是讲过

4
00:00:11,140 --> 00:00:13,420
包括params去获取我们router的一个参数

5
00:00:13,420 --> 00:00:14,940
那么这里呢有一种情况

6
00:00:14,940 --> 00:00:16,580
我们一起来看一下

7
00:00:16,580 --> 00:00:20,880
那么如果说我们访问根目录的时候

8
00:00:20,880 --> 00:00:23,520
我们给他一个name等于jack

9
00:00:23,520 --> 00:00:26,300
那么我们的query是不是就会返回给我们一个对象

10
00:00:26,300 --> 00:00:26,720
namejack

11
00:00:26,720 --> 00:00:29,000
那么如果说是这样的一种情况呢

12
00:00:29,000 --> 00:00:31,300
比如说我们给了一个同样的字段也叫name

13
00:00:31,300 --> 00:00:32,840
然后呢他呢叫做

14
00:00:32,840 --> 00:00:33,860
jams

15
00:00:33,860 --> 00:00:36,420
一个name jack一个name和jams

16
00:00:36,420 --> 00:00:37,440
那么我们的

17
00:00:37,440 --> 00:00:39,760
那么我们的query到底是什么

18
00:00:39,760 --> 00:00:42,560
那么我们的query到底是什么

19
00:00:42,560 --> 00:00:45,640
这里呢我们就来看一下

20
00:00:45,640 --> 00:00:52,040
我们打印出来看一下contest.query

21
00:00:52,040 --> 00:00:55,120
好刷新

22
00:00:55,120 --> 00:00:57,420
大家可以看到是不是name jack

23
00:00:57,680 --> 00:00:59,640
那么如果说我们有这样的一种需求

24
00:00:59,640 --> 00:01:01,900
我们既要获取Jack又要获取James怎么办

25
00:01:01,900 --> 00:01:03,080
那么其实呢

26
00:01:03,080 --> 00:01:06,240
在context也提供给我们的一个方法

27
00:01:06,240 --> 00:01:07,560
叫做context.queries

28
00:01:07,560 --> 00:01:09,760
那么queries就可以获取了我们相同的资料

29
00:01:09,760 --> 00:01:10,720
我们再刷新一下

30
00:01:10,720 --> 00:01:11,900
大家是不是可以看到

31
00:01:11,900 --> 00:01:13,460
name它呢返回的结果

32
00:01:13,460 --> 00:01:14,200
是不是一个宿主啊

33
00:01:14,200 --> 00:01:15,280
Jack和James都在里面

34
00:01:15,280 --> 00:01:17,820
那么这里呢就是我们去获取咱们

35
00:01:17,820 --> 00:01:19,580
相同参数的一个方法

36
00:01:19,580 --> 00:01:20,140
queries

37
00:01:20,140 --> 00:01:21,460
那么还有一种情况是什么

38
00:01:21,460 --> 00:01:23,180
刚才我们queries是不是讲到了

39
00:01:23,180 --> 00:01:24,100
是通过地址的一个get

40
00:01:24,100 --> 00:01:25,940
那么post的参数怎么样获取

41
00:01:25,940 --> 00:01:30,720
那么我们之前是不是通过Core去演示过咱们的一个Core玻璃材那个插件去解析我们的Post参数

42
00:01:30,720 --> 00:01:32,880
那么在Controller里面我们怎么样去解析呢

43
00:01:32,880 --> 00:01:37,940
实际上框架内部它也去内置了我们的一个PolyPars的中间键来对这类的请求去进行解析

44
00:01:37,940 --> 00:01:42,000
那么我们这里就来演示一下怎么样通过ETG去获取我们的一个

45
00:01:42,000 --> 00:01:44,940
获取我们的一个Post参数

46
00:01:44,940 --> 00:01:47,080
好我们先来看一下路由是什么

47
00:01:47,080 --> 00:01:52,240
假如说我们现在有这样的一个路由叫做AddUser

48
00:01:52,240 --> 00:01:54,120
你比如说我们通过Post参数去添加一个用户

49
00:01:54,120 --> 00:01:56,880
这里呢我已经提前给定义好了

50
00:01:56,880 --> 00:01:58,540
那么如果说我们获取参数成功呢

51
00:01:58,540 --> 00:01:59,540
它就还返回一个success

52
00:01:59,540 --> 00:02:01,740
那么它的路由呢是add user

53
00:02:01,740 --> 00:02:04,280
那么这里呢其实我们都已经定义好了

54
00:02:04,280 --> 00:02:06,820
那么我们通过postment来访问一下

55
00:02:06,820 --> 00:02:09,160
看到底子是怎么样去获取的

56
00:02:09,160 --> 00:02:10,000
npm run dv

57
00:02:10,000 --> 00:02:13,160
那么呢我们通过add user在那个路由

58
00:02:13,160 --> 00:02:13,980
我们去对它进行post

59
00:02:13,980 --> 00:02:15,340
我们来解析一下它的参数

60
00:02:15,340 --> 00:02:18,460
好这里呢有一个add user

61
00:02:18,460 --> 00:02:23,120
然后咱们在request里面

62
00:02:24,120 --> 00:02:25,400
我们来胜费一下

63
00:02:25,400 --> 00:02:27,700
好 我们来看一下结果

64
00:02:27,700 --> 00:02:29,500
大家可以看到是不是我们这里

65
00:02:29,500 --> 00:02:31,040
什么都没有返回啊对吧

66
00:02:31,040 --> 00:02:32,060
没有返回success

67
00:02:32,060 --> 00:02:33,840
我们来看一下有没有报错

68
00:02:33,840 --> 00:02:39,220
其实呢这里可以看到

69
00:02:39,220 --> 00:02:40,000
什么呢

70
00:02:40,000 --> 00:02:41,280
这里来报一个错误什么

71
00:02:41,280 --> 00:02:42,300
invalid

72
00:02:42,300 --> 00:02:44,080
invalid CSRF token

73
00:02:44,080 --> 00:02:47,420
说明一个什么问题说明了我们在post的时候没有去传递一个参数

74
00:02:47,420 --> 00:02:48,180
什么意思

75
00:02:48,180 --> 00:02:50,240
其实我们可以看一下

76
00:02:50,240 --> 00:02:53,560
在1GG中会默认带上CSRF的一个防护

77
00:02:53,820 --> 00:02:56,960
如果说你不加上poken是取不到post中的post中的参数了

78
00:02:56,960 --> 00:02:57,680
什么意思

79
00:02:57,680 --> 00:03:00,360
也就是说我们在使用egg这样一个框架的时候

80
00:03:00,360 --> 00:03:02,420
你直接去进行post 你是获取不到他的一个参数

81
00:03:02,420 --> 00:03:06,220
你必须去带上他框架所约定的一个csrf的一个防护

82
00:03:06,220 --> 00:03:08,180
那么这里什么是csrf 这里可能呢

83
00:03:08,180 --> 00:03:09,920
我需要给同学们去简单的去介绍一下

84
00:03:09,920 --> 00:03:11,020
然后我们再去讲后面内容

85
00:03:11,020 --> 00:03:12,320
因为如果说不把这个地方搞清楚

86
00:03:12,320 --> 00:03:13,120
可能同学们稍微有点懵

87
00:03:13,120 --> 00:03:15,480
那么什么是csrf

88
00:03:15,480 --> 00:03:17,420
他其实是我们httb一种常见的漏洞

89
00:03:17,420 --> 00:03:21,860
这里呢我来给同学们来简单的去介绍一下什么是csrf

90
00:03:22,880 --> 00:03:23,800
那么假如说

91
00:03:23,800 --> 00:03:25,640
我们现在在知乎这样一个界面

92
00:03:25,640 --> 00:03:27,220
我这里来写篇文章

93
00:03:27,220 --> 00:03:29,120
我这里来简单写篇文章

94
00:03:29,120 --> 00:03:31,200
那么如果说我们去定义一个标题

95
00:03:31,200 --> 00:03:32,220
哈哈

96
00:03:32,220 --> 00:03:34,400
好 这里呢来

97
00:03:34,400 --> 00:03:35,720
比如说我们来演示

98
00:03:35,720 --> 00:03:36,840
csrf

99
00:03:36,840 --> 00:03:38,280
我们正文输入一段

100
00:03:38,280 --> 00:03:39,760
scribe

101
00:03:39,760 --> 00:03:41,360
scribe

102
00:03:41,360 --> 00:03:43,100
我们来写一段GS的脚本

103
00:03:43,100 --> 00:03:43,360
对吧

104
00:03:43,360 --> 00:03:43,680
scribe

105
00:03:43,680 --> 00:03:44,860
GS脚本

106
00:03:44,860 --> 00:03:46,700
然后我们给它let一个

107
00:03:46,700 --> 00:03:48,220
随便要let一个1

108
00:03:48,220 --> 00:03:48,840
好

109
00:03:48,840 --> 00:03:49,940
那么我们来发布一下

110
00:03:49,940 --> 00:03:50,480
发布

111
00:03:50,480 --> 00:03:52,020
好

112
00:03:52,020 --> 00:03:53,700
他这里呢需要我添加一个话题

113
00:03:53,700 --> 00:03:55,680
我们随便添一个好下一步走

114
00:03:55,680 --> 00:03:57,260
大家看到我们的文章是不是

115
00:03:57,260 --> 00:04:00,540
script alert 1111那么此时有没有弹框

116
00:04:00,540 --> 00:04:02,100
有没有真正的anet没有吧

117
00:04:02,100 --> 00:04:02,700
对吧

118
00:04:02,700 --> 00:04:04,200
我们来看一下咱们的页面

119
00:04:04,200 --> 00:04:05,160
它到底是怎么回事

120
00:04:05,160 --> 00:04:06,100
为什么没有弹出

121
00:04:06,100 --> 00:04:14,520
好这里呢其实咱们的script alert

122
00:04:14,520 --> 00:04:15,700
是不是在html里面

123
00:04:15,700 --> 00:04:17,080
是不是被我们去转成了一个什么呀

124
00:04:17,080 --> 00:04:18,020
是不是被转成了自不穿了

125
00:04:18,020 --> 00:04:19,020
所以他没有弹出

126
00:04:19,020 --> 00:04:20,900
那么我们在一机里面去演示一下

127
00:04:22,020 --> 00:04:24,120
我们在我们的框架里面去演示一下 如果说我们去

128
00:04:24,120 --> 00:04:25,980
会发生一下什么事情

129
00:04:25,980 --> 00:04:28,440
那么这里呢 我们去创建了一个csrf 这样一个gs

130
00:04:28,440 --> 00:04:30,560
那么我们的玻璃里面写了这样一段字幕串

131
00:04:30,560 --> 00:04:32,860
script 同样的呢 咱们的elette

132
00:04:32,860 --> 00:04:34,860
大家可以看到 我们的玻璃是什么呀

133
00:04:34,860 --> 00:04:36,260
我们的玻璃是什么呀

134
00:04:36,260 --> 00:04:38,280
我们的玻璃是不是字幕串 对吧

135
00:04:38,280 --> 00:04:40,880
咱们的readbounce

136
00:04:40,880 --> 00:04:43,020
实际上是字幕串

137
00:04:43,020 --> 00:04:45,520
那么大家猜一下我们的页面 会展示一个什么

138
00:04:45,520 --> 00:04:47,320
会展示一个字幕串 这样一个字幕串吗

139
00:04:47,320 --> 00:04:49,020
同学们猜一下

140
00:04:49,920 --> 00:04:51,960
那么我们来直接访问看一下到底是怎么回事

141
00:04:51,960 --> 00:04:53,300
看有没有印证你们的猜想

142
00:04:53,300 --> 00:04:55,100
我们来访问csrf这样一个路由

143
00:04:55,100 --> 00:05:02,620
csrf

144
00:05:02,620 --> 00:05:03,060
好

145
00:05:03,060 --> 00:05:05,200
大家看到我们的页面是不是没有展示出一个支付串了

146
00:05:05,200 --> 00:05:06,240
反而弹出了一个

147
00:05:06,240 --> 00:05:07,380
弹窗

148
00:05:07,380 --> 00:05:09,180
是不是因为我们的scribe的标签生效了

149
00:05:09,180 --> 00:05:09,920
他给我们弹了一段

150
00:05:09,920 --> 00:05:11,980
gs访问你的支付网连接

151
00:05:11,980 --> 00:05:14,660
那么其实这里呢就是csrf攻击的一个雏形

152
00:05:14,660 --> 00:05:17,300
那么这里它为什么会导致一个安全隐患呢

153
00:05:17,300 --> 00:05:18,520
我给同学们去举个例子

154
00:05:19,480 --> 00:05:20,500
我给同学们去举个例子

155
00:05:20,500 --> 00:05:24,340
我们刚才是不是发布了一篇文章

156
00:05:24,340 --> 00:05:25,880
然后在里面写了一段

157
00:05:25,880 --> 00:05:26,900
自伏创scribe的

158
00:05:26,900 --> 00:05:28,440
那么如果说你这一段没有处理好

159
00:05:28,440 --> 00:05:29,980
我们是不是极有可能在你的

160
00:05:29,980 --> 00:05:31,260
发布文章的时候

161
00:05:31,260 --> 00:05:32,540
是不是直接执行一段GS

162
00:05:32,540 --> 00:05:34,320
那么如果说你刚刚访问过

163
00:05:34,320 --> 00:05:34,840
淘宝

164
00:05:34,840 --> 00:05:35,360
网站

165
00:05:35,360 --> 00:05:36,880
那么其实我们的用户信息一般会存在哪里

166
00:05:36,880 --> 00:05:37,920
是不是存在cookie里面

167
00:05:37,920 --> 00:05:39,960
那么如果说你去访问这篇文章

168
00:05:39,960 --> 00:05:41,240
那么如果说GS直接执行

169
00:05:41,240 --> 00:05:41,760
有一些

170
00:05:41,760 --> 00:05:43,800
黑客他直接在里面去植入一段

171
00:05:43,800 --> 00:05:44,820
scribe的GS脚本

172
00:05:44,820 --> 00:05:45,840
直接去访问你的

173
00:05:45,840 --> 00:05:46,880
支付宝转账链接

174
00:05:47,120 --> 00:05:49,420
那么此时就会造成一个安全的一个问题

175
00:05:49,420 --> 00:05:50,440
可能会盗取你的一个账号

176
00:05:50,440 --> 00:05:51,980
那么这里呢

177
00:05:51,980 --> 00:05:54,540
我只是给同学们去简单的介绍一下什么是csif

178
00:05:54,540 --> 00:05:56,600
那么实际上它是非常的复杂的一个过程

179
00:05:56,600 --> 00:05:58,640
那么对这块感兴趣的同学可以去

180
00:05:58,640 --> 00:06:03,000
去搜索一些文章去详细的去看一下什么是csif

181
00:06:03,000 --> 00:06:04,780
因为我们的重点还是放在控制器

182
00:06:04,780 --> 00:06:05,800
那么这里呢其实

183
00:06:05,800 --> 00:06:07,600
这样就会造成咱们一个安全漏洞

184
00:06:07,600 --> 00:06:09,140
特别是像这样一种表单提交的

185
00:06:09,140 --> 00:06:10,680
可以去让用户自己去输入一些内容

186
00:06:10,680 --> 00:06:12,980
所以我们需要去做一些

187
00:06:12,980 --> 00:06:15,280
csif的防范

188
00:06:15,540 --> 00:06:19,380
那么一级会默认带上的这样一种的 会默认带上咱们的一个csrf

189
00:06:19,380 --> 00:06:21,420
黄泛 那么怎么去做呢

190
00:06:21,420 --> 00:06:23,980
首先我们通过context.csrf

191
00:06:23,980 --> 00:06:28,340
context下面有他这个属性 然后可以读取到一段token 我们把它给打印出来看一下

192
00:06:28,340 --> 00:06:38,580
好 那么我们来看一下咱们的context下面到底有没有csrf这样的一个字段

193
00:06:38,580 --> 00:06:42,940
好 我们通过posterman再去胜得一下

194
00:06:45,540 --> 00:06:49,820
好 其实这样是打印不出来的 为什么呀 其实在这里他的请求已经被拦截了

195
00:06:49,820 --> 00:06:50,400
 因为我们有权限

196
00:06:50,400 --> 00:06:51,680
所以呢 我们在

197
00:06:51,680 --> 00:06:53,740
index里面把它给打印出来

198
00:06:53,740 --> 00:06:57,320
csrf

199
00:06:57,320 --> 00:06:59,880
contest

200
00:06:59,880 --> 00:07:01,160
csrf

201
00:07:01,160 --> 00:07:06,280
好 那么我们通过访问首页来看一下 我们的一个csrf 他这样一个字段是什么

202
00:07:06,280 --> 00:07:10,120
好 大家可以看到 我们是不是打印了一串

203
00:07:10,120 --> 00:07:15,240
打印一串这样一段加密过的密文了 那么其实呢 他就是进行csrf防范的一个

204
00:07:15,540 --> 00:07:17,940
那么我们怎么样去进行post呢

205
00:07:17,940 --> 00:07:18,840
我们需要

206
00:07:18,840 --> 00:07:21,980
我们呢

207
00:07:21,980 --> 00:07:23,540
需要在head中加入这样的一个请求头

208
00:07:23,540 --> 00:07:25,140
叫做x-csrf token

209
00:07:25,140 --> 00:07:26,700
那么我们在postman里面去添加一下

210
00:07:26,700 --> 00:07:28,540
好

211
00:07:28,540 --> 00:07:29,480
我们添加一下

212
00:07:29,480 --> 00:07:30,280
其实这里呢

213
00:07:30,280 --> 00:07:31,440
我已经提前给添好了

214
00:07:31,440 --> 00:07:31,860
好

215
00:07:31,860 --> 00:07:32,220
此时呢

216
00:07:32,220 --> 00:07:32,980
我们直接来渗得一下

217
00:07:32,980 --> 00:07:33,400
走理

218
00:07:33,400 --> 00:07:34,500
大家可以看到

219
00:07:34,500 --> 00:07:35,060
是不是成功了呀

220
00:07:35,060 --> 00:07:35,560
因为我们是不是

221
00:07:35,560 --> 00:07:36,960
把我们一个请求头给添加上来

222
00:07:36,960 --> 00:07:39,040
那么我们为什么要添加到

223
00:07:39,040 --> 00:07:40,120
咱们那个head里面呢

224
00:07:40,120 --> 00:07:40,540
而不是cookie

225
00:07:40,540 --> 00:07:43,020
其实我们进行csrf攻击

226
00:07:43,020 --> 00:07:44,680
它的一个基本的原理是什么

227
00:07:44,680 --> 00:07:48,280
是不是就是令我们cookie会在相同的域名自动去发送cookie的一个漏洞呢

228
00:07:48,280 --> 00:07:50,600
那么如果说你在head里面它其实不会去自动发送的

229
00:07:50,600 --> 00:07:52,500
所以这样就堵捉了csrf这样一个攻击的渠道

230
00:07:52,500 --> 00:07:53,820
好

231
00:07:53,820 --> 00:07:56,120
那么此时我们来读取一下我们的body

232
00:07:56,120 --> 00:08:05,600
好

233
00:08:05,600 --> 00:08:06,740
我们再来圣得一下

234
00:08:06,740 --> 00:08:08,720
那么其实我们的body填的是namejack

235
00:08:08,720 --> 00:08:09,300
非常简单

236
00:08:09,300 --> 00:08:12,220
大家看到我们是不是现在通过contest.request.body

237
00:08:12,220 --> 00:08:14,280
就拿到了我们post过来的一个参数

238
00:08:14,280 --> 00:08:16,440
你玻璃对吧 好 这里呢就是我们

239
00:08:16,440 --> 00:08:22,260
是也是rf 防范以及post 一个过程 我们接下来看一下重定像 那么在康在那么在

240
00:08:22,260 --> 00:08:25,380
康泰市里面其实也提供了一个什么呢 提供了一个

241
00:08:25,380 --> 00:08:28,440
ready react 这样一个方法 可以让我们去重定像我们的页面

242
00:08:28,440 --> 00:08:33,130
那么其实是重定像当他需要一下配置 你比如说我们去重定像的时候

243
00:08:33,130 --> 00:08:33,560
 你可能

244
00:08:33,560 --> 00:08:36,900
你不需要你的接口去随便的去跳转到一些链接 那么我们可以在

245
00:08:36,900 --> 00:08:38,680
config 里面去配置一些白名单

246
00:08:38,680 --> 00:08:42,280
那么我们就来测试一下 你比如说我们是不是要在security里面去

247
00:08:42,960 --> 00:08:46,460
创建一个白名单 对吧 好 这里呢 我们就来演示一下 比如说我们在config里面

248
00:08:46,460 --> 00:08:51,560
在Security里面 这样一个字段里面去 咱们去创建一个白名单 比如说我们只能

249
00:08:51,560 --> 00:08:55,060
只能将我们项目往百度这样一个页面去跳 那我们就来看一下到底能不能跳

250
00:08:55,060 --> 00:08:58,460
比如说我们去 比如说我们现在定义了一个redirect这样一个控制器

251
00:08:58,460 --> 00:09:00,260
然后router呢 我们来看一下router

252
00:09:00,260 --> 00:09:05,060
那么router就是redirect 然后呢 当我们访问到这样一个路由的时候 就会把我们去重定向到淘宝

253
00:09:05,060 --> 00:09:06,460
那么我们来看一下能不能到淘宝

254
00:09:06,460 --> 00:09:10,260
其实应该是不能的吧 因为它不在白名单里面 我们来测试一下

255
00:09:12,960 --> 00:09:14,660
readread

256
00:09:14,660 --> 00:09:17,000
好 这里大家可以看到是不是反护了一个error

257
00:09:17,000 --> 00:09:18,120
他说什么呢

258
00:09:18,120 --> 00:09:19,260
readread新一字

259
00:09:19,260 --> 00:09:20,940
说明呢 被禁止了

260
00:09:20,940 --> 00:09:22,260
因为呢 你不在白名单里面

261
00:09:22,260 --> 00:09:24,440
那么我们来试一下我们能不能访问到咱们的一个百度

262
00:09:24,440 --> 00:09:26,880
百度 好

263
00:09:26,880 --> 00:09:32,320
大家可以看到当我们访问的时候是不是已经重新下来咱们的一个百度在一个页面

264
00:09:32,320 --> 00:09:34,180
为什么 因为他在我们的一个白名单里面

265
00:09:34,180 --> 00:09:37,300
好 这里呢就是我们控制器里面的一个内容

266
00:09:37,300 --> 00:09:38,420
好 我们一起来

267
00:09:38,420 --> 00:09:40,900
总结一下咱们这些课所讲解的一个内容

268
00:09:40,900 --> 00:09:43,820
我们这节课讲的是不是讲讲一个获取参数

269
00:09:43,820 --> 00:09:46,000
那么获取参数是不是有四种方式

270
00:09:46,000 --> 00:09:46,420
params

271
00:09:46,420 --> 00:09:47,740
query

272
00:09:47,740 --> 00:09:48,780
还有什么呢

273
00:09:48,780 --> 00:09:49,520
query对吧

274
00:09:49,520 --> 00:09:52,100
那么query是不是可以获取咱们多个的重复参数

275
00:09:52,100 --> 00:09:53,300
包括还有一个什么呀

276
00:09:53,300 --> 00:09:54,220
是不是body呀

277
00:09:54,220 --> 00:09:55,300
post的

278
00:09:55,300 --> 00:09:58,500
那么post的body我们需要注意什么

279
00:09:58,500 --> 00:10:02,860
是不是需要去注意带上csrf的一个token

280
00:10:02,860 --> 00:10:04,560
那么什么是csrf

281
00:10:04,560 --> 00:10:05,440
希望同学们下去之后呢

282
00:10:05,440 --> 00:10:06,860
能够去详细的去看一下它一个介绍

283
00:10:06,860 --> 00:10:10,600
我们是不是还讲解了一个

284
00:10:10,600 --> 00:10:12,120
重定箱啊

285
00:10:12,120 --> 00:10:14,320
那么重定箱是不是需要去设置一些白名单

286
00:10:14,320 --> 00:10:15,080
对吧

287
00:10:15,080 --> 00:10:18,760
好这里呢就是我们这节课的内容

