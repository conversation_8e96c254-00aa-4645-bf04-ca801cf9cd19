1
00:00:00,000 --> 00:00:05,840
好 这节课 我们就来看一下radis 他的一个命令行客户端 也就是咱们的radis

2
00:00:05,840 --> 00:00:06,140
 CLI

3
00:00:06,140 --> 00:00:12,280
他的一些操作 我们先来简单的去执行一些radis CLI 他的一些命令 咱们来热议热声

4
00:00:12,280 --> 00:00:20,360
首先radis CLI 他的执行咱们刚才是不是已经演示了呀 比如说我们去启用一个radis-saver

5
00:00:20,360 --> 00:00:22,020
 这里呢就启用一个radis的服务

6
00:00:22,020 --> 00:00:26,360
当我们执行radis CLI的时候 我们就会去连接咱们的saver 并且来起一个交互式的命令室窗口

7
00:00:27,640 --> 00:00:33,640
其实呢 我们在radis-cli的时候 它还可以去指定一些参数 比如说-h指定咱们的一个

8
00:00:33,640 --> 00:00:39,640
服务器 咱们的地址 本地p呢咱们的端口号 默认是6379

9
00:00:39,640 --> 00:00:43,960
这里呢 如果说 如果说你什么都不写 他就会默认去读起咱们刚才一个radis-conf

10
00:00:43,960 --> 00:00:46,000
 如果说你去传入一些参数 他就会去

11
00:00:46,000 --> 00:00:51,800
修改咱们的配置 他就会去连接理所 指定了这样一些端口

12
00:00:51,800 --> 00:00:56,780
好 我们来看一下radis-cli 它的一些命令 我们随便举两个例子

13
00:00:57,140 --> 00:01:02,520
比如说咱们的radis cly怎么样去使用 咱们来感受一下 比如说ping是什么呀

14
00:01:02,520 --> 00:01:03,860
 咱们ping是不是可以去

15
00:01:03,860 --> 00:01:10,640
咱们在lilux里面 咱们在lilux里面是不是可以去ping一个页面呢 比如说我们去ping一个百度点看

16
00:01:10,640 --> 00:01:16,260
大家看到没有 说明我咱们的一次request和一个response 他的时间是23.836毫秒

17
00:01:16,260 --> 00:01:20,360
说明了咱们能够去访问百度 那么咱们再ping一下淘宝

18
00:01:21,640 --> 00:01:28,040
这个呢就是去测试咱们一个网络的一个情况 你比如说你随便拼一个 拼一个你瞎写肯定是拼不通的

19
00:01:28,040 --> 00:01:32,640
哎居然有这样一个网站 咱们再来瞎拼一个

20
00:01:32,640 --> 00:01:40,320
大家看到没有 cannot resolve underhost 说明他没有在一个域名咱们拼不通

21
00:01:40,320 --> 00:01:42,880
好 其实呢在

22
00:01:42,880 --> 00:01:45,440
在redis cli 里面拼在一个命令

23
00:01:45,440 --> 00:01:46,980
其实呢

24
00:01:47,500 --> 00:01:56,200
和咱们去聘一个网页是一样的咱们聘他会访问一个碰说明我们的链接已经连通了他聘的是谁了聘的是咱们的本地的6379这样一个端口也就是咱们radis

25
00:01:56,200 --> 00:02:00,820
那我这里呢大家看到大家注意到里面我们的

26
00:02:00,820 --> 00:02:06,440
命令是大写的其实呢在radis里面他的命令部分大小写你去小写的去聘也是可以的但是完全没有问题

27
00:02:06,440 --> 00:02:12,340
好我们再来看一下有没有什么其他的命令比如说我们去以口以口是不是输出那咱们来试一下以口一个

28
00:02:13,360 --> 00:02:17,800
哈喽 好是不是咱们输出了一个哈喽 这里呢就是raditcli他的一个

29
00:02:17,800 --> 00:02:19,340
咱们找了一下感觉

30
00:02:19,340 --> 00:02:26,360
这里呢我们来看一下 刚才我们是不是pin了一下之后就会回复咱们的碰

31
00:02:26,360 --> 00:02:28,840
 也就是咱们的返回值 说明了pin通了

32
00:02:28,840 --> 00:02:34,000
那么如果说我们随便去输入一个不存在的命令 看他会返回一个什么

33
00:02:34,000 --> 00:02:39,480
比如说我们去瞎写 大家看到没有 那么raditcli在一个命令交互式窗口

34
00:02:39,480 --> 00:02:40,240
 他会给我们一个error

35
00:02:40,240 --> 00:02:41,600
unlowcommand ggg

36
00:02:42,800 --> 00:02:44,600
他呢 都会明确的告诉你没有这个命令

37
00:02:44,600 --> 00:02:45,880
这呢是redis

38
00:02:45,880 --> 00:02:46,900
cli他的一个

39
00:02:46,900 --> 00:02:47,920
错误回复

40
00:02:47,920 --> 00:02:49,200
也就是一个错误的防徽值

41
00:02:49,200 --> 00:02:50,220
那我们再来看一下

42
00:02:50,220 --> 00:02:52,520
整数回复

43
00:02:52,520 --> 00:02:53,560
分离了几个命令

44
00:02:53,560 --> 00:02:55,080
只是咱们找一下感觉

45
00:02:55,080 --> 00:02:57,400
正式了API的学习会在后面的课程

46
00:02:57,400 --> 00:02:58,160
大家不要着急

47
00:02:58,160 --> 00:02:59,440
咱们现在是找一下感觉

48
00:02:59,440 --> 00:03:00,200
我们来看一下

49
00:03:00,200 --> 00:03:01,240
整数的一个

50
00:03:01,240 --> 00:03:02,000
回复

51
00:03:02,000 --> 00:03:04,040
比如说

52
00:03:04,040 --> 00:03:06,100
在redis里面他有这个命令叫做

53
00:03:06,100 --> 00:03:06,860
incr

54
00:03:06,860 --> 00:03:08,400
什么意思呢

55
00:03:08,400 --> 00:03:10,960
比如说刚才我们讲到了

56
00:03:11,220 --> 00:03:14,620
Redis它是不是一个字典类型呢 字典类型其实都是k value k value

57
00:03:14,620 --> 00:03:18,060
它可以将k中存储的数字增1 什么意思

58
00:03:18,060 --> 00:03:20,180
比如说你可以存了一个

59
00:03:20,180 --> 00:03:25,860
value是1 你执行了incr这样一个命令就会把1变成2 你再执行把2变成3

60
00:03:25,860 --> 00:03:30,620
那如果说这样一个k不存在 那么k的值就会被初始化为0 再执行这样一个操作

61
00:03:30,620 --> 00:03:35,780
那么如果说值包含错误的类型或字不穿的值不能表示为数字 则返回一个错误

62
00:03:35,780 --> 00:03:40,010
也就是咱们刚才所提到的一个error 好 那么我们就来试一下这样一个命令到底是怎么样一个

63
00:03:40,010 --> 00:03:40,820
 怎么样一回事

64
00:03:41,220 --> 00:03:43,500
首先我们来IACR一个

65
00:03:43,500 --> 00:03:45,780
ABC

66
00:03:45,780 --> 00:03:47,180
那么我们的ABC

67
00:03:47,180 --> 00:03:48,660
是不是没有这样一个属性了

68
00:03:48,660 --> 00:03:49,380
那么它会是什么

69
00:03:49,380 --> 00:03:50,180
反馈值是不是0

70
00:03:50,180 --> 00:03:51,140
22

71
00:03:51,140 --> 00:03:52,480
可能呢

72
00:03:52,480 --> 00:03:53,120
老师这里呢

73
00:03:53,120 --> 00:03:54,460
其实在数据库里面之前写过ABC

74
00:03:54,460 --> 00:03:55,620
所以说呢

75
00:03:55,620 --> 00:03:56,740
它触发的值是22

76
00:03:56,740 --> 00:03:58,420
我们来换一个其他的值

77
00:03:58,420 --> 00:03:59,920
IACR

78
00:03:59,920 --> 00:04:02,100
ABCD

79
00:04:02,100 --> 00:04:03,200
是1

80
00:04:03,200 --> 00:04:03,660
大家看到没有

81
00:04:03,660 --> 00:04:05,840
因为它最开始的值是0

82
00:04:05,840 --> 00:04:06,900
我们执行一次之后

83
00:04:06,900 --> 00:04:07,460
它变成1

84
00:04:07,460 --> 00:04:08,240
我们再执行一次

85
00:04:08,240 --> 00:04:09,000
是变成2

86
00:04:09,000 --> 00:04:10,400
它会不断的去递增

87
00:04:10,400 --> 00:04:13,180
好 这里呢 我们再来看一下

88
00:04:13,180 --> 00:04:16,560
字幅创的回复

89
00:04:16,560 --> 00:04:20,060
也就是说 比如说 比如说我们去get一个属性

90
00:04:20,060 --> 00:04:24,480
在redis里面 它可以去咱们的key value 怎么样去读取 实际上是get 这样一个

91
00:04:24,480 --> 00:04:29,460
命令 咱们去比如说get一个 刚才咱们abc是不是他的只是22啊 咱们来get一下

92
00:04:29,460 --> 00:04:31,900
get abc 大家看到没有 22

93
00:04:31,900 --> 00:04:34,620
但是如果说你去get一个不存在的字

94
00:04:36,260 --> 00:04:41,120
下写 它会返回一个 和咱们GS里面的弱类型有点类似 它的意思就是空

95
00:04:41,120 --> 00:04:43,180
好 接下来我们再来看一下

96
00:04:43,180 --> 00:04:45,980
多行字不串的回复

97
00:04:45,980 --> 00:04:49,820
Case新 什么意思 我们刚才是不是去执行

98
00:04:49,820 --> 00:04:55,970
ABC的时候发现它只是22了 因为咱们在Redis的数据库里面有ABC这样一个属性

99
00:04:55,970 --> 00:04:57,260
 但是我们不知道

100
00:04:57,260 --> 00:05:00,580
Redis里面到底有哪些属性 其实就可以通过这样一个命令Case

101
00:05:00,580 --> 00:05:03,660
咱们来看一下Case新 新是一个通沛服

102
00:05:03,900 --> 00:05:06,000
大家看到有这样的一些属性ABC5

103
00:05:06,000 --> 00:05:09,480
ABCD1100它呢都是Redis里面的K

104
00:05:09,480 --> 00:05:11,920
然后呢它还会对应一些属性

105
00:05:11,920 --> 00:05:14,280
好

106
00:05:14,280 --> 00:05:16,440
接下来我们来看一下

107
00:05:16,440 --> 00:05:21,000
在Redis里面呢

108
00:05:21,000 --> 00:05:24,480
我们也可以通过CLI的方式去修改咱们Redis COMF中的配置

109
00:05:24,480 --> 00:05:29,800
也就是咱们刚才所讲义中提到的这样一段代码

110
00:05:29,800 --> 00:05:32,340
它就是咱们Redis初始化会去读取的一段配置

111
00:05:32,340 --> 00:05:35,760
那么我们实际上也可以通过命令的形式去修改它那个配置

112
00:05:35,760 --> 00:05:39,060
比如说我们要修改咱们Comp中的日子级别

113
00:05:39,060 --> 00:05:39,960
之前我们是不是讲过

114
00:05:39,960 --> 00:05:44,280
在咱们的Redis的Comp文件里面会有一些去修改日子的级别

115
00:05:44,280 --> 00:05:45,080
它也总共有四个

116
00:05:45,080 --> 00:05:47,560
比如说Warning,Debug等等

117
00:05:47,560 --> 00:05:49,440
那么如果说我们要去修改它

118
00:05:49,440 --> 00:05:53,300
咱们就可以执行Config,Set,LogLeave

119
00:05:53,300 --> 00:05:56,600
LogLeave是不是咱们的Comp,咱们的配置文件的属性呢

120
00:05:56,600 --> 00:05:57,740
咱们要把它修改为什么呢

121
00:05:57,740 --> 00:05:58,540
Warning

122
00:05:58,540 --> 00:06:00,940
这里了

123
00:06:00,940 --> 00:06:07,080
radis CLI他就会明确的返回给你一个OK说明咱们的配置呢已经修改成功了

124
00:06:07,080 --> 00:06:10,160
我们来看一下radis多数据库的一个命令

125
00:06:10,160 --> 00:06:16,560
他是什么意思咱们刚才在讲解radis抗护数他的一些配置的时候是不是有一个database的属性可以去控制radis

126
00:06:16,560 --> 00:06:20,140
他最多会可以创建多少个数据库也就是可以创建多少个radis数据库

127
00:06:20,140 --> 00:06:21,420
那么如果说

128
00:06:21,420 --> 00:06:23,720
我们要去切换他怎么样去切换

129
00:06:23,720 --> 00:06:25,520
其实呢每一个数据库他呢

130
00:06:25,520 --> 00:06:30,640
咱们radis去连接的时候他默认会选择0号数据库其实呢我们可能会16个数据库

131
00:06:30,640 --> 00:06:33,860
我们如果说要切换的话 就去通过 比如说slack1

132
00:06:33,860 --> 00:06:37,360
这里呢 咱们的命令行就会变成redis1 也就是咱们的1号数据库

133
00:06:37,360 --> 00:06:41,100
这里呢 老师呢 就不去演示了 因为我们目前呢只有只有一个

134
00:06:41,100 --> 00:06:45,200
那么后面咱们在项目中呢 可能会去建多个redis数据库 到时呢

135
00:06:45,200 --> 00:06:47,000
老师呢 再来给大家去演示

136
00:06:47,000 --> 00:06:49,760
而且呢 另外这里有一些需要注意的点

137
00:06:49,760 --> 00:06:54,080
那么redis呢 它不支持为每个数据库去设置不同的访问密码 所以呢 一个客户端

138
00:06:54,080 --> 00:06:56,680
要么可以访问全部数据库 要么呢

139
00:06:56,680 --> 00:06:59,260
连一个数据库也没有权限访问

140
00:06:59,260 --> 00:07:00,120
那么

141
00:07:00,640 --> 00:07:03,560
我们Redis它到底怎么样去使用呢

142
00:07:03,560 --> 00:07:06,840
它为什么不能对每个数据库去设置不同的防御密码

143
00:07:06,840 --> 00:07:10,520
因为它跟咱们Redis的应用场景是相关的

144
00:07:10,520 --> 00:07:14,040
你比如说我们0号数据库去存储某个应用生产环境中的数据

145
00:07:14,040 --> 00:07:18,940
那么就使用1号数据库去存储测试环境中的数据

146
00:07:18,940 --> 00:07:19,460
什么意思

147
00:07:19,460 --> 00:07:21,200
也就是说咱们的Redis

148
00:07:21,200 --> 00:07:23,260
咱们的Redis的一些数据库

149
00:07:23,260 --> 00:07:25,760
比如说咱们的0号1号到16号

150
00:07:25,760 --> 00:07:29,960
它们分别适合去存储同一个项目中不同的数据

151
00:07:29,960 --> 00:07:31,920
比如说咱们同一个项目

152
00:07:31,920 --> 00:07:35,300
它生产环境中的数据和测试环境中的数据

153
00:07:35,300 --> 00:07:37,500
但是Redis不适合

154
00:07:37,500 --> 00:07:39,160
你比如说它同样一个Redis

155
00:07:39,160 --> 00:07:40,120
分为很多的一些库

156
00:07:40,120 --> 00:07:43,520
它不适合用0号的数据库去存储A应用的数据

157
00:07:43,520 --> 00:07:46,200
而用1号数据库去存储B应用的数据

158
00:07:46,200 --> 00:07:46,680
什么意思

159
00:07:46,680 --> 00:07:47,560
说白了

160
00:07:47,560 --> 00:07:49,980
咱们去创建一个Redis一系列的数据库

161
00:07:49,980 --> 00:07:52,200
咱们一个Redis的实力只能对应一个项目

162
00:07:52,200 --> 00:07:53,000
不能对应多个项目

163
00:07:53,000 --> 00:07:53,920
这样会不是很好

164
00:07:53,920 --> 00:07:54,720
为什么呢

165
00:07:54,720 --> 00:07:56,020
因为咱们的Redis非常的轻量

166
00:07:56,020 --> 00:07:57,160
你去实力

167
00:07:57,160 --> 00:07:58,620
你去创建一个新的Redis实力

168
00:07:58,620 --> 00:07:59,540
也不会占有很多

169
00:07:59,540 --> 00:08:00,980
只会占有1兆的内存

170
00:08:00,980 --> 00:08:02,480
所以你不用去担心

171
00:08:02,480 --> 00:08:05,480
多个Redis实力会额外的占有很多的一些内存

172
00:08:05,480 --> 00:08:07,160
好 这里就是这节课的内容

173
00:08:07,160 --> 00:08:08,860
因为咱们只是去随便敲了一些

174
00:08:08,860 --> 00:08:10,860
Redis CLI的命令去找了一下感觉

175
00:08:10,860 --> 00:08:12,980
所以老师这节课就不去总结了

176
00:08:12,980 --> 00:08:15,120
同学们可以自己去回顾一下

177
00:08:15,120 --> 00:08:16,520
好 这里就是这节课的内容

