1
00:00:00,000 --> 00:00:02,040
好,我们终于结束了枯燥的第一章

2
00:00:02,040 --> 00:00:03,520
那么这里我们就进入第二章

3
00:00:03,520 --> 00:00:04,680
radis进阶与实战

4
00:00:04,680 --> 00:00:06,760
这一章的内容稍微会有趣一点

5
00:00:06,760 --> 00:00:07,680
不像咱们的第一章

6
00:00:07,680 --> 00:00:09,400
都是一些概念和一些API

7
00:00:09,400 --> 00:00:10,720
以及一些咱们数据结构的介绍

8
00:00:10,720 --> 00:00:11,980
很乏味,没意思

9
00:00:11,980 --> 00:00:13,000
那么这一章呢

10
00:00:13,000 --> 00:00:14,560
我们就会去讲解radis

11
00:00:14,560 --> 00:00:15,600
它的一些比较难度的内容

12
00:00:15,600 --> 00:00:16,700
也就是进阶以及实战

13
00:00:16,700 --> 00:00:18,180
实战会结合我们的node.js

14
00:00:18,180 --> 00:00:19,560
去实现一些小的项目

15
00:00:19,560 --> 00:00:21,480
好,首先我们这一节课会讲一下

16
00:00:21,480 --> 00:00:23,660
radis每一个概念

17
00:00:23,660 --> 00:00:24,200
叫做事物

18
00:00:24,200 --> 00:00:27,200
之前我们是不是举过一个付款和收钱的例子

19
00:00:27,200 --> 00:00:29,340
这里呢我们同样再举一个例子

20
00:00:29,340 --> 00:00:30,240
也就是咱们微博

21
00:00:30,240 --> 00:00:31,560
微博经常用吧

22
00:00:31,560 --> 00:00:33,820
微博呢是不是有一个功能叫关注和被关注

23
00:00:33,820 --> 00:00:36,080
那么关注和被关注

24
00:00:36,080 --> 00:00:38,480
我们如果说要去实现这样一个功能

25
00:00:38,480 --> 00:00:41,220
我们是不是需要去用一个字段去存储

26
00:00:41,220 --> 00:00:43,400
比如说我们会新建一个user

27
00:00:43,400 --> 00:00:44,660
然后加上id

28
00:00:44,660 --> 00:00:46,740
后面呢followers

29
00:00:46,740 --> 00:00:47,500
followers是什么意思

30
00:00:47,500 --> 00:00:47,860
粉丝

31
00:00:47,860 --> 00:00:49,920
然后呢following有哪些人关注了你

32
00:00:49,920 --> 00:00:52,360
我们用两个字段去存储这一个关系

33
00:00:52,360 --> 00:00:52,740
好

34
00:00:52,740 --> 00:00:53,780
那么我们来看一下

35
00:00:53,780 --> 00:00:56,000
咱们怎么样去实现这个功能

36
00:00:56,000 --> 00:00:57,400
怎么样去体现事物

37
00:00:57,400 --> 00:00:58,920
它在Reddit里面的一个作用

38
00:00:58,920 --> 00:01:00,700
我们直接来通过代码来理解

39
00:01:00,700 --> 00:01:05,880
首先

40
00:01:05,880 --> 00:01:08,140
老规矩三天过来

41
00:01:08,140 --> 00:01:10,560
好

42
00:01:10,560 --> 00:01:12,700
然后我们功能是什么

43
00:01:12,700 --> 00:01:13,420
需求是什么

44
00:01:13,420 --> 00:01:15,760
关注与被关注

45
00:01:15,760 --> 00:01:16,060
好

46
00:01:16,060 --> 00:01:17,980
首先我们是不是去建立一个方式了

47
00:01:17,980 --> 00:01:19,100
比如说叫做Follow

48
00:01:19,100 --> 00:01:20,580
也就是一个关注函数

49
00:01:20,580 --> 00:01:21,980
因为我们要去实现这样一个功能

50
00:01:21,980 --> 00:01:23,000
实现关注与被关注

51
00:01:23,000 --> 00:01:24,440
也就是我们在使用的时候

52
00:01:24,440 --> 00:01:27,660
我们是不是直接调用follow

53
00:01:27,660 --> 00:01:29,320
比如说我关注了

54
00:01:29,320 --> 00:01:31,940
你

55
00:01:31,940 --> 00:01:33,840
我们这样去调用

56
00:01:33,840 --> 00:01:34,480
调用一个方法

57
00:01:34,480 --> 00:01:35,420
是不是就实现了我关注你

58
00:01:35,420 --> 00:01:36,880
然后在redis里面会存储一些数据

59
00:01:36,880 --> 00:01:38,040
好 接下来我们来看一下

60
00:01:38,040 --> 00:01:39,500
首先它介绍两个参数

61
00:01:39,500 --> 00:01:40,480
一个是我 一个是你

62
00:01:40,480 --> 00:01:41,640
我是什么

63
00:01:41,640 --> 00:01:42,620
是不是咱们的currentuser

64
00:01:42,620 --> 00:01:43,420
也就是目标

65
00:01:43,420 --> 00:01:45,120
也就是咱们当前用户是谁

66
00:01:45,120 --> 00:01:46,020
第二个参数呢

67
00:01:46,020 --> 00:01:46,960
targetuser

68
00:01:46,960 --> 00:01:48,200
也就是我们的目标用户是谁

69
00:01:48,200 --> 00:01:48,980
我关注你

70
00:01:48,980 --> 00:01:49,900
我是current

71
00:01:49,900 --> 00:01:50,680
你是target

72
00:01:50,680 --> 00:01:53,260
好 那我们再去client点

73
00:01:53,260 --> 00:01:53,860
什么是不是存储

74
00:01:53,860 --> 00:01:55,080
此时是不是要做存储操作

75
00:01:55,080 --> 00:01:55,920
存储

76
00:01:55,920 --> 00:01:56,740
那么我们存储

77
00:01:56,740 --> 00:01:57,720
粉丝关系

78
00:01:57,720 --> 00:01:58,740
用一种什么样的数据结构

79
00:01:58,740 --> 00:01:59,200
比较好

80
00:01:59,200 --> 00:02:00,700
我们是不是学过

81
00:02:00,700 --> 00:02:00,960
字不串

82
00:02:00,960 --> 00:02:01,580
闪链集合

83
00:02:01,580 --> 00:02:02,140
游戏集合

84
00:02:02,140 --> 00:02:03,380
那么粉丝关系用什么

85
00:02:03,380 --> 00:02:04,320
用列表好不好

86
00:02:04,320 --> 00:02:05,880
没必要吧

87
00:02:05,880 --> 00:02:06,580
闪链了

88
00:02:06,580 --> 00:02:08,000
不行吧

89
00:02:08,000 --> 00:02:08,380
因为我们

90
00:02:08,380 --> 00:02:09,420
一个人

91
00:02:09,420 --> 00:02:10,280
他会有很多的粉丝

92
00:02:10,280 --> 00:02:12,580
最好的情况

93
00:02:12,580 --> 00:02:13,440
是不是用集合呀

94
00:02:13,440 --> 00:02:14,080
为什么呀

95
00:02:14,080 --> 00:02:15,340
因为我们粉丝没有顺序

96
00:02:15,340 --> 00:02:16,260
不需要排序

97
00:02:16,260 --> 00:02:17,180
所以说那最好

98
00:02:17,180 --> 00:02:17,960
就是用集合

99
00:02:17,960 --> 00:02:19,820
那么集合怎么去添加呢

100
00:02:19,820 --> 00:02:20,080
比如说

101
00:02:20,080 --> 00:02:20,600
我们去

102
00:02:20,600 --> 00:02:22,020
集合是不是通过

103
00:02:22,020 --> 00:02:24,200
好,那我们比如说指定一个User

104
00:02:24,200 --> 00:02:25,380
User

105
00:02:25,380 --> 00:02:26,460
Current

106
00:02:26,460 --> 00:02:27,220
CurrentUser

107
00:02:27,220 --> 00:02:28,820
CurrentUser

108
00:02:28,820 --> 00:02:33,360
CurrentUser

109
00:02:33,360 --> 00:02:34,700
好,首先

110
00:02:34,700 --> 00:02:37,160
首先我们这样一个动作是什么呀

111
00:02:37,160 --> 00:02:39,240
我们这样一个动作是不是我关注你

112
00:02:39,240 --> 00:02:44,040
好,我关注你

113
00:02:44,040 --> 00:02:47,500
那么我关注你

114
00:02:47,500 --> 00:02:48,800
我们去存储一个User

115
00:02:48,800 --> 00:02:49,660
然后ID是我

116
00:02:49,660 --> 00:02:50,760
ID是我

117
00:02:50,760 --> 00:02:52,300
我关注你的时候我的什么

118
00:02:52,300 --> 00:02:53,820
哪一项列表里面会添加

119
00:02:53,820 --> 00:02:55,940
就是我的关注列表会添加

120
00:02:55,940 --> 00:02:59,860
关注列表会添加

121
00:02:59,860 --> 00:03:01,760
也就是说我们的user

122
00:03:01,760 --> 00:03:05,200
我的关注会添加你

123
00:03:05,200 --> 00:03:06,640
关注会添加你

124
00:03:06,640 --> 00:03:06,960
你是谁

125
00:03:06,960 --> 00:03:07,680
你是target

126
00:03:07,680 --> 00:03:12,980
好这里呢

127
00:03:12,980 --> 00:03:13,920
我们就实现了一个

128
00:03:13,920 --> 00:03:15,320
我关注你

129
00:03:15,320 --> 00:03:17,020
那么这里就结束了吗

130
00:03:17,020 --> 00:03:19,540
我只需要在我的关注列表存储了

131
00:03:19,540 --> 00:03:20,540
target就够了吗

132
00:03:20,540 --> 00:03:24,820
好像还不够 为什么 因为是不是要在你的粉丝里面去添加我呀 我关注了你

133
00:03:24,820 --> 00:03:27,900
然后呢 我就成为了你的粉丝 对吧 所以我们还需要去添加

134
00:03:27,900 --> 00:03:29,420
哦 这里是s add

135
00:03:29,420 --> 00:03:31,200
s add

136
00:03:31,200 --> 00:03:35,300
user

137
00:03:35,300 --> 00:03:40,320
我是你的粉丝 所以呢 id 此时要存到 是不是要存到你的集合里面去

138
00:03:40,320 --> 00:03:41,780
所以就是target user

139
00:03:41,780 --> 00:03:45,460
我是你的什么粉丝 存谁 存我吧

140
00:03:45,460 --> 00:03:48,180
这里呢 就有实现了我们的

141
00:03:50,540 --> 00:03:52,480
实现了我们这样一个

142
00:03:52,480 --> 00:03:53,980
关注与被关注的需求

143
00:03:53,980 --> 00:03:55,180
不过同学们注意

144
00:03:55,180 --> 00:03:56,060
这里有没有什么问题

145
00:03:56,060 --> 00:03:57,580
有没有什么问题

146
00:03:57,580 --> 00:03:59,820
因为如果

147
00:03:59,820 --> 00:04:00,820
如果

148
00:04:00,820 --> 00:04:02,500
如果我们在这一行

149
00:04:02,500 --> 00:04:03,780
如果报错了怎么办

150
00:04:03,780 --> 00:04:04,880
咱们在这一行

151
00:04:04,880 --> 00:04:06,320
咱们在第二行

152
00:04:06,320 --> 00:04:07,940
假如说我们在第二行报错

153
00:04:07,940 --> 00:04:09,000
这里报错

154
00:04:09,000 --> 00:04:10,860
会发生一件什么事情

155
00:04:10,860 --> 00:04:12,580
是不是

156
00:04:12,580 --> 00:04:14,900
我把你添加到我的关注列表

157
00:04:14,900 --> 00:04:15,580
但是呢

158
00:04:15,580 --> 00:04:17,340
在添加粉丝的过程中

159
00:04:17,340 --> 00:04:18,080
假如说我们

160
00:04:18,080 --> 00:04:20,120
这行代码我们执行错了

161
00:04:20,120 --> 00:04:22,840
是不是仅仅把你加到的我的观众列表

162
00:04:22,840 --> 00:04:24,120
但是呢我没有成为你的粉丝

163
00:04:24,120 --> 00:04:25,840
这样我们的程序是不是会有问题啊

164
00:04:25,840 --> 00:04:27,480
咱们的数据存储不一致啊

165
00:04:27,480 --> 00:04:28,880
所以呢在Reddit里面

166
00:04:28,880 --> 00:04:30,080
事物就解决了这样一个问题

167
00:04:30,080 --> 00:04:32,480
好那么我们来看一下

168
00:04:32,480 --> 00:04:34,680
在Reddit里面的事物是怎么样去使用的

169
00:04:34,680 --> 00:04:41,060
其实在Reddit里面的事物非常简单

170
00:04:41,060 --> 00:04:43,800
它的一个命令叫做Multi

171
00:04:43,800 --> 00:04:45,120
Multi是什么意思

172
00:04:45,120 --> 00:04:46,460
在英文里面它的翻译是什么

173
00:04:46,460 --> 00:04:47,480
我们一起来看一下

174
00:04:47,480 --> 00:04:48,540
是不是多种啊

175
00:04:48,540 --> 00:04:49,940
其实和事物还没什么联系

176
00:04:49,940 --> 00:04:51,100
但是在Reddit里面

177
00:04:51,100 --> 00:04:52,960
就是通过这个关键字去实现事物的

178
00:04:52,960 --> 00:04:54,160
事物是什么意思呢

179
00:04:54,160 --> 00:04:56,100
事物它就是一组命令的集合

180
00:04:56,100 --> 00:04:58,440
你一个事物中的命令

181
00:04:58,440 --> 00:04:59,020
要么都执行

182
00:04:59,020 --> 00:04:59,680
要么都不执行

183
00:04:59,680 --> 00:05:01,600
什么意思

184
00:05:01,600 --> 00:05:02,300
什么意思

185
00:05:02,300 --> 00:05:02,860
也就是说

186
00:05:02,860 --> 00:05:05,440
如果说我们用事物去添加我们的集合

187
00:05:05,440 --> 00:05:06,900
你要么这两行都执行

188
00:05:06,900 --> 00:05:07,760
要么都不执行

189
00:05:07,760 --> 00:05:09,360
你不能说一行执行

190
00:05:09,360 --> 00:05:09,900
一行不执行

191
00:05:09,900 --> 00:05:12,180
因为你其中某一行可能会报错

192
00:05:12,180 --> 00:05:14,160
所以说我们可以用事物去解决这个问题

193
00:05:14,160 --> 00:05:15,380
好

194
00:05:15,380 --> 00:05:16,340
那么我们就来看一下

195
00:05:16,340 --> 00:05:18,020
怎么样去使用事物

196
00:05:18,020 --> 00:05:23,820
假如说我这里举个例子

197
00:05:23,820 --> 00:05:25,260
首先我们执行一条命令

198
00:05:25,260 --> 00:05:25,780
是什么

199
00:05:25,780 --> 00:05:26,220
multi

200
00:05:26,220 --> 00:05:28,080
此时呢

201
00:05:28,080 --> 00:05:29,420
就会进入我们一个事务模式

202
00:05:29,420 --> 00:05:30,420
然后我们去set一个

203
00:05:30,420 --> 00:05:31,720
比如说我们去set一个字段

204
00:05:31,720 --> 00:05:32,520
叫做就叫事务

205
00:05:32,520 --> 00:05:33,480
给他一个值

206
00:05:33,480 --> 00:05:34,400
叫做

207
00:05:34,400 --> 00:05:35,460
hello

208
00:05:35,460 --> 00:05:37,320
此时会有一个返回值

209
00:05:37,320 --> 00:05:39,300
困硬的

210
00:05:39,300 --> 00:05:39,960
我们先不管

211
00:05:39,960 --> 00:05:41,920
此时我们是不是设置了一个

212
00:05:41,920 --> 00:05:42,760
字段事务

213
00:05:42,760 --> 00:05:43,220
叫做hello

214
00:05:43,220 --> 00:05:44,780
但是如果说我们在下一行

215
00:05:44,780 --> 00:05:45,340
下一行

216
00:05:45,340 --> 00:05:46,240
我们瞎执行

217
00:05:46,240 --> 00:05:46,860
瞎写

218
00:05:46,860 --> 00:05:47,580
此时报错

219
00:05:47,580 --> 00:05:48,940
那么大家觉得

220
00:05:48,940 --> 00:05:50,360
我们的事物

221
00:05:50,360 --> 00:05:51,340
这样一个字段

222
00:05:51,340 --> 00:05:52,400
hello它写进去没有

223
00:05:52,400 --> 00:05:54,300
然后事物它怎么去退出

224
00:05:54,300 --> 00:05:55,060
它用这样一个命令

225
00:05:55,060 --> 00:05:56,620
exec

226
00:05:56,620 --> 00:05:57,920
比如说我们exec退出

227
00:05:57,920 --> 00:05:58,500
好

228
00:05:58,500 --> 00:05:59,820
此时它会有些报错信息

229
00:05:59,820 --> 00:06:00,340
这里后面会讲

230
00:06:00,340 --> 00:06:01,400
我们来看一下

231
00:06:01,400 --> 00:06:03,440
我们来get一下事物

232
00:06:03,440 --> 00:06:04,320
hello它设置成功没有

233
00:06:04,320 --> 00:06:04,640
get

234
00:06:04,640 --> 00:06:05,640
事物

235
00:06:05,640 --> 00:06:06,480
大家可以看到

236
00:06:06,480 --> 00:06:07,960
事物其实是没有设置成功的

237
00:06:07,960 --> 00:06:09,260
也就是说我们刚才的

238
00:06:09,260 --> 00:06:10,240
关注与被关注

239
00:06:10,240 --> 00:06:11,280
是不是就可以用我们的事物

240
00:06:11,280 --> 00:06:12,140
去解决这个问题

241
00:06:12,140 --> 00:06:13,740
我们执行了两条命令

242
00:06:13,740 --> 00:06:15,400
首先我们给事物

243
00:06:15,400 --> 00:06:16,320
事物这样一个字段

244
00:06:16,320 --> 00:06:17,180
写了一个字叫hello

245
00:06:17,180 --> 00:06:19,040
然后呢后面又执行一条命令出错了

246
00:06:19,040 --> 00:06:20,700
然后我们退出事物通过EXEC

247
00:06:20,700 --> 00:06:22,580
但是如果说你其中有一条命令出错

248
00:06:22,580 --> 00:06:24,460
那么整个事物它都会执行失败

249
00:06:24,460 --> 00:06:28,460
这里呢就是我们事物它的一个概念

250
00:06:28,460 --> 00:06:30,920
我们再来看一下

251
00:06:30,920 --> 00:06:32,140
在事物里面

252
00:06:32,140 --> 00:06:32,840
在什么呢

253
00:06:32,840 --> 00:06:34,580
Redis的事物里面它的错误是怎么样去处理

254
00:06:34,580 --> 00:06:38,200
首先语法错误

255
00:06:38,200 --> 00:06:40,100
它可以发现并且中断后面的执行

256
00:06:40,100 --> 00:06:41,300
运行错误呢

257
00:06:41,300 --> 00:06:42,240
Redis无法发现

258
00:06:42,240 --> 00:06:42,740
什么意思

259
00:06:42,740 --> 00:06:43,260
什么意思

260
00:06:43,260 --> 00:06:44,160
我们刚才

261
00:06:44,160 --> 00:06:45,680
我们刚才是不是执行了一条

262
00:06:45,680 --> 00:06:47,780
我们刚才这段代码执行的是什么

263
00:06:47,780 --> 00:06:48,480
是不是语法错误啊

264
00:06:48,480 --> 00:06:48,800
同学们

265
00:06:48,800 --> 00:06:51,120
我们刚才执行这条是不是语法错误啊

266
00:06:51,120 --> 00:06:52,520
因为咱们的ASDA什么什么什么

267
00:06:52,520 --> 00:06:53,200
是没有这条命令

268
00:06:53,200 --> 00:06:53,880
它是语法错误

269
00:06:53,880 --> 00:06:55,020
它是可以发现

270
00:06:55,020 --> 00:06:57,120
而且呢也可以去中断

271
00:06:57,120 --> 00:07:03,580
中断我们的一个执行

272
00:07:03,580 --> 00:07:04,620
好

273
00:07:04,620 --> 00:07:05,600
那么我们来看一下

274
00:07:05,600 --> 00:07:06,100
有一种情况

275
00:07:06,100 --> 00:07:07,060
它是不能够被发现的

276
00:07:07,060 --> 00:07:07,840
也就是运行错误

277
00:07:07,840 --> 00:07:08,980
那么什么是运行错误呢

278
00:07:08,980 --> 00:07:10,540
这里呢我来给大家举个例子

279
00:07:10,540 --> 00:07:12,920
比如说我们同样的进入

280
00:07:12,920 --> 00:07:14,500
进入咱们的事物

281
00:07:14,500 --> 00:07:16,120
母体

282
00:07:16,120 --> 00:07:16,540
好

283
00:07:16,540 --> 00:07:18,100
比如说我们去SET一个

284
00:07:18,100 --> 00:07:19,960
451

285
00:07:19,960 --> 00:07:21,160
它的值呢叫做

286
00:07:21,160 --> 00:07:22,060
也叫哈喽

287
00:07:22,060 --> 00:07:22,840
好

288
00:07:22,840 --> 00:07:23,560
这里呢

289
00:07:23,560 --> 00:07:24,560
我们是不是已经设置成功了

290
00:07:24,560 --> 00:07:26,160
但是我们现在去执行一条错误的命令

291
00:07:26,160 --> 00:07:27,200
比如说我们去L push

292
00:07:27,200 --> 00:07:28,320
451

293
00:07:28,320 --> 00:07:29,560
哈喽

294
00:07:29,560 --> 00:07:31,040
大家觉得这里会不会报错

295
00:07:31,040 --> 00:07:32,000
一定会吧

296
00:07:32,000 --> 00:07:32,780
为什么呀

297
00:07:32,780 --> 00:07:33,740
因为我们的SET

298
00:07:33,740 --> 00:07:34,820
SET是不是闪电

299
00:07:34,820 --> 00:07:35,600
但是呢

300
00:07:35,600 --> 00:07:36,940
我们又给它去L push

301
00:07:36,940 --> 00:07:37,980
L push式的列表吧

302
00:07:37,980 --> 00:07:38,880
这里肯定会冲突

303
00:07:38,880 --> 00:07:39,540
执行

304
00:07:39,540 --> 00:07:40,040
Quin

305
00:07:40,040 --> 00:07:40,600
它没有报错

306
00:07:40,600 --> 00:07:41,300
但是我们在

307
00:07:41,300 --> 00:07:42,720
EXEC

308
00:07:42,720 --> 00:07:45,400
退出的时候

309
00:07:45,400 --> 00:07:46,360
它会告诉我们错误

310
00:07:46,360 --> 00:07:48,480
怎么可以hold in the run kind of value

311
00:07:48,480 --> 00:07:49,320
说明了

312
00:07:49,320 --> 00:07:51,900
数据类型有错误

313
00:07:51,900 --> 00:07:52,640
那么这里

314
00:07:52,640 --> 00:07:54,060
按照我们刚才的说法

315
00:07:54,060 --> 00:07:55,020
你是不是有一个地方报错

316
00:07:55,020 --> 00:07:56,740
那么我们两条命令执行都会失败

317
00:07:56,740 --> 00:07:57,980
但是我们来看一下

318
00:07:57,980 --> 00:07:59,640
451

319
00:07:59,640 --> 00:08:00,360
它到底有没有值

320
00:08:00,360 --> 00:08:01,420
是不是有一个值叫做hello

321
00:08:01,420 --> 00:08:02,480
好

322
00:08:02,480 --> 00:08:04,060
那么在redis里面

323
00:08:04,060 --> 00:08:05,640
它的语法错误是可以发现的

324
00:08:05,640 --> 00:08:06,900
但是运行错误

325
00:08:06,900 --> 00:08:07,800
是不是无法发现的

326
00:08:07,800 --> 00:08:08,720
你比如说我们刚才

327
00:08:08,720 --> 00:08:10,120
操作了一个集合

328
00:08:10,120 --> 00:08:10,780
然后呢

329
00:08:10,780 --> 00:08:12,400
用l push去操作这一个集合

330
00:08:12,400 --> 00:08:13,940
它们两个类型是不是发生了一些冲突

331
00:08:13,940 --> 00:08:15,780
但是在我们的结果里面

332
00:08:15,780 --> 00:08:17,420
是不是依然咱们的set事物成功了

333
00:08:17,420 --> 00:08:19,360
说明咱们的运行错误

334
00:08:19,360 --> 00:08:20,760
Redis它是没法发现的

335
00:08:20,760 --> 00:08:21,920
那么怎么样去解决这个问题呢

336
00:08:21,920 --> 00:08:24,740
其实也没有什么更好的办法

337
00:08:24,740 --> 00:08:25,400
因为在语言层面

338
00:08:25,400 --> 00:08:26,600
Redis它没有解决这个问题

339
00:08:26,600 --> 00:08:28,240
而且在Redis里面

340
00:08:28,240 --> 00:08:30,660
它也没有咱们关系数据库提供的一个回滚功能

341
00:08:30,660 --> 00:08:32,980
就类似咱们Gate里面的一个回滚

342
00:08:32,980 --> 00:08:35,700
所以说开发者必须在事物执行出错后

343
00:08:35,700 --> 00:08:36,820
自己收拾剩下的烂摊子

344
00:08:36,820 --> 00:08:37,460
说白了

345
00:08:37,460 --> 00:08:38,540
Redis这些运行错误

346
00:08:38,540 --> 00:08:39,360
你必须自己去解决

347
00:08:39,360 --> 00:08:41,340
他在事物里面没有办法帮你去做

348
00:08:41,340 --> 00:08:43,580
那么有什么比较好的方法去解决这样一个问题呢

349
00:08:43,580 --> 00:08:45,060
或者说你怎么样去在开发中去避免呢

350
00:08:45,060 --> 00:08:47,740
你可以去保证你命名的一些规范

351
00:08:47,740 --> 00:08:50,740
包括来你去把你的数据库给规划好

352
00:08:50,740 --> 00:08:51,520
你只能这样去解决

353
00:08:51,520 --> 00:08:53,680
你比如说我们js里面有些运行错误

354
00:08:53,680 --> 00:08:54,420
是不是也没法去解决

355
00:08:54,420 --> 00:08:55,000
我们怎么去做

356
00:08:55,000 --> 00:08:57,320
是不是可以去通过引入咱们的一些jsLint

357
00:08:57,320 --> 00:08:58,880
包括去跑一些自动化的脚本

358
00:08:58,880 --> 00:08:59,340
去检测

359
00:08:59,340 --> 00:09:01,140
包括咱们使用js一种强类型语言

360
00:09:01,140 --> 00:09:02,600
比如说TypesCraft

361
00:09:02,600 --> 00:09:04,520
在街市里面可以通过这些方式去解决

362
00:09:04,520 --> 00:09:05,880
那么在redis里面同样的

363
00:09:05,880 --> 00:09:08,580
你也可以去利用一些工具去检查它的类型

364
00:09:08,580 --> 00:09:09,480
包括了一些令塔工具

365
00:09:09,480 --> 00:09:11,440
咱们只能借助外界去解决

366
00:09:11,440 --> 00:09:14,000
但是呢无法通过它的语言本身去解决这个问题

367
00:09:14,000 --> 00:09:15,920
好这里呢就是这几个内容

368
00:09:15,920 --> 00:09:16,760
我们来总结一下

369
00:09:16,760 --> 00:09:20,820
刚才我们是不是讲解了

370
00:09:20,820 --> 00:09:22,000
redis里面那个事物

371
00:09:22,000 --> 00:09:23,260
那么事物它是做什么的

372
00:09:23,260 --> 00:09:24,800
我们总结为一句话

373
00:09:24,800 --> 00:09:25,500
是不是就是执行

374
00:09:25,500 --> 00:09:28,100
多条命令

375
00:09:28,100 --> 00:09:29,680
而且呢

376
00:09:29,680 --> 00:09:32,220
只要出错

377
00:09:32,220 --> 00:09:32,820
就什么

378
00:09:32,820 --> 00:09:33,340
全部中断

379
00:09:33,340 --> 00:09:34,160
对吧

380
00:09:34,160 --> 00:09:34,860
好

381
00:09:34,860 --> 00:09:35,820
那么他的命令是什么

382
00:09:35,820 --> 00:09:37,100
事物他的命令是什么

383
00:09:37,100 --> 00:09:37,400
是不是

384
00:09:37,400 --> 00:09:38,380
母体翻译为

385
00:09:38,380 --> 00:09:39,880
他的翻译为多种

386
00:09:39,880 --> 00:09:40,520
多种的

387
00:09:40,520 --> 00:09:41,280
多种

388
00:09:41,280 --> 00:09:42,060
我也不知道为什么

389
00:09:42,060 --> 00:09:43,280
使用了这样一个单词

390
00:09:43,280 --> 00:09:44,020
好

391
00:09:44,020 --> 00:09:46,560
那么在事物里面

392
00:09:46,560 --> 00:09:47,200
他的错误

393
00:09:47,200 --> 00:09:48,280
他的错误处理有几种

394
00:09:48,280 --> 00:09:49,820
错误处理

395
00:09:49,820 --> 00:09:53,300
是不是有两种啊

396
00:09:53,300 --> 00:09:54,100
有哪两种

397
00:09:54,100 --> 00:09:55,340
哪两种错误处理的方式

398
00:09:55,340 --> 00:09:58,280
是不是一种是语法错误

399
00:09:58,280 --> 00:10:00,260
一种是运行错误

400
00:10:00,260 --> 00:10:01,080
它们有什么区别

401
00:10:01,080 --> 00:10:02,680
语法错误是不是可以

402
00:10:02,680 --> 00:10:04,400
中断全部

403
00:10:04,400 --> 00:10:06,180
但是运行错误呢

404
00:10:06,180 --> 00:10:08,140
无法中断

405
00:10:08,140 --> 00:10:09,420
怎么办

406
00:10:09,420 --> 00:10:10,540
你要靠自己去解决

407
00:10:10,540 --> 00:10:11,280
好

408
00:10:11,280 --> 00:10:12,720
这里呢就是咱们这一节的内容

