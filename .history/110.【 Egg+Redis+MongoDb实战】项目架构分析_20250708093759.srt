1
00:00:00,000 --> 00:00:04,800
好 那么之前呢 我们是不是已经讲解过了咱们项目的一个项目图 接下来我们来看一下咱们项目的一个整体架构

2
00:00:04,800 --> 00:00:07,640
其实我们的整体架构呢 也非常的

3
00:00:07,640 --> 00:00:08,640
简单

4
00:00:08,640 --> 00:00:11,680
首先呢 绿色部分呢 代表我们的一个客户端 也就是咱们的浏览器

5
00:00:11,680 --> 00:00:13,760
那么Redis呢 顾名思义 也就是咱们的

6
00:00:13,760 --> 00:00:15,880
Redis 数据库 那么蒙哥DB呢

7
00:00:15,880 --> 00:00:19,000
也是咱们的一个数据库 那么他们有什么区别 同学们回忆一下

8
00:00:19,000 --> 00:00:21,760
Redis它是不是一种类存型数据库 它的速度非常快呀

9
00:00:22,000 --> 00:00:23,540
那么mongodb呢其实和我们mongodb一样

10
00:00:23,540 --> 00:00:24,560
它是一种存储型

11
00:00:24,560 --> 00:00:25,840
存储在咱们的硬盘中的

12
00:00:25,840 --> 00:00:27,120
其实大家可以把理解为mongodb呢

13
00:00:27,120 --> 00:00:28,400
其实就是我们的一个mongodb

14
00:00:28,400 --> 00:00:30,440
之所以老师这里呢去选用mongodb

15
00:00:30,440 --> 00:00:32,500
是因为呢同学们呢可能对nodejs比较熟悉一点

16
00:00:32,500 --> 00:00:33,000
对吧

17
00:00:33,000 --> 00:00:33,520
那么

18
00:00:33,520 --> 00:00:36,340
同样的咱们在那种架构如果说你把mongodb换成mongodb

19
00:00:36,340 --> 00:00:37,620
也是完全可以行得通的

20
00:00:37,620 --> 00:00:38,380
好

21
00:00:38,380 --> 00:00:40,680
那我们来看一下他们之间的一个关系

22
00:00:40,680 --> 00:00:41,960
首先呢咱们的一个登录

23
00:00:41,960 --> 00:00:43,240
是不是需要保持登录Ti

24
00:00:43,240 --> 00:00:45,040
那么保持登录Ti这一块呢其实就

25
00:00:45,040 --> 00:00:46,320
需要去使用到咱们的一个

26
00:00:46,320 --> 00:00:48,040
redis和咱们的一个浏览器进行通信

27
00:00:48,040 --> 00:00:50,120
那么其实这幅图可以非常清晰的看出来

28
00:00:50,120 --> 00:00:51,120
我们保持登录态

29
00:00:51,120 --> 00:00:52,720
是完全和mongodb无关的

30
00:00:52,720 --> 00:00:54,620
只需要redis就可以去保持咱们的登录态

31
00:00:54,620 --> 00:00:55,200
那么

32
00:00:55,200 --> 00:00:57,460
再来看一下我们的一个登录业务

33
00:00:57,460 --> 00:00:58,320
我们的登录业务呢

34
00:00:58,320 --> 00:00:59,500
其实是根据咱们的浏览器

35
00:00:59,500 --> 00:01:01,480
redis mongodb三者所联合完成的

36
00:01:01,480 --> 00:01:02,860
这里呢就是我们整体项目的一个架构

37
00:01:02,860 --> 00:01:04,380
可能同学们呢听到这里了稍微有点懵

38
00:01:04,380 --> 00:01:05,500
不知道是怎么回事

39
00:01:05,500 --> 00:01:06,360
没关系

40
00:01:06,360 --> 00:01:07,360
咱们慢慢的来理解

41
00:01:07,360 --> 00:01:08,220
相信同学们呢

42
00:01:08,220 --> 00:01:09,380
在学习完之后

43
00:01:09,380 --> 00:01:10,640
完全可以理解这一套架构

44
00:01:10,640 --> 00:01:11,860
大家不要担心

45
00:01:11,860 --> 00:01:13,620
好 接下来我们来看一下咱们的一个学习目标

46
00:01:13,620 --> 00:01:15,980
首先呢 可能同学们呢在学习

47
00:01:15,980 --> 00:01:17,900
这一张的内容呢需要有一些潜质知识

48
00:01:17,900 --> 00:01:19,660
大家呢可能需要去看一下一一级的视频

49
00:01:19,660 --> 00:01:21,260
那么一一级的视频在哪里呢

50
00:01:21,260 --> 00:01:22,800
其实在博学谷里面

51
00:01:22,800 --> 00:01:23,720
nodejs进阶

52
00:01:23,720 --> 00:01:25,780
Web应用开发框架里面

53
00:01:25,780 --> 00:01:27,640
有很大一部分内容呢是去

54
00:01:27,640 --> 00:01:28,580
是在讲解一一级

55
00:01:28,580 --> 00:01:29,460
比如说呢我们来看一下目录

56
00:01:29,460 --> 00:01:30,160
大家可以看到

57
00:01:30,160 --> 00:01:32,660
基本上都是去讲一级的内容

58
00:01:32,660 --> 00:01:36,440
那么这期的课程呢其实也是老师我去录制的

59
00:01:36,440 --> 00:01:37,980
所以说如果说有兴趣同学到这里了

60
00:01:37,980 --> 00:01:39,760
好 那么如果说有兴趣同学呢

61
00:01:39,760 --> 00:01:42,840
可能呢也需要去看一下一期的视频 因为我们这节课的实战是基于

62
00:01:42,840 --> 00:01:46,420
一期继续讲解了 当然了 如果说你的nodejs基础比较扎实

63
00:01:46,420 --> 00:01:51,040
你不看也没关系 因为我们整体的课程目标呢 咱们其实是去了解登录流程

64
00:01:51,040 --> 00:01:54,100
那么一期其实只是一个框架 你知道老师在做什么 也可以

65
00:01:54,100 --> 00:01:57,680
首先我们 接下来我们来看一下咱们的学习目标

66
00:01:57,680 --> 00:02:02,560
第一步呢 我们可能需要了解一下cookie和session之间的关系 因为之所以同学们看了这样一套架构

67
00:02:02,560 --> 00:02:03,580
可能只是心里有点懵

68
00:02:04,080 --> 00:02:07,160
咱们既要使用到redis 又要mongodb 还要设计到咱们浏览器

69
00:02:07,160 --> 00:02:10,220
你还要去使用egg 因为egg是我们的nodejs框架

70
00:02:10,220 --> 00:02:11,760
那么可能学习东西有点多

71
00:02:11,760 --> 00:02:12,780
其实他们的基础

72
00:02:12,780 --> 00:02:15,600
就是咱们http 里面的cookie和session

73
00:02:15,600 --> 00:02:18,160
如果说同学们想去理解这一套架构

74
00:02:18,160 --> 00:02:20,720
可能对cookie和session需要有一个非常清晰的理解

75
00:02:20,720 --> 00:02:22,760
不然可能后面的课程会有点难度

76
00:02:22,760 --> 00:02:23,540
接下来

77
00:02:23,540 --> 00:02:25,080
当然也是我们比较重要的部分

78
00:02:25,080 --> 00:02:27,880
我们需要去从前后端的角度全面的去了解

79
00:02:27,880 --> 00:02:29,940
登陆流程那么学习完咱们这一期的课程之后

80
00:02:29,940 --> 00:02:30,700
就可以学习到

81
00:02:31,220 --> 00:02:33,780
然后呢 我们还需要去了解Redis在登录流程中的一个作用

82
00:02:33,780 --> 00:02:36,340
那么可能有的同学们呢 在学习了第二章之后

83
00:02:36,340 --> 00:02:39,160
大家还是心里表梦 我Redis到底是做什么的

84
00:02:39,160 --> 00:02:42,480
虽然说我们都知道Redis是内层性数据库 那么我们什么时候用什么时候不用

85
00:02:42,480 --> 00:02:43,260
 可能大家还是表梦

86
00:02:43,260 --> 00:02:45,040
所以说呢 我们来看一下在这样一个项目中

87
00:02:45,040 --> 00:02:49,140
Redis它使用场景是什么 我们为什么要去使用它 接下来我们来看一下学习原因

88
00:02:49,140 --> 00:02:53,500
其实老师呢 为什么要去设计这样一系列课程 也就是咱们为什么要设计这样的一个实战

89
00:02:54,260 --> 00:02:55,240
为什么要去实现登陆流程

90
00:02:55,240 --> 00:02:55,740
其实呢

91
00:02:55,740 --> 00:02:57,840
理解登陆流程对于前端来讲非常重要

92
00:02:57,840 --> 00:02:59,320
同学们一定要注意非常重要

93
00:02:59,320 --> 00:03:01,160
为什么这么说呢

94
00:03:01,160 --> 00:03:03,600
因为登陆流程它在面试中的出现频率非常高

95
00:03:03,600 --> 00:03:06,020
而且我个人曾经在面试中非常喜欢问你这样的问题

96
00:03:06,020 --> 00:03:07,820
比如说你做的项目是如何进行登陆

97
00:03:07,820 --> 00:03:09,500
你如何保持登陆态的

98
00:03:09,500 --> 00:03:11,260
而且我发现一个很奇怪的问题

99
00:03:11,260 --> 00:03:13,400
很多呢有三年到五年经验的一个前端工程师

100
00:03:13,400 --> 00:03:15,260
80%以上的同学

101
00:03:15,260 --> 00:03:17,820
你如果让他说登陆流程

102
00:03:17,820 --> 00:03:19,100
可能很多人会梦逼

103
00:03:19,100 --> 00:03:20,260
这个非常奇怪的问题

104
00:03:20,260 --> 00:03:21,080
非常非常多的前端

105
00:03:21,080 --> 00:03:22,800
说不出来登陆流程是怎么样的

106
00:03:22,800 --> 00:03:24,360
那么即使说出来也非常的牵强

107
00:03:24,360 --> 00:03:26,400
所以说这样一个问题其实比较难

108
00:03:26,400 --> 00:03:27,640
因为题目比较抽象

109
00:03:27,640 --> 00:03:28,540
你很难去背诵

110
00:03:28,540 --> 00:03:29,680
为什么这么说呢

111
00:03:29,680 --> 00:03:31,380
因为我曾经工作的几家公司

112
00:03:31,380 --> 00:03:33,320
最终我发现每一家公司登录流程不一样

113
00:03:33,320 --> 00:03:34,580
有的可能使用SSO

114
00:03:34,580 --> 00:03:35,920
有的可能使用OIS 2.0

115
00:03:35,920 --> 00:03:37,020
有的可能去使用Cookie

116
00:03:37,020 --> 00:03:37,900
有的使用C信

117
00:03:37,900 --> 00:03:40,180
它们各种各样的方式去保持它们的登录态

118
00:03:40,180 --> 00:03:42,100
但是原理都是相通的

119
00:03:42,100 --> 00:03:43,300
你只要去理解它的原理

120
00:03:43,300 --> 00:03:44,100
那么我相信

121
00:03:44,100 --> 00:03:46,260
你就可以去理解整个登录流程

122
00:03:46,260 --> 00:03:46,880
而且呢

123
00:03:46,880 --> 00:03:47,700
这样一道面试题

124
00:03:47,700 --> 00:03:49,060
它可以去考察很多方面的知识

125
00:03:49,060 --> 00:03:50,800
比如说对http的理解

126
00:03:50,800 --> 00:03:53,020
因为我们登录肯定会涉及前后端的通信

127
00:03:53,020 --> 00:03:54,900
包括cookie和session的一个区别

128
00:03:54,900 --> 00:03:56,580
因为大部分企业都是使用cookie和session

129
00:03:56,580 --> 00:03:58,020
去进行登录タイ的一个校验

130
00:03:58,020 --> 00:04:00,080
而且还可以加深你对数据库的一个理解

131
00:04:00,080 --> 00:04:01,000
包括外表安全性

132
00:04:01,000 --> 00:04:02,260
安全性就不用说了

133
00:04:02,260 --> 00:04:03,900
登录肯定涉及到你一个密码相关的

134
00:04:03,900 --> 00:04:05,320
那么它的安全性肯定是非常重要

135
00:04:05,320 --> 00:04:06,220
所以说同学们

136
00:04:06,220 --> 00:04:08,600
一定要学好咱们的登录流程

137
00:04:08,600 --> 00:04:09,420
你理解清楚了

138
00:04:09,420 --> 00:04:11,400
可能对你未来的面试非常有帮助

139
00:04:11,400 --> 00:04:13,280
好那么这里其实就是我们这节课了

140
00:04:13,280 --> 00:04:14,600
内容接下来

141
00:04:14,600 --> 00:04:16,940
我会带同学们去了解一下咱们的Cooking和Sense

142
00:04:16,940 --> 00:04:18,020
因为如果这一块你搞不清楚

143
00:04:18,020 --> 00:04:19,300
可能真的非常难

