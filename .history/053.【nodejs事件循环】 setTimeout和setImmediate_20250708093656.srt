1
00:00:00,000 --> 00:00:03,820
好,我们接下来看一下代码执行的第三个片段

2
00:00:03,820 --> 00:00:07,580
那么这第三题呢,其实是一道非常经典的面试题

3
00:00:07,580 --> 00:00:08,780
也是非常难度一道题目

4
00:00:08,780 --> 00:00:12,400
曾经呢,我自己在面试一些名企的时候遇到过这样的题目

5
00:00:12,400 --> 00:00:14,260
但是呢,当时其实没有很好的答出来

6
00:00:14,260 --> 00:00:15,420
也可以说同学们

7
00:00:15,420 --> 00:00:18,380
今天看的这样一个视频也是非常的幸运

8
00:00:18,380 --> 00:00:20,920
你们可以在今后的面试中一定要起到面试官

9
00:00:20,920 --> 00:00:22,180
你一定要问这样一道题目

10
00:00:22,180 --> 00:00:23,820
因为如果说这道题目你答出来了

11
00:00:23,820 --> 00:00:25,680
那么你是非常亮眼的

12
00:00:25,680 --> 00:00:29,260
为什么?因为它是在nodejs里面比较

13
00:00:29,260 --> 00:00:30,580
恶心的一部分

14
00:00:30,580 --> 00:00:31,400
也比较诡异

15
00:00:31,400 --> 00:00:32,460
但是没关系

16
00:00:32,460 --> 00:00:33,360
其实很简单

17
00:00:33,360 --> 00:00:35,260
那么我们先来看一下

18
00:00:35,260 --> 00:00:36,240
到底是什么一个问题

19
00:00:36,240 --> 00:00:38,320
好

20
00:00:38,320 --> 00:00:39,520
其实问题很简单

21
00:00:39,520 --> 00:00:40,640
首先我们来看一下

22
00:00:40,640 --> 00:00:41,660
这样一个代码片段

23
00:00:41,660 --> 00:00:44,320
setout和setimmediate

24
00:00:44,320 --> 00:00:47,340
之前老师师律讲过

25
00:00:47,340 --> 00:00:49,780
setout和setimmediate

26
00:00:49,780 --> 00:00:51,500
他们两个是约等于关系

27
00:00:51,500 --> 00:00:52,300
他们并不相等

28
00:00:52,300 --> 00:00:53,000
算说都是义部的

29
00:00:53,000 --> 00:00:54,180
那么他们的区别在哪里

30
00:00:54,180 --> 00:00:55,700
老师直接运行代码

31
00:00:55,700 --> 00:00:56,280
你们就可以看到

32
00:00:56,280 --> 00:00:57,520
他们的区别在哪里

33
00:00:59,260 --> 00:01:02,260
这里是一段demo,我已经写好了

34
00:01:02,260 --> 00:01:04,260
这里我们来看一下

35
00:01:04,260 --> 00:01:07,260
他的执行顺序是什么样,同学们猜一下

36
00:01:07,260 --> 00:01:09,260
是setTimeout零先执行

37
00:01:09,260 --> 00:01:11,260
还是上面的先执行,哪个先执行

38
00:01:11,260 --> 00:01:13,260
我们来看一下,到底怎么回事

39
00:01:13,260 --> 00:01:17,260
loadsetimmediate.js

40
00:01:17,260 --> 00:01:19,260
好

41
00:01:19,260 --> 00:01:21,260
同学们看到没有,这里可能看的不是很清晰

42
00:01:21,260 --> 00:01:23,260
我们重新来run一下

43
00:01:23,260 --> 00:01:25,260
执行

44
00:01:25,260 --> 00:01:27,260
第四执行谁,setimmediate

45
00:01:27,260 --> 00:01:28,760
后面才是set him out

46
00:01:28,760 --> 00:01:32,260
那么我们可以认为set immediate

47
00:01:32,260 --> 00:01:34,260
set immediate他在set him out之前执行吗

48
00:01:34,260 --> 00:01:35,260
可不可以这样认为

49
00:01:35,260 --> 00:01:37,260
好像可以

50
00:01:37,260 --> 00:01:39,260
那么我们再执行一次看一下

51
00:01:39,260 --> 00:01:41,260
同学们看到没有set him out先执行了

52
00:01:41,260 --> 00:01:43,260
set immediate后执行

53
00:01:43,260 --> 00:01:44,260
这是什么鬼

54
00:01:44,260 --> 00:01:47,260
是不是咱们代码写了问题咱们编音器报错了吗

55
00:01:47,260 --> 00:01:49,260
实际上没有咱们再运行一次看一下

56
00:01:49,260 --> 00:01:52,260
看到没有他在前面他在后面咱们再运行一次

57
00:01:52,260 --> 00:01:54,260
好还是这样一个结果

58
00:01:54,260 --> 00:01:55,260
好咱们再运行一次

59
00:01:55,260 --> 00:01:56,200
是不是咱们的

60
00:01:56,200 --> 00:01:58,060
咱们的乐件事是不是抽风了呀

61
00:01:58,060 --> 00:01:59,280
明明shed immediately在前面

62
00:01:59,280 --> 00:02:00,640
怎么这一次执行shed them out在前面

63
00:02:00,640 --> 00:02:01,240
咱们再执行一次

64
00:02:01,240 --> 00:02:04,020
看到没有shed them out在前面

65
00:02:04,020 --> 00:02:05,440
那么我们是不是可以得出一个结论

66
00:02:05,440 --> 00:02:05,920
同学们

67
00:02:05,920 --> 00:02:09,760
shed them out和这个东西

68
00:02:09,760 --> 00:02:14,320
执行顺序不确定

69
00:02:14,320 --> 00:02:16,080
不确定这是什么鬼

70
00:02:16,080 --> 00:02:17,280
这是什么鬼

71
00:02:17,280 --> 00:02:18,800
是不是很奇怪呀

72
00:02:18,800 --> 00:02:19,360
那么为什么

73
00:02:19,360 --> 00:02:21,120
一般我们碰到这种不确定的问题

74
00:02:21,120 --> 00:02:22,080
是不是很难

75
00:02:22,080 --> 00:02:24,180
包括同学们你们可以去网上去搜

76
00:02:24,180 --> 00:02:25,840
为什么setTimeout和setImmediate

77
00:02:25,840 --> 00:02:26,720
他们的执行顺序不确定

78
00:02:26,720 --> 00:02:28,160
可以说你

79
00:02:28,160 --> 00:02:29,920
说一天你得找出答案

80
00:02:29,920 --> 00:02:31,620
因为没有人能够把这个问题解释得很清楚

81
00:02:31,620 --> 00:02:32,820
那么没关系

82
00:02:32,820 --> 00:02:35,980
今天老师来带同学们来真正的去解开这一层迷雾

83
00:02:35,980 --> 00:02:37,240
我们再来看另一个弹幕

84
00:02:37,240 --> 00:02:38,980
我们呢

85
00:02:38,980 --> 00:02:38,980
在fs.referferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferferfer

86
00:02:38,980 --> 00:02:41,800
在fs.ifl是什么

87
00:02:41,800 --> 00:02:45,640
是不是读取文件呢 然后一个callback对吧 那我们来看一下

88
00:02:45,640 --> 00:02:47,420
在他的callback里面

89
00:02:47,420 --> 00:02:50,500
setimmediate和settimeout他们的整形顺序是什么样的

90
00:02:50,500 --> 00:02:52,800
我们再来看一下

91
00:02:52,800 --> 00:02:56,640
loadsetimmediate22.js

92
00:02:56,640 --> 00:02:59,720
看到没有 好 老师把它清空一下 避免干扰 重新执行

93
00:02:59,720 --> 00:03:03,040
setimmediate在前面 settimeout在后面

94
00:03:03,040 --> 00:03:05,600
setimmediate在前面 settimeout在后面

95
00:03:05,600 --> 00:03:08,940
刚才不是不确定了吗 是咱们执行次数不够 好 那咱们执行多一点

96
00:03:08,980 --> 00:03:11,180
同学们看了没有

97
00:03:11,180 --> 00:03:12,940
它永远在前面

98
00:03:12,940 --> 00:03:14,160
它永远都在前面

99
00:03:14,160 --> 00:03:16,440
那这里是不是很奇怪了

100
00:03:16,440 --> 00:03:17,680
同学们

101
00:03:17,680 --> 00:03:18,400
在这里

102
00:03:18,400 --> 00:03:20,940
直行顺序是确定的

103
00:03:20,940 --> 00:03:23,340
那么这样子行

104
00:03:23,340 --> 00:03:24,620
它直行顺序是不确定的

105
00:03:24,620 --> 00:03:25,200
这是什么鬼

106
00:03:25,200 --> 00:03:25,760
是不是同学们

107
00:03:25,760 --> 00:03:26,720
是不是彻底迷茫了

108
00:03:26,720 --> 00:03:28,180
实际上

109
00:03:28,180 --> 00:03:30,060
实际上

110
00:03:30,060 --> 00:03:31,960
这里我来给同学们解释一下原因

111
00:03:31,960 --> 00:03:32,820
首先

112
00:03:32,820 --> 00:03:34,300
我们在loadgs中

113
00:03:34,300 --> 00:03:35,960
可以得出这样一个结论

114
00:03:35,960 --> 00:03:36,680
这是loadgs

115
00:03:36,680 --> 00:03:38,240
它的官方文档里面去介绍的

116
00:03:38,240 --> 00:03:38,640
就是说

117
00:03:38,640 --> 00:03:42,740
setmout0和setmout1是相等的

118
00:03:42,740 --> 00:03:43,260
怎么样去理解

119
00:03:43,260 --> 00:03:47,980
你比如说你给一个定时器设置0毫秒返回和0毫秒返回和1毫秒返回

120
00:03:47,980 --> 00:03:50,440
他们两个是完全相等待的肉类件事里面

121
00:03:50,440 --> 00:03:50,840
为什么呀

122
00:03:50,840 --> 00:03:53,720
因为不知道同学们在浏览器里面有没有去了解过

123
00:03:53,720 --> 00:03:55,260
实际上在浏览器里面

124
00:03:55,260 --> 00:04:02,900
setmout0是等于setmout4的

125
00:04:02,900 --> 00:04:03,340
为什么呀

126
00:04:03,340 --> 00:04:05,040
难道我们设置0毫秒

127
00:04:05,040 --> 00:04:06,260
它就真的是0毫秒之后执行吗

128
00:04:06,260 --> 00:04:06,960
其实不是的

129
00:04:06,960 --> 00:04:08,700
在浏览器里面它实际上是4毫秒

130
00:04:08,700 --> 00:04:09,740
为什么呀

131
00:04:09,740 --> 00:04:12,120
因为我们如果说0毫秒去执行

132
00:04:12,120 --> 00:04:13,580
可能会影响性能

133
00:04:13,580 --> 00:04:14,860
因为没必要我们一定0毫秒

134
00:04:14,860 --> 00:04:15,780
0毫秒是个什么概念

135
00:04:15,780 --> 00:04:17,860
0毫秒它是不是同步了呀

136
00:04:17,860 --> 00:04:19,760
咱们两个代码之间的执行肯定会有间隔

137
00:04:19,760 --> 00:04:20,440
怎么可能会有0呢

138
00:04:20,440 --> 00:04:21,600
它只是一种写法而已

139
00:04:21,600 --> 00:04:22,980
实际上在浏览器里面

140
00:04:22,980 --> 00:04:24,060
你去share timeout 0

141
00:04:24,060 --> 00:04:25,400
实际上它是4秒钟之后去执行的

142
00:04:25,400 --> 00:04:27,320
那么在node里面它是1毫秒之后才去执行的

143
00:04:27,320 --> 00:04:28,400
同学们记住这是一个结论

144
00:04:28,400 --> 00:04:30,400
那么我们再来看一下

145
00:04:30,400 --> 00:04:31,960
它和我们刚才发生的这种情况

146
00:04:31,960 --> 00:04:34,880
他有什么关系呢 我们是不是 我们是不是可以分析出来

147
00:04:34,880 --> 00:04:41,060
set him out 他是一毫秒执行的 咱们可以理解为他是相等的 那么同学们是不是可以

148
00:04:41,060 --> 00:04:46,700
猜测一下咱们的set immediate 他有时候是在一毫秒之前执行的 有时候又是一毫秒之后执行的

149
00:04:46,700 --> 00:04:46,860
 对吧

150
00:04:46,860 --> 00:04:48,560
咱们是不是可以得出这样一个结论

151
00:04:48,560 --> 00:04:51,160
就是这样一个方法

152
00:04:51,160 --> 00:04:56,060
他有时候是一毫秒之前执行

153
00:04:56,060 --> 00:04:57,360
有时候

154
00:05:01,560 --> 00:05:04,060
又是1毫秒之后执行

155
00:05:04,060 --> 00:05:06,520
那么难道真的是这样吗

156
00:05:06,520 --> 00:05:07,560
其实不一定

157
00:05:07,560 --> 00:05:08,200
我们先来看一下

158
00:05:08,200 --> 00:05:09,460
我们先来分析一下

159
00:05:09,460 --> 00:05:10,720
他一种确定的情况

160
00:05:10,720 --> 00:05:12,820
就在FS去读取文件的回调里面

161
00:05:12,820 --> 00:05:13,820
他为什么执行顺序是确定的

162
00:05:13,820 --> 00:05:15,500
我们现在看一下这样一种情况

163
00:05:15,500 --> 00:05:18,560
同样的我们来看图分析

164
00:05:18,560 --> 00:05:21,800
我们先把代码贴过来

165
00:05:21,800 --> 00:05:25,000
FS

166
00:05:25,000 --> 00:05:25,860
确实这块很诡异

167
00:05:25,860 --> 00:05:27,320
同学们但是搞清楚

168
00:05:27,320 --> 00:05:30,360
你就会觉得霍乱开了

169
00:05:30,360 --> 00:05:31,000
好

170
00:05:31,000 --> 00:05:32,560
首先咱们执行时间是不是0 同学们

171
00:05:32,560 --> 00:05:34,400
那么第一次

172
00:05:34,400 --> 00:05:35,840
第一次

173
00:05:35,840 --> 00:05:38,040
咱们来分析一下 再一段代码

174
00:05:38,040 --> 00:05:39,400
fs.readfile

175
00:05:39,400 --> 00:05:41,140
我们先不管它什么时候执行

176
00:05:41,140 --> 00:05:42,540
咱们此时执行的时候

177
00:05:42,540 --> 00:05:43,560
fsreadfile肯定需要时间

178
00:05:43,560 --> 00:05:44,240
它是一个逆步的

179
00:05:44,240 --> 00:05:44,960
那么pore这里

180
00:05:44,960 --> 00:05:45,960
pore这里

181
00:05:45,960 --> 00:05:47,500
是不是咱们它没有设置定时器

182
00:05:47,500 --> 00:05:47,840
对吧

183
00:05:47,840 --> 00:05:48,900
是不是没有设置定时器啊

184
00:05:48,900 --> 00:05:49,340
没有timer

185
00:05:49,340 --> 00:05:50,800
因为咱们主线层上面是不是没有time

186
00:05:50,800 --> 00:05:51,500
所以会走上面

187
00:05:51,500 --> 00:05:52,800
那么如果说走上面

188
00:05:52,800 --> 00:05:54,040
咱们的pore queen此时是不是空

189
00:05:54,040 --> 00:05:55,440
这里呢

190
00:05:55,440 --> 00:05:57,940
我们先来把fs丢到io里面去

191
00:06:00,500 --> 00:06:03,500
fis.readfile

192
00:06:03,500 --> 00:06:09,760
回掉他的执行时间 我们不知道 假设是我们讲三两毫秒 随便吧 这个无所谓

193
00:06:09,760 --> 00:06:15,300
好 此时时间为零 对吧 此时破了是不是会主射 如果代码没有设定 set

194
00:06:15,300 --> 00:06:17,980
 immediate 是不是没设定 因为咱们主线程上面是没有的 他是在回调里面

195
00:06:17,980 --> 00:06:18,680
 所以说是没有设定的

196
00:06:18,680 --> 00:06:21,500
那么呢 该阶段会等待 callback 加入 poor queen

197
00:06:21,500 --> 00:06:26,300
什么意思 就是咱们的第一次时间情况一直会主射在破了这里 然后呢两毫秒之后

198
00:06:26,300 --> 00:06:29,420
两毫秒之后 fs的readfile会加入

199
00:06:30,140 --> 00:06:31,260
对吧 同学们 是不是

200
00:06:31,260 --> 00:06:37,400
那么在两毫秒执行谁 是不是执行这样一个callback 执行他

201
00:06:37,400 --> 00:06:40,640
他做了一件什么事情 同学们在两毫秒的时候

202
00:06:40,640 --> 00:06:45,420
是不是一个setimmediate的一个一步回掉 一个setmout的一个一步回掉 好

203
00:06:45,420 --> 00:06:46,140
 那咱们来看一下

204
00:06:46,140 --> 00:06:51,400
此时在执行setimmediate的时候 和settimeout 他在一个主线程执行的时候

205
00:06:51,400 --> 00:06:52,940
 是不是已经进入了第二轮程循环 同学们

206
00:06:52,940 --> 00:06:59,140
当咱们执行到setimmediate的时候 他会走到哪里

207
00:06:59,940 --> 00:07:00,600
会走到哪里

208
00:07:00,600 --> 00:07:02,700
我们来看一下文档

209
00:07:02,700 --> 00:07:04,900
其实这里老是没有贴过来

210
00:07:04,900 --> 00:07:08,100
咱们破破阶段过来之后是什么

211
00:07:08,100 --> 00:07:09,300
是不是check check执行什么

212
00:07:09,300 --> 00:07:11,340
check执行setimmediate设置的回调

213
00:07:11,340 --> 00:07:12,240
会在此阶段被调用

214
00:07:12,240 --> 00:07:14,100
那么咱们把check阶段加入

215
00:07:14,100 --> 00:07:15,540
加到咱们的头里面去

216
00:07:15,540 --> 00:07:19,500
check

217
00:07:19,500 --> 00:07:20,880
check是干嘛的

218
00:07:20,880 --> 00:07:21,440
还记得吗

219
00:07:21,440 --> 00:07:23,440
刚讲的setimmediate设置的回调

220
00:07:23,440 --> 00:07:24,600
会在此阶段被调用

221
00:07:24,600 --> 00:07:27,840
谁呀同学们

222
00:07:27,840 --> 00:07:28,500
是不是他呀

223
00:07:28,500 --> 00:07:29,400
是不是他呀同学们

224
00:07:29,940 --> 00:07:34,340
因为咱们 fs real费要执行完成之后是不是就被回收了

225
00:07:34,340 --> 00:07:35,220
然后呢

226
00:07:35,220 --> 00:07:36,340
他里面有个主线程

227
00:07:36,340 --> 00:07:37,540
主线程是什么sade immediate

228
00:07:37,540 --> 00:07:38,540
sade immediate

229
00:07:38,540 --> 00:07:41,040
是不是在千里面去执行对吧

230
00:07:41,040 --> 00:07:43,440
sade immediate

231
00:07:43,440 --> 00:07:46,040
他是不是在他是不是在咱们的

232
00:07:46,040 --> 00:07:48,400
咱们的千亿阶段去执行呢

233
00:07:48,400 --> 00:07:49,100
对吧

234
00:07:49,100 --> 00:07:50,200
刚才老师给同学们看了文档

235
00:07:50,200 --> 00:07:51,640
那么千亿的执行完成之后

236
00:07:51,640 --> 00:07:54,100
这里是不是在他们out

237
00:07:54,100 --> 00:07:55,640
塞的他们out在哪个阶段去执行

238
00:07:55,640 --> 00:07:57,180
是不是在timer呀

239
00:07:57,180 --> 00:07:58,180
对吧

240
00:07:59,940 --> 00:08:04,620
timer settimeout

241
00:08:04,620 --> 00:08:08,240
那么随先执行是不是setimmediate先执行

242
00:08:08,240 --> 00:08:10,540
然后在第二轮视线循环的开始

243
00:08:10,540 --> 00:08:12,760
在timer这个阶段就会去执行settimeout

244
00:08:12,760 --> 00:08:14,300
执行完成丢进去

245
00:08:14,300 --> 00:08:16,040
那么随先执行是不是setimmediate

246
00:08:16,040 --> 00:08:16,960
好

247
00:08:16,960 --> 00:08:19,680
这里同学们了解了吗

248
00:08:19,680 --> 00:08:20,420
那我们再来看一下

249
00:08:20,420 --> 00:08:22,380
我们再来看一下

250
00:08:22,380 --> 00:08:25,200
第一段的代码执行

251
00:08:25,200 --> 00:08:25,880
这样一段

252
00:08:25,880 --> 00:08:27,180
它为什么时间是不确定的

253
00:08:27,180 --> 00:08:27,920
其实这里呢

254
00:08:27,920 --> 00:08:28,580
就比较好理解

255
00:08:28,580 --> 00:08:29,760
因为什么呢

256
00:08:29,760 --> 00:08:30,260
其实这里呢

257
00:08:30,260 --> 00:08:32,820
因为EVNNOVE它的启动也是需要时间的

258
00:08:32,820 --> 00:08:35,980
可能执行到POL阶段已经超过了一毫秒

259
00:08:35,980 --> 00:08:37,600
SETIME OUT会先执行

260
00:08:37,600 --> 00:08:39,000
反正SETIMEDIATE先执行

261
00:08:39,000 --> 00:08:40,020
好 我们再来分析一下

262
00:08:40,020 --> 00:08:41,240
为什么时间顺序是不确定的

263
00:08:41,240 --> 00:08:44,180
首先我们的IO里面

264
00:08:44,180 --> 00:08:46,620
好 老师先把代码贴一下

265
00:08:46,620 --> 00:08:55,920
好 我们的代码是不是变成这样

266
00:08:55,920 --> 00:08:56,560
对吧

267
00:08:56,560 --> 00:08:57,860
SETIMEDIATE SETIME OUT

268
00:08:57,860 --> 00:08:59,220
FS没有

269
00:08:59,220 --> 00:09:01,160
好

270
00:09:01,160 --> 00:09:01,920
首先

271
00:09:01,920 --> 00:09:03,460
同学们先确定一点

272
00:09:03,460 --> 00:09:05,500
even the node的启动

273
00:09:05,500 --> 00:09:06,460
也是需要时间的

274
00:09:06,460 --> 00:09:07,880
什么意思

275
00:09:07,880 --> 00:09:08,640
也就是说

276
00:09:08,640 --> 00:09:09,280
你什么都不做

277
00:09:09,280 --> 00:09:09,960
在 node的介绍

278
00:09:09,960 --> 00:09:10,540
它的四点循环

279
00:09:10,540 --> 00:09:11,160
整个过程中

280
00:09:11,160 --> 00:09:12,000
它一个状态

281
00:09:12,000 --> 00:09:12,620
一个状态的切换

282
00:09:12,620 --> 00:09:13,460
是需要时间的

283
00:09:13,460 --> 00:09:14,200
如果说

284
00:09:14,200 --> 00:09:15,220
咱们切到pro的时候

285
00:09:15,220 --> 00:09:16,420
咱们首先到pro

286
00:09:16,420 --> 00:09:17,460
此时是不是代码

287
00:09:17,460 --> 00:09:18,440
是不是设定了timer

288
00:09:18,440 --> 00:09:19,020
对吧

289
00:09:19,020 --> 00:09:19,980
是不是设定了timer

290
00:09:19,980 --> 00:09:21,820
set him out

291
00:09:21,820 --> 00:09:22,180
是不是timer

292
00:09:22,180 --> 00:09:22,540
也就是说

293
00:09:22,540 --> 00:09:23,560
咱们到了timer这里

294
00:09:23,560 --> 00:09:24,700
如果pro快

295
00:09:24,700 --> 00:09:25,400
这种空闲状态

296
00:09:25,400 --> 00:09:26,980
是此地pro状态

297
00:09:26,980 --> 00:09:27,580
为空闲状态

298
00:09:27,580 --> 00:09:27,980
也就是说

299
00:09:27,980 --> 00:09:28,320
比如说

300
00:09:28,320 --> 00:09:29,080
比如说同学们

301
00:09:29,080 --> 00:09:31,460
咱们此时的时间是

302
00:09:31,460 --> 00:09:33,460
0

303
00:09:33,460 --> 00:09:35,280
咱们进入pro去等待

304
00:09:35,280 --> 00:09:36,960
等待settimeout它的加入

305
00:09:36,960 --> 00:09:38,860
那么如果说

306
00:09:38,860 --> 00:09:41,100
它的even的事件启动的过程中

307
00:09:41,100 --> 00:09:42,940
从timer到io到pro

308
00:09:42,940 --> 00:09:44,240
从timer

309
00:09:44,240 --> 00:09:46,860
timer到io到pro

310
00:09:46,860 --> 00:09:48,360
在一个阶段它超过了一毫秒

311
00:09:48,360 --> 00:09:49,500
超过了一毫秒

312
00:09:49,500 --> 00:09:53,260
如果说此时时间是1.5毫秒

313
00:09:53,260 --> 00:09:55,360
此时时间是1.5毫秒

314
00:09:55,360 --> 00:09:55,960
同学们注意听

315
00:09:55,960 --> 00:09:57,500
此时时间是1.5毫秒

316
00:09:57,500 --> 00:09:58,220
它的even的loop

317
00:09:58,220 --> 00:09:59,660
第一次的event loop刚到pro

318
00:09:59,660 --> 00:10:00,860
刚才是不是讲的了

319
00:10:00,860 --> 00:10:02,140
从timer到io到pro

320
00:10:02,140 --> 00:10:02,960
它是需要时间的

321
00:10:02,960 --> 00:10:04,080
nolyds它的启动需要时间

322
00:10:04,080 --> 00:10:04,840
此时呢

323
00:10:04,840 --> 00:10:05,620
刚好到pro的时候

324
00:10:05,620 --> 00:10:06,480
时间是1.5毫秒

325
00:10:06,480 --> 00:10:08,380
那么pro进入空闲状态

326
00:10:08,380 --> 00:10:09,620
event loop将检查timers

327
00:10:09,620 --> 00:10:11,020
如果有一个timers时间已经到了

328
00:10:11,020 --> 00:10:12,560
那么此时set him out

329
00:10:12,560 --> 00:10:13,320
零是不是已经到达了

330
00:10:13,320 --> 00:10:14,580
因为它零是不是就等于一样

331
00:10:14,580 --> 00:10:15,380
刚才是不是讲过

332
00:10:15,380 --> 00:10:17,340
此时set him out

333
00:10:17,340 --> 00:10:19,400
是不是就会进入pro

334
00:10:19,400 --> 00:10:20,280
然后去执行

335
00:10:20,280 --> 00:10:20,920
执行完成之后

336
00:10:20,920 --> 00:10:21,980
走里回收

337
00:10:21,980 --> 00:10:23,380
然后到check阶段

338
00:10:23,380 --> 00:10:23,940
check阶段执行

339
00:10:23,940 --> 00:10:24,520
immediate

340
00:10:24,520 --> 00:10:25,440
那么此时呢

341
00:10:25,440 --> 00:10:27,480
就是set him out

342
00:10:27,480 --> 00:10:27,960
先执行

343
00:10:27,960 --> 00:10:29,580
然后那是它immediate后执行

344
00:10:29,580 --> 00:10:29,880
好

345
00:10:29,880 --> 00:10:31,000
那么咱们再来看一种情况

346
00:10:31,000 --> 00:10:31,780
就假如说啊

347
00:10:31,780 --> 00:10:32,140
同学们

348
00:10:32,140 --> 00:10:33,460
假如说

349
00:10:33,460 --> 00:10:35,260
当前的时间是

350
00:10:35,260 --> 00:10:38,200
0.8毫秒

351
00:10:38,200 --> 00:10:40,860
0.8毫秒的时候咱们就已经到pull了

352
00:10:40,860 --> 00:10:41,280
好

353
00:10:41,280 --> 00:10:41,840
那咱们来看一下

354
00:10:41,840 --> 00:10:43,440
如果pull coin进入空状态时

355
00:10:43,440 --> 00:10:43,920
因为呢

356
00:10:43,920 --> 00:10:44,600
我们将检查timers

357
00:10:44,600 --> 00:10:45,820
此时呢

358
00:10:45,820 --> 00:10:46,800
咱们的settimeout

359
00:10:46,800 --> 00:10:47,740
是不是时间

360
00:10:47,740 --> 00:10:49,780
时间是不是1毫秒啊

361
00:10:49,780 --> 00:10:50,120
同学们

362
00:10:50,120 --> 00:10:52,020
它的执行实际数是1毫秒

363
00:10:52,020 --> 00:10:55,540
1毫秒

364
00:10:55,540 --> 00:10:55,880
对吧

365
00:10:55,880 --> 00:10:56,480
但是

366
00:10:56,480 --> 00:10:57,100
此时时间

367
00:10:57,100 --> 00:10:59,440
但是此时当前时间是0.8就已经到破了

368
00:10:59,440 --> 00:11:01,380
因为咱们even loop它启动的时间可能会快一点

369
00:11:01,380 --> 00:11:02,300
所以呢

370
00:11:02,300 --> 00:11:04,340
破就会直接跳过到check

371
00:11:04,340 --> 00:11:05,380
setimmediate

372
00:11:05,380 --> 00:11:07,060
它先执行走里进去

373
00:11:07,060 --> 00:11:09,000
然后check执行完成之后

374
00:11:09,000 --> 00:11:10,340
再到第二轮世界熊的timer

375
00:11:10,340 --> 00:11:12,120
到到timer的时候

376
00:11:12,120 --> 00:11:13,960
是不是就会去执行setimout

377
00:11:13,960 --> 00:11:14,980
它好进去

378
00:11:14,980 --> 00:11:15,360
所以呢

379
00:11:15,360 --> 00:11:18,080
此时setimmediate就在setimout的前面

380
00:11:18,080 --> 00:11:19,320
这里就是呢

381
00:11:19,320 --> 00:11:19,940
他们两个的顺序

382
00:11:19,940 --> 00:11:21,480
不一定的原因

383
00:11:21,480 --> 00:11:23,560
也就是取决于你even loop的启动速度

384
00:11:23,560 --> 00:11:23,840
好

385
00:11:23,840 --> 00:11:24,120
那么呢

386
00:11:24,120 --> 00:11:24,360
这里呢

387
00:11:24,360 --> 00:11:26,280
老师来回顾一下咱们刚才所讲的两个例子

388
00:11:26,280 --> 00:11:27,800
首先第一种

389
00:11:27,800 --> 00:11:29,140
是不是他们两个顺序不确定

390
00:11:29,140 --> 00:11:29,820
为什么不确定

391
00:11:29,820 --> 00:11:31,220
是不是需要咱们的event node的启动

392
00:11:31,220 --> 00:11:33,180
如果说它的时间超过一毫秒

393
00:11:33,180 --> 00:11:34,220
settimeout就会先执行

394
00:11:34,220 --> 00:11:34,680
反之

395
00:11:34,680 --> 00:11:36,200
如果说它不足一毫秒

396
00:11:36,200 --> 00:11:37,640
咱们settoimmediate就会先执行

397
00:11:37,640 --> 00:11:40,120
那么为什么在readfail里面

398
00:11:40,120 --> 00:11:42,860
settoimmediate它会先执行呢

399
00:11:42,860 --> 00:11:43,560
为什么呀

400
00:11:43,560 --> 00:11:45,880
是不是咱们在readfail里面

401
00:11:45,880 --> 00:11:46,980
它在pull这里会阻射

402
00:11:46,980 --> 00:11:48,020
那么阻射的时候

403
00:11:48,020 --> 00:11:49,000
是不是一直会等待timer

404
00:11:49,000 --> 00:11:50,240
等待timer回来

405
00:11:50,240 --> 00:11:52,080
也就是说会去等待它

406
00:11:52,080 --> 00:11:54,140
然后再去checksettoimmediate

407
00:11:54,140 --> 00:11:55,740
所以说在fx模块里面

408
00:11:55,740 --> 00:11:56,440
它就会

409
00:11:56,440 --> 00:11:58,180
执行顺序是确定的

410
00:11:58,180 --> 00:12:00,100
所以这就是咱们

411
00:12:00,100 --> 00:12:03,040
Sense, Immediate和 Set Time Out

412
00:12:03,040 --> 00:12:04,440
它们两个这么神奇的一个原因

413
00:12:04,440 --> 00:12:06,140
同学们好好理解一下

414
00:12:06,140 --> 00:12:07,940
好 这一节课的内容就到这里

