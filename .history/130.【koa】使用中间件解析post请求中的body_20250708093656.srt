1
00:00:00,000 --> 00:00:03,320
好 这一节课我们就来看一下webapp的内容

2
00:00:03,320 --> 00:00:05,640
其实呢 我呢会在这里呢去讲解两个例子

3
00:00:05,640 --> 00:00:06,400
第一个

4
00:00:06,400 --> 00:00:08,700
request参数处理 第二个呢文件上传

5
00:00:08,700 --> 00:00:11,520
那么request是不是咱们客户端去发起的一些请求

6
00:00:11,520 --> 00:00:12,800
比如说get或者post

7
00:00:12,800 --> 00:00:14,080
我们去处理它的一些参数

8
00:00:14,080 --> 00:00:17,160
那么我们的get的请求参数是不是很简单呢

9
00:00:17,160 --> 00:00:18,440
比如说大家可以看到

10
00:00:18,440 --> 00:00:20,220
我们如果说去发现一个get的请求

11
00:00:20,220 --> 00:00:21,000
去访问淘宝

12
00:00:21,000 --> 00:00:23,300
然后我们去加入一些name或者age

13
00:00:23,300 --> 00:00:26,120
我们是不是直接可以通过咱们的一个地址去解析

14
00:00:26,120 --> 00:00:27,140
name和age

15
00:00:27,140 --> 00:00:29,180
通过我们的一些证则或者一些其他的方式

16
00:00:29,440 --> 00:00:32,260
那么我们的post请求怎么样去解析我们所传递的参数呢

17
00:00:32,260 --> 00:00:35,840
其实蓝点就在于我们怎么样去解析post

18
00:00:35,840 --> 00:00:40,200
其实这里呢我们可以先看一下httpnodejs他的一个文档

19
00:00:40,200 --> 00:00:43,000
同学们先有些概念我们再去讲会稍微好一点

20
00:00:43,000 --> 00:00:46,840
我们来看一下咱们nodejs中他的一个httprequest这一章的内容

21
00:00:46,840 --> 00:00:48,640
好这里呢是他的一大坨

22
00:00:48,640 --> 00:00:50,440
他的一些api的使用方法

23
00:00:50,440 --> 00:00:51,460
我们先不用去关注

24
00:00:51,460 --> 00:00:52,480
我们重点看

25
00:00:52,480 --> 00:00:54,780
我们重点其实看这样一段话

26
00:00:54,780 --> 00:00:57,600
http.request返回一个

27
00:00:58,880 --> 00:01:01,700
返回一个http.clientRequest的那个实力

28
00:01:01,700 --> 00:01:04,000
那么clientRequest实力是一个可写流

29
00:01:04,000 --> 00:01:05,280
什么是可写流啊 同学们

30
00:01:05,280 --> 00:01:07,080
可写流是时间 等一下我会介绍

31
00:01:07,080 --> 00:01:09,880
那么如果说你需要使用post的请求上传文件

32
00:01:09,880 --> 00:01:12,960
则可以写入到clientRequest的对象

33
00:01:12,960 --> 00:01:13,720
什么意思

34
00:01:13,720 --> 00:01:14,760
其实这段

35
00:01:14,760 --> 00:01:16,280
其实这段话的意思是什么呢

36
00:01:16,280 --> 00:01:18,080
你需要使用post的请求

37
00:01:18,080 --> 00:01:19,360
你不仅是上传文件

38
00:01:19,360 --> 00:01:23,960
你一旦使用到post的请求 其实呢 它都会写到咱们的clientRequest的对象里面去

39
00:01:23,960 --> 00:01:26,780
不仅仅是上传文件 这里其实它的文档描述稍微有点问题

40
00:01:26,780 --> 00:01:28,580
好 那么我们来看一下

41
00:01:28,880 --> 00:01:31,380
client request实力是一个可写流

42
00:01:31,380 --> 00:01:33,160
我们重点是不是关注什么是可写流啊

43
00:01:33,160 --> 00:01:36,100
那么可写流呢其实是咱们loadgs里面的一个概念

44
00:01:36,100 --> 00:01:37,140
它叫做什么呢

45
00:01:37,140 --> 00:01:39,120
叫做stream

46
00:01:39,120 --> 00:01:40,720
那么stream

47
00:01:40,720 --> 00:01:43,140
好这里它的文档有些复杂

48
00:01:43,140 --> 00:01:44,540
我们有一个稍微简单一点的

49
00:01:44,540 --> 00:01:46,640
其实我们可以看到loadgsstream流

50
00:01:46,640 --> 00:01:47,640
它呢

51
00:01:47,640 --> 00:01:51,400
它其实这里也介绍了loadgsstream

52
00:01:51,400 --> 00:01:54,960
对http服务器发起请求的request对象就是一个stream

53
00:01:54,960 --> 00:01:57,780
那么stream它有什么作用呢

54
00:01:57,780 --> 00:01:58,900
其实它可以通过

55
00:01:58,900 --> 00:02:00,580
我们其实重点关注什么呢

56
00:02:00,580 --> 00:02:01,620
我们重点关注

57
00:02:01,620 --> 00:02:03,580
它的一些使用方法

58
00:02:03,580 --> 00:02:06,420
所有的sgroom对象都是event emit的实例

59
00:02:06,420 --> 00:02:07,320
常用的事件有什么

60
00:02:07,320 --> 00:02:08,240
data

61
00:02:08,240 --> 00:02:08,760
end

62
00:02:08,760 --> 00:02:09,900
error

63
00:02:09,900 --> 00:02:10,520
finish

64
00:02:10,520 --> 00:02:11,080
什么意思

65
00:02:11,080 --> 00:02:12,620
是不是说明我们可以通过

66
00:02:12,620 --> 00:02:13,220
end方法

67
00:02:13,220 --> 00:02:14,400
是不是监听到我们的一些

68
00:02:14,400 --> 00:02:17,020
data件的事件和end这样的数件

69
00:02:17,020 --> 00:02:18,340
其实我们去解析pose的时候

70
00:02:18,340 --> 00:02:19,260
我们重点关注的是什么

71
00:02:19,260 --> 00:02:20,860
我们重点关注的是

72
00:02:20,860 --> 00:02:22,220
data和end

73
00:02:22,220 --> 00:02:23,480
这样两个事件

74
00:02:23,480 --> 00:02:23,900
好

75
00:02:23,900 --> 00:02:24,760
那么我们就来看一下

76
00:02:24,760 --> 00:02:25,700
他们到底是怎么样

77
00:02:25,700 --> 00:02:26,440
一回事

78
00:02:26,440 --> 00:02:31,900
首先呢我们这里呢通过router.post呢咱们来根目录去执行咱们的命

79
00:02:31,900 --> 00:02:33,100
我们呢在命里面去

80
00:02:33,100 --> 00:02:36,640
我们在命里面去打一个断点

81
00:02:36,640 --> 00:02:38,500
好 我们先启动

82
00:02:38,500 --> 00:02:42,200
好 这里呢我们会使用到一个工具去模拟咱们的post的请求

83
00:02:42,200 --> 00:02:45,600
为什么呀 因为我们直接通过浏览器直接访问咱们localhost3000

84
00:02:45,600 --> 00:02:46,640
是不是只能模拟get的请求

85
00:02:46,640 --> 00:02:48,600
所以这里呢我们就需要使用到一个工具

86
00:02:48,600 --> 00:02:54,280
需要使用模拟请求的工具叫什么呢

87
00:02:54,280 --> 00:02:58,540
这个叫post们 同学们可以去百度安装一下 非常简单 其实这个工具长这个样子

88
00:02:58,540 --> 00:03:04,360
我们来设置一下 首先content type 改为application节省 说明了我们客户端发起了数据结构是一个节省

89
00:03:04,360 --> 00:03:09,260
然后去修改咱们玻璃里面的内容 咱们玻璃呢 输了一段节省周创 叫做hello

90
00:03:09,260 --> 00:03:10,400
 world 我们直接来设计

91
00:03:10,400 --> 00:03:14,640
大家可以看到我们是不是已经进了咱们的断点 我们来看一下此时context的对象是什么

92
00:03:14,640 --> 00:03:22,160
我们重点关注是不是request 那么我们去看一下request 它里面有没有一些跟咱们玻璃相关的信息

93
00:03:22,160 --> 00:03:23,040
 我们能不能够直接拿到

94
00:03:24,120 --> 00:03:24,880
其实这样看了一圈

95
00:03:24,880 --> 00:03:26,160
其实是没有了

96
00:03:26,160 --> 00:03:27,960
为什么刚才讲到了

97
00:03:27,960 --> 00:03:29,240
是不是在军牛里面

98
00:03:29,240 --> 00:03:30,260
那么是军牛

99
00:03:30,260 --> 00:03:31,280
他

100
00:03:31,280 --> 00:03:34,100
其实就是咱们contest.req

101
00:03:34,100 --> 00:03:35,640
他其实就是咱们军牛的一个

102
00:03:35,640 --> 00:03:36,400
实力

103
00:03:36,400 --> 00:03:37,680
这是call里面他去

104
00:03:37,680 --> 00:03:39,220
装装的那么我们就来看一下

105
00:03:39,220 --> 00:03:41,020
他到底是什么我们怎么样去

106
00:03:41,020 --> 00:03:42,040
读取到咱们

107
00:03:42,040 --> 00:03:42,800
玻底的内容

108
00:03:42,800 --> 00:03:45,880
首先当我们去挖一个数字

109
00:03:45,880 --> 00:03:50,740
为什么因为我们是不是获取的是牛啊牛他是一段一段的发送过来的

110
00:03:51,520 --> 00:03:56,380
因为他不是一次性的去传输他和字不创是不一样的所以我们需要去用数组去存储一段流然后把它给合并

111
00:03:56,380 --> 00:03:58,440
需要使用

112
00:03:58,440 --> 00:04:00,480
数组去存储

113
00:04:00,480 --> 00:04:02,280
然后

114
00:04:02,280 --> 00:04:04,320
合并

115
00:04:04,320 --> 00:04:07,140
刚才讲到了我们的

116
00:04:07,140 --> 00:04:09,960
Stream流他是不是也继承于咱们的EventEmit这样一个对象啊

117
00:04:09,960 --> 00:04:10,720
对吧

118
00:04:10,720 --> 00:04:11,740
所以呢我们去

119
00:04:11,740 --> 00:04:13,800
但是这里呢他不是通过

120
00:04:13,800 --> 00:04:16,100
On去监听的他是通过

121
00:04:16,100 --> 00:04:18,400
AdListener

122
00:04:18,400 --> 00:04:19,420
什么呢

123
00:04:19,940 --> 00:04:23,780
他去的event list呢也就是说监听了我们的data时间其他和m是类似的

124
00:04:23,780 --> 00:04:35,820
类似好

125
00:04:35,820 --> 00:04:39,660
那么这里我们首先来把data打印出来看一下他到底是个什么玩意

126
00:04:39,660 --> 00:04:43,500
比如说我们去打印一下

127
00:04:43,500 --> 00:04:48,860
我们刚才讲的data他是一个牛对吧那我们看一下到底什么是牛

128
00:04:49,940 --> 00:04:55,060
好 我们重启一下 那么我们再来通过Postman来发起一下 走

129
00:04:55,060 --> 00:05:05,040
好 我们的断点好像没有生效 为什么 因为我们其实是没有end了 我们这里其实需要end一下contact.req.

130
00:05:18,360 --> 00:05:19,640
好,我们再来启动一下

131
00:05:19,640 --> 00:05:20,660
走

132
00:05:20,660 --> 00:05:23,740
好,大家可以看到我们服务器报错了

133
00:05:23,740 --> 00:05:25,520
我们来看一下报什么错

134
00:05:25,520 --> 00:05:28,860
contest.riq.la listener is not function

135
00:05:28,860 --> 00:05:30,640
好,不好意思呢,我这里

136
00:05:30,640 --> 00:05:32,960
我这里拼错了,Listener好

137
00:05:32,960 --> 00:05:34,240
好,那我们重新看一下

138
00:05:34,240 --> 00:05:36,540
走

139
00:05:36,540 --> 00:05:39,600
好,大家可以看到我们断点是不是已经过来了,那我们来看一下data是什么

140
00:05:39,600 --> 00:05:40,640
其实大家可以看到

141
00:05:40,640 --> 00:05:42,420
data是一个二进制的buffer

142
00:05:42,420 --> 00:05:46,260
对象,其实buffer是什么,是不是也是nodejs里面他的一种数据结构,一种二进制的数据结构

143
00:05:47,280 --> 00:05:50,680
我们刚才讲到的 它是牛 牛其实传输的是二进制 那我们就来看一下buffer

144
00:05:50,680 --> 00:05:51,540
 它到底是什么

145
00:05:51,540 --> 00:05:57,040
我们来看一下buffer它的介绍

146
00:05:57,040 --> 00:06:00,340
加script 语言自身只有周创数据类型

147
00:06:00,340 --> 00:06:05,750
没有二进制数据类型 但是处理向TCP或者文件牛是必须使用二进制数据

148
00:06:05,750 --> 00:06:07,640
 因此在nodejs中定义了一个

149
00:06:07,640 --> 00:06:12,480
buffer类 好 其实我们了解到这里就可以了 如果是有兴趣深入了解的同学可以自己去看一下文档

150
00:06:12,480 --> 00:06:16,780
好 我们刚才是不是已经了解了咱们的buffer 其实它是一个二进制的一个牛

151
00:06:17,800 --> 00:06:20,860
所以说我们在接收到数据的时候怎么办呢

152
00:06:20,860 --> 00:06:22,140
我们把buffer这样一个

153
00:06:22,140 --> 00:06:23,940
二进制的流怎么把它给

154
00:06:23,940 --> 00:06:25,480
是不是push到咱们的一个

155
00:06:25,480 --> 00:06:26,760
data array里面去

156
00:06:26,760 --> 00:06:31,100
好

157
00:06:31,100 --> 00:06:33,160
我们在data在一个时间阶段去push

158
00:06:33,160 --> 00:06:34,180
那么我们在md里面呢

159
00:06:34,180 --> 00:06:35,200
就去解析它

160
00:06:35,200 --> 00:06:35,960
比如说我们去

161
00:06:35,960 --> 00:06:37,760
net一个data等于

162
00:06:37,760 --> 00:06:38,520
什么呢

163
00:06:38,520 --> 00:06:40,580
buffer它是node.js全局提供了一个

164
00:06:40,580 --> 00:06:41,340
对象

165
00:06:41,340 --> 00:06:43,140
它下面有一个conquite的方法

166
00:06:43,140 --> 00:06:43,900
可以去合并

167
00:06:43,900 --> 00:06:45,180
是不是合并我们的一个

168
00:06:45,440 --> 00:06:51,580
然后把它解析成什么呢 把它解析成字不串 它下面还会有一个tosegene 这样一个方法

169
00:06:51,580 --> 00:06:53,880
那么我们来把data打印出来 看一下它是什么

170
00:06:53,880 --> 00:07:00,800
好 打个断理 先停一下 好 打个断理 这个断理给去了

171
00:07:00,800 --> 00:07:02,840
好 我们重新来运行一下

172
00:07:02,840 --> 00:07:09,240
这里好 我们的断点打上了 我们来看一下data是什么 大家看到

173
00:07:09,240 --> 00:07:12,580
我们的data是不是就是howlowworld这样一个字不串呢 那么如果说我们想去解析玻璃

174
00:07:12,580 --> 00:07:15,320
 是不是通过接生点pass就可以拿到我们howlowworld这样一个对象

175
00:07:15,440 --> 00:07:18,260
这里就是我们通过core去解析玻璃的一个过程

176
00:07:18,260 --> 00:07:19,540
大家有没有发现一个问题啊

177
00:07:19,540 --> 00:07:20,560
这样是不是很麻烦

178
00:07:20,560 --> 00:07:23,380
我们既要把它转成buffer然后呢还需要去解析我们的一个buffer

179
00:07:23,380 --> 00:07:26,440
而且我们的post他是不是需要考虑很多情况

180
00:07:26,440 --> 00:07:28,760
你比如说一些安全性问题一些字路创的转移

181
00:07:28,760 --> 00:07:31,060
包括了我们可能content我们的content type

182
00:07:31,060 --> 00:07:31,820
可能不是节省

183
00:07:31,820 --> 00:07:33,880
可能是其他的比如说我们浏览器原生的form data

184
00:07:33,880 --> 00:07:35,400
所以呢我们这里需要使用到

185
00:07:35,400 --> 00:07:40,020
咱们core的一个插件叫做core body可以方便我们去解析咱们的一个post请求

186
00:07:40,020 --> 00:07:44,120
这里呢我们就直接来看一下core body他到底是一个什么玩意

187
00:07:44,880 --> 00:07:50,060
这里呢是CoreBody他的一个Github他的一个Redmi 我们来看一下 首先安装了npmInstall

188
00:07:50,060 --> 00:07:52,320
 CoreBody 我们来看一下它的用法 其实非常简单

189
00:07:52,320 --> 00:07:57,380
比如说我们去require它 因为它是一个中间键 我们用试一下 如果说我们需要去解析咱们的一个

190
00:07:57,380 --> 00:08:01,920
解析咱们的一个咱们post 里面的body是什么 其实它就在咱们的contest

191
00:08:01,920 --> 00:08:03,940
 request下面有一个body在里面对象

192
00:08:03,940 --> 00:08:09,040
其实咱们的CoreBody是不是自动帮我们给挂载到咱们的contest.request 这样一个对象下面去了

193
00:08:09,040 --> 00:08:11,260
 好 那我们就来试验一下 到底是不是怎么回事

194
00:08:14,160 --> 00:08:14,920
好 我们先来引入一下

195
00:08:14,920 --> 00:08:17,480
等于require

196
00:08:17,480 --> 00:08:22,600
cora-boli

197
00:08:22,600 --> 00:08:27,980
好 我们来use一下app.use

198
00:08:27,980 --> 00:08:31,060
cora-boli

199
00:08:31,060 --> 00:08:35,920
好 这里呢 我们就把代码给注释一下 我们直接来

200
00:08:35,920 --> 00:08:37,720
我们直接来 比如说我们去

201
00:08:37,720 --> 00:08:39,500
我们去定义一个

202
00:08:39,500 --> 00:08:40,520
属性

203
00:08:40,520 --> 00:08:42,580
定义一个变量 玻璃等于什么呢

204
00:08:42,580 --> 00:08:43,600
contest.require

205
00:08:43,860 --> 00:08:45,860
request.body

206
00:08:45,860 --> 00:08:53,580
好 我把它打印出来

207
00:08:53,580 --> 00:08:56,660
然后呢 打个断点

208
00:08:56,660 --> 00:08:59,980
好 我们重启一下

209
00:08:59,980 --> 00:09:04,860
好 这里呢 我们继续发一个请求来看一下

210
00:09:04,860 --> 00:09:06,140
走

211
00:09:06,140 --> 00:09:12,780
contest.request.body

212
00:09:13,860 --> 00:09:19,500
好 我们来看一下控制台有没有 报错信息 好像也没有 再来

213
00:09:19,500 --> 00:09:25,900
好 刚才是不是报错了 我们来看一下文档

214
00:09:25,900 --> 00:09:31,520
其实文档里面他说的是 咱们去通过中间键的方式去use他

215
00:09:31,520 --> 00:09:35,360
但是呢 如果说你和core 咱们的root一起使用的话 你必须

216
00:09:35,360 --> 00:09:39,460
你必须在你处理这个路由里面去加入咱们的core玻璃 为什么 他的解释是什么

217
00:09:39,720 --> 00:09:43,320
就是说你最好你只在你需要的地方去解析你的玻璃

218
00:09:43,320 --> 00:09:45,600
如果说你不需要你就没有必要把咱们的

219
00:09:45,600 --> 00:09:49,000
抗尔杆玻璃了搞成他一个是不是咱们根目录下面的一个中间键

220
00:09:49,000 --> 00:09:50,980
他的代码给扩过来看一下到底是什么回事

221
00:09:50,980 --> 00:09:54,560
不过大家需要注意的是什么呢

222
00:09:54,560 --> 00:09:56,100
是他这里依赖的是抗尔杆

223
00:09:56,100 --> 00:09:57,640
导致咱们依赖的是什么

224
00:09:57,640 --> 00:09:59,180
是不是少了一个R呀

225
00:09:59,180 --> 00:10:01,480
他其实是两个库所以呢我们需要去安装一下

226
00:10:01,480 --> 00:10:03,020
这里呢其实我已经体现给安装好了

227
00:10:03,020 --> 00:10:03,780
同学们呢

228
00:10:03,780 --> 00:10:04,800
这里需要注意

229
00:10:04,800 --> 00:10:06,340
不是

230
00:10:06,340 --> 00:10:08,640
其实他们两个功能是类似的

231
00:10:08,900 --> 00:10:13,900
只是说呢 是不同团队所开发的 那么呢 在玻璃在一个中间间里面 他所使用的是这样一个库

232
00:10:13,900 --> 00:10:18,900
其实在使用前面是没有什么太大的影响的 那么我们来 我们重点来看一下咱们的玻璃

233
00:10:18,900 --> 00:10:21,340
这里呢 我们先打一个断点

234
00:10:21,340 --> 00:10:23,660
好 我们来

235
00:10:23,660 --> 00:10:27,100
首先来把它关掉 好 我们来重新跑一遍

236
00:10:27,100 --> 00:10:29,660
好 那么我们来发起一个请求看一下

237
00:10:29,660 --> 00:10:31,360
posterman 走

238
00:10:31,360 --> 00:10:36,040
大家看到我们的断点是不是已经过来了 我们来看一下康康泰的点位快是在一个属性

239
00:10:36,040 --> 00:10:37,060
 他下面是不是有一个玻璃

240
00:10:37,660 --> 00:10:38,420
Body里面是不是一个对象

241
00:10:38,420 --> 00:10:39,200
好了word

242
00:10:39,200 --> 00:10:40,220
此时呢就说明

243
00:10:40,220 --> 00:10:41,240
说明什么呢

244
00:10:41,240 --> 00:10:42,520
是不是cora body在一个中间键

245
00:10:42,520 --> 00:10:43,300
把咱们的body从

246
00:10:43,300 --> 00:10:44,820
已经给成功了给解析了

247
00:10:44,820 --> 00:10:45,860
这里就是cora-body

248
00:10:45,860 --> 00:10:46,880
他在一个中间键的用法

249
00:10:46,880 --> 00:10:47,900
好我们就来

250
00:10:47,900 --> 00:10:50,200
总结一下这一刻所讲解的内容

251
00:10:50,200 --> 00:10:52,000
咱们这一刻是不是讲解了

252
00:10:52,000 --> 00:10:52,760
解析

253
00:10:52,760 --> 00:10:56,860
解析咱们的post中的body

254
00:10:56,860 --> 00:11:00,180
首先我们是不是讲解了我们原生的cora怎么去解析

255
00:11:00,180 --> 00:11:01,460
是不是通过

256
00:11:01,460 --> 00:11:02,240
通过监听什么

257
00:11:02,240 --> 00:11:05,300
是不是通过监听咱们的data和end这两个事件去

258
00:11:05,300 --> 00:11:06,840
解析什么呢

259
00:11:07,660 --> 00:11:11,760
是不是解析buffer 二进制用什么方法 是不是使用它里面的一个

260
00:11:11,760 --> 00:11:15,080
Q使菌方法 然后呢是不是就可以得到咱们传输的一个

261
00:11:15,080 --> 00:11:17,380
节省之不串 然后才去解析它 第二种方式呢

262
00:11:17,380 --> 00:11:21,220
通过cora-body在一个中间 但是呢你需要注意的问题是什么

263
00:11:21,220 --> 00:11:22,500
你需要注意是什么

264
00:11:22,500 --> 00:11:23,540
注意

265
00:11:23,540 --> 00:11:25,580
结合的是

266
00:11:25,580 --> 00:11:27,880
是不是cora-rooter在一个cora

267
00:11:27,880 --> 00:11:29,940
还有一个问题需要注意什么 是不是你

268
00:11:29,940 --> 00:11:31,220
在哪里

269
00:11:31,220 --> 00:11:32,240
写路由

270
00:11:32,240 --> 00:11:33,520
就在哪

271
00:11:33,520 --> 00:11:34,280
引入

272
00:11:34,280 --> 00:11:35,300
cora

273
00:11:35,820 --> 00:11:38,300
总结来说就是你需要什么是不是暗需

274
00:11:38,300 --> 00:11:41,880
暗需引用

275
00:11:41,880 --> 00:11:47,120
好这里就是我们这节课的内容

