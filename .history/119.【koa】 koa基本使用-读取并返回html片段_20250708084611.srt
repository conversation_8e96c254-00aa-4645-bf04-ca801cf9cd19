1
00:00:00,000 --> 00:00:04,620
好 我们刚才是不是讲到了怎么通过context对象去进行一个response

2
00:00:04,620 --> 00:00:07,800
那么我们来看一下htp它到底有哪些response的类型

3
00:00:07,800 --> 00:00:11,080
那么首先Core它默认的返回类型是text plane

4
00:00:11,080 --> 00:00:13,360
如果说你想要其他的返回类型内容

5
00:00:13,360 --> 00:00:17,640
你首先可以使用咱们context request.accept去判断一下

6
00:00:17,640 --> 00:00:20,440
客户端需要接受什么数据 什么意思

7
00:00:20,440 --> 00:00:23,100
首先我们客户端去请求服务端的时候

8
00:00:23,100 --> 00:00:25,920
我们是不是需要去传入一个accepts这样一个字段

9
00:00:25,920 --> 00:00:28,980
这样一个字段的时候是不是咱们http里面的

10
00:00:28,980 --> 00:00:30,000
你比如说

11
00:00:30,000 --> 00:00:33,080
比如说我们来看一下咱们的客户端发出去到底是什么

12
00:00:33,080 --> 00:00:37,940
还是我们刚才去比如说我们去启动刚才的一个

13
00:00:37,940 --> 00:00:43,060
啊会占用啊大家要需要注意你比如说我们刚才是不是启动了node chapter

14
00:00:43,060 --> 00:00:44,600
 1 app.gs 这里大家可以看到

15
00:00:44,600 --> 00:00:53,040
咱们3000端口其实已经被占用了addressed already in use 为什么呀其实我们是不是已经断掉了但是他为什么会被占用呢其实是我们在调试的时候是没断掉大家看到

16
00:00:53,040 --> 00:00:55,600
没断掉所以我们需要去点击在一个停止按钮

17
00:00:55,600 --> 00:00:56,880
此时他才会释放

18
00:00:56,880 --> 00:00:58,160
我们再起

19
00:00:58,680 --> 00:01:05,960
这里呢其实会大家会经常碰到 向大家注意好 我们来看一下这样一个htb请求咱们的咱们刚才是不是讲了咱们的requise的他的accept之断呢

20
00:01:05,960 --> 00:01:06,440
 我们来看一下

21
00:01:06,440 --> 00:01:08,320
accept之断是什么

22
00:01:08,320 --> 00:01:12,720
requise的requise的header是咱们是不是在黑的里面找

23
00:01:12,720 --> 00:01:16,100
accept是什么text html 也就是说说明什么问题

24
00:01:16,100 --> 00:01:23,960
只要是我们http 地址发出去的请求 他默认的accept 也就是说 accept之断 他的意思就是说我期望客户端给我返回什么样的数据

25
00:01:24,440 --> 00:01:28,940
他呢就会传入test html 怎么application xml 他呢是按一个优先级排序了

26
00:01:28,940 --> 00:01:31,800
他的优先级最高的就是我希望去得到一个html的片段

27
00:01:31,800 --> 00:01:33,620
好

28
00:01:33,620 --> 00:01:35,840
这里呢

29
00:01:35,840 --> 00:01:41,920
我们呢 你比如说我们扣端是不是传了我希望去得到html 对吧 那么

30
00:01:41,920 --> 00:01:48,820
咱们的服务端也就是noggs 他如果说你看到你要hml 那么noggs 他是不是就会去返回一个hm片段回来

31
00:01:48,820 --> 00:01:50,860
那么假如说你希望比如说扣端

32
00:01:51,820 --> 00:01:56,960
需要叉码药了是不是也通过比如说咱们的都是杰斯康泰斯的首先我去看一下你扣断要什么比如说你要叉码药

33
00:01:56,960 --> 00:02:03,200
此时呢我呢就给一个叉码药并且呢我是不是需要把咱们的扣端不咱们露了解释是不是需要把瑞子棒死的太快给掉了

34
00:02:03,200 --> 00:02:10,960
因为你exy的说明你要叉码药那么我返回给你的时候是不是也要给我的返回给你的数据一个标记啊说明我给你的我给你的就是叉码药

35
00:02:10,960 --> 00:02:14,560
对吧因为如果说咱们的瑞子棒死不去标记一个太谱

36
00:02:14,940 --> 00:02:15,900
那给占一个玻璃

37
00:02:15,900 --> 00:02:17,780
那么客户端也不知道你到底返回的什么

38
00:02:17,780 --> 00:02:19,580
那么你返回的是IQML还是XML呢

39
00:02:19,580 --> 00:02:21,180
他也不知道还是资不穿呢

40
00:02:21,180 --> 00:02:23,180
所以说咱们Response也需要给一个type

41
00:02:23,180 --> 00:02:26,780
同样的如果说你客户端需要一个节省

42
00:02:26,780 --> 00:02:29,180
那么Response他就会返回一个节省对象

43
00:02:29,180 --> 00:02:30,380
而且还会把type给带上

44
00:02:30,380 --> 00:02:32,460
这里其实属于咱们http里面的一个内容

45
00:02:32,460 --> 00:02:33,700
如果说对这块有所疑问

46
00:02:33,700 --> 00:02:35,060
或者说稍微有点不明白同学

47
00:02:35,060 --> 00:02:36,260
可以来去回顾一下

48
00:02:36,260 --> 00:02:37,580
或者温习一下http里面内容

49
00:02:37,580 --> 00:02:39,300
其实这块内容也不是很难

50
00:02:39,300 --> 00:02:41,220
很好的去其实很好理解

51
00:02:41,220 --> 00:02:43,340
好这里就是咱们都的GS里面

52
00:02:43,340 --> 00:02:47,440
htb 他那些response的类型 也就是咱们怎么样去通过call的context去处理

53
00:02:47,440 --> 00:02:50,500
去处理咱们的response和request他类型之间的一些关系

54
00:02:50,500 --> 00:02:52,040
这里呢 我们再来看一下

55
00:02:52,040 --> 00:02:53,320
我们

56
00:02:53,320 --> 00:02:58,440
怎么样让call去读取一个fm模板文件 然后将在一个模板返回用户 什么意思

57
00:02:58,440 --> 00:03:00,500
什么意思

58
00:03:00,500 --> 00:03:02,540
好

59
00:03:02,540 --> 00:03:03,820
我们刚才

60
00:03:03,820 --> 00:03:07,920
同学们 我们刚才是不是通过context.response.boli 是不是返回一个字幕创

61
00:03:07,920 --> 00:03:08,680
好的word

62
00:03:08,680 --> 00:03:11,760
其实呢 我们浏览器是不是把它解析成 也是解析成一个fm 为什么

63
00:03:12,020 --> 00:03:15,140
因为刚才讲到的我们浏览器默认发出去了accept自断是不是HML

64
00:03:15,140 --> 00:03:18,900
所以只是服务端漏进去返回的时候就自动的去处理给了他一个HML片段

65
00:03:18,900 --> 00:03:20,800
那么他为什么会自动

66
00:03:20,800 --> 00:03:22,320
是不是Core去处理的

67
00:03:22,320 --> 00:03:23,740
就和咱们的status200一样

68
00:03:23,740 --> 00:03:25,000
其实明明默认是404

69
00:03:25,000 --> 00:03:27,220
那么为什么他返回的status是200

70
00:03:27,220 --> 00:03:30,100
因为咱们去修改body的时候是不是有个set默认给改掉了

71
00:03:30,100 --> 00:03:30,620
其实和这里

72
00:03:30,620 --> 00:03:32,920
他为什么会返回HML

73
00:03:32,920 --> 00:03:34,460
HoloWord为什么会返回HML

74
00:03:34,460 --> 00:03:35,080
原理是一样

75
00:03:35,080 --> 00:03:36,460
好

76
00:03:36,460 --> 00:03:38,160
那么这里我们来看一下

77
00:03:38,160 --> 00:03:40,400
假如说我们想读取一个HML模板

78
00:03:40,400 --> 00:03:41,720
然后把它给返回一个扣端怎么样去做

79
00:03:41,720 --> 00:03:45,220
好 这里我已经写好了一个demo

80
00:03:45,220 --> 00:03:46,660
比如说我们现在写了一个demo

81
00:03:46,660 --> 00:03:48,640
比如说有这样一个HM的模板

82
00:03:48,640 --> 00:03:50,440
我们弄的件事怎么样返回给咱们的扣呢

83
00:03:50,440 --> 00:03:54,300
好 我们就在HML.js里面去写

84
00:03:54,300 --> 00:03:56,260
首先我把这样一个代码给扣过来

85
00:03:56,260 --> 00:04:05,100
那么如果说我们想去

86
00:04:05,100 --> 00:04:07,660
想去返回这样一个demo.HML

87
00:04:07,660 --> 00:04:09,380
首先我们先来分析一下思路

88
00:04:09,380 --> 00:04:12,240
我们必须去做的事情是什么

89
00:04:12,240 --> 00:04:13,680
是不是使用

90
00:04:13,680 --> 00:04:14,460
漏的GS

91
00:04:14,460 --> 00:04:15,260
干嘛

92
00:04:15,260 --> 00:04:16,780
是不是读取文件呢

93
00:04:16,780 --> 00:04:21,360
要不呢

94
00:04:21,360 --> 00:04:23,900
是不是就通过RazeBounce

95
00:04:23,900 --> 00:04:26,260
把咱们漏的GS读取的内容给返回个扣端呢

96
00:04:26,260 --> 00:04:27,400
RazeBounce

97
00:04:27,400 --> 00:04:29,400
把读取的内容

98
00:04:29,400 --> 00:04:31,420
返回给扣端

99
00:04:31,420 --> 00:04:31,800
好

100
00:04:31,800 --> 00:04:33,320
那么我们应该怎么样去做呢

101
00:04:33,320 --> 00:04:34,780
漏的GS怎么样去读取文件

102
00:04:34,780 --> 00:04:36,020
是不是来白死模块啊

103
00:04:36,020 --> 00:04:36,320
同学们

104
00:04:36,320 --> 00:04:37,560
比如说

105
00:04:37,560 --> 00:04:38,300
等于

106
00:04:38,300 --> 00:04:39,340
require

107
00:04:39,340 --> 00:04:41,700
怎么呢

108
00:04:41,700 --> 00:04:42,380
FS

109
00:04:42,380 --> 00:04:43,020
好

110
00:04:43,020 --> 00:04:44,320
那么这里呢

111
00:04:44,320 --> 00:04:44,900
我们先来

112
00:04:44,900 --> 00:04:47,240
读取一下

113
00:04:47,240 --> 00:04:55,620
好

114
00:04:55,620 --> 00:04:56,080
先刷掉

115
00:04:56,080 --> 00:04:58,080
首先

116
00:04:58,080 --> 00:05:01,020
首先其他的代码

117
00:05:01,020 --> 00:05:01,580
我们不需要动吧

118
00:05:01,580 --> 00:05:02,140
我们只需要在

119
00:05:02,140 --> 00:05:02,980
main这样一个方法里面

120
00:05:02,980 --> 00:05:03,840
是不是把咱们的response

121
00:05:03,840 --> 00:05:04,420
给改成

122
00:05:04,420 --> 00:05:05,460
咱们的既然是读取

123
00:05:05,460 --> 00:05:06,460
而且要买的文件就可以了

124
00:05:06,460 --> 00:05:06,900
首先

125
00:05:06,900 --> 00:05:08,100
咱们来设置一下

126
00:05:08,100 --> 00:05:09,740
康泰的点瑞子

127
00:05:09,740 --> 00:05:12,060
棒是不是讲到了

128
00:05:12,060 --> 00:05:13,460
咱们是不是要把太婆改为什么

129
00:05:13,460 --> 00:05:14,520
是不改为学买哦

130
00:05:14,520 --> 00:05:14,720
好

131
00:05:14,720 --> 00:05:16,580
那么改为埃取tml之后呢

132
00:05:16,580 --> 00:05:18,600
比如说咱们康泰的磁石

133
00:05:18,600 --> 00:05:20,380
是不是咱们要修改咱们的波地啊

134
00:05:20,380 --> 00:05:21,240
一只棒是点

135
00:05:21,240 --> 00:05:22,220
波地等于什么呢

136
00:05:22,220 --> 00:05:23,960
是不是等于咱们fs读取了

137
00:05:23,960 --> 00:05:25,080
学买文件的片段呢

138
00:05:25,080 --> 00:05:25,980
怎么样去读取了

139
00:05:25,980 --> 00:05:26,240
这里呢

140
00:05:26,240 --> 00:05:27,280
我们通过流的形式去读

141
00:05:27,280 --> 00:05:28,120
这些性能会好一些

142
00:05:28,120 --> 00:05:29,340
rate

143
00:05:29,340 --> 00:05:31,900
是不是去create

144
00:05:31,900 --> 00:05:32,540
一个reads dream

145
00:05:32,540 --> 00:05:33,600
因为我们是去读取它

146
00:05:33,600 --> 00:05:34,180
对吧

147
00:05:34,180 --> 00:05:35,380
reads dream

148
00:05:35,380 --> 00:05:36,900
什么呢

149
00:05:36,900 --> 00:05:37,900
咱们的demo

150
00:05:37,900 --> 00:05:39,900
DemoHMI的路径是什么

151
00:05:39,900 --> 00:05:41,900
DemoHMI的路径是什么

152
00:05:41,900 --> 00:05:43,900
Demo.js

153
00:05:43,900 --> 00:05:45,900
好 大家觉得这样我们能不能读取到我们的HMI的文件了

154
00:05:45,900 --> 00:05:47,900
大家思考一下

155
00:05:47,900 --> 00:05:49,900
不知道 对吧

156
00:05:49,900 --> 00:05:51,900
那我们就来看一下到底能不能读取到

157
00:05:51,900 --> 00:05:53,900
好 这里应该不是APP.js吧

158
00:05:53,900 --> 00:05:55,900
应该是什么

159
00:05:55,900 --> 00:05:57,900
HTML.js

160
00:05:57,900 --> 00:05:59,900
好 是不是启动了服务

161
00:05:59,900 --> 00:06:01,900
那我们来看一下到底能不能读取到

162
00:06:01,900 --> 00:06:03,900
好 大家可以看到这里是不是报错了

163
00:06:03,900 --> 00:06:04,900
LowSatisfierwall

164
00:06:04,900 --> 00:06:06,900
DirectoryOpen.js

165
00:06:06,900 --> 00:06:08,620
为什么呀

166
00:06:08,620 --> 00:06:09,960
有的同学可能会问

167
00:06:09,960 --> 00:06:12,040
我ihml.gms在这里

168
00:06:12,040 --> 00:06:13,840
我的demo.hml.gms在这里

169
00:06:13,840 --> 00:06:15,020
他们俩是同级的

170
00:06:15,020 --> 00:06:17,020
我点斜杠demo.gms

171
00:06:17,020 --> 00:06:17,860
为什么读到

172
00:06:17,860 --> 00:06:19,500
这不是没毛病吗

173
00:06:19,500 --> 00:06:19,820
对吧

174
00:06:19,820 --> 00:06:21,160
其实大家注意

175
00:06:21,160 --> 00:06:21,960
大家注意个问题

176
00:06:21,960 --> 00:06:24,200
我们执行的命令是什么呀

177
00:06:24,200 --> 00:06:25,900
我们执行的命令是不是

178
00:06:25,900 --> 00:06:28,420
是不是no的

179
00:06:28,420 --> 00:06:29,320
瞎子1

180
00:06:29,320 --> 00:06:30,020
ihm.gms

181
00:06:30,020 --> 00:06:32,380
那么我们的点斜杠是相对于谁

182
00:06:32,380 --> 00:06:33,760
我们的点斜杠

183
00:06:33,760 --> 00:06:35,180
是不是相对于他呀

184
00:06:35,180 --> 00:06:36,380
是不是相对于他

185
00:06:36,380 --> 00:06:39,000
那么它下面有没有一个demo点HML

186
00:06:39,000 --> 00:06:41,080
有没有一个demo点HML

187
00:06:41,080 --> 00:06:42,300
那写错了

188
00:06:42,300 --> 00:06:44,080
有没有

189
00:06:44,080 --> 00:06:46,780
它下面没有吧

190
00:06:46,780 --> 00:06:48,900
是不是只有在chapter1这样一个文件夹下面

191
00:06:48,900 --> 00:06:49,620
才有对吧

192
00:06:49,620 --> 00:06:50,760
那怎么去解决这个问题

193
00:06:50,760 --> 00:06:52,240
之前我们是不是讲过

194
00:06:52,240 --> 00:06:55,140
需要使用到pass这样一个

195
00:06:55,140 --> 00:07:00,020
是不是需要使用pass这样一个

196
00:07:00,020 --> 00:07:02,540
防反rogyas提供了一个

197
00:07:02,540 --> 00:07:04,000
是不是处理咱们路径

198
00:07:04,000 --> 00:07:04,940
好

199
00:07:04,940 --> 00:07:05,880
那么我们再来看一下

200
00:07:05,880 --> 00:07:35,880
我们直接pass.resolve.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo.dmo

201
00:07:35,880 --> 00:07:38,180
也就是指的这个DRM是不是指的咱们chapp的1

202
00:07:38,180 --> 00:07:43,880
那么这样你再去点斜杠demo.js 这样是不是就可以

203
00:07:43,880 --> 00:07:46,680
这样是不是就可以去读到来

204
00:07:46,680 --> 00:07:47,980
所以那同学们一定要注意

205
00:07:47,980 --> 00:07:49,880
一定要区别

206
00:07:49,880 --> 00:07:51,180
路径

207
00:07:51,180 --> 00:07:52,880
好好体会一下

208
00:07:52,880 --> 00:07:55,580
好 这里那是非常重要的一部分

209
00:07:55,580 --> 00:07:57,080
很多同学们一不小心就会去搞错

210
00:07:57,080 --> 00:07:58,580
这里是nodejs他比较难的一个原因

211
00:07:58,580 --> 00:08:00,380
为什么说有的同学学了一年两年

212
00:08:00,380 --> 00:08:02,080
nodejs好像还是学了一头雾水

213
00:08:02,080 --> 00:08:02,880
就是这样一个原因

214
00:08:02,880 --> 00:08:03,880
你碰到问题的时候

215
00:08:03,880 --> 00:08:05,180
一定要去思考他的本质是什么

216
00:08:05,180 --> 00:08:05,820
他的原因是什么

217
00:08:05,820 --> 00:08:06,420
你不能说

218
00:08:06,420 --> 00:08:07,420
我看到大家都用什么

219
00:08:07,420 --> 00:08:08,380
passing,resort,passing

220
00:08:08,380 --> 00:08:10,060
join.demo.hml

221
00:08:10,060 --> 00:08:11,840
那么我看到别人这样用

222
00:08:11,840 --> 00:08:12,280
我就这样用

223
00:08:12,280 --> 00:08:13,020
你一定要去思考

224
00:08:13,020 --> 00:08:13,920
这样的原因是什么

225
00:08:13,920 --> 00:08:14,880
为什么我的文件读书不到

226
00:08:14,880 --> 00:08:16,600
你只有去把握

227
00:08:16,600 --> 00:08:17,720
一些代码

228
00:08:17,720 --> 00:08:18,460
它执行失败的原因

229
00:08:18,460 --> 00:08:19,560
那么你才能去成长

230
00:08:19,560 --> 00:08:20,060
你不能说

231
00:08:20,060 --> 00:08:21,160
我光去写

232
00:08:21,160 --> 00:08:21,640
但是不思考

233
00:08:21,640 --> 00:08:22,940
但是不行的

234
00:08:22,940 --> 00:08:23,560
其实这样

235
00:08:23,560 --> 00:08:24,900
都是一些细节问题

236
00:08:24,900 --> 00:08:25,340
但是如果说

237
00:08:25,340 --> 00:08:26,080
你把这样一些细节问题

238
00:08:26,080 --> 00:08:26,780
给思考透彻了

239
00:08:26,780 --> 00:08:27,820
你的技术自然会成长

240
00:08:27,820 --> 00:08:30,100
好

241
00:08:30,100 --> 00:08:30,980
那么我来执行看一下

242
00:08:30,980 --> 00:08:38,360
好大家看到我们是不是反过来

243
00:08:38,360 --> 00:08:39,220
我是Demo

244
00:08:39,220 --> 00:08:39,600
对吧

245
00:08:39,600 --> 00:08:40,580
我们来看一下Response

246
00:08:40,580 --> 00:08:43,800
Response

247
00:08:43,800 --> 00:08:44,600
我们刚才是不是改过来

248
00:08:44,600 --> 00:08:44,900
HTML

249
00:08:44,900 --> 00:08:45,800
Contest.type

250
00:08:45,800 --> 00:08:46,560
TestHTML

251
00:08:46,560 --> 00:08:46,840
对吧

252
00:08:46,840 --> 00:08:47,200
好

253
00:08:47,200 --> 00:08:48,120
这里呢就是我们

254
00:08:48,120 --> 00:08:49,700
这里呢就是我们

255
00:08:49,700 --> 00:08:51,700
Core他的一些基本使用

256
00:08:51,700 --> 00:08:53,140
那么我们来总结一下

257
00:08:53,140 --> 00:08:54,060
这一节课他的一些内容

258
00:09:00,980 --> 00:09:03,920
好 我们刚才是不是讲的http

259
00:09:03,920 --> 00:09:07,440
他的一些Response 他的一个特点是什么

260
00:09:07,440 --> 00:09:13,360
特点是什么 是不是服务端需要知道什么 是不是需要知道客户端

261
00:09:13,360 --> 00:09:17,180
要什么 对吧 那么你客户端要什么 通过什么之段

262
00:09:17,180 --> 00:09:21,760
客户端是不是通过Request点

263
00:09:21,760 --> 00:09:28,540
Saps这样一个之段了 那么服务端呢 服务端通过什么 是不是Response点

264
00:09:28,540 --> 00:09:30,280
它们是层对层存在的

265
00:09:30,280 --> 00:09:31,420
你Apps要什么

266
00:09:31,420 --> 00:09:32,820
那么我的Response点Type

267
00:09:32,820 --> 00:09:33,380
它就是什么

268
00:09:33,380 --> 00:09:33,660
对吧

269
00:09:33,660 --> 00:09:34,000
好

270
00:09:34,000 --> 00:09:36,060
我们刚才是不是还讲呢

271
00:09:36,060 --> 00:09:37,140
我们NodeGIS

272
00:09:37,140 --> 00:09:39,080
咱们去返回HTML

273
00:09:39,080 --> 00:09:40,320
片段了

274
00:09:40,320 --> 00:09:40,600
对吧

275
00:09:40,600 --> 00:09:41,340
咱们去返回

276
00:09:41,340 --> 00:09:42,040
是不是首先

277
00:09:42,040 --> 00:09:43,860
使用FS

278
00:09:43,860 --> 00:09:45,000
读取吧

279
00:09:45,000 --> 00:09:45,700
那么FS模块

280
00:09:45,700 --> 00:09:46,840
它是不是有很多读取文件的方法

281
00:09:46,840 --> 00:09:47,760
你可以选择其中一种

282
00:09:47,760 --> 00:09:48,620
根据你的业务场景

283
00:09:48,620 --> 00:09:49,360
那么FS

284
00:09:49,360 --> 00:09:50,620
它有哪些读取方法呢

285
00:09:50,620 --> 00:09:52,740
这里面可能你需要自己去读文档的

286
00:09:52,740 --> 00:09:54,160
你比如说我可以去同步的读取

287
00:09:54,160 --> 00:09:54,600
一步的读取

288
00:09:54,600 --> 00:09:55,940
可以以牛的形式去读取

289
00:09:55,940 --> 00:09:56,360
对吧

290
00:09:56,360 --> 00:10:00,430
因为老师这里不可能说讲到一点了就去给他们去温习一下 那么我们的课程可能就没法上了

291
00:10:00,430 --> 00:10:03,240
 所以说呢 我们一定要去学会自己去学习 自己去看网站

292
00:10:03,240 --> 00:10:09,160
好 那么我读取到之后 是不是通过Response 是不是通过咱们contest点 瑞兹

293
00:10:09,160 --> 00:10:12,920
bounce 返回数据啊

294
00:10:12,920 --> 00:10:17,220
好 那么我们返回数据的时候 是不是需要做一个什么问题啊

295
00:10:17,220 --> 00:10:19,660
是不是入境了

296
00:10:19,660 --> 00:10:21,760
pass点resolve

297
00:10:22,760 --> 00:10:27,260
是不是咱们要搞清楚pass and resolve 和咱们直接直接输入 比如说你直接输入怎么点斜杠

298
00:10:27,260 --> 00:10:30,700
 它的一个区别是什么 对吧 因为咱们load 命令执行的时候是什么

299
00:10:30,700 --> 00:10:34,800
它的路径就是基于谁的 所以说你需要去利用pass and resolve 在一个loadload

300
00:10:34,800 --> 00:10:36,760
 它原生提供了一个模块去解决这个问题

301
00:10:36,760 --> 00:10:38,640
好 这里就是我们这几个的内容

