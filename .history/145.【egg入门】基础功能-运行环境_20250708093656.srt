1
00:00:00,000 --> 00:00:01,540
好这节课我们就来看一下运行环境

2
00:00:01,540 --> 00:00:04,600
那么运行环境在一GG里面它呢其实是通过

3
00:00:04,600 --> 00:00:09,220
再来一个配置一GG server因为你比如说你去等于pro就把它设置为一个什么呢是不是生产环境

4
00:00:09,220 --> 00:00:13,560
那么我们在开发中起时呢我们需要去区分环境你比如说生产环境和开发环境

5
00:00:13,560 --> 00:00:14,340
像wipack

6
00:00:14,340 --> 00:00:17,920
它呢是什么环境下的产物是不是一个开发环境我们所需要的一个工具

7
00:00:17,920 --> 00:00:19,960
那么运行环境实际上是非常重要的

8
00:00:19,960 --> 00:00:23,560
在nodejs里面会根据node因为来区分一个运行环境

9
00:00:23,560 --> 00:00:24,840
是不是通过process

10
00:00:24,840 --> 00:00:26,880
node因为是不是process下面一个属性

11
00:00:27,140 --> 00:00:32,260
但是呢在1G 里面他使用这样一个变量去区分 他会更加的精细 什么意思

12
00:00:32,260 --> 00:00:33,020
 那我们先来看一下

13
00:00:33,020 --> 00:00:38,920
我们在1G 里面怎么去获取 我们当前一个运行环境 通过app.config.inv 那我们就来看一下

14
00:00:38,920 --> 00:00:42,240
首先呢 你比如说我们在

15
00:00:42,240 --> 00:00:43,260
home里面

16
00:00:43,260 --> 00:00:44,540
我们去

17
00:00:44,540 --> 00:00:47,360
我们去把咱们的环境打印出来

18
00:00:47,360 --> 00:00:49,420
app.config.inv

19
00:00:49,420 --> 00:00:52,220
好 那我们来把咱们的

20
00:00:52,220 --> 00:00:54,540
服务给重启下 npm run dv

21
00:00:57,140 --> 00:00:58,940
好,走里,大家觉得我们现在在什么环境

22
00:00:58,940 --> 00:01:03,800
大家可以看到,是不是local,local代表什么,是不是代表本地,对吧,因为我们现在是一个开发环境

23
00:01:03,800 --> 00:01:04,820
那么如果说

24
00:01:04,820 --> 00:01:06,440
如果说我们要在生产环境去

25
00:01:06,440 --> 00:01:09,420
部署怎么办,那么在1GG里面,我们的生产环境的一个

26
00:01:09,420 --> 00:01:12,500
比如说我们去启动服务的命令是什么,实际上是npm run

27
00:01:12,500 --> 00:01:19,160
好,大家可以看到,我们的服务启动之后,是不是进程马上退到后台里面去了

28
00:01:19,160 --> 00:01:21,720
1GG start,local host 7001

29
00:01:21,720 --> 00:01:25,560
但是呢,我们的进程是不是已经进入了一个守护进程的一个状态,因为在什么呢

30
00:01:26,060 --> 00:01:30,060
在咱们的一个生产环境里面是不是不能你挂在命令行里面 因为如果说你的命令行窗口一关闭

31
00:01:30,060 --> 00:01:32,460
 是不是进程就退出了 所以说呢他需要去使用一个守护进程

32
00:01:32,460 --> 00:01:33,740
那么我们来看一下

33
00:01:33,740 --> 00:01:36,560
这里我们就来看一下我们一个生产环境是什么样的一个

34
00:01:36,560 --> 00:01:38,340
好 走你

35
00:01:38,340 --> 00:01:40,660
好 大家可以看到我们是不是在一个

36
00:01:40,660 --> 00:01:45,000
守护进程里面看不到我们一个打印出来的一个信息啊 不过这里我们有个办法是什么

37
00:01:45,000 --> 00:01:48,080
我们是不是可以直接通过玻璃把它给打印出来

38
00:01:48,080 --> 00:01:50,380
好 那么我们来重启一下服务

39
00:01:50,380 --> 00:01:55,760
好 其实这里会报一个错 报什么错呢 原因是什么呢

40
00:01:56,020 --> 00:01:58,320
原因是不是因为我们现在是使用了一个守护进程

41
00:01:58,320 --> 00:02:00,620
那么我们是不是先要需要把我们的一个进程给推出

42
00:02:00,620 --> 00:02:03,180
好 那么我们就来把进程给推出一下 重启一遍

43
00:02:03,180 --> 00:02:05,500
-i7001

44
00:02:05,500 --> 00:02:08,300
好 PID是6834 我们把它给杀掉

45
00:02:08,300 --> 00:02:11,120
好 那我们就来重启一下

46
00:02:11,120 --> 00:02:14,700
好 我们来访问一下页面

47
00:02:14,700 --> 00:02:15,480
prod

48
00:02:15,480 --> 00:02:20,070
大家是不是可以看到 当我们通过npm start去启动我们服务的时候 我们的环境变量变成了什么

49
00:02:20,070 --> 00:02:21,100
 是不是变成prod呀

50
00:02:21,100 --> 00:02:23,920
prod是什么 是不是 production 生产环境

51
00:02:24,180 --> 00:02:26,220
那么这里是一GG他自己去提供了

52
00:02:26,220 --> 00:02:27,760
而且这里呢会有一个印射关系

53
00:02:27,760 --> 00:02:29,300
有一个印射关系

54
00:02:29,300 --> 00:02:32,620
你比如说在nodeGGS里面我们的生产环境他默认的是production

55
00:02:32,620 --> 00:02:34,540
那么在一GG里面实际上他规定的是prod

56
00:02:34,540 --> 00:02:35,180
包括了

57
00:02:35,180 --> 00:02:39,020
开发依赖在nodeGGS里面就是dv或者development

58
00:02:39,020 --> 00:02:41,840
但是在一GG里面是local你包括一个测试环境

59
00:02:41,840 --> 00:02:45,680
在node里面是test在一GG里面是unittest代表了单元测试

60
00:02:45,680 --> 00:02:50,300
而且呢你不仅可以去使用他默认的一些环境面量而且你还可以去自己去定义

61
00:02:50,300 --> 00:02:53,360
你比如说你以后需要使用到一个集成测试环境比如说sit

62
00:02:53,360 --> 00:02:55,780
你呢可以在启动命令的时候呢去设置

63
00:02:55,780 --> 00:02:59,380
一GG server因为这样的一个变量可以去控制

64
00:02:59,380 --> 00:03:00,760
那我们就来尝试一下

65
00:03:00,760 --> 00:03:04,480
好 这里呢我们好像是不是也需要把进程先给杀掉啊

66
00:03:04,480 --> 00:03:05,540
6970

67
00:03:05,540 --> 00:03:07,680
KL-96970

68
00:03:07,680 --> 00:03:10,840
好 我们重新 我们呢在启动的时候是不是用这样的一个命令呢

69
00:03:10,840 --> 00:03:13,060
一GG server因为等于

70
00:03:13,060 --> 00:03:16,780
好 我们呢 比如说我们把它改为set

71
00:03:16,780 --> 00:03:18,480
然后我们再来执行npm run

72
00:03:18,480 --> 00:03:19,400
start

73
00:03:19,400 --> 00:03:22,260
好 我们再来访问一下下面

74
00:03:22,260 --> 00:03:24,260
大家可以看到我们的环境变量是不是变成Site

75
00:03:24,260 --> 00:03:28,260
好 那么这里呢就是我们运行环境的一个

76
00:03:28,260 --> 00:03:30,260
内容我们一起来总结一下

77
00:03:30,260 --> 00:03:34,260
我们刚才是不是讲到了咱们的一个

78
00:03:34,260 --> 00:03:36,260
运行

79
00:03:36,260 --> 00:03:38,260
运行环境

80
00:03:38,260 --> 00:03:40,260
那么呢我们可以通过什么方式去修改它

81
00:03:40,260 --> 00:03:42,260
是不是

82
00:03:42,260 --> 00:03:44,260
1GG

83
00:03:44,260 --> 00:03:46,260
什么呢 server

84
00:03:46,260 --> 00:03:48,260
-inv等于

85
00:03:48,260 --> 00:03:50,260
等于什么呢等于你需要了一个

86
00:03:50,260 --> 00:03:51,800
一个是不是需要的一个环境了

87
00:03:51,800 --> 00:03:52,560
然后再去执行

88
00:03:52,560 --> 00:03:55,540
然后是不是再去执行

89
00:03:55,540 --> 00:03:57,180
npm run start

90
00:03:57,180 --> 00:03:59,980
那么运行环境在哪里可以获取到呢

91
00:03:59,980 --> 00:04:03,920
是不是你可以通过app.app什么是不是

92
00:04:03,920 --> 00:04:08,160
config.inv去获取咱们的一个运行环境

93
00:04:08,160 --> 00:04:10,060
好这里就是我们这节课的内容

