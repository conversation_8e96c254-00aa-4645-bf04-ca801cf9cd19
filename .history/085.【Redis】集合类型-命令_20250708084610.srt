1
00:00:00,000 --> 00:00:01,920
好,这一节课我们就来看一下集合类型

2
00:00:01,920 --> 00:00:06,320
那么在Redis里面它有一种数据类型非常适合存储文章的标签

3
00:00:06,320 --> 00:00:09,220
之前我们讲过我们文章是不是会打一些tags

4
00:00:09,220 --> 00:00:10,280
你比如说一篇文章

5
00:00:10,280 --> 00:00:13,140
你是加码类型的,技术类型的,生活类型的,汽车类型的

6
00:00:13,140 --> 00:00:14,400
它就是咱们文章的一个tags

7
00:00:14,400 --> 00:00:16,060
也就是一个集合

8
00:00:16,060 --> 00:00:18,980
那么什么是集合呢?

9
00:00:18,980 --> 00:00:21,660
其实集合的概念在我们高中的数学课本中就已经学习过

10
00:00:21,660 --> 00:00:22,680
集合

11
00:00:22,680 --> 00:00:26,920
那么一个集合类型它最多可以存储2的30,2的次方

12
00:00:26,920 --> 00:00:27,860
那么

13
00:00:27,860 --> 00:00:30,160
集合类型和我们的列表

14
00:00:30,160 --> 00:00:33,020
它又有一些相似之处和一些区别

15
00:00:33,020 --> 00:00:33,800
你比如说

16
00:00:33,800 --> 00:00:35,620
我们的集合类型和列表

17
00:00:35,620 --> 00:00:36,540
它们都可以去存储

18
00:00:36,540 --> 00:00:37,700
2的32之方减1

19
00:00:37,700 --> 00:00:39,780
但是我们的列表是不是有顺序

20
00:00:39,780 --> 00:00:41,320
你比如说L迫习和R迫习

21
00:00:41,320 --> 00:00:42,040
你从右边去迫习

22
00:00:42,040 --> 00:00:42,680
从左边去迫习

23
00:00:42,680 --> 00:00:44,040
它去按顺序去存取

24
00:00:44,040 --> 00:00:45,540
但是我们的集合不一样

25
00:00:45,540 --> 00:00:46,220
它没有顺序

26
00:00:46,220 --> 00:00:47,520
你存进去它就是随机的

27
00:00:47,520 --> 00:00:49,260
但是集合它是唯一的

28
00:00:49,260 --> 00:00:49,840
你比如说

29
00:00:49,840 --> 00:00:50,680
你一个数组里面

30
00:00:50,680 --> 00:00:51,200
是不是可以存储

31
00:00:51,200 --> 00:00:52,060
很多个0很多个1

32
00:00:52,060 --> 00:00:53,140
很多个数字都可以一样

33
00:00:53,140 --> 00:00:54,540
但是集合它不一样

34
00:00:54,540 --> 00:00:55,760
它是唯一的

35
00:00:55,760 --> 00:00:58,760
也就说咱们的集合

36
00:00:58,760 --> 00:00:59,980
在整个集合里面

37
00:00:59,980 --> 00:01:00,460
你存了一个0

38
00:01:00,460 --> 00:01:01,300
你就不能再存0了

39
00:01:01,300 --> 00:01:02,380
它是唯一的

40
00:01:02,380 --> 00:01:04,420
那么集合类型

41
00:01:04,420 --> 00:01:05,820
它常用的操作是什么呢

42
00:01:05,820 --> 00:01:06,220
你比如说

43
00:01:06,220 --> 00:01:07,000
首先肯定是可以加入

44
00:01:07,000 --> 00:01:08,040
或者酸出元素的

45
00:01:08,040 --> 00:01:08,980
包括了判断某个元素

46
00:01:08,980 --> 00:01:09,600
是不是存在

47
00:01:09,600 --> 00:01:11,000
它怎么样实现

48
00:01:11,000 --> 00:01:11,600
它是通过

49
00:01:11,600 --> 00:01:12,460
在Reddit内部

50
00:01:12,460 --> 00:01:13,860
它是使用一个空的散列表

51
00:01:13,860 --> 00:01:14,900
也就是说通过HashTable

52
00:01:14,900 --> 00:01:15,540
去实现的

53
00:01:15,540 --> 00:01:16,760
这些操作的时间复杂度

54
00:01:16,760 --> 00:01:17,700
都是O1

55
00:01:17,700 --> 00:01:18,560
很低吧

56
00:01:18,560 --> 00:01:20,540
那么它的一个特点是什么

57
00:01:20,540 --> 00:01:21,440
它最厉害的地方

58
00:01:21,440 --> 00:01:22,160
就是可以进行

59
00:01:22,160 --> 00:01:23,700
并且交集和差级的一个运算

60
00:01:23,700 --> 00:01:24,720
那么我们就来看一下

61
00:01:24,720 --> 00:01:25,480
怎么去运算

62
00:01:25,480 --> 00:01:26,260
首先

63
00:01:26,260 --> 00:01:28,040
我们现在看一下

64
00:01:28,040 --> 00:01:29,580
他的第1个命令

65
00:01:29,580 --> 00:01:32,920
怎么样去增加和删除元素

66
00:01:32,920 --> 00:01:35,720
比如说

67
00:01:35,720 --> 00:01:39,060
大家看到S是可以想到我们的set

68
00:01:39,060 --> 00:01:39,820
咱们的GS里面

69
00:01:39,820 --> 00:01:41,880
ES6里面是不是也学过一个咱们set类型了

70
00:01:41,880 --> 00:01:42,900
比如说你去绿一个set

71
00:01:42,900 --> 00:01:43,920
那么咱们GS里面newset

72
00:01:43,920 --> 00:01:44,680
他也是唯一的吧

73
00:01:44,680 --> 00:01:45,200
好

74
00:01:45,200 --> 00:01:46,220
首先我们来看一下怎么样去

75
00:01:46,220 --> 00:01:47,240
新增一个元素

76
00:01:47,240 --> 00:01:48,020
比如说你去

77
00:01:48,020 --> 00:01:49,040
SA的一个

78
00:01:49,040 --> 00:01:50,060
Letters

79
00:01:50,060 --> 00:01:51,080
Letters A

80
00:01:51,860 --> 00:01:55,700
好 那么此时呢 我们就在集合letters里面去添加一个

81
00:01:55,700 --> 00:01:57,240
其实呢我们

82
00:01:57,240 --> 00:02:00,060
还可以去添加一些 比如说sl

83
00:02:00,060 --> 00:02:01,340
letters

84
00:02:01,340 --> 00:02:02,860
abc

85
00:02:02,860 --> 00:02:07,220
咱们反复指示 反复指示什么 就是你添加了多少个元素 刚才我们讲到在集合里面

86
00:02:07,220 --> 00:02:11,320
是不是不能够重复啊 所以呢 只有b和c给添加进去了 a 没有添加进去 这里呢就是

87
00:02:11,320 --> 00:02:12,080
集合

88
00:02:12,080 --> 00:02:17,720
好 那么我们再来看一下

89
00:02:17,720 --> 00:02:21,560
这边去酸除一个元素 你比如说我们要酸除就可以使用

90
00:02:21,820 --> 00:02:25,920
sremletters

91
00:02:25,920 --> 00:02:26,940
比如说我们要把A双掉

92
00:02:26,940 --> 00:02:30,020
好 此时那就双掉了一个元素

93
00:02:30,020 --> 00:02:33,860
那么我们双掉了A之后

94
00:02:33,860 --> 00:02:34,620
看一下

95
00:02:34,620 --> 00:02:36,160
咱们集合中所有的元素是哪些

96
00:02:36,160 --> 00:02:37,440
就可以利用咱们的第二个命令

97
00:02:37,440 --> 00:02:38,720
叫做smembers

98
00:02:38,720 --> 00:02:41,780
好 我们来执行一下smembers

99
00:02:41,780 --> 00:02:43,320
k k是什么呢 letters

100
00:02:43,320 --> 00:02:46,140
拼错了

101
00:02:46,140 --> 00:02:48,700
smembers letters c和b

102
00:02:48,700 --> 00:02:51,000
大家看到没有我们存储的时候

103
00:02:51,000 --> 00:02:54,840
是存储了ABC 但是我们读取是CB 所以说我们的集合它是无序的

104
00:02:54,840 --> 00:02:58,940
那么我们怎么样去判断一个元素在不在我们的集合中呢 就可以通过

105
00:02:58,940 --> 00:03:01,240
s is member

106
00:03:01,240 --> 00:03:04,320
去判断 比如说s is member

107
00:03:04,320 --> 00:03:07,380
letters

108
00:03:07,380 --> 00:03:09,940
我们来判断下A在不在里面 A 返回一个0 说明它不在

109
00:03:09,940 --> 00:03:12,760
比如说我们去判断下B在不在 返回1 说明它在里面吧

110
00:03:12,760 --> 00:03:14,300
那我们集合间的运算

111
00:03:14,300 --> 00:03:15,320
其实和我们

112
00:03:15,320 --> 00:03:17,360
课本里面的集合运算很像

113
00:03:17,360 --> 00:03:19,420
也比如说 其实它很简单

114
00:03:20,440 --> 00:03:21,200
他有三个命令

115
00:03:21,200 --> 00:03:22,240
sdiff

116
00:03:22,240 --> 00:03:24,020
sintel和sunion

117
00:03:24,020 --> 00:03:24,800
diff是什么呢

118
00:03:24,800 --> 00:03:25,820
我们两个集合

119
00:03:25,820 --> 00:03:27,360
哪些元素不一样

120
00:03:27,360 --> 00:03:28,120
就是diff

121
00:03:28,120 --> 00:03:29,400
intel呢是不是我们的交集啊

122
00:03:29,400 --> 00:03:30,420
他们都存在哪些元素

123
00:03:30,420 --> 00:03:31,440
union呢就是并集

124
00:03:31,440 --> 00:03:32,720
交集和我们的一个差集

125
00:03:32,720 --> 00:03:33,760
首先我们来看一下

126
00:03:33,760 --> 00:03:35,800
差集

127
00:03:35,800 --> 00:03:37,080
sdiff在一个命令

128
00:03:37,080 --> 00:03:38,360
那么这里呢

129
00:03:38,360 --> 00:03:40,660
首先我们来看一下

130
00:03:40,660 --> 00:03:42,200
比如说我们来重新去设置一下

131
00:03:42,200 --> 00:03:43,740
sadd

132
00:03:43,740 --> 00:03:44,760
比如说我们去set

133
00:03:44,760 --> 00:03:45,520
sadd

134
00:03:45,520 --> 00:03:46,800
比如说我们来存储一个

135
00:03:46,800 --> 00:03:49,120
a在一个元素里面去存储

136
00:03:49,120 --> 00:03:49,880
123

137
00:03:50,400 --> 00:03:56,600
好,我们存储了三个字,我们再来去存储个s add b,我们来存储234

138
00:03:56,600 --> 00:04:00,300
我们来看一下sdif他们的差距是什么

139
00:04:00,300 --> 00:04:04,400
比如说我们去判断a和b

140
00:04:04,400 --> 00:04:06,800
他们的差距是不是应该是1和4啊

141
00:04:06,800 --> 00:04:09,300
但是我们来看一下结果,它反复只是1

142
00:04:09,300 --> 00:04:10,800
什么意思

143
00:04:10,800 --> 00:04:15,500
其实sdif它会去计算b基于a,它有个是不一样的

144
00:04:15,500 --> 00:04:19,100
你比如说a里面有123,b里面有234,那么他们的差距是具于a来计算的

145
00:04:19,100 --> 00:04:23,460
所以是1,如果说你反过来,s div ba,它的差距就是4

146
00:04:23,460 --> 00:04:26,260
这里其实很好理解,你谁在前面就是基于谁去算

147
00:04:26,260 --> 00:04:31,140
而且s div它还支持重拾传入多个键

148
00:04:31,140 --> 00:04:33,180
你比如说我们去s set

149
00:04:33,180 --> 00:04:37,540
add一个c,我们给它存入

150
00:04:37,540 --> 00:04:40,340
3,4,5

151
00:04:40,340 --> 00:04:46,500
那么我们来看一下s div a,b,c,它们有哪些区别,是不是还是1

152
00:04:47,000 --> 00:04:50,840
所以呢你传入多个键他也是基于A去计算的

153
00:04:50,840 --> 00:04:54,940
那么接下来我们来看一下并级

154
00:04:54,940 --> 00:04:55,960
Sintel

155
00:04:55,960 --> 00:04:58,000
我们

156
00:04:58,000 --> 00:05:02,360
我们的A里面有123,B里面有234,我们来看他们的交集是什么,是不是23呢

157
00:05:02,360 --> 00:05:06,200
比如说Sintel A和B交集23

158
00:05:06,200 --> 00:05:10,560
那么同样的他们也支持传入多个键,比如说我们传入ABC

159
00:05:10,560 --> 00:05:14,900
Sintel A,B,C,大家看到没有他们的交集是3,为什么呀

160
00:05:15,680 --> 00:05:20,040
A里面有123,B里面有234,C里面有345,它们的交集是不是3呢

161
00:05:20,040 --> 00:05:23,360
好,那我们再来看一下第三个命令,suling,也就是并级

162
00:05:23,360 --> 00:05:25,160
suling

163
00:05:25,160 --> 00:05:28,740
A和B,它的并级是什么,是不是1234啊

164
00:05:28,740 --> 00:05:32,580
同样的它也可以支持多个字去传入sulingabc

165
00:05:32,580 --> 00:05:35,400
1235,对吧

166
00:05:35,400 --> 00:05:37,960
好,这里就是我们这一节课的内容

167
00:05:37,960 --> 00:05:40,000
咱们来总结一下

168
00:05:43,320 --> 00:05:45,620
我们刚才是不是讲到了我们的集合呀

169
00:05:45,620 --> 00:05:49,460
那么集合里面它有一些

170
00:05:49,460 --> 00:05:51,000
命令

171
00:05:51,000 --> 00:05:52,020
刚才讲了哪些命令

172
00:05:52,020 --> 00:05:53,560
首先第1个是不是添加和双除

173
00:05:53,560 --> 00:05:56,880
添加sadd

174
00:05:56,880 --> 00:05:58,160
双除了srem

175
00:05:58,160 --> 00:06:00,980
那么为什么以s开头啊

176
00:06:00,980 --> 00:06:02,520
大家还记得我们的闪念吗

177
00:06:02,520 --> 00:06:03,540
以h开头

178
00:06:03,540 --> 00:06:04,320
因为它是hush

179
00:06:04,320 --> 00:06:05,340
那么集合了set吧

180
00:06:05,340 --> 00:06:07,640
因为在yes6里面是不是也有一个集合的概念newset

181
00:06:07,640 --> 00:06:08,660
所以呢它是以s开头

182
00:06:08,660 --> 00:06:10,460
咱们又讲了

183
00:06:10,460 --> 00:06:12,000
是不是获取所有元素啊

184
00:06:12,000 --> 00:06:14,300
获取所有元素 什么命令

185
00:06:14,300 --> 00:06:16,860
是不是smembles

186
00:06:16,860 --> 00:06:21,480
还有呢 判断哪个元素

187
00:06:21,480 --> 00:06:24,280
是不是存不存在 判断存在用什么

188
00:06:24,280 --> 00:06:25,560
s is

189
00:06:25,560 --> 00:06:29,400
最后我们是不是又讲了他们的交集

190
00:06:29,400 --> 00:06:30,440
并级

191
00:06:30,440 --> 00:06:33,240
和xd

192
00:06:33,240 --> 00:06:36,580
好 那么交集是什么

193
00:06:36,580 --> 00:06:37,860
s inter

194
00:06:37,860 --> 00:06:39,140
并级呢

195
00:06:39,140 --> 00:06:39,640
s

196
00:06:39,640 --> 00:06:40,920
uni

197
00:06:41,700 --> 00:06:42,980
sif

198
00:06:42,980 --> 00:06:45,780
好 这里就是咱们这节课的内容

