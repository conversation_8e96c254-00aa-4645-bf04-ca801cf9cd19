1
00:00:00,000 --> 00:00:06,320
好 那么接下来我们就来把我们刚才所写的这个就是处理页面中的静态资源

2
00:00:06,320 --> 00:00:09,120
这种荣余的代码我们来给它统一的来处理一下

3
00:00:09,120 --> 00:00:13,660
也就是说现在大家可以看到我们这种代码写的其实很多地方都是重复的

4
00:00:13,660 --> 00:00:14,880
就是重复性太高了

5
00:00:14,880 --> 00:00:17,880
而且如果说假如说我在这个页面当中

6
00:00:17,880 --> 00:00:20,240
假如说我在这里我再来加一个

7
00:00:20,240 --> 00:00:21,480
例如我来加一个图片

8
00:00:21,480 --> 00:00:24,060
例如这个图片呢我让它来assize

9
00:00:24,060 --> 00:00:27,780
images里面这个01就是01.png

10
00:00:27,780 --> 00:00:30,020
也就是我们现在看到的这张图片

11
00:00:30,020 --> 00:00:31,240
那么这个时候

12
00:00:31,240 --> 00:00:32,840
来我们再回到这个流浪器当中

13
00:00:32,840 --> 00:00:33,640
我们来刷新一下

14
00:00:33,640 --> 00:00:35,120
那么此时我们就看到图片

15
00:00:35,120 --> 00:00:35,820
现在就是404

16
00:00:35,820 --> 00:00:36,940
原因是什么呢

17
00:00:36,940 --> 00:00:38,960
原因就是当它来到这个服务端的时候

18
00:00:38,960 --> 00:00:40,120
那么这里没有

19
00:00:40,120 --> 00:00:43,240
并没有这个代码能处理这个请求路径

20
00:00:43,240 --> 00:00:45,300
所以说就走到了最后的这个out

21
00:00:45,300 --> 00:00:46,360
然后处理了404

22
00:00:46,360 --> 00:00:47,100
所以就是这样

23
00:00:47,100 --> 00:00:48,560
那如果说你想单独处理它

24
00:00:48,560 --> 00:00:50,060
那么你还要是不是来加一个

25
00:00:50,060 --> 00:00:53,500
ioseifurl等于-assize-emage-0点偏见

26
00:00:53,500 --> 00:00:54,440
你还得这样来做

27
00:00:54,440 --> 00:00:56,040
所以说就变得非常的麻烦了

28
00:00:56,040 --> 00:00:56,640
那么此时的话

29
00:00:56,640 --> 00:00:58,000
我们可以来把这个过程的

30
00:00:58,000 --> 00:01:00,020
来给大家统一的来简化处理

31
00:01:00,020 --> 00:01:01,300
那么此时我们就发现

32
00:01:01,300 --> 00:01:03,500
其实针对于我们这个assess

33
00:01:03,500 --> 00:01:04,720
静态资源来说

34
00:01:04,720 --> 00:01:05,740
他们都有个规律

35
00:01:05,740 --> 00:01:06,580
什么规律呢

36
00:01:06,580 --> 00:01:07,540
就是他们的请求路径

37
00:01:07,540 --> 00:01:09,320
是都以-assess开头

38
00:01:09,320 --> 00:01:10,780
都以-assess开头

39
00:01:10,780 --> 00:01:11,340
也就是说

40
00:01:11,340 --> 00:01:11,760
那么这个时候

41
00:01:11,760 --> 00:01:13,220
我们就可以来统一处理

42
00:01:13,220 --> 00:01:13,660
也就是说

43
00:01:13,660 --> 00:01:15,500
如果这他的请求路径

44
00:01:15,500 --> 00:01:16,640
是以-assess开头

45
00:01:16,640 --> 00:01:18,640
那我能不能就直接去尝试着

46
00:01:18,640 --> 00:01:20,620
把它认为是一个静态资源

47
00:01:20,620 --> 00:01:22,900
我直接去读取物理词盘上

48
00:01:22,900 --> 00:01:23,680
这个assess里面

49
00:01:23,680 --> 00:01:24,900
对应的这个文件路径呢

50
00:01:24,900 --> 00:01:25,760
如果读到

51
00:01:25,760 --> 00:01:27,300
那么我就把它响应给这个客户端

52
00:01:27,300 --> 00:01:28,100
如果读不到

53
00:01:28,100 --> 00:01:29,400
那我就给它一个404

54
00:01:29,400 --> 00:01:30,900
所以说这样的话

55
00:01:30,900 --> 00:01:32,220
我们就能来统一处理了

56
00:01:32,220 --> 00:01:33,360
那么现在这样来写的话

57
00:01:33,360 --> 00:01:34,540
我们就应该这样来做

58
00:01:34,540 --> 00:01:35,300
大家就来看一下

59
00:01:35,300 --> 00:01:36,280
那么此时首先

60
00:01:36,280 --> 00:01:38,620
我们把后面的这几个else if

61
00:01:38,620 --> 00:01:39,760
我们先给它去掉

62
00:01:39,760 --> 00:01:40,540
我们先给它去掉

63
00:01:40,540 --> 00:01:41,820
我们关键的就是在这里

64
00:01:41,820 --> 00:01:42,820
我们在这里

65
00:01:42,820 --> 00:01:43,780
不再让它来写

66
00:01:43,780 --> 00:01:46,200
如果url是写死的这种

67
00:01:46,200 --> 00:01:47,000
就是路线了

68
00:01:47,000 --> 00:01:48,580
我们让它url点

69
00:01:48,580 --> 00:01:49,880
star with

70
00:01:49,880 --> 00:01:51,980
也就是说

71
00:01:51,980 --> 00:01:53,140
如果这个url是

72
00:01:53,140 --> 00:01:54,560
它开头

73
00:01:54,560 --> 00:01:55,320
也就是说

74
00:01:55,320 --> 00:01:57,840
如果这个URL以-assize-开头

75
00:01:57,840 --> 00:01:59,580
那为什么后面要加这个-呢

76
00:01:59,580 --> 00:02:01,120
也就是说如果它是assize

77
00:02:01,120 --> 00:02:02,020
如果它是这样的路径

78
00:02:02,020 --> 00:02:02,460
我们不管

79
00:02:02,460 --> 00:02:03,660
我们只是关心什么的

80
00:02:03,660 --> 00:02:04,600
我们只关心这种就是

81
00:02:04,600 --> 00:02:07,000
以-assize-开头的这样的路径

82
00:02:07,000 --> 00:02:08,200
那么后面是什么

83
00:02:08,200 --> 00:02:08,800
我们不关心

84
00:02:08,800 --> 00:02:10,080
这就是如果以它开头

85
00:02:10,080 --> 00:02:11,500
如果以它开头

86
00:02:11,500 --> 00:02:12,100
那么此时

87
00:02:12,100 --> 00:02:12,800
注意

88
00:02:12,800 --> 00:02:14,740
那我们就可以在这来readfile

89
00:02:14,740 --> 00:02:16,180
直接来尝试读这个文件

90
00:02:16,180 --> 00:02:17,420
那么读这个文件的话

91
00:02:17,420 --> 00:02:18,400
我们在这就可以这样来

92
00:02:18,400 --> 00:02:20,440
我们让它进行一个字符串拼接

93
00:02:20,440 --> 00:02:21,860
那么字符串拼接的话

94
00:02:21,860 --> 00:02:23,100
就是需要这样来写

95
00:02:23,100 --> 00:02:25,300
那么中间这一头

96
00:02:25,300 --> 00:02:26,380
我们就可以给它去掉

97
00:02:26,380 --> 00:02:27,860
那么这里就是我们的URL

98
00:02:27,860 --> 00:02:28,740
因为此时的话

99
00:02:28,740 --> 00:02:30,980
就是相当于我们在读取这个点

100
00:02:30,980 --> 00:02:31,860
点什么呢

101
00:02:31,860 --> 00:02:32,620
然后跟上

102
00:02:32,620 --> 00:02:36,300
例如我的请求路径是-assess-js-慢点js

103
00:02:36,300 --> 00:02:37,980
那么此时我们注意

104
00:02:37,980 --> 00:02:39,060
你读取这个路径的时候

105
00:02:39,060 --> 00:02:40,740
要么你把这个前面这个杠给它删掉

106
00:02:40,740 --> 00:02:41,560
因为这样的话

107
00:02:41,560 --> 00:02:43,020
你读文件它才是一个相对路径

108
00:02:43,020 --> 00:02:44,500
如果说前面是以杠开头

109
00:02:44,500 --> 00:02:45,460
那么它是一个绝对路径

110
00:02:45,460 --> 00:02:47,720
所以说要么你在前面给它加一个点

111
00:02:47,720 --> 00:02:49,500
要么你在这里把这个杠给它去掉

112
00:02:49,500 --> 00:02:50,560
所以说都可以

113
00:02:50,560 --> 00:02:51,500
例如我这里呢

114
00:02:51,500 --> 00:02:53,500
我就是给它往前面加一个点

115
00:02:53,500 --> 00:02:54,280
那么这样的话

116
00:02:54,280 --> 00:02:56,440
就把这个路径的变成了一个相对路径

117
00:02:56,440 --> 00:02:57,140
好

118
00:02:57,140 --> 00:02:57,920
那么这样写好以后

119
00:02:57,920 --> 00:02:58,300
也就是说

120
00:02:58,300 --> 00:02:59,080
如果你是他

121
00:02:59,080 --> 00:03:00,980
那么我在这里就直接来读取这个文件

122
00:03:00,980 --> 00:03:02,520
然后有error有data

123
00:03:02,520 --> 00:03:03,120
好

124
00:03:03,120 --> 00:03:03,440
但是呢

125
00:03:03,440 --> 00:03:05,140
我们注意这个文件没有问题

126
00:03:05,140 --> 00:03:06,120
但是这里有一个小问题

127
00:03:06,120 --> 00:03:06,860
就是在于什么呢

128
00:03:06,860 --> 00:03:07,740
就是在于这个就是

129
00:03:07,740 --> 00:03:09,340
content type在这里有问题

130
00:03:09,340 --> 00:03:09,880
也就是说

131
00:03:09,880 --> 00:03:11,180
我们这content type到底写什么

132
00:03:11,180 --> 00:03:13,580
就是它可能是一个什么呢

133
00:03:13,580 --> 00:03:14,280
它可能是图片

134
00:03:14,280 --> 00:03:15,280
它可能是css

135
00:03:15,280 --> 00:03:16,260
它可能还是js

136
00:03:16,260 --> 00:03:17,580
那么在这里怎么来写

137
00:03:17,580 --> 00:03:18,880
所以它就是一个问题了

138
00:03:18,880 --> 00:03:21,580
那这个问题该怎么来做呢

139
00:03:21,580 --> 00:03:23,140
其实主要就是我们来看一下

140
00:03:23,140 --> 00:03:23,760
也就是说

141
00:03:23,760 --> 00:03:25,120
我们实际上有一个

142
00:03:25,120 --> 00:03:26,980
叫做http content type

143
00:03:26,980 --> 00:03:28,460
它的一个文件对照表

144
00:03:28,460 --> 00:03:29,240
也就是说

145
00:03:29,240 --> 00:03:30,600
实际上我们有一个所谓的规范

146
00:03:30,600 --> 00:03:31,280
也就是说

147
00:03:31,280 --> 00:03:33,080
这个ds文件的content type

148
00:03:33,080 --> 00:03:34,420
css文件的content type

149
00:03:34,420 --> 00:03:36,000
包括atmr文件的content type

150
00:03:36,000 --> 00:03:37,680
它都有的对应关系

151
00:03:37,680 --> 00:03:39,240
另外我们在这里可以查看

152
00:03:39,240 --> 00:03:40,160
我们这个OS加呢

153
00:03:40,160 --> 00:03:40,940
所提供的一个就是

154
00:03:40,940 --> 00:03:42,960
content type一个产用的对照表

155
00:03:42,960 --> 00:03:44,480
那么在这我们可以来看一下

156
00:03:44,480 --> 00:03:46,700
这些就是不同的文件类型

157
00:03:46,700 --> 00:03:47,940
所对应的content type

158
00:03:47,940 --> 00:03:50,160
那么它们都是以这个文件扩展名

159
00:03:50,160 --> 00:03:51,940
来有一个具体的对应关系

160
00:03:51,940 --> 00:03:53,820
流点AVI的就是video-AVI

161
00:03:53,820 --> 00:03:56,160
包括这个流我们来搜索一下

162
00:03:56,160 --> 00:03:57,780
这个就是点PNG

163
00:03:57,780 --> 00:04:00,320
那么点PNG对应的就是image-PNG

164
00:04:00,320 --> 00:04:02,920
或者是application-X-PNG都可以

165
00:04:02,920 --> 00:04:05,360
那么包括我们这个流点js

166
00:04:05,360 --> 00:04:08,460
那么这个点js就是application-X-JavaSport

167
00:04:08,460 --> 00:04:11,200
其实或者text text-JavaSport也可以

168
00:04:11,200 --> 00:04:13,700
包括我们这个流点ATMR

169
00:04:13,700 --> 00:04:15,040
也有textATMR

170
00:04:15,040 --> 00:04:17,800
或者是点CSS就是text-CSS

171
00:04:17,800 --> 00:04:20,240
所以说这里就有一个常用的对照表

172
00:04:20,240 --> 00:04:21,780
那这里就有问题就是说

173
00:04:21,780 --> 00:04:22,680
难道说我们自己

174
00:04:22,680 --> 00:04:23,840
那我们怎么就是说

175
00:04:23,840 --> 00:04:26,020
我们在这里既然你要来统一处理这个资源

176
00:04:26,020 --> 00:04:27,620
那也就是说你需要根据

177
00:04:27,620 --> 00:04:29,660
需要根据这个文件的后对面来处理

178
00:04:29,660 --> 00:04:31,300
例如文件后对面是这个

179
00:04:31,300 --> 00:04:31,900
例如点A

180
00:04:31,900 --> 00:04:33,180
那么我们就要根据这个对照表

181
00:04:33,180 --> 00:04:35,000
就是找到这个点A所对应的counter type

182
00:04:35,000 --> 00:04:36,260
那这样就麻烦了

183
00:04:36,260 --> 00:04:37,080
我们自己来找这个麻烦

184
00:04:37,080 --> 00:04:39,040
也就是说如果说我们能有这么一个字典

185
00:04:39,040 --> 00:04:40,700
然后让我们去进行查找

186
00:04:40,700 --> 00:04:41,800
那就非常方便了

187
00:04:41,800 --> 00:04:42,380
那这里注意

188
00:04:42,380 --> 00:04:43,740
我们也不需要去找这样的东西

189
00:04:43,740 --> 00:04:44,600
当然简单一点

190
00:04:44,600 --> 00:04:46,160
你可以自己去做这么一个字典

191
00:04:46,160 --> 00:04:46,500
也可以

192
00:04:46,500 --> 00:04:47,960
但是这里的话呢

193
00:04:47,960 --> 00:04:48,880
我们可以来使用一个

194
00:04:48,880 --> 00:04:50,620
就是在Node中提供的一个第三方包

195
00:04:50,620 --> 00:04:51,840
叫做NodeMem

196
00:04:51,840 --> 00:04:53,640
那么这个包它的一个作用

197
00:04:53,640 --> 00:04:55,840
它呢就能帮我们去

198
00:04:55,840 --> 00:04:57,740
给定一个文件扩展棉

199
00:04:57,740 --> 00:05:00,520
然后返回它对应的CounterType

200
00:05:00,520 --> 00:05:01,020
也就是说

201
00:05:01,020 --> 00:05:01,880
实际上它内部呢

202
00:05:01,880 --> 00:05:03,200
就是说它内部呢

203
00:05:03,200 --> 00:05:03,940
就提供了这么一个

204
00:05:03,940 --> 00:05:05,280
就是CounterType一个对照表

205
00:05:05,280 --> 00:05:06,320
也就是说你告诉它

206
00:05:06,320 --> 00:05:06,960
扩展棉是什么

207
00:05:06,960 --> 00:05:08,420
它给你返回这个对应的

208
00:05:08,420 --> 00:05:09,120
就是这个CounterType

209
00:05:09,120 --> 00:05:09,920
这就是链

210
00:05:09,920 --> 00:05:11,160
那么这样的话

211
00:05:11,160 --> 00:05:12,100
那接下来就方便很多了

212
00:05:12,100 --> 00:05:13,040
我们就可以来使用这个包

213
00:05:13,040 --> 00:05:14,200
来解决这个CounterType的问题

214
00:05:14,200 --> 00:05:15,700
所以我们在这呢

215
00:05:15,700 --> 00:05:16,960
就打开我们的命令行

216
00:05:16,960 --> 00:05:17,960
我们在这像它一样

217
00:05:17,960 --> 00:05:19,020
我们来npm install

218
00:05:19,020 --> 00:05:21,140
我们把这个包来给它装进来

219
00:05:21,140 --> 00:05:22,480
回到命连栏档中

220
00:05:22,480 --> 00:05:24,400
我们带着c把原来的服务先给它关掉

221
00:05:24,400 --> 00:05:27,080
好 然后这个时候我们来node install

222
00:05:27,080 --> 00:05:29,220
好 这个包名我们刚才也看到了

223
00:05:29,220 --> 00:05:29,920
就叫做man

224
00:05:29,920 --> 00:05:31,640
我们把它安装一下

225
00:05:31,640 --> 00:05:33,200
我们看到这里呢

226
00:05:33,200 --> 00:05:34,140
报了一个错

227
00:05:34,140 --> 00:05:36,900
应该是npm install

228
00:05:36,900 --> 00:05:37,480
man

229
00:05:37,480 --> 00:05:39,760
好了 我们把它在这给它装进来

230
00:05:39,760 --> 00:05:40,760
好 我们在这就给它装好了

231
00:05:40,760 --> 00:05:41,620
装好以后

232
00:05:41,620 --> 00:05:42,280
装好以后

233
00:05:42,280 --> 00:05:43,700
我们此时我们再来

234
00:05:43,700 --> 00:05:45,660
把我们的这个04 assess

235
00:05:45,660 --> 00:05:46,660
先给它启动起来

236
00:05:46,660 --> 00:05:47,480
好 启动起来以后

237
00:05:47,480 --> 00:05:48,760
然后回到我们的代码当中

238
00:05:48,760 --> 00:05:49,780
回到我们代码当中

239
00:05:49,780 --> 00:05:51,860
我们可以像这里一样来使用这个包

240
00:05:51,860 --> 00:05:53,520
首先来把这个包给它引进来

241
00:05:53,520 --> 00:05:56,140
然后调用这个包的一个方法叫做get type

242
00:05:56,140 --> 00:05:57,060
给它一个扩展名

243
00:05:57,060 --> 00:05:59,580
然后它给你返回这个对应的get type

244
00:05:59,580 --> 00:06:00,300
好了

245
00:06:00,300 --> 00:06:02,240
那么这个时候我们回到我们的便机器当中

246
00:06:02,240 --> 00:06:02,860
好

247
00:06:02,860 --> 00:06:03,640
我们在这里首先

248
00:06:03,640 --> 00:06:06,140
我们来加载这个包

249
00:06:06,140 --> 00:06:07,280
好

250
00:06:07,280 --> 00:06:07,920
加载进来以后

251
00:06:07,920 --> 00:06:08,940
然后往下来

252
00:06:08,940 --> 00:06:09,140
好

253
00:06:09,140 --> 00:06:10,360
往下来我们接下来就要在这里

254
00:06:10,360 --> 00:06:12,960
我们可以在这里来单独的来处理一下

255
00:06:12,960 --> 00:06:14,920
也就是这个meme.get type

256
00:06:14,920 --> 00:06:16,060
那么这里要给一个什么呢

257
00:06:16,060 --> 00:06:16,220
注意

258
00:06:16,220 --> 00:06:18,940
这里是不是要给一个文件的扩展名啊

259
00:06:18,940 --> 00:06:20,680
文件的扩展名怎么得到呢

260
00:06:20,680 --> 00:06:22,460
注意我们在这里可以使用

261
00:06:22,460 --> 00:06:25,320
NodeGIS当中为我们提供的这个核心模块PASS

262
00:06:25,320 --> 00:06:26,580
PASS

263
00:06:26,580 --> 00:06:28,060
好那么这个PASS的话呢

264
00:06:28,060 --> 00:06:30,240
它就可以帮我们来得到一个扩展名

265
00:06:30,240 --> 00:06:31,540
所以说我们在这里就比如这

266
00:06:31,540 --> 00:06:33,360
在这PASS.extname

267
00:06:33,360 --> 00:06:35,960
就是核心模块PASS有个方法叫做extname

268
00:06:35,960 --> 00:06:37,320
就是说你给它一个路径

269
00:06:37,320 --> 00:06:38,320
例如我给它一个UR了

270
00:06:38,320 --> 00:06:40,840
那么它能帮我得到在这个路径当中

271
00:06:40,840 --> 00:06:43,380
在这个路径当中的就是扩展名部分

272
00:06:43,380 --> 00:06:46,220
例如我们可以在这里快速给大家来演示一下

273
00:06:46,220 --> 00:06:48,300
我进入Node的IETL环境

274
00:06:48,300 --> 00:06:50,400
例如我在这里直接passextname

275
00:06:50,400 --> 00:06:52,460
例如我在这里输入一个路径

276
00:06:52,460 --> 00:06:53,800
例如我在这里输入一个就是

277
00:06:53,800 --> 00:06:56,060
-1-B-C-2

278
00:06:56,060 --> 00:06:58,020
然后是abc.js

279
00:06:58,020 --> 00:06:59,560
那么在这我们大家就看到

280
00:06:59,560 --> 00:07:00,940
是不是得到了这个叫.js

281
00:07:00,940 --> 00:07:02,240
是不是扩展名啊

282
00:07:02,240 --> 00:07:03,400
那么这样没有问题的话

283
00:07:03,400 --> 00:07:05,460
那么此时这个方法就得到了这个扩展名

284
00:07:05,460 --> 00:07:06,320
得到以后

285
00:07:06,320 --> 00:07:08,540
那么然后再交给这个gettype的方法来使用

286
00:07:08,540 --> 00:07:09,560
那么它就根据扩展名

287
00:07:09,560 --> 00:07:11,400
是得到了它对应的contenttype的脸

288
00:07:11,400 --> 00:07:13,900
所以说接下来我们在这就可以单独来写一写

289
00:07:13,900 --> 00:07:15,400
content tab

290
00:07:15,400 --> 00:07:17,420
那么在这里写好以后

291
00:07:17,420 --> 00:07:20,840
然后接下来我们就把这个text-css

292
00:07:20,840 --> 00:07:23,240
把这段文本来稍微的把它改一改

293
00:07:23,240 --> 00:07:24,340
当然后面这个charset

294
00:07:24,340 --> 00:07:26,020
我们就不要直接给它去掉了

295
00:07:26,020 --> 00:07:27,100
也就是说这一部分直接去掉

296
00:07:27,100 --> 00:07:27,820
变成什么呢

297
00:07:27,820 --> 00:07:29,660
我们在这得到的这个conton tab

298
00:07:29,660 --> 00:07:30,940
那么这样的话

299
00:07:30,940 --> 00:07:32,620
我们再就给它统一的处理好了

300
00:07:32,620 --> 00:07:34,140
当然不要忘了一件事

301
00:07:34,140 --> 00:07:35,820
就是说我们在读取这个文件的时候

302
00:07:35,820 --> 00:07:36,660
如果读取失败

303
00:07:36,660 --> 00:07:37,380
那大家想想

304
00:07:37,380 --> 00:07:39,300
那是不是就证明这个文件没有读到

305
00:07:39,300 --> 00:07:40,960
就是说它以-asset开头

306
00:07:40,960 --> 00:07:41,560
但是呢

307
00:07:41,560 --> 00:07:43,220
并没有assize里面的这个对应的资源

308
00:07:43,220 --> 00:07:44,100
例如他在这写一个

309
00:07:44,100 --> 00:07:45,160
-assize-a.js

310
00:07:45,160 --> 00:07:46,760
那么assize里面并没有这个a.js

311
00:07:46,760 --> 00:07:48,260
所以他就会引起这个if error报错

312
00:07:48,260 --> 00:07:49,920
那么我们应该在这个through error

313
00:07:49,920 --> 00:07:50,520
我们在这里

314
00:07:50,520 --> 00:07:52,900
其实也应该像处理这个404一样

315
00:07:52,900 --> 00:07:54,180
来处理这个资源

316
00:07:54,180 --> 00:07:56,100
所以说我们在这里也应该这么来写

317
00:07:56,100 --> 00:07:57,280
我们大家可以来看一下

318
00:07:57,280 --> 00:07:58,260
就是state code的404

319
00:07:58,260 --> 00:08:00,060
因为此时我们尝试去读记管件读不到

320
00:08:00,060 --> 00:08:00,740
那读不到就报错

321
00:08:00,740 --> 00:08:02,040
那报错的话就证明它不存在

322
00:08:02,040 --> 00:08:02,460
不存在

323
00:08:02,460 --> 00:08:04,320
所以说这里给个404是最合适的

324
00:08:04,320 --> 00:08:05,640
原来是那其他这儿不有个404吗

325
00:08:05,640 --> 00:08:06,800
注意这俩它不一样

326
00:08:06,800 --> 00:08:07,480
不一样

327
00:08:07,480 --> 00:08:08,980
就这个404是针对的是什么呢

328
00:08:08,980 --> 00:08:11,200
就针对的是非assize处理的404的

329
00:08:11,200 --> 00:08:12,300
而这里的是针对的是

330
00:08:12,300 --> 00:08:13,900
就是它起动中间以assize开头

331
00:08:13,900 --> 00:08:15,060
但是assize里面并没有这个资源

332
00:08:15,060 --> 00:08:16,700
那是针对它的一个404处理

333
00:08:16,700 --> 00:08:17,680
虽然结果是一样的

334
00:08:17,680 --> 00:08:18,300
但这个逻辑

335
00:08:18,300 --> 00:08:19,320
注意它不能去公用

336
00:08:19,320 --> 00:08:20,440
必须分开来写

337
00:08:20,440 --> 00:08:21,820
必须这么来做

338
00:08:21,820 --> 00:08:21,980
好了

339
00:08:21,980 --> 00:08:23,500
那我们在这里写好以后

340
00:08:23,500 --> 00:08:25,600
然后接下来回到你的这个轮览器当中

341
00:08:25,600 --> 00:08:27,080
好我们此时我们再来测试一下

342
00:08:27,080 --> 00:08:28,420
对我们在这里刷新一下

343
00:08:28,420 --> 00:08:30,840
我们的服务呢没有启动起来

344
00:08:30,840 --> 00:08:32,120
好回到这里

345
00:08:32,120 --> 00:08:33,640
我们先退出我们刚才的这个

346
00:08:33,640 --> 00:08:35,200
Node中的REPR模式

347
00:08:35,200 --> 00:08:36,120
然后现在我们在这里node mode

348
00:08:36,120 --> 00:08:37,700
然后04assass.js

349
00:08:37,700 --> 00:08:38,820
好 启动成功以后

350
00:08:38,820 --> 00:08:39,980
回到你的浏览器当中

351
00:08:39,980 --> 00:08:41,380
我们在这里刷新一下

352
00:08:41,380 --> 00:08:42,900
好 那么此时大家就可以看到

353
00:08:42,900 --> 00:08:44,140
我们在这个浏览器当中

354
00:08:44,140 --> 00:08:45,300
是不是就正确的收到了

355
00:08:45,300 --> 00:08:46,100
我们的样式

356
00:08:46,100 --> 00:08:47,820
还有我们的脚本

357
00:08:47,820 --> 00:08:48,740
在这里是执行了这个

358
00:08:48,740 --> 00:08:49,580
输出了好了wall的

359
00:08:49,580 --> 00:08:51,060
包括我们看到这张图片

360
00:08:51,060 --> 00:08:52,840
现在是不是也被正确的输出了

361
00:08:52,840 --> 00:08:54,580
好 那么主要原因呢

362
00:08:54,580 --> 00:08:56,860
就在于我们是不是在这个代码当中

363
00:08:56,860 --> 00:08:58,600
就是对这个assass资源

364
00:08:58,600 --> 00:09:00,560
是不是给它进行了一个统一的处理

365
00:09:00,560 --> 00:09:02,000
进行了一个统一的处理

366
00:09:02,000 --> 00:09:04,260
那对于其他的资源

367
00:09:04,260 --> 00:09:05,180
例如这个什么index

368
00:09:05,180 --> 00:09:05,820
这些东西呢

369
00:09:05,820 --> 00:09:07,140
我们还是要单独来做

370
00:09:07,140 --> 00:09:08,320
因为对于我们的网页

371
00:09:08,320 --> 00:09:09,400
我们要单独去处理

372
00:09:09,400 --> 00:09:10,160
例如如果说

373
00:09:10,160 --> 00:09:11,640
你要进行这个动态网页之类的

374
00:09:11,640 --> 00:09:13,540
那你就不能让网页的处理

375
00:09:13,540 --> 00:09:15,180
和这个静态资源的处理混在一起

376
00:09:15,180 --> 00:09:15,860
因为这样的话

377
00:09:15,860 --> 00:09:17,060
你就不好去单独去做了

378
00:09:17,060 --> 00:09:17,800
因为静态资源

379
00:09:17,800 --> 00:09:19,480
它没有任何额外其他的逻辑

380
00:09:19,480 --> 00:09:20,520
它就是纯粹的

381
00:09:20,520 --> 00:09:21,620
把这个文件读出来

382
00:09:21,620 --> 00:09:22,700
然后发送给客户端

383
00:09:22,700 --> 00:09:23,700
中间可以说

384
00:09:23,700 --> 00:09:25,000
不需要做任何处理

385
00:09:25,000 --> 00:09:26,320
所以说他们可以在这去

386
00:09:26,320 --> 00:09:27,500
公共的统一来处理

387
00:09:27,500 --> 00:09:29,140
那么前提条件是一定要什么呢

388
00:09:29,140 --> 00:09:30,320
就是将你的静态资源

389
00:09:30,320 --> 00:09:32,580
去统一的保存到某个目录当中

390
00:09:32,580 --> 00:09:33,480
因为我这里就让它

391
00:09:33,480 --> 00:09:35,480
统一的放到这个Assise目录当中

392
00:09:35,480 --> 00:09:36,760
而你的网页注意

393
00:09:36,760 --> 00:09:39,400
它不能和这些资源来混合来使用

394
00:09:39,400 --> 00:09:40,540
千万不要让它来混合来使用

395
00:09:40,540 --> 00:09:41,060
因为我说

396
00:09:41,060 --> 00:09:43,520
我们的网页不是简简单单的静态文件

397
00:09:43,520 --> 00:09:45,380
我们将来还要让它变得动态起来

398
00:09:45,380 --> 00:09:48,480
所以说它还需要在这单独的来进行编写

