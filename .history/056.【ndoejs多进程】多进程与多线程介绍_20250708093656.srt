1
00:00:00,260 --> 00:00:02,860
好,我们这期课就来讲解nodejs多进程

2
00:00:02,860 --> 00:00:05,360
那么这一章老师会分为四个部分去讲

3
00:00:05,360 --> 00:00:07,760
首先,我们为什么要使用多进程

4
00:00:07,760 --> 00:00:11,500
既然需要多进程,那么一定离不开一个关键字

5
00:00:11,500 --> 00:00:13,320
多进程,它们有什么区别

6
00:00:13,320 --> 00:00:16,580
然后在nodejs里面,怎么样去开启多进程和多进程

7
00:00:16,580 --> 00:00:20,200
以及Cluster,它是nodejs开启多进程的一个核心

8
00:00:20,200 --> 00:00:21,320
它的一个原理是什么

9
00:00:21,320 --> 00:00:24,300
这里就是本章老师要介绍的内容

10
00:00:24,300 --> 00:00:26,860
那么这一节课呢,老师主要会讲解

11
00:00:26,860 --> 00:00:28,360
我们为什么要使用多进程

12
00:00:28,360 --> 00:00:30,560
以及多进程和多线程它的一个区别

13
00:00:30,560 --> 00:00:31,340
好

14
00:00:31,340 --> 00:00:32,200
首先我们来看一下

15
00:00:32,200 --> 00:00:33,140
为什么要使用多进程

16
00:00:33,140 --> 00:00:35,200
之前老师有没有讲过

17
00:00:35,200 --> 00:00:36,260
GS是一门什么样的语言

18
00:00:36,260 --> 00:00:37,120
是不是单线程

19
00:00:37,120 --> 00:00:37,540
为什么

20
00:00:37,540 --> 00:00:39,400
因为GS它来源于浏览器

21
00:00:39,400 --> 00:00:41,680
我们之前在讲解浏览器里面

22
00:00:41,680 --> 00:00:42,360
受限循环的时候

23
00:00:42,360 --> 00:00:42,900
是不是解释过

24
00:00:42,900 --> 00:00:44,120
因为你操作一个动物的时候

25
00:00:44,120 --> 00:00:45,500
如果说你是多线程的去操作

26
00:00:45,500 --> 00:00:46,780
那么浏览器它不知道

27
00:00:46,780 --> 00:00:48,600
你到底是要双除动物还是添加动物

28
00:00:48,600 --> 00:00:50,500
那么在loadGS里面存不存在这样的问题了

29
00:00:50,500 --> 00:00:51,540
loadGS里面是没有动物的

30
00:00:51,540 --> 00:00:52,580
这是第一个原因

31
00:00:52,580 --> 00:00:53,780
那么第二个呢

32
00:00:53,780 --> 00:00:55,060
更加核心的原因是什么

33
00:00:55,060 --> 00:00:57,020
就是因为在处理http请求的时候

34
00:00:57,020 --> 00:00:59,480
那么咱们的服务端处理http请求

35
00:00:59,480 --> 00:01:00,800
是不是数量会非常庞大呀

36
00:01:00,800 --> 00:01:02,080
有可能有几千万条

37
00:01:02,080 --> 00:01:02,820
几万条

38
00:01:02,820 --> 00:01:03,300
几百万条

39
00:01:03,300 --> 00:01:04,320
甚至上一条都有可能

40
00:01:04,320 --> 00:01:05,560
那么你只要有一条

41
00:01:05,560 --> 00:01:06,640
客户端的请求过来

42
00:01:06,640 --> 00:01:07,660
你服务器发生了错误

43
00:01:07,660 --> 00:01:08,860
如果GS是单线程的

44
00:01:08,860 --> 00:01:09,760
是不是就会直接导致

45
00:01:09,760 --> 00:01:10,840
整个服务崩溃

46
00:01:10,840 --> 00:01:12,660
那么这是灾难级的

47
00:01:12,660 --> 00:01:13,320
所以说呢

48
00:01:13,320 --> 00:01:14,960
我们需要使用多进程

49
00:01:14,960 --> 00:01:15,900
来避免这种情况

50
00:01:15,900 --> 00:01:16,840
怎么理解

51
00:01:16,840 --> 00:01:18,500
是不是你一个进程挂掉了

52
00:01:18,500 --> 00:01:19,280
还有其他的进程

53
00:01:19,280 --> 00:01:20,240
可以为客户去服务

54
00:01:20,240 --> 00:01:20,840
所以呢

55
00:01:20,840 --> 00:01:21,820
就解决这样一个问题

56
00:01:21,820 --> 00:01:22,880
所以我们需要多进程

57
00:01:22,880 --> 00:01:23,440
好

58
00:01:23,440 --> 00:01:24,300
那么我们来看一下

59
00:01:24,300 --> 00:01:26,180
多进程它和多线程

60
00:01:26,180 --> 00:01:26,900
是什么

61
00:01:26,900 --> 00:01:27,960
它们有什么区别

62
00:01:27,960 --> 00:01:29,880
首先这里是老师从

63
00:01:29,880 --> 00:01:32,320
官方的一些介绍

64
00:01:32,320 --> 00:01:33,980
以及网上老师搜索的一些文章

65
00:01:33,980 --> 00:01:35,240
它里面去摘出来的一些解释

66
00:01:35,240 --> 00:01:37,500
进程是资源分配的最小单位

67
00:01:37,500 --> 00:01:39,280
线程是CPU调度的最小单位

68
00:01:39,280 --> 00:01:41,040
听起来是不是好像有点

69
00:01:41,040 --> 00:01:42,080
不是很好理解

70
00:01:42,080 --> 00:01:44,200
进程是资源分配的最小单位

71
00:01:44,200 --> 00:01:44,900
我分配的是谁

72
00:01:44,900 --> 00:01:47,320
线程是CPU调度的最小单位

73
00:01:47,320 --> 00:01:48,340
那么CPU大家都知道

74
00:01:48,340 --> 00:01:49,480
比如说AMD

75
00:01:49,480 --> 00:01:51,080
Intel他们都是产CPU的厂商

76
00:01:51,080 --> 00:01:52,540
那么CPU调度的最小单位

77
00:01:52,540 --> 00:01:53,600
那我怎么知道是什么

78
00:01:53,600 --> 00:01:54,200
对吧

79
00:01:54,200 --> 00:01:56,120
好 大家看一下有没有其他的解释

80
00:01:56,120 --> 00:01:58,020
进程 资源分配的最小单位

81
00:01:58,020 --> 00:02:00,360
线程 程序执行的最小单位

82
00:02:00,360 --> 00:02:02,440
好像还是不是很好理解

83
00:02:02,440 --> 00:02:03,100
没关系

84
00:02:03,100 --> 00:02:05,260
我们再来看一下他们更加具体的介绍

85
00:02:05,260 --> 00:02:06,860
线程是进程的一个执行器

86
00:02:06,860 --> 00:02:08,780
是CPU调度和分配的基本单位

87
00:02:08,780 --> 00:02:11,000
它是比进程更小人独立运行的基本单位

88
00:02:11,000 --> 00:02:12,860
一个进程有几个线程组成

89
00:02:12,860 --> 00:02:14,500
线程与同属一个进程的其他线程

90
00:02:14,500 --> 00:02:16,280
共享进程所拥有的全部资源

91
00:02:16,280 --> 00:02:18,560
好,这里呢,其实我们稍微有一个概念了

92
00:02:18,560 --> 00:02:19,820
是什么意思呢?就是说

93
00:02:19,820 --> 00:02:22,300
咱们关键看这一句话

94
00:02:22,300 --> 00:02:24,660
一个进程有几个线程组成

95
00:02:24,660 --> 00:02:26,960
说明了咱们一个程序

96
00:02:26,960 --> 00:02:28,980
它的一个进程有很多线程组成

97
00:02:28,980 --> 00:02:31,100
然后呢,一个进程下的这些线程

98
00:02:31,100 --> 00:02:33,440
它们之间是可以共享一些资源的

99
00:02:33,440 --> 00:02:35,320
什么意思?是不是就是它们之间可以去通信呢?

100
00:02:35,320 --> 00:02:37,580
也就是说一个进程下面的线程是可以去通信的

101
00:02:37,580 --> 00:02:44,540
一个进程下面的线程是可以去

102
00:02:44,540 --> 00:02:48,640
通信的共享支援

103
00:02:48,640 --> 00:02:49,140
好

104
00:02:49,140 --> 00:02:50,560
那我们来看一下进城的解释

105
00:02:50,560 --> 00:02:52,560
进城有独立的地质空间

106
00:02:52,560 --> 00:02:53,660
一个进城崩溃后

107
00:02:53,660 --> 00:02:54,440
在保护模糊下

108
00:02:54,440 --> 00:02:56,300
不会对其他进城产生影响

109
00:02:56,300 --> 00:02:57,540
而县城只是一个进城中的

110
00:02:57,540 --> 00:02:58,280
不同执行路径

111
00:02:58,280 --> 00:02:59,940
县城有自己的堆胀和局部变量

112
00:02:59,940 --> 00:03:01,480
但县城没有单独的地质空间

113
00:03:01,480 --> 00:03:02,180
一个县城死掉

114
00:03:02,180 --> 00:03:03,440
就等于整个进城死掉

115
00:03:03,440 --> 00:03:05,920
咱们关键看什么

116
00:03:05,920 --> 00:03:07,240
是不是进城它有独立的县城

117
00:03:07,240 --> 00:03:08,580
独立的地质空间

118
00:03:08,580 --> 00:03:09,740
那么一个县城死掉

119
00:03:09,740 --> 00:03:11,060
就会让整个进城死掉

120
00:03:11,060 --> 00:03:11,660
好

121
00:03:11,660 --> 00:03:13,240
那么这里是县城和进城

122
00:03:13,240 --> 00:03:14,000
它的一个概念

123
00:03:14,000 --> 00:03:15,040
那么我们具体来看一下

124
00:03:15,040 --> 00:03:17,320
在咱们的应用里面

125
00:03:17,320 --> 00:03:18,200
实际的生活中

126
00:03:18,200 --> 00:03:18,940
到底什么是现成

127
00:03:18,940 --> 00:03:19,480
什么是进程

128
00:03:19,480 --> 00:03:20,320
那么老师这里呢

129
00:03:20,320 --> 00:03:21,180
就拿谷歌浏览器

130
00:03:21,180 --> 00:03:22,100
来看一下

131
00:03:22,100 --> 00:03:23,660
我们怎么样去看呢

132
00:03:23,660 --> 00:03:25,700
首先我们来看一下

133
00:03:25,700 --> 00:03:26,400
我们的浏览器

134
00:03:26,400 --> 00:03:28,340
那么老师教大家一个技巧

135
00:03:28,340 --> 00:03:29,200
我们怎么样去看

136
00:03:29,200 --> 00:03:30,880
程序它有多少个进程

137
00:03:30,880 --> 00:03:31,820
在Mac里面

138
00:03:31,820 --> 00:03:33,120
我们看活动监视器

139
00:03:33,120 --> 00:03:34,420
搜索

140
00:03:34,420 --> 00:03:36,240
Chrome

141
00:03:36,240 --> 00:03:37,580
大家看到没有

142
00:03:37,580 --> 00:03:38,460
进程名称

143
00:03:38,460 --> 00:03:39,700
是不是咱们的Chrome下面

144
00:03:39,700 --> 00:03:41,040
就有很多的进程

145
00:03:41,040 --> 00:03:42,940
一二三四五六七八

146
00:03:42,940 --> 00:03:43,340
8个

147
00:03:43,340 --> 00:03:44,340
这块呢

148
00:03:44,340 --> 00:03:45,580
全部都是谷歌浏览器

149
00:03:45,580 --> 00:03:46,580
它提供了一些进程

150
00:03:46,580 --> 00:03:47,380
包括呢

151
00:03:47,380 --> 00:03:48,080
它每一个进程里面

152
00:03:48,080 --> 00:03:48,580
多少个线程

153
00:03:48,580 --> 00:03:50,160
4个8个10个13个等等

154
00:03:50,160 --> 00:03:50,620
好

155
00:03:50,620 --> 00:03:52,060
那么在浏览器里面

156
00:03:52,060 --> 00:03:53,080
它到底什么样是一个

157
00:03:53,080 --> 00:03:55,740
什么样是一个进程呢

158
00:03:55,740 --> 00:03:56,700
不知道大家注意到没有

159
00:03:56,700 --> 00:04:00,240
不知道大家注意到没有

160
00:04:00,240 --> 00:04:01,380
咱们浏览器

161
00:04:01,380 --> 00:04:02,220
是不是只开了一个页面

162
00:04:02,220 --> 00:04:02,680
但是呢

163
00:04:02,680 --> 00:04:03,740
它的进程有8个

164
00:04:03,740 --> 00:04:06,560
这两老师来做一个实验

165
00:04:06,560 --> 00:04:07,500
咱们来加一些碳

166
00:04:07,500 --> 00:04:11,640
看到没有

167
00:04:11,640 --> 00:04:12,220
大家看到没有

168
00:04:12,220 --> 00:04:13,820
咱们的进程是不是明显的增多了

169
00:04:13,820 --> 00:04:15,120
说明什么问题

170
00:04:15,120 --> 00:04:17,600
说明咱们开启一个新的窗口的时候

171
00:04:17,600 --> 00:04:18,720
在咱们浏览器里面

172
00:04:18,720 --> 00:04:21,040
它就会去添加一些新的进程

173
00:04:21,040 --> 00:04:23,780
好 把这些关闭

174
00:04:23,780 --> 00:04:25,160
咱们看是不是就减少了

175
00:04:25,160 --> 00:04:25,940
说明什么问题

176
00:04:25,940 --> 00:04:29,420
说明在浏览器里面

177
00:04:29,420 --> 00:04:30,520
一个type就是一个进程

178
00:04:30,520 --> 00:04:32,340
那么线程是什么呢

179
00:04:32,340 --> 00:04:33,080
线程是什么

180
00:04:33,080 --> 00:04:34,620
刚才是不是解释过了

181
00:04:34,620 --> 00:04:35,620
咱们一个进程里面

182
00:04:35,620 --> 00:04:36,360
是不是有很多的线程

183
00:04:36,360 --> 00:04:38,740
好

184
00:04:38,740 --> 00:04:41,680
那么一个type有很多的线程组成

185
00:04:41,680 --> 00:04:43,200
那么这些线程它是做什么的呀

186
00:04:43,200 --> 00:04:44,600
线程咱们在GS里面

187
00:04:44,600 --> 00:04:45,880
是不是GS有个执行线程

188
00:04:45,880 --> 00:04:47,440
然后呢还有service worker

189
00:04:47,440 --> 00:04:49,080
如果说咱们在前面的学习

190
00:04:49,080 --> 00:04:50,700
或者说同学们注意到前段的一个发展

191
00:04:50,700 --> 00:04:52,460
service worker也是浏览器单独提供一个

192
00:04:52,460 --> 00:04:54,040
线程包括垃圾回收

193
00:04:54,040 --> 00:04:55,240
包括浏览界的渲染线程

194
00:04:55,240 --> 00:04:55,920
比如说CSS

195
00:04:55,920 --> 00:04:57,060
DOM等等

196
00:04:57,060 --> 00:04:58,320
这里呢

197
00:04:58,320 --> 00:04:59,120
他们这些

198
00:04:59,120 --> 00:05:00,980
线程就组合成了浏览器的一个窗口

199
00:05:00,980 --> 00:05:01,920
比如说咱们一个窗口

200
00:05:01,920 --> 00:05:03,440
是不是有了GS去GS执行线程

201
00:05:03,440 --> 00:05:04,320
有了咱们的渲染线程

202
00:05:04,320 --> 00:05:06,080
那么我们页面是不是就可以渲染出来了呀

203
00:05:06,080 --> 00:05:06,720
这里呢

204
00:05:06,720 --> 00:05:09,020
就是进程和线程的一个关系在浏览器里面

205
00:05:09,020 --> 00:05:10,220
那么咱们再来看一下

206
00:05:10,220 --> 00:05:11,660
在loadgs里面

207
00:05:11,660 --> 00:05:14,320
它的线程和进程又是什么样的一个关系

208
00:05:14,320 --> 00:05:15,520
好我们来看一下

209
00:05:15,520 --> 00:05:23,140
首先老师这里呢

210
00:05:23,140 --> 00:05:24,980
有一段loadgs的代码

211
00:05:24,980 --> 00:05:27,500
首先通过http去创造一个服务

212
00:05:27,500 --> 00:05:28,940
然后监听8000端口很简单

213
00:05:28,940 --> 00:05:31,520
比如说咱们访问8000端口就会访问一个

214
00:05:31,520 --> 00:05:31,840
好喽

215
00:05:31,840 --> 00:05:33,220
好那我们来启动看一下

216
00:05:33,220 --> 00:05:36,700
loadhttp.js

217
00:05:36,700 --> 00:05:37,420
好

218
00:05:37,420 --> 00:05:38,760
咱们再来看

219
00:05:38,760 --> 00:05:40,040
搜索一下load

220
00:05:40,040 --> 00:05:41,360
大家看到没有

221
00:05:41,360 --> 00:05:42,140
咱们呢

222
00:05:42,140 --> 00:05:43,220
有了一个load的进程

223
00:05:43,220 --> 00:05:43,860
也就是说

224
00:05:43,860 --> 00:05:44,500
说明个什么问题

225
00:05:44,500 --> 00:05:45,780
是不是咱们一个http服务

226
00:05:45,780 --> 00:05:46,340
就是一个进程

227
00:05:46,340 --> 00:05:47,160
那么进程里面呢

228
00:05:47,160 --> 00:05:48,040
有七个线程

229
00:05:48,040 --> 00:05:49,700
那么这些线程是做什么的

230
00:05:49,700 --> 00:05:51,660
在loadgs里面

231
00:05:51,660 --> 00:05:52,800
它的线程主要有主线程

232
00:05:52,800 --> 00:05:53,820
比如说获取代码

233
00:05:53,820 --> 00:05:54,880
GS的执行

234
00:05:54,880 --> 00:05:56,460
还有编译线程

235
00:05:56,460 --> 00:05:58,220
在v8引擎里面

236
00:05:58,220 --> 00:05:59,880
咱们GS是边执行边优化的

237
00:05:59,880 --> 00:06:00,820
咱们loadgs介入呢

238
00:06:00,820 --> 00:06:01,480
就是v8引擎

239
00:06:01,480 --> 00:06:01,940
所以说呢

240
00:06:01,940 --> 00:06:02,740
它有一个编译线程

241
00:06:02,740 --> 00:06:03,420
包括profile

242
00:06:03,420 --> 00:06:05,560
不知道同学们有没有用过

243
00:06:05,560 --> 00:06:07,580
有没有用过浏览器里面的Performance

244
00:06:07,580 --> 00:06:09,160
Performance是什么呢

245
00:06:09,160 --> 00:06:10,420
就是咱们去录品用的

246
00:06:10,420 --> 00:06:12,280
可以去分析咱们页面的一些性能

247
00:06:12,280 --> 00:06:13,240
可能有些同学们用过

248
00:06:13,240 --> 00:06:14,980
这是浏览器里面的一个分析性能的工具

249
00:06:14,980 --> 00:06:16,000
那么在NoteJS里面

250
00:06:16,000 --> 00:06:17,620
Profile它也是去分析性能的

251
00:06:17,620 --> 00:06:18,400
记录哪些方法

252
00:06:18,400 --> 00:06:19,300
耗时提供优化支持

253
00:06:19,300 --> 00:06:20,200
包括一些其他的线程

254
00:06:20,200 --> 00:06:20,980
用于垃圾回收

255
00:06:20,980 --> 00:06:22,620
也就是说咱们这七个线程

256
00:06:22,620 --> 00:06:24,040
组成了这样一个NoteJS的一个进程

257
00:06:24,040 --> 00:06:25,640
去提供http的服务

258
00:06:25,640 --> 00:06:26,340
好

259
00:06:26,340 --> 00:06:27,300
那么我们再来看一下

260
00:06:27,300 --> 00:06:34,100
这里呢

261
00:06:34,100 --> 00:06:35,360
我们在

262
00:06:35,360 --> 00:06:37,260
http请求的时候

263
00:06:37,260 --> 00:06:40,920
咱们http会对咱们的服务器

264
00:06:40,920 --> 00:06:42,520
可以向其他的服务去发起一些请求

265
00:06:42,520 --> 00:06:43,500
在这里呢

266
00:06:43,500 --> 00:06:44,380
我把它改写了一下

267
00:06:44,380 --> 00:06:45,480
在咱们的服务

268
00:06:45,480 --> 00:06:46,560
接收响应的时候

269
00:06:46,560 --> 00:06:47,620
又会去请求其他的服务

270
00:06:47,620 --> 00:06:48,080
咱们来看一下

271
00:06:48,080 --> 00:06:49,380
这个时候现成会变化

272
00:06:49,380 --> 00:06:50,720
关键看这一段代码

273
00:06:50,720 --> 00:06:52,240
刚才这段代码

274
00:06:52,240 --> 00:06:53,620
是不是咱们刚才起来是没有的

275
00:06:53,620 --> 00:06:54,940
那么这里呢

276
00:06:54,940 --> 00:06:56,480
我们去请求其他服务

277
00:06:56,480 --> 00:06:58,360
我们再来我们来看一下有什么区别

278
00:06:58,360 --> 00:07:02,560
load htgpr.js

279
00:07:02,560 --> 00:07:05,820
好 这里呢 我们在8001这样一个端口起一个服务

280
00:07:05,820 --> 00:07:08,260
好 咱们再来看一下

281
00:07:08,260 --> 00:07:12,100
大家看到没有 是不是咱们load有两个进程

282
00:07:12,100 --> 00:07:13,640
是不是验证了刚才的说法

283
00:07:13,640 --> 00:07:14,700
一个服务就是一个进程

284
00:07:14,700 --> 00:07:15,880
那么呢 它们都是七个线程

285
00:07:15,880 --> 00:07:17,300
好 这里老师来做一个操作

286
00:07:17,300 --> 00:07:18,860
8001

287
00:07:18,860 --> 00:07:20,500
好 咱们再来看一下

288
00:07:20,500 --> 00:07:23,440
大家看到没有 它的线程变成了11

289
00:07:23,440 --> 00:07:24,680
说明了什么问题

290
00:07:24,680 --> 00:07:26,760
说明一个什么问题

291
00:07:26,760 --> 00:07:27,380
同学们

292
00:07:27,380 --> 00:07:30,720
说明咱们的nodejs

293
00:07:30,720 --> 00:07:33,060
它不是一开始就把所有的线程都准备好了

294
00:07:33,060 --> 00:07:34,520
只是说它需要提供服务的时候

295
00:07:34,520 --> 00:07:37,160
nodejs它在去动态的去创建线程

296
00:07:37,160 --> 00:07:38,100
那么这里呢

297
00:07:38,100 --> 00:07:40,080
就是nodejs里面进程和线程的一个关系

298
00:07:40,080 --> 00:07:40,620
好

299
00:07:40,620 --> 00:07:41,880
这里呢老师来回顾一下

300
00:07:41,880 --> 00:07:43,460
我们这几个所讲的内容

301
00:07:43,460 --> 00:07:44,700
首先我们为什么要使用多线程

302
00:07:44,700 --> 00:07:45,920
是不是在nodejs里面

303
00:07:45,920 --> 00:07:48,700
咱们http需要去提供服务

304
00:07:48,700 --> 00:07:49,780
为了咱们服务的稳定

305
00:07:49,780 --> 00:07:51,300
所以要提供多进程

306
00:07:51,300 --> 00:07:53,760
那么多进程它和多线程又有什么区别

307
00:07:53,760 --> 00:07:55,720
首先进程

308
00:07:55,720 --> 00:07:59,100
进程它是不是有独立的地质空间

309
00:07:59,100 --> 00:08:00,200
那么呢

310
00:08:00,200 --> 00:08:00,600
线程呢

311
00:08:00,600 --> 00:08:02,340
是不是多个线程组成一个进程

312
00:08:02,340 --> 00:08:03,380
那么在浏览器里面

313
00:08:03,380 --> 00:08:04,460
一个窗口就是一个进程

314
00:08:04,460 --> 00:08:05,400
那么每一个窗口

315
00:08:05,400 --> 00:08:06,620
是不是会需要有渲染线程

316
00:08:06,620 --> 00:08:07,520
GS执行线程

317
00:08:07,520 --> 00:08:08,640
垃圾回收线程

318
00:08:08,640 --> 00:08:10,320
缺一不可组成咱们一个窗口

319
00:08:10,320 --> 00:08:11,620
那么只要有一个线程崩溃了

320
00:08:11,620 --> 00:08:14,040
那么我们的浏览器就不能够去运行

321
00:08:14,040 --> 00:08:14,640
为什么

322
00:08:14,640 --> 00:08:15,860
咱们没有渲染线程

323
00:08:15,860 --> 00:08:16,900
光有GS执行线程

324
00:08:16,900 --> 00:08:17,620
我们的浏览器能不能用

325
00:08:17,620 --> 00:08:18,120
不可以吧

326
00:08:18,120 --> 00:08:20,000
那么光有渲染线程和GS执行线程

327
00:08:20,000 --> 00:08:20,940
没有垃圾回收也不行吧

328
00:08:20,940 --> 00:08:21,540
那么呢

329
00:08:21,540 --> 00:08:22,380
如果说没有垃圾回收

330
00:08:22,380 --> 00:08:23,800
我们是不是马上就会爆炸呢

331
00:08:23,800 --> 00:08:24,420
内存会溢出

332
00:08:24,420 --> 00:08:25,860
包括绿井也是同样的

333
00:08:25,860 --> 00:08:27,800
我们每一个http服务都是一个进程

334
00:08:27,800 --> 00:08:28,600
那么呢

335
00:08:28,600 --> 00:08:30,440
它的一些编译线程

336
00:08:30,440 --> 00:08:31,160
以及呢主线程

337
00:08:31,160 --> 00:08:32,200
包括它的一些优化线程

338
00:08:32,200 --> 00:08:33,140
组成了咱们一个进程

339
00:08:33,140 --> 00:08:33,380
好

340
00:08:33,380 --> 00:08:34,860
这里呢就是这节课的内容

