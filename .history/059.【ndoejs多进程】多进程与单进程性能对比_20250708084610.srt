1
00:00:00,520 --> 00:00:03,080
好 刚才我们是不是已经利用了cluster去开启了多进程

2
00:00:03,080 --> 00:00:04,600
那么这节课我们来看一下

3
00:00:04,600 --> 00:00:06,920
多进程它和单进程之间性能的一个对比

4
00:00:06,920 --> 00:00:10,500
咱们开启了多进程 说了它如何如何好 如何如何性能强

5
00:00:10,500 --> 00:00:12,040
那么我们来真正的

6
00:00:12,040 --> 00:00:13,820
比一比是罗兹司马 拉出来溜一溜

7
00:00:13,820 --> 00:00:16,380
首先

8
00:00:16,380 --> 00:00:18,680
这里老师会用一个工具叫做AB

9
00:00:18,680 --> 00:00:19,720
AB是什么呀

10
00:00:19,720 --> 00:00:22,280
AB是阿帕奇它自带的一个压力测试工具

11
00:00:22,280 --> 00:00:23,300
大家在学习

12
00:00:23,300 --> 00:00:26,120
前面的课程的时候是不是已经了解过了咱们的阿帕奇服务器它是

13
00:00:26,120 --> 00:00:27,900
专门用于http服务的

14
00:00:28,420 --> 00:00:31,440
这里呢也是老师去推荐大家去使用麦克的一个原因

15
00:00:31,440 --> 00:00:34,880
为什么呀

16
00:00:34,880 --> 00:00:36,620
因为麦克它自带阿巴奇

17
00:00:36,620 --> 00:00:37,720
如果说你用的是windows

18
00:00:37,720 --> 00:00:39,300
可能呢你还需要去到处去安装

19
00:00:39,300 --> 00:00:41,400
阿巴奇才会有AB这样一个命令

20
00:00:41,400 --> 00:00:43,040
那么在麦克里面它是原生自带的

21
00:00:43,040 --> 00:00:44,160
这里呢它有两个命令

22
00:00:44,160 --> 00:00:45,180
一个是-n 一个是-c

23
00:00:45,180 --> 00:00:46,080
-n是什么呢

24
00:00:46,080 --> 00:00:47,180
就是咱们的请求数量

25
00:00:47,180 --> 00:00:49,000
比如说你一次性发出去多少个请求

26
00:00:49,000 --> 00:00:49,820
这里呢是1000个

27
00:00:49,820 --> 00:00:50,520
并发数呢

28
00:00:50,520 --> 00:00:52,400
就是说在这1000个请求当中

29
00:00:52,400 --> 00:00:54,200
会有多少个请求同时发出去

30
00:00:54,200 --> 00:00:55,300
这就是-n和-c

31
00:00:55,300 --> 00:00:57,720
后面呢就是你要去请求的地址

32
00:00:58,240 --> 00:00:59,520
好 那我们直接来看一下

33
00:00:59,520 --> 00:01:04,380
首先我们来启动一个单进程的模型

34
00:01:04,380 --> 00:01:09,500
来个 是不是咱们http.js 它是个单进程吗 只开启了一个进程 在8000端口下面

35
00:01:09,500 --> 00:01:11,280
好 那么呢 我们就来去

36
00:01:11,280 --> 00:01:14,620
我们来启动一下

37
00:01:14,620 --> 00:01:16,160
ls

38
00:01:16,160 --> 00:01:18,960
loadhttp.js

39
00:01:18,960 --> 00:01:23,060
loadhttp.js

40
00:01:23,580 --> 00:01:27,680
点GS好咱们的8000端口已经启动了这里呢咱们来用AB命令

41
00:01:27,680 --> 00:01:30,500
AB-N是什么呀是不是数量

42
00:01:30,500 --> 00:01:34,580
-C并发数

43
00:01:34,580 --> 00:01:35,860
20

44
00:01:35,860 --> 00:01:37,400
地址呢

45
00:01:37,400 --> 00:01:39,200
localhost

46
00:01:39,200 --> 00:01:40,980
3000好咱们来看一下

47
00:01:40,980 --> 00:01:43,540
好像有点问题没关系

48
00:01:43,540 --> 00:01:49,180
我们不能填localhost我们要填本级地址

49
00:01:49,180 --> 00:01:52,260
不对好像是8000端口

50
00:01:53,580 --> 00:01:55,300
好像也不对 好 那咱们重新试一下吧

51
00:01:55,300 --> 00:01:57,260
8000 好 走

52
00:01:57,260 --> 00:02:01,060
大家看到没有 咱们是不是已经发出去了 发了1000次100% 全部都完成了

53
00:02:01,060 --> 00:02:03,420
这里那是单进程 说明什么问题

54
00:02:03,420 --> 00:02:07,140
说明他很快一瞬间1000条琴就已经模拟完了 说明什么问题

55
00:02:07,140 --> 00:02:09,140
他其实反应跑得还挺快的

56
00:02:09,140 --> 00:02:15,980
那咱们再把数量加大一点 咱们加到2000条看一下 好像还是很快

57
00:02:15,980 --> 00:02:19,180
我们需要他慢一点 需要他能卡住 再最好3000条

58
00:02:19,180 --> 00:02:21,220
好像是不是稍微慢一点呢 咱们再加

59
00:02:22,580 --> 00:02:23,860
4000条好卡住了

60
00:02:23,860 --> 00:02:25,140
大家注意到没有

61
00:02:25,140 --> 00:02:28,460
已经开始卡住了400 800 1200 1600 283200

62
00:02:28,460 --> 00:02:31,280
服务器可能卡不住了

63
00:02:31,280 --> 00:02:32,560
可能卡不住了

64
00:02:32,560 --> 00:02:33,580
大家注意到没有

65
00:02:33,580 --> 00:02:35,120
3200这里卡住卡死了

66
00:02:35,120 --> 00:02:37,940
说明咱们的单进程这里呢挂掉

67
00:02:37,940 --> 00:02:39,980
好取消一下

68
00:02:39,980 --> 00:02:42,040
好那我们来看一下多进程的模型

69
00:02:42,040 --> 00:02:43,320
他是什么样的情况

70
00:02:43,320 --> 00:02:44,860
我们把代码改一下

71
00:02:44,860 --> 00:02:48,440
把我们的8核咱们刚才是不是开了8核fork的8次

72
00:02:48,440 --> 00:02:50,480
那么我们把端口改到8001

73
00:02:52,580 --> 00:02:54,620
以免和我们的单进程模型冲突了

74
00:02:54,620 --> 00:02:57,180
好

75
00:02:57,180 --> 00:02:59,500
露的还许http

76
00:02:59,500 --> 00:03:00,520
cluster

77
00:03:00,520 --> 00:03:02,060
好走你

78
00:03:02,060 --> 00:03:03,340
八个进程

79
00:03:03,340 --> 00:03:04,860
咱们再来看一下

80
00:03:04,860 --> 00:03:06,400
咱们把它改成八千零一

81
00:03:06,400 --> 00:03:07,680
同学们注意到没有

82
00:03:07,680 --> 00:03:09,480
是不是一瞬间咱们的八核

83
00:03:09,480 --> 00:03:11,260
一瞬间就完成了对吧

84
00:03:11,260 --> 00:03:13,580
那咱们再来看一下单核表现怎么样

85
00:03:13,580 --> 00:03:15,880
好像他也还可以

86
00:03:15,880 --> 00:03:18,440
夫妻不是很稳定说明单核不是很稳定没关系咱们继续加

87
00:03:18,440 --> 00:03:20,480
注意到八千是什么八千是咱们的单核

88
00:03:20,480 --> 00:03:21,760
也咱们的单进程

89
00:03:22,020 --> 00:03:24,020
好 那咱们把并发加一点 加了50 看一下表现怎么样

90
00:03:24,020 --> 00:03:26,020
n4000并发50

91
00:03:26,020 --> 00:03:28,860
哎 好像还是可以啊 好 挂了

92
00:03:28,860 --> 00:03:32,260
大家注意到没有 咱们服务器挂了 并发50 挂了

93
00:03:32,260 --> 00:03:39,820
很慢 对吧 卡了几秒钟才执行完 好 那咱们来看一下多核表现怎么样

94
00:03:39,820 --> 00:03:41,900
8000是多核吧 多进程

95
00:03:41,900 --> 00:03:45,260
走 大家注意到没有 都非常快

96
00:03:45,260 --> 00:03:46,420
走

97
00:03:46,420 --> 00:03:47,800
走

98
00:03:48,800 --> 00:03:52,640
好 那我们如果说把并发调到100 看我们的多进程能不能扛住

99
00:03:52,640 --> 00:03:53,400
100

100
00:03:53,400 --> 00:03:56,220
好 100好像多进程也扛不住了

101
00:03:56,220 --> 00:03:57,760
并发量稍微有点大

102
00:03:57,760 --> 00:04:02,620
好 我们改小一点 改到80

103
00:04:02,620 --> 00:04:04,160
走 好 好像不行

104
00:04:04,160 --> 00:04:07,240
改到60

105
00:04:07,240 --> 00:04:10,040
走 可以吧 咱们最后试一次看单进程表现怎么样

106
00:04:10,040 --> 00:04:11,840
哎 单进程好像也可以

107
00:04:11,840 --> 00:04:14,660
说明他要休息一回 单进程

108
00:04:14,660 --> 00:04:16,440
4000 60 8000

109
00:04:16,440 --> 00:04:17,480
好 卡住

110
00:04:18,500 --> 00:04:21,580
但注意到没有咱们的单进程模型他有时候会卡住

111
00:04:21,580 --> 00:04:23,620
对吧现在已经跑不了了

112
00:04:23,620 --> 00:04:28,220
那咱们来看一下多进程有没有这种情况多核8000走

113
00:04:28,220 --> 00:04:29,260
哎又卡住

114
00:04:29,260 --> 00:04:31,040
没卡住对吧走

115
00:04:31,040 --> 00:04:35,400
可能我们的系统cpu占用量稍微有点高我们看一下

116
00:04:35,400 --> 00:04:38,220
没有

117
00:04:38,220 --> 00:04:39,240
继续

118
00:04:39,240 --> 00:04:42,300
大家注意到没有其实我们的多核他会稍微稳定一点

119
00:04:42,300 --> 00:04:44,100
稍微稳定一点他的

120
00:04:46,920 --> 00:04:48,960
60可能有点有点扛不住

121
00:04:48,960 --> 00:04:51,780
我们调低一点 调低一点就能够去体现它的一个差别

122
00:04:51,780 --> 00:05:01,520
好 卡住 继续

123
00:05:01,520 --> 00:05:02,540
卡住

124
00:05:02,540 --> 00:05:05,360
如果说咱们的单核再来试一下

125
00:05:05,360 --> 00:05:12,260
单核好像比多核还稳定 其实这里造成一个原因是什么呢

126
00:05:12,260 --> 00:05:14,060
可能是可能是我们有些进程

127
00:05:14,320 --> 00:05:17,160
咱们在启动多进程的时候没有去处理一些异常

128
00:05:17,160 --> 00:05:19,360
可能我们的有些进程其实已经挂掉了

129
00:05:19,360 --> 00:05:21,280
因为我们的多进程模型非常简单

130
00:05:21,280 --> 00:05:23,480
如果说你的多进程比较的健壮

131
00:05:23,480 --> 00:05:25,460
可以说它的性能是远高于单进程的

132
00:05:25,460 --> 00:05:26,460
咱们重启一遍可以看一下

133
00:05:26,460 --> 00:05:27,580
它的性能会好很多

134
00:05:27,580 --> 00:05:28,800
大家注意到大家注意看

135
00:05:28,800 --> 00:05:31,240
大家注意看

136
00:05:31,240 --> 00:05:32,200
它的性能会好很多

137
00:05:32,200 --> 00:05:33,900
因为可能我们在刚才请求的时候

138
00:05:33,900 --> 00:05:35,580
直接把有些子进程给打挂了

139
00:05:35,580 --> 00:05:36,440
但是没有一个重启机制

140
00:05:36,440 --> 00:05:38,060
因为我们的class的模型非常简单

141
00:05:38,060 --> 00:05:40,120
这里老师就不给同学们继续去演示了

142
00:05:40,120 --> 00:05:41,820
同学们可以自己下去去玩一下

143
00:05:41,820 --> 00:05:43,160
那我们来总结一下这节课

144
00:05:43,160 --> 00:05:46,820
我们刚才是不是去创建了一个多进程和一个单进程的模型

145
00:05:46,820 --> 00:05:50,760
通过AB这样一个工具去发起并发的请求

146
00:05:50,760 --> 00:05:52,060
这里可能效果不是很明显

147
00:05:52,060 --> 00:05:52,820
稍微有一点点

148
00:05:52,820 --> 00:05:56,760
可能是因为咱们的cluster没有去很好的处理它们的重启机制

149
00:05:56,760 --> 00:05:58,580
其实这里在多进程里面

150
00:05:58,580 --> 00:06:00,860
它的性能是远远要好于单进程的

151
00:06:00,860 --> 00:06:02,980
有兴趣的同学也可以下去之后

152
00:06:02,980 --> 00:06:06,220
自己通过AB安装一个去测试一下

153
00:06:06,220 --> 00:06:07,860
可以很明显的发现它们之间的区别

154
00:06:07,860 --> 00:06:09,760
好 这里就是这一节课的内容

