1
00:00:00,520 --> 00:00:01,540
好 同学们大家好

2
00:00:01,540 --> 00:00:02,820
那么这节课我们就来看一下

3
00:00:02,820 --> 00:00:04,860
nodejs操作redis数据库

4
00:00:04,860 --> 00:00:07,420
首先

5
00:00:07,420 --> 00:00:08,960
这一系列课程

6
00:00:08,960 --> 00:00:13,560
我会带着同学们去了解一下咱们框架的一个选型以及可视化工具的安装

7
00:00:13,560 --> 00:00:14,600
为什么安装可视化工具

8
00:00:14,600 --> 00:00:16,380
为什么呀

9
00:00:16,380 --> 00:00:19,720
我们不可能永远用这个小黑窗去看数据吧 其实很蛋疼

10
00:00:19,720 --> 00:00:21,500
不知道同学们有没有用过get的

11
00:00:21,500 --> 00:00:23,040
可视化工具非常好用

12
00:00:23,040 --> 00:00:24,060
比如说我们去

13
00:00:24,060 --> 00:00:26,620
咱们是不是经常getlog对吧

14
00:00:26,620 --> 00:00:27,900
get status

15
00:00:27,900 --> 00:00:29,700
其实呢它都是一些

16
00:00:30,000 --> 00:00:30,520
命令行

17
00:00:30,520 --> 00:00:31,920
但是如果说你用可扫工具

18
00:00:31,920 --> 00:00:33,320
它可能更加直观一点

19
00:00:33,320 --> 00:00:35,760
然后还会介绍一些

20
00:00:35,760 --> 00:00:37,940
nodejs去操作redis的一些语法

21
00:00:37,940 --> 00:00:40,240
好那么这节课我们就来主要讲解

22
00:00:40,240 --> 00:00:42,540
框架的一个选择和咱们可扫工具栏装

23
00:00:42,540 --> 00:00:44,080
首先框架的选择

24
00:00:44,080 --> 00:00:45,620
nodejs去操作redis序库

25
00:00:45,620 --> 00:00:46,640
主流框架左右两个

26
00:00:46,640 --> 00:00:47,400
一个是noderedis

27
00:00:47,400 --> 00:00:48,180
一个是iorelis

28
00:00:48,180 --> 00:00:49,460
那么我们怎么去选择

29
00:00:49,460 --> 00:00:51,000
之前我们是不是讲过

30
00:00:51,000 --> 00:00:52,780
redis和meme卡写他的选择

31
00:00:52,780 --> 00:00:53,300
怎么选

32
00:00:53,300 --> 00:00:54,320
是不是看随的是大多

33
00:00:54,320 --> 00:00:56,120
那么我们这里就来看一下noderedis

34
00:00:56,120 --> 00:00:57,900
和iorelis他们随的是大多

35
00:00:57,900 --> 00:00:58,680
好

36
00:00:59,440 --> 00:01:02,520
这里呢 我们已经查过了 大家可以看到loadradish它有多少星

37
00:01:02,520 --> 00:01:04,560
是不是11000多个星呢

38
00:01:04,560 --> 00:01:06,600
那么我们再来看一下

39
00:01:06,600 --> 00:01:07,880
Ioradish

40
00:01:07,880 --> 00:01:13,000
好 我们可以看到

41
00:01:13,000 --> 00:01:14,280
它有6000颗星

42
00:01:14,280 --> 00:01:17,100
那么同学们 我们能不能得出结论

43
00:01:17,100 --> 00:01:18,640
我们是不是可以选择

44
00:01:18,640 --> 00:01:19,920
我们是不是可以选择

45
00:01:19,920 --> 00:01:20,940
选择谁呀

46
00:01:20,940 --> 00:01:23,000
是不是选择loadradish

47
00:01:23,000 --> 00:01:24,280
其实并不是这样的

48
00:01:24,280 --> 00:01:26,060
其实并不是这样的

49
00:01:26,060 --> 00:01:29,400
我们呢 这里呢 我们老师呢会选择Ioradish

50
00:01:29,440 --> 00:01:31,040
其实我们去选择一个酷的时候

51
00:01:31,040 --> 00:01:32,920
对不一定 单纯的去看它的start

52
00:01:32,920 --> 00:01:34,880
为什么IoRedis这里比较特殊呢

53
00:01:34,880 --> 00:01:35,880
其实呢 具体

54
00:01:35,880 --> 00:01:38,280
具体呢 我们可以看一下

55
00:01:38,280 --> 00:01:40,200
比如说我们去选择一个酷的时候

56
00:01:40,200 --> 00:01:41,200
你也可以去查一下

57
00:01:41,200 --> 00:01:43,400
比如说我们会去搜索

58
00:01:43,400 --> 00:01:45,360
IoRedis和咱们的NodeRedis

59
00:01:45,360 --> 00:01:46,160
它的一个区别是什么

60
00:01:46,160 --> 00:01:47,080
或者说我们怎么去选择

61
00:01:47,080 --> 00:01:48,600
我们可以通过Google

62
00:01:48,600 --> 00:01:49,880
比如说我们去搜索

63
00:01:49,880 --> 00:01:55,080
IoRedis和NodeRedis

64
00:01:55,080 --> 00:01:59,120
选择

65
00:01:59,440 --> 00:02:00,940
好 大家可以看到

66
00:02:00,940 --> 00:02:01,960
第一条

67
00:02:01,960 --> 00:02:03,480
也就是咱们的这篇文章

68
00:02:03,480 --> 00:02:04,400
老师已经打开了

69
00:02:04,400 --> 00:02:05,200
我们可以看到

70
00:02:05,200 --> 00:02:07,160
可以看到在业内

71
00:02:07,160 --> 00:02:09,200
我们对IoRedis和LowRedis的一些介绍

72
00:02:09,200 --> 00:02:11,060
其他重点可以看到什么呢

73
00:02:11,060 --> 00:02:11,560
比如说

74
00:02:11,560 --> 00:02:14,140
LowRedis和IoRedis是目前

75
00:02:14,140 --> 00:02:17,500
是咱们目前使用比较广泛的一个LowRedis客户端

76
00:02:17,500 --> 00:02:20,120
那么IoRedis是我在半年前开发的

77
00:02:20,120 --> 00:02:20,680
说明什么问题

78
00:02:20,680 --> 00:02:22,520
说明这篇文章的作者

79
00:02:22,520 --> 00:02:23,560
是不是就是IoRedis的作者

80
00:02:23,560 --> 00:02:26,080
对吧 好

81
00:02:26,080 --> 00:02:29,680
那么他遇到了一些什么问题

82
00:02:29,680 --> 00:02:31,240
那时我参与的公司项目

83
00:02:31,240 --> 00:02:32,440
对Redis的稳定性要求很高

84
00:02:32,440 --> 00:02:33,600
然后在应用开发阶段

85
00:02:33,600 --> 00:02:34,400
发现当时的Redis和Redis

86
00:02:34,400 --> 00:02:36,160
几乎是那时候唯一的选择

87
00:02:36,160 --> 00:02:37,180
什么说明什么问题

88
00:02:37,180 --> 00:02:38,500
是不是Redis它出现的比较早

89
00:02:38,500 --> 00:02:39,760
然后存在一些严重的bug

90
00:02:39,760 --> 00:02:41,000
如果不加以修改的话

91
00:02:41,000 --> 00:02:41,840
无法继续使用下去

92
00:02:41,840 --> 00:02:44,160
最初作者

93
00:02:44,160 --> 00:02:45,140
也就是IoRedis的设置

94
00:02:45,140 --> 00:02:46,840
作者他想给Redis去打一些

95
00:02:46,840 --> 00:02:47,520
Patch

96
00:02:47,520 --> 00:02:48,540
也就是布丁

97
00:02:48,540 --> 00:02:50,260
但是发现不好解决

98
00:02:50,260 --> 00:02:51,000
需要大范围成功

99
00:02:51,000 --> 00:02:53,920
所以说作者才自己研发了IoRedis

100
00:02:53,920 --> 00:02:55,600
那么说明什么问题

101
00:02:55,600 --> 00:02:57,900
是不是说明radius他可以去解决

102
00:02:57,900 --> 00:03:00,460
可以去解决什么是不是no rise他解决不了的一些问题

103
00:03:00,460 --> 00:03:02,260
而且的iorealis他的作者

104
00:03:02,260 --> 00:03:03,800
是咱们的

105
00:03:03,800 --> 00:03:05,080
相对是国产的

106
00:03:05,080 --> 00:03:07,120
那么国产他本身在开源的项目里面

107
00:03:07,120 --> 00:03:08,140
是大会比较少

108
00:03:08,140 --> 00:03:09,680
这是一个不可否定的事实

109
00:03:09,680 --> 00:03:10,960
那么iorealis呢

110
00:03:10,960 --> 00:03:13,000
他已经被国内外众多的公司使用

111
00:03:13,000 --> 00:03:14,800
所以我们这里可以得出几个结论

112
00:03:14,800 --> 00:03:15,560
一个

113
00:03:15,560 --> 00:03:18,900
一个iorealis他解决了一些no rise没有解决的问题

114
00:03:18,900 --> 00:03:21,720
第二个其实iorealis他也已经已经被很多

115
00:03:21,720 --> 00:03:23,760
国内外一些公司所使用也就是被认可

116
00:03:23,760 --> 00:03:25,040
所以说这里我们来选择

117
00:03:25,300 --> 00:03:26,580
选择IoRedis

118
00:03:26,580 --> 00:03:30,420
好 那么既然我们已经

119
00:03:30,420 --> 00:03:32,720
留下了结论 我们要选择IoRedis

120
00:03:32,720 --> 00:03:34,260
所以呢

121
00:03:34,260 --> 00:03:37,580
我们来看一下怎么去安装

122
00:03:37,580 --> 00:03:39,640
其实你安装IoRedis的命运很简单

123
00:03:39,640 --> 00:03:41,940
只需要使用npm install IoRedis就可以了

124
00:03:41,940 --> 00:03:43,480
好

125
00:03:43,480 --> 00:03:48,860
那么安装的过程呢 这里老师就不带大家去安装了 因为非常简单就很命我们来看一下可受化工具

126
00:03:48,860 --> 00:03:49,620
首先

127
00:03:49,620 --> 00:03:53,200
首先可受化工具分为几种啊 同学们

128
00:03:53,200 --> 00:03:55,260
其实分为两种

129
00:03:55,300 --> 00:03:56,900
一种收费的就免费了对吧

130
00:03:56,900 --> 00:03:59,400
收费的肯定是很少

131
00:03:59,400 --> 00:04:01,800
反正我是不会去用的

132
00:04:01,800 --> 00:04:05,600
比如说我们想要去比如说我们去搜索

133
00:04:05,600 --> 00:04:06,800
radis

134
00:04:06,800 --> 00:04:10,000
其实这里了我之前已经收过了大家可以看到

135
00:04:10,000 --> 00:04:11,600
radis有一些比较主流的可操工具

136
00:04:11,600 --> 00:04:15,100
我们来看一下首先这样一个radis desktop desktop manage

137
00:04:15,100 --> 00:04:16,200
我们来看一下官网

138
00:04:16,200 --> 00:04:17,900
好当到了

139
00:04:17,900 --> 00:04:20,700
大家可以看到它是收费了一年多少钱

140
00:04:20,700 --> 00:04:21,700
一年多少钱

141
00:04:23,000 --> 00:04:25,820
是不是30美金 30美金差不多人民币100多块钱吧 挺贵的

142
00:04:25,820 --> 00:04:28,380
那么其实大家看到

143
00:04:28,380 --> 00:04:32,220
什么

144
00:04:32,220 --> 00:04:33,500
one

145
00:04:33,500 --> 00:04:36,060
for request 等于 one year of fail

146
00:04:36,060 --> 00:04:38,100
也就是说你只要给他的

147
00:04:38,100 --> 00:04:39,640
给这样一个项目去贡献

148
00:04:39,640 --> 00:04:40,920
贡献银行代码

149
00:04:40,920 --> 00:04:42,720
你就可以了一年的免费使用

150
00:04:42,720 --> 00:04:43,740
对吧

151
00:04:43,740 --> 00:04:47,580
所以同学们一定要好好去学义技术 学好之后呢 你还可以免费的去使用他们的工具

152
00:04:47,580 --> 00:04:49,620
所以这里呢 我们

153
00:04:50,140 --> 00:04:53,980
就是选择免费的 有一款比较好用的 叫做another redis desktop manage another

154
00:04:53,980 --> 00:04:56,540
好 我们来看一下

155
00:04:56,540 --> 00:04:58,580
他的官网 咱们可操工具的官网

156
00:04:58,580 --> 00:05:10,360
好 大家其实可以看到

157
00:05:10,360 --> 00:05:11,900
大家可以看到

158
00:05:11,900 --> 00:05:14,980
这款可操工具呢 它支持windows news mic 三个版本

159
00:05:14,980 --> 00:05:17,780
那么因为老是电脑是mic的 所以说我们去安装mic

160
00:05:18,300 --> 00:05:21,380
其实很简单 点击这样一个dmg文件就可以安装了 我这里已经安装好了

161
00:05:21,380 --> 00:05:23,680
 所以就不给同学们去演示 安装了一个过程了 我们直接来看一下

162
00:05:23,680 --> 00:05:26,740
这款可操工具 它一个结果 安装之后长什么样

163
00:05:26,740 --> 00:05:36,600
好 其实是长这样的 我们这里也已经安装好了 已经安装好了

164
00:05:36,600 --> 00:05:38,520
好 我们来连接一下看一下

165
00:05:38,520 --> 00:05:41,860
好 这里呢 报了一个错 什么意思呢

166
00:05:41,860 --> 00:05:47,740
To my attempts to reconnect please check the server status 说明什么问题 说明我们的reddit服务是不是没起用

167
00:05:48,000 --> 00:05:50,240
那么我们就来启动一下 怎么启动radis服务啊 兄弟们

168
00:05:50,240 --> 00:05:53,320
还记得启动radis服务的命令吗 是不是radis杀

169
00:05:53,320 --> 00:05:58,260
sever啊 好 这里呢 我们就已经启动了 默论端口是什么 6379 好 我们来连接一下

170
00:05:58,260 --> 00:06:02,520
好 这里呢 大家就可以看到 我们的radis数据库

171
00:06:02,520 --> 00:06:03,600
他呢

172
00:06:03,600 --> 00:06:06,200
可以是一个 他呢 就是一个可通话界面

173
00:06:06,200 --> 00:06:12,320
大家知道这是什么吗 db0 db0

174
00:06:12,320 --> 00:06:17,480
我们之前是不是讲过 在radis里面是不是最多 最多可以建立多少个连接

175
00:06:17,480 --> 00:06:18,160
是不是16呗

176
00:06:18,160 --> 00:06:19,980
大家看到0到15是不刚好16呗

177
00:06:19,980 --> 00:06:21,000
而且呢

178
00:06:21,000 --> 00:06:22,720
它会显示一些信息

179
00:06:22,720 --> 00:06:24,060
你比如说我们现在是空的

180
00:06:24,060 --> 00:06:24,700
我们去新增一个k

181
00:06:24,700 --> 00:06:26,020
比如说我们去添加一个死菌

182
00:06:26,020 --> 00:06:26,860
也就是k倍率

183
00:06:26,860 --> 00:06:27,900
添加一个死菌

184
00:06:27,900 --> 00:06:29,200
比如说

185
00:06:29,200 --> 00:06:30,860
菌叫做a

186
00:06:30,860 --> 00:06:32,120
内门叫做1保存

187
00:06:32,120 --> 00:06:33,060
好大家可以看到

188
00:06:33,060 --> 00:06:34,480
我们现在其实已经保存成功了

189
00:06:34,480 --> 00:06:37,500
大家可以看到

190
00:06:37,500 --> 00:06:38,480
我们其实已经保存成功了

191
00:06:47,480 --> 00:06:50,580
好

192
00:06:50,580 --> 00:06:52,940
这里的展示好像没有数据

193
00:06:52,940 --> 00:06:53,520
我们等一下

194
00:06:53,520 --> 00:06:54,620
我们等一下可以

195
00:06:54,620 --> 00:06:56,220
通过代码带的同学们去演示一下

196
00:06:56,220 --> 00:06:57,060
看一下我们的可照工具

197
00:06:57,060 --> 00:06:58,060
到底怎么样去使用

198
00:06:58,060 --> 00:06:58,660
好 这里呢

199
00:06:58,660 --> 00:07:00,560
我们就来先总结一下

200
00:07:00,560 --> 00:07:02,120
这节课我们所讲解的内容

201
00:07:02,120 --> 00:07:14,140
好 这节课我们是不是讲了

202
00:07:14,140 --> 00:07:16,200
咱们loadgs去操作我们的

203
00:07:16,200 --> 00:07:21,200
我们的框架选型有什么?是不是load

204
00:07:21,200 --> 00:07:26,200
load刚redis

205
00:07:26,200 --> 00:07:28,200
还有呢?IOredis

206
00:07:28,200 --> 00:07:30,200
我们选择了谁?

207
00:07:30,200 --> 00:07:32,200
我们是不是选择他,为什么?

208
00:07:32,200 --> 00:07:34,200
因为他解决了一些

209
00:07:34,200 --> 00:07:36,200
是不是解决了一些

210
00:07:36,200 --> 00:07:38,200
我们loadredis没有解决的问题

211
00:07:38,200 --> 00:07:40,200
对吧

212
00:07:40,200 --> 00:07:42,200
好

213
00:07:42,200 --> 00:07:44,200
那么我们还讲了什么

214
00:07:44,200 --> 00:07:46,100
是不是还讲解了我们可刷工具的安装

215
00:07:46,100 --> 00:07:49,520
我们使用了什么

216
00:07:49,520 --> 00:07:51,400
我们使用了什么可刷工具是不是

217
00:07:51,400 --> 00:07:55,380
A lot of redis desktop manage 对吧

218
00:07:55,380 --> 00:07:58,880
好 那么这里呢就是我们这几个的内容

