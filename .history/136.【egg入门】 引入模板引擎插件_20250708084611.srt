1
00:00:00,000 --> 00:00:02,820
好这节课我们就来看一下在1GG里面怎么样去使用模板渲染

2
00:00:02,820 --> 00:00:04,100
那么因为我们的

3
00:00:04,100 --> 00:00:07,940
业务不可能说只去比如说你去返回一些节省和自主创

4
00:00:07,940 --> 00:00:11,520
你可能去你可能需要去直接去返回咱们的HME面那么怎么办呢

5
00:00:11,520 --> 00:00:12,800
其实我们需要去使用

6
00:00:12,800 --> 00:00:14,080
1GG的一个插件

7
00:00:14,080 --> 00:00:15,100
就是咱们的一个模板

8
00:00:15,100 --> 00:00:18,680
我们怎么去开启了在config的plugin在一个GS里面

9
00:00:18,680 --> 00:00:19,720
我们去配置他

10
00:00:19,720 --> 00:00:23,040
比如说把它的enable把它打开然后去引入你所需要的包

11
00:00:23,040 --> 00:00:25,600
然后去加入一些比如说你在config的

12
00:00:25,600 --> 00:00:28,420
他的默认配置里面你去对view在一个字段去进行配置

13
00:00:28,680 --> 00:00:30,860
这里也是1GG汤里面去约定的内容

14
00:00:30,860 --> 00:00:34,620
好 那么我们这里就来感受一下怎么样去开启我们的一个模板渲染

15
00:00:34,620 --> 00:00:36,880
好 首先呢 我们来安装一下依赖

16
00:00:36,880 --> 00:00:40,800
安装一下依赖

17
00:00:40,800 --> 00:00:44,740
那么在安装依赖的时候呢 我们就来先配置一下

18
00:00:44,740 --> 00:00:47,000
首先我们是不是要在plugin.js里面去配置啊

19
00:00:47,000 --> 00:00:47,920
我们来看一下配置是什么

20
00:00:47,920 --> 00:00:50,040
export 我们要导入一个

21
00:00:50,040 --> 00:00:54,140
好 我们现在把它给粘过来

22
00:00:54,140 --> 00:00:56,640
我们是不是要导出一个launcher是这样的一个属性呢

23
00:00:56,640 --> 00:00:57,840
因为我们的模板用的是汤

24
00:00:57,840 --> 00:00:58,340
所以呢

25
00:00:58,340 --> 00:01:00,640
这里我们来改造一下

26
00:01:00,640 --> 00:01:07,140
比如说我们要给导处的对象添添加一个这样的浪加是在一个属性然后来给他两个

27
00:01:07,140 --> 00:01:09,840
给他两个配置

28
00:01:09,840 --> 00:01:15,040
好这里了我们就已经配置好了咱们的一个

29
00:01:15,040 --> 00:01:20,540
plugin那么plugin配置完之后我们是不是还要需要去对咱们的默认的config进行一个配置他是配置什么的呀

30
00:01:20,540 --> 00:01:22,540
咱们刚才的plugin

31
00:01:22,540 --> 00:01:24,240
是不是告诉一机记

32
00:01:26,340 --> 00:01:28,840
就是需要加入模板引擎

33
00:01:28,840 --> 00:01:32,320
那么我们在这里配完之后

34
00:01:32,320 --> 00:01:33,680
我们还需要在它的这个里面

35
00:01:33,680 --> 00:01:35,720
config.default件事里面去配置

36
00:01:35,720 --> 00:01:37,520
因为我们还需要去告诉模板引擎

37
00:01:37,520 --> 00:01:39,160
你怎么去渲染给它一些出手啊

38
00:01:39,160 --> 00:01:41,140
配置那么我们也来给粘贴过来

39
00:01:41,140 --> 00:01:44,680
export.view

40
00:01:44,680 --> 00:01:52,640
其实这里呢

41
00:01:52,640 --> 00:01:54,540
我们就直接把export改为什么呢

42
00:01:54,540 --> 00:01:57,100
咱们直接把export改为config就可以了

43
00:01:57,100 --> 00:01:58,540
这里他这两个配置意思是什么呢

44
00:01:58,540 --> 00:02:00,540
就是说一个配置default view 引记

45
00:02:00,540 --> 00:02:02,540
default view 引记

46
00:02:02,540 --> 00:02:04,540
什么意思呢 就是说默认的

47
00:02:04,540 --> 00:02:06,540
模板引擎

48
00:02:06,540 --> 00:02:08,540
是longjacks

49
00:02:08,540 --> 00:02:10,540
那么longjacks也是1GG团队所默认的一个模板引擎

50
00:02:10,540 --> 00:02:13,540
当然了 你也可以去使用其他那些你所喜欢的模板引擎

51
00:02:13,540 --> 00:02:15,540
这里我就不去过多的介绍

52
00:02:15,540 --> 00:02:16,540
包括这里有个marking之段

53
00:02:16,540 --> 00:02:20,540
什么意思 是不是就把咱们 比如说你ng为结尾的一些文件

54
00:02:20,540 --> 00:02:22,540
可以解析成咱们的一个longjacks

55
00:02:22,540 --> 00:02:24,900
这样一个模板 其实就是做了一个map的作用

56
00:02:24,900 --> 00:02:29,440
好 那么这里呢 在在1GG它的约定里面 我们的模板一定要写在

57
00:02:29,440 --> 00:02:33,440
我们的模板一定要写在app它的一个view文件夹下面

58
00:02:33,440 --> 00:02:37,660
你比如说我们去创建了一个hello.ng

59
00:02:37,660 --> 00:02:40,440
这样为结尾的一个文件 我们来粘贴一段html片段

60
00:02:40,440 --> 00:02:46,100
好 那么我们就来尝试一下怎么样去渲染它

61
00:02:46,100 --> 00:02:48,300
那么怎么样去渲染它

62
00:02:48,300 --> 00:02:51,900
首先我们是不是需要去在controller里面去定义啊

63
00:02:52,540 --> 00:02:58,740
我们第1步需要在control定义

64
00:02:58,740 --> 00:03:00,740
是不是定义怎么渲染呢

65
00:03:00,740 --> 00:03:01,140
对吧

66
00:03:01,140 --> 00:03:01,540
好

67
00:03:01,540 --> 00:03:06,340
那么我们来定义一个比如说async去定义一个hello方法

68
00:03:06,340 --> 00:03:09,540
然后咯

69
00:03:10,940 --> 00:03:13,680
我们通过contest.render

70
00:03:13,680 --> 00:03:18,680
这里呢 咱们view里面是不是有个hello.ng

71
00:03:18,680 --> 00:03:23,340
所以我们去rello.ng它呢会自动的读取

72
00:03:23,340 --> 00:03:29,800
view下面的hello.ng其实这里它也是1GG里面的一个约定

73
00:03:29,800 --> 00:03:34,680
那么在contest.render里面我们需要加入wait这样一个关键字

74
00:03:34,680 --> 00:03:37,280
因为contest.render它是一个异步的一个过程

75
00:03:37,280 --> 00:03:39,940
好 那么我们既然写完了contrune了 还需要做什么

76
00:03:39,940 --> 00:03:41,980
是不是要在root里面去订一站一个路由啊

77
00:03:41,980 --> 00:03:45,820
好 那么我们起诉是不是和咱们的

78
00:03:45,820 --> 00:03:48,380
是不是和咱们

79
00:03:48,380 --> 00:03:53,500
之前一样的root.get 比如说我们去订一个hello咱们通过命名空间conturner.home

80
00:03:53,500 --> 00:03:54,780
点什么是不是点hello啊

81
00:03:54,780 --> 00:03:56,700
好 那么我们就来看一下

82
00:03:56,700 --> 00:04:00,940
是不是这么回事 npm run dv

83
00:04:00,940 --> 00:04:09,120
好 我们我们先来访问一下 哎 不好意思 我们先来访问一下咱们的根目录

84
00:04:09,940 --> 00:04:11,980
啊嗨一几几那咱们来访问一下哈喽看一下

85
00:04:11,980 --> 00:04:16,600
好大家可以看到我们的模板是不是已经生效了呀

86
00:04:16,600 --> 00:04:19,660
我们来打开咱们的控制体控制台来看一下

87
00:04:19,660 --> 00:04:27,340
好哈喽是不是返回一个HML content type tx html那么说明了咱们的模板引擎已经

88
00:04:27,340 --> 00:04:28,380
已经生效了

89
00:04:28,380 --> 00:04:33,500
好那么这里呢我们就来回顾一下咱们刚才所讲解模板引擎的一个过程

90
00:04:33,500 --> 00:04:35,800
我们刚才是不是讲到了

91
00:04:36,560 --> 00:04:37,560
我把引擎呢

92
00:04:37,560 --> 00:04:40,660
那么首先怎么样去安装 是不是npm install

93
00:04:40,660 --> 00:04:46,120
egg-view-long-junks

94
00:04:46,120 --> 00:04:51,120
好 那么我们怎么样去配置了 是不是需要在config的plugin里面去

95
00:04:51,120 --> 00:04:53,080
配置了 那么它是配置什么呢

96
00:04:53,080 --> 00:04:55,200
它是不是引入插件了

97
00:04:55,200 --> 00:04:59,600
引入插件 那么我们除了引入插件之外

98
00:04:59,600 --> 00:05:05,880
是不是还需要做一次配置 叫做config.default.

99
00:05:06,120 --> 00:05:10,720
既然是他是做什么呢是不是对模板引擎进行配置

100
00:05:10,720 --> 00:05:14,380
因为呢你引入之后那么你还需要去告诉咱们的框架他

101
00:05:14,380 --> 00:05:17,520
模板引擎比如说怎么样去渲染用什么样的后缀

102
00:05:17,520 --> 00:05:23,120
可能还会需要有些各种配置这里就是需要去在两个地方去配置的一个原因

103
00:05:23,120 --> 00:05:25,420
然后我们怎么去渲染呢怎么去渲染

104
00:05:25,420 --> 00:05:27,820
是不是通过context的Render

105
00:05:27,820 --> 00:05:29,520
不过大家注意我们要加上什么关键字

106
00:05:29,520 --> 00:05:31,480
就是AVET因为它是一个逆步的过程

107
00:05:31,480 --> 00:05:33,000
好这里就是我们这几个的内容

