1
00:00:00,520 --> 00:00:03,320
好 这一节课我们来学习Cluster相关的API

2
00:00:03,320 --> 00:00:05,120
那么在讲Cluster相关API的时候呢

3
00:00:05,120 --> 00:00:06,140
我们会分为三个部分去讲

4
00:00:06,140 --> 00:00:07,420
第一个Process进程

5
00:00:07,420 --> 00:00:08,700
ChillProcess子进程

6
00:00:08,700 --> 00:00:10,240
包括Cluster集群

7
00:00:10,240 --> 00:00:13,320
我这里讲Cluster为什么还会讲前面两部分内容呢

8
00:00:13,320 --> 00:00:13,820
因为

9
00:00:13,820 --> 00:00:16,640
他们三者是紧密相连的Cluster依赖于前两者

10
00:00:16,640 --> 00:00:17,660
所以说咱们必须

11
00:00:17,660 --> 00:00:19,460
要把这三部分都掌握了

12
00:00:19,460 --> 00:00:21,760
那么这节课主要内容咱们来看一下Process进程

13
00:00:21,760 --> 00:00:25,080
它在load里面的一些属性或者方法包括里面的一些事件是怎么一回事

14
00:00:25,080 --> 00:00:27,640
首先看一下介绍

15
00:00:27,900 --> 00:00:29,960
Process对象是Node的一个全局对象

16
00:00:29,960 --> 00:00:31,520
提供当前Node的进程信息

17
00:00:31,520 --> 00:00:32,840
也可以在脚本的任意位置使用

18
00:00:32,840 --> 00:00:33,960
不必通过require命令加载

19
00:00:33,960 --> 00:00:34,600
说明什么问题

20
00:00:34,600 --> 00:00:36,480
是不是咱们的Process对象

21
00:00:36,480 --> 00:00:37,520
它是Node的一个全局对象

22
00:00:37,520 --> 00:00:40,400
也就跟咱们的浏览器里面的Windows一样

23
00:00:40,400 --> 00:00:42,600
在Windows下面是不是也有很多一些全局的方法

24
00:00:42,600 --> 00:00:44,840
但Process它也是Node里面的一个全局方法

25
00:00:44,840 --> 00:00:46,500
这里呢它里面有一些属性

26
00:00:46,500 --> 00:00:49,560
ArgVinVPIDPlatformVersion

27
00:00:49,560 --> 00:00:52,020
咱们呢空说没意思

28
00:00:52,020 --> 00:00:53,020
我们直接通过代码来看一下

29
00:00:53,020 --> 00:00:56,060
好这里呢老师其实已经贴过来了

30
00:00:56,060 --> 00:00:57,060
我们一个一个来看一下

31
00:00:57,060 --> 00:00:57,960
看他会打印出什么内容

32
00:00:57,960 --> 00:00:59,620
这里教大家一个办法

33
00:00:59,620 --> 00:01:02,180
通过断点去看

34
00:01:02,180 --> 00:01:02,940
我们还

35
00:01:02,940 --> 00:01:04,220
需不需要傻乎乎的

36
00:01:04,220 --> 00:01:05,000
去把它给打印出来

37
00:01:05,000 --> 00:01:05,760
这是老子刚才打印的

38
00:01:05,760 --> 00:01:07,040
打印成这样是不是没法看了

39
00:01:07,040 --> 00:01:07,820
所以

40
00:01:07,820 --> 00:01:09,600
刚才咱们居然写到了调试技巧

41
00:01:09,600 --> 00:01:10,120
咱们就用

42
00:01:10,120 --> 00:01:10,880
咱们学到的内容

43
00:01:10,880 --> 00:01:11,900
去看一下

44
00:01:11,900 --> 00:01:13,180
首先浪积.json

45
00:01:13,180 --> 00:01:14,980
已经设好了process.json

46
00:01:14,980 --> 00:01:15,740
那我们直接

47
00:01:15,740 --> 00:01:17,800
那我们直接开始

48
00:01:17,800 --> 00:01:19,080
小虫子

49
00:01:19,080 --> 00:01:19,840
走

50
00:01:19,840 --> 00:01:20,620
好第一步

51
00:01:20,620 --> 00:01:21,640
process.argb

52
00:01:21,640 --> 00:01:22,400
咱们看一下

53
00:01:22,400 --> 00:01:23,680
他是不是返回一个

54
00:01:23,680 --> 00:01:24,460
宿主

55
00:01:24,460 --> 00:01:25,740
包含了

56
00:01:25,740 --> 00:01:27,740
不含了load进程式的命令参数什么意思

57
00:01:27,740 --> 00:01:30,740
咱们启动它的时候怎么样去启动啊是不是load process.js

58
00:01:30,740 --> 00:01:33,740
大家看到没有咱们这个js文件叫做process.js

59
00:01:33,740 --> 00:01:36,740
所以说咱们的rv里面是不是应该有两个

60
00:01:36,740 --> 00:01:40,740
是不是应该有两个应该有两个属性数字里面一个是load

61
00:01:40,740 --> 00:01:43,740
一个呢是process.js对吧

62
00:01:43,740 --> 00:01:46,740
我们看一下是不是怎么回事大家看到没有里面是一个数字

63
00:01:46,740 --> 00:01:49,740
里面是一个绝对路径load process

64
00:01:49,740 --> 00:01:51,740
为什么不是load和process.js呢

65
00:01:51,740 --> 00:01:53,740
因为在loadjs里面我们只有绝对路径才是准确的

66
00:01:53,740 --> 00:01:56,040
才是准确的 你光写一个漏的和processing ds

67
00:01:56,040 --> 00:01:57,640
他是不是不知道你到底在哪个路径下面

68
00:01:57,640 --> 00:02:00,540
所以说在漏的ds他处理的时候把它转化成绝对路径

69
00:02:00,540 --> 00:02:02,440
好 咱们来看一下inv

70
00:02:02,440 --> 00:02:04,140
inv是什么呢

71
00:02:04,140 --> 00:02:05,740
它返回包含用户环境信息的对象

72
00:02:05,740 --> 00:02:08,440
可以在脚本中对这个对象进行增商改查的操作

73
00:02:08,440 --> 00:02:12,040
也就是说主要是用户环境信息

74
00:02:12,040 --> 00:02:14,840
好 我们来看一下里面有哪些内容

75
00:02:14,840 --> 00:02:17,740
首先有lunk 是不是咱们的语言呢

76
00:02:17,740 --> 00:02:20,340
local in us utf-8编码

77
00:02:20,340 --> 00:02:22,940
包括咱们的nvm的一个路径

78
00:02:23,240 --> 00:02:25,540
nvm是什么呀 是不是管你load的模块的一个包

79
00:02:25,540 --> 00:02:27,840
pwd 当前目录

80
00:02:27,840 --> 00:02:31,940
包括jsh 是咱们的一个shear的一个工具

81
00:02:31,940 --> 00:02:35,780
好 这里就是一些环境信息 咱们来看一下pid 49249

82
00:02:35,780 --> 00:02:39,360
打开活动监视器 看一下load pid 4929 可以对应吧

83
00:02:39,360 --> 00:02:40,900
好

84
00:02:40,900 --> 00:02:45,520
platform Darwin Darwin是什么呢 Darwin实际上是在mike下面 他那个平台就叫做Darwin

85
00:02:45,520 --> 00:02:48,080
Version是我们的nodejs的版本

86
00:02:48,080 --> 00:02:49,100
大家看到没有

87
00:02:49,100 --> 00:02:50,120
nodejs的版本

88
00:02:51,400 --> 00:02:53,960
老实的是10.15.3也是比较新的一个版本

89
00:02:53,960 --> 00:02:56,000
好 这里就是Process它的一些属性

90
00:02:56,000 --> 00:02:57,040
那咱们接下来看一下

91
00:02:57,040 --> 00:02:58,560
Process有哪些方法

92
00:02:58,560 --> 00:03:02,160
首先Process.cwd返回nodg.js当前的工作目录

93
00:03:02,160 --> 00:03:04,200
包括了NextTick

94
00:03:04,200 --> 00:03:06,000
是不是咱们之前讲世界循环的时候讲过

95
00:03:06,000 --> 00:03:08,040
它是在咱们世界循环的

96
00:03:08,040 --> 00:03:10,080
所切换的中间对吧

97
00:03:10,080 --> 00:03:11,620
ExitProcess退出

98
00:03:11,620 --> 00:03:13,680
时候驻发了一个方法

99
00:03:13,680 --> 00:03:15,980
好 这里我们就来看一下

100
00:03:15,980 --> 00:03:20,840
咱们先把调整工具关一下

101
00:03:20,840 --> 00:03:21,540
好注释

102
00:03:21,540 --> 00:03:25,640
这里呢 老师就不去一一的去

103
00:03:25,640 --> 00:03:30,680
去带个同学看了 我们就看一个process.cwd 看一下他是怎么回事

104
00:03:30,680 --> 00:03:35,840
console.log process.cwd

105
00:03:35,840 --> 00:03:39,260
好 走

106
00:03:39,260 --> 00:03:42,320
打个办理 把这一段点都消掉 不需要了

107
00:03:42,320 --> 00:03:46,600
走 好 咱们来看一下cwd执行之后反为什么 这样

108
00:03:50,840 --> 00:03:54,240
好像不可以 我们需要一个变量去接受一下 没关系

109
00:03:54,240 --> 00:04:01,340
咱们挖一个cwd等于process.cwd

110
00:04:01,340 --> 00:04:10,640
好 大家看到没有 是不是返回了当前我的一个工作目录啊

111
00:04:10,640 --> 00:04:12,840
3-2 cwd

112
00:04:12,840 --> 00:04:19,640
也就是咱们当前的一个工作目录 在vascode里面的3-2当前工作目录

113
00:04:19,640 --> 00:04:21,380
好 那我们来看一下

114
00:04:21,380 --> 00:04:24,820
其他的KL给指定进程发送信号

115
00:04:24,820 --> 00:04:26,040
这里让老师不方便去演示

116
00:04:26,040 --> 00:04:28,320
同学们可以在今后的应用中

117
00:04:28,320 --> 00:04:29,500
自己去测试他的一些方法

118
00:04:29,500 --> 00:04:30,500
比如说KL杀了一个进程

119
00:04:30,500 --> 00:04:31,440
Exit退出一个进程

120
00:04:31,440 --> 00:04:32,920
好 那我们再来看一下

121
00:04:32,920 --> 00:04:34,720
Process里面它有哪些事件

122
00:04:34,720 --> 00:04:36,640
BeforeExit

123
00:04:36,640 --> 00:04:38,700
Exit Uncut Exception

124
00:04:38,700 --> 00:04:40,180
其实我们的重点是什么呢

125
00:04:40,180 --> 00:04:41,200
就是说在当前进程

126
00:04:41,200 --> 00:04:42,900
如果说掏出一个没有捕获的错误的时候

127
00:04:42,900 --> 00:04:45,620
可以通过Uncut Exception去接收

128
00:04:45,620 --> 00:04:46,920
那么我们来看一下

129
00:04:46,920 --> 00:04:54,300
beforeexit是什么

130
00:04:54,300 --> 00:04:58,140
beforeexit是不是就咱们的退出事件之前

131
00:04:58,140 --> 00:04:59,840
退出之前before的一个勾子

132
00:04:59,840 --> 00:05:00,820
或者会出发这样一个事件

133
00:05:00,820 --> 00:05:03,820
exit就是咱们的程序退出的时候会调用

134
00:05:03,820 --> 00:05:04,700
比如说

135
00:05:04,700 --> 00:05:08,580
比如说这里老师举一个uncut exception

136
00:05:08,580 --> 00:05:09,240
它的例子是什么

137
00:05:09,240 --> 00:05:11,580
我们就来看一下uncut exception

138
00:05:11,580 --> 00:05:12,500
它怎么样去捕捉错误

139
00:05:12,500 --> 00:05:14,320
其实它和beforeexit的warning

140
00:05:14,320 --> 00:05:15,360
它们使用方法都差不多

141
00:05:15,360 --> 00:05:17,360
所以呢 老师这里只介绍 Uncuting Steps

142
00:05:17,360 --> 00:05:18,360
首先

143
00:05:18,360 --> 00:05:21,360
他是做什么的呀 刚才是不是说过了 不获错误的

144
00:05:21,360 --> 00:05:22,360
非常有用

145
00:05:22,360 --> 00:05:24,360
大家一定要好好学这一个方法

146
00:05:24,360 --> 00:05:26,360
咱们在loadds里面去做大型项目的时候

147
00:05:26,360 --> 00:05:29,360
一定要用它去兜底 防止发生一些错误

148
00:05:29,360 --> 00:05:32,360
这个方法非常关键 大不要忽略它

149
00:05:32,360 --> 00:05:35,360
比如说有错误 咱们把它挡出来concel.log

150
00:05:35,360 --> 00:05:37,360
发生错误

151
00:05:37,360 --> 00:05:41,360
呀 好 那这里呢 老师来做一个试验

152
00:05:41,360 --> 00:05:45,360
就这样

153
00:05:45,360 --> 00:06:15,360
bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb

154
00:06:15,360 --> 00:06:16,900
为什么

155
00:06:16,900 --> 00:06:19,500
是不是因为在BBB

156
00:06:19,500 --> 00:06:23,040
已经错误了呀

157
00:06:23,040 --> 00:06:25,280
错误之后导致GS崩溃吧

158
00:06:25,280 --> 00:06:26,780
因为GS它是单线程执行的

159
00:06:26,780 --> 00:06:28,080
只要你在BBB这里挂掉了

160
00:06:28,080 --> 00:06:29,420
后面的代码就不会去执行

161
00:06:29,420 --> 00:06:34,240
所以process.oncut exception

162
00:06:34,240 --> 00:06:34,660
这样一个方法

163
00:06:34,660 --> 00:06:35,900
它不是捕捉同步代码错误

164
00:06:35,900 --> 00:06:37,680
它是去捕捉异部代码错误

165
00:06:37,680 --> 00:06:40,200
专门

166
00:06:40,200 --> 00:06:44,880
捕捉异部代码错误

167
00:06:44,880 --> 00:06:47,040
好 那么我们来看一下 异部代码怎么样去捕捉

168
00:06:47,040 --> 00:06:51,160
好 我们来把刚才http的代码直接扣过来

169
00:06:51,160 --> 00:06:56,560
无关的代码刷掉 好 咱们在他的

170
00:06:56,560 --> 00:06:58,480
位置bounce里面去加入一个

171
00:06:58,480 --> 00:07:02,080
bbb 这里 他是不是异部的呀 同学们

172
00:07:02,080 --> 00:07:05,440
quareless7里面他这样一个毁掉 是不是接受请求的时候才会执行bbb

173
00:07:05,440 --> 00:07:06,720
那么这个时候他是不是会报错

174
00:07:06,720 --> 00:07:11,000
那么procept.onuncut exception 他能够捕捉到吗 咱们测试一下

175
00:07:13,560 --> 00:07:15,360
好 启动了8000端口 我们来访问一下

176
00:07:15,360 --> 00:07:16,640
localhost

177
00:07:16,640 --> 00:07:18,420
8000

178
00:07:18,420 --> 00:07:20,480
好 已经访问了

179
00:07:20,480 --> 00:07:23,280
他直接挂起了 为什么呀 因为咱们报错了 发生错误

180
00:07:23,280 --> 00:07:25,080
BBB is not defined

181
00:07:25,080 --> 00:07:26,620
说明什么问题啊

182
00:07:26,620 --> 00:07:28,400
说明我们的错误是不是已经被捕捉到了呀

183
00:07:28,400 --> 00:07:29,680
所以呢

184
00:07:29,680 --> 00:07:30,960
uncut 一个selfing

185
00:07:30,960 --> 00:07:34,040
专门用于捕捉

186
00:07:34,040 --> 00:07:35,840
eep错误

187
00:07:35,840 --> 00:07:38,400
特别是http 为什么呀 因为咱们的服务

188
00:07:38,400 --> 00:07:41,460
nodejs的大部分应用场景都是做http的服务器

189
00:07:41,720 --> 00:07:44,020
所以这样一个方法就可以捕捉咱们异不代码的错误

190
00:07:44,020 --> 00:07:44,800
还有一种

191
00:07:44,800 --> 00:07:46,840
怎么去说是不是你要去trycatch

192
00:07:46,840 --> 00:07:49,140
但是你所有的代码都去trycatch总有遗漏吧

193
00:07:49,140 --> 00:07:51,200
那么uncatch一个ception它就是一个

194
00:07:51,200 --> 00:07:53,760
兜底方案

195
00:07:53,760 --> 00:07:54,780
好

196
00:07:54,780 --> 00:07:56,060
这里就是process的

197
00:07:56,060 --> 00:07:57,600
介绍我们来回顾一下

198
00:07:57,600 --> 00:07:59,120
process里面是不是有一些属性

199
00:07:59,120 --> 00:08:00,400
argv

200
00:08:00,400 --> 00:08:01,680
它是仿佛一个宿主

201
00:08:01,680 --> 00:08:02,960
就是咱们输入命令的一些

202
00:08:02,960 --> 00:08:03,740
属性

203
00:08:03,740 --> 00:08:05,280
因为v是咱们的用户环境信息

204
00:08:05,280 --> 00:08:06,040
pid 进程号

205
00:08:06,040 --> 00:08:07,320
bateform 操作系统

206
00:08:07,320 --> 00:08:08,340
version 漏的版本

207
00:08:08,340 --> 00:08:09,360
它有哪些方法呢

208
00:08:09,360 --> 00:08:10,140
比如说cwd

209
00:08:10,140 --> 00:08:10,900
咱们的工作目录

210
00:08:11,720 --> 00:08:13,020
excit退出进程

211
00:08:13,020 --> 00:08:14,860
就是把咱们的进程退出或者杀用

212
00:08:14,860 --> 00:08:16,340
它还可以监听一些事件

213
00:08:16,340 --> 00:08:16,740
通过什么

214
00:08:16,740 --> 00:08:17,260
是不是通过en

215
00:08:17,260 --> 00:08:18,500
它可以去监听excit

216
00:08:18,500 --> 00:08:19,640
咱们的退出事件之前

217
00:08:19,640 --> 00:08:20,700
包括去捕捉一些错误

218
00:08:20,700 --> 00:08:21,360
重点关注

219
00:08:21,360 --> 00:08:22,460
这个uncut exception

220
00:08:22,460 --> 00:08:23,200
在一个捕捉错误

221
00:08:23,200 --> 00:08:24,800
是使用非常高频的一个方法

222
00:08:24,800 --> 00:08:27,740
所以这里需要同学们去重点关注

223
00:08:27,740 --> 00:08:29,900
重点

224
00:08:29,900 --> 00:08:32,900
关注

225
00:08:32,900 --> 00:08:34,500
包括去捕捉一些污灵事件

226
00:08:34,500 --> 00:08:36,460
好 这里就是这样一节课的内容

