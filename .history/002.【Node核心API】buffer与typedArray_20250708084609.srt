1
00:00:00,000 --> 00:00:05,880
好 当然前面的在buffer里面的其实提到了一个关键词是什么呢 是不是Type的ray

2
00:00:05,880 --> 00:00:09,220
那么这里呢 我们得看一下Type的ray 它到底是什么东西

3
00:00:09,220 --> 00:00:13,060
其实Type的ray它是gs里面 也就是我们浏览器里面其实存在

4
00:00:13,060 --> 00:00:18,680
而浏览器里面其实也可以去操作二进制的一些数据 因为也涉及到文件的上传等等的一些二进制的操作

5
00:00:18,680 --> 00:00:20,740
好 我们来看一下

6
00:00:20,740 --> 00:00:26,120
那么二进制的数组由三类对象组成 一个是arebuffer对象 它代表原始的二进制数据

7
00:00:26,360 --> 00:00:30,160
第2个呢 type的瑞士图用来去读写我们简单类型的一个二进制数据

8
00:00:30,160 --> 00:00:33,680
第3个呢就是delview 用来读写咱们复杂类型的一个二进制数据

9
00:00:33,680 --> 00:00:36,080
好 首先我们来看一下如何去创建一个我们的rebuffer

10
00:00:36,080 --> 00:00:40,000
其实呢也很简单 那直接去对它进行lue一个rebuffer

11
00:00:40,000 --> 00:00:42,240
然后呢传入它的长度为64

12
00:00:42,240 --> 00:00:45,040
好 那么呢也可以打印出来之后发现呢其实都是0

13
00:00:45,040 --> 00:00:49,920
因为我们去lue它的时候呢 你只传了一个长度 但是它并不知道内容是什么

14
00:00:49,920 --> 00:00:51,440
好 那么如果说

15
00:00:51,440 --> 00:00:53,500
你此时呢需要把这样的一个字物串

16
00:00:53,500 --> 00:00:55,660
我是谁等等的一些后面的一些字幕串呢

17
00:00:55,660 --> 00:00:56,720
如果说想把它给保存

18
00:00:56,720 --> 00:00:57,960
为一个二进制的数据

19
00:00:57,960 --> 00:00:59,360
好 那么怎么办呢

20
00:00:59,360 --> 00:01:01,740
这里呢有两种方法

21
00:01:01,740 --> 00:01:03,240
第一种

22
00:01:03,240 --> 00:01:04,100
直接存在

23
00:01:04,100 --> 00:01:05,160
第一种呢就是

24
00:01:05,160 --> 00:01:07,380
首先呢去定义了咱们这样的一个字幕串

25
00:01:07,380 --> 00:01:09,420
那么我们的字幕串是不是有长度啊

26
00:01:09,420 --> 00:01:09,720
对吧

27
00:01:09,720 --> 00:01:10,080
好

28
00:01:10,080 --> 00:01:10,940
所以说呢

29
00:01:10,940 --> 00:01:12,340
它直接通过UaryBuffer

30
00:01:12,340 --> 00:01:13,940
传入咱们字幕串的一个长度乘以2

31
00:01:13,940 --> 00:01:14,720
为什么乘以2

32
00:01:14,720 --> 00:01:16,940
因为刚才输入这样的一些字符呢

33
00:01:16,940 --> 00:01:18,520
它比如说我是谁

34
00:01:18,520 --> 00:01:20,380
它们的分别呢都在用两个字节

35
00:01:20,380 --> 00:01:22,300
所以说呢你需要去申请

36
00:01:23,060 --> 00:01:25,120
比如说我是谁 这一段字符串 它的长度为

37
00:01:25,120 --> 00:01:29,460
它有15个字符 那么它的长度是多少 说是30个字节 我这里打个比方啊

38
00:01:29,460 --> 00:01:33,820
它有30个字节 那么所以说你是不是需要去开辟一个30个字节这样的一个空间呢

39
00:01:33,820 --> 00:01:36,120
那么把它存在内存中 是一个ss

40
00:01:36,120 --> 00:01:37,140
好 那么此时

41
00:01:37,140 --> 00:01:39,700
那么此时我们的数据是不是还没有存入啊

42
00:01:39,700 --> 00:01:43,800
为什么呀 因为我们去入一个rebuffer的时候呢 只传入了长度 其实并没有传入内容

43
00:01:43,800 --> 00:01:46,360
那么我们内容如何写入咱们的一个二进字里面去呢

44
00:01:46,360 --> 00:01:49,440
其实呢rebuffer并没有提供

45
00:01:49,440 --> 00:01:51,220
类似我们去读写的方法

46
00:01:51,480 --> 00:01:56,340
我们只能够通过设置宿主的内容来将它转换为我们的buff,那么我们如何去做呢

47
00:01:56,340 --> 00:02:01,460
首先你创建了一个buff,也就是通过buff创建的长度呢,假如说为30个字节,对吧

48
00:02:01,460 --> 00:02:05,820
此时你其实需要去new一个unit8,也就是unit8

49
00:02:05,820 --> 00:02:11,960
好,那么你创建了这样的一个unit这样一个宿主之后呢,然后传入你刚才创建了这样一个二进制的一个arraybuffer

50
00:02:11,960 --> 00:02:15,280
然后通过去操作咱们unitarray它的一个下标

51
00:02:15,280 --> 00:02:19,900
然后去修改我们buffer里面的一个内容,因为前面咱们也介绍过了

52
00:02:20,160 --> 00:02:22,460
array buffer呢 它其实是是不是无法

53
00:02:22,460 --> 00:02:27,320
无法进行读写的 对吧 只有type array才可以 那么我们刚才的

54
00:02:27,320 --> 00:02:33,480
咱们刚才的unit bar array 其实呢 它也是属于我们type array的一种 好 那么type

55
00:02:33,480 --> 00:02:35,780
 array呢 到底有几种呢 我们来看一下

56
00:02:35,780 --> 00:02:37,560
我们来看一下type array它的定义

57
00:02:37,560 --> 00:02:40,120
好 我们来看一下它的其实呢

58
00:02:40,120 --> 00:02:41,920
type array指的都是其中之一

59
00:02:41,920 --> 00:02:48,840
比如说 int bar array unit bar array int 16 array 分别的对应8进制 16进制 32进制等等

60
00:02:48,840 --> 00:02:50,120
 咱们那些二进制的一些

61
00:02:50,160 --> 00:02:55,920
操作好 这里就是type rate的一些定义 我们来看一下buffer和它的一些关系

62
00:02:55,920 --> 00:02:59,880
那么首先buffer的实力也是我们utf82 rate它的一个实力

63
00:02:59,880 --> 00:03:04,240
然后buffer对象的内存是拷配到我们的type rate而不是共性

64
00:03:04,240 --> 00:03:09,360
而且你也可以通过咱们的type rate去创建buffer

65
00:03:09,360 --> 00:03:10,900
我们来看一下 如何去创建呢

66
00:03:10,900 --> 00:03:14,220
首先定义了一个rate我们去new的unit16

67
00:03:14,220 --> 00:03:16,520
也就是咱们16进制的一个二进制 长度为2

68
00:03:16,520 --> 00:03:19,600
此时我们直接通过下标来修改它的一个值

69
00:03:19,600 --> 00:03:22,600
好 那么修改它的值之后呢 我们如何去创建一个buffer呢

70
00:03:22,600 --> 00:03:25,380
其实呢 直接就是比如说我们buffer1等于buffer.from

71
00:03:25,380 --> 00:03:28,080
咱们前面讲解的过程中呢 我们的from接受的是什么呀

72
00:03:28,080 --> 00:03:31,740
是不是一个数字里面传入的是字不创 对吧

73
00:03:31,740 --> 00:03:34,180
那么实际上呢 你也可以去传入这样的一个数字

74
00:03:34,180 --> 00:03:36,280
那么这样一个数字呢 其实是代表一个16进制的一个

75
00:03:36,280 --> 00:03:40,180
二进制的一个unit的一个 咱们的一个type的ray 对吧

76
00:03:40,180 --> 00:03:42,220
那么这里呢 是buffer1

77
00:03:42,220 --> 00:03:44,200
那么如果说呢 你通过这样的一种方式

78
00:03:44,200 --> 00:03:46,600
buffer.from传入我们ray.buffer

79
00:03:46,600 --> 00:03:47,840
那么也就是我们

80
00:03:48,320 --> 00:03:52,680
array里面的一个buffer这样的属性 为什么他有这样的一个属性呢 其实原因就是这样一句话

81
00:03:52,680 --> 00:03:57,020
buffer的实力也是unit bar array的实力 说明了他们都是typearray

82
00:03:57,020 --> 00:04:01,240
也就是相当于 如果说我们用oop的思想来讲的话 他的祖先是不是都是typearray啊

83
00:04:01,240 --> 00:04:01,640
 所以说呢

84
00:04:01,640 --> 00:04:04,440
你可以去读取到buffer这样一个属性

85
00:04:04,440 --> 00:04:09,060
接下来就是他所打印的一些信息 我们呢也把它给拷费过来看一下

86
00:04:09,060 --> 00:04:13,920
我们来看一下他到底运行出来的是一个什么样的一个结果

87
00:04:13,920 --> 00:04:20,100
好 这里呢 来重新创建一个cs 我们呢 叫做buffer 然后叫做type.js

88
00:04:20,100 --> 00:04:25,140
好 我们呢 把代码给传填过来 执行一下load

89
00:04:25,140 --> 00:04:27,360
buffer-

90
00:04:27,360 --> 00:04:29,400
好 我们来运行一下

91
00:04:29,400 --> 00:04:39,960
好 大家可以看到 我们呢 第一个已经打印出来了我们的buffer1 那么第二个呢

92
00:04:39,960 --> 00:04:41,920
 也就打印出来了我们的一个buffer2

93
00:04:41,920 --> 00:04:45,240
其实这里呢我们好像打算并没有发现什么新的东西

94
00:04:45,240 --> 00:04:48,940
因为二星这个数据呢在我们的控制台里面呢其实是阅读不是很友好

95
00:04:48,940 --> 00:04:51,320
所以咱们呢先不去管我们先接着往下看

96
00:04:51,320 --> 00:04:56,600
好我们来看一下buffer与迭代器

97
00:04:56,600 --> 00:05:00,420
buffer的实力呢可以使用for off语法呢进行迭代

98
00:05:00,420 --> 00:05:02,660
比如说我们创建了一个buffer

99
00:05:02,660 --> 00:05:04,460
创建了一个长度为3

100
00:05:04,460 --> 00:05:05,740
然后里面的值能为123

101
00:05:05,740 --> 00:05:09,300
其实呢可以使用咱们的一个for off

102
00:05:09,300 --> 00:05:11,620
在那个迭代器对它进行便利

103
00:05:11,620 --> 00:05:14,740
那么foroff是不是咱们1.26里面的一个内容啊 我们就要测试一下吧

104
00:05:14,740 --> 00:05:21,920
我们依然来创建一个buffer

105
00:05:21,920 --> 00:05:25,740
buffer-咱们的一个foroff.js

106
00:05:25,740 --> 00:05:32,040
我们把它给钢贴进来 咱们来执行下load buffer foroff

107
00:05:32,040 --> 00:05:39,540
好 大家可以看到 我们是不是已经打印出来了 要删了 说明了buffer它其实是有一个特性

108
00:05:39,540 --> 00:05:40,080
 就是可变力

109
00:05:40,660 --> 00:05:45,420
我们这里呢 把咱们的ds呢 把它稍微的整理一下 我们把它都给拖到我们buffer在一个文件夹里面去

110
00:05:45,420 --> 00:05:49,880
因为方便到后面同学们呢 下去之后可以去看一下咱们的一个代码

111
00:05:49,880 --> 00:05:54,500
好 那么回到我们的文章中 那么接下来的内容呢 大家不要看咱们的进度条

112
00:05:54,500 --> 00:05:54,900
 还非常的

113
00:05:54,900 --> 00:05:59,780
非常的短 是吧 其实后面内容呢 都是buffer的一些api以及废弃的一些内容

114
00:05:59,780 --> 00:06:05,140
那我们其实在我们nodeds早期的版本中 会使用newbuffer去创建一个咱们的buffer

115
00:06:05,140 --> 00:06:07,660
 那么实际上后面是不是改为了buffer.from啊 对吧

116
00:06:08,060 --> 00:06:11,380
好 那么后面内容其实就是对buffer一些API的一些介绍

117
00:06:11,380 --> 00:06:16,500
那么我们就不去一个一个的去看了 有兴趣去详细的了解buffer的同学可以去看一下

118
00:06:16,500 --> 00:06:22,660
而其实这里也可以去提醒一下同学们 我们在学习核心模块的时候 包括学习其他的一些框架的时候

119
00:06:22,660 --> 00:06:27,000
我们其实可以去把握一个重点 没有必要把那些稳当的都完全的去读完

120
00:06:27,000 --> 00:06:29,060
 因为你只需要知道咱们buffer是做什么的

121
00:06:29,060 --> 00:06:34,420
他解决了什么问题 这样就可以了 因为你的API你即使今天看完了 可能睡了一觉明天你就都给忘记了

122
00:06:34,420 --> 00:06:35,200
 效率是

123
00:06:35,460 --> 00:06:37,500
比较低了 所以说这里也不建议 同学们去

124
00:06:37,500 --> 00:06:41,340
学那么多 在那些细字末节 我们主要是掌握

125
00:06:41,340 --> 00:06:44,420
那么的一个思想 而且呢buffer是一块的哦

126
00:06:44,420 --> 00:06:47,500
因为它涉及到一个二进字 本身就是一个比较枯燥的内容

127
00:06:47,500 --> 00:06:51,580
所以说呢 我们只需要去理解 它是操作我们的二进字就可以了 因为我们后面呢

128
00:06:51,580 --> 00:06:54,140
 咱们在学习htb以及操作文件的时候呢

129
00:06:54,140 --> 00:06:59,780
我们的自然会去接触二进字这部分的内容 好 这里呢就是我们buffer这一节的一个内容

