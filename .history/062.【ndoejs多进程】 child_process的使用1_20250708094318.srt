1
00:00:00,000 --> 00:00:02,160
好,这节课我们就来讲解Chill的Process

2
00:00:02,160 --> 00:00:04,980
那么Chill的Process老师会分为两节课去讲

3
00:00:04,980 --> 00:00:06,520
为什么?因为它比较复杂

4
00:00:06,520 --> 00:00:08,020
它的核心就是五个API

5
00:00:08,020 --> 00:00:10,060
这节课我们会讲前面四个

6
00:00:10,060 --> 00:00:12,560
首先Chill的Process是什么?

7
00:00:12,560 --> 00:00:16,460
它是LodeGIS中用于创建纸巾层的一个模块

8
00:00:16,460 --> 00:00:19,280
那么咱们之前使用的Cluster

9
00:00:19,280 --> 00:00:21,840
就是基于Chill的Process这样一个模块去封装的

10
00:00:21,840 --> 00:00:23,540
所以说它非常的重要

11
00:00:23,540 --> 00:00:26,840
好,我们来看第一个方法

12
00:00:26,840 --> 00:00:27,520
Exec

13
00:00:27,520 --> 00:00:29,140
Exec是什么呢?

14
00:00:29,340 --> 00:00:34,460
一步衍生出一个谢尔然后在谢尔中执行命令且缓冲任何产生的输出运行结束后调运回调函数

15
00:00:34,460 --> 00:00:35,480
什么意思

16
00:00:35,480 --> 00:00:36,500
是不是就是

17
00:00:36,500 --> 00:00:39,320
在nodejs代码里面去执行谢尔命令了

18
00:00:39,320 --> 00:00:41,880
然后他是一步的可以返回咱们输出的结果

19
00:00:41,880 --> 00:00:45,460
他的官方解释稍微有点复杂其实很简单

20
00:00:45,460 --> 00:00:49,300
就是执行谢尔然后呢返回输出那么我们就来看一下怎么样去使用

21
00:00:49,300 --> 00:00:52,380
EXEC

22
00:00:52,380 --> 00:00:56,220
怎么读呢读exec

23
00:00:56,220 --> 00:00:58,260
首先咱们挖一个

24
00:00:58,780 --> 00:01:04,920
Axec from .require

25
00:01:04,920 --> 00:01:09,540
require什么呀是不是chill process .exec

26
00:01:09,540 --> 00:01:13,120
好怎么样使用很简单Axec第1个参数

27
00:01:13,120 --> 00:01:17,220
就咱们命令比如说lsls是什么呀是不是就是执行咱们的

28
00:01:17,220 --> 00:01:20,540
打印出咱们当前目录了文件名或者文件

29
00:01:20,540 --> 00:01:22,340
好第2个参数呢回调

30
00:01:22,340 --> 00:01:24,120
回调里面接受什么

31
00:01:24,120 --> 00:01:25,920
1个1r第2个

32
00:01:27,200 --> 00:01:31,940
第2个 其实呢在咱们的代码提示里面已经有了 第1个 error 第2个呢 stdout

33
00:01:31,940 --> 00:01:32,580
 它是一个死菌

34
00:01:32,580 --> 00:01:36,860
输出是死菌 stdout 是吗 是不是只有咱们的输出out 输出 第3个呢 std

35
00:01:36,860 --> 00:01:37,820
 error 就是咱们的错误信息

36
00:01:37,820 --> 00:01:41,160
stdout std error

37
00:01:41,160 --> 00:01:45,300
好 那我们就把输出打击出来看一下console.org

38
00:01:45,300 --> 00:01:47,940
stdout

39
00:01:47,940 --> 00:01:51,780
就是这么简单 我们看一下

40
00:01:51,780 --> 00:01:54,080
肯定一下 node

41
00:01:55,880 --> 00:01:58,440
load sec.js好

42
00:01:58,440 --> 00:02:00,740
看了没有 std 二次

43
00:02:00,740 --> 00:02:03,040
把咱们 ios需要打印的信息都已经打出来了

44
00:02:03,040 --> 00:02:04,840
他们两个是不是相等了

45
00:02:04,840 --> 00:02:06,640
这两个

46
00:02:06,640 --> 00:02:10,480
对吧好

47
00:02:10,480 --> 00:02:13,280
那

48
00:02:13,280 --> 00:02:18,660
注意咱们怎么怎么样去打印错误信息啊但那些什么面其实他的容错是比较重要

49
00:02:18,660 --> 00:02:21,220
在新手机编程的时候经常会忘记错处理

50
00:02:25,060 --> 00:02:27,620
其实错误处理和咱们的业务代码

51
00:02:27,620 --> 00:02:28,900
一样重要

52
00:02:28,900 --> 00:02:33,060
为什么啊 因为你 因为js他是单线程的 如果说你没有去错误处理错误

53
00:02:33,060 --> 00:02:34,780
 那么后面的代码都不会去执行 都会

54
00:02:34,780 --> 00:02:37,860
直接会导致咱们的服务崩溃 所以你一定要去错误处理错误

55
00:02:37,860 --> 00:02:40,940
这是非常重要 假如说发生错误了 咱们打印出来

56
00:02:40,940 --> 00:02:45,020
std error

57
00:02:45,020 --> 00:02:47,840
好 那我们来看一下错误信息是什么

58
00:02:47,840 --> 00:02:51,180
这里还没有发生错误 是吧 我们怎么样的错误

59
00:02:51,180 --> 00:02:54,240
这里呢 多加个sls 咱们来看一下错误怎么样

60
00:02:54,760 --> 00:02:56,400
nodeexec

61
00:02:56,400 --> 00:02:57,560
好 大家看到没有发生错误了

62
00:02:57,560 --> 00:02:59,860
std error ls command node found

63
00:02:59,860 --> 00:03:02,460
这里呢就是它的一个错误处理

64
00:03:02,460 --> 00:03:03,960
那么其实它还有第二种方式

65
00:03:03,960 --> 00:03:06,700
刚才我们是不是通过回调

66
00:03:06,700 --> 00:03:09,560
回调的方式去接收咱们的输出啊

67
00:03:09,560 --> 00:03:10,420
第二种呢

68
00:03:10,420 --> 00:03:14,620
可以通过流的方式去接收

69
00:03:14,620 --> 00:03:16,420
结果

70
00:03:16,420 --> 00:03:18,160
再有什么好处通过流的方式

71
00:03:18,160 --> 00:03:19,920
因为如果说咱们通过回调

72
00:03:19,920 --> 00:03:21,420
不知道大家还记不记得

73
00:03:21,420 --> 00:03:22,960
类似什么

74
00:03:22,960 --> 00:03:24,720
是不是类似文件读取啊

75
00:03:24,720 --> 00:03:26,780
咱们文件读取是不是可以通过Stream流的形式去读

76
00:03:26,780 --> 00:03:28,720
你不需要把一个文件读完了再返回结果

77
00:03:28,720 --> 00:03:29,720
其实这里是一样的

78
00:03:29,720 --> 00:03:32,320
比如说咱们的需要执行的系统命令非常的

79
00:03:32,320 --> 00:03:34,280
比如说的返回结果很长

80
00:03:34,280 --> 00:03:35,460
你不可能等半天吧

81
00:03:35,460 --> 00:03:37,920
通过流的方式你就可以他边去返回输出

82
00:03:37,920 --> 00:03:40,180
边一边呢咱们去看结果

83
00:03:40,180 --> 00:03:41,040
这样他速度会快一点

84
00:03:41,040 --> 00:03:42,780
怎么样去做呢

85
00:03:42,780 --> 00:03:44,580
哇

86
00:03:44,580 --> 00:03:45,400
Q的等于

87
00:03:45,400 --> 00:03:47,520
1XEC

88
00:03:47,520 --> 00:03:48,560
LS

89
00:03:48,560 --> 00:03:50,680
他只接受一个参数

90
00:03:50,680 --> 00:03:51,500
这里了

91
00:03:51,500 --> 00:03:52,800
咱们注视一下

92
00:03:54,720 --> 00:03:59,120
chill.stdout.on

93
00:03:59,120 --> 00:04:02,720
data 好 我们把data打印出来看一下是什么

94
00:04:02,720 --> 00:04:05,220
data

95
00:04:05,220 --> 00:04:07,220
council.log

96
00:04:07,220 --> 00:04:09,220
data

97
00:04:09,220 --> 00:04:10,220
data 好

98
00:04:10,220 --> 00:04:12,720
data 看到没有

99
00:04:12,720 --> 00:04:15,720
这里是通过stream的形式 通过流的形式去打印

100
00:04:15,720 --> 00:04:17,720
那么怎么样做错误处理

101
00:04:17,720 --> 00:04:21,720
chill.std.on

102
00:04:21,720 --> 00:04:22,720
data

103
00:04:22,720 --> 00:04:27,580
然后呢把 arrow打印出来

104
00:04:27,580 --> 00:04:30,920
好这里呢老师就不去继续做演示了

105
00:04:30,920 --> 00:04:33,220
同学们可以下去自己研究一下好

106
00:04:33,220 --> 00:04:35,000
那我们继续看下一个API

107
00:04:35,000 --> 00:04:39,620
刚才介绍了exec这里是exec think think是什么意思啊是不是同步

108
00:04:39,620 --> 00:04:43,460
其实咱们通过他的名称就可以知道他是做什么了是不是exec同步版本

109
00:04:43,460 --> 00:04:46,780
exec是刚才讲到的是异步了那么我们来

110
00:04:46,780 --> 00:04:48,840
验证一下他到底是不是异步了

111
00:04:50,120 --> 00:05:20,120
咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬咬�

112
00:05:20,120 --> 00:05:20,960
exic file 它是做什么的

113
00:05:20,960 --> 00:05:22,540
是不是也是去执行

114
00:05:22,540 --> 00:05:23,640
谢特定的程序

115
00:05:23,640 --> 00:05:24,480
其实就是谢

116
00:05:24,480 --> 00:05:26,060
那么它的区别是什么呀

117
00:05:26,060 --> 00:05:27,440
就是参数作为宿主传入

118
00:05:27,440 --> 00:05:29,040
不会被罢息解释

119
00:05:29,040 --> 00:05:30,600
因此就有较高的安全性

120
00:05:30,600 --> 00:05:32,040
咱们来体验一下它的关键词

121
00:05:32,040 --> 00:05:33,760
参数作为宿主传入

122
00:05:33,760 --> 00:05:34,220
第二个呢

123
00:05:34,220 --> 00:05:34,820
安全性

124
00:05:34,820 --> 00:05:35,440
它更安全

125
00:05:35,440 --> 00:05:36,060
为什么

126
00:05:36,060 --> 00:05:37,120
咱们就来演示一下

127
00:05:37,120 --> 00:05:40,320
exic file

128
00:05:40,320 --> 00:05:41,880
好

129
00:05:41,880 --> 00:05:44,160
我们现在看一下exic file怎么样去执行

130
00:05:44,160 --> 00:05:45,560
首先呢

131
00:05:45,560 --> 00:05:47,520
我们刚才是不是执行的ls命令

132
00:05:47,520 --> 00:05:48,620
它是不是会有一些参数

133
00:05:48,620 --> 00:05:49,140
比如说-c

134
00:05:49,140 --> 00:05:51,260
-c是什么呢

135
00:05:51,260 --> 00:05:52,140
-c是什么意思呀

136
00:05:52,140 --> 00:05:54,460
-c其实就是对咱们的一个文件

137
00:05:54,460 --> 00:05:56,200
或者文件夹进行一个格式化

138
00:05:56,200 --> 00:05:58,220
这里呢咱们可能太少了

139
00:05:58,220 --> 00:05:58,820
没什么明显的效果

140
00:05:58,820 --> 00:05:59,340
不过这里不重要

141
00:05:59,340 --> 00:06:01,000
我们演示重点是参数

142
00:06:01,000 --> 00:06:01,800
怎么传入参数

143
00:06:01,800 --> 00:06:02,200
好

144
00:06:02,200 --> 00:06:06,120
首先呢引入exig file

145
00:06:06,120 --> 00:06:07,980
这里是不是es6的复制结构啊

146
00:06:07,980 --> 00:06:08,300
对吧

147
00:06:08,300 --> 00:06:09,180
好exig file

148
00:06:09,180 --> 00:06:11,080
命令ls参数

149
00:06:11,080 --> 00:06:12,640
它呢接受三个参数

150
00:06:12,640 --> 00:06:13,340
第一个

151
00:06:13,340 --> 00:06:13,820
效

152
00:06:13,820 --> 00:06:14,700
第二个

153
00:06:14,700 --> 00:06:15,700
RX

154
00:06:15,700 --> 00:06:17,260
它呢以数组来形式

155
00:06:17,260 --> 00:06:18,260
存在

156
00:06:18,260 --> 00:06:21,080
第3个 回掉 callback 好

157
00:06:21,080 --> 00:06:23,640
咱们刚才之前命令是-c

158
00:06:23,640 --> 00:06:25,180
回掉

159
00:06:25,180 --> 00:06:34,140
第1个 arrow 第2个 std out 第3个 std arrow

160
00:06:34,140 --> 00:06:37,460
好 我们来打印出来看一下它的输出是什么

161
00:06:37,460 --> 00:06:39,260
std out

162
00:06:39,260 --> 00:06:46,680
好 咱们来打印打印一下 low的

163
00:06:47,700 --> 00:06:51,180
exic fire 好 大家看到没有 已经输出了

164
00:06:51,180 --> 00:06:54,240
是不是和咱们刚才执行的ls-c是一样的

165
00:06:54,240 --> 00:06:59,270
那我就来看一下 在exic里面 我们需要去给ls-c怎么样 是不咱们直接-c呀

166
00:06:59,270 --> 00:07:00,440
 好 咱们再来打印一下

167
00:07:00,440 --> 00:07:04,940
可以吧

168
00:07:04,940 --> 00:07:07,460
好

169
00:07:07,460 --> 00:07:13,500
好 刚才我们是不是看到了

170
00:07:15,140 --> 00:07:21,280
Xfile他和exec的区别是不是就是参数以宿主的形式传入啊就有点内随咱们js里面

171
00:07:21,280 --> 00:07:26,140
扩和apply的一个区别还有一个比较重要的就是他就有较高安全性这里怎么去理解

172
00:07:26,140 --> 00:07:32,040
我们通过exec去举例子比如说比如说我们写了一个pass

173
00:07:32,040 --> 00:07:34,080
等于点点斜杠

174
00:07:34,080 --> 00:07:39,460
好那么我们在exec的时候呢去接收一个动态的参数

175
00:07:39,460 --> 00:07:40,480
比如说

176
00:07:41,260 --> 00:07:45,100
ls-c这里呢老是用es6的语法去写ls-c

177
00:07:45,100 --> 00:07:47,140
空格多了

178
00:07:47,140 --> 00:07:49,460
他是不是会把咱们上一集目录的

179
00:07:49,460 --> 00:07:51,760
文件列表展出来我们来看一下

180
00:07:51,760 --> 00:07:53,540
exec

181
00:07:53,540 --> 00:07:56,880
loadexec.js

182
00:07:56,880 --> 00:07:57,900
大家看了没有

183
00:07:57,900 --> 00:08:03,540
他呢会把咱们chill process的上一集目录chapter4下面的所有文件集文件夹打印出来

184
00:08:03,540 --> 00:08:04,560
好

185
00:08:04,560 --> 00:08:09,420
那么他的安全性问题出在哪里呢

186
00:08:10,700 --> 00:08:14,160
哪里不安全 其实不安全的就是这个怕是他这样一个动态的变量 你比如说啊

187
00:08:14,160 --> 00:08:14,660
 比如说

188
00:08:14,660 --> 00:08:15,740
我这样去洗

189
00:08:15,740 --> 00:08:17,660
啊m-f

190
00:08:17,660 --> 00:08:20,760
同学们还记不记得按钢2f他的命令是什么意思

191
00:08:20,760 --> 00:08:27,070
是不是删除啊 那么如果说在用户动态输入的时候 他加入一个反斜杠 然后按钢2f

192
00:08:27,070 --> 00:08:27,700
 是不是就会把咱们

193
00:08:27,700 --> 00:08:34,020
咱们目录里面些文件夹给删掉 这样是非常危险的 那么exic file 他通过宿主的形式去传入参数的话

194
00:08:34,020 --> 00:08:34,880
 他就会过滤

195
00:08:34,880 --> 00:08:38,120
exic file

196
00:08:38,120 --> 00:08:40,100
会自动

197
00:08:40,700 --> 00:08:42,520
过滤一些

198
00:08:42,520 --> 00:08:45,040
敏感的字幕串

199
00:08:45,040 --> 00:08:46,460
比如

200
00:08:46,460 --> 00:08:48,140
发音斜杠

201
00:08:48,140 --> 00:08:49,300
分号等等

202
00:08:49,300 --> 00:08:50,140
再呢

203
00:08:50,140 --> 00:08:51,180
它的安全性就会提高

204
00:08:51,180 --> 00:08:52,220
以防止用户输入的时候

205
00:08:52,220 --> 00:08:53,320
它输入一些恶意的代码

206
00:08:53,320 --> 00:08:54,220
把你的系统电脚

207
00:08:54,220 --> 00:08:55,340
某些给刷掉

208
00:08:55,340 --> 00:08:56,560
这里呢就是AXXFile

209
00:08:56,560 --> 00:08:58,420
我们来看一下

210
00:08:58,420 --> 00:08:59,320
Spoin

211
00:08:59,320 --> 00:09:01,120
Spoin它是什么呢

212
00:09:01,120 --> 00:09:02,660
Spoin方法创建一个子进程

213
00:09:02,660 --> 00:09:03,540
来执行特定的系统

214
00:09:03,540 --> 00:09:05,080
用法呢和AXXFile类似

215
00:09:05,080 --> 00:09:05,660
但是呢

216
00:09:05,660 --> 00:09:07,000
它不能以回调函数的方式

217
00:09:07,000 --> 00:09:08,160
去接收输出

218
00:09:08,160 --> 00:09:09,740
怎么去理解

219
00:09:09,740 --> 00:09:11,100
我们直接来看代码

220
00:09:11,100 --> 00:09:13,700
好

221
00:09:13,700 --> 00:09:14,460
这里呢

222
00:09:14,460 --> 00:09:14,860
老师吧

223
00:09:14,860 --> 00:09:16,260
例子已经准备好了

224
00:09:16,260 --> 00:09:17,660
我们首先来看一下

225
00:09:17,660 --> 00:09:20,300
首先咱们require一个spine

226
00:09:20,300 --> 00:09:21,180
通过chill process

227
00:09:21,180 --> 00:09:22,540
然后去执行ls

228
00:09:22,540 --> 00:09:23,660
那么他呢

229
00:09:23,660 --> 00:09:24,520
刚才咱们是不是讲到了

230
00:09:24,520 --> 00:09:25,740
spine它的用法

231
00:09:25,740 --> 00:09:26,880
与exigfile方法类似

232
00:09:26,880 --> 00:09:27,460
说明一个什么问题

233
00:09:27,460 --> 00:09:27,860
它是不是

234
00:09:27,860 --> 00:09:29,520
它的参数也是以数据形式存在

235
00:09:29,520 --> 00:09:30,620
但是它不能接受callback

236
00:09:30,620 --> 00:09:31,500
它只能监听事件

237
00:09:31,500 --> 00:09:32,240
监听事件是什么

238
00:09:32,240 --> 00:09:32,800
是不是就是咱们的

239
00:09:32,800 --> 00:09:33,140
stream

240
00:09:33,140 --> 00:09:33,840
好

241
00:09:33,840 --> 00:09:34,640
让我来看一下

242
00:09:34,640 --> 00:09:35,760
spinels

243
00:09:35,760 --> 00:09:36,600
第二个参数

244
00:09:36,600 --> 00:09:37,880
-c

245
00:09:37,880 --> 00:09:38,520
好

246
00:09:38,520 --> 00:09:39,240
咱们来看一下

247
00:09:39,240 --> 00:09:39,940
它的输出是什么

248
00:09:39,940 --> 00:09:43,620
console.log data

249
00:09:43,620 --> 00:09:45,220
好

250
00:09:45,220 --> 00:09:45,940
那我们来看一下

251
00:09:45,940 --> 00:09:46,900
它的输出

252
00:09:46,900 --> 00:09:48,220
loadsupon.js

253
00:09:48,220 --> 00:09:49,020
大家看到没有

254
00:09:49,020 --> 00:09:49,600
data buffer

255
00:09:49,600 --> 00:09:50,840
后面它是一个buffer对象

256
00:09:50,840 --> 00:09:52,320
那么buffer是什么呀

257
00:09:52,320 --> 00:09:54,220
buffer是不是二性自然loadjs里面的

258
00:09:54,220 --> 00:09:55,440
那么如果说

259
00:09:55,440 --> 00:09:56,560
我们要去把它转换成

260
00:09:56,560 --> 00:09:57,180
我们能看懂了

261
00:09:57,180 --> 00:09:58,100
是不是需要去把它

262
00:09:58,100 --> 00:09:59,240
通过tube

263
00:09:59,240 --> 00:10:01,440
使均方法转为

264
00:10:01,440 --> 00:10:03,200
咱们能认识的编码

265
00:10:03,200 --> 00:10:04,420
是什么utf8

266
00:10:04,420 --> 00:10:06,020
好

267
00:10:06,020 --> 00:10:07,200
我们来看一下

268
00:10:07,200 --> 00:10:08,340
大家看到没有

269
00:10:08,340 --> 00:10:09,320
是不是已经转换成

270
00:10:09,320 --> 00:10:11,040
我们能够去识别的制度创

271
00:10:11,040 --> 00:10:11,640
对吧

272
00:10:11,640 --> 00:10:11,940
好

273
00:10:11,940 --> 00:10:13,340
这里呢就是对

274
00:10:13,340 --> 00:10:14,760
Spine的一个介绍

275
00:10:14,760 --> 00:10:16,160
我们呢来回顾一下

276
00:10:16,160 --> 00:10:16,740
这节课的内容

277
00:10:16,740 --> 00:10:18,540
首先我们是不是讲的四个方法

278
00:10:18,540 --> 00:10:19,280
一个Exec

279
00:10:19,280 --> 00:10:20,300
它呢

280
00:10:20,300 --> 00:10:20,940
是什么

281
00:10:20,940 --> 00:10:21,920
是不是执行义部的命令

282
00:10:21,920 --> 00:10:22,280
然后呢

283
00:10:22,280 --> 00:10:24,860
它既可以通过回掉横数的方式去

284
00:10:24,860 --> 00:10:25,680
输出

285
00:10:25,680 --> 00:10:26,900
也可以通过事件的形式输出

286
00:10:26,900 --> 00:10:27,440
但是

287
00:10:27,440 --> 00:10:29,380
它是不是参数里面

288
00:10:29,380 --> 00:10:30,000
可以随便输入

289
00:10:30,000 --> 00:10:31,080
安全性不是很高

290
00:10:31,080 --> 00:10:33,140
比如说我们要执行LS

291
00:10:33,140 --> 00:10:34,120
是不是直接这样

292
00:10:34,120 --> 00:10:35,200
输入咱们的参数

293
00:10:35,200 --> 00:10:36,320
假如你输入一个变量

294
00:10:36,320 --> 00:10:37,500
可能会有问题

295
00:10:37,500 --> 00:10:38,340
再会有安全性问题

296
00:10:38,340 --> 00:10:41,280
那么ExecSync它是Exec的同步版本

297
00:10:41,280 --> 00:10:43,000
ExecFile它的安全性是不是更高

298
00:10:43,000 --> 00:10:44,700
因为它的参数以数字的形式传入

299
00:10:44,700 --> 00:10:47,760
那么Spine是不是和ExecFile很类似

300
00:10:47,760 --> 00:10:50,680
但是它只能通过事件监听的形式来输出结果

301
00:10:50,680 --> 00:10:53,200
而且它是以什么形式接收数据

302
00:10:53,200 --> 00:10:54,820
是不是Buffer

303
00:10:54,820 --> 00:10:57,060
好 这里就是这节课的内容

