1
00:00:00,000 --> 00:00:03,700
好 从这节课开始呢 我们就来学习nodejs的核心API

2
00:00:03,700 --> 00:00:06,560
我们来看一下咱们这一系列课程我们的一个目标是什么

3
00:00:06,560 --> 00:00:08,820
理解nodejs常用的核心API

4
00:00:08,820 --> 00:00:11,880
第二个呢 加深对nodejs设计思想的一个理解

5
00:00:11,880 --> 00:00:15,020
我们的目标呢 其实非常的清晰 也很简单

6
00:00:15,020 --> 00:00:17,800
我们呢 就是去学习nodejs的一些常用和核心的API

7
00:00:17,800 --> 00:00:18,920
为什么要学习它呢

8
00:00:18,920 --> 00:00:20,680
主要呢 有两点原因

9
00:00:20,680 --> 00:00:23,180
第一个 你只有理解了核心的API

10
00:00:23,180 --> 00:00:25,840
才能对我们nodejs的使用更加的得心应手

11
00:00:25,840 --> 00:00:28,780
就跟咱们起楼房一样 你只要把地基打好了

12
00:00:28,780 --> 00:00:30,060
你的房子它才能建的更多

13
00:00:30,060 --> 00:00:33,900
第二个 面试中也会经常遇到关于nodejs API的使用

14
00:00:33,900 --> 00:00:36,980
其实我们得换一个角度去理解这个问题

15
00:00:36,980 --> 00:00:40,300
为什么这么说 因为平时在面试中 面试官一定不会问你

16
00:00:40,300 --> 00:00:44,660
nodejs的一些API是如何使用的 对吧 比如说http这样一个模块有哪些方法

17
00:00:44,660 --> 00:00:46,700
包括了比如说stream它有哪些方法

18
00:00:46,700 --> 00:00:48,500
面试官肯定不会这么去问

19
00:00:48,500 --> 00:00:55,920
他一定会去一些其他的方式来考察你对nodejs的它的一些核心模块以及API的一些理解

20
00:00:56,680 --> 00:00:59,500
那么从一些你对API的理解中就可以得出

21
00:00:59,500 --> 00:01:02,100
你到底是一个什么层次的一个基础水平

22
00:01:02,100 --> 00:01:02,980
所以说呢

23
00:01:02,980 --> 00:01:03,800
nodejs的核心API

24
00:01:03,800 --> 00:01:05,520
虽然说我们这期的课程目标很简单

25
00:01:05,520 --> 00:01:05,860
但是呢

26
00:01:05,860 --> 00:01:06,980
它却非常的重要

27
00:01:06,980 --> 00:01:09,280
也是你后面的nodejs进阶了一个基石

28
00:01:09,280 --> 00:01:09,680
好

29
00:01:09,680 --> 00:01:10,720
那么我们废话不多说

30
00:01:10,720 --> 00:01:13,080
我们直接来进入它的一个学习

31
00:01:13,080 --> 00:01:15,720
我们如何来学习咱们这一系列课程呢

32
00:01:15,720 --> 00:01:16,680
其实呢

33
00:01:16,680 --> 00:01:19,140
因为我们这一系列课程呢

34
00:01:19,140 --> 00:01:21,440
咱们只会去讲它的API

35
00:01:21,440 --> 00:01:22,620
所以说我们不会分为一张

36
00:01:22,620 --> 00:01:23,820
第二张第三张等等

37
00:01:23,820 --> 00:01:25,880
我们会咱们会把nodejs

38
00:01:25,880 --> 00:01:29,980
就是它的官方站点里面的它的一个文档 我们会把它里面一些比较核心的

39
00:01:29,980 --> 00:01:34,580
API咱们把它给挑出来去给同学们做一个讲解 如果说我没有去讲的地方

40
00:01:34,580 --> 00:01:37,400
 那就说明了大家可以去把它跳过 或者说呢

41
00:01:37,400 --> 00:01:44,320
客家自己去看一下就够了 首先我们来看第1个咱们的核心的API 也就是Logs的一个核心模块

42
00:01:44,320 --> 00:01:48,920
那就是buffer缓冲去 我们来看一下它的一个概念

43
00:01:48,920 --> 00:01:54,560
在引入type.ray之前javascript的语言没有用于读取或操作二进制数据的机制

44
00:01:55,060 --> 00:01:57,280
所以这里我们可以得到

45
00:01:57,280 --> 00:02:01,040
在很早以前GS其实是没有操作二进制数据的一个能力

46
00:02:01,040 --> 00:02:02,300
那么引入buffer在一个类之后

47
00:02:02,300 --> 00:02:04,700
它是不是就可以去操作我们的二进制的一个数据

48
00:02:04,700 --> 00:02:06,860
那么它通常用于我们TCP的流

49
00:02:06,860 --> 00:02:07,880
TCP是什么

50
00:02:07,880 --> 00:02:08,800
是不是就是我们的网络

51
00:02:08,800 --> 00:02:11,520
文件系统就是操作本地的文件

52
00:02:11,520 --> 00:02:14,740
以及与八位的字节流进行交互

53
00:02:14,740 --> 00:02:17,240
也就是进行咱们的一些二进制的运算

54
00:02:17,240 --> 00:02:20,520
而且我们重点看这里

55
00:02:20,520 --> 00:02:23,080
buffer的大小在创建时确定且无法更改

56
00:02:23,080 --> 00:02:24,280
大家思考一下

57
00:02:24,280 --> 00:02:27,160
我们借实去操作咱们的一些比如说数组

58
00:02:27,160 --> 00:02:28,080
包括对象的时候

59
00:02:28,080 --> 00:02:31,880
那么数组和对象它能不能够去确定咱们的一个大小

60
00:02:31,880 --> 00:02:32,540
而且无法更改

61
00:02:32,540 --> 00:02:35,840
好像不行对吧

62
00:02:35,840 --> 00:02:39,740
假如说我们去创建一个数组

63
00:02:39,740 --> 00:02:43,300
比如说我们去挖一个AR等于一个new array

64
00:02:43,300 --> 00:02:47,840
其实如果我们再去创建的话

65
00:02:47,840 --> 00:02:48,900
比如说我们去new array一个8

66
00:02:48,900 --> 00:02:51,200
咱们是不是可以创建一个长度为8的一个数组

67
00:02:51,200 --> 00:02:51,700
对吧

68
00:02:51,700 --> 00:02:52,840
如果说不幸的同学们

69
00:02:52,840 --> 00:02:54,520
我们来来打印一下它的长度

70
00:02:54,520 --> 00:02:57,200
大家可以看到它的长度为8

71
00:02:57,200 --> 00:02:59,760
那么我们能不能够修改它的大小呢 其实可以

72
00:02:59,760 --> 00:03:02,320
假如我们给它去进行一个push

73
00:03:02,320 --> 00:03:05,120
我们来push一个数字1

74
00:03:05,120 --> 00:03:07,440
我们再来看一下它的一个长度是什么

75
00:03:07,440 --> 00:03:08,960
大家可以看到长度速度变为9了

76
00:03:08,960 --> 00:03:10,760
说明我们的gs里面其实呢

77
00:03:10,760 --> 00:03:13,840
其实咱们是不是数字和对象都不能够去固定它的长度

78
00:03:13,840 --> 00:03:15,120
但是buffer可以

79
00:03:15,120 --> 00:03:18,960
而且你的buffer内在全局的重用域中

80
00:03:18,960 --> 00:03:21,760
你无需去require 因为它就是在我们的global下面

81
00:03:21,760 --> 00:03:24,760
这里呢就是它的一段相当于是给我们提供的一段demon

82
00:03:24,760 --> 00:03:26,540
我们把它给粘贴过来

83
00:03:26,540 --> 00:03:27,800
咱们把它给运行一下看一下

84
00:03:27,800 --> 00:03:30,520
好 这里呢我们创建了一个buffer.js

85
00:03:30,520 --> 00:03:32,340
我这里呢把它给粘过来

86
00:03:32,340 --> 00:03:33,540
然后呢去对它进行

87
00:03:33,540 --> 00:03:34,600
咱们来运行一下

88
00:03:34,600 --> 00:03:36,880
load buffer.js

89
00:03:36,880 --> 00:03:40,740
好 大家可以看到了咱们的控制台

90
00:03:40,740 --> 00:03:41,760
是不是打印出来的一些东西

91
00:03:41,760 --> 00:03:43,740
那么它们分别对应了咱们的

92
00:03:43,740 --> 00:03:46,300
刚才打印的这样一些buffer

93
00:03:46,300 --> 00:03:48,400
好 我们呢一个一个的来看

94
00:03:48,400 --> 00:03:49,260
所以我们来看第一横

95
00:03:49,260 --> 00:03:52,220
Const的一个Buff1等于Buffer.alloc

96
00:03:52,220 --> 00:03:53,360
传入了一个参数10

97
00:03:53,360 --> 00:03:54,680
好 我们来看一下它的解释

98
00:03:54,680 --> 00:03:55,880
创建的一个长度为10

99
00:03:55,880 --> 00:03:57,680
那么也就是说我们Buffer下面呢

100
00:03:57,680 --> 00:03:58,860
有一个alloc这样一个方法

101
00:03:58,860 --> 00:03:59,920
那么传入了第一个参数呢

102
00:03:59,920 --> 00:04:01,400
就为10它的长度

103
00:04:01,400 --> 00:04:02,800
好 那么第二个参数呢

104
00:04:02,800 --> 00:04:03,540
可能就是它的值

105
00:04:03,540 --> 00:04:04,660
如果说我们没有传的话

106
00:04:04,660 --> 00:04:05,940
默认用0去填充

107
00:04:05,940 --> 00:04:06,680
好 我们把它给打一出来

108
00:04:06,680 --> 00:04:07,680
好 我们来看一下Buffer2

109
00:04:07,680 --> 00:04:08,940
Buffer2呢 这里呢

110
00:04:08,940 --> 00:04:09,580
和我们刚才一样

111
00:04:09,580 --> 00:04:10,740
只是它接受的第二个参数为1

112
00:04:10,740 --> 00:04:12,580
说明它创建一个长度为10

113
00:04:12,580 --> 00:04:14,400
而且呢 用01去填充的一个Buffer

114
00:04:14,400 --> 00:04:16,620
好 接下来我们来看下Buffer3

115
00:04:16,620 --> 00:04:20,540
它放进了一个长度为实且未初始化的Buffer

116
00:04:20,540 --> 00:04:23,140
那么这样一个方法调用比Buffer.lock更快

117
00:04:23,140 --> 00:04:26,200
但是返回的Buffer实力可能会包含旧的数据

118
00:04:26,200 --> 00:04:29,640
因此你需要去使用Fail或者Write去进行重写

119
00:04:29,640 --> 00:04:31,480
这里就是Buffer3 我们来看下Buffer4

120
00:04:31,480 --> 00:04:36,360
那么Buffer4我们的Buffer是不是不仅可以去传入你的一个长度

121
00:04:36,360 --> 00:04:37,460
以及它填充的一个值

122
00:04:37,460 --> 00:04:38,780
你还可以去传入一个什么样

123
00:04:38,780 --> 00:04:40,580
通过Buffer.from传入一个数组

124
00:04:40,580 --> 00:04:43,120
那么传入数组说明此时我们的Buffer的长度是不是为3

125
00:04:43,120 --> 00:04:45,120
然后填充的值呢分别对应的是123

126
00:04:45,120 --> 00:04:47,480
包括呢你也可以去传入一个制服创

127
00:04:47,480 --> 00:04:48,760
那么制服创呢默认了

128
00:04:48,760 --> 00:04:51,820
它呢是一个utf-8的一个编码

129
00:04:51,820 --> 00:04:54,360
那么如果说呢你去传入一个test

130
00:04:54,360 --> 00:04:56,180
后面呢传入一个lusting1

131
00:04:56,180 --> 00:04:57,180
那么lusting1是什么呢

132
00:04:57,180 --> 00:05:00,060
它其实是我们的一种和utf-8有点类似

133
00:05:00,060 --> 00:05:01,980
它呢也是一种特殊的编码

134
00:05:01,980 --> 00:05:04,200
特殊的编码

135
00:05:04,200 --> 00:05:09,600
好我们呢把它的打印信息给同步一下

136
00:05:09,600 --> 00:05:11,700
8和3 8和4

137
00:05:11,700 --> 00:05:17,020
我们再来重新执行一下咱们的buff.js

138
00:05:17,020 --> 00:05:21,560
我们的分别来对应咱们刚才创建了一个buff.lock

139
00:05:21,560 --> 00:05:22,000
我们来看一下

140
00:05:22,000 --> 00:05:25,180
第一个这里代表了它是一个buff

141
00:05:25,180 --> 00:05:27,620
然后里面是不是都是填充了长度为10的0

142
00:05:27,620 --> 00:05:31,520
那么我们再来看一下我们的一个buff2

143
00:05:31,520 --> 00:05:33,240
我们的buff2

144
00:05:33,240 --> 00:05:35,520
这里有点问题

145
00:05:35,520 --> 00:05:37,240
咱们刚才粘贴错了

146
00:05:40,240 --> 00:05:42,860
好,我们来重新执行下弄的buffer

147
00:05:42,860 --> 00:05:44,480
好,接下来我们来看一下第二个

148
00:05:44,480 --> 00:05:48,180
我们第二个呢,创建的是一个长度为10的buffer

149
00:05:48,180 --> 00:05:49,160
然后呢,填充的是为1

150
00:05:49,160 --> 00:05:50,940
大家可以看到这里都是01001,对吧

151
00:05:50,940 --> 00:05:52,260
好,我们来看一下第三个

152
00:05:52,260 --> 00:05:54,160
通过一个allog and save

153
00:05:54,160 --> 00:05:57,300
创建的呢,其实呢就是一个

154
00:05:57,300 --> 00:06:00,320
没有去进行初始化的一个buffer,对吧

155
00:06:00,320 --> 00:06:02,400
好,所以呢,它传入的就是它

156
00:06:02,400 --> 00:06:03,560
好,我们来看一下buffer4

157
00:06:03,560 --> 00:06:05,840
我们的buffer呢,还可以去接受一个宿主,123

158
00:06:05,840 --> 00:06:07,000
对吧

159
00:06:07,000 --> 00:06:11,000
好 那么后面的Buff5呢 Buff6呢也都是同理

160
00:06:11,000 --> 00:06:14,300
那么这里呢 我可能需要给同学们去讲解一下什么是UTF-8

161
00:06:14,300 --> 00:06:16,720
因为有的同学可能对这个编码呢 可能还不是很理解

162
00:06:16,720 --> 00:06:19,660
因为我们可能说从我们学习前端到现在

163
00:06:19,660 --> 00:06:23,540
咱们的编辑器也好 是不是到处都充促着UTF-8

164
00:06:23,540 --> 00:06:26,500
但是呢 有的同学可能一直不理解到底什么是UTF-8

165
00:06:26,500 --> 00:06:28,720
对吧 所以说呢 我们呢就来一起看一下

166
00:06:28,720 --> 00:06:32,980
好 那么我们来看一下到底什么是UTF-8

167
00:06:32,980 --> 00:06:36,000
好 这里呢是我找到了一篇文章 我觉得写得非常好

168
00:06:36,000 --> 00:06:41,800
首先 我们先来了解一个概念 叫做ascll制服机 那么它是什么东西呢

169
00:06:41,800 --> 00:06:48,700
它这种制服机一共包含128个制服 那么这128个制服分别用7个比特位

170
00:06:48,700 --> 00:06:55,200
因为我们的计算机是不是最小的单位就是比特 对吧 因为如果说是计算机专业的同学可能会有所了解

171
00:06:55,200 --> 00:07:01,400
因为我们的计算机所有的 咱们不管是cpu的运算也好 它的最底层是不是都是基于咱们的0101

172
00:07:01,400 --> 00:07:01,800
 对吧

173
00:07:01,800 --> 00:07:06,160
我们的0101所占的一个位置就是一个byte 相当于是我们二级管的一个原理

174
00:07:06,160 --> 00:07:13,580
为了操作方便呢 我们128个字符是不是刚好是2的七十方还是六十方 同学们可以自己算一下

175
00:07:13,580 --> 00:07:18,180
那么为了操作方便呢 我们不妨用一个字节 因为我们一般的一个字节等于八个比特位

176
00:07:18,180 --> 00:07:22,540
所以说我们一般的用八个比特位来代表我们的ascl这样一个编码

177
00:07:22,540 --> 00:07:27,140
那么其实如果说我们世界上没有这么多的国家 比如说只有英文

178
00:07:27,140 --> 00:07:31,760
因为我们这128个字符里面就包括ABCD 包括你一些常用符号 逗号 句号

179
00:07:31,760 --> 00:07:32,620
空格等等

180
00:07:32,620 --> 00:07:35,080
那么如果说我们全世界的通用性语言都是

181
00:07:35,080 --> 00:07:36,580
英语的话

182
00:07:36,580 --> 00:07:38,800
那么其实这128个字符是完全足够的

183
00:07:38,800 --> 00:07:39,140
但是

184
00:07:39,140 --> 00:07:41,860
我们需要解决一个问题是什么呢

185
00:07:41,860 --> 00:07:44,740
比如说咱们的中文有多少个汉字

186
00:07:44,740 --> 00:07:45,780
是不是非常非常多

187
00:07:45,780 --> 00:07:47,680
包括全世界还有日本韩国等等

188
00:07:47,680 --> 00:07:48,840
这样的一些国家

189
00:07:48,840 --> 00:07:50,820
在一些非英语为母语的一些国家

190
00:07:50,820 --> 00:07:52,240
那么它的一些字符都需要去表示

191
00:07:52,240 --> 00:07:52,920
那么怎么办呢

192
00:07:52,920 --> 00:07:54,380
你128个字符表示

193
00:07:54,380 --> 00:07:55,480
是不是不够啊

194
00:07:55,480 --> 00:07:55,720
对吧

195
00:07:55,720 --> 00:07:58,840
所以后来又出现了unicode这样的一种编码

196
00:07:58,840 --> 00:08:00,160
那么unicode大家可以看到了

197
00:08:00,160 --> 00:08:03,420
他其实呢可以包含上百万个字符

198
00:08:03,420 --> 00:08:06,100
那么他为什么可以包含上百万个字符呢

199
00:08:06,100 --> 00:08:06,680
我们来看一下

200
00:08:06,680 --> 00:08:09,420
其实呢原理呢也非常的简单

201
00:08:09,420 --> 00:08:12,700
我们的asc字符集

202
00:08:12,700 --> 00:08:15,660
他是不是占用了我们的一个字节

203
00:08:15,660 --> 00:08:15,960
对吧

204
00:08:15,960 --> 00:08:16,800
那么一个字节呢

205
00:08:16,800 --> 00:08:18,760
其实可以去代表128个字符

206
00:08:18,760 --> 00:08:19,440
好

207
00:08:19,440 --> 00:08:20,740
但是呢我们unicode呢

208
00:08:20,740 --> 00:08:22,520
他需要用几个字节才能去表示呢

209
00:08:22,520 --> 00:08:24,600
他呢需要用三个字节才能去表示

210
00:08:24,600 --> 00:08:26,360
那么我们三个字节可以表示

211
00:08:26,360 --> 00:08:27,640
多少内容呢

212
00:08:27,640 --> 00:08:27,880
对吧

213
00:08:27,880 --> 00:08:30,240
那么我们三个字节是不是有多少个位样

214
00:08:30,240 --> 00:08:31,920
我们一个字节有八位

215
00:08:31,920 --> 00:08:32,200
对吧

216
00:08:32,200 --> 00:08:34,760
那么咱们的三个字节就是二十四位

217
00:08:34,760 --> 00:08:35,840
大家可以去算一下

218
00:08:35,840 --> 00:08:37,100
二的二十四次方式多少

219
00:08:37,100 --> 00:08:40,860
所以说unicode就可以去表示非常非常多的一个字符

220
00:08:40,860 --> 00:08:43,520
那么全世界咱们的一些语言都可以去包含进来

221
00:08:43,520 --> 00:08:48,100
那么后来在unicode里面也遇到了一个比较棘手的问题

222
00:08:48,100 --> 00:08:49,060
什么问题呢

223
00:08:49,060 --> 00:08:49,800
我们一起来看一下

224
00:08:49,800 --> 00:08:52,380
刚才其实我们讲到了

225
00:08:52,380 --> 00:08:56,900
在unicode里面是不是它的一个字符会占据三个字节

226
00:08:56,900 --> 00:08:57,160
对吧

227
00:08:57,160 --> 00:08:59,760
那么假如说我们现在有这样的一个数换

228
00:08:59,760 --> 00:09:01,080
一个英文单字A

229
00:09:01,080 --> 00:09:01,680
一个数字3

230
00:09:01,680 --> 00:09:02,620
一个中文里面的中

231
00:09:02,620 --> 00:09:04,320
包括一个咱们人民币的一个符号

232
00:09:04,320 --> 00:09:06,160
如果说我们用unicode去表示的话

233
00:09:06,160 --> 00:09:09,040
我们是不是一共需要多少位去进行存储

234
00:09:09,040 --> 00:09:11,340
咱们一共需要多少个字节去存储

235
00:09:11,340 --> 00:09:12,820
是不是需要12个呀

236
00:09:12,820 --> 00:09:15,720
因为它需要使用咱们的

237
00:09:15,720 --> 00:09:19,300
它需要使用四个unicode的字符

238
00:09:19,300 --> 00:09:21,740
那么一个unicode的字符又占用咱们三个字节

239
00:09:21,740 --> 00:09:23,340
所以说一起需要12个字节

240
00:09:23,340 --> 00:09:24,600
而且大家可以去观察到

241
00:09:24,600 --> 00:09:27,040
因为我们的英文字母A

242
00:09:27,040 --> 00:09:29,040
它非常的小

243
00:09:29,040 --> 00:09:31,340
所以说它只需要一个字节就够用了

244
00:09:31,340 --> 00:09:33,240
后面的两个字节其实都会造成

245
00:09:33,240 --> 00:09:34,480
是不是造成咱们空间的一个浪费

246
00:09:34,480 --> 00:09:37,060
假如说我们表示四个字符

247
00:09:37,060 --> 00:09:38,940
那么我们就浪费了几个字节

248
00:09:38,940 --> 00:09:39,360
大家看一下

249
00:09:39,360 --> 00:09:41,380
那么灰色部分其实都是浪费的字节

250
00:09:41,380 --> 00:09:43,880
浪费了一个 两个 三个 四个 五个

251
00:09:43,880 --> 00:09:45,940
那么如果说你的文章非常庞大

252
00:09:45,940 --> 00:09:47,020
是不是会浪费大量的内存

253
00:09:47,020 --> 00:09:49,120
这是unicode遇到了第一个问题

254
00:09:49,120 --> 00:09:50,720
好 接下来我们来看一下

255
00:09:50,720 --> 00:09:52,340
第二个问题

256
00:09:52,340 --> 00:09:53,580
因为刚才我们提到了

257
00:09:53,580 --> 00:09:54,500
使用unicode呢

258
00:09:54,500 --> 00:09:55,160
它会有

259
00:09:55,160 --> 00:09:56,440
如果说我们使用固定的

260
00:09:56,440 --> 00:09:58,240
自己来去存储一个支付串的话

261
00:09:58,240 --> 00:09:59,480
可能会有内存的浪费

262
00:09:59,480 --> 00:09:59,900
所以说呢

263
00:09:59,900 --> 00:10:00,480
有的人呢

264
00:10:00,480 --> 00:10:01,640
就想出了另外一种办法

265
00:10:01,640 --> 00:10:02,440
什么办法呢

266
00:10:02,440 --> 00:10:05,640
我们能不能够把灰色的这部分

267
00:10:05,640 --> 00:10:06,300
把它给拴掉

268
00:10:06,300 --> 00:10:07,080
对吧

269
00:10:07,080 --> 00:10:08,060
那么假如说我们转换之后

270
00:10:08,060 --> 00:10:08,920
我们来进行检测

271
00:10:08,920 --> 00:10:09,480
那么如果说

272
00:10:09,480 --> 00:10:10,100
你这个位置呢

273
00:10:10,100 --> 00:10:10,540
什么都没有表示

274
00:10:10,540 --> 00:10:11,260
我们就把它给拴掉

275
00:10:11,260 --> 00:10:12,220
那么这样一种方法

276
00:10:12,220 --> 00:10:13,440
到底能不能够行得通呢

277
00:10:13,440 --> 00:10:14,480
那么假如说

278
00:10:14,480 --> 00:10:15,840
我们把灰色去拴掉之后

279
00:10:15,840 --> 00:10:16,320
变成了它

280
00:10:16,320 --> 00:10:18,200
那么大家能不能够看出来

281
00:10:18,200 --> 00:10:19,720
它到底是什么呀

282
00:10:19,720 --> 00:10:20,320
其实呢

283
00:10:20,320 --> 00:10:21,280
还是解决不了我们的问题

284
00:10:21,280 --> 00:10:21,820
为什么呢

285
00:10:21,820 --> 00:10:23,980
因为我们的一个字符

286
00:10:23,980 --> 00:10:27,160
是不是有可能由2A和3117所组成的

287
00:10:27,160 --> 00:10:27,740
对吧

288
00:10:27,740 --> 00:10:29,740
是不是有可能由3A和DA所组成的

289
00:10:29,740 --> 00:10:31,360
那么你这样的一种情况

290
00:10:31,360 --> 00:10:32,000
你能够看出来

291
00:10:32,000 --> 00:10:34,620
它到底是2A31还是3ADA吗

292
00:10:34,620 --> 00:10:35,940
看不出来吧

293
00:10:35,940 --> 00:10:38,640
所以说咱们的方案2也失败

294
00:10:38,640 --> 00:10:42,400
因此后来出现了一种新的编码方案

295
00:10:42,400 --> 00:10:43,000
叫做什么呢

296
00:10:43,000 --> 00:10:44,280
叫做UTF-8

297
00:10:44,280 --> 00:10:47,760
那么UTF-8是如何解决这样一个问题的呢

298
00:10:47,760 --> 00:10:50,280
那么UTF-8其实非常简单

299
00:10:50,280 --> 00:10:51,640
那么如果说我们只有一个字节

300
00:10:51,640 --> 00:10:52,840
那么最高的比特为0

301
00:10:52,840 --> 00:10:53,840
那么如果说有多个字节

302
00:10:53,840 --> 00:10:55,420
那么就从第一个字节的最高位开始

303
00:10:55,420 --> 00:10:57,400
那么连续有几个比特为了值为1

304
00:10:57,400 --> 00:10:58,920
那么就使用几个字节码

305
00:10:58,920 --> 00:11:01,120
剩下的字节均以10开头

306
00:11:01,120 --> 00:11:03,100
那么其实它要描述的意思是什么呢

307
00:11:03,100 --> 00:11:04,080
它要描述的意思就是

308
00:11:04,080 --> 00:11:06,140
当你看到我UTF编码的时候

309
00:11:06,140 --> 00:11:07,160
那么你就知道

310
00:11:07,160 --> 00:11:08,660
我这样一个字符有几个

311
00:11:08,660 --> 00:11:09,680
咱们字节所组成

312
00:11:09,680 --> 00:11:12,420
所以说这里就是UTF-8它的一个由来

313
00:11:12,420 --> 00:11:16,120
那么到这里我们就简单的来回顾一下

314
00:11:16,120 --> 00:11:17,840
咱们这节课所讲解的一个内容

315
00:11:17,840 --> 00:11:19,760
我们这节课其实主要就是讲解了

316
00:11:19,760 --> 00:11:22,000
咱们buffer这样的一个api对吧

317
00:11:22,000 --> 00:11:23,760
那么buffer它的核心是什么呢

318
00:11:23,760 --> 00:11:25,440
是不是操作我们二进制的一个数据

319
00:11:25,440 --> 00:11:26,760
而且它有一个特点是什么呀

320
00:11:26,760 --> 00:11:28,360
是不是buffer的大小在创建时确定

321
00:11:28,360 --> 00:11:29,280
而且呢无法更改

322
00:11:29,280 --> 00:11:31,240
那么我们GS的宿主和对象是不是创建之后

323
00:11:31,240 --> 00:11:32,560
可以随便去改它的大小啊

324
00:11:32,560 --> 00:11:34,920
而且呢buffer它这个类在全局的作用域中

325
00:11:34,920 --> 00:11:36,240
你无需去require

326
00:11:36,240 --> 00:11:38,280
包括我们刚才在大门里面去演示的时候

327
00:11:38,280 --> 00:11:39,240
大家是不是可以看到

328
00:11:39,240 --> 00:11:41,520
我们的buffer是不是并没有去require

329
00:11:41,520 --> 00:11:43,640
好这里呢就是我们这节课的内容

