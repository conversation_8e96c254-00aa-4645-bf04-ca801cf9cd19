1
00:00:00,000 --> 00:00:04,320
好,接下来我们进入redis最重要的一部分学习

2
00:00:04,320 --> 00:00:06,000
也就是redis的数据类型

3
00:00:06,000 --> 00:00:07,660
我们一个数据库来讲

4
00:00:07,660 --> 00:00:09,780
最关键的是不是就是它的一个数据结构

5
00:00:09,780 --> 00:00:12,440
那么之前我们是不是刚才已经安装过的redis

6
00:00:12,440 --> 00:00:13,240
已经热了一下声

7
00:00:13,240 --> 00:00:15,540
接下来我们来真正进入redis的世界

8
00:00:15,540 --> 00:00:17,900
那么我们来讲解redis数据类型之前

9
00:00:17,900 --> 00:00:21,020
我们先来大力的看一下redis它

10
00:00:21,020 --> 00:00:23,760
有哪些操作可以怎么去玩

11
00:00:23,760 --> 00:00:26,260
我们再随便挑一点命令

12
00:00:26,260 --> 00:00:27,120
咱们找一下感觉

13
00:00:27,120 --> 00:00:28,000
大不要着急

14
00:00:28,700 --> 00:00:32,700
这结合呢 我们主要来找一下感觉 然后呢 我们会去一个一个的去介绍

15
00:00:32,700 --> 00:00:33,820
 Redis 里面所有的数据类型

16
00:00:33,820 --> 00:00:36,380
好 首先我们来看一下

17
00:00:36,380 --> 00:00:42,560
怎么样去获取符合规则的件名的列表 我们刚才是不是已经有一个命名叫做

18
00:00:42,560 --> 00:00:45,340
 case 新 对吧 它可以获取所有的件

19
00:00:45,340 --> 00:00:46,880
那么实际上呢

20
00:00:46,880 --> 00:00:52,760
我们 case 不仅可以使用新 我们还可以使用 pattern pattern 是什么 是不是通配服

21
00:00:52,760 --> 00:00:54,040
 有一点类似咱们的

22
00:00:54,040 --> 00:00:56,600
正则 表达式 他主要支持

23
00:00:57,620 --> 00:00:58,380
用四种方式

24
00:00:58,380 --> 00:01:00,440
一个问号匹配一个字符

25
00:01:00,440 --> 00:01:01,720
第二个呢新

26
00:01:01,720 --> 00:01:03,260
通配任意字符

27
00:01:03,260 --> 00:01:05,040
第三个

28
00:01:05,040 --> 00:01:06,060
括号

29
00:01:06,060 --> 00:01:09,400
括号是不是咱们通过正则去匹配邮箱的时候

30
00:01:09,400 --> 00:01:10,940
使用的比较多啊

31
00:01:10,940 --> 00:01:14,000
比如说B到D A B C D B和D都可以

32
00:01:14,000 --> 00:01:16,060
括号兼的任意一个字符

33
00:01:16,060 --> 00:01:17,840
第四个反鞋缸

34
00:01:17,840 --> 00:01:19,900
比如说我们需要去转移我们要转移问号

35
00:01:19,900 --> 00:01:22,960
是不是感觉和咱们的正则比较是非常的类似

36
00:01:22,960 --> 00:01:25,020
那么这里呢会有一些

37
00:01:25,020 --> 00:01:26,040
提示

38
00:01:26,040 --> 00:01:27,060
比如说

39
00:01:27,620 --> 00:01:30,340
比如说Case会建立Redis中所有的键

40
00:01:30,340 --> 00:01:32,220
那么当数量过多时会影响性能

41
00:01:32,220 --> 00:01:33,080
所以不建议使用

42
00:01:33,080 --> 00:01:33,640
什么意思

43
00:01:33,640 --> 00:01:36,300
咱们Case这样一个方法少用

44
00:01:36,300 --> 00:01:38,580
后面会有更好的方法去便利我们所谓的属性

45
00:01:38,580 --> 00:01:40,820
还有一个就是老师之前介绍过的Redis

46
00:01:40,820 --> 00:01:42,540
它的命令是不区分大小型的

47
00:01:42,540 --> 00:01:44,520
好 咱们来看一下第二个

48
00:01:44,520 --> 00:01:46,940
怎么样去判断一个键它是否存在

49
00:01:46,940 --> 00:01:48,460
命令很简单

50
00:01:48,460 --> 00:01:49,120
Exist

51
00:01:49,120 --> 00:01:50,360
然后加上K的名称

52
00:01:50,360 --> 00:01:51,580
比如说我们去

53
00:01:51,580 --> 00:01:54,560
Exist

54
00:01:54,560 --> 00:01:56,560
Exist

55
00:01:56,560 --> 00:01:58,560
咱们来看一下

56
00:01:58,560 --> 00:02:01,560
exists

57
00:02:01,560 --> 00:02:03,560
什么呢 比如说ABC

58
00:02:03,560 --> 00:02:05,560
是不是有这样一个K啊 如果说有就返回1

59
00:02:05,560 --> 00:02:06,560
大家看到没有 返回1

60
00:02:06,560 --> 00:02:08,560
那么咱们来看一下有没有

61
00:02:08,560 --> 00:02:09,560
ABCD1呢 没有

62
00:02:09,560 --> 00:02:11,560
所以它返回0 这里很简单

63
00:02:11,560 --> 00:02:13,560
好 我们来看一下下一个 双除

64
00:02:13,560 --> 00:02:14,560
双除一个K

65
00:02:14,560 --> 00:02:16,560
比如说我们来

66
00:02:16,560 --> 00:02:17,560
第二

67
00:02:17,560 --> 00:02:19,560
第二一个ABC

68
00:02:19,560 --> 00:02:20,560
我们就把它双了

69
00:02:20,560 --> 00:02:21,560
好 返回1

70
00:02:21,560 --> 00:02:23,560
是不是说明成功了呀

71
00:02:23,560 --> 00:02:25,560
好 那我们来看一下

72
00:02:25,560 --> 00:02:27,760
k 新 看一下还有没有

73
00:02:27,760 --> 00:02:29,560
ABC是不是没有呢

74
00:02:29,560 --> 00:02:32,760
好 这里呢就是双除了一个方法

75
00:02:32,760 --> 00:02:35,060
好 那么如何双除多个k呢

76
00:02:35,060 --> 00:02:39,760
其实比如说我们来看一下现在还有哪些k

77
00:02:39,760 --> 00:02:41,560
比如说abc5

78
00:02:41,560 --> 00:02:44,060
我们就把abc和1000这两个k给双掉 怎么双

79
00:02:44,060 --> 00:02:47,060
dl abc1000

80
00:02:47,060 --> 00:02:49,060
好 我们来看一下

81
00:02:49,060 --> 00:02:53,060
integ1 说明只双掉了一个

82
00:02:54,560 --> 00:02:55,580
我们来看一下ks

83
00:02:55,580 --> 00:03:04,800
其实呢1代表双处成功 我们ABC和1000其实已经双处了 这里呢就是DL它去双处多个k的一种方法

84
00:03:04,800 --> 00:03:07,360
其实还有另外一种方法 就是利用Linux的管道

85
00:03:07,360 --> 00:03:11,460
XAR XRX 其实管道呢

86
00:03:11,460 --> 00:03:18,620
它是Linux mini里面去谢尔脚本的一种技巧 感兴趣的同学呢可以去查一下它的用法

87
00:03:18,620 --> 00:03:19,400
 其实非常的巧妙

88
00:03:20,420 --> 00:03:24,520
我们再来看一下获取件值的数据类型是什么一种方式

89
00:03:24,520 --> 00:03:29,640
比如说我们在GS里面怎么样去判断

90
00:03:29,640 --> 00:03:31,680
一个

91
00:03:31,680 --> 00:03:34,500
一个东西的他的一个类型是否通过typeoff

92
00:03:34,500 --> 00:03:35,780
那么我们来看一下

93
00:03:35,780 --> 00:03:36,540
首先

94
00:03:36,540 --> 00:03:37,580
咱们来去set

95
00:03:37,580 --> 00:03:39,100
set命令是做什么的就是去

96
00:03:39,100 --> 00:03:40,900
写入属性

97
00:03:40,900 --> 00:03:42,180
比如说咱们去set一个for

98
00:03:42,180 --> 00:03:42,940
值为1

99
00:03:42,940 --> 00:03:45,000
那我们来判断一下for它是什么类型的

100
00:03:45,000 --> 00:03:46,540
type for

101
00:03:46,540 --> 00:03:47,300
死菌

102
00:03:47,300 --> 00:03:48,320
说明我们的redis

103
00:03:48,580 --> 00:03:51,660
去所存储的数据类型就是制度创

104
00:03:51,660 --> 00:03:54,980
那么我们再来看一下

105
00:03:54,980 --> 00:03:57,020
还有一个命令叫做l push

106
00:03:57,020 --> 00:03:59,340
l push是什么呢

107
00:03:59,340 --> 00:04:03,940
l push 它实际上是一种列表类型有一点类似于我们GS里面的数据

108
00:04:03,940 --> 00:04:07,520
这里我们后面会去详细的介绍这种数据结构

109
00:04:07,520 --> 00:04:10,600
我们先来看一下咱们push的一个bar

110
00:04:10,600 --> 00:04:11,880
给它一个值为1

111
00:04:11,880 --> 00:04:14,940
咱们来看一下bar它是一个什么类型

112
00:04:14,940 --> 00:04:16,220
type

113
00:04:17,760 --> 00:04:23,300
那么此时呢,它是一个list,这一个数据结构

114
00:04:23,300 --> 00:04:41,200
好,这里呢就是咱们radis数据类型,咱们先去感受一下它,我们来总结一下,首先呢,case可以呢去写入一些part,有点类似咱们的政策,去便利或者说筛选我们的k,然后呢通过exist可以去判断一个键,它是不是存在

115
00:04:41,200 --> 00:04:43,100
通过dl命令呢可以去双除

116
00:04:43,100 --> 00:04:47,460
通过type呢可以去获取我们radis的一些数据类型

117
00:04:47,760 --> 00:04:50,660
好 这里就是这一节课的内容

