1
00:00:00,000 --> 00:00:02,080
好 这节课呢 我们就来看一下

2
00:00:02,080 --> 00:00:03,520
自定义启动

3
00:00:03,520 --> 00:00:05,340
那么什么是自动义启动呢

4
00:00:05,340 --> 00:00:08,000
是不是我们自己需要去定义一些启动的文件呢

5
00:00:08,000 --> 00:00:10,180
我们需要去在咱们应用启动的时候去做一些事情

6
00:00:10,180 --> 00:00:12,040
好 那么我们来看一下它的解释

7
00:00:12,040 --> 00:00:14,660
我们常常需要在应用启动期间去进行一些初始化工作

8
00:00:14,660 --> 00:00:17,020
等初始化完成后应用才能启动成功

9
00:00:17,020 --> 00:00:19,000
并且来开始对外提供服务

10
00:00:19,000 --> 00:00:21,600
那么大家可以看一下我们之前是不是定义过一个什么呀

11
00:00:21,600 --> 00:00:22,420
是不是APP.js

12
00:00:22,420 --> 00:00:24,460
我们呢也在应用启动的时候做了一些初始化工作

13
00:00:24,460 --> 00:00:25,960
那么我们来看一下我们之前是做了什么

14
00:00:25,960 --> 00:00:28,560
我们之前是不是在APP.js里面

15
00:00:28,560 --> 00:00:34,700
是不是通过 比如说我们去监听一个request 或者response 包括咱们监听了一次我们的server

16
00:00:34,700 --> 00:00:39,400
也就是http服务启动完毕之后就会通知我们的app 告诉你们我们的服务启动完毕了

17
00:00:39,400 --> 00:00:40,840
 大家看到这里其实已经打印出来了

18
00:00:40,840 --> 00:00:47,000
那么我们这里呢 其实呢 有另外一种方式 框架呢 提供了一个统一的入口文件

19
00:00:47,000 --> 00:00:49,040
 app.js 进行我们启动过程的自定义

20
00:00:49,040 --> 00:00:50,840
这个文件呢 它返回一个类

21
00:00:50,840 --> 00:00:52,360
那么我们目前是不是

22
00:00:52,360 --> 00:00:54,420
返回了什么呀 返回一个方形

23
00:00:54,920 --> 00:00:59,360
那么方形呢 它可以说是无状态的 但是呢 我们的类 它呢 状态会更丰富一点

24
00:00:59,360 --> 00:01:02,860
 所以说呢 如果说我们需要去做的启动了 比如说我们需要去定制化

25
00:01:02,860 --> 00:01:05,420
 我们一些启动 包括了很复杂 我们就可以考虑使用类 而不是方形

26
00:01:05,420 --> 00:01:10,230
那么我们通过定义啊 这个类中的生命周期方法来执行啊 咱们应用过程中的一些初手工作

27
00:01:10,230 --> 00:01:13,260
 那么大家看到生命周期函数 这几个字的时候大家可以想到什么呀

28
00:01:13,260 --> 00:01:19,360
大家可以想什么 是不是可以想到react和view啊 我们的 我们的react和view里面是不是也有生命周期函数

29
00:01:19,360 --> 00:01:22,940
 那么其实这里的app.js 他其实也是啊 学习了他们的诗形

30
00:01:23,200 --> 00:01:25,640
说说我们的前端的发展可以说是越来越的童话

31
00:01:25,640 --> 00:01:29,100
你学你学好一门技术然后再去学其他的其实大家都是抄了抄去

32
00:01:29,100 --> 00:01:31,600
天下五公里那抄就看你会抄不会抄

33
00:01:31,600 --> 00:01:34,000
这里我们来看一下有哪些生命周期函数

34
00:01:34,000 --> 00:01:35,260
比如说config where load

35
00:01:35,260 --> 00:01:36,300
config did load

36
00:01:36,300 --> 00:01:37,800
did load where ready did ready

37
00:01:37,800 --> 00:01:39,640
包括了sever did ready before close

38
00:01:39,640 --> 00:01:41,540
那么大家看到这些生命周期的

39
00:01:41,540 --> 00:01:44,140
他的一些命名是不是和read的非常像

40
00:01:44,140 --> 00:01:45,440
比如说component did mount

41
00:01:45,440 --> 00:01:46,640
component where mount

42
00:01:46,640 --> 00:01:49,740
那么我们就来看一下怎么样去定义我们的一个生命周期函数

43
00:01:49,740 --> 00:01:52,840
这里我们就来先写一下

44
00:01:53,200 --> 00:02:20,820
刚才我们是不是提到了需要去export有什么,是不是我们需要去导出一个内容,所以我们先来定一个class,比如说我们就叫做app-boot-hook,什么意思啊,是不是我们来启动一些勾扎,然后把它给导出,module.export,把咱们的一个内容导出,好,那么首先呢,它会有一些生命周期的方法,对吧,其中有一个叫做config,config,vr-load,

45
00:02:22,600 --> 00:02:24,140
好 那么他是做什么的呢

46
00:02:24,140 --> 00:02:26,440
那么在一个生命周期啊 他是做什么的啊

47
00:02:26,440 --> 00:02:27,720
config workload

48
00:02:27,720 --> 00:02:29,000
当我们光听名字

49
00:02:29,000 --> 00:02:30,540
就是是不是在我们的

50
00:02:30,540 --> 00:02:31,820
配置加载

51
00:02:31,820 --> 00:02:33,600
之前的一个咱们的一个

52
00:02:33,600 --> 00:02:34,880
勾长也就是说

53
00:02:34,880 --> 00:02:38,980
在一个生命周期发生的时候 我们的配置是不是还没有加载完成 加载完成前的一瞬间

54
00:02:38,980 --> 00:02:40,000
 也就是说

55
00:02:40,000 --> 00:02:40,780
在这里

56
00:02:40,780 --> 00:02:43,600
我们是不是还有最后一次机会去修改配置

57
00:02:43,600 --> 00:02:51,020
对吧 那么他的应用场景是什么呀

58
00:02:51,020 --> 00:02:52,560
他有哪些应用场景

59
00:02:52,600 --> 00:02:53,980
其实这里呢我给大家举个例子

60
00:02:53,980 --> 00:02:56,360
我们来看一下我们的配置文件

61
00:02:56,360 --> 00:02:58,680
其实我们的配置文件

62
00:02:58,680 --> 00:03:00,760
你比如说我们我们目前要哪些配置文件呢

63
00:03:00,760 --> 00:03:01,260
对吧

64
00:03:01,260 --> 00:03:02,360
比如说我们有一个case

65
00:03:02,360 --> 00:03:03,760
就是我们应用了一个关键字

66
00:03:03,760 --> 00:03:04,680
包括了Middleware

67
00:03:04,680 --> 00:03:05,900
我们引入了哪些中间键

68
00:03:05,900 --> 00:03:07,420
Security我们的安全策略

69
00:03:07,420 --> 00:03:09,500
Log我们怎么样去打印我们的日子

70
00:03:09,500 --> 00:03:11,580
View呢就是我们的一个模板引擎的配置

71
00:03:11,580 --> 00:03:13,260
那么假如说我们有这样一种情况

72
00:03:13,260 --> 00:03:16,000
有什么情况下我们需要通过GS去操作我们的配置呢

73
00:03:16,000 --> 00:03:19,040
你比如说我们在config里面去传了一段加密的文字

74
00:03:19,040 --> 00:03:21,860
然后我们可能需要用一个函数去把它给解密

75
00:03:21,860 --> 00:03:24,560
这里呢就可以用到咱们的config window的这样一个钩子

76
00:03:24,560 --> 00:03:26,160
所以这种情况

77
00:03:26,160 --> 00:03:27,980
比如说我们连接买收口数据库的时候

78
00:03:27,980 --> 00:03:28,600
我们的密码

79
00:03:28,600 --> 00:03:30,460
你不想识明文

80
00:03:30,460 --> 00:03:31,560
不需要别人知道你的密码是什么

81
00:03:31,560 --> 00:03:32,760
你就可以以一段加密的注串

82
00:03:32,760 --> 00:03:33,960
丢到配置文件里面去

83
00:03:33,960 --> 00:03:36,360
然后呢咱们在config window的里面去把它给解密

84
00:03:36,360 --> 00:03:40,900
这里呢就是它的一种应用场景

85
00:03:40,900 --> 00:03:41,840
那么还有其他的应用场景呢

86
00:03:41,840 --> 00:03:43,840
大家可以去思考一下

87
00:03:43,840 --> 00:03:45,180
或者说自己做项目的时候呢

88
00:03:45,180 --> 00:03:46,640
也可以去体会一下

89
00:03:46,640 --> 00:03:47,160
好

90
00:03:47,160 --> 00:03:48,540
那么我们在这里把它给打印一下

91
00:03:48,540 --> 00:03:49,180
它那个时机

92
00:03:49,180 --> 00:03:50,480
这里呢我们就不去

93
00:03:50,480 --> 00:03:51,140
不去演示

94
00:03:51,140 --> 00:03:54,460
就是我们去解密码这个密码在哪个过程的 因为我们现在是不是还没有连接数据库啊

95
00:03:54,460 --> 00:03:57,540
我们来把它打印一下

96
00:03:57,540 --> 00:04:02,400
好config workload 好 接下来我们再来介绍下一个

97
00:04:02,400 --> 00:04:07,780
那么呢 既然有wareload 这里呢 还需要给大家去提示一下是什么呢

98
00:04:07,780 --> 00:04:10,600
就是我们的config workload 呢 他只能是

99
00:04:10,600 --> 00:04:12,380
只能是同步的

100
00:04:12,380 --> 00:04:14,440
好 那么怎么理解呢

101
00:04:14,440 --> 00:04:18,540
其实呢 我们在appboardhooks里面去加入一些生命周期 有些生命周期他是同步的

102
00:04:18,540 --> 00:04:20,060
 有一些生命周期的他可以是一步的

103
00:04:20,320 --> 00:04:23,140
比如说我们为了漏的他说他一定会对应什么呀

104
00:04:23,140 --> 00:04:24,680
是不是一定会对应我们的

105
00:04:24,680 --> 00:04:29,800
地的都来因为你因为我们的react是不是有我要吗所以接下来就是地的吗

106
00:04:29,800 --> 00:04:34,400
所以这里呢我们有一个什么方法了我们呢可以是而且呢他可以是一步的叫做地的

107
00:04:34,400 --> 00:04:36,700
漏的这里他代表什么意思

108
00:04:36,700 --> 00:04:39,780
他代表什么意思其实就是代表我们的配置

109
00:04:39,780 --> 00:04:42,600
已经加载

110
00:04:42,600 --> 00:04:44,120
完成

111
00:04:44,120 --> 00:04:47,200
好那么其实这里呢就很好理解对吧

112
00:04:47,460 --> 00:04:48,740
那么加载完成

113
00:04:48,740 --> 00:04:53,120
加载完成 我们这里有什么应用场景呢 其实其中有一个应用场景呢 我这里可以给大家去提示一下

114
00:04:53,120 --> 00:04:53,860
 你比如说

115
00:04:53,860 --> 00:04:58,460
比如说 我们的配置已经加载完成 说明一个什么问题 我们是不是可以获取完整的配置文件

116
00:04:58,460 --> 00:05:01,280
 说明了配置文件到这里开始它就会固定不变了

117
00:05:01,280 --> 00:05:04,620
因为你已经加载完成了 我们所有操作时机 操作时机已经过去了

118
00:05:04,620 --> 00:05:06,920
所以说在d-load里面 我们的配置是固定不变的

119
00:05:06,920 --> 00:05:08,460
我们的配置

120
00:05:08,460 --> 00:05:09,740
非常的

121
00:05:09,740 --> 00:05:13,060
稳定 所以我们其中一个应用场景是什么 我们可以

122
00:05:14,600 --> 00:05:17,240
可以读取配置 然后呢 咱们运行一些

123
00:05:17,240 --> 00:05:23,440
其他的服务 你比如说 我们在1GG之外 我们可能需要读取他的一个配置

124
00:05:23,440 --> 00:05:24,840
 然后去提一些第三方的服务

125
00:05:24,840 --> 00:05:28,310
但是第三方的服务需要去读取配置 所以你就可以在DedMonk里面去做 这里呢

126
00:05:28,310 --> 00:05:29,860
 就是他的一个应用场景

127
00:05:29,860 --> 00:05:40,080
好 那么我们同样的把它给打进出来 因为等一下我们要看一下他们的一个加载顺序到底是怎么样的

128
00:05:40,080 --> 00:05:43,340
DedLoad 这里呢 就是配置加载完成的一个情况

129
00:05:44,600 --> 00:05:47,480
那么这里呢 其实还有一个视频这些叫做什么呢 也是比较常用了 叫做

130
00:05:47,480 --> 00:05:49,400
D的 叫做WearReady

131
00:05:49,400 --> 00:05:56,920
那么WearReady顾名思义 是不是我们的应用

132
00:05:56,920 --> 00:06:00,720
啊 在完成之前的一个时机呢 其实呢 我们准确来讲 它的一个时机是什么呢

133
00:06:00,720 --> 00:06:04,480
就是我们所有插件已经加载

134
00:06:04,480 --> 00:06:08,840
完成 然后呢 可能应用整体还没有Ready

135
00:06:12,260 --> 00:06:13,440
什么意思

136
00:06:13,440 --> 00:06:15,240
因为我们的应用是不是分为什么呀

137
00:06:15,240 --> 00:06:17,100
是不是分为应用插件中间键

138
00:06:17,100 --> 00:06:18,200
那么你的插件加载完成

139
00:06:18,200 --> 00:06:20,260
但是你的应用是不是还整体没有加载完成

140
00:06:20,260 --> 00:06:21,920
所以我们的服务现在还不能启动

141
00:06:21,920 --> 00:06:23,200
只是说它代表的核心是什么

142
00:06:23,200 --> 00:06:25,500
核心是插件已经加载完成

143
00:06:25,500 --> 00:06:28,120
那么在这个阶段我们有哪些应用场景呢

144
00:06:28,120 --> 00:06:29,720
我们可以去做什么

145
00:06:29,720 --> 00:06:32,300
其实我们在应用

146
00:06:32,300 --> 00:06:34,580
没有完全ready之前

147
00:06:34,580 --> 00:06:37,020
其实它有一点类似咱们react的什么设计

148
00:06:37,020 --> 00:06:39,420
类似我们的component

149
00:06:39,420 --> 00:06:42,100
那么在这样一个生命周期里面呢 我们其实可以去

150
00:06:42,100 --> 00:06:46,320
我们可以在这里去先 我们可以先初始化

151
00:06:46,320 --> 00:06:52,580
一些数据你比如说我们去服务的时候 可能呢 我们会先去读取一些缓重或者什么样的

152
00:06:52,580 --> 00:06:53,980
 我们可以在这里去初始化一些数据

153
00:06:53,980 --> 00:06:57,220
这里呢 就是well ready 它有作用 好 我们同样再来把它打印给大家出来

154
00:06:57,220 --> 00:07:02,380
那么同样的啊 那么well ready 它会对应什么呀

155
00:07:02,380 --> 00:07:06,620
well ready 它一定会对应什么呀 一定会对应我们的一个dead ready 对吧

156
00:07:07,460 --> 00:07:10,840
所以说他这个生命这些函数其实就是完全是模仿我们的一样

157
00:07:10,840 --> 00:07:14,920
那我这里呢其实就很好理解就是我们的应用呢已经起用完毕了

158
00:07:14,920 --> 00:07:19,660
我们应用已经起用完毕

159
00:07:19,660 --> 00:07:21,600
那么一个他的应用场景是什么呢

160
00:07:21,600 --> 00:07:28,700
应用场景是什么

161
00:07:28,700 --> 00:07:30,860
其实大家我们之前是不是讲过呀

162
00:07:30,860 --> 00:07:32,540
我们创建context有哪些方式

163
00:07:32,540 --> 00:07:36,260
比如说我们在我们可以在哪里去读取我们一个context

164
00:07:36,260 --> 00:07:36,840
对吗

165
00:07:37,460 --> 00:07:38,820
比如说我们的

166
00:07:38,820 --> 00:07:41,480
比如说我们的controller里面对吧

167
00:07:41,480 --> 00:07:43,200
我们通过this可以拿到我们的context

168
00:07:43,200 --> 00:07:45,720
但是如果说我们想在app.js里面

169
00:07:45,720 --> 00:07:47,060
去提前拿到咱们的一个context

170
00:07:47,060 --> 00:07:50,580
那么context它是不是在我们的一个请求过程中才会去创建了

171
00:07:50,580 --> 00:07:53,800
那么我们的app.js执行的时候咱们的http是不是还没有请求

172
00:07:53,800 --> 00:07:55,480
那么我们只是需要context怎么办对吧

173
00:07:55,480 --> 00:07:58,000
我们的其中应用场景之一呢

174
00:07:58,000 --> 00:07:59,320
就是请求之外

175
00:07:59,320 --> 00:08:01,060
创建context

176
00:08:01,060 --> 00:08:01,560
其实这里呢

177
00:08:01,560 --> 00:08:03,120
我们之前是讲过通过一个什么方法

178
00:08:03,120 --> 00:08:04,760
是不是create

179
00:08:04,760 --> 00:08:07,300
alloy对吧

180
00:08:07,300 --> 00:08:10,160
阿罗一摩斯康

181
00:08:10,160 --> 00:08:12,640
这里了通过这样一个方法

182
00:08:12,640 --> 00:08:15,340
我们是不是可以创建我们额外的一个康泰斯去做一些事情

183
00:08:15,340 --> 00:08:15,780
对吧

184
00:08:15,780 --> 00:08:16,320
好

185
00:08:16,320 --> 00:08:17,580
这里呢就是一个啊

186
00:08:17,580 --> 00:08:17,940
Did ready

187
00:08:17,940 --> 00:08:18,980
我们的应用启动完毕

188
00:08:18,980 --> 00:08:19,740
那么这里呢

189
00:08:19,740 --> 00:08:21,740
还有一个非常重要的顺利多期是什么呢

190
00:08:21,740 --> 00:08:23,660
叫做

191
00:08:23,660 --> 00:08:24,760
7

192
00:08:24,760 --> 00:08:26,360
Did ready

193
00:08:26,360 --> 00:08:28,660
那么7

194
00:08:28,660 --> 00:08:29,480
Did ready是什么意思呢

195
00:08:29,480 --> 00:08:30,320
其实也非常好理解

196
00:08:30,320 --> 00:08:31,760
我们的应用启动完毕之后

197
00:08:31,760 --> 00:08:32,860
我们的http服务起来没有

198
00:08:32,860 --> 00:08:33,460
还没有吧

199
00:08:33,460 --> 00:08:34,880
因为我们应用启动的时候

200
00:08:34,880 --> 00:08:35,940
需要去啊

201
00:08:35,940 --> 00:08:36,420
进行我们一个

202
00:08:36,420 --> 00:08:38,760
比如说我们去hdb.listen去调用一个listen方法

203
00:08:38,760 --> 00:08:39,720
然后去监听某个端口

204
00:08:39,720 --> 00:08:42,420
那么此时呢就是代表我们的http

205
00:08:42,420 --> 00:08:44,720
启动完成

206
00:08:44,720 --> 00:08:46,680
好 那么我们把它来打印出来看一下

207
00:08:46,680 --> 00:08:49,600
 server dvd

208
00:08:49,600 --> 00:08:51,860
好 那么呢我们来

209
00:08:51,860 --> 00:08:53,140
这里呢我们来run一下

210
00:08:53,140 --> 00:08:56,300
看到底是怎么回事

211
00:08:56,300 --> 00:09:00,720
好 大家可以看到我们的生命都稀疏已经出来了

212
00:09:00,720 --> 00:09:02,040
我们最先执行的是什么

213
00:09:02,040 --> 00:09:03,100
config when load

214
00:09:03,100 --> 00:09:04,320
也就是它配置执行之前

215
00:09:04,320 --> 00:09:05,940
那么dd load呢也就是我们的配置

216
00:09:05,940 --> 00:09:07,240
我们配置加载完成

217
00:09:07,240 --> 00:09:08,240
那么为了Ready呢

218
00:09:08,240 --> 00:09:10,040
是不是我们应用启动之前了

219
00:09:10,040 --> 00:09:12,140
那么DealReady也就是我们应用已经启动了

220
00:09:12,140 --> 00:09:13,140
那么ServerDealReady

221
00:09:13,140 --> 00:09:15,240
是不是就代表我们的HTTP服务已经起来了

222
00:09:15,240 --> 00:09:18,140
也就是说只是可以监听我们用我们客户的一些请求

223
00:09:18,140 --> 00:09:18,740
对吧

224
00:09:18,740 --> 00:09:19,140
好

225
00:09:19,140 --> 00:09:21,440
那么这里呢就是他的咱们

226
00:09:21,440 --> 00:09:24,140
app.js里面一些基本书名周期的一些应用

227
00:09:24,140 --> 00:09:25,440
那么这里还有一个问题是什么呢

228
00:09:25,440 --> 00:09:26,940
不知道同学们有注意到

229
00:09:26,940 --> 00:09:29,440
我们之前是不是在app里面去监听的一些事件呢

230
00:09:29,440 --> 00:09:30,940
你比如说我们在wans里面去监听Server

231
00:09:30,940 --> 00:09:32,940
我们去监听request和response

232
00:09:32,940 --> 00:09:34,640
那么我们通过laid方式怎么样去监听呢

233
00:09:34,640 --> 00:09:36,200
因为我们之前的逻辑不能给丢掉

234
00:09:36,200 --> 00:09:39,500
其实这里它有一个构造函数

235
00:09:39,500 --> 00:09:41,720
叫做Construct

236
00:09:41,720 --> 00:09:44,500
它里面其实会把APP给我们传递进来

237
00:09:44,500 --> 00:09:45,720
所以我们这里可以通过

238
00:09:45,720 --> 00:09:48,940
比如说我们想去wans一下我们的Sev

239
00:09:48,940 --> 00:09:51,560
我们把它的注释给消掉

240
00:09:51,560 --> 00:09:52,320
格式化

241
00:09:52,320 --> 00:09:54,160
这里我们来测试一下

242
00:09:54,160 --> 00:09:55,700
我们的HTB-启动完毕

243
00:09:55,700 --> 00:09:56,600
它到底会不会打印

244
00:09:56,600 --> 00:10:00,820
这里其实大家就可以看到

245
00:10:00,820 --> 00:10:03,840
我们其实已经监听到这样的一个事件

246
00:10:03,840 --> 00:10:05,240
好这里呢就是我们

247
00:10:05,240 --> 00:10:08,000
这里启动了一个内容我们来总结一下

248
00:10:08,000 --> 00:10:16,080
那么我们的核心是不是讲了他的生命周期啊对吧

249
00:10:16,080 --> 00:10:17,180
核心是生命周期

250
00:10:17,180 --> 00:10:18,780
那么他有哪些生命周期呢

251
00:10:18,780 --> 00:10:21,980
第一个config will load对吧

252
00:10:21,980 --> 00:10:23,840
什么是不是我们的配置加载之前

253
00:10:23,840 --> 00:10:24,480
然后呢

254
00:10:24,480 --> 00:10:25,500
dead load对吧

255
00:10:25,500 --> 00:10:27,440
也就是我们的一个配置文件加载完成

256
00:10:27,440 --> 00:10:30,400
还有一个will ready

257
00:10:30,400 --> 00:10:32,680
我们的是不是我们的应用启动之前

258
00:10:32,680 --> 00:10:34,780
包括对应的就有D的Ready

259
00:10:34,780 --> 00:10:35,820
我们的应用启动完成

260
00:10:35,820 --> 00:10:37,020
那么应用启动完成之后

261
00:10:37,020 --> 00:10:38,820
是不是还需要去启动我们的http的一个server

262
00:10:38,820 --> 00:10:40,480
所以呢这里还会有

263
00:10:40,480 --> 00:10:44,860
ServerD的Ready

264
00:10:44,860 --> 00:10:47,760
好这里呢就是我们这节套的内容

