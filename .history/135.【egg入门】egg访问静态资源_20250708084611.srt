1
00:00:00,000 --> 00:00:01,400
好,这里我们就来看一下

2
00:00:01,400 --> 00:00:03,260
用一GG怎么样去读取我们的静态资源

3
00:00:03,260 --> 00:00:04,800
我们前面讲括话的时候

4
00:00:04,800 --> 00:00:07,600
我们是不是要使用一些中间键去访问我们的静态资源呢

5
00:00:07,600 --> 00:00:10,380
那么在一GG里面实际上它内置了一个Static的插件

6
00:00:10,380 --> 00:00:13,400
而且在线上环境建议部署了CDN无需该插件

7
00:00:13,400 --> 00:00:14,600
这句话怎么样去理解

8
00:00:14,600 --> 00:00:18,240
虽然说我们一GG它内置了咱们的静态资源的一些插件

9
00:00:18,240 --> 00:00:20,180
但是你去真正的去做项目的时候

10
00:00:20,180 --> 00:00:21,560
比如说我们去一些企业里面

11
00:00:21,560 --> 00:00:23,080
我们去找工作我们入职之后

12
00:00:23,080 --> 00:00:24,120
在我们的实际项目中

13
00:00:24,120 --> 00:00:26,060
其实咱们的电台资源都会部署到CDN

14
00:00:26,060 --> 00:00:26,920
为什么呢

15
00:00:26,920 --> 00:00:29,020
因为CDN它是比较稳定的

16
00:00:29,020 --> 00:00:31,140
而且它不会占用你服务器的资源

17
00:00:31,140 --> 00:00:32,820
会保证你项目的一个性能

18
00:00:32,820 --> 00:00:34,480
那么具体CDN的一些好处

19
00:00:34,480 --> 00:00:36,660
可能需要同学们在今后的开发中去慢慢的去体会

20
00:00:36,660 --> 00:00:37,780
这里我们现在看一下

21
00:00:37,780 --> 00:00:39,860
一机机里面的电台资源它是怎么去弄的

22
00:00:39,860 --> 00:00:41,260
首先Static插件呢

23
00:00:41,260 --> 00:00:43,380
它会默认的去映射Public这样的一个目录

24
00:00:43,380 --> 00:00:44,020
在APP下面

25
00:00:44,020 --> 00:00:44,740
此处呢

26
00:00:44,740 --> 00:00:47,300
我们把电台资源都放了APP的Public目录即可

27
00:00:47,300 --> 00:00:47,660
什么意思

28
00:00:47,660 --> 00:00:49,660
也就是说你把你的CSS或者GS

29
00:00:49,660 --> 00:00:52,100
你直接放到app的public目录下面就可以了

30
00:00:52,100 --> 00:00:53,660
它自动的去帮你去访问进来资源

31
00:00:53,660 --> 00:00:55,580
这里呢其实也就体现了我们的一个原则是什么

32
00:00:55,580 --> 00:00:56,940
约定有一个配置

33
00:00:56,940 --> 00:00:58,040
那么我们就来尝试一下

34
00:00:58,040 --> 00:01:01,180
好这里呢其实我们项目出手画的时候

35
00:01:01,180 --> 00:01:02,520
它是不是已经帮我们建好了一个文件

36
00:01:02,520 --> 00:01:03,340
目录叫做public

37
00:01:03,340 --> 00:01:06,200
好那么我们随便来建一个js试一下

38
00:01:06,200 --> 00:01:08,200
比如说我们就叫做hello.js

39
00:01:08,200 --> 00:01:10,020
咱们在里面去随便写

40
00:01:10,020 --> 00:01:11,940
比如说我们去console.log打印一段

41
00:01:11,940 --> 00:01:13,680
hello.js

42
00:01:13,680 --> 00:01:16,220
好那么我们来重启一下服务

43
00:01:16,220 --> 00:01:18,900
我们来看能不能访问到咱们的一个hello.js

44
00:01:18,900 --> 00:01:24,340
好我们来访问

45
00:01:24,340 --> 00:01:26,080
hello.js

46
00:01:26,080 --> 00:01:27,020
好大家可以看到

47
00:01:27,020 --> 00:01:28,260
视频是多的放的

48
00:01:28,260 --> 00:01:29,120
那我们再来试一下

49
00:01:29,120 --> 00:01:30,260
是不是在public

50
00:01:30,260 --> 00:01:31,540
这样一个目录下面

51
00:01:31,540 --> 00:01:32,380
public

52
00:01:32,380 --> 00:01:34,040
好大家这样就可以看到

53
00:01:34,040 --> 00:01:34,920
我们是不是已经访问到了

54
00:01:34,920 --> 00:01:35,460
咱们的精彩资源

55
00:01:35,460 --> 00:01:36,000
concel.log

56
00:01:36,000 --> 00:01:37,040
hello.js

57
00:01:37,040 --> 00:01:37,720
说明一个什么问题

58
00:01:37,720 --> 00:01:38,980
是不是说明咱们的精彩资源

59
00:01:38,980 --> 00:01:40,020
是以public开头的

60
00:01:40,020 --> 00:01:40,540
后面呢

61
00:01:40,540 --> 00:01:41,700
是以咱们的ts

62
00:01:41,700 --> 00:01:42,120
你在哪里

63
00:01:42,120 --> 00:01:43,540
咱们就输入什么样的路径

64
00:01:43,540 --> 00:01:44,420
它会自动帮你了

65
00:01:44,420 --> 00:01:45,180
去完成

66
00:01:45,180 --> 00:01:46,180
那么我们这里呢

67
00:01:46,180 --> 00:01:47,280
就来总结一下

68
00:01:47,280 --> 00:01:48,360
我们这节课的内容

69
00:01:48,360 --> 00:01:50,380
我们是不是使用177去访问了我们的一个

70
00:01:50,380 --> 00:01:51,380
竞态资源

71
00:01:51,380 --> 00:01:52,560
那么static

72
00:01:52,560 --> 00:01:54,960
是内置的

73
00:01:54,960 --> 00:01:56,560
那么它在哪一个目录下面呢

74
00:01:56,560 --> 00:01:59,460
是不是在app的public

75
00:01:59,460 --> 00:02:01,220
那么你的访问路径呢

76
00:02:01,220 --> 00:02:02,060
访问路径是什么

77
00:02:02,060 --> 00:02:04,420
是不是你的localhost

78
00:02:04,420 --> 00:02:06,160
什么呀71001

79
00:02:06,160 --> 00:02:07,620
public

80
00:02:07,620 --> 00:02:10,460
然后呢是你所写的一些竞态资源的目录

81
00:02:10,460 --> 00:02:12,000
好这里呢就是我们这节课的内容

