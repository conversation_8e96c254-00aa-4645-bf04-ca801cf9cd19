1
00:00:00,760 --> 00:00:02,820
这一节课咱们就来讲解红任务与微任务

2
00:00:02,820 --> 00:00:04,600
刚才老师是不是留了一个学念

3
00:00:04,600 --> 00:00:06,920
就是咱们的settimeout和promise它的一个指形顺序

4
00:00:06,920 --> 00:00:08,700
对吧同学们还记不记得咱们这张图

5
00:00:08,700 --> 00:00:13,320
在IO里面settimeout和promise到底谁先进入咱们的任务对面

6
00:00:13,320 --> 00:00:14,340
这里是不知道

7
00:00:14,340 --> 00:00:15,100
所以

8
00:00:15,100 --> 00:00:18,680
当同学们学习了微任务与红任务之后就可以去理解

9
00:00:18,680 --> 00:00:20,220
这里也是咱们本节课的目标

10
00:00:20,220 --> 00:00:21,240
老师会怎么去讲呢

11
00:00:21,240 --> 00:00:23,300
首先老师会去介绍一下红任务与微任务它的一个

12
00:00:23,300 --> 00:00:25,600
概念以及咱们通过刚才的那个例子

13
00:00:25,860 --> 00:00:27,400
去深入理解什么是红任务什么是微任务

14
00:00:27,400 --> 00:00:30,460
好他那个概念是什么呢首先在咱们

15
00:00:30,460 --> 00:00:31,500
JS的

16
00:00:31,500 --> 00:00:35,580
单线程中的任务他又可以细封为红任务和微任务

17
00:00:35,580 --> 00:00:37,120
红任务是什么呢macrotask

18
00:00:37,120 --> 00:00:40,460
他包括scriptscript就是咱们JS单线程的一整条执行站

19
00:00:40,460 --> 00:00:43,520
很好理解吧包括sertimeout定时器

20
00:00:43,520 --> 00:00:45,060
以及node中的sertimmediate

21
00:00:45,060 --> 00:00:45,820
iO

22
00:00:45,820 --> 00:00:47,100
包括咱们的渲染

23
00:00:47,100 --> 00:00:49,920
其实怎么去理解我们大部分的操作他都是在红任务中的

24
00:00:49,920 --> 00:00:51,460
在浏览器中microtask

25
00:00:51,460 --> 00:00:54,020
只有promise是属于他的咱们常用的就是promise

26
00:00:55,300 --> 00:00:58,200
那么像 next tick next tick 他是 node gs 里面的内容

27
00:00:58,200 --> 00:01:00,140
Observa 和母配型Observa 咱们不是很常用

28
00:01:00,140 --> 00:01:01,000
所以说大家只要

29
00:01:01,000 --> 00:01:04,400
深刻的去记住 promise 他是微动就可以了

30
00:01:04,400 --> 00:01:06,640
那么他们是怎么去执行的呢

31
00:01:06,640 --> 00:01:08,200
首先红任务会进入主线层

32
00:01:08,200 --> 00:01:10,400
那么在执行的过程中会去收集微动

33
00:01:10,400 --> 00:01:11,200
怎么办理解

34
00:01:11,200 --> 00:01:12,300
比如说我们现在在执行

35
00:01:12,300 --> 00:01:13,400
你只要碰到了 promise

36
00:01:13,400 --> 00:01:14,900
那么我们就把 promise 他的一步回掉

37
00:01:14,900 --> 00:01:15,700
加入微动对面

38
00:01:15,700 --> 00:01:18,100
此时他执行 promise 的微任务对面的时候

39
00:01:18,100 --> 00:01:21,000
又会把咱们微任务中去收集到了红任务

40
00:01:21,000 --> 00:01:22,100
去添加了红任务对面

41
00:01:22,600 --> 00:01:23,840
然后再次去执行红任务

42
00:01:23,840 --> 00:01:25,600
反复的去执行一二两个步骤

43
00:01:25,600 --> 00:01:26,480
怎么去理解

44
00:01:26,480 --> 00:01:27,220
说简单一点

45
00:01:27,220 --> 00:01:28,920
看这个图

46
00:01:28,920 --> 00:01:30,200
首先我们会去执行红任务

47
00:01:30,200 --> 00:01:31,240
红任务我们遇到Promise

48
00:01:31,240 --> 00:01:32,480
就加入微任务对面

49
00:01:32,480 --> 00:01:33,740
然后再去执行微任务

50
00:01:33,740 --> 00:01:34,800
微任务执行完之后

51
00:01:34,800 --> 00:01:35,780
咱们又去进入红任务

52
00:01:35,780 --> 00:01:36,540
反复的去循环

53
00:01:36,540 --> 00:01:38,340
其实它就是微任务和红任务

54
00:01:38,340 --> 00:01:39,300
反复执行那个过程

55
00:01:39,300 --> 00:01:40,180
那么这里我们就来看一下

56
00:01:40,180 --> 00:01:41,120
刚才的例子

57
00:01:41,120 --> 00:01:41,820
怎么去分析它

58
00:01:41,820 --> 00:01:43,220
好

59
00:01:43,220 --> 00:01:44,660
So timeout

60
00:01:44,660 --> 00:01:45,540
Promise执行结果

61
00:01:45,540 --> 00:01:46,120
是什么

62
00:01:46,120 --> 00:01:46,880
同学们记了吗

63
00:01:46,880 --> 00:01:47,700
是不是第一个

64
00:01:47,700 --> 00:01:48,420
命

65
00:01:48,420 --> 00:01:49,540
第二个呢

66
00:01:49,540 --> 00:01:50,200
Premise

67
00:01:50,200 --> 00:01:51,140
第三个

68
00:01:51,140 --> 00:01:51,760
set

69
00:01:51,760 --> 00:01:53,820
time

70
00:01:53,820 --> 00:01:54,520
out

71
00:01:54,520 --> 00:01:54,800
好

72
00:01:54,800 --> 00:01:56,780
那老师呢画一幅图来帮助大家理解一下

73
00:01:56,780 --> 00:01:58,120
首先

74
00:01:58,120 --> 00:02:01,440
我们是不是会有一个红任务对立与微任务对立啊

75
00:02:01,440 --> 00:02:01,780
同学们

76
00:02:01,780 --> 00:02:02,420
对吧

77
00:02:02,420 --> 00:02:02,680
好

78
00:02:02,680 --> 00:02:03,440
老师先来画一下

79
00:02:03,440 --> 00:02:05,100
红任务

80
00:02:05,100 --> 00:02:08,700
微任务

81
00:02:08,700 --> 00:02:10,540
那么红任务

82
00:02:10,540 --> 00:02:12,360
微任务之外是不是还有一个IO啊

83
00:02:12,360 --> 00:02:13,860
我们专门负责等待的线程

84
00:02:13,860 --> 00:02:14,940
这也是浏览器提供的

85
00:02:14,940 --> 00:02:15,680
我们先不用去关注

86
00:02:15,680 --> 00:02:18,360
首先我们的红任务会有哪些会加入红任务

87
00:02:18,360 --> 00:02:20,580
咱们先把它准备好

88
00:02:20,580 --> 00:02:23,540
首先咱们页面中是对三个函数

89
00:02:23,540 --> 00:02:25,040
一个是setTimeout

90
00:02:25,040 --> 00:02:26,320
还有一个

91
00:02:26,320 --> 00:02:29,560
promise.resolve

92
00:02:29,560 --> 00:02:31,640
第三个呢

93
00:02:31,640 --> 00:02:32,940
logmin

94
00:02:32,940 --> 00:02:34,920
这里的老师是简写

95
00:02:34,920 --> 00:02:37,120
好咱们现在开始去执行

96
00:02:37,120 --> 00:02:38,860
首先我们第一行代码setTimeout

97
00:02:38,860 --> 00:02:39,600
它是什么

98
00:02:39,600 --> 00:02:40,180
是不是红任务

99
00:02:40,180 --> 00:02:41,740
加进去

100
00:02:41,740 --> 00:02:45,000
好咱们加到红任务里面去

101
00:02:45,000 --> 00:02:45,520
好

102
00:02:45,520 --> 00:02:47,940
加到红任务里面去

103
00:02:47,940 --> 00:02:48,980
他是不是里面一个回调

104
00:02:48,980 --> 00:02:51,380
0秒之后会打印一个set him out

105
00:02:51,380 --> 00:02:52,940
那么咱们io里面

106
00:02:52,940 --> 00:02:54,300
是不是就有了log

107
00:02:54,300 --> 00:02:58,440
set time out

108
00:02:58,440 --> 00:02:58,860
对吧

109
00:02:58,860 --> 00:02:59,420
好

110
00:02:59,420 --> 00:03:02,380
那么此时set him out

111
00:03:02,380 --> 00:03:02,860
是不是出战

112
00:03:02,860 --> 00:03:04,300
因为咱们的红任务已经执行完了

113
00:03:04,300 --> 00:03:05,600
他

114
00:03:05,600 --> 00:03:07,340
此时呢

115
00:03:07,340 --> 00:03:08,780
promise.resolve

116
00:03:08,780 --> 00:03:09,780
是不是再次入战

117
00:03:09,780 --> 00:03:12,780
那么promise.resolve入战之后

118
00:03:12,780 --> 00:03:14,120
他的一个正方法

119
00:03:14,120 --> 00:03:16,680
是不是Promise是属于红任务还是微任务啊 同学们

120
00:03:16,680 --> 00:03:18,720
是不是微任务

121
00:03:18,720 --> 00:03:19,760
那么

122
00:03:19,760 --> 00:03:23,600
咱们的log promise 是不是会加入微任务里面去

123
00:03:23,600 --> 00:03:25,380
log

124
00:03:25,380 --> 00:03:30,500
Promise 好 他会加入微任务对立

125
00:03:30,500 --> 00:03:35,120
那么呢Promise Resolve 他的任务是不完成了 对吧 出战

126
00:03:35,120 --> 00:03:37,680
然后log 入战

127
00:03:37,680 --> 00:03:38,960
然后呢 执行完

128
00:03:38,960 --> 00:03:39,980
出战

129
00:03:39,980 --> 00:03:41,520
那么我们刚才是不是讲过了

130
00:03:41,520 --> 00:03:43,040
红任务执行完之后

131
00:03:43,040 --> 00:03:43,820
干什么

132
00:03:44,120 --> 00:03:48,150
是不是红任务执行完之后 再去微任务队列里面去看有没有微任务 好 现在我们发现有个log

133
00:03:48,150 --> 00:03:49,180
 promise 执行完

134
00:03:49,180 --> 00:03:50,180
出战

135
00:03:50,180 --> 00:03:53,020
好 那么此时咱们事情完成了吗

136
00:03:53,020 --> 00:03:54,820
还没有吧 对吧

137
00:03:54,820 --> 00:03:57,080
我们是不是还有一个

138
00:03:57,080 --> 00:03:58,020
爱欧

139
00:03:58,020 --> 00:04:02,120
log settimeout没有执行完了 那么settimeout 他那个回调函数是红任务还是微弄完

140
00:04:02,120 --> 00:04:03,220
 是不是他属于红任务啊

141
00:04:03,220 --> 00:04:04,220
同学们

142
00:04:04,220 --> 00:04:07,320
所以呢 这里了 还会有一个

143
00:04:07,920 --> 00:04:10,080
会产生一个新的红的对面刚才咱们是不是讲过了

144
00:04:10,080 --> 00:04:13,860
和红任务执行完之后执行微动微动执行完之后咱们又会产生一个新的红任务

145
00:04:13,860 --> 00:04:15,720
新的红任务是谁是不是咱们爱我返回

146
00:04:15,720 --> 00:04:18,480
logsatmout他会加入咱们新的红任务对面去

147
00:04:18,480 --> 00:04:20,020
红任务对面里面去此时

148
00:04:20,020 --> 00:04:23,820
在satmout这样方法他的一个回调里面是不是没有普罗密斯了

149
00:04:23,820 --> 00:04:24,960
也就是说他不会产生微对面

150
00:04:24,960 --> 00:04:27,180
那么咱们咱们的微对面此时呢

151
00:04:27,180 --> 00:04:28,720
是空的对吧

152
00:04:28,720 --> 00:04:30,560
微动他是空的

153
00:04:30,560 --> 00:04:33,120
所以咱们logsatmout执行完出弹

154
00:04:33,120 --> 00:04:34,600
那么咱们整个代码就已经执行完了

155
00:04:34,600 --> 00:04:35,760
所以我们的打印顺序

156
00:04:35,760 --> 00:04:36,820
是什么呀

157
00:04:36,820 --> 00:04:37,140
是不是

158
00:04:37,140 --> 00:04:37,760
main

159
00:04:37,760 --> 00:04:39,000
promise

160
00:04:39,000 --> 00:04:40,560
然后set them out

161
00:04:40,560 --> 00:04:41,000
对吧

162
00:04:41,000 --> 00:04:41,740
好

163
00:04:41,740 --> 00:04:42,820
那么这里就是咱们通过

164
00:04:42,820 --> 00:04:44,340
红任务和微任务去分析

165
00:04:44,340 --> 00:04:45,440
它的一个执行过程

166
00:04:45,440 --> 00:04:46,480
那么同学们请注意

167
00:04:46,480 --> 00:04:50,360
我们一直提到事件循环

168
00:04:50,360 --> 00:04:51,900
一直提到一个概念

169
00:04:51,900 --> 00:04:54,500
叫做事件循环

170
00:04:54,500 --> 00:04:58,060
那么

171
00:04:58,060 --> 00:05:00,140
什么样的一个事件

172
00:05:00,140 --> 00:05:00,740
它叫一个循环

173
00:05:00,740 --> 00:05:02,260
因为循环循环说明它有很多环嘛

174
00:05:02,260 --> 00:05:03,060
咱们怎么样去闭环

175
00:05:03,060 --> 00:05:05,440
是不是我们每执行一轮

176
00:05:05,440 --> 00:05:06,820
红任务和微任务

177
00:05:06,820 --> 00:05:08,440
咱们就叫做一轮世界循环了

178
00:05:08,440 --> 00:05:09,040
对吧

179
00:05:09,040 --> 00:05:12,940
每执行完一轮

180
00:05:12,940 --> 00:05:14,840
红任务

181
00:05:14,840 --> 00:05:19,940
和微任务就叫做一环

182
00:05:19,940 --> 00:05:22,600
世界

183
00:05:22,600 --> 00:05:24,820
这里呢就是咱们世界循环的

184
00:05:24,820 --> 00:05:27,100
一个准确的概念在浏览器里面

185
00:05:27,100 --> 00:05:28,280
好老师呢把图片保存一下

186
00:05:28,280 --> 00:05:31,040
这里咱们来回顾一下

187
00:05:31,040 --> 00:05:32,680
什么是红任务和微任务

188
00:05:32,680 --> 00:05:33,840
那么我们的红任务

189
00:05:33,840 --> 00:05:34,400
是不是首先

190
00:05:34,400 --> 00:05:35,640
我们执行

191
00:05:35,640 --> 00:05:36,660
主线程的时候

192
00:05:36,660 --> 00:05:37,000
scribe

193
00:05:37,000 --> 00:05:38,240
就是咱们的JS代码

194
00:05:38,240 --> 00:05:39,020
从第一项开始执行

195
00:05:39,020 --> 00:05:40,300
它是不是红任务

196
00:05:40,300 --> 00:05:42,000
那么咱们碰到

197
00:05:42,000 --> 00:05:42,480
settimeout

198
00:05:42,480 --> 00:05:42,980
setinterval

199
00:05:42,980 --> 00:05:43,620
这样的一步的时候

200
00:05:43,620 --> 00:05:44,800
我们是不是把它放到IO里面去

201
00:05:44,800 --> 00:05:45,400
先去等待

202
00:05:45,400 --> 00:05:45,920
不用管

203
00:05:45,920 --> 00:05:47,980
然后我们只要碰到了Promise

204
00:05:47,980 --> 00:05:48,900
是不是就把它丢到

205
00:05:48,900 --> 00:05:49,660
微任务对面里面去

206
00:05:49,660 --> 00:05:50,920
当咱们的主线程

207
00:05:50,920 --> 00:05:52,020
执行完成之后

208
00:05:52,020 --> 00:05:52,600
咱们再去

209
00:05:52,600 --> 00:05:53,620
微任务对面里面去找

210
00:05:53,620 --> 00:05:54,680
执行

211
00:05:54,680 --> 00:05:56,580
咱们的微任务对面的任务

212
00:05:56,580 --> 00:05:57,240
比如说Promise

213
00:05:57,240 --> 00:05:58,100
它的一些callback

214
00:05:58,100 --> 00:06:00,020
那么当咱们的VA任务队列执行完成之后

215
00:06:00,020 --> 00:06:02,120
是不是再次开启一轮新的循环

216
00:06:02,120 --> 00:06:03,020
也就是咱们的红任务

217
00:06:03,020 --> 00:06:04,520
比如说咱们的set him out

218
00:06:04,520 --> 00:06:05,240
它那个定视器

219
00:06:05,240 --> 00:06:07,380
它的koback将推入一个新的红队列

220
00:06:07,380 --> 00:06:07,620
对吧

221
00:06:07,620 --> 00:06:09,520
咱们反复的循环这个过程

222
00:06:09,520 --> 00:06:11,520
它就叫做世界循环

223
00:06:11,520 --> 00:06:12,480
好

224
00:06:12,480 --> 00:06:13,440
老师呢

225
00:06:13,440 --> 00:06:15,140
这节课内容就先讲到这里

