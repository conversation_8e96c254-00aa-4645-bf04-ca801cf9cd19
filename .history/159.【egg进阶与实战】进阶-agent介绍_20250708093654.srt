1
00:00:00,000 --> 00:00:02,640
好,这节课我们就来看一下多进程模型和进程间通信

2
00:00:02,640 --> 00:00:06,140
那么呢,我们之前是不是讲过,在nodejs课程里面

3
00:00:06,140 --> 00:00:07,920
我们怎么样去进行多进程,是不是cluster

4
00:00:07,920 --> 00:00:09,880
那么近时间的通信呢,咱们利用IPC

5
00:00:09,880 --> 00:00:11,200
这里呢,其实我们前面都学过

6
00:00:11,200 --> 00:00:13,140
所以呢,现在简单的回顾一下

7
00:00:13,140 --> 00:00:16,040
我们都知道GS它的代码是运行在什么环境下

8
00:00:16,040 --> 00:00:16,640
是一个单线程

9
00:00:16,640 --> 00:00:18,540
那么单线程它的一个特点是什么

10
00:00:18,540 --> 00:00:20,020
是不是只能利用咱们CPU的一盒

11
00:00:20,020 --> 00:00:21,620
但是现在我们计算机是不是都是多盒的

12
00:00:21,620 --> 00:00:24,780
那么为了解决我们去充分的利用CPU多盒的一个优势

13
00:00:24,780 --> 00:00:27,220
怎么去做,是不是就需要我们一个多进程模型

14
00:00:27,220 --> 00:00:28,980
那么nodejs里面它有一个解决方案是什么

15
00:00:28,980 --> 00:00:29,780
是不是clust的模块

16
00:00:29,780 --> 00:00:32,260
那么在clust里面呢又分为master和worker

17
00:00:32,260 --> 00:00:34,100
那么master就好比包工头

18
00:00:34,100 --> 00:00:35,260
那么worker就好比工人

19
00:00:35,260 --> 00:00:37,660
那么cpu的核数是不是就是我们工人的数量呢

20
00:00:37,660 --> 00:00:38,220
比如说你是8核

21
00:00:38,220 --> 00:00:39,760
那么我们就能够叫8个工人

22
00:00:39,760 --> 00:00:40,880
能不能叫9个不可以吧

23
00:00:40,880 --> 00:00:41,080
为什么

24
00:00:41,080 --> 00:00:42,380
是不是我们的工资预算不够啊

25
00:00:42,380 --> 00:00:42,840
只能请8个

26
00:00:42,840 --> 00:00:44,220
那么你有一个工人跑了

27
00:00:44,220 --> 00:00:45,440
那么包工头再来请一个新的

28
00:00:45,440 --> 00:00:46,220
但是总数是8个

29
00:00:46,220 --> 00:00:48,140
好我们来简单来看一下

30
00:00:48,140 --> 00:00:50,260
咱们用clust的模块去进行

31
00:00:50,260 --> 00:00:51,980
咱们一个多形成的一个代码

32
00:00:51,980 --> 00:00:52,680
我们来回顾一下

33
00:00:52,680 --> 00:00:54,060
首先我们是不是require这一个模块

34
00:00:54,060 --> 00:00:56,260
通过cluster is master这一个方法

35
00:00:56,260 --> 00:00:58,000
是不是可以判断我们现在是不是在主进程里面

36
00:00:58,000 --> 00:00:59,140
那么如果说我们在主进程里面

37
00:00:59,140 --> 00:01:01,160
是不是首先我们获取到我们CPU的一个核数

38
00:01:01,160 --> 00:01:01,760
比如说你有8核

39
00:01:01,760 --> 00:01:02,980
此时我们通过一个货血环

40
00:01:02,980 --> 00:01:03,920
是不是创造8个功能

41
00:01:03,920 --> 00:01:05,160
也就是让我们去创造8个walker

42
00:01:05,160 --> 00:01:06,760
好那么创造完成之后

43
00:01:06,760 --> 00:01:08,040
这里有一段AOS的代码

44
00:01:08,040 --> 00:01:09,300
那么AOS是不是就是我们walker

45
00:01:09,300 --> 00:01:11,080
那么功能去执行的具体的一个业务逻辑

46
00:01:11,080 --> 00:01:12,040
比如说我们每一个县程

47
00:01:12,040 --> 00:01:15,300
我们每一个进程去创造一个http的服务

48
00:01:15,300 --> 00:01:16,200
去监听80端口

49
00:01:16,200 --> 00:01:17,340
这里就是我们cluster

50
00:01:17,340 --> 00:01:18,660
它一个简单的一个介绍

51
00:01:18,660 --> 00:01:19,800
那么我们来看一下

52
00:01:19,800 --> 00:01:20,800
在一GG里面

53
00:01:20,800 --> 00:01:22,240
它的一个多进程模型是什么样的

54
00:01:22,240 --> 00:01:23,280
我们既然刚才讲到的

55
00:01:23,280 --> 00:01:23,580
漏的GS

56
00:01:23,580 --> 00:01:24,900
它既然已经提供了解决方案

57
00:01:24,900 --> 00:01:26,160
那么一GG它的解决方案

58
00:01:26,160 --> 00:01:27,760
和我们的漏的GS解决方案是一样的吗

59
00:01:27,760 --> 00:01:29,300
我们接下来就来看一下

60
00:01:29,300 --> 00:01:31,600
好 首先我们来看一下

61
00:01:31,600 --> 00:01:32,900
首先我们来看一下

62
00:01:32,900 --> 00:01:34,440
这是我能从咱们一GG

63
00:01:34,440 --> 00:01:36,300
它那个文档里面去摘出来的一段话

64
00:01:36,300 --> 00:01:37,680
作为企业级的解决方案

65
00:01:37,680 --> 00:01:39,120
企业级的解决方案指的是什么

66
00:01:39,120 --> 00:01:39,920
是不是就是指的一GG

67
00:01:39,920 --> 00:01:41,580
要考虑的东西还有很多

68
00:01:41,580 --> 00:01:43,680
Walker进程异常推出以后如何处理

69
00:01:43,680 --> 00:01:45,760
那么多个Walker进程之间如何共享资源

70
00:01:45,760 --> 00:01:47,920
那么多个Walker进程之间如何调度

71
00:01:47,920 --> 00:01:48,600
说明一个什么问题

72
00:01:48,600 --> 00:01:51,240
是不是说明一机器它另有高见了

73
00:01:51,240 --> 00:01:51,520
对吧

74
00:01:51,520 --> 00:01:52,840
所以我们就接着往下看

75
00:01:52,840 --> 00:01:55,180
首先我们来看一下进程守护

76
00:01:55,180 --> 00:01:56,760
什么是进程守护

77
00:01:56,760 --> 00:01:57,940
那么进程守护

78
00:01:57,940 --> 00:01:59,760
是不是就是提升我们代码的一个健壮性

79
00:01:59,760 --> 00:02:01,820
我们之前在讲漏的JS过程里面

80
00:02:01,820 --> 00:02:03,360
是不是也讲过进程守护在那个概念

81
00:02:03,360 --> 00:02:04,900
它就像一个天使一样保护我们进程

82
00:02:04,900 --> 00:02:05,980
让它不会轻易的挂掉

83
00:02:05,980 --> 00:02:06,800
以及会重启

84
00:02:06,800 --> 00:02:07,440
好

85
00:02:07,440 --> 00:02:08,600
那么我们来看一下

86
00:02:08,600 --> 00:02:10,360
一般来讲loadgs它的进程退出呢

87
00:02:10,360 --> 00:02:11,560
可以分为两类

88
00:02:11,560 --> 00:02:12,780
一类是未捕获的异常

89
00:02:12,780 --> 00:02:14,600
一类呢是OM和系统异常

90
00:02:14,600 --> 00:02:16,100
首先我们来看一下未捕获的异常

91
00:02:16,100 --> 00:02:17,300
那么未捕获的异常

92
00:02:17,300 --> 00:02:18,900
我们通常是通过process.on

93
00:02:18,900 --> 00:02:20,800
process是不是我们loadgs原生提供的一个模块

94
00:02:20,800 --> 00:02:23,240
它呢监听uncut exception这样一个事件

95
00:02:23,240 --> 00:02:24,600
那么这个事件经常会怎么样去触发

96
00:02:24,600 --> 00:02:25,640
我们之前是不是做过试验

97
00:02:25,640 --> 00:02:27,560
咱们代码一旦报错就会触发这样一个事件

98
00:02:27,560 --> 00:02:29,720
然后呢我们可以在这里去对它进行处理

99
00:02:29,720 --> 00:02:29,880
好

100
00:02:29,880 --> 00:02:31,220
那么这里我们怎么样去处理呢

101
00:02:31,220 --> 00:02:31,840
一般来讲

102
00:02:31,840 --> 00:02:32,820
是不是进行优雅退出

103
00:02:32,820 --> 00:02:34,280
之前我们是不是也讲过

104
00:02:34,280 --> 00:02:34,500
好

105
00:02:34,500 --> 00:02:35,860
那么要怎么样去优雅退出呢

106
00:02:35,860 --> 00:02:37,500
它是不是主要分为

107
00:02:37,500 --> 00:02:38,720
是不是主要分为三步

108
00:02:38,720 --> 00:02:39,140
首先

109
00:02:39,140 --> 00:02:40,540
我们首先出

110
00:02:40,540 --> 00:02:41,700
我们的一个工人里面

111
00:02:41,700 --> 00:02:42,720
它出错了之后怎么办

112
00:02:42,720 --> 00:02:43,760
我们的Worker

113
00:02:43,760 --> 00:02:45,320
在一般在我们的Low的经验室

114
00:02:45,320 --> 00:02:46,400
我们的Worker一般是做干什么

115
00:02:46,400 --> 00:02:47,480
是不是提供HTTP的服务

116
00:02:47,480 --> 00:02:48,180
首先呢

117
00:02:48,180 --> 00:02:49,240
我们是不是要关闭

118
00:02:49,240 --> 00:02:50,040
在一个TCP的Surfer

119
00:02:50,040 --> 00:02:50,640
为什么呀

120
00:02:50,640 --> 00:02:51,460
因为我们的HTTP

121
00:02:51,460 --> 00:02:52,400
是不是基于TCP

122
00:02:52,400 --> 00:02:52,940
对吧

123
00:02:52,940 --> 00:02:53,900
基于TCP的一个协议

124
00:02:53,900 --> 00:02:55,200
所以说我们此时

125
00:02:55,200 --> 00:02:57,460
首先我们要把我们的TCP的服务给关掉

126
00:02:57,460 --> 00:02:59,400
我们要断开和Mask的IPC通道

127
00:02:59,400 --> 00:02:59,900
也就是说呢

128
00:02:59,900 --> 00:03:01,420
我们要放弃这个功能

129
00:03:01,420 --> 00:03:03,540
此时不再接受新的用户请求

130
00:03:03,540 --> 00:03:04,700
好

131
00:03:04,700 --> 00:03:06,300
那么我们断掉链接之后

132
00:03:06,300 --> 00:03:08,000
是不是需要立即去fork一个新的walker

133
00:03:08,000 --> 00:03:08,280
对吧

134
00:03:08,280 --> 00:03:08,960
刚才我们是不是讲到了

135
00:03:08,960 --> 00:03:09,880
有一个功能他说不干了

136
00:03:09,880 --> 00:03:11,460
我们此时要去创造一个新的功能

137
00:03:11,460 --> 00:03:12,560
我们要请一个新的人来

138
00:03:12,560 --> 00:03:13,160
因为我们要

139
00:03:13,160 --> 00:03:13,500
对吧

140
00:03:13,500 --> 00:03:15,120
我们八核要去利用我们的cpu核数

141
00:03:15,120 --> 00:03:15,580
好

142
00:03:15,580 --> 00:03:17,820
那么我们去创造一个新的功能之后

143
00:03:17,820 --> 00:03:18,820
那么我们的异常的walker

144
00:03:18,820 --> 00:03:19,700
需要等待一段时间

145
00:03:19,700 --> 00:03:20,360
为什么要等待

146
00:03:20,360 --> 00:03:22,220
因为我们发生错误之前

147
00:03:22,220 --> 00:03:24,200
是不是还会有一些请求等待在那里

148
00:03:24,200 --> 00:03:24,420
对吧

149
00:03:24,420 --> 00:03:24,960
你比如说

150
00:03:24,960 --> 00:03:27,040
我们的http服务是不是需要时间

151
00:03:27,040 --> 00:03:28,460
那么假如说我们一秒

152
00:03:28,460 --> 00:03:29,380
只能处理一个请求

153
00:03:29,380 --> 00:03:30,320
但是这一秒钟之内

154
00:03:30,320 --> 00:03:31,020
来了四个人

155
00:03:31,020 --> 00:03:32,240
或者说五个客户端

156
00:03:32,240 --> 00:03:33,360
来请求我们的服务

157
00:03:33,360 --> 00:03:34,600
那么后面的是不是在排队

158
00:03:34,600 --> 00:03:35,920
所以我们去关闭

159
00:03:35,920 --> 00:03:36,700
在一个walker之前

160
00:03:36,700 --> 00:03:38,620
是不是需要处理完了

161
00:03:38,620 --> 00:03:39,640
就举个例子

162
00:03:39,640 --> 00:03:40,780
是不是就跟我们离职一样

163
00:03:40,780 --> 00:03:41,920
你如果说不想干了

164
00:03:41,920 --> 00:03:42,440
你能不能说

165
00:03:42,440 --> 00:03:43,380
跟你的老板说

166
00:03:43,380 --> 00:03:44,020
我不想干了

167
00:03:44,020 --> 00:03:44,500
你就可以直接走

168
00:03:44,500 --> 00:03:44,880
不可以吧

169
00:03:44,880 --> 00:03:45,880
你是不是需要把你

170
00:03:45,880 --> 00:03:46,820
手头没有完成的工作

171
00:03:46,820 --> 00:03:48,040
完成了再走

172
00:03:48,040 --> 00:03:48,400
对吧

173
00:03:48,400 --> 00:03:50,040
说白了就是要把你的屁股擦干净了

174
00:03:50,040 --> 00:03:50,280
对吧

175
00:03:50,280 --> 00:03:50,600
好

176
00:03:50,600 --> 00:03:51,840
那么我们就来看一下

177
00:03:51,840 --> 00:03:54,000
另外一种异常情况是怎么样的

178
00:03:54,000 --> 00:03:55,880
另外一种印查情况就是OM系统一场

179
00:03:55,880 --> 00:03:57,240
那么什么是OM呢

180
00:03:57,240 --> 00:03:58,080
OM这里呢

181
00:03:58,080 --> 00:03:59,680
简单的给同学们来解释一下

182
00:03:59,680 --> 00:04:02,760
OM其实就是我们的一个内存用镜

183
00:04:02,760 --> 00:04:04,740
因为我们的系统内存是有限的

184
00:04:04,740 --> 00:04:05,720
那么你开启一个worker的时候

185
00:04:05,720 --> 00:04:06,900
是不是就会占据我们的内存

186
00:04:06,900 --> 00:04:07,800
那么还有一种情况呢

187
00:04:07,800 --> 00:04:08,760
就是咱们的crush

188
00:04:08,760 --> 00:04:10,900
那么crush其实它是咱们的一个操作系统

189
00:04:10,900 --> 00:04:11,520
导致的

190
00:04:11,520 --> 00:04:12,880
那么什么意思呢

191
00:04:12,880 --> 00:04:14,520
OM和系统一场说白了

192
00:04:14,520 --> 00:04:16,500
它的原因就是咱们操作系统引起的一些异常

193
00:04:16,500 --> 00:04:18,380
这些异常呢我们是无法捕获的

194
00:04:18,380 --> 00:04:19,520
咱们举个例子

195
00:04:19,520 --> 00:04:21,820
你比如说你的电脑就是一台服器

196
00:04:21,820 --> 00:04:23,200
那么此时我把你的电源给拔了

197
00:04:23,200 --> 00:04:24,320
你的服务器是不是断掉了

198
00:04:24,320 --> 00:04:25,320
那么你可以通过代码

199
00:04:25,320 --> 00:04:27,000
可以通过Uncut Exception去捕获了吗

200
00:04:27,000 --> 00:04:27,480
捕获不到吧

201
00:04:27,480 --> 00:04:28,260
因为我直接把你的

202
00:04:28,260 --> 00:04:29,920
是不是把你的插头都给拔了

203
00:04:29,920 --> 00:04:31,160
所以此时就捕获不到

204
00:04:31,160 --> 00:04:31,560
错误

205
00:04:31,560 --> 00:04:32,800
那么这里呢就是咱们

206
00:04:32,800 --> 00:04:34,660
在项目里面常见的两种错误

207
00:04:34,660 --> 00:04:37,840
那么我们来看一下一段注释

208
00:04:37,840 --> 00:04:38,940
那么在框架里

209
00:04:38,940 --> 00:04:40,000
我们常常采用

210
00:04:40,000 --> 00:04:41,880
Grasp和EGG Cluster

211
00:04:41,880 --> 00:04:43,380
两个模块配合实现上面的逻辑

212
00:04:43,380 --> 00:04:43,940
这套方案呢

213
00:04:43,940 --> 00:04:46,300
已经在阿里巴巴和蚂蚁金服的生产环境广泛使用

214
00:04:46,300 --> 00:04:48,320
而且经受过双十一的大处的考验

215
00:04:48,320 --> 00:04:50,220
所以是相对稳定和靠谱的

216
00:04:50,220 --> 00:04:50,780
说明一个什么问题

217
00:04:50,780 --> 00:04:52,140
说明

218
00:04:52,140 --> 00:04:54,660
说明在那一套进程守护的机制

219
00:04:54,660 --> 00:04:56,620
是完全可以去

220
00:04:56,620 --> 00:04:59,960
是不是可以去应付一些高频化的场景

221
00:04:59,960 --> 00:05:01,880
那么这里可能有同学会有疑问的

222
00:05:01,880 --> 00:05:02,920
既然这种方式很好

223
00:05:02,920 --> 00:05:04,780
包括我们肉类杰S原生提供也可以做到

224
00:05:04,780 --> 00:05:07,200
那么一GG它到底有没有做一些额外的事情

225
00:05:07,200 --> 00:05:08,320
那我们接着往下看

226
00:05:08,320 --> 00:05:09,000
大家不要着急

227
00:05:09,000 --> 00:05:11,560
我们这里就看一下进程的一个机制

228
00:05:11,560 --> 00:05:13,640
那么这里也是引用官方的一段话

229
00:05:13,640 --> 00:05:14,300
说到这里

230
00:05:14,300 --> 00:05:15,560
那么肉类杰S多性程的方案

231
00:05:15,560 --> 00:05:16,260
贸事已经成行

232
00:05:16,260 --> 00:05:17,120
为什么贸事已经成行呢

233
00:05:17,120 --> 00:05:17,740
因为上面说的

234
00:05:17,740 --> 00:05:19,260
他们经受了双十一的考验

235
00:05:19,260 --> 00:05:21,260
所以贸事已经成行

236
00:05:21,260 --> 00:05:22,620
这也是早期使用的方案

237
00:05:22,620 --> 00:05:23,660
说明了早期使用的方案

238
00:05:23,660 --> 00:05:24,660
他可以经受住考验

239
00:05:24,660 --> 00:05:25,000
但是呢

240
00:05:25,000 --> 00:05:26,360
是不是还会有一些小的问题

241
00:05:26,360 --> 00:05:27,920
但是后来发现

242
00:05:27,920 --> 00:05:29,320
工作中其实并不需要

243
00:05:29,320 --> 00:05:30,700
每一个worker都去干活

244
00:05:30,700 --> 00:05:31,940
那么如果都来做呢

245
00:05:31,940 --> 00:05:33,020
会浪费资源

246
00:05:33,020 --> 00:05:33,820
那么什么意思

247
00:05:33,820 --> 00:05:36,420
其实他这里需要表达的意思是什么呢

248
00:05:36,420 --> 00:05:37,060
也就是说

249
00:05:37,060 --> 00:05:38,480
咱们的worker

250
00:05:38,480 --> 00:05:39,400
咱们的worker

251
00:05:39,400 --> 00:05:40,100
咱们有些工作

252
00:05:40,100 --> 00:05:41,640
是不是并不需要每一个worker都去做

253
00:05:41,640 --> 00:05:42,620
那么如果都来做呢

254
00:05:42,620 --> 00:05:43,680
就会浪费资源

255
00:05:43,680 --> 00:05:45,060
那么举个例子是什么呢

256
00:05:45,060 --> 00:05:46,900
比如说在每天的零点

257
00:05:46,900 --> 00:05:48,800
我们要将刚前的日志文件

258
00:05:48,800 --> 00:05:50,540
按照日期进行重新命名

259
00:05:50,540 --> 00:05:53,080
而且会去销毁一些以前的日志文件

260
00:05:53,080 --> 00:05:54,460
简单点是什么意思呢

261
00:05:54,460 --> 00:05:55,720
就是说我们的Worker

262
00:05:55,720 --> 00:05:58,020
在某一个时间去操作日志

263
00:05:58,020 --> 00:05:59,340
那么这里是不是会造成一个问题

264
00:05:59,340 --> 00:06:00,740
我们之前讲多线程的时候

265
00:06:00,740 --> 00:06:01,720
是不是解释过这样一个问题

266
00:06:01,720 --> 00:06:03,400
GS为什么是多线程呢

267
00:06:03,400 --> 00:06:05,020
GS为什么是单线程

268
00:06:05,020 --> 00:06:05,900
它不能是多线程呢

269
00:06:05,900 --> 00:06:06,400
为什么呢

270
00:06:06,400 --> 00:06:07,420
因为如果说你多个线程

271
00:06:07,420 --> 00:06:08,840
多个线程去操作同一个动物

272
00:06:08,840 --> 00:06:09,740
此时是不是会造成冲突

273
00:06:09,740 --> 00:06:11,940
那么这里就和我们的一个日志文件的操作

274
00:06:11,940 --> 00:06:12,620
非常类似

275
00:06:12,620 --> 00:06:13,940
你比如说你的系统是8核

276
00:06:13,940 --> 00:06:15,060
你是不是8个work

277
00:06:15,060 --> 00:06:17,720
那么这8个work如果同时去操作一个日子文件

278
00:06:17,720 --> 00:06:19,020
是不是有可能造成冲突

279
00:06:19,020 --> 00:06:20,780
所以这里在1GG里面

280
00:06:20,780 --> 00:06:22,740
它就加入了一个agent机制

281
00:06:22,740 --> 00:06:24,340
所以这是1GG的一个特点

282
00:06:24,340 --> 00:06:26,980
所以我们这里稍微做一下笔记

283
00:06:26,980 --> 00:06:29,640
1GG和loadGS

284
00:06:29,640 --> 00:06:33,480
是不是原生多进程不同的地方

285
00:06:33,480 --> 00:06:35,740
所以这是咱们的一个核心

286
00:06:35,740 --> 00:06:37,360
它加入了一个agent这样的机制

287
00:06:37,360 --> 00:06:39,340
那么我们接下来继续来看一下agent到底是什么

288
00:06:39,340 --> 00:06:40,580
好

289
00:06:40,580 --> 00:06:42,720
那么我们接下来继续看一下agint到底是什么

290
00:06:42,720 --> 00:06:44,760
首先我们来看一下这张图

291
00:06:44,760 --> 00:06:49,380
那么以往我们的一个cluster的模型是什么呀

292
00:06:49,380 --> 00:06:50,760
是不是一个master下面呢有很多工人

293
00:06:50,760 --> 00:06:52,040
我们之前是不是也画过类似的图

294
00:06:52,040 --> 00:06:53,360
那么这里呢在egg里面

295
00:06:53,360 --> 00:06:54,860
他额外添加了一个agint

296
00:06:54,860 --> 00:06:56,280
那么agint是什么

297
00:06:56,280 --> 00:06:57,600
我们来看一下他一个解释

298
00:06:57,600 --> 00:07:00,860
那么agint就好比master给其他的worker请了一个秘书

299
00:07:00,860 --> 00:07:02,360
他不对外提供服务

300
00:07:02,360 --> 00:07:03,740
只给appworker把工

301
00:07:03,740 --> 00:07:04,840
专门了处理一些公共事务

302
00:07:04,840 --> 00:07:06,720
现在我们的多进程模型就变成了下面这个样子

303
00:07:06,720 --> 00:07:07,920
那么agint是什么

304
00:07:07,920 --> 00:07:09,460
其实说到这里大家可能有点迷

305
00:07:09,460 --> 00:07:10,000
这里呢

306
00:07:10,000 --> 00:07:11,120
我来给大家去

307
00:07:11,120 --> 00:07:12,440
稍微的简单的去梳理一下

308
00:07:12,440 --> 00:07:14,100
首先我们的CPU

309
00:07:14,100 --> 00:07:15,200
是不是假如我们CPU是8核

310
00:07:15,200 --> 00:07:15,920
那么此时

311
00:07:15,920 --> 00:07:17,380
如果说我们加入了Gint

312
00:07:17,380 --> 00:07:18,000
我们的Walker

313
00:07:18,000 --> 00:07:18,860
此时变成了几个

314
00:07:18,860 --> 00:07:19,320
是不是7个

315
00:07:19,320 --> 00:07:20,400
为什么呀

316
00:07:20,400 --> 00:07:21,340
因为刚才讲到了

317
00:07:21,340 --> 00:07:22,660
Gint好比给其他的Marts

318
00:07:22,660 --> 00:07:23,180
请了一个秘书

319
00:07:23,180 --> 00:07:23,480
对吧

320
00:07:23,480 --> 00:07:24,880
所以我们分配了一个

321
00:07:24,880 --> 00:07:26,220
是不是分配了一个CPU的核

322
00:07:26,220 --> 00:07:27,200
给了咱们的一个Gint

323
00:07:27,200 --> 00:07:29,220
所以咱们以前的8个Walker

324
00:07:29,220 --> 00:07:30,240
分为了7和1

325
00:07:30,240 --> 00:07:31,880
那么Gint就相当于是一个秘书

326
00:07:31,880 --> 00:07:32,560
比如说很简单

327
00:07:32,560 --> 00:07:33,640
我去带一个团队

328
00:07:33,640 --> 00:07:34,940
我去管理8个人

329
00:07:34,940 --> 00:07:36,380
但是呢我发现这八个人很不好管

330
00:07:36,380 --> 00:07:38,160
我需要呢有一个人帮我去做一些琐事

331
00:07:38,160 --> 00:07:39,620
所以呢此时呢我请了一个秘书

332
00:07:39,620 --> 00:07:42,220
但是呢公司给了预算工资预算又不贵又不够

333
00:07:42,220 --> 00:07:43,480
对吧比如说每人工资一千块

334
00:07:43,480 --> 00:07:45,160
公司只给了八千块预算让我去招人

335
00:07:45,160 --> 00:07:46,520
那么我招了一个秘书之后

336
00:07:46,520 --> 00:07:48,500
是不是需要把啊把我们啊

337
00:07:48,500 --> 00:07:50,000
我可给辞退一个对吧

338
00:07:50,000 --> 00:07:51,600
这里呢就是咱们进的一个概念

339
00:07:51,600 --> 00:07:53,680
我们来看一下咱们进的

340
00:07:53,680 --> 00:07:55,320
他的一个启动顺序是怎么样的

341
00:07:55,320 --> 00:07:59,380
我们之前如果说没有进程

342
00:07:59,380 --> 00:08:00,500
是不是首先创建一个妈的

343
00:08:00,500 --> 00:08:02,380
然后根据咱们cpu的核数去复合八次

344
00:08:02,380 --> 00:08:03,280
我们此时来看一下

345
00:08:03,280 --> 00:08:04,200
加入了进去之后

346
00:08:04,200 --> 00:08:05,680
咱们的一个启动过程是什么样的

347
00:08:05,680 --> 00:08:08,340
好

348
00:08:08,340 --> 00:08:08,880
首先呢

349
00:08:08,880 --> 00:08:10,740
我们master还是先去第一步

350
00:08:10,740 --> 00:08:11,100
首先呢

351
00:08:11,100 --> 00:08:12,720
去是不是去启动我们的进去进程

352
00:08:12,720 --> 00:08:13,580
所以说第一步

353
00:08:13,580 --> 00:08:15,000
第一步咱们先找秘书

354
00:08:15,000 --> 00:08:15,500
然后呢

355
00:08:15,500 --> 00:08:16,720
我们秘书找好之后

356
00:08:16,720 --> 00:08:18,040
咱们的通知master

357
00:08:18,040 --> 00:08:19,160
我们的秘书找好了

358
00:08:19,160 --> 00:08:20,240
那么秘书找好之后

359
00:08:20,240 --> 00:08:22,840
是不是再来去找我们的工人了

360
00:08:22,840 --> 00:08:23,000
好

361
00:08:23,000 --> 00:08:23,980
那么工人找完之后

362
00:08:23,980 --> 00:08:24,500
就通知master

363
00:08:24,500 --> 00:08:25,840
告诉我们的应用程序

364
00:08:25,840 --> 00:08:27,180
我们现在可以开始干活了

365
00:08:27,180 --> 00:08:29,080
我们的秘书和功能都已经完毕了

366
00:08:29,080 --> 00:08:31,240
那么另外关于

367
00:08:31,240 --> 00:08:32,920
啊进的我可能还有几点需要注意

368
00:08:32,920 --> 00:08:34,340
还几点需要注意

369
00:08:34,340 --> 00:08:37,200
那么由于 app 涡了他依赖于进的

370
00:08:37,200 --> 00:08:41,660
所以我们必须等待进的出事完成之后才能去fork app 涡什么意思

371
00:08:41,660 --> 00:08:45,720
说明了我们的功能是依赖于秘书的我们的秘书优先级是比功能高的就是这么个意思

372
00:08:45,720 --> 00:08:51,220
好 那么来进的它虽然是咱们涡口小蜜但是业务相关的工作不应该放了来进的上去做

373
00:08:51,220 --> 00:08:54,800
什么意思是不是说明咱们来进的是不负责具体的业务

374
00:08:54,800 --> 00:08:57,880
比如说工人需要搬装那么秘书要不要搬装不用吧对吧

375
00:08:58,540 --> 00:09:02,900
好 那么呢 还有一个 我们需要注意 那么agent它的定位非常的特殊

376
00:09:02,900 --> 00:09:04,980
 所以我们需要保证它那个稳定 什么意思

377
00:09:04,980 --> 00:09:09,090
那么我们的功能挂了 是不是有master去给他重启 但是呢 我们的agent挂了

378
00:09:09,090 --> 00:09:10,540
 不会有人给他重启

379
00:09:10,540 --> 00:09:14,740
所以我们必须在agent里面去做一些日子的记录 我们要时刻关注我们的一个日子

380
00:09:14,740 --> 00:09:19,600
好 这里呢 就是我们关于agent 它那个介绍 我们来简单的回顾一下

381
00:09:19,600 --> 00:09:26,580
我们刚才是不是讲到了咱们的一级镜里面那个多镜层 对吧 它是不是相比

382
00:09:26,580 --> 00:09:28,260
 相比我们low的gds原生

383
00:09:28,540 --> 00:09:29,840
新增了一个什么

384
00:09:29,840 --> 00:09:30,580
新增了一个

385
00:09:30,580 --> 00:09:32,320
Gent的概念

386
00:09:32,320 --> 00:09:35,180
它是不是相当于我们Walker的一个秘书

387
00:09:35,180 --> 00:09:36,820
那么Gent它的作用是什么

388
00:09:36,820 --> 00:09:38,260
是不是我们还没讲

389
00:09:38,260 --> 00:09:39,240
那么接下来呢

390
00:09:39,240 --> 00:09:39,880
我们就来看一下

391
00:09:39,880 --> 00:09:41,920
Gent它到底是做什么用的

392
00:09:41,920 --> 00:09:42,800
以及怎么用

393
00:09:42,800 --> 00:09:43,360
好这里呢

394
00:09:43,360 --> 00:09:44,680
我们就先讲到这里

