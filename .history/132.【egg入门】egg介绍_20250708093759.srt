1
00:00:00,000 --> 00:00:02,680
好 这节课我们就来看一下第二章的内容一GG入门

2
00:00:02,680 --> 00:00:05,280
那么这节课我们主要来讲解一GG是什么

3
00:00:05,280 --> 00:00:08,980
好 那么一GG呢 它实际上是由阿里巴巴团队开源的一款

4
00:00:08,980 --> 00:00:10,540
基于Core的应用框架

5
00:00:10,540 --> 00:00:12,960
Core我们之前是不是已经学习过了呀

6
00:00:12,960 --> 00:00:15,400
而且呢 已经在集团内部服务了大量的楼梯件事系统

7
00:00:15,400 --> 00:00:16,920
说明了一GG这样一个框架

8
00:00:16,920 --> 00:00:19,700
是不是在阿里巴巴他们的团队里面使用的非常的广泛

9
00:00:19,700 --> 00:00:22,240
好 那么我们来看一下它的官网是什么样的

10
00:00:22,240 --> 00:00:23,140
可以说是非常精美

11
00:00:23,140 --> 00:00:25,640
这里呢 有一句话 为企业及框架和应用而生

12
00:00:25,640 --> 00:00:27,360
那么其实这样一句话就可以去解释

13
00:00:27,360 --> 00:00:28,520
一机器它是做什么的

14
00:00:28,520 --> 00:00:30,600
是不是专门去解决我们企业的一些问题

15
00:00:30,600 --> 00:00:31,800
好 我们来看一下

16
00:00:31,800 --> 00:00:33,160
它官网里面怎么样去解释

17
00:00:33,160 --> 00:00:34,040
这样一个框架

18
00:00:34,040 --> 00:00:35,580
那么一机器是什么

19
00:00:35,580 --> 00:00:38,160
它为企业极框架和应用而生

20
00:00:38,160 --> 00:00:39,280
什么意思

21
00:00:39,280 --> 00:00:41,300
是不是专门为企业极打造的

22
00:00:41,300 --> 00:00:42,480
也就是说专门去做复杂的应用

23
00:00:42,480 --> 00:00:43,820
好 我们重点关注一下

24
00:00:43,820 --> 00:00:45,900
一机器它的一个设计原则是什么

25
00:00:45,900 --> 00:00:48,800
他们的官方文档就说到

26
00:00:48,800 --> 00:00:50,180
我们深知企业极应用

27
00:00:50,180 --> 00:00:51,840
在追求规范和共建的同时

28
00:00:51,840 --> 00:00:54,380
还需要考虑如何平衡不同团队之间的差异

29
00:00:54,380 --> 00:00:55,120
求同存异

30
00:00:55,120 --> 00:00:55,640
什么意思

31
00:00:55,640 --> 00:00:57,160
其实我们一般在企业中

32
00:00:57,160 --> 00:00:59,000
是不是分为很多的部门

33
00:00:59,000 --> 00:01:00,000
比如说你是做OE的

34
00:01:00,000 --> 00:01:01,740
它是做电商的

35
00:01:01,740 --> 00:01:03,700
然后它还有可能是做一些其他的业务

36
00:01:03,700 --> 00:01:04,160
我们呢

37
00:01:04,160 --> 00:01:05,440
不同的部门业务

38
00:01:05,440 --> 00:01:06,540
业务不一样

39
00:01:06,540 --> 00:01:08,120
你对框架的需求是不是也就不一样

40
00:01:08,120 --> 00:01:08,800
比如说我们举个例子

41
00:01:08,800 --> 00:01:10,280
假如说你是做后台的

42
00:01:10,280 --> 00:01:12,540
你可能比较适合做一些SPA的页面

43
00:01:12,540 --> 00:01:14,600
那么如果说你是做一些QC的

44
00:01:14,600 --> 00:01:15,040
比如说淘宝

45
00:01:15,040 --> 00:01:16,280
或者说是一些资讯类

46
00:01:16,280 --> 00:01:17,540
像头条这样的网站

47
00:01:17,540 --> 00:01:18,640
那么你是不是要考虑SEO

48
00:01:18,640 --> 00:01:19,800
那么根据业务的不一样

49
00:01:19,800 --> 00:01:20,700
肯定我们的框架

50
00:01:20,700 --> 00:01:22,200
选择也不一样

51
00:01:22,200 --> 00:01:23,480
那么呢

52
00:01:23,480 --> 00:01:24,520
他这里就考虑到

53
00:01:24,520 --> 00:01:25,980
去平衡不同团队之间的差异

54
00:01:25,980 --> 00:01:27,860
所以我们没有选择社区

55
00:01:27,860 --> 00:01:29,560
常见框架的大集市模式

56
00:01:29,560 --> 00:01:30,540
比如说集成一些数据库

57
00:01:30,540 --> 00:01:31,100
模板引擎

58
00:01:31,100 --> 00:01:31,820
前端框架等等

59
00:01:31,820 --> 00:01:33,180
在一机记里面

60
00:01:33,180 --> 00:01:34,260
说明他是没有的

61
00:01:34,260 --> 00:01:35,580
而是专注于提供

62
00:01:35,580 --> 00:01:37,100
web开发的核心功能和一套

63
00:01:37,100 --> 00:01:39,340
灵活可扩展的插件机制

64
00:01:39,340 --> 00:01:40,460
而且呢

65
00:01:40,460 --> 00:01:41,860
我们不会做出技术选行

66
00:01:41,860 --> 00:01:42,900
因为固定的技术选行

67
00:01:42,900 --> 00:01:44,160
会使框架的扩展性变差

68
00:01:44,160 --> 00:01:45,380
无法满足定制需求

69
00:01:45,380 --> 00:01:47,600
通过一GG团队的架构师和技术负责人

70
00:01:47,600 --> 00:01:48,340
可以非常的容易

71
00:01:48,340 --> 00:01:49,760
基于自身的技术架构

72
00:01:49,760 --> 00:01:50,980
去扩展一些

73
00:01:50,980 --> 00:01:52,520
适合自身业务场地的框架

74
00:01:52,520 --> 00:01:54,240
其实我们这里可以提炼出什么信息

75
00:01:54,240 --> 00:01:55,500
是不是说明一GG这样一个框架

76
00:01:55,500 --> 00:01:57,620
它是可以说是非常的灵活

77
00:01:57,620 --> 00:01:58,760
可以满足于

78
00:01:58,760 --> 00:02:00,500
你们是不是不同团队之间的一些差异

79
00:02:00,500 --> 00:02:01,360
你什么样的团队

80
00:02:01,360 --> 00:02:01,800
什么样的业务

81
00:02:01,800 --> 00:02:03,200
都可以考虑去使用一GG

82
00:02:03,200 --> 00:02:04,660
那么它的一个插件机制

83
00:02:04,660 --> 00:02:06,360
有很高的一些扩展性

84
00:02:06,360 --> 00:02:08,160
而且在一GG里面是奉行

85
00:02:08,160 --> 00:02:09,920
约定优于配置

86
00:02:09,920 --> 00:02:11,640
按照一套统一的约定进行开发

87
00:02:11,640 --> 00:02:12,300
什么意思

88
00:02:12,300 --> 00:02:14,740
这说明在一GG这样一个框架里面

89
00:02:14,740 --> 00:02:15,860
它肯定会有一些约束

90
00:02:15,860 --> 00:02:16,920
你比如说我们去写react

91
00:02:16,920 --> 00:02:17,900
去写view的时候

92
00:02:17,900 --> 00:02:19,540
我们是不是要按照它的一些生命周期去写

93
00:02:19,540 --> 00:02:20,600
比如说我们去发起请求

94
00:02:20,600 --> 00:02:21,760
你必须要在component

95
00:02:21,760 --> 00:02:22,660
deamount里面去写

96
00:02:22,660 --> 00:02:22,960
对吧

97
00:02:22,960 --> 00:02:24,060
那么一GG同样的

98
00:02:24,060 --> 00:02:25,260
它也会去约定一些

99
00:02:25,260 --> 00:02:26,500
比如说它的一些生命周期

100
00:02:26,500 --> 00:02:28,060
或者说它的一些目录的规范

101
00:02:28,060 --> 00:02:30,080
你必须按照它的这样的约定去写

102
00:02:30,080 --> 00:02:30,840
这样是不是可以降低

103
00:02:30,840 --> 00:02:32,640
对于我们团队之间的一个沟通成本了对吧

104
00:02:32,640 --> 00:02:35,200
好这里呢是一GG他那个核心思想

105
00:02:35,200 --> 00:02:41,340
然后这里呢在官网他还介绍了与社区框架的一些差异比如说和express和CS的一些

106
00:02:41,340 --> 00:02:43,380
差异不过总的来说

107
00:02:43,380 --> 00:02:48,000
总的来说这里呢我就不去详细介绍总的来说一GG是优于他们的好那么我们来看一下他有哪些特性

108
00:02:48,000 --> 00:02:53,360
提供基于一GG定制上层框架能力说明什么他有一个什么扩展性

109
00:02:53,360 --> 00:02:55,160
而且呢高度可扩展的插件机制

110
00:02:55,160 --> 00:02:59,520
也说明他呢可以去写一些插件比如说我们的webpack对吧你可以去写些loader包括了一些插件

111
00:02:59,520 --> 00:03:01,520
而且呢会内置多进程管理

112
00:03:01,520 --> 00:03:03,520
我们之前是不是学习过nodejs的什么呀

113
00:03:03,520 --> 00:03:04,360
是不是多进程

114
00:03:04,360 --> 00:03:07,200
包括了我们去部署的时候是不是要去利用咱们cpu的多核

115
00:03:07,200 --> 00:03:09,000
那么在一期里面的他其实是内置的

116
00:03:09,000 --> 00:03:10,920
而且呢他是基于core2开发的

117
00:03:10,920 --> 00:03:13,080
什么意思是说明他的底层是基于core的

118
00:03:13,080 --> 00:03:13,720
而且呢

119
00:03:13,720 --> 00:03:15,600
框架稳定测试覆盖率高

120
00:03:15,600 --> 00:03:16,840
这里呢就说明他有框架很稳定

121
00:03:16,840 --> 00:03:17,960
而且呢是渐进式开发

122
00:03:17,960 --> 00:03:18,800
那么什么是渐进式开发

123
00:03:18,800 --> 00:03:20,920
我们我们后面的人会去介绍

124
00:03:20,920 --> 00:03:22,960
好这里呢就是他官网对一期的一个介绍

125
00:03:22,960 --> 00:03:25,240
我们来回到我们的讲义

126
00:03:25,240 --> 00:03:29,240
这里其实我们可以用一张图来带同他们去理解一下一机械它到底是做了什么事情

127
00:03:29,240 --> 00:03:32,000
你比如说我们去做后台开发的时候

128
00:03:32,000 --> 00:03:35,380
我们的底层是不是基于标击s 然后基于标击s 咱们的社区是不是诞生了

129
00:03:35,380 --> 00:03:36,000
Core

130
00:03:36,000 --> 00:03:40,240
对吧 好 那么Core其实它是不是需要去写很多的一些中间件

131
00:03:40,240 --> 00:03:42,640
这些中间件的话 其实我们每个业务不可能说

132
00:03:42,640 --> 00:03:45,780
去重新写 我们可能会用一些第三方开源的

133
00:03:45,780 --> 00:03:49,060
那么呢 一机既然它就是基于Core去封装一套自己的东西

134
00:03:49,060 --> 00:03:52,720
然后你还可以去一机 还可以基于一机器去扩展自己的业务

135
00:03:52,720 --> 00:03:54,520
因为他刚才咱们在文档里面是不是看到了

136
00:03:54,700 --> 00:03:59,060
他去他可以解决一些团队之间的差异性 你比如说你是蚂蚁团队对吧 蚂蚁金服

137
00:03:59,060 --> 00:04:03,140
他就可以自己的团队去扩展一套基于1G去扩展了他自己的一套

138
00:04:03,140 --> 00:04:08,260
framework framework是不是自己的一个框架 你比如说阿里民也可以呢 基于1G去扩展一套自己的框架

139
00:04:08,260 --> 00:04:08,780
 包括

140
00:04:08,780 --> 00:04:09,300
UC

141
00:04:09,300 --> 00:04:14,420
好 那么这里呢 当他的框架构建完成之后 其实这部分主要是是不是团队的架构师去做的

142
00:04:14,420 --> 00:04:14,920
 你比如说

143
00:04:14,920 --> 00:04:18,000
你在蚂蚁工作 或者未来呢 你会去UC或者阿里民去工作

144
00:04:18,260 --> 00:04:19,280
你们团队是不是有一个架构师

145
00:04:19,280 --> 00:04:21,240
专门来就一GG去封装一些框架

146
00:04:21,240 --> 00:04:22,540
然后你拿着去用

147
00:04:22,540 --> 00:04:25,000
给予上层框架开发的应用

148
00:04:25,000 --> 00:04:25,680
上层框架是谁

149
00:04:25,680 --> 00:04:27,460
是不是咱们的这一层啊

150
00:04:27,460 --> 00:04:28,120
所以说呢

151
00:04:28,120 --> 00:04:30,740
咱们的开发者只需要关注最上层底层呢

152
00:04:30,740 --> 00:04:31,880
可以说是团队的一些架构师

153
00:04:31,880 --> 00:04:33,920
或者说非常熟悉业务的人去做一些封装

154
00:04:33,920 --> 00:04:34,620
这里呢就是

155
00:04:34,620 --> 00:04:38,960
这里呢就是一GG他在整个业务和团队里面所处的一个位置

156
00:04:38,960 --> 00:04:40,860
好那么我们来

157
00:04:40,860 --> 00:04:44,600
那么呢这里呢我们就来总结一下我们刚才所讲解的内容

158
00:04:47,300 --> 00:04:49,340
好刚才我们是不是讲到了咱们的

159
00:04:49,340 --> 00:04:51,900
咱们call它是什么呀对吧

160
00:04:51,900 --> 00:04:53,440
哦不对是一GG入门

161
00:04:53,440 --> 00:04:57,540
一GG入门好我们刚才是不是讲到了一GG它是什么呀

162
00:04:57,540 --> 00:04:58,820
但还有印象它是什么吗

163
00:04:58,820 --> 00:05:03,940
它是不是基于call封装的一个企业级

164
00:05:03,940 --> 00:05:05,980
企业级应用框架

165
00:05:05,980 --> 00:05:07,780
那么它有什么特点啊

166
00:05:07,780 --> 00:05:09,820
是不是可以写插件

167
00:05:09,820 --> 00:05:12,380
而且呢是不是可以根据

168
00:05:12,380 --> 00:05:14,440
业务去封装

169
00:05:14,440 --> 00:05:16,220
自己的什么呀是不是frame

170
00:05:16,220 --> 00:05:17,000
frame

171
00:05:17,300 --> 00:05:21,140
framework是什么是不是就是你给予你自己不同的业务团队去封装自己的插件

172
00:05:21,140 --> 00:05:22,420
而且他的核心思想是什么

173
00:05:22,420 --> 00:05:23,440
约定

174
00:05:23,440 --> 00:05:24,980
优于

175
00:05:24,980 --> 00:05:27,280
配置

176
00:05:27,280 --> 00:05:31,900
但就可以去减少团队之间的一个沟通成本好这里就是我们这节课的内容

