1
00:00:00,000 --> 00:00:04,480
好 这节课我们就来看一下我们具体怎么样去实现我们在一个leadfollow的一个模式

2
00:00:04,480 --> 00:00:09,400
那么接下来下面的一些代码都是我从egg的官网所找过来它的一个demo

3
00:00:09,400 --> 00:00:11,080
那么这里呢 我就带着同学们去理解一下

4
00:00:11,080 --> 00:00:16,580
首先呢 我们第一步是不是要去实现我们刚才所约定的一个抽象类啊

5
00:00:16,580 --> 00:00:18,900
也就是我们前面所介绍的咱们的一个客户端接口类型的抽象

6
00:00:18,900 --> 00:00:23,040
它的核心是什么呀 是不是实现一个subscribe和一个publish

7
00:00:23,040 --> 00:00:25,400
包括了一个是不是咱们一个数据的调用类啊

8
00:00:25,400 --> 00:00:27,280
对吧 好 我们来看一下 首先

9
00:00:28,960 --> 00:00:31,260
首先咱们引入的url和base

10
00:00:31,260 --> 00:00:35,100
我们来去定一个registeredclient

11
00:00:35,100 --> 00:00:36,900
咱们是否计成的 计成于base

12
00:00:36,900 --> 00:00:38,180
然后在construct里面

13
00:00:38,180 --> 00:00:39,460
大家去注意看 这里有一个

14
00:00:39,460 --> 00:00:41,760
地方叫做enit method

15
00:00:41,760 --> 00:00:44,320
咱们是super 里面去调用 调用base的构造函数

16
00:00:44,320 --> 00:00:45,080
去传入一个配置

17
00:00:45,080 --> 00:00:47,140
它可以把咱们的ready方法改为enit

18
00:00:47,140 --> 00:00:49,180
大家可以看到我们这里是不是有一个enit的方法

19
00:00:49,180 --> 00:00:50,720
这里才会去调用decent ready

20
00:00:50,720 --> 00:00:53,020
其实它是为了进行什么 其实这里已经写好了注释

21
00:00:53,020 --> 00:00:54,300
去指定咱们的一个一步启动

22
00:00:54,300 --> 00:00:56,360
然后这里在构造函数里面去添加了两个属性

23
00:00:56,360 --> 00:00:57,880
一个是option 一个是register

24
00:00:58,140 --> 00:01:03,520
然后register呢 其实它是一个map 然后option呢就是咱们类里面所传递过来的一个配置文件

25
00:01:03,520 --> 00:01:07,360
好 那么这里呢 首先这是一个获取配置的方法 这里就无需过多的介绍

26
00:01:07,360 --> 00:01:09,400
然后subscribe 我们来看一下它是怎么去实现的

27
00:01:09,400 --> 00:01:13,760
那么subscribe呢 首先 我们会读取到我们所坚定subscribe是什么 是不是订阅

28
00:01:13,760 --> 00:01:19,140
我们的k呢 是不是获取到我们所订阅的事件的名称 对吧 用k去代表它 因为我们的reg是什么样

29
00:01:19,140 --> 00:01:23,030
reg 实际上就是我们订阅的事件名 那么事件名其实这里呢 它是用一个对象去表示的

30
00:01:23,030 --> 00:01:23,220
 为什么

31
00:01:23,220 --> 00:01:24,760
它这里调用了reg dataid

32
00:01:25,280 --> 00:01:27,480
然后返回了一个k 这里呢 他通过this.n

33
00:01:27,480 --> 00:01:31,480
this.n 那么this.n 看起来是不是有点像我们去订阅一个事件呢

34
00:01:31,480 --> 00:01:33,980
是不是像咱们nodejs里面的什么呀 是不是even and meet

35
00:01:33,980 --> 00:01:37,380
那么难道我们的base它是继承于咱们的一个even and meet吗

36
00:01:37,380 --> 00:01:38,680
实际上确实是这样的

37
00:01:38,680 --> 00:01:43,310
因为我自己来下去查过一些资料 确实是咱们的base是继承于咱们的nodejs的一个even

38
00:01:43,310 --> 00:01:43,580
 and meet对象

39
00:01:43,580 --> 00:01:44,280
好 那么这里呢

40
00:01:44,280 --> 00:01:47,580
咱们通过on去是不是去订阅一个k 然后呢

41
00:01:47,580 --> 00:01:50,680
订阅的方法就是咱们的nees 通过subscribe所注册的

42
00:01:51,180 --> 00:01:55,220
那么如果说有一个data 咱们能通过map 里面去取 通过这个k去取 如果说有数据

43
00:01:55,220 --> 00:01:57,940
 我们还会去通过一步的方式去执行我们的一个

44
00:01:57,940 --> 00:02:00,060
如果我们所坚定的事件 那么这里呢就是他对

45
00:02:00,060 --> 00:02:04,120
咱们当前的client对我们订阅的一个是不是一个封装案 我们只要实现就可以了

46
00:02:04,120 --> 00:02:05,000
 好 那么来看一下发布

47
00:02:05,000 --> 00:02:10,120
那么发布的是public这样一个方法 首先还是会获取个k 然后他这里定义的开关叫做欠几的

48
00:02:10,120 --> 00:02:13,880
其实核心的思想是什么呀 如果说你的数据发生了变化 那么呢 我就去

49
00:02:13,880 --> 00:02:19,600
emmit 是什么呀 emmit 是不是就是我们的一个发布啊 那么nodegns 他是不是一个on

50
00:02:19,600 --> 00:02:20,100
 一个emmit

51
00:02:20,360 --> 00:02:24,600
那么呢 其实这里呢 我们只是订了一个什么 一个subscribe 一个publish 其实它的作用是类似的

52
00:02:24,600 --> 00:02:28,800
好 那么我们就去发布一个k 然后呢 再把这样一些方法所需要的参数给传入

53
00:02:28,800 --> 00:02:32,640
 这里就实现了我们是不是实现了我们一个client这样一个类 对吧

54
00:02:32,640 --> 00:02:34,240
好 那么我们实现了

55
00:02:34,240 --> 00:02:39,000
实现了我们刚才对客户端client的一个抽象 那么接下来我们是不是就要进行第二步

56
00:02:39,000 --> 00:02:43,600
那么刚才我们所实现的client 其实有些同学很疑惑 我实现一个publish 一个subscribe

57
00:02:43,600 --> 00:02:44,160
 到底干什么呀

58
00:02:44,160 --> 00:02:48,520
是不是需要我们来进程和worker去通信 对吧 那么我们刚才既然实现了一个client

59
00:02:48,520 --> 00:02:49,760
 那么它其实是什么呀

60
00:02:50,360 --> 00:02:52,160
其实这里呢没有给同学们去讲

61
00:02:52,160 --> 00:02:54,960
他其实就是进行

62
00:02:54,960 --> 00:02:57,280
socket

63
00:02:57,280 --> 00:03:02,400
基础类那么我们既然完成了socket他的一个基础类对吧

64
00:03:02,400 --> 00:03:07,520
那我完成了socket的基础类我们是随跟随通信是我们的agent worker那么接下来肯定是在agent

65
00:03:07,520 --> 00:03:09,040
 worker上面是去做一些文章对吧

66
00:03:09,040 --> 00:03:15,440
好那么这里呢首先我们去引入一个registerclient对吧好那么registerclient呢其实也是咱们进行socket的一个相关的库

67
00:03:15,440 --> 00:03:18,780
那么在github上面其实也可以说的大家可以去看一下他的一个

68
00:03:19,540 --> 00:03:24,660
介绍 其实和我们刚才的base sdk一样 其实也没有文档写的也非常少 我们都要去考拆

69
00:03:24,660 --> 00:03:26,700
 好 那么其实这里的不是重点 我们来看一下

70
00:03:26,700 --> 00:03:31,320
首先 我们在agint上面去挂载一个属性叫做registerclient

71
00:03:31,320 --> 00:03:36,440
然后呢 我们去通过它那个cluster方法 也就是什么呢 去创建了我们这样的一个实例

72
00:03:36,440 --> 00:03:37,200
 然后呢

73
00:03:37,200 --> 00:03:42,320
我们去调用它这样一个扣张的ready方法 说明我们的agint是不是已经准备就绪了

74
00:03:42,320 --> 00:03:47,860
所以呢 我们在corelogger 是不是在我们的框架上面去打印日资啊 咱们去打印一个info类型的日子

75
00:03:47,860 --> 00:03:49,500
 我们的registerclient is ready

76
00:03:49,540 --> 00:03:50,380
register代表什么

77
00:03:50,380 --> 00:03:51,200
registry

78
00:03:51,200 --> 00:03:52,160
registry代表什么

79
00:03:52,160 --> 00:03:52,900
是不是我们注册呀

80
00:03:52,900 --> 00:03:54,760
注册是不是就是我们的服务商业去相连

81
00:03:54,760 --> 00:03:55,160
对吧

82
00:03:55,160 --> 00:03:56,380
那么呢

83
00:03:56,380 --> 00:03:58,020
我们的agent呢

84
00:03:58,020 --> 00:03:58,900
既然负责注册

85
00:03:58,900 --> 00:04:00,140
那么我们就来看一下app

86
00:04:00,140 --> 00:04:01,020
做了一件什么事情

87
00:04:01,020 --> 00:04:02,020
那么什么是app啊

88
00:04:02,020 --> 00:04:03,340
app是不是就代表我们的

89
00:04:03,340 --> 00:04:04,760
代表我们的什么呀

90
00:04:04,760 --> 00:04:06,040
app是不是就代表我们的一个worker

91
00:04:06,040 --> 00:04:06,520
同样呢

92
00:04:06,520 --> 00:04:08,680
他也需要一个register这样的一个client

93
00:04:08,680 --> 00:04:10,060
那么其实他的一个核心

94
00:04:10,060 --> 00:04:10,980
核心是什么呢

95
00:04:10,980 --> 00:04:14,380
其实他上面也挂在了一个registerclient这样的一个属性

96
00:04:14,380 --> 00:04:14,760
好

97
00:04:14,760 --> 00:04:15,280
那么呢

98
00:04:15,280 --> 00:04:16,900
当他初始化完成create之后

99
00:04:16,900 --> 00:04:18,260
这样调用他的reality方法

100
00:04:18,260 --> 00:04:19,040
reality方法调用

101
00:04:19,040 --> 00:04:23,320
然后是不是在app上面就挂在了registry client这样的一个实力

102
00:04:23,320 --> 00:04:24,420
然后就可以调用

103
00:04:24,420 --> 00:04:27,440
是不是我们刚才我们扣到那个subscribe和我们的publish

104
00:04:27,440 --> 00:04:29,300
我们通过subscribe和publish

105
00:04:29,300 --> 00:04:32,620
是不是可以完成我们app和agint之间的一个通信呢

106
00:04:32,620 --> 00:04:34,820
其实这里呢就是我们去实现

107
00:04:34,820 --> 00:04:37,320
实现我们的一个led follow模式

108
00:04:37,320 --> 00:04:38,480
它那个具体的方法

109
00:04:38,480 --> 00:04:42,220
那么这一块呢同学们可能需要去反复的去看我们的视频

110
00:04:42,220 --> 00:04:44,420
包括了自己去看一下一级文档需要反复去读

111
00:04:44,420 --> 00:04:45,680
因为这里呢确实是比较复杂

112
00:04:45,680 --> 00:04:47,400
因为你们可能需要去看一下socket

113
00:04:47,400 --> 00:04:51,720
需要去看一下什么是你的佛罗 对吧 那么还要去看一下什么是心跳 包括了

114
00:04:51,720 --> 00:04:55,200
什么是subscribe 什么是publish 包括event 包括nodejs的

115
00:04:55,200 --> 00:04:58,460
event 这样一个对象 那么其实我们是不是

116
00:04:58,460 --> 00:05:02,280
学到一点深度之后 你需要去有很多的一个知识储备量

117
00:05:02,280 --> 00:05:06,760
那么其实这里呢 希望同学们去好好的领悟一下 那么这里呢 是一个纯粹对代码一个分析的过程

118
00:05:06,760 --> 00:05:09,500
所以呢 我就不去做一个总结了 那么有一位同学可以去

119
00:05:09,500 --> 00:05:12,020
多看几个视频 好 这里呢 就是我们这节奏的内容

