1
00:00:00,000 --> 00:00:02,680
好 这节课我们就进入HTTP模块的学习

2
00:00:02,680 --> 00:00:04,800
好 我们来看一下它的文档

3
00:00:04,800 --> 00:00:07,140
要使用HTTP服务器和客户端

4
00:00:07,140 --> 00:00:08,400
必须Requell HTTP

5
00:00:08,400 --> 00:00:11,380
说明它必须通过Requell的形式去引入

6
00:00:11,380 --> 00:00:12,860
它不是NodeGS的一个全球变量

7
00:00:12,860 --> 00:00:14,820
在NodeGS中

8
00:00:14,820 --> 00:00:18,220
HTTP接口旨在支持传统上难以使用的协议的许多特性

9
00:00:18,220 --> 00:00:20,900
特别是大块的可能快编码的消息

10
00:00:20,900 --> 00:00:23,300
那么接口永远不会缓冲整个请求或响应

11
00:00:23,300 --> 00:00:25,140
用户能够流失的传输数据

12
00:00:25,140 --> 00:00:27,440
好 我们来看一下HTTP消息头

13
00:00:27,440 --> 00:00:29,320
那么它由如下对象去表示

14
00:00:29,320 --> 00:00:30,480
content length

15
00:00:30,480 --> 00:00:32,460
content type connection

16
00:00:32,460 --> 00:00:33,840
post accept

17
00:00:33,840 --> 00:00:35,980
那么content length

18
00:00:35,980 --> 00:00:37,240
这里就是我们消息的长度

19
00:00:37,240 --> 00:00:39,100
那么type text plain

20
00:00:39,100 --> 00:00:40,380
说明它传输的是一个重文本

21
00:00:40,380 --> 00:00:42,720
那么我们熟悉的有text html

22
00:00:42,720 --> 00:00:44,140
iall application json

23
00:00:44,140 --> 00:00:45,620
那么connection keep alive

24
00:00:45,620 --> 00:00:46,480
这里它就代表

25
00:00:46,480 --> 00:00:48,240
我们是否需要去开启长链接

26
00:00:48,240 --> 00:00:50,440
那么keep alive

27
00:00:50,440 --> 00:00:52,760
其实在咱们的http版本里面

28
00:00:52,760 --> 00:00:54,800
一般都是默认会去开启的

29
00:00:54,800 --> 00:00:56,620
比如说我们这里随便进入一个网站

30
00:00:56,620 --> 00:00:57,640
我们就进入百度

31
00:00:57,640 --> 00:01:00,620
其实大家可以看到了我们基本上所有的请求

32
00:01:00,620 --> 00:01:03,460
旁边都有一个keep alive的一个属性

33
00:01:03,460 --> 00:01:12,480
大家可以看到connection keep alive对吧

34
00:01:12,480 --> 00:01:14,840
好 那么我们再找一个其他的

35
00:01:14,840 --> 00:01:27,000
好 我们来找一下XR

36
00:01:27,000 --> 00:01:34,880
好 可以看到我们的Connection也是Keep Alive

37
00:01:34,880 --> 00:01:38,120
那么Keep Alive其实就是可以保持我们的一个长链接

38
00:01:38,120 --> 00:01:39,660
可以附用我们的一些请求

39
00:01:39,660 --> 00:01:41,860
好 这里就是Connection

40
00:01:41,860 --> 00:01:43,760
包括host 后者就代表我们的域名

41
00:01:43,760 --> 00:01:45,720
那么accept就代表咱们的扣端

42
00:01:45,720 --> 00:01:47,640
支持接收哪些类型的一些消息

43
00:01:47,640 --> 00:01:51,120
为了支持所有可能的HTTP应用程序

44
00:01:51,120 --> 00:01:53,900
RodGS的HTTP API它都非常的底层

45
00:01:53,900 --> 00:01:56,000
它仅进行流出离合消息解析

46
00:01:56,000 --> 00:01:58,160
它将消息解析为消息头和消息主体

47
00:01:58,160 --> 00:01:59,400
头就是head

48
00:01:59,400 --> 00:02:00,620
主体就是bully

49
00:02:00,620 --> 00:02:04,100
但不会解析具体的消息头或者消息主体

50
00:02:04,100 --> 00:02:05,800
大家有没有发现咱们http

51
00:02:05,800 --> 00:02:06,920
这样的一个模块

52
00:02:06,920 --> 00:02:09,900
它的文档写得非常的会是难懂

53
00:02:09,900 --> 00:02:10,980
其实确实是这样的

54
00:02:10,980 --> 00:02:12,140
我们去学习协议的时候

55
00:02:12,140 --> 00:02:13,320
一般都会比较枯燥

56
00:02:13,320 --> 00:02:15,100
有的同学可能会说

57
00:02:15,100 --> 00:02:16,660
我们平时去创建一个http服务器

58
00:02:16,660 --> 00:02:17,460
不是非常简单吧

59
00:02:17,460 --> 00:02:18,020
对吧

60
00:02:18,020 --> 00:02:19,000
我们去create一个server

61
00:02:19,000 --> 00:02:20,600
然后去nation一个端口

62
00:02:20,600 --> 00:02:22,600
这样我们就可以去创建我们一个http请求

63
00:02:22,600 --> 00:02:23,860
你比如说call吧里面

64
00:02:23,860 --> 00:02:24,760
我们就可以使用中间键

65
00:02:24,760 --> 00:02:27,760
Request和Response去定义我们的一些header和Response

66
00:02:27,760 --> 00:02:28,440
包括EGG

67
00:02:28,440 --> 00:02:31,660
其实HGB模块它确实经过我们的EU框架封装之后

68
00:02:31,660 --> 00:02:32,540
使用非常简单

69
00:02:32,540 --> 00:02:34,260
但是它的底层确实非常复杂

70
00:02:34,260 --> 00:02:35,900
我们可以看一下

71
00:02:35,900 --> 00:02:38,060
它这里提了很多

72
00:02:38,060 --> 00:02:41,500
我们都好像非常陌生的一些概念

73
00:02:41,500 --> 00:02:42,440
比如说agint这样一个类

74
00:02:42,440 --> 00:02:43,600
ClientRequest

75
00:02:43,600 --> 00:02:45,780
ServerLate

76
00:02:45,780 --> 00:02:47,220
ServerResponse

77
00:02:47,220 --> 00:02:49,460
包括了IncomingMessage

78
00:02:49,460 --> 00:02:51,280
那么其实我们Core1里面的Request

79
00:02:51,280 --> 00:02:53,100
其实就是咱们基于我们的IncomingMessage

80
00:02:53,100 --> 00:02:54,500
这样一个类去实现的

81
00:02:54,500 --> 00:02:59,560
那么http这一块我们就不去学习它们的在那些底层的细节了

82
00:02:59,560 --> 00:03:00,360
原因是什么呢

83
00:03:00,360 --> 00:03:02,480
因为我们http它这样一种协议

84
00:03:02,480 --> 00:03:05,760
我们nodegs只是按照http标准把它给实现了

85
00:03:05,760 --> 00:03:08,620
我们没有必要去理解它的具体的实现

86
00:03:08,620 --> 00:03:10,520
咱们只需要去理解如何去使用就可以了

87
00:03:10,520 --> 00:03:14,000
然后后面我们会讲到http的一个发展过程

88
00:03:14,000 --> 00:03:15,960
这里就是我们面词中的经常遇到了

89
00:03:15,960 --> 00:03:21,020
我们首先来看一下如何使用http模块去创建一个我们的http服务

90
00:03:21,020 --> 00:03:24,140
这里我的代码已经给大家准备好了

91
00:03:24,140 --> 00:03:26,560
其实HETP的模块使用非常简单

92
00:03:26,560 --> 00:03:28,060
只需要调用一个方法

93
00:03:28,060 --> 00:03:28,540
Create server

94
00:03:28,540 --> 00:03:30,160
那么Create server接受一个回调函数

95
00:03:30,160 --> 00:03:33,340
那么Function里面会接受一个IQ和IIS

96
00:03:33,340 --> 00:03:35,500
分别的代表我们的Request和Response

97
00:03:35,500 --> 00:03:38,500
那么我们Create server之后

98
00:03:38,500 --> 00:03:39,380
是不是需要监听一个单口

99
00:03:39,380 --> 00:03:40,340
监听单口就是3000

100
00:03:40,340 --> 00:03:41,840
那么我们监听完单口之后

101
00:03:41,840 --> 00:03:42,940
首先我们的Response

102
00:03:42,940 --> 00:03:44,020
你是不是需要给它一个head

103
00:03:44,020 --> 00:03:45,500
你需要告诉我们的浏览器

104
00:03:45,500 --> 00:03:47,780
你返回的是一个什么类型的信息

105
00:03:47,780 --> 00:03:49,900
那么我们浏览器得知你的content type

106
00:03:49,900 --> 00:03:51,060
是text HTML之后

107
00:03:51,060 --> 00:03:53,180
它是不是会按照HTML格式去解析

108
00:03:53,180 --> 00:03:54,540
我们的消息啊

109
00:03:54,540 --> 00:03:55,120
然后呢

110
00:03:55,120 --> 00:03:56,080
他Response呢

111
00:03:56,080 --> 00:03:57,600
就给我们去写入了一个HT标签

112
00:03:57,600 --> 00:03:58,640
那么在end之后呢

113
00:03:58,640 --> 00:03:59,020
结束之后

114
00:03:59,020 --> 00:03:59,420
在后面呢

115
00:03:59,420 --> 00:04:00,240
又去插了一个P标签

116
00:04:00,240 --> 00:04:00,660
好

117
00:04:00,660 --> 00:04:01,480
那么我们就来测试一下

118
00:04:01,480 --> 00:04:02,060
我们这一段代码

119
00:04:02,060 --> 00:04:06,460
我们直接

120
00:04:06,460 --> 00:04:07,080
我们呢

121
00:04:07,080 --> 00:04:08,340
先进入我们的一个HTTP

122
00:04:08,340 --> 00:04:08,660
我们呢

123
00:04:08,660 --> 00:04:10,880
执行load.http.js

124
00:04:10,880 --> 00:04:12,940
好

125
00:04:12,940 --> 00:04:14,220
那么我们的端口已经启动了

126
00:04:14,220 --> 00:04:14,840
我们来访问一下

127
00:04:14,840 --> 00:04:18,380
好

128
00:04:18,380 --> 00:04:18,920
大家可以看到

129
00:04:18,920 --> 00:04:20,280
我们是不是已经页面中展示出来了

130
00:04:20,280 --> 00:04:21,880
咱们的load.js和Hello World

131
00:04:21,880 --> 00:04:25,880
这样 好 那么这里呢 我们就做一个测试 如果说我们把他这样一个head把它给拴掉

132
00:04:25,880 --> 00:04:27,880
那么大家认为我们的页面还会长这样吗

133
00:04:27,880 --> 00:04:30,880
好 那么我们就来测试一下

134
00:04:30,880 --> 00:04:42,880
好 好像我们的协议 那么他可能浏览器他可能会自动检测到你在那些标签

135
00:04:42,880 --> 00:04:46,880
如果说发现你实际上标签 他就自动的把它给解析成了一个ictml

136
00:04:46,880 --> 00:04:50,710
我们来看一下他的head 因为我们此时是并没有给他一个rebounce 给他一个content

137
00:04:50,710 --> 00:04:50,880
 type的

138
00:04:51,880 --> 00:04:54,120
这里其实没有给它type

139
00:04:54,120 --> 00:04:55,460
那么我们这里来做一下修改

140
00:04:55,460 --> 00:04:57,120
我们把它改为text plane

141
00:04:57,120 --> 00:04:58,620
看一下会发生什么情况

142
00:04:58,620 --> 00:04:59,540
那么我们的浏览器

143
00:04:59,540 --> 00:05:02,220
还会不会按照HTML的格式去解析它们

144
00:05:02,220 --> 00:05:03,280
大家可以看到

145
00:05:03,280 --> 00:05:05,080
此时咱们的页面

146
00:05:05,080 --> 00:05:06,960
是不是都已经变为了字不串了

147
00:05:06,960 --> 00:05:09,080
看到没有

148
00:05:09,080 --> 00:05:09,680
它是一个字不串

149
00:05:09,680 --> 00:05:12,520
那么我们的css和gss

150
00:05:12,520 --> 00:05:14,440
它的content type是什么呢

151
00:05:14,440 --> 00:05:14,920
我们来看一下

152
00:05:14,920 --> 00:05:15,840
咱们随便找一个gss

153
00:05:15,840 --> 00:05:17,620
大家可以看到了

154
00:05:17,620 --> 00:05:19,340
我们gss的content type

155
00:05:19,340 --> 00:05:21,720
是applicationx-javascript

156
00:05:21,720 --> 00:05:24,480
看一下我们的CSS是什么

157
00:05:24,480 --> 00:05:29,960
我们找到一个CSS文件

158
00:05:29,960 --> 00:05:35,860
可以看到我们的CSS就是ContentTypeTextCSS

159
00:05:35,860 --> 00:05:38,440
所以说我们浏览器去解析文件的时候

160
00:05:38,440 --> 00:05:40,740
你必须指定你返回的内容是什么格式

161
00:05:40,740 --> 00:05:43,200
不然浏览器不知道以什么协议去解析它们

162
00:05:43,200 --> 00:05:45,780
那么这里就是我们的HTTP模块

163
00:05:45,780 --> 00:05:48,880
那么下一节课我们就将进入HTTP2

164
00:05:48,880 --> 00:05:50,500
这样一个模块的一个讲解

