1
00:00:00,000 --> 00:00:02,040
好 那么我们继续 我们是不是还有一个

2
00:00:02,040 --> 00:00:04,860
交演参数没搞定了 那我们就来看一下怎么搞

3
00:00:04,860 --> 00:00:08,200
呃 这里呢 我们首先来看一下咱们1GG他的一个文档

4
00:00:08,200 --> 00:00:11,260
其实文档里面教过我们怎么去进行咱们参数的一个交演

5
00:00:11,260 --> 00:00:12,280
我们来看一下他的一个

6
00:00:12,280 --> 00:00:13,320
文档

7
00:00:13,320 --> 00:00:15,100
因为我们一定要师出有名吧

8
00:00:15,100 --> 00:00:17,660
不然同学们还以为老师是一个引路子对吧

9
00:00:17,660 --> 00:00:19,200
好 我们来看一下

10
00:00:19,200 --> 00:00:22,280
其实呢 我们需要去使用

11
00:00:22,280 --> 00:00:24,840
1GG里面他的一个插件

12
00:00:24,840 --> 00:00:25,860
叫做validate

13
00:00:25,860 --> 00:00:27,400
好 那么我们先来安装一下

14
00:00:27,400 --> 00:00:29,960
咱们边安装边介绍 节约同学们的时间

15
00:00:30,000 --> 00:00:31,800
好咱们把它断掉npm install

16
00:00:31,800 --> 00:00:34,860
npm i 杠稿 save

17
00:00:34,860 --> 00:00:35,880
好我们来安装一下

18
00:00:35,880 --> 00:00:39,220
其实它的使用呢也是非常的简单

19
00:00:39,220 --> 00:00:42,540
比如说呢你要在plugin.js里面首先是不是咱们引入这样一个插件呢

20
00:00:42,540 --> 00:00:43,320
egg-validate

21
00:00:43,320 --> 00:00:44,080
然后在

22
00:00:44,080 --> 00:00:46,900
配置里面呢其实好像咱们不需要配置引入就可以了

23
00:00:46,900 --> 00:00:47,660
它没有一些配置

24
00:00:47,660 --> 00:00:48,940
那么我们怎么样去使用呢

25
00:00:48,940 --> 00:00:51,240
其实只要去调用dates.contest.validate

26
00:00:51,240 --> 00:00:53,800
那么你引入一个插件它就会在咱们的contest下面去挂入

27
00:00:53,800 --> 00:00:54,580
挂在这样一个方法

28
00:00:54,580 --> 00:00:58,160
其实呢它接受两个参数咱们第一个参数是不是传入咱们的一个教育规则

29
00:00:58,160 --> 00:00:59,700
比如说title和content

30
00:00:59,700 --> 00:01:01,520
我们定义它的太后一定要是死菌

31
00:01:01,520 --> 00:01:02,840
那么如果说你传的不是死菌

32
00:01:02,840 --> 00:01:03,440
是不是会报错

33
00:01:03,440 --> 00:01:04,900
那么第二个参数是什么呢

34
00:01:04,900 --> 00:01:05,940
是不是我们数据远呢

35
00:01:05,940 --> 00:01:06,640
大家可以发现

36
00:01:06,640 --> 00:01:08,220
我们去教议你光去validate

37
00:01:08,220 --> 00:01:09,360
一个类型没有用

38
00:01:09,360 --> 00:01:10,340
因为你要告诉他

39
00:01:10,340 --> 00:01:11,100
你需要去检测谁

40
00:01:11,100 --> 00:01:12,440
但是如果说不传的话

41
00:01:12,440 --> 00:01:13,900
其实他给我们去封装好了

42
00:01:13,900 --> 00:01:14,680
会自动的去传入

43
00:01:14,680 --> 00:01:15,920
contest.request的玻璃

44
00:01:15,920 --> 00:01:16,720
因为一般来讲

45
00:01:16,720 --> 00:01:17,700
我们都是去教议玻璃

46
00:01:17,700 --> 00:01:18,660
所以说咱们第二个参数

47
00:01:18,660 --> 00:01:20,180
很少需要去使用

48
00:01:20,180 --> 00:01:20,640
好

49
00:01:20,640 --> 00:01:21,220
那么我们就来看一下

50
00:01:21,220 --> 00:01:21,860
到底怎么去用

51
00:01:21,860 --> 00:01:24,280
好

52
00:01:24,280 --> 00:01:24,600
这里呢

53
00:01:24,600 --> 00:01:25,300
其实已经

54
00:01:25,300 --> 00:01:26,680
已经安装好了

55
00:01:26,680 --> 00:01:27,460
那么我们重新来

56
00:01:29,700 --> 00:01:33,680
那么首先我们在使用validate之前

57
00:01:33,680 --> 00:01:35,580
我们来传入一个错误的参数来试一下

58
00:01:35,580 --> 00:01:37,780
因为我们在schema里面是不是已经定义了

59
00:01:37,780 --> 00:01:38,560
我们都是Screen内向

60
00:01:38,560 --> 00:01:39,360
我们来传入一个数字

61
00:01:39,360 --> 00:01:41,460
看一下咱们它有没有错误提示

62
00:01:41,460 --> 00:01:42,460
大家可以看到

63
00:01:42,460 --> 00:01:45,480
其实我们的title其实呢

64
00:01:45,480 --> 00:01:48,180
它在存储的时候会自动的把它转为字不串

65
00:01:48,180 --> 00:01:48,900
其实大家可以看到

66
00:01:48,900 --> 00:01:50,800
实际上我们数据是存储进去了

67
00:01:50,800 --> 00:01:52,060
但是我们传入的是数字

68
00:01:52,060 --> 00:01:53,580
我们也可以去数据库里面去看一下

69
00:01:53,580 --> 00:01:55,300
我们来刷新一下

70
00:01:55,300 --> 00:01:56,320
其实大家可以看到

71
00:01:56,320 --> 00:01:58,960
它其实把我们存储的数字当成了字不串

72
00:01:58,960 --> 00:02:03,200
去存储 其实这样是不好的 所以说我们需要去用validate 来去约束它

73
00:02:03,200 --> 00:02:05,980
好 那么呢 我们直接来

74
00:02:05,980 --> 00:02:10,040
直接来 怎么 首先呢 我们是不是要定义一个什么

75
00:02:10,040 --> 00:02:12,320
比如说我们去定义一个变量 const

76
00:02:12,320 --> 00:02:16,440
我们的一个create 因为我们教员是不是会有规则

77
00:02:16,440 --> 00:02:18,440
比如说一个create 创建规则是什么呢

78
00:02:18,440 --> 00:02:21,820
title 我们希望它是一个什么 是不是一个使均

79
00:02:21,820 --> 00:02:25,080
然后content呢 也希望它呢 是一个使均

80
00:02:25,080 --> 00:02:27,880
好 那么我们怎么样去使用呢 刚才是不是讲过呀

81
00:02:28,760 --> 00:02:33,000
是不是直接通过contest.valid state

82
00:02:33,000 --> 00:02:35,840
然后呢是不是传入我们的一个规则就可以了

83
00:02:35,840 --> 00:02:39,160
那么呢实际上第二个参数呢是它的一个数据员

84
00:02:39,160 --> 00:02:41,480
但是呢我们如果说不传它会自动的去读取

85
00:02:41,480 --> 00:02:45,680
我们contest.required.blog会自动读取它

86
00:02:45,680 --> 00:02:49,460
好那么我们接下来来看一下有没有生效

87
00:02:49,460 --> 00:02:52,380
比如说我们刚才是不是传了一个数据

88
00:02:52,380 --> 00:02:53,800
传了一个数字走剩的

89
00:02:53,800 --> 00:02:57,020
好大家可以看到我们现在是不是已经失败了

90
00:02:57,020 --> 00:02:58,520
但是现在看不看到错误性西洋

91
00:02:58,520 --> 00:02:59,440
是不是无法看到

92
00:02:59,440 --> 00:03:00,980
那么我们来看一下原因到底是什么

93
00:03:00,980 --> 00:03:05,020
其实我们可以看到

94
00:03:05,020 --> 00:03:07,200
这里报了一个错

95
00:03:07,200 --> 00:03:08,720
type error

96
00:03:08,720 --> 00:03:10,060
contest.validate

97
00:03:10,060 --> 00:03:11,300
为什么

98
00:03:11,300 --> 00:03:12,480
是不是因为我们的插件没影了

99
00:03:12,480 --> 00:03:13,320
因为刚才太激动了

100
00:03:13,320 --> 00:03:13,880
忘了你插件

101
00:03:13,880 --> 00:03:15,520
那么但是大家注意到一个问题没有

102
00:03:15,520 --> 00:03:18,380
我们为什么错误信息里面没有打印出这一句话

103
00:03:18,380 --> 00:03:19,800
因为比如说

104
00:03:19,800 --> 00:03:21,280
咱们把这个data给打印出来

105
00:03:21,280 --> 00:03:23,580
我们来看一下error里面的data是什么

106
00:03:23,580 --> 00:03:26,340
因为我们明明代表报错了

107
00:03:26,340 --> 00:03:27,640
但是我们的error信息为什么没有

108
00:03:27,640 --> 00:03:28,940
这里呢就值得我们去研究

109
00:03:28,940 --> 00:03:30,800
好 大家可以看到

110
00:03:30,800 --> 00:03:32,900
咱们的错误信息是不是已经打印出来了

111
00:03:32,900 --> 00:03:33,920
对吧

112
00:03:33,920 --> 00:03:36,460
那么既然打印出来了

113
00:03:36,460 --> 00:03:37,980
那么为什么我们Postment里面

114
00:03:37,980 --> 00:03:39,620
没有去显示

115
00:03:39,620 --> 00:03:40,860
还是显示一个空对象

116
00:03:40,860 --> 00:03:42,080
那么原因是什么呢

117
00:03:42,080 --> 00:03:43,620
其实大家可以注意到

118
00:03:43,620 --> 00:03:44,560
我们这里是一个什么

119
00:03:44,560 --> 00:03:45,160
它是一个字不串

120
00:03:45,160 --> 00:03:46,400
那么我们这里接收的时候

121
00:03:46,400 --> 00:03:47,120
是不是接收了一个对象

122
00:03:47,120 --> 00:03:47,980
那么怎么办

123
00:03:47,980 --> 00:03:49,200
其实很简单

124
00:03:49,200 --> 00:03:50,420
我们通过一个error字段

125
00:03:50,420 --> 00:03:51,040
专门去接收

126
00:03:51,040 --> 00:03:51,960
它是不是就可以解决这样一个问题

127
00:03:51,960 --> 00:03:53,260
是不是它既可以兼容

128
00:03:53,260 --> 00:03:55,980
这样它是不是可以兼容

129
00:03:55,980 --> 00:03:58,540
对象和乌串 对吧

130
00:03:58,540 --> 00:04:00,580
所以这样就完美的解决这个问题

131
00:04:00,580 --> 00:04:01,620
好 那么我们再来试一下

132
00:04:01,620 --> 00:04:03,140
我们的错误性能不能够给打印出来

133
00:04:03,140 --> 00:04:06,220
好 走理

134
00:04:06,220 --> 00:04:14,160
好 大家可以看到其实很尴尬

135
00:04:14,160 --> 00:04:16,460
我们这样依然把我们的错误给打印不出来

136
00:04:16,460 --> 00:04:18,260
那么原因是什么呢

137
00:04:18,260 --> 00:04:20,040
大家其实有时候不要去自作聪明

138
00:04:20,040 --> 00:04:21,060
其实原因是什么

139
00:04:21,060 --> 00:04:22,860
大家可以看到我们现在的一个error是什么呀

140
00:04:22,860 --> 00:04:25,160
是不是我们lodegs的type error

141
00:04:25,160 --> 00:04:27,500
也就是我们的一个代码是不是写的问题

142
00:04:27,500 --> 00:04:29,060
那么我们这样的代码

143
00:04:29,060 --> 00:04:30,580
它其实可不可以展示给用户了

144
00:04:30,580 --> 00:04:31,140
其实你也可以

145
00:04:31,140 --> 00:04:32,280
你去调用一个tuce dream方法

146
00:04:32,280 --> 00:04:33,560
那么可以把对象转为一个字不串

147
00:04:33,560 --> 00:04:35,480
那么为什么咱们漏的js

148
00:04:35,480 --> 00:04:36,900
咱们js发生了一些原生错误

149
00:04:36,900 --> 00:04:37,620
没有展示给用户了

150
00:04:37,620 --> 00:04:39,300
因为这样可能会让一些

151
00:04:39,300 --> 00:04:40,620
比如说黑客或者一些

152
00:04:40,620 --> 00:04:43,040
思想不是很好的人会猜到一个代码

153
00:04:43,040 --> 00:04:43,540
所以说呢

154
00:04:43,540 --> 00:04:45,640
这样它默认是没有显示给用户了

155
00:04:45,640 --> 00:04:46,980
那么如果说你真的去想看它

156
00:04:46,980 --> 00:04:47,760
你的错误是什么

157
00:04:47,760 --> 00:04:50,160
是不是在我们的日志文件里面会去记录啊

158
00:04:50,160 --> 00:04:51,500
比如说我们去咱们翻到最下面

159
00:04:51,500 --> 00:04:53,000
咱们翻到最下面

160
00:04:53,000 --> 00:04:55,840
大家可以看到我们刚才所发生的错误

161
00:04:55,840 --> 00:04:56,760
load.js type of error

162
00:04:56,760 --> 00:04:57,980
contest.validate

163
00:04:57,980 --> 00:04:58,660
以及load.funk信

164
00:04:58,660 --> 00:04:59,500
其实我们在这里

165
00:04:59,500 --> 00:05:01,160
也可以看到我们的一个错误信息

166
00:05:01,160 --> 00:05:01,780
所以说呢

167
00:05:01,780 --> 00:05:03,300
我们的代码错误

168
00:05:03,300 --> 00:05:04,640
在我们的一个for里面呢

169
00:05:04,640 --> 00:05:05,540
它是不会去处理的

170
00:05:05,540 --> 00:05:07,500
所以说我们来把它给还原

171
00:05:07,500 --> 00:05:08,600
我们就不用去多次一举

172
00:05:08,600 --> 00:05:10,160
使用一个error

173
00:05:10,160 --> 00:05:12,480
好

174
00:05:12,480 --> 00:05:14,080
我们把刚才的代码给删掉

175
00:05:14,080 --> 00:05:14,500
好

176
00:05:14,500 --> 00:05:14,960
那么这里呢

177
00:05:14,960 --> 00:05:17,040
我们就来去引入我们的一个插件

178
00:05:17,040 --> 00:05:18,360
我们的插件呢

179
00:05:18,360 --> 00:05:20,060
就是什么呢

180
00:05:20,060 --> 00:05:20,520
validate

181
00:05:20,520 --> 00:05:27,380
好 我们直接把它给粘过来

182
00:05:27,380 --> 00:05:30,780
好 这里呢 我们就已经引入了我们一个插件

183
00:05:30,780 --> 00:05:32,600
这里呢 咱们把项目给重启一下

184
00:05:32,600 --> 00:05:36,880
好 我这里呢 再来发送一下请求

185
00:05:36,880 --> 00:05:39,540
大家可以看到 此时是不是发生了一个错误

186
00:05:39,540 --> 00:05:41,220
那么这里呢 就会给了我们一个错误信息

187
00:05:41,220 --> 00:05:42,500
那么错误信息是什么呢

188
00:05:42,500 --> 00:05:45,580
Message Validation Forward 教育失败

189
00:05:45,580 --> 00:05:47,300
Code Invalidate Param

190
00:05:47,300 --> 00:05:48,760
说明呢 你传了错误的参数

191
00:05:48,760 --> 00:05:51,560
那么它其实已经明确的告诉你的错误是什么

192
00:05:51,560 --> 00:05:53,200
ArosMessage should be a string

193
00:05:53,200 --> 00:05:54,420
对吧

194
00:05:54,420 --> 00:05:55,620
说明了你有一个字段

195
00:05:55,620 --> 00:05:56,460
Title这样一个字段

196
00:05:56,460 --> 00:05:57,600
说明了你要给他一个使均

197
00:05:57,600 --> 00:05:58,520
好我们来给他一个使均

198
00:05:58,520 --> 00:06:02,600
好随便写一个

199
00:06:02,600 --> 00:06:03,220
好大家可以看到

200
00:06:03,220 --> 00:06:05,740
我们现在是不是就成功的去添加了一条数据

201
00:06:05,740 --> 00:06:06,940
刷新一下大家可以看到

202
00:06:06,940 --> 00:06:08,520
我们刚才是不是给添加进去了

203
00:06:08,520 --> 00:06:11,460
好那么这里呢就是我们Validate的一个使用

204
00:06:11,460 --> 00:06:13,040
我们来简单的来总结一下

205
00:06:13,040 --> 00:06:14,920
那么我们Validate首先最核心的是什么呀

206
00:06:14,920 --> 00:06:15,720
是不是首先要引入啊

207
00:06:15,720 --> 00:06:17,140
刚才我们是不是就忘了去引入

208
00:06:17,140 --> 00:06:19,300
那么Validate是不是使用非常简单

209
00:06:19,300 --> 00:06:21,680
直接调用在context下面调用这个方法

210
00:06:21,680 --> 00:06:22,760
然后传入我们的一个规则

211
00:06:22,760 --> 00:06:24,720
比如说我们的规则是title content对吧

212
00:06:24,720 --> 00:06:26,320
都是使均内型

213
00:06:26,320 --> 00:06:27,240
那么它到底有哪些规则

214
00:06:27,240 --> 00:06:28,080
我们刚才其实忘了讲

215
00:06:28,080 --> 00:06:29,540
那么怎么去看呢

216
00:06:29,540 --> 00:06:30,780
其实同学们可以去

217
00:06:30,780 --> 00:06:33,540
其实它的官方网站也提供了

218
00:06:33,540 --> 00:06:35,580
我们这里来简单带同学们来看一下

219
00:06:35,580 --> 00:06:38,140
ETG

220
00:06:38,140 --> 00:06:40,600
Github是不是真是一个好东西

221
00:06:40,600 --> 00:06:41,960
我们有问题都去找它

222
00:06:41,960 --> 00:06:42,700
Validate

223
00:06:42,700 --> 00:06:46,520
好大家可以看到

224
00:06:46,520 --> 00:06:47,640
大家去看这样一句话

225
00:06:47,640 --> 00:06:48,600
1GValidate

226
00:06:48,600 --> 00:06:50,560
支持所有的一些什么的一些配置

227
00:06:50,560 --> 00:06:52,480
你可以呢去看parampt documents

228
00:06:52,480 --> 00:06:53,080
说明一个什么问题

229
00:06:53,080 --> 00:06:54,880
是不是说明我们的validate这样一个插件

230
00:06:54,880 --> 00:06:55,420
其实它呢

231
00:06:55,420 --> 00:06:56,820
也是去基于别人的库

232
00:06:56,820 --> 00:06:57,640
去做了一个二次开发

233
00:06:57,640 --> 00:06:59,060
叫做parampt这样一个库

234
00:06:59,060 --> 00:07:00,080
那么它的一些规则呢

235
00:07:00,080 --> 00:07:00,860
它的文档就在这里

236
00:07:00,860 --> 00:07:01,760
那么有兴趣的同学们呢

237
00:07:01,760 --> 00:07:02,620
可以去自己去看一下

238
00:07:02,620 --> 00:07:04,560
那么validate呢

239
00:07:04,560 --> 00:07:06,380
我们其实除了讲解它的API的使用

240
00:07:06,380 --> 00:07:07,440
咱们是不是还讲了一些

241
00:07:07,440 --> 00:07:08,900
咱们关于错误处理的东西啊

242
00:07:08,900 --> 00:07:09,220
对吧

243
00:07:09,220 --> 00:07:10,160
那么我们的错误处理

244
00:07:10,160 --> 00:07:11,960
其实你比如说我们可以显示的

245
00:07:11,960 --> 00:07:12,520
那么漏的件事

246
00:07:12,520 --> 00:07:13,400
它一定让你去显示

247
00:07:13,400 --> 00:07:14,140
那么不能显示的

248
00:07:14,140 --> 00:07:15,180
可能考虑到一些安全性问题

249
00:07:15,180 --> 00:07:15,760
比如说你代码

250
00:07:15,760 --> 00:07:16,600
自己的运行错误

251
00:07:16,600 --> 00:07:17,360
它不会暴露给你的

252
00:07:17,360 --> 00:07:18,320
那么如果说你要看

253
00:07:18,320 --> 00:07:19,120
是不是跑到我们的一个

254
00:07:19,120 --> 00:07:21,200
我们的一个日志文件里面去看了

255
00:07:21,200 --> 00:07:21,480
好

256
00:07:21,480 --> 00:07:22,940
那么这里呢就是我们

257
00:07:22,940 --> 00:07:26,860
咱们关于添加数据这块的内容

258
00:07:26,860 --> 00:07:28,240
那么接下来

259
00:07:28,240 --> 00:07:29,680
我们需要去做什么了呢

260
00:07:29,680 --> 00:07:33,720
其实我们是不是已经完成了

261
00:07:33,720 --> 00:07:34,800
我们的一个添加数据

262
00:07:34,800 --> 00:07:38,360
那么我完成添加数据之后

263
00:07:38,360 --> 00:07:40,740
进行我们数据的一个

264
00:07:40,740 --> 00:07:41,960
最后的一个查询

265
00:07:41,960 --> 00:07:44,520
因为我们添加数据之后就可以查了

266
00:07:44,520 --> 00:07:47,480
那么这里就是我们这一块的内容

