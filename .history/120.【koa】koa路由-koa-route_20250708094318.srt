1
00:00:00,000 --> 00:00:01,800
好 这节课呢 我们就来看一下

2
00:00:01,800 --> 00:00:02,560
咱们

3
00:00:02,560 --> 00:00:03,580
路由

4
00:00:03,580 --> 00:00:05,380
那么路由到底是什么呢

5
00:00:05,380 --> 00:00:06,920
什么是路由啊 同学们

6
00:00:06,920 --> 00:00:08,200
我们前段路由 同学们应该了解过吧

7
00:00:08,200 --> 00:00:09,720
比如说 我们去访问一个页面的时候

8
00:00:09,720 --> 00:00:12,040
在view里面 包括reft里面 都有路由一个概念

9
00:00:12,040 --> 00:00:13,320
比如说访问一个地址

10
00:00:13,320 --> 00:00:15,360
那么我们会通过咱们不同的地址去vendor

11
00:00:15,360 --> 00:00:17,660
不一样的方法 也就是咱们渲染不一样的页面

12
00:00:17,660 --> 00:00:19,960
那么node.js里面 它的路由其实是一样的

13
00:00:19,960 --> 00:00:20,740
你比如说

14
00:00:20,740 --> 00:00:23,300
我们的request 它的pass 不一样

15
00:00:23,300 --> 00:00:24,840
比如说访问咱们的一个

16
00:00:24,840 --> 00:00:26,880
比如访问我们的根部录 就给一个

17
00:00:26,880 --> 00:00:27,900
访问一个hello world

18
00:00:28,160 --> 00:00:31,740
那么如果说你访问一个别的路径 是不是需要去resbounce一些别的东西

19
00:00:31,740 --> 00:00:33,280
好 那我们就直接来看一下

20
00:00:33,280 --> 00:00:34,560
我们的代码

21
00:00:34,560 --> 00:00:36,100
怎么样去写

22
00:00:36,100 --> 00:00:42,240
比如说这里呢 我来建一个router.js

23
00:00:42,240 --> 00:00:46,340
我们还是来复制一下app.js

24
00:00:46,340 --> 00:00:48,900
router.js

25
00:00:48,900 --> 00:00:54,280
好 我们来先提一个需求 假如说

26
00:00:58,160 --> 00:01:00,100
假如访问

27
00:01:00,100 --> 00:01:02,500
访问咱们的

28
00:01:02,500 --> 00:01:04,500
根目录

29
00:01:04,500 --> 00:01:06,500
则返回

30
00:01:06,500 --> 00:01:08,080
hello word

31
00:01:08,080 --> 00:01:10,240
第二个呢

32
00:01:10,240 --> 00:01:11,620
假如

33
00:01:11,620 --> 00:01:12,840
不是

34
00:01:12,840 --> 00:01:14,160
根目录

35
00:01:14,160 --> 00:01:15,960
则返回一点

36
00:01:15,960 --> 00:01:17,400
别的东西

37
00:01:17,400 --> 00:01:19,980
那么我们怎么样去做呢

38
00:01:19,980 --> 00:01:21,080
同学思考一下

39
00:01:21,080 --> 00:01:23,220
怎么样去实现一个基本的路由呢

40
00:01:23,220 --> 00:01:24,840
是不是通过

41
00:01:24,840 --> 00:01:26,740
咱们的context的request

42
00:01:26,740 --> 00:01:27,860
它的一个pass属性呢

43
00:01:27,860 --> 00:01:29,780
我们就可以知道客户端他访问的地址是什么

44
00:01:29,780 --> 00:01:31,960
我们通过客户端他的一个地址我们再来

45
00:01:31,960 --> 00:01:35,800
去做判断我们的response需要去访问什么东西对吧好那我们就来看一下

46
00:01:35,800 --> 00:01:38,620
首先肯定是需要一个

47
00:01:38,620 --> 00:01:42,200
if

48
00:01:42,200 --> 00:01:45,260
alse吧

49
00:01:45,260 --> 00:01:52,700
如果什么呢如果我们contact.request.pass等于

50
00:01:52,700 --> 00:01:57,040
这不等于咱们的根目录那么contact.res

51
00:01:57,860 --> 00:01:59,920
bounce.body等于

52
00:01:59,920 --> 00:02:01,900
怎么样是不是等于我们呢

53
00:02:01,900 --> 00:02:03,260
hello.word

54
00:02:03,260 --> 00:02:04,960
那么如果他不是的呢

55
00:02:04,960 --> 00:02:06,120
如果说不是个目录

56
00:02:06,120 --> 00:02:07,320
是不是就返回一点其他的

57
00:02:07,320 --> 00:02:10,360
也就是说contest.response.

58
00:02:10,360 --> 00:02:11,760
首先呢我们给他定一个type

59
00:02:11,760 --> 00:02:13,860
我们呢同样的也返回个HML

60
00:02:13,860 --> 00:02:20,260
contest.response.body等于

61
00:02:20,260 --> 00:02:22,260
比如说我们去返回一个

62
00:02:22,260 --> 00:02:24,320
A标签

63
00:02:24,320 --> 00:02:27,200
给他一个ref等于什么呢

64
00:02:27,200 --> 00:02:28,740
我们直接给他指向咱们的根目录

65
00:02:28,740 --> 00:02:31,040
好 给他一个其他

66
00:02:31,040 --> 00:02:32,840
好 那么我们来启动看一下

67
00:02:32,840 --> 00:02:33,860
到底可不可以

68
00:02:33,860 --> 00:02:38,460
loadloadchaps

69
00:02:38,460 --> 00:02:43,840
rooter.js

70
00:02:43,840 --> 00:02:44,600
走

71
00:02:44,600 --> 00:02:48,200
好 我们来访问看一下

72
00:02:48,200 --> 00:02:51,520
Intel 拿server level 报错了

73
00:02:51,520 --> 00:02:52,800
看一下哪里报错了

74
00:02:52,800 --> 00:02:54,340
cannot set property

75
00:02:54,340 --> 00:02:56,120
path of unifind

76
00:02:56,120 --> 00:03:00,220
好 大家可以看到我们contest的点是不是request的这个单词拼错了呀

77
00:03:00,220 --> 00:03:02,520
request

78
00:03:02,520 --> 00:03:03,800
好 我们重启一下

79
00:03:03,800 --> 00:03:09,440
走 大家可以看到我们的根目鲁是不是好得握的呀

80
00:03:09,440 --> 00:03:12,760
好 不好意思 我们的根目鲁是不是好得握的 假如我们去访问一个其他的弟子

81
00:03:12,760 --> 00:03:14,800
 我们来看一下 我们来下访问一个

82
00:03:14,800 --> 00:03:17,880
还是他 对吧

83
00:03:17,880 --> 00:03:22,740
好 为什么会造成这样一种现象呢

84
00:03:26,120 --> 00:03:27,980
为什么会造成这样一种现象

85
00:03:27,980 --> 00:03:28,800
我们来打一个断点

86
00:03:28,800 --> 00:03:30,740
我们来打一个断点看一下

87
00:03:30,740 --> 00:03:35,160
Rooter.js

88
00:03:35,160 --> 00:03:36,680
好 走

89
00:03:36,680 --> 00:03:38,060
我们先把它给断掉

90
00:03:38,060 --> 00:03:38,860
走

91
00:03:38,860 --> 00:03:41,440
访问

92
00:03:41,440 --> 00:03:42,900
好 我们来看一下

93
00:03:42,900 --> 00:03:45,740
contest.request.pass

94
00:03:45,740 --> 00:03:46,540
它的值到底是什么

95
00:03:46,540 --> 00:03:49,200
大家看到

96
00:03:49,200 --> 00:03:50,520
我们是不是在一个代码写错了

97
00:03:50,520 --> 00:03:51,300
是不是三个点一个

98
00:03:51,300 --> 00:03:53,080
对吧 好

99
00:03:53,080 --> 00:03:55,400
所以遇到问题我们不要慌

100
00:03:55,400 --> 00:03:56,700
只要去把它给

101
00:03:56,700 --> 00:03:59,240
找到原因就可以了

102
00:03:59,240 --> 00:04:01,040
走大家可以看到是不是其他点

103
00:04:01,040 --> 00:04:01,980
走我们是不是走到根目录

104
00:04:01,980 --> 00:04:06,100
好这里呢就是我们通过core画

105
00:04:06,100 --> 00:04:07,780
去实现我们一个路由的一个

106
00:04:07,780 --> 00:04:09,100
最基本的一种方式

107
00:04:09,100 --> 00:04:10,160
好那我们来

108
00:04:10,160 --> 00:04:12,440
那么我们继续

109
00:04:12,440 --> 00:04:13,720
其实呢我们

110
00:04:13,720 --> 00:04:16,300
去在core画里面去实现一个路由

111
00:04:16,300 --> 00:04:17,240
有更加简单的方法

112
00:04:17,240 --> 00:04:19,520
什么呢我们需要去使用一个corerouter

113
00:04:19,520 --> 00:04:20,780
这个模块可以让我们

114
00:04:20,780 --> 00:04:22,780
去做这样做这样一件事情的时候呢

115
00:04:22,780 --> 00:04:23,120
更加的

116
00:04:23,120 --> 00:04:24,200
简单

117
00:04:24,200 --> 00:04:25,220
corerouter

118
00:04:25,220 --> 00:04:26,620
那么我们先来安装一下

119
00:04:26,620 --> 00:04:30,460
比如说我们去npm install core-router

120
00:04:30,460 --> 00:04:32,200
那么就让它先安装

121
00:04:32,200 --> 00:04:34,780
我们来看一下我们怎么样去使用我们的core-router

122
00:04:34,780 --> 00:04:35,920
去进行我们的一个路由

123
00:04:35,920 --> 00:04:40,760
比如说我们是不是首先

124
00:04:40,760 --> 00:04:43,720
首先是不是要引入我们的一个root模块呀

125
00:04:43,720 --> 00:04:45,460
等于require

126
00:04:45,460 --> 00:04:47,560
怎么样是不是core-router

127
00:04:47,560 --> 00:04:49,940
好那么当我们去引入它之后

128
00:04:49,940 --> 00:04:53,880
好这一段呢

129
00:04:53,880 --> 00:04:55,420
我们就先把给刷掉

130
00:04:55,420 --> 00:04:56,700
先把给

131
00:04:56,700 --> 00:04:58,480
注释掉吧

132
00:04:58,480 --> 00:05:04,880
好比如说他咱们的cora-root他是怎么去使用的呢

133
00:05:04,880 --> 00:05:12,820
比如说我们去写了这样一个方法比如说我们去cont的一个main等于什么呢

134
00:05:12,820 --> 00:05:15,380
context

135
00:05:15,380 --> 00:05:22,800
然后呢contact.res其实就是这样一行代码contact.response.body等于好了word

136
00:05:23,880 --> 00:05:26,080
好

137
00:05:26,080 --> 00:05:28,220
那么我们去use main

138
00:05:28,220 --> 00:05:28,900
对吧

139
00:05:28,900 --> 00:05:30,800
那么此时是不是你无论访回

140
00:05:30,800 --> 00:05:32,900
此时你是不是无论访问任何地址

141
00:05:32,900 --> 00:05:34,360
他都会访回一个Hello World

142
00:05:34,360 --> 00:05:36,720
那么不信的同学可以来试一下

143
00:05:36,720 --> 00:05:38,560
你无论输入什么地址

144
00:05:38,560 --> 00:05:40,000
他是不是都会访问

145
00:05:40,000 --> 00:05:41,000
访回Hello World

146
00:05:41,000 --> 00:05:41,740
对吧

147
00:05:41,740 --> 00:05:43,940
因为咱们APP是直接use了我们的contest

148
00:05:43,940 --> 00:05:45,040
也就是说每次请就过来

149
00:05:45,040 --> 00:05:46,200
他都会把玻璃改为Hello World

150
00:05:46,200 --> 00:05:48,640
那么我们如果说需要通过路由去判断

151
00:05:48,640 --> 00:05:50,000
我们就需要续使用到root

152
00:05:50,000 --> 00:05:51,240
那么他是怎么样去用的呢

153
00:05:51,240 --> 00:05:51,940
其实非常的简单

154
00:05:51,940 --> 00:05:54,940
比如说我们去要用router.get

155
00:05:54,940 --> 00:05:57,940
router.get它是一个方法第一个参数

156
00:05:57,940 --> 00:06:01,940
第一个参数接收咱们的路径

157
00:06:01,940 --> 00:06:05,940
第二个就是咱们的一个contest的参数

158
00:06:05,940 --> 00:06:07,940
比如说我们去get的一个目录

159
00:06:07,940 --> 00:06:09,940
后面就接上咱们的main方法

160
00:06:09,940 --> 00:06:12,940
main就是咱们怎么样去处理这样一个路径

161
00:06:12,940 --> 00:06:15,940
也就是咱们刚才的contest

162
00:06:15,940 --> 00:06:17,940
此时我们来看一下是不是成效了

163
00:06:20,940 --> 00:06:22,220
好 如果放的

164
00:06:22,220 --> 00:06:24,020
大家可以看到

165
00:06:24,020 --> 00:06:27,860
我们访问其他路径是如果放的 但是我们访问更多路径就是好了 word

166
00:06:27,860 --> 00:06:33,220
这里就是它root 它的一个core-root 它的一个什么方法 比如说我们去访问一个其他的路径

167
00:06:33,220 --> 00:06:34,500
 咱们app.use

168
00:06:34,500 --> 00:06:38,340
root.get

169
00:06:38,340 --> 00:06:40,900
比如说我们访问一个其他

170
00:06:40,900 --> 00:06:44,240
然后我们这里是不是要去定一个其他方法啊 同学们

171
00:06:44,240 --> 00:06:46,800
const 其他等于

172
00:06:46,800 --> 00:06:50,380
context

173
00:06:50,940 --> 00:06:55,000
其他

174
00:06:55,000 --> 00:06:57,820
好 我们来重启一下服务

175
00:06:57,820 --> 00:07:00,520
好 跟路径是Hello World

176
00:07:00,520 --> 00:07:02,700
其他 对吧

177
00:07:02,700 --> 00:07:05,380
这里呢就是core root 它的一个具体的使用

178
00:07:05,380 --> 00:07:07,460
那么core-root 当然肯定使用

179
00:07:07,460 --> 00:07:08,580
它还有一些其他的API

180
00:07:08,580 --> 00:07:10,800
那么我们一起来看一下

181
00:07:10,800 --> 00:07:15,380
core-root

182
00:07:15,380 --> 00:07:19,260
它呢有四千多颗 star 对吧

183
00:07:19,260 --> 00:07:22,340
其实呢它也是Core它的一个官方的一个root的使用方法

184
00:07:22,340 --> 00:07:27,700
那么如果说同学们想去了解更多的可以去看一下Core root它的一个官网

185
00:07:27,700 --> 00:07:29,020
去看一下到底怎么去使用

186
00:07:29,020 --> 00:07:31,640
比如说呢你去访问一个ID的时候

187
00:07:31,640 --> 00:07:33,660
因为我们比如说你去访问一个user

188
00:07:33,660 --> 00:07:34,780
user可能会有很多

189
00:07:34,780 --> 00:07:38,000
但是呢你呢就可以通过一个动态的一个ID

190
00:07:38,000 --> 00:07:39,780
去获取到你当时访问的是哪一个ID

191
00:07:39,780 --> 00:07:41,440
其实root它有非常多的一些用法

192
00:07:41,440 --> 00:07:43,280
这样一种语法是否和咱们view里面

193
00:07:43,280 --> 00:07:46,020
一个root包括read里面的一个root使用方法都非常的类似

194
00:07:46,020 --> 00:07:47,100
那么它还有一些更多的方法

195
00:07:47,100 --> 00:07:48,220
同学们可以自己去看一下文档

196
00:07:48,220 --> 00:07:50,740
咱们直接通过github去搜索就可以了

197
00:07:50,740 --> 00:07:56,120
因为咱们这一刻咱们这些的课程重点是call我不可能说把所有东西都讲到

198
00:07:56,120 --> 00:07:59,300
那我这里的同学们可以自己去看一下call里面他的一些root模块

199
00:07:59,300 --> 00:08:02,080
好 那我们先来回顾一下这节课的内容

200
00:08:02,080 --> 00:08:09,940
刚才我们是不是讲到了call里面的路由啊

201
00:08:09,940 --> 00:08:11,620
它是不是有两种方式 一种是原生

202
00:08:11,620 --> 00:08:12,900
那么原生的方式怎么去做

203
00:08:12,900 --> 00:08:15,820
是不是通过contest点

204
00:08:15,820 --> 00:08:17,820
request

205
00:08:17,820 --> 00:08:19,100
request.pass

206
00:08:19,100 --> 00:08:19,860
为什么

207
00:08:19,860 --> 00:08:23,700
是不是因为咱们的request的属性是不是会通过call挂载到咱们contact上面

208
00:08:23,700 --> 00:08:25,500
那么request里面他有一个pass属性

209
00:08:25,500 --> 00:08:29,080
你可以去通过pass属性去判断我们的客轮返回过来的路径

210
00:08:29,080 --> 00:08:30,620
然后呢你去做一些返回

211
00:08:30,620 --> 00:08:32,160
那么你还呢可以通过call

212
00:08:32,160 --> 00:08:34,200
root去处理吧

213
00:08:34,200 --> 00:08:34,980
我们去使用呢

214
00:08:34,980 --> 00:08:36,260
是不是直接调用

215
00:08:36,260 --> 00:08:40,100
root.get

216
00:08:40,100 --> 00:08:42,140
那么get是咱们的http方法

217
00:08:42,140 --> 00:08:45,460
同样呢你可以去root.postgetdeletepro啊是不是都可以啊

218
00:08:45,460 --> 00:08:47,520
好 那么这里呢就是我们这几个的内容

