1
00:00:00,000 --> 00:00:02,460
好,这一课我们来讲解一下

2
00:00:02,460 --> 00:00:06,280
IPC通信也是我们本系列课程的最后一节课

3
00:00:06,280 --> 00:00:07,820
那么什么是IPC通信呢

4
00:00:07,820 --> 00:00:10,120
IPC通信其实就是进程之间的通信

5
00:00:10,120 --> 00:00:12,040
那么进程之间的通信

6
00:00:12,040 --> 00:00:14,580
我们之前是讲过class的怎么在进程之间通信

7
00:00:14,580 --> 00:00:16,740
是不是通过我们的

8
00:00:16,740 --> 00:00:18,500
比如说我们去引入一个class的

9
00:00:18,500 --> 00:00:19,920
我们在主线层里面去fork

10
00:00:19,920 --> 00:00:21,400
fork之后是不是会返回一个walker

11
00:00:21,400 --> 00:00:22,700
也就是咱们子进程的一个对象

12
00:00:22,700 --> 00:00:25,280
通过send的方法给咱们子进程去发送消息

13
00:00:25,280 --> 00:00:28,500
在子进程里面通过process的onmessage去监听

14
00:00:28,500 --> 00:00:31,700
那么如果说walker想给我们的master去发消息怎么去做呢

15
00:00:31,700 --> 00:00:33,260
是不是咱们也可以通过process.send

16
00:00:33,260 --> 00:00:35,560
这样就形成了我们的一个ipc通道

17
00:00:35,560 --> 00:00:36,040
一个pipe

18
00:00:36,040 --> 00:00:38,220
这里就叫做进程间的一个通信

19
00:00:38,220 --> 00:00:40,340
不知道大家有没有考虑过一个问题

20
00:00:40,340 --> 00:00:43,680
我们class的ipc通道只存在于master和walker之间

21
00:00:43,680 --> 00:00:46,280
那么我们的walker和walker如果说需要通信

22
00:00:46,280 --> 00:00:46,680
怎么办

23
00:00:46,680 --> 00:00:47,760
大家有没有想过这样一个问题

24
00:00:47,760 --> 00:00:51,040
我们来画幅图来看一下

25
00:00:51,040 --> 00:00:52,300
首先我们有master

26
00:00:52,300 --> 00:00:55,120
假如说

27
00:00:55,120 --> 00:00:58,020
假如说我们的master和walker之间需要通信

28
00:00:58,020 --> 00:01:00,540
是不是我们的master首先会剩的呀

29
00:01:00,540 --> 00:01:01,640
然后呢

30
00:01:01,640 --> 00:01:03,180
walker去on什么

31
00:01:03,180 --> 00:01:05,960
on一个message

32
00:01:05,960 --> 00:01:08,940
这样呢就形成了我们master和walker之间的一个通道

33
00:01:08,940 --> 00:01:11,840
但是假如walker1和walker2之间需要通信

34
00:01:11,840 --> 00:01:12,320
怎么办

35
00:01:12,320 --> 00:01:13,860
怎么办

36
00:01:13,860 --> 00:01:17,460
好像目前来讲我们也没有讲过

37
00:01:17,460 --> 00:01:18,660
6GS的文档也没有说过

38
00:01:18,660 --> 00:01:18,960
怎么办

39
00:01:18,960 --> 00:01:21,540
其实有这样一种在业内边流行的办法是什么呢

40
00:01:21,540 --> 00:01:27,860
通过master为桥梁去转发

41
00:01:27,860 --> 00:01:28,520
什么意思

42
00:01:28,520 --> 00:01:29,960
我们master

43
00:01:29,960 --> 00:01:31,220
是不是知道咱们

44
00:01:31,220 --> 00:01:31,700
Walker1

45
00:01:31,700 --> 00:01:32,060
Walker2

46
00:01:32,060 --> 00:01:32,840
咱们这四个Walker

47
00:01:32,840 --> 00:01:33,960
他们的各自的PID

48
00:01:33,960 --> 00:01:34,900
Master是不是知道

49
00:01:34,900 --> 00:01:36,500
那么在Walker1

50
00:01:36,500 --> 00:01:36,820
他想

51
00:01:36,820 --> 00:01:37,920
比如说我想跟Walker2

52
00:01:37,920 --> 00:01:38,720
通信之前

53
00:01:38,720 --> 00:01:40,540
Walker1想跟Walker2通信

54
00:01:40,540 --> 00:01:42,660
那么咱们就跟打电话一样

55
00:01:42,660 --> 00:01:43,780
我告诉master

56
00:01:43,780 --> 00:01:45,040
我说我要跟Walker2通信

57
00:01:45,040 --> 00:01:45,900
是不是Walker1

58
00:01:45,900 --> 00:01:48,320
直接把Walker2的PID

59
00:01:48,320 --> 00:01:49,340
给传递过去就可以了

60
00:01:49,340 --> 00:01:50,280
比如说我去

61
00:01:50,280 --> 00:01:51,980
我去Walker1

62
00:01:51,980 --> 00:01:53,360
给master去圣的了一个

63
00:01:53,360 --> 00:01:54,480
比如说Walker2的PID

64
00:01:54,480 --> 00:01:55,240
我要跟他说话

65
00:01:55,240 --> 00:01:56,100
我要说什么什么什么

66
00:01:56,100 --> 00:01:57,360
就类似于我们的电话的

67
00:01:57,360 --> 00:01:58,000
接线源

68
00:01:58,000 --> 00:01:59,560
比如说

69
00:01:59,560 --> 00:02:00,380
比如

70
00:02:00,380 --> 00:02:02,380
walker1

71
00:02:02,380 --> 00:02:03,220
携带

72
00:02:03,220 --> 00:02:09,260
walker2的pid给master

73
00:02:09,260 --> 00:02:14,840
然后

74
00:02:14,840 --> 00:02:16,400
发送

75
00:02:16,400 --> 00:02:18,800
然后通过master

76
00:02:18,800 --> 00:02:20,760
转接

77
00:02:20,760 --> 00:02:21,960
是不是和咱们打电话

78
00:02:21,960 --> 00:02:23,040
其实很类似啊

79
00:02:23,040 --> 00:02:23,860
我要跟谁打电话

80
00:02:23,860 --> 00:02:25,320
就以前我们是不是有一个接线源

81
00:02:25,320 --> 00:02:26,260
打个电话就唯

82
00:02:26,260 --> 00:02:28,380
请你让某某某接一下电话

83
00:02:28,380 --> 00:02:29,380
然后这个接线员就

84
00:02:29,380 --> 00:02:31,340
给咱们的目标对象给接过去

85
00:02:31,340 --> 00:02:32,740
其实Master也是这样一个作用

86
00:02:32,740 --> 00:02:34,360
在咱们进程之间通信的时候

87
00:02:34,360 --> 00:02:36,040
比如说我们Walker1和Walker2要通信

88
00:02:36,040 --> 00:02:38,700
其实也是靠PID去转发的

89
00:02:38,700 --> 00:02:40,020
那么具体是怎么样去转发

90
00:02:40,020 --> 00:02:41,480
在咱们后面的课程

91
00:02:41,480 --> 00:02:42,580
我们会讲到nodejs

92
00:02:42,580 --> 00:02:44,860
与Quava与EGG的一些应用

93
00:02:44,860 --> 00:02:46,280
那么在讲我们nodejs实战的时候

94
00:02:46,280 --> 00:02:47,580
我们会去详细的去讲解

95
00:02:47,580 --> 00:02:49,820
他们只进程之间的通信的一个过程

96
00:02:49,820 --> 00:02:50,520
好

97
00:02:50,520 --> 00:02:51,660
那么讲到这里呢

98
00:02:51,660 --> 00:02:53,720
我们nodejs它的一个

99
00:02:53,720 --> 00:02:54,460
世界循环

100
00:02:54,460 --> 00:02:56,060
以及多进程的整个异性的课程

101
00:02:56,060 --> 00:02:57,240
到这里就结束了

102
00:02:57,240 --> 00:02:59,600
其实这段课程对同学们来讲是非常重要的

103
00:02:59,600 --> 00:03:01,440
虽然说我们都讲的是一些基础

104
00:03:01,440 --> 00:03:02,520
没有写过一些业务代码

105
00:03:02,520 --> 00:03:03,420
其实非常的重要

106
00:03:03,420 --> 00:03:06,000
因为你理解了它load.js的一个世界循环

107
00:03:06,000 --> 00:03:09,360
以及load.js它的一些多进程模型

108
00:03:09,360 --> 00:03:11,800
会对你后面去解决一些更复杂的问题

109
00:03:11,800 --> 00:03:12,660
非常的有帮助

110
00:03:12,660 --> 00:03:13,900
所以希望同学们能够去

111
00:03:13,900 --> 00:03:15,860
好好的去学习和领悟一下

112
00:03:15,860 --> 00:03:17,640
好 这里就是这几课的内容

