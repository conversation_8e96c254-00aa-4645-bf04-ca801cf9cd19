1
00:00:00,000 --> 00:00:01,840
好,这节课我们就来看一下

2
00:00:01,840 --> 00:00:05,180
Core中它,乐机S它对AERO事件的监听

3
00:00:05,180 --> 00:00:07,480
你比如说,我们刚才是不是谈到了

4
00:00:07,480 --> 00:00:09,440
如果说我们没有错误处理的中间件

5
00:00:09,440 --> 00:00:12,020
那么我们的错误是不是会被我们的乐机S它去捕捉

6
00:00:12,020 --> 00:00:13,460
其实在哪里捕捉的呢

7
00:00:13,460 --> 00:00:16,320
其实就在APP,也就是Core提供的它APP里面

8
00:00:16,320 --> 00:00:18,160
它呢,用M去监听了一个

9
00:00:18,160 --> 00:00:19,320
AERO这样的事件

10
00:00:19,320 --> 00:00:21,300
那么如果说你发生了一些错误没有被捕捉到

11
00:00:21,300 --> 00:00:23,560
它其实会被我们Core里面的APP给捕捉到

12
00:00:23,560 --> 00:00:26,000
那么我们就来看一下它到底是什么回事

13
00:00:26,000 --> 00:00:29,760
好,这里呢我们需要把咱们的AERO HAND给先注释掉

14
00:00:29,760 --> 00:00:31,160
home也给注释掉

15
00:00:31,160 --> 00:00:32,540
你比如说我们去root.get

16
00:00:32,540 --> 00:00:33,540
这些main的时候

17
00:00:33,540 --> 00:00:35,900
假如说我们直接通过contest的

18
00:00:35,900 --> 00:00:38,500
throw了一个500

19
00:00:38,500 --> 00:00:40,640
那么呢我们是不是还需要去监听一下

20
00:00:40,640 --> 00:00:42,920
在app下面app.on

21
00:00:42,920 --> 00:00:45,400
error

22
00:00:45,400 --> 00:00:51,760
比如说我们去把咱们的error给打印一下

23
00:00:51,760 --> 00:00:56,880
好我们来重启一下

24
00:00:56,880 --> 00:01:05,580
没成功看一下什么原因

25
00:01:05,580 --> 00:01:11,980
康说了一个多的方式啊我们说是应该是康说点落给好我们来重启一下

26
00:01:11,980 --> 00:01:17,180
好大家可以看到是不是防护了一个500这个错误让我们来看我们的错误被捕捉到没有

27
00:01:17,180 --> 00:01:22,140
大家可以看到error internal servererror服器内部错误是不是已经被捕捉到了呀

28
00:01:22,140 --> 00:01:23,580
error对应的是它

29
00:01:23,580 --> 00:01:26,000
可能有的同学不信

30
00:01:26,000 --> 00:01:28,400
我们来把它弄明显一点

31
00:01:28,400 --> 00:01:30,480
好刷新error

32
00:01:30,480 --> 00:01:32,100
大家可以看到

33
00:01:32,100 --> 00:01:34,480
是不是我们通过app它的一个error事件

34
00:01:34,480 --> 00:01:35,980
给监听到了

35
00:01:35,980 --> 00:01:39,140
我们内部发生一些错误

36
00:01:39,140 --> 00:01:41,260
那么有的同学可能会问了

37
00:01:41,260 --> 00:01:43,220
你app明明可以用error方法去监听

38
00:01:43,220 --> 00:01:45,140
那么我们的trycatch它又有什么用

39
00:01:45,140 --> 00:01:46,360
比如说我们错误出于中间键

40
00:01:46,360 --> 00:01:47,960
它有什么用呢

41
00:01:47,960 --> 00:01:49,180
好那么我们再来看一下

42
00:01:49,180 --> 00:01:51,280
那么我们再来看一下它会发生

43
00:01:51,280 --> 00:01:52,760
比如说我们把我们中间给打开

44
00:01:52,760 --> 00:01:54,780
我们通过ErrorHand去捕捉我们的错误

45
00:01:54,780 --> 00:01:56,840
同时呢我们用APP去监听Error事件

46
00:01:56,840 --> 00:01:57,840
那么我们来看一下

47
00:01:57,840 --> 00:02:00,620
到底谁能够捕捉到错误

48
00:02:00,620 --> 00:02:02,320
好 走理

49
00:02:02,320 --> 00:02:03,700
是不是发生了错误了呀 500

50
00:02:03,700 --> 00:02:07,580
好 大家可以看到

51
00:02:07,580 --> 00:02:08,900
已经捕捉到了错误

52
00:02:08,900 --> 00:02:10,820
是不是咱们的中间键打印了错误

53
00:02:10,820 --> 00:02:12,260
那么我们的Core里面

54
00:02:12,260 --> 00:02:13,400
它APP下面的一个Error事件

55
00:02:13,400 --> 00:02:14,080
是不是没捕捉到

56
00:02:14,080 --> 00:02:14,900
为什么呀

57
00:02:14,900 --> 00:02:16,600
其实这里呢可以得出一个结论

58
00:02:16,600 --> 00:02:17,780
如果说

59
00:02:17,780 --> 00:02:20,620
错误提前被

60
00:02:20,620 --> 00:02:23,200
catch处理了

61
00:02:23,200 --> 00:02:26,460
那么不会触发error事件

62
00:02:26,460 --> 00:02:29,220
那么有的同学会说

63
00:02:29,220 --> 00:02:30,860
那假如说我们有一种场景

64
00:02:30,860 --> 00:02:31,600
什么场景呢

65
00:02:31,600 --> 00:02:32,100
你比如说

66
00:02:32,100 --> 00:02:34,900
比如说我们想在app.onerror这里呢

67
00:02:34,900 --> 00:02:35,880
比如说我们去统一的

68
00:02:35,880 --> 00:02:38,140
假如说我们需要去

69
00:02:38,140 --> 00:02:45,000
假如我们要去做一些统一的处理

70
00:02:45,000 --> 00:02:45,540
什么意思

71
00:02:45,540 --> 00:02:47,260
什么意思

72
00:02:47,260 --> 00:02:48,020
也就是说

73
00:02:48,020 --> 00:02:50,160
假如说我们想在app.onerror

74
00:02:50,160 --> 00:02:51,600
因为它是我们所有错误的一个入口

75
00:02:51,600 --> 00:02:51,820
对吧

76
00:02:51,820 --> 00:02:54,000
我们所有的错误都会在app的on error

77
00:02:54,000 --> 00:02:55,980
在一个方法里面去触发

78
00:02:55,980 --> 00:02:58,760
因为你可能并不在我们的一个

79
00:02:58,760 --> 00:03:00,720
比如说不在我们一个中间键的范围以内

80
00:03:00,720 --> 00:03:02,340
比如说一些其他框架层面的错误

81
00:03:02,340 --> 00:03:03,980
其实呢也可以在app的on error里面去触发

82
00:03:03,980 --> 00:03:05,440
如果说我们想去统一处理怎么办

83
00:03:05,440 --> 00:03:06,440
但是呢

84
00:03:06,440 --> 00:03:08,120
我们的中间键又已经先catch了

85
00:03:08,120 --> 00:03:09,980
它没有触发我们app的on error方法

86
00:03:09,980 --> 00:03:10,640
怎么办

87
00:03:10,640 --> 00:03:12,240
我们的诉求是不是需要所有的错误

88
00:03:12,240 --> 00:03:14,100
你都要走appon error

89
00:03:14,100 --> 00:03:15,400
在error事件里面去触发

90
00:03:15,400 --> 00:03:16,240
怎么办

91
00:03:16,240 --> 00:03:18,420
其实这里有一个方法是什么呢

92
00:03:18,420 --> 00:03:19,240
比如说

93
00:03:19,240 --> 00:03:21,040
假如说我们发生了错误

94
00:03:21,040 --> 00:03:22,780
我们可以去手动的去触发

95
00:03:22,780 --> 00:03:27,020
contact.app.amit

96
00:03:27,020 --> 00:03:28,300
amit什么呢

97
00:03:28,300 --> 00:03:29,400
amit一个error

98
00:03:29,400 --> 00:03:32,280
比如说我们去传入一个发生的错误

99
00:03:32,280 --> 00:03:33,460
好

100
00:03:33,460 --> 00:03:34,460
这里我们再来试一下

101
00:03:34,460 --> 00:03:37,140
会发生一件什么事情

102
00:03:37,140 --> 00:03:40,420
好

103
00:03:40,420 --> 00:03:41,280
大家是不是可以看到

104
00:03:41,280 --> 00:03:43,880
我们不仅中间键它补助到的错误

105
00:03:43,880 --> 00:03:45,220
那么我们app的error事件

106
00:03:45,220 --> 00:03:46,140
其实也补助到的错误

107
00:03:46,140 --> 00:03:47,220
是不是就解决了我们这样一个问题

108
00:03:47,220 --> 00:03:48,060
对吧

109
00:03:48,060 --> 00:03:48,380
同学们

110
00:03:48,380 --> 00:03:50,420
好 这里就是我们

111
00:03:50,420 --> 00:03:52,540
这里就是我们通过

112
00:03:52,540 --> 00:03:54,740
app的emit去强行的

113
00:03:54,740 --> 00:03:56,960
去调用我们app的enerror去触发它

114
00:03:56,960 --> 00:03:58,720
那么为什么有的同学可能会问呢

115
00:03:58,720 --> 00:03:59,920
我们为什么可以去emit

116
00:03:59,920 --> 00:04:01,140
emit这样一个方法是哪里来的

117
00:04:01,140 --> 00:04:02,320
包括我们的app.en

118
00:04:02,320 --> 00:04:03,540
它是从哪里去监听事件的

119
00:04:03,540 --> 00:04:04,820
其实我们的整个call

120
00:04:04,820 --> 00:04:06,300
包括咱们整个app这样一个对象

121
00:04:06,300 --> 00:04:08,200
都是继承了我们nodegs的一个event的对象

122
00:04:08,200 --> 00:04:09,620
我们来可以看一下event是什么

123
00:04:09,620 --> 00:04:11,620
其实我们之前的内容其实讲过

124
00:04:11,620 --> 00:04:12,920
在咱们nodegs里面

125
00:04:12,920 --> 00:04:14,760
它有一个event emitter这样一个对象

126
00:04:14,760 --> 00:04:16,660
包括了我们去讲nodegs事件循环的时候

127
00:04:16,660 --> 00:04:17,580
是不是也讲过这样一个类

128
00:04:17,580 --> 00:04:18,680
好

129
00:04:18,680 --> 00:04:19,740
我们来看一下nodeGS

130
00:04:19,740 --> 00:04:20,880
它的一个even的emitter

131
00:04:20,880 --> 00:04:21,360
这样一个类

132
00:04:21,360 --> 00:04:24,260
那么even的emitter

133
00:04:24,260 --> 00:04:24,880
它的核心是什么

134
00:04:24,880 --> 00:04:26,260
它就是事件的出发

135
00:04:26,260 --> 00:04:26,860
与事件的监听

136
00:04:26,860 --> 00:04:27,400
怎么样出发

137
00:04:27,400 --> 00:04:29,080
是不是刚才咱们讲到emitter

138
00:04:29,080 --> 00:04:30,380
怎么样监听呢

139
00:04:30,380 --> 00:04:32,300
我们来看一下

140
00:04:32,300 --> 00:04:33,360
它的一个使用的小demo

141
00:04:33,360 --> 00:04:34,240
其实非常简单

142
00:04:34,240 --> 00:04:35,280
比如说我们去从

143
00:04:35,280 --> 00:04:36,320
咱们nodeGS event

144
00:04:36,320 --> 00:04:37,440
它下面有一个对象

145
00:04:37,440 --> 00:04:38,100
叫做even的emitter

146
00:04:38,100 --> 00:04:39,140
我们去new一个之后

147
00:04:39,140 --> 00:04:40,320
然后就可以去监听

148
00:04:40,320 --> 00:04:41,140
比如说我们去通过

149
00:04:41,140 --> 00:04:42,900
on监听一个sum

150
00:04:42,900 --> 00:04:44,100
event这样一个事件

151
00:04:44,100 --> 00:04:45,540
然后在一个定时器里面

152
00:04:45,540 --> 00:04:46,700
去通过emitter去触发它

153
00:04:46,700 --> 00:04:50,520
那么刚才我们是不是通过app on arrow和app meet arrow

154
00:04:50,520 --> 00:04:52,140
去实现了咱们这样一个错误的处理

155
00:04:52,140 --> 00:04:54,400
其实咱们的cora它那个app这样一个对象

156
00:04:54,400 --> 00:04:56,820
其实也是继承着咱们nodejs里面的event emitter

157
00:04:56,820 --> 00:04:59,940
好 那么这里呢就是我们

158
00:04:59,940 --> 00:05:02,980
cora里面对arrow它的一个监听

159
00:05:02,980 --> 00:05:03,640
我们来总结

160
00:05:03,640 --> 00:05:05,760
总结一下咱们这几个的内容

161
00:05:05,760 --> 00:05:08,880
刚才我们是不是讲到咱们errow时间的监听

162
00:05:08,880 --> 00:05:09,460
怎么样去监听

163
00:05:09,460 --> 00:05:12,820
是不是通过app的on去监听我们的errow

164
00:05:12,820 --> 00:05:13,900
事件怎么样去触发呢

165
00:05:13,900 --> 00:05:15,940
是不是可以通过我们的emitter呀

166
00:05:15,940 --> 00:05:16,440
为什么呀

167
00:05:16,440 --> 00:05:19,100
是不是它继承于咱们nodegs中的event对象

168
00:05:19,100 --> 00:05:20,240
下了什么方法

169
00:05:20,240 --> 00:05:21,840
event

170
00:05:21,840 --> 00:05:25,900
event meet

171
00:05:25,900 --> 00:05:26,440
对吧

172
00:05:26,440 --> 00:05:26,740
好

173
00:05:26,740 --> 00:05:28,080
这里就是我们这几个的内容

