1
00:00:00,000 --> 00:00:01,700
好 这节课我们就来看一下

2
00:00:01,700 --> 00:00:04,700
怎么样使用call去抛出一个500和一个试音试错误

3
00:00:04,700 --> 00:00:07,000
首先我们来看一下500

4
00:00:07,000 --> 00:00:11,500
比如说我们现在建了一个error.gs

5
00:00:11,500 --> 00:00:14,500
我们就来看一下咱们在min这个方法里面

6
00:00:14,500 --> 00:00:17,500
比如说我们直接去contextctx.throw

7
00:00:17,500 --> 00:00:18,800
它下面其实有spro这样一个方法

8
00:00:18,800 --> 00:00:20,500
比如说我们去抛出一个500

9
00:00:20,500 --> 00:00:22,000
这样呢 其实就可以抛出一个500错误

10
00:00:22,000 --> 00:00:24,500
我们来试一下loaderror.gs

11
00:00:28,000 --> 00:00:29,860
Nocal host 3000

12
00:00:29,860 --> 00:00:35,860
好 大家可以看到我们页面是不是抛出了一个500

13
00:00:35,860 --> 00:00:39,860
Intern的serve error说服务器的也不错误

14
00:00:39,860 --> 00:00:40,860
这里就是500

15
00:00:40,860 --> 00:00:42,980
那么同学们思考一下

16
00:00:42,980 --> 00:00:44,280
我们刚才抛出了一个500

17
00:00:44,280 --> 00:00:46,260
那么假如我们去serve一个404呢

18
00:00:46,260 --> 00:00:47,020
可不可以

19
00:00:47,020 --> 00:00:48,780
那么我们就来试一下

20
00:00:48,780 --> 00:00:54,260
404

21
00:00:54,260 --> 00:00:56,180
而且大家发现没有

22
00:00:56,180 --> 00:00:57,340
大家发现没有

23
00:00:57,340 --> 00:01:02,800
我们去throw 500或者throw 适应室的时候 是不是咱们浏览器会自动的给我们返回一些关键字啊

24
00:01:02,800 --> 00:01:06,220
 比如说notfond 比如说 是不是咱们的save arrow

25
00:01:06,220 --> 00:01:11,020
其实它是咱们call里面去封装的 你比如说返回适应室的时候 他自动的会注入一些

26
00:01:11,020 --> 00:01:14,740
 比如说把咱们context的玻璃改为notfond 咱们就能仿到notfond这样一个页面

27
00:01:14,740 --> 00:01:21,260
那么有的同学也可能会问了 我们刚才是不是throw了500和适应室 那我们能够throw一个200了

28
00:01:21,260 --> 00:01:23,020
 会发生一件什么事情 那我们再来看一下

29
00:01:25,700 --> 00:01:31,280
好 大家可以看到 其实当我们去throw200的时候 我们页面是不是会返回一个status200

30
00:01:31,280 --> 00:01:31,600
 ok啊

31
00:01:31,600 --> 00:01:37,420
说明一个什么问题 说明了咱们context的点throw 他其实并不是抛错的 他只是说你返回一个

32
00:01:37,420 --> 00:01:40,160
状态 你比如说以五开头或者四开头的 他就认为

33
00:01:40,160 --> 00:01:43,840
你拿着你发生错 但是你如果去抛出一个200 他依然会认为你是ok的

34
00:01:43,840 --> 00:01:45,940
好 那我们接下来再看一下

35
00:01:45,940 --> 00:01:50,280
另外一种抛错的方式 比如说咱们的适应是 你如果说想定制化一些

36
00:01:50,280 --> 00:01:53,400
你想要 比如说你去抛出一个适应是

37
00:01:53,840 --> 00:01:56,660
你说我不想告诉用户 咱们的配置是多放的 我想说一些别的话

38
00:01:56,660 --> 00:02:00,500
那么我们可不可以去定制呢 其实也可以 比如说你可以把你的status 改为视频室

39
00:02:00,500 --> 00:02:01,780
 然后把玻璃修改一下

40
00:02:01,780 --> 00:02:03,060
那么我们就来看一下

41
00:02:03,060 --> 00:02:10,740
可不可以这么玩 比如说我们contact.res.bounce.什么呢 是不是status

42
00:02:10,740 --> 00:02:16,120
比如说我们去等于视频室 然后咱们把它的玻璃给改一下

43
00:02:16,620 --> 00:02:20,460
racebounce.body 等于什么呢 啊等于

44
00:02:20,460 --> 00:02:22,760
页面 没有

45
00:02:22,760 --> 00:02:24,820
找到 好 我们再来

46
00:02:24,820 --> 00:02:26,600
重新访问一下

47
00:02:26,600 --> 00:02:31,220
好 大家可以看到 我们此时状态是不是404

48
00:02:31,220 --> 00:02:33,260
然后呢 返回的是 页面没有找到

49
00:02:33,260 --> 00:02:35,560
那么我们可以依次内推 比如说你去

50
00:02:35,560 --> 00:02:36,340
比如说你去

51
00:02:36,340 --> 00:02:39,660
Status改为500 那么玻璃呢 改一些别的字是不是也可以实现这样的需求

52
00:02:39,660 --> 00:02:44,520
好 这里呢 就是咱们口网里面去抛出500错误和404错误的分别

53
00:02:44,520 --> 00:02:46,060
两种方法 那么我们就来

54
00:02:46,580 --> 00:02:47,100
回顾一下

55
00:02:47,100 --> 00:02:50,420
我们刚才是不是讲解了咱们call

56
00:02:50,420 --> 00:02:51,960
抛错了一下方式

57
00:02:51,960 --> 00:02:54,260
抛出

58
00:02:54,260 --> 00:02:55,280
错误

59
00:02:55,280 --> 00:02:58,860
你比如说你是不是可以通过context.sport还有一种方式呢

60
00:02:58,860 --> 00:02:59,120
咱们是不是直接改context.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.sport.s

61
00:02:59,380 --> 00:03:03,140
是不是直接改contest.res.bounce.status

62
00:03:03,140 --> 00:03:06,040
然后呢是不是修改

63
00:03:06,040 --> 00:03:12,820
Body啊这两种方式都是可以了 但是throw呢 他是不是会给一些call 他自定义了一些咱们的爆错的一些提示

64
00:03:12,820 --> 00:03:18,340
但是呢 你可以通过修改他的body的方式去做一些定制化的信息 好 这里呢就是我们这节课的内容

