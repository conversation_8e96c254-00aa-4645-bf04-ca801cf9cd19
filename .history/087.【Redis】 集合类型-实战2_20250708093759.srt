1
00:00:00,000 --> 00:00:03,600
好 这节课我们就来看一下怎么样通过Tax去搜索一篇文章

2
00:00:03,600 --> 00:00:04,820
比如说

3
00:00:04,820 --> 00:00:07,920
比如说我们举个例子

4
00:00:07,920 --> 00:00:10,660
我们需要去实现一个需求

5
00:00:10,660 --> 00:00:14,120
就是说你需要去查出某些标签下的所有文章

6
00:00:14,120 --> 00:00:16,000
比如说有些文章它属于漏的

7
00:00:16,000 --> 00:00:17,120
或者属于加网类型的文章

8
00:00:17,120 --> 00:00:18,520
你需要通过Tax把它给查询出来

9
00:00:18,520 --> 00:00:20,000
如果说要实现这样一个需求

10
00:00:20,000 --> 00:00:22,800
我们通过咱们以往的关系性数据库怎么样去做

11
00:00:22,800 --> 00:00:24,740
之前我们是不是讲过

12
00:00:24,740 --> 00:00:25,800
你如果要实现Tax

13
00:00:25,800 --> 00:00:27,280
在咱们的关系性数据库

14
00:00:27,280 --> 00:00:27,860
比如说买SOCO

15
00:00:27,860 --> 00:00:28,860
你要去实现三张表

16
00:00:28,860 --> 00:00:31,020
首先你的文章ID

17
00:00:31,020 --> 00:00:32,720
文章ID和文章标题

18
00:00:32,720 --> 00:00:33,500
需要去建一张表

19
00:00:33,500 --> 00:00:35,200
去和他们之间对应关系

20
00:00:35,200 --> 00:00:36,360
同学们还记得我们之前

21
00:00:36,360 --> 00:00:38,460
实际上过这样一个demo吗

22
00:00:38,460 --> 00:00:40,500
我们文章的缩列名和我们的ID

23
00:00:40,500 --> 00:00:42,440
是不是专门通过一个字段去存储

24
00:00:42,440 --> 00:00:43,640
比如是不是叫做

25
00:00:43,640 --> 00:00:45,220
slog.tubeID

26
00:00:45,220 --> 00:00:47,000
去存储咱们文章的ID和标题

27
00:00:47,000 --> 00:00:49,140
他们之间的一个映射关系

28
00:00:49,140 --> 00:00:50,900
那么我们还需要存储什么呢

29
00:00:50,900 --> 00:00:52,040
还需要存储什么

30
00:00:52,040 --> 00:00:53,560
是不是还需要去存储

31
00:00:53,560 --> 00:00:56,240
我们文章的ID和我们的标签ID

32
00:00:56,240 --> 00:00:58,060
你比如说一个42号文章

33
00:00:58,060 --> 00:00:59,980
它对应的标签的ID是什么

34
00:00:59,980 --> 00:01:00,960
标签的ID

35
00:01:00,960 --> 00:01:03,960
你比如说你文章是42号

36
00:01:03,960 --> 00:01:05,060
好

37
00:01:05,060 --> 00:01:07,020
那么你这篇文章它的ID是不是有多个

38
00:01:07,020 --> 00:01:07,900
所以你需要去

39
00:01:07,900 --> 00:01:09,140
比如说你有一个1号标签

40
00:01:09,140 --> 00:01:10,100
一个4号标签

41
00:01:10,100 --> 00:01:11,120
一个5号标签

42
00:01:11,120 --> 00:01:12,260
但是你1号标签

43
00:01:12,260 --> 00:01:12,760
4号标签

44
00:01:12,760 --> 00:01:13,900
5号标签分别是什么呢

45
00:01:13,900 --> 00:01:15,040
不知道吧

46
00:01:15,040 --> 00:01:16,820
所以你还要去另外一张表中去查

47
00:01:16,820 --> 00:01:17,260
哪一张呢

48
00:01:17,260 --> 00:01:18,100
也就是中间这张表

49
00:01:18,100 --> 00:01:19,760
中间这一表存储的是什么

50
00:01:19,760 --> 00:01:22,520
是不是存储了咱们的标签和名称之间的关系

51
00:01:22,520 --> 00:01:23,700
你比如说

52
00:01:23,700 --> 00:01:24,740
你比如说

53
00:01:24,740 --> 00:01:26,620
需要去存储ID和名称

54
00:01:26,620 --> 00:01:27,120
比如说1号

55
00:01:27,120 --> 00:01:28,280
一号对应的是nodejs

56
00:01:28,280 --> 00:01:30,300
四号对应的是java

57
00:01:30,300 --> 00:01:32,380
你五号是不是也就对应的是什么

58
00:01:32,380 --> 00:01:33,120
python

59
00:01:33,120 --> 00:01:34,440
老师这里来举例子

60
00:01:34,440 --> 00:01:36,100
在咱们传统的关系数据库里面

61
00:01:36,100 --> 00:01:37,760
你要去实现通过标签去搜索文章

62
00:01:37,760 --> 00:01:39,000
你需要去建三张表

63
00:01:39,000 --> 00:01:40,740
才可以实现这样一个功能

64
00:01:40,740 --> 00:01:42,220
那么建三张表还不算

65
00:01:42,220 --> 00:01:44,240
我们来看一下它最复杂的地方

66
00:01:44,240 --> 00:01:45,320
也是最恶心的地方是什么

67
00:01:45,320 --> 00:01:46,220
大家来看一下

68
00:01:46,220 --> 00:01:48,340
你比如说我们要去寻找

69
00:01:48,340 --> 00:01:49,800
一篇文章

70
00:01:49,800 --> 00:01:51,100
它既属于java

71
00:01:51,100 --> 00:01:52,020
又属于mysoco

72
00:01:52,020 --> 00:01:53,900
又属于redis这三个标签文章

73
00:01:53,900 --> 00:01:55,000
也就是说一篇文章

74
00:01:55,000 --> 00:01:55,960
它同时打了三个标签

75
00:01:55,960 --> 00:02:00,000
如果说我们用一个circle语句去查询

76
00:02:00,000 --> 00:02:02,000
把这些文章给查询出来

77
00:02:02,000 --> 00:02:04,020
咱们的circle语句需要这样去写

78
00:02:04,020 --> 00:02:06,180
需要这样去写

79
00:02:06,180 --> 00:02:08,220
说实话

80
00:02:08,220 --> 00:02:09,240
我看不懂

81
00:02:09,240 --> 00:02:10,540
很郁闷

82
00:02:10,540 --> 00:02:11,500
这怎么去看

83
00:02:11,500 --> 00:02:12,660
这世人看的代码吗

84
00:02:12,660 --> 00:02:13,380
很难去阅读

85
00:02:13,380 --> 00:02:14,540
select

86
00:02:14,540 --> 00:02:15,720
什么什么什么

87
00:02:15,720 --> 00:02:16,560
from

88
00:02:16,560 --> 00:02:17,540
一大堆

89
00:02:17,540 --> 00:02:18,300
will呢

90
00:02:18,300 --> 00:02:19,220
will它这个ID

91
00:02:19,220 --> 00:02:19,940
要等于

92
00:02:19,940 --> 00:02:21,200
我们需要去查询的ID

93
00:02:21,200 --> 00:02:21,500
而且呢

94
00:02:21,500 --> 00:02:22,600
他还要印加把

95
00:02:22,600 --> 00:02:23,500
mysocleredis

96
00:02:23,500 --> 00:02:24,120
and

97
00:02:24,120 --> 00:02:26,880
然后呢groupby还要属于某一个组

98
00:02:26,880 --> 00:02:28,300
然后还要什么什么

99
00:02:28,300 --> 00:02:29,660
大家觉得这样的代码可读吗

100
00:02:29,660 --> 00:02:31,440
是不是很不易阅读而且容易出错

101
00:02:31,440 --> 00:02:34,240
这也是关系数据库它的一个缺点

102
00:02:34,240 --> 00:02:35,920
而且它的查询效率是比较低的

103
00:02:35,920 --> 00:02:36,280
为什么呀

104
00:02:36,280 --> 00:02:38,680
比如说我们通过去写CSS的时候

105
00:02:38,680 --> 00:02:40,740
你如果说去写这样一段代码

106
00:02:40,740 --> 00:02:41,280
比如说DIV

107
00:02:41,280 --> 00:02:42,900
下面有个P标签

108
00:02:42,900 --> 00:02:44,360
P标签的下面有一个span

109
00:02:44,360 --> 00:02:45,760
span下面有个I标签

110
00:02:45,760 --> 00:02:47,400
然后你去给它写一些样式

111
00:02:47,400 --> 00:02:48,880
那么在CSS里面

112
00:02:48,880 --> 00:02:50,860
它的一个渲染效率是不是很低呀

113
00:02:50,860 --> 00:02:51,720
我们讲前端的时候

114
00:02:52,920 --> 00:02:54,200
在咱们的雅虎金规里面

115
00:02:54,200 --> 00:02:54,760
是不是有一条准则

116
00:02:54,760 --> 00:02:55,200
是什么

117
00:02:55,200 --> 00:02:55,960
我们的CS

118
00:02:55,960 --> 00:02:57,280
它嵌套的层数越少越好

119
00:02:57,280 --> 00:02:58,360
因为你嵌套多了会影响性能

120
00:02:58,360 --> 00:03:00,020
在咱们MySoco的数据库里面

121
00:03:00,020 --> 00:03:00,540
同样的

122
00:03:00,540 --> 00:03:01,960
你在一些关键词写多了

123
00:03:01,960 --> 00:03:02,620
说明它查询

124
00:03:02,620 --> 00:03:03,340
非常的复杂

125
00:03:03,340 --> 00:03:04,440
它的性能也不会好

126
00:03:04,440 --> 00:03:06,520
所以这是MySoco它的一个缺点

127
00:03:06,520 --> 00:03:08,020
那么我们来看一下

128
00:03:08,020 --> 00:03:09,060
通过Redis怎么样去实现

129
00:03:09,060 --> 00:03:10,880
其实通过Redis就非常好实现

130
00:03:10,880 --> 00:03:13,580
其实我们用Redis去存取的时候

131
00:03:13,580 --> 00:03:14,360
也需要去实现

132
00:03:14,360 --> 00:03:16,080
我们的两张表

133
00:03:16,080 --> 00:03:17,700
那么这两张表怎么样去存取

134
00:03:17,700 --> 00:03:18,500
同学们我们来看一下

135
00:03:18,500 --> 00:03:21,840
我们来看一下

136
00:03:21,840 --> 00:03:23,760
首先

137
00:03:23,760 --> 00:03:25,780
首先我们是不是要去

138
00:03:25,780 --> 00:03:27,000
低步

139
00:03:27,000 --> 00:03:29,240
首先低步

140
00:03:29,240 --> 00:03:32,200
首先低步我们是不是要去存储

141
00:03:32,200 --> 00:03:33,840
存储文章

142
00:03:33,840 --> 00:03:35,800
对应的Tax

143
00:03:35,800 --> 00:03:36,880
什么意思

144
00:03:36,880 --> 00:03:38,640
你比如说咱们看这张图

145
00:03:38,640 --> 00:03:40,160
我们ID为1的文章

146
00:03:40,160 --> 00:03:42,040
你比如说存储了一个Tax

147
00:03:42,040 --> 00:03:42,520
它为Java

148
00:03:42,520 --> 00:03:43,980
我们ID为2的文章

149
00:03:43,980 --> 00:03:44,480
它有两个

150
00:03:44,480 --> 00:03:46,060
比如说它既属于Java又属于MySoco

151
00:03:46,060 --> 00:03:48,100
那么ID为3的文章

152
00:03:48,100 --> 00:03:48,760
这里是三篇文章

153
00:03:48,760 --> 00:03:49,320
ID为3的文章

154
00:03:49,320 --> 00:03:50,140
它存储了三个标签

155
00:03:50,140 --> 00:03:51,600
Java、MySoco和Redis

156
00:03:51,600 --> 00:03:54,120
如果说我们的数据结构

157
00:03:54,120 --> 00:03:56,140
到这里能不能实现

158
00:03:56,140 --> 00:03:57,480
我们去查询的需求

159
00:03:57,480 --> 00:03:59,160
比如说查询它同时

160
00:03:59,160 --> 00:03:59,940
它是属于加码

161
00:03:59,940 --> 00:04:00,460
属于MySoco

162
00:04:00,460 --> 00:04:01,620
属于Redis的文章把它查出来

163
00:04:01,620 --> 00:04:04,660
好像很难吧

164
00:04:04,660 --> 00:04:05,640
因为你也不知道

165
00:04:05,640 --> 00:04:06,920
因为你不可能说去便利

166
00:04:06,920 --> 00:04:09,400
整个比如说你不可能说

167
00:04:09,400 --> 00:04:10,660
把所有的文章ID列举出来

168
00:04:10,660 --> 00:04:12,380
然后一个一个的去货群还吧

169
00:04:12,380 --> 00:04:13,480
然后去寻找什么加码

170
00:04:13,480 --> 00:04:14,520
MySoco Redis在不这里面

171
00:04:14,520 --> 00:04:15,320
肯定不可以

172
00:04:15,320 --> 00:04:17,040
所以我们还需要一种

173
00:04:17,040 --> 00:04:18,660
还需要去建立一个关系

174
00:04:18,660 --> 00:04:19,360
你比如说

175
00:04:19,360 --> 00:04:20,840
我们建立一个什么关系呢

176
00:04:20,840 --> 00:04:25,000
我们需要去存储

177
00:04:25,000 --> 00:04:26,800
存储什么呢

178
00:04:26,800 --> 00:04:30,580
存储某一类Tax下面

179
00:04:30,580 --> 00:04:34,680
有哪些文章ID

180
00:04:34,680 --> 00:04:37,180
什么意思啊

181
00:04:37,180 --> 00:04:38,340
同学们

182
00:04:38,340 --> 00:04:39,240
你比如说首先第一步

183
00:04:39,240 --> 00:04:40,200
你存储文章的时候

184
00:04:40,200 --> 00:04:42,520
你是不是同时会把这篇文章的Tax

185
00:04:42,520 --> 00:04:43,740
给存到这篇文章ID下面

186
00:04:43,740 --> 00:04:44,220
好

187
00:04:44,220 --> 00:04:45,880
那么你存储Tax的时候

188
00:04:45,880 --> 00:04:47,440
其实同时你反过来

189
00:04:47,440 --> 00:04:49,040
反过来也要通过Tax

190
00:04:49,040 --> 00:04:50,500
把这些文章的ID给存进去

191
00:04:50,500 --> 00:04:51,760
你比如说你需要去建一个字段

192
00:04:51,760 --> 00:04:53,140
叫做tag

193
00:04:53,140 --> 00:04:54,360
redis post

194
00:04:54,360 --> 00:04:54,960
什么意思

195
00:04:54,960 --> 00:04:56,420
说明你存储的类型是tag

196
00:04:56,420 --> 00:04:57,760
那么咱们的tag

197
00:04:57,760 --> 00:04:58,760
比如说你定义了一个redis

198
00:04:58,760 --> 00:04:59,880
redis它属于变量

199
00:04:59,880 --> 00:05:03,060
然后咱们redis下面文章的id对应的是几

200
00:05:03,060 --> 00:05:05,320
redis对应的文章id是不是三了

201
00:05:05,320 --> 00:05:07,380
因为只有我们的三号文章才打了

202
00:05:07,380 --> 00:05:08,100
redis这样一个tag

203
00:05:08,100 --> 00:05:10,640
所以说我们去对每一个tag

204
00:05:10,640 --> 00:05:13,380
对每一个tag和咱们的文章id对应

205
00:05:13,380 --> 00:05:14,440
建立一个映射关系

206
00:05:14,440 --> 00:05:16,940
你比如说咱们的mysoco

207
00:05:16,940 --> 00:05:17,660
mysoco

208
00:05:17,660 --> 00:05:18,780
哪些文章有mysoco

209
00:05:18,780 --> 00:05:20,600
是不是我们的2号和3号文章

210
00:05:20,600 --> 00:05:22,720
所以它去存储了2和3

211
00:05:22,720 --> 00:05:23,280
这样一个ID

212
00:05:23,280 --> 00:05:24,660
那么我们再看一下Java

213
00:05:24,660 --> 00:05:25,860
哪些文章的ID

214
00:05:25,860 --> 00:05:27,340
里面有Java这样一个tag

215
00:05:27,340 --> 00:05:28,640
是不是123都有

216
00:05:28,640 --> 00:05:30,020
123都有吧

217
00:05:30,020 --> 00:05:30,640
都有Java

218
00:05:30,640 --> 00:05:32,360
好

219
00:05:32,360 --> 00:05:34,560
那么这里我们怎么样去实现这样一个需求呢

220
00:05:34,560 --> 00:05:35,720
你假如现在我们的需求来了

221
00:05:35,720 --> 00:05:36,920
你要去查询

222
00:05:36,920 --> 00:05:39,860
既打了Java又打了买SOCO

223
00:05:39,860 --> 00:05:41,020
还打了Redis

224
00:05:41,020 --> 00:05:42,620
还打了Redis这样一个标签的一篇文章

225
00:05:42,620 --> 00:05:43,600
它的ID是什么

226
00:05:43,600 --> 00:05:45,120
我们怎么去做

227
00:05:45,120 --> 00:05:46,480
是不是只需要一条命令呢

228
00:05:46,480 --> 00:05:46,780
我们去

229
00:05:46,780 --> 00:05:48,600
我们是不是只需要去求

230
00:05:48,600 --> 00:05:50,000
他们的差级

231
00:05:50,000 --> 00:05:51,760
你比如说

232
00:05:51,760 --> 00:05:56,520
求他们的差级

233
00:05:56,520 --> 00:05:57,180
是不是就可以完成

234
00:05:57,180 --> 00:05:58,840
咱们这样的一个需求

235
00:05:58,840 --> 00:05:59,920
我们只需要

236
00:05:59,920 --> 00:06:01,060
我们只需要把radis

237
00:06:01,060 --> 00:06:02,300
mysoco和java

238
00:06:02,300 --> 00:06:03,900
咱们去取一个交集

239
00:06:03,900 --> 00:06:04,980
你是不是就可以知道

240
00:06:04,980 --> 00:06:06,260
同时属于java

241
00:06:06,260 --> 00:06:07,000
mysoco和radis

242
00:06:07,000 --> 00:06:08,140
这样的三个标签文章

243
00:06:08,140 --> 00:06:08,640
是谁呢

244
00:06:08,640 --> 00:06:09,500
好

245
00:06:09,500 --> 00:06:10,420
那么这里呢

246
00:06:10,420 --> 00:06:12,980
就是我们通过radis去改造

247
00:06:12,980 --> 00:06:16,480
我们文章存取和查询tags

248
00:06:16,480 --> 00:06:17,540
这样的一个需求

249
00:06:17,540 --> 00:06:19,100
我们这里来总结一下

250
00:06:19,100 --> 00:06:22,420
首先通过mysoco去存储我们的标签

251
00:06:22,420 --> 00:06:24,460
它是不是需要去实现三张表

252
00:06:24,460 --> 00:06:25,900
那么三张表不说

253
00:06:25,900 --> 00:06:27,520
它的一个soco语句是不是很复杂

254
00:06:27,520 --> 00:06:28,960
soco语句非常的复杂

255
00:06:28,960 --> 00:06:30,880
那么通过我们的radis怎么去做呢

256
00:06:30,880 --> 00:06:31,600
其实分为两步

257
00:06:31,600 --> 00:06:33,560
首先呢你把每一篇文章对应的tags

258
00:06:33,560 --> 00:06:34,620
先存起来

259
00:06:34,620 --> 00:06:35,700
你比如说id123

260
00:06:35,700 --> 00:06:37,500
id123它分别存储了tags

261
00:06:37,500 --> 00:06:37,980
是java

262
00:06:37,980 --> 00:06:39,640
比如说第二篇文章存储了两个tags

263
00:06:39,640 --> 00:06:40,940
第三篇文章的话存了三个tags

264
00:06:40,940 --> 00:06:43,320
那么你在存储文章tags的同时

265
00:06:43,320 --> 00:06:43,820
你反过来

266
00:06:43,820 --> 00:06:45,600
把这些tags对应的文章id

267
00:06:45,600 --> 00:06:46,580
你也给存起来

268
00:06:46,580 --> 00:06:48,020
你比如说Redis对应的ID为3

269
00:06:48,020 --> 00:06:49,400
MySQL对应的文章是23

270
00:06:49,400 --> 00:06:51,020
Java对应的文章是123

271
00:06:51,020 --> 00:06:53,420
那么如果说你需要去查询哪一篇文章的ID

272
00:06:53,420 --> 00:06:54,920
哪一篇文章的tag

273
00:06:54,920 --> 00:06:56,420
它既有Java又有MySQL

274
00:06:56,420 --> 00:06:57,960
然后呢还有Redis

275
00:06:57,960 --> 00:07:02,260
你只需要把tagJavaPost和tagMySQLPost

276
00:07:02,260 --> 00:07:03,860
也就是你把他们三个

277
00:07:03,860 --> 00:07:06,060
把他们三个取一个交集

278
00:07:06,060 --> 00:07:07,420
是不是就可以得出

279
00:07:07,420 --> 00:07:09,040
得出什么呢

280
00:07:09,040 --> 00:07:10,680
你把他们三个取一个交集

281
00:07:10,680 --> 00:07:11,840
比如说他们

282
00:07:11,840 --> 00:07:12,760
比如说Redis

283
00:07:12,760 --> 00:07:14,620
我们的Redis

284
00:07:14,620 --> 00:07:16,620
mysoco和加勒取一个交集

285
00:07:16,620 --> 00:07:19,120
如果说他们的交集存在一个值

286
00:07:19,120 --> 00:07:20,780
是不是也就存在一篇文章

287
00:07:20,780 --> 00:07:22,020
拥有他们三个的tags

288
00:07:22,020 --> 00:07:22,580
你比如说

289
00:07:22,580 --> 00:07:24,500
你比如说他们的交集是谁

290
00:07:24,500 --> 00:07:25,440
他们的交集是不是三

291
00:07:25,440 --> 00:07:27,000
说明id为三的这篇文章

292
00:07:27,000 --> 00:07:28,280
他拥有三个tags

293
00:07:28,280 --> 00:07:29,540
redis mysoco和加勒

294
00:07:29,540 --> 00:07:33,060
好这里就是我们这一节课的内容

