1
00:00:00,000 --> 00:00:04,600
好 那么这一节课呢 我们来看一下如何去编写schema以及设计存储的字段

2
00:00:04,600 --> 00:00:06,660
好 那么我们

3
00:00:06,660 --> 00:00:10,760
在哪里去写 大家去思考一下 我们在哪里去写schema

4
00:00:10,760 --> 00:00:13,560
大家思考一下 在controller里面去写吗

5
00:00:13,560 --> 00:00:15,100
在service吗

6
00:00:15,100 --> 00:00:17,660
大家可以暂停想一下 我们去哪里去

7
00:00:17,660 --> 00:00:19,200
设计咱们一个schema

8
00:00:19,200 --> 00:00:23,800
好 那么这里就不给同学们去卖关子了 实际上我们在咱们eGG里面一个哪里呢

9
00:00:23,800 --> 00:00:24,840
 这里是他的一个官网

10
00:00:24,840 --> 00:00:27,640
我们可以收到一个叫做model 因为我们前面讲说是不是

11
00:00:27,900 --> 00:00:29,660
说到Model这个东西我们在后面的内容会去讲

12
00:00:29,660 --> 00:00:30,800
实际上在这里我们就有使用到

13
00:00:30,800 --> 00:00:32,400
那么我们来看一下他官网的一个介绍

14
00:00:32,400 --> 00:00:34,340
其实他的介绍是非常的少的

15
00:00:34,340 --> 00:00:37,340
AppModel用于放置领域模型可选

16
00:00:37,340 --> 00:00:39,100
由领域内相关插件约定

17
00:00:39,100 --> 00:00:40,760
如1GG-Saculize

18
00:00:40,760 --> 00:00:41,980
那么Saculize是什么呢

19
00:00:41,980 --> 00:00:43,880
它其实也是一种对数据进行处理的一个酷

20
00:00:43,880 --> 00:00:45,940
那么同样的我们去操作我们的一个Mongos

21
00:00:45,940 --> 00:00:47,360
其实也可以把我们的一个模型

22
00:00:47,360 --> 00:00:49,020
其实它的核心就是模型

23
00:00:49,020 --> 00:00:50,960
也可以把我们的数据模型给放到Model下面去

24
00:00:50,960 --> 00:00:52,920
那么其实说的神乎其神什么是领域

25
00:00:52,920 --> 00:00:55,020
包括现在咱们领域炒得非常火的一个叫什么呢

26
00:00:55,020 --> 00:00:56,340
叫做领域驱动式开发

27
00:00:56,340 --> 00:00:59,060
其实他们的都是一些概念

28
00:00:59,060 --> 00:01:00,800
说的神乎其成其实非常简单

29
00:01:00,800 --> 00:01:01,820
那么这里呢

30
00:01:01,820 --> 00:01:03,180
我来给同学们演示一下

31
00:01:03,180 --> 00:01:04,060
我们怎么样去写一个model

32
00:01:04,060 --> 00:01:06,420
大家不要被这样一些概念给吓住

33
00:01:06,420 --> 00:01:07,240
其实非常的简单

34
00:01:07,240 --> 00:01:08,360
那么有的人会说

35
00:01:08,360 --> 00:01:10,340
老师你的model为什么介绍这里没有去介绍呢

36
00:01:10,340 --> 00:01:12,100
其实我根本就没有搞清楚它的概念

37
00:01:12,100 --> 00:01:12,900
那么其实这里呢

38
00:01:12,900 --> 00:01:13,900
不是我不去介绍model

39
00:01:13,900 --> 00:01:14,740
因为它的文档里面

40
00:01:14,740 --> 00:01:16,720
其实对model的一个介绍本身就是非常少的

41
00:01:16,720 --> 00:01:18,160
对model的介绍非常少

42
00:01:18,160 --> 00:01:19,680
大家可以看到我去搜索model都说不到

43
00:01:19,680 --> 00:01:20,340
所以说呢

44
00:01:20,340 --> 00:01:22,140
其实文档里面对Model也没有一个具体的介绍

45
00:01:22,140 --> 00:01:24,060
我们通过使用来看一下它到底怎么用

46
00:01:24,060 --> 00:01:25,100
其实非常的简单

47
00:01:25,100 --> 00:01:27,120
这次它文档里面没有去介绍的一个原因

48
00:01:27,120 --> 00:01:27,540
好

49
00:01:27,540 --> 00:01:29,280
同学们跟着老师去写就可以了

50
00:01:29,280 --> 00:01:31,840
首先我们在APP下面去创建一个Model这样一个文件夹

51
00:01:31,840 --> 00:01:33,420
然后接下来干什么

52
00:01:33,420 --> 00:01:35,280
我们是不是要去设计我们的一个数据库

53
00:01:35,280 --> 00:01:36,040
之前讲到了

54
00:01:36,040 --> 00:01:37,980
我们需要去在一个表里面去存张

55
00:01:37,980 --> 00:01:39,060
存储我们的文章列表

56
00:01:39,060 --> 00:01:39,600
所以说呢

57
00:01:39,600 --> 00:01:41,720
我们去创建一个叫做Posts.js

58
00:01:41,720 --> 00:01:42,660
那么这样一个js

59
00:01:42,660 --> 00:01:43,920
其实它代表了一个什么意思呢

60
00:01:43,920 --> 00:01:45,500
然后我来给大家去做一下笔记

61
00:01:45,500 --> 00:01:48,060
Posts.js其实就是代表咱们

62
00:01:48,680 --> 00:01:50,880
存储的表叫做

63
00:01:50,880 --> 00:01:52,820
posed好

64
00:01:52,820 --> 00:01:57,600
那么不理解没关系等一下我们程序运行起来大家就可以很快的理解

65
00:01:57,600 --> 00:02:00,000
相信同学们一定能理解首先我们去摸掉

66
00:02:00,000 --> 00:02:03,460
也一个是因为我们必须去导出一个

67
00:02:03,460 --> 00:02:07,300
东西然后咱们去是不是咱们有个方式呢里面会传入一个app

68
00:02:07,300 --> 00:02:10,500
首先呢我们在app里面可以取到一个东西叫做mongos

69
00:02:10,500 --> 00:02:15,420
他是咱们咱们mongos一个实力挂载的app上面是不是和我们的很多的插件类似啊

70
00:02:15,420 --> 00:02:16,420
比如说你去做一个模板

71
00:02:16,420 --> 00:02:17,380
是不是有个rend的方法

72
00:02:17,380 --> 00:02:18,780
也挂载在咱们的APP下面

73
00:02:18,780 --> 00:02:19,680
或者context下面

74
00:02:19,680 --> 00:02:21,160
所以说其实这里呢是类似的

75
00:02:21,160 --> 00:02:22,700
这里呢就咱们不去做过多的介绍了

76
00:02:22,700 --> 00:02:23,980
因为咱们引入了一个什么呀

77
00:02:23,980 --> 00:02:24,660
对吧

78
00:02:24,660 --> 00:02:26,400
我们是不是引入了咱们

79
00:02:26,400 --> 00:02:27,960
eGG-Mongos这样一个插件

80
00:02:27,960 --> 00:02:30,200
所以说你的APP下面就有了Mongos这样一个对象

81
00:02:30,200 --> 00:02:33,780
因为引入了插件

82
00:02:33,780 --> 00:02:34,760
好 那我们继续

83
00:02:34,760 --> 00:02:36,820
首先我们引入了Mongos这样一个对象之后

84
00:02:36,820 --> 00:02:38,940
那么大家还记不记得我们怎么样去使用Mongos

85
00:02:38,940 --> 00:02:41,840
第一步是不是创建一个schema呀

86
00:02:41,840 --> 00:02:42,120
对吧

87
00:02:42,120 --> 00:02:42,980
对吧

88
00:02:42,980 --> 00:02:43,260
同学们

89
00:02:43,260 --> 00:02:44,840
比如说我们这里呢

90
00:02:44,840 --> 00:02:46,320
是不是需要去创建一个schema

91
00:02:46,320 --> 00:02:47,520
首先呢我们去Const

92
00:02:47,520 --> 00:02:51,000
Const的一个schema

93
00:02:51,000 --> 00:02:53,040
可能有的同学会问

94
00:02:53,040 --> 00:02:54,840
你这里为什么不叫post之schema

95
00:02:54,840 --> 00:02:56,680
其实随意啊这里随意

96
00:02:56,680 --> 00:02:57,180
为什么呢

97
00:02:57,180 --> 00:02:58,580
因为我们现在的这样一个js

98
00:02:58,580 --> 00:02:59,080
它在哪里呢

99
00:02:59,080 --> 00:03:02,300
它是不是就在app的appmodel的post.js里面呢

100
00:03:02,300 --> 00:03:03,400
你即使你叫schema

101
00:03:03,400 --> 00:03:05,140
大家也可以认为你就是一个post之schema

102
00:03:05,140 --> 00:03:05,480
对吧

103
00:03:05,480 --> 00:03:06,680
比如说你哪天拿一个user

104
00:03:06,680 --> 00:03:06,880
对吧

105
00:03:06,880 --> 00:03:08,600
你在user.js里面你也叫schema

106
00:03:08,600 --> 00:03:10,400
当然其实也会对应到一个user下面去

107
00:03:10,400 --> 00:03:11,400
所以说呢大家不要

108
00:03:11,400 --> 00:03:12,840
不要担心我们的命名

109
00:03:12,840 --> 00:03:14,140
是不是看不出来我们是哪个schema

110
00:03:14,140 --> 00:03:16,660
其实是可以的

111
00:03:16,660 --> 00:03:17,540
我们可以通过JS去看

112
00:03:17,540 --> 00:03:18,240
那么这里呢

113
00:03:18,240 --> 00:03:20,560
首先呢我们schema等于

114
00:03:20,560 --> 00:03:23,480
蒙古斯点

115
00:03:23,480 --> 00:03:24,640
咱们蒙古斯上面呢

116
00:03:24,640 --> 00:03:27,220
其实有一个schema在那一个方法

117
00:03:27,220 --> 00:03:28,260
对吧

118
00:03:28,260 --> 00:03:29,660
那么呢我们怎么去使用呢

119
00:03:29,660 --> 00:03:30,980
是不是咱们要去lue一下

120
00:03:30,980 --> 00:03:31,640
对吧

121
00:03:31,640 --> 00:03:32,760
咱们去lue一下咱们的

122
00:03:32,760 --> 00:03:33,860
蒙古斯下面schema方法

123
00:03:33,860 --> 00:03:34,420
然后

124
00:03:34,420 --> 00:03:36,140
咱们之前是不是讲过

125
00:03:36,140 --> 00:03:37,700
我们schema里面传入的是什么呀

126
00:03:37,700 --> 00:03:38,320
是不是传入的

127
00:03:38,320 --> 00:03:39,820
其实是咱们表的之端呢

128
00:03:39,820 --> 00:03:41,260
也就是咱们文章的之端

129
00:03:41,260 --> 00:03:42,760
那么我们文章的之端

130
00:03:42,760 --> 00:03:43,700
需要去设计哪两个之端

131
00:03:43,700 --> 00:03:44,620
之前我们是不是讲过

132
00:03:44,620 --> 00:03:45,900
是不是一个title

133
00:03:45,900 --> 00:03:46,360
对吧

134
00:03:46,360 --> 00:03:46,900
还有一个呢

135
00:03:46,900 --> 00:03:47,600
这是咱们一个content

136
00:03:47,600 --> 00:03:48,320
也就是我们的证文

137
00:03:48,320 --> 00:03:49,380
这里呢传入什么呢

138
00:03:49,380 --> 00:03:50,980
是不是传入我们的一个类型呢

139
00:03:50,980 --> 00:03:51,220
使君

140
00:03:51,220 --> 00:03:53,440
我们的title和我们的证文呢

141
00:03:53,440 --> 00:03:55,040
它们都是一个使君类型的

142
00:03:55,040 --> 00:03:55,980
好

143
00:03:55,980 --> 00:03:56,480
那么呢

144
00:03:56,480 --> 00:03:57,860
我们既然创建了一个schema之后

145
00:03:57,860 --> 00:03:59,240
我们此时是不是要调用

146
00:03:59,240 --> 00:04:01,280
调用我们mongos的model方法

147
00:04:01,280 --> 00:04:02,580
把它给创建一个类出来呀

148
00:04:02,580 --> 00:04:02,980
对吧

149
00:04:02,980 --> 00:04:04,720
那么首先这里呢

150
00:04:04,720 --> 00:04:06,540
我们创建了

151
00:04:06,540 --> 00:04:08,960
schema

152
00:04:08,960 --> 00:04:09,960
接下来呢

153
00:04:09,960 --> 00:04:10,860
我们就需要去

154
00:04:10,860 --> 00:04:13,040
咱们需要去创建一个

155
00:04:13,040 --> 00:04:15,340
model 它是一个类

156
00:04:15,340 --> 00:04:18,640
好 那么呢 这里呢 怎么去创建呢 首先我们去

157
00:04:18,640 --> 00:04:21,900
mongus.model

158
00:04:21,900 --> 00:04:27,480
咱们呢 给它一个名称叫做post 说明了我们在一个model的名称叫做post

159
00:04:27,480 --> 00:04:31,460
那么posts对应呢 是什么呀 是不是我们的表的名称呢post 对吧 好 那么呢

160
00:04:31,460 --> 00:04:35,800
它所对应的schema就是我们刚才所定义的这样一个schema 此时呢 我们把它给

161
00:04:35,800 --> 00:04:40,660
return出去 咱们在model里面 把我们在那一个类return出去

162
00:04:43,040 --> 00:04:48,920
那么这里

163
00:04:48,920 --> 00:04:50,360
那么这里为什么要把

164
00:04:50,360 --> 00:04:51,480
要把我们的

165
00:04:51,480 --> 00:04:53,380
mongos.model在那个lay return出去

166
00:04:53,380 --> 00:04:54,160
有的同学们可能会问

167
00:04:54,160 --> 00:04:55,460
我为什么不去retain一个schema

168
00:04:55,460 --> 00:04:56,440
其实这里呢

169
00:04:56,440 --> 00:04:58,120
我来给同学们去稍微解释一下

170
00:04:58,120 --> 00:05:00,580
因为我们的schema呢

171
00:05:00,580 --> 00:05:02,060
它是对咱们数据制造的一个描述

172
00:05:02,060 --> 00:05:02,740
那么model呢

173
00:05:02,740 --> 00:05:03,360
它其实是什么

174
00:05:03,360 --> 00:05:04,780
它是不是一个lay啊

175
00:05:04,780 --> 00:05:05,140
对吧

176
00:05:05,140 --> 00:05:06,060
那么我们retain出去

177
00:05:06,060 --> 00:05:06,740
其实大家注意

178
00:05:06,740 --> 00:05:07,280
这里呢

179
00:05:07,280 --> 00:05:08,500
可以提前给同学们去透露一下

180
00:05:08,500 --> 00:05:09,320
在context上面

181
00:05:09,320 --> 00:05:10,520
比如说

182
00:05:10,520 --> 00:05:11,120
比如说

183
00:05:11,120 --> 00:05:12,640
因为我们是不是之前写过插件呢

184
00:05:12,640 --> 00:05:13,420
你在Instant里面

185
00:05:13,420 --> 00:05:14,720
比如说你去写个context.js

186
00:05:14,720 --> 00:05:15,840
或者说application.js

187
00:05:15,840 --> 00:05:16,500
你就会在app

188
00:05:16,500 --> 00:05:18,280
或者咱们的context上面去扩展一些属性

189
00:05:18,280 --> 00:05:18,720
同样的

190
00:05:18,720 --> 00:05:21,040
我们现在是不是在model里面去

191
00:05:21,040 --> 00:05:22,000
咱们写一个js

192
00:05:22,000 --> 00:05:22,320
对吧

193
00:05:22,320 --> 00:05:23,140
posts.js

194
00:05:23,140 --> 00:05:24,140
那么说明什么问题

195
00:05:24,140 --> 00:05:24,940
大家猜一下

196
00:05:24,940 --> 00:05:26,100
是不是在我们的app

197
00:05:26,100 --> 00:05:27,380
或者说的context

198
00:05:27,380 --> 00:05:28,360
下面是不是一个model属性

199
00:05:28,360 --> 00:05:29,920
然后model.posts

200
00:05:29,920 --> 00:05:31,800
是不是就可以访问到我们这样一个实例

201
00:05:31,800 --> 00:05:33,160
如果说你return的是schema

202
00:05:33,160 --> 00:05:34,260
是不是它就可以访问到schema

203
00:05:34,260 --> 00:05:36,200
那么你如果说return一个mongos.model

204
00:05:36,200 --> 00:05:36,780
也就是model类

205
00:05:36,780 --> 00:05:38,980
那么你就可以去访问到一个类

206
00:05:38,980 --> 00:05:41,640
那么我们为什么去return model

207
00:05:41,640 --> 00:05:42,360
而不是schema呢

208
00:05:42,360 --> 00:05:44,020
其实大家可以注意到我们前面的一个文档

209
00:05:44,020 --> 00:05:45,100
老是把前面的文档给翻出来

210
00:05:45,100 --> 00:05:47,040
大家就可以去稍微可以去理解一下

211
00:05:47,040 --> 00:05:48,900
那么我们刚才我们前面的在讲

212
00:05:48,900 --> 00:05:50,360
咱们前面在讲

213
00:05:50,360 --> 00:05:51,860
咱们前面在讲蒙古斯的内容里面的时候

214
00:05:51,860 --> 00:05:53,840
咱们的一个大部分操作是对谁做操作

215
00:05:53,840 --> 00:05:55,840
比如说我们是不是创建了一个post

216
00:05:55,840 --> 00:05:57,200
咱们相当于return的是谁

217
00:05:57,200 --> 00:05:58,680
咱们相当于return的是谁

218
00:05:58,680 --> 00:06:00,820
是不是return的是他

219
00:06:00,820 --> 00:06:03,900
那么实际上我们的大部分操作是基于谁去操作的

220
00:06:03,900 --> 00:06:05,240
是不是基于post去操作的

221
00:06:05,240 --> 00:06:06,920
我们return的是咱们的model在那个类

222
00:06:06,920 --> 00:06:09,160
那么其实我们后面的增山改查

223
00:06:09,160 --> 00:06:10,980
其实都是基于咱们在一个类

224
00:06:12,360 --> 00:06:23,660
可以去查找然后remove可以刷除update的可以去更新我们的大部分的使用都是使用到我们的一个在那个类所以说我们把它给return出去那么这里呢也是我们的一个相当于是一种设计原则也是一种经验

225
00:06:23,660 --> 00:06:30,900
这里呢我来做一下笔记因为contact上面可以访问到module类那么在我们的业务里面

226
00:06:30,900 --> 00:06:38,600
会经常调用这个类的方法进行增上改查

227
00:06:38,600 --> 00:06:40,800
因为我们浮端的核心是不是就是增上改查

228
00:06:40,800 --> 00:06:43,800
好 那么到这里呢 其实我们的model就已经去

229
00:06:43,800 --> 00:06:45,100
咱们已经完成了

230
00:06:45,100 --> 00:06:48,200
那么在这里呢 我们其实不需要对model去在任何地方去进行引入

231
00:06:48,200 --> 00:06:51,300
因为它就跟咱们的一个exend和heap里面的内容是一样的

232
00:06:51,300 --> 00:06:53,400
它会在框架里面自动去帮我们去进行一个加载

233
00:06:53,400 --> 00:06:55,900
所以说我们不需要去做其他的事情就OK了

234
00:06:55,900 --> 00:06:58,100
所以在这里呢 还是给同学们去提示一下

235
00:06:58,100 --> 00:06:59,600
类似

236
00:06:59,600 --> 00:07:02,200
tennis里面的内容

237
00:07:02,200 --> 00:07:03,200
无需

238
00:07:03,200 --> 00:07:05,100
手动加载

239
00:07:05,100 --> 00:07:08,100
好 那么如果说同学们后面复习的时候呢

240
00:07:08,100 --> 00:07:11,100
其实可以多看一下老师在咱们代码里面的一些注释

241
00:07:11,100 --> 00:07:15,100
好 那么这里呢就是我们是不是已经完成了我们第二部的一个内容啊

242
00:07:15,100 --> 00:07:18,100
对吧 完成了我们编写schema以及设计我们存储的字段

243
00:07:18,100 --> 00:07:21,100
好 这里呢我们就来简单的来总结一下

244
00:07:21,100 --> 00:07:23,100
设计schema是不是很简单呢

245
00:07:23,100 --> 00:07:25,100
首先呢我们来思考一下我们数据库的字段名

246
00:07:25,100 --> 00:07:26,100
是什么是不是content和title

247
00:07:26,100 --> 00:07:31,100
然后呢是不是在我们的在我们的app的model下面创建一个posts.js

248
00:07:31,100 --> 00:07:33,100
那么它呢是不是对应我们的表叫做posts

249
00:07:33,100 --> 00:07:36,100
好 那么呢我们通过model.export是不是会返回一个方形

250
00:07:36,100 --> 00:07:42,400
方形然后会注入一个app那么app下面呢因为我们引入了咱们1G-mongos这样一个插件的原因咱们app下面是不是有mongos这样一个方法

251
00:07:42,400 --> 00:07:46,580
那么接下来呢我们是创建一个schemaschema里面是不是传入我们数据库设计的自断title

252
00:07:46,580 --> 00:07:46,840
 content

253
00:07:46,840 --> 00:07:49,900
好那么我们设计完咱们schema之后是不是生成我们model这样一个类

254
00:07:49,900 --> 00:07:54,060
那么为什么去retend咱们model这样一个类呢是不是因为我们业务中增山改查都是基于它的

255
00:07:54,060 --> 00:07:57,820
那么我们完成编写schema设计存储自断之后

256
00:07:57,820 --> 00:08:00,220
同学们思考下我们下一步应该干嘛呀

257
00:08:00,220 --> 00:08:02,220
是不是应该添加数据啊

258
00:08:02,600 --> 00:08:03,880
那么添加数据可以平衡添加吗

259
00:08:03,880 --> 00:08:04,400
不可以吧

260
00:08:04,400 --> 00:08:05,360
你是不是要通过路由

261
00:08:05,360 --> 00:08:07,060
那么路由会到哪里去呢

262
00:08:07,060 --> 00:08:07,660
是不是到控制器

263
00:08:07,660 --> 00:08:09,760
所以我们添加数据一定要经过控制器

264
00:08:09,760 --> 00:08:10,260
好

265
00:08:10,260 --> 00:08:11,020
那么就跟着老师

266
00:08:11,020 --> 00:08:12,160
咱们进入下一步的开发

