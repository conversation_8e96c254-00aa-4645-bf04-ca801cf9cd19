1
00:00:00,000 --> 00:00:01,760
我们现在的话呢

2
00:00:01,760 --> 00:00:04,440
就已经完成了对一个线面的一个渲染

3
00:00:04,440 --> 00:00:06,800
那么我们有了这个页面之后

4
00:00:06,800 --> 00:00:08,000
然后接下来我们来看一下

5
00:00:08,000 --> 00:00:09,500
如果说我们页面当中

6
00:00:09,500 --> 00:00:10,860
有一些静态资源的依赖

7
00:00:10,860 --> 00:00:12,060
什么意思呢

8
00:00:12,060 --> 00:00:13,520
例如我在当前这个页面当中

9
00:00:13,520 --> 00:00:15,580
来引入我们assize当中

10
00:00:15,580 --> 00:00:17,540
这个CSS里面的慢点CSS

11
00:00:17,540 --> 00:00:19,680
和js目录当中的慢点js

12
00:00:19,680 --> 00:00:21,840
也就是说页面当中会有一些静态资源

13
00:00:21,840 --> 00:00:23,140
那么现在我们来看一下

14
00:00:23,140 --> 00:00:24,820
我们的网页或者说浏览器

15
00:00:24,820 --> 00:00:27,200
能不能正确的来处理这些静态资源链接

16
00:00:27,200 --> 00:00:28,600
那么首先的话

17
00:00:28,600 --> 00:00:30,400
我们在这里来放入一个link标签

18
00:00:30,400 --> 00:00:32,080
我们让它来加载assense

19
00:00:32,080 --> 00:00:33,680
css里面的man.css

20
00:00:33,680 --> 00:00:34,800
那么同样的

21
00:00:34,800 --> 00:00:35,700
我们在最后的这个位置

22
00:00:35,700 --> 00:00:37,960
我们要放入一个script标签

23
00:00:37,960 --> 00:00:40,800
我们让它加载这里面的这个js文件

24
00:00:40,800 --> 00:00:42,240
我们把它写好以后

25
00:00:42,240 --> 00:00:43,880
然后接下来回到这个浏览器当中

26
00:00:43,880 --> 00:00:45,340
我们在这里来刷新一下

27
00:00:45,340 --> 00:00:46,680
刷新完之后

28
00:00:46,680 --> 00:00:47,940
那么此时大家就可以看到

29
00:00:47,940 --> 00:00:49,160
咦我们在这里

30
00:00:49,160 --> 00:00:51,680
这个man.css和man.js

31
00:00:51,680 --> 00:00:53,120
他们也渲染成功了

32
00:00:53,120 --> 00:00:55,400
但是呢我们并没有看到

33
00:00:55,400 --> 00:00:57,100
在我们这个css当中

34
00:00:57,100 --> 00:00:59,620
为这个HE标签设置的一个颜色

35
00:00:59,620 --> 00:01:00,220
但是没有

36
00:01:00,220 --> 00:01:01,920
包括我们在js当中

37
00:01:01,920 --> 00:01:02,680
我们写一句代码

38
00:01:02,680 --> 00:01:03,660
我们让它输出了Hello World

39
00:01:03,660 --> 00:01:05,420
那么此时我们还看到

40
00:01:05,420 --> 00:01:06,300
在控制台当中

41
00:01:06,300 --> 00:01:07,880
不仅没有打印出这个Hello World

42
00:01:07,880 --> 00:01:09,100
而且还看到报了一个错

43
00:01:09,100 --> 00:01:10,420
来自于慢点js

44
00:01:10,420 --> 00:01:12,300
那这个到底是什么原因呢

45
00:01:12,300 --> 00:01:14,100
我们可以通过这个网络请求

46
00:01:14,100 --> 00:01:15,900
我们首先来看一下慢点CSS

47
00:01:15,900 --> 00:01:17,780
我们可以看到它的response

48
00:01:17,780 --> 00:01:18,840
也就是说它的响应

49
00:01:18,840 --> 00:01:21,120
此时并不是我们所看到的

50
00:01:21,120 --> 00:01:23,080
这个慢点CSS里面的内容

51
00:01:23,080 --> 00:01:23,580
而是我们看到

52
00:01:23,580 --> 00:01:25,400
我们去请求慢点CSS的时候

53
00:01:25,400 --> 00:01:27,420
他想得到的是页面的内容

54
00:01:27,420 --> 00:01:30,080
也就是说当前这个页面的内容

55
00:01:30,080 --> 00:01:31,540
而我们去打开这个慢点js

56
00:01:31,540 --> 00:01:33,320
我们发现这个慢点js下回来的内容

57
00:01:33,320 --> 00:01:34,680
也是我们这个页面的内容

58
00:01:34,680 --> 00:01:36,600
这个到底是怎么回事呢

59
00:01:36,600 --> 00:01:37,300
原因呢

60
00:01:37,300 --> 00:01:38,020
其实非常的简单

61
00:01:38,020 --> 00:01:39,460
原因主要就是在于

62
00:01:39,460 --> 00:01:41,360
当我们来请求我们网页

63
00:01:41,360 --> 00:01:43,260
请求我们这个服务端的时候

64
00:01:43,260 --> 00:01:45,100
那么服务端首先

65
00:01:45,100 --> 00:01:46,620
服务端首先在这里呢

66
00:01:46,620 --> 00:01:47,400
读取这个文件

67
00:01:47,400 --> 00:01:48,240
然后想给了我们

68
00:01:48,240 --> 00:01:50,640
那么他这里读取这个indux.atmail文件

69
00:01:50,640 --> 00:01:51,940
注意还是那句话

70
00:01:51,940 --> 00:01:52,720
就是他不关心

71
00:01:52,720 --> 00:01:53,520
你请求的是杠

72
00:01:53,520 --> 00:01:54,100
还是杠A

73
00:01:54,100 --> 00:01:54,560
还是杠B

74
00:01:54,560 --> 00:01:55,000
还是杠C

75
00:01:55,000 --> 00:01:55,560
他不关心

76
00:01:55,560 --> 00:01:57,340
也就是说你发起的任何请求

77
00:01:57,340 --> 00:01:58,160
这里响应

78
00:01:58,160 --> 00:01:59,540
都是来给你响应

79
00:01:59,540 --> 00:02:01,240
我们这个index.atmr这个页面

80
00:02:01,240 --> 00:02:02,560
那么由此时

81
00:02:02,560 --> 00:02:03,380
我们现在呢

82
00:02:03,380 --> 00:02:04,260
对我们的服务端

83
00:02:04,260 --> 00:02:05,400
发起这样一个请求地址

84
00:02:05,400 --> 00:02:06,160
那么服务端在这里

85
00:02:06,160 --> 00:02:07,000
就读取这个文件

86
00:02:07,000 --> 00:02:07,840
然后响应到了客户端

87
00:02:07,840 --> 00:02:09,020
那么客户端此时呢

88
00:02:09,020 --> 00:02:10,240
就收到了这个页面

89
00:02:10,240 --> 00:02:11,920
能收到这个页面内容之后

90
00:02:11,920 --> 00:02:13,020
注意浏览器呢

91
00:02:13,020 --> 00:02:15,200
就要从上到下去依次解析

92
00:02:15,200 --> 00:02:16,400
那么在解析的过程当中

93
00:02:16,400 --> 00:02:18,100
如果说发泄这样一些

94
00:02:18,100 --> 00:02:19,600
具有外链的这些资源

95
00:02:19,600 --> 00:02:21,260
例如我们的link标签

96
00:02:21,260 --> 00:02:22,700
那么此时浏览器

97
00:02:22,700 --> 00:02:24,840
就会针对这个Link标签中的

98
00:02:24,840 --> 00:02:26,780
这个HARF去发起一次新的请求

99
00:02:26,780 --> 00:02:28,880
新的请求要来请求这个

100
00:02:28,880 --> 00:02:30,020
专门的这个CSS资源

101
00:02:30,020 --> 00:02:31,660
那么浏览器继续往下解析

102
00:02:31,660 --> 00:02:34,080
当浏览器解析到这个SQL的标签的时候

103
00:02:34,080 --> 00:02:36,280
那么又发现它是一个外链资源

104
00:02:36,280 --> 00:02:37,860
那么这个时候浏览器就针对它

105
00:02:37,860 --> 00:02:39,600
再去发起一次新的请求

106
00:02:39,600 --> 00:02:41,680
所以说这个时候我们大家就在这里可以看到

107
00:02:41,680 --> 00:02:43,580
我们明明在这里往的是这样一个路径

108
00:02:43,580 --> 00:02:46,120
但是实际上我们浏览器一共发出了三次请求

109
00:02:46,120 --> 00:02:48,780
原因是在第一次收到这个页面资源的时候

110
00:02:48,780 --> 00:02:50,180
浏览器在解析的过程当中

111
00:02:50,180 --> 00:02:52,060
会对页面中的这些外列资源

112
00:02:52,060 --> 00:02:53,020
发起新的请求

113
00:02:53,020 --> 00:02:53,940
所以你就能看到

114
00:02:53,940 --> 00:02:56,140
还有次请求叫做慢点CSS

115
00:02:56,140 --> 00:02:58,280
还有次请求叫做慢点js

116
00:02:58,280 --> 00:02:59,640
所以就是这样的

117
00:02:59,640 --> 00:03:00,440
而这两个请求

118
00:03:00,440 --> 00:03:01,520
到达我们服务端之后

119
00:03:01,520 --> 00:03:03,540
我们服务端没有经过任何判断处理

120
00:03:03,540 --> 00:03:06,120
而是直接读取index的ATMR页面

121
00:03:06,120 --> 00:03:07,000
然后发送给客户端

122
00:03:07,000 --> 00:03:07,960
所以说此时

123
00:03:07,960 --> 00:03:09,220
你请求慢点CSS

124
00:03:09,220 --> 00:03:10,700
你收到的也是ATMR自伏串

125
00:03:10,700 --> 00:03:11,700
你请求慢点js

126
00:03:11,700 --> 00:03:13,620
收到的也是我们ATMR页面自伏串

127
00:03:13,620 --> 00:03:14,760
所以就是这个原因

128
00:03:14,760 --> 00:03:16,580
那有朋友们问

129
00:03:16,580 --> 00:03:18,160
那这个时候我们应该怎么办呢

130
00:03:18,160 --> 00:03:18,560
注意

131
00:03:18,560 --> 00:03:20,500
其实页面中的任何资源

132
00:03:20,500 --> 00:03:21,400
他们要来请求的话

133
00:03:21,400 --> 00:03:22,020
大家可以看到

134
00:03:22,020 --> 00:03:23,220
他们是不是都有这个

135
00:03:23,220 --> 00:03:24,180
所谓的请求路径

136
00:03:24,180 --> 00:03:25,120
也就是说

137
00:03:25,120 --> 00:03:27,140
我们应该对这些资源的请求

138
00:03:27,140 --> 00:03:29,000
都做单独的判断

139
00:03:29,000 --> 00:03:29,540
也就是说

140
00:03:29,540 --> 00:03:30,820
例如如果他请求这首页

141
00:03:30,820 --> 00:03:32,160
那我们才给他渲染这个

142
00:03:32,160 --> 00:03:33,360
Indexing AT4页面

143
00:03:33,360 --> 00:03:35,100
如果说他请求的是这个

144
00:03:35,100 --> 00:03:36,520
-assize CSS 慢点 CSS

145
00:03:36,520 --> 00:03:37,780
那我们是不是应该

146
00:03:37,780 --> 00:03:39,420
就来读取CSS目录下的这个

147
00:03:39,420 --> 00:03:40,080
慢点 CSS

148
00:03:40,080 --> 00:03:41,300
来想象给这个客户端呀

149
00:03:41,300 --> 00:03:43,340
那么这个慢点 js也是同理

150
00:03:43,340 --> 00:03:44,580
所以说

151
00:03:44,580 --> 00:03:46,060
为了完成这样一个功能

152
00:03:46,060 --> 00:03:49,820
那接下来我们就来到我们的03asizeGS这个文件当中

153
00:03:49,820 --> 00:03:52,120
我们单独的来写一段来实现一下这个功能

154
00:03:52,120 --> 00:03:52,760
好

155
00:03:52,760 --> 00:03:53,500
那么这个时候呢

156
00:03:53,500 --> 00:03:54,200
我们只需要在这里

157
00:03:54,200 --> 00:03:56,600
还是我们要拿到它的请求路径

158
00:03:56,600 --> 00:03:58,060
也就是URL

159
00:03:58,060 --> 00:03:59,960
拿到它

160
00:03:59,960 --> 00:04:00,760
拿到它以后

161
00:04:00,760 --> 00:04:02,740
那么这个时候我们就来加一个简单的判断

162
00:04:02,740 --> 00:04:04,460
如果它等于杠

163
00:04:04,460 --> 00:04:07,260
那么这个时候我们强让它来执行这段单

164
00:04:07,260 --> 00:04:08,780
好

165
00:04:08,780 --> 00:04:10,480
这个就表示来渲染我们的手艺

166
00:04:10,480 --> 00:04:11,940
然后把下面这一段给它删掉

167
00:04:11,940 --> 00:04:12,280
没有用

168
00:04:12,280 --> 00:04:15,640
如果这个URL是什么呢

169
00:04:15,640 --> 00:04:16,700
例如我们这个叫做

170
00:04:16,700 --> 00:04:18,100
例如先来我们的css

171
00:04:18,100 --> 00:04:19,880
这个就是assess

172
00:04:19,880 --> 00:04:21,100
好 这个资源路径

173
00:04:21,100 --> 00:04:22,340
我们把它拿过来

174
00:04:22,340 --> 00:04:23,000
如果是它

175
00:04:23,000 --> 00:04:23,880
那么此时大家想一想

176
00:04:23,880 --> 00:04:24,580
我们是不是应该来

177
00:04:24,580 --> 00:04:27,080
应该来读取css里面的慢点css

178
00:04:27,080 --> 00:04:28,080
所以说这个时候

179
00:04:28,080 --> 00:04:29,080
我们把它拿过来

180
00:04:29,080 --> 00:04:29,880
招过来

181
00:04:29,880 --> 00:04:31,840
那么此时这个读取的文件路径

182
00:04:31,840 --> 00:04:34,540
那么它就应该是assess里面的css

183
00:04:34,540 --> 00:04:35,900
应该来读取这个文件

184
00:04:35,900 --> 00:04:37,580
好了 如果读取成功

185
00:04:37,580 --> 00:04:39,440
那么此时这个content type

186
00:04:39,440 --> 00:04:40,900
那就不是atmer了

187
00:04:40,900 --> 00:04:42,400
而是css

188
00:04:42,400 --> 00:04:43,240
那么这样写完以后

189
00:04:43,240 --> 00:04:44,800
注意我们对css资源的处理

190
00:04:44,800 --> 00:04:45,640
在这里就写好了

191
00:04:45,640 --> 00:04:46,800
好 消耗以后

192
00:04:46,800 --> 00:04:47,680
然后回到命令箱当中

193
00:04:47,680 --> 00:04:49,620
我们把这个03Assess这个脚本

194
00:04:49,620 --> 00:04:51,000
来给它启动起来

195
00:04:51,000 --> 00:04:53,120
好 回到命令箱当中

196
00:04:53,120 --> 00:04:55,340
我们首先给它把原来的关键

197
00:04:55,340 --> 00:04:57,840
然后node03Assess.js启动

198
00:04:57,840 --> 00:04:58,800
好 启动成功

199
00:04:58,800 --> 00:04:59,760
启动成功以后

200
00:04:59,760 --> 00:05:01,540
回到我们的浏览器当中

201
00:05:01,540 --> 00:05:03,380
好 此时我们再来刷新一下

202
00:05:03,380 --> 00:05:04,940
好 那么大家此时就发现

203
00:05:04,940 --> 00:05:06,460
我们现在是不是就已经

204
00:05:06,460 --> 00:05:08,060
这个样式是不是就注意好了

205
00:05:08,060 --> 00:05:09,520
我们打开这个慢点CSS

206
00:05:09,520 --> 00:05:11,080
我们看到此时就正确的收到了

207
00:05:11,080 --> 00:05:12,900
这个慢点CSS的一个想法结果

208
00:05:12,900 --> 00:05:15,000
但是此时对于这个慢点jsS

209
00:05:15,000 --> 00:05:15,900
他还没有任何内容

210
00:05:15,900 --> 00:05:17,600
我们还发现浏览器此时呢

211
00:05:17,600 --> 00:05:19,040
一直在在等待

212
00:05:19,040 --> 00:05:20,380
一直在加载当中

213
00:05:20,380 --> 00:05:22,980
原因是此时我们并没有任何代码

214
00:05:22,980 --> 00:05:24,800
去处理这个慢点js的一个请求

215
00:05:24,800 --> 00:05:26,220
所以说浏览器呢就一直在等待

216
00:05:26,220 --> 00:05:28,220
因为他此时收不到任何响应

217
00:05:28,220 --> 00:05:28,400
好

218
00:05:28,400 --> 00:05:29,260
那么现在这样的话

219
00:05:29,260 --> 00:05:30,760
我们就回到这个代码当中

220
00:05:30,760 --> 00:05:32,960
我们实际上只需要在这里

221
00:05:32,960 --> 00:05:34,560
我们再给他加一段

222
00:05:34,560 --> 00:05:37,960
也就是说如果这个url等于是assize

223
00:05:37,960 --> 00:05:41,780
gapGSgap慢点js

224
00:05:41,780 --> 00:05:42,320
好

225
00:05:42,320 --> 00:05:42,980
如果是这样的话

226
00:05:42,980 --> 00:05:43,860
我们把上面这段代码

227
00:05:43,860 --> 00:05:44,960
我们再次的给他拿下来

228
00:05:44,960 --> 00:05:46,020
拿下来之后

229
00:05:46,020 --> 00:05:47,840
那么此时我们就是要来读取

230
00:05:47,840 --> 00:05:49,620
GS目录下的慢点js了

231
00:05:49,620 --> 00:05:52,040
那么同样的这个text

232
00:05:52,040 --> 00:05:53,460
那么这个就不再是js

233
00:05:53,460 --> 00:05:55,620
那么应该是JavaScript

234
00:05:55,620 --> 00:05:57,960
好了

235
00:05:57,960 --> 00:05:59,040
那么这样写好以后

236
00:05:59,040 --> 00:06:00,840
然后接下来我们追

237
00:06:00,840 --> 00:06:02,280
我们刚才这个命令行们

238
00:06:02,280 --> 00:06:03,380
我们要使用Nordomob

239
00:06:03,380 --> 00:06:04,500
否则它不会动重启

240
00:06:04,500 --> 00:06:05,560
再来启动一下

241
00:06:05,560 --> 00:06:06,680
回到这段期当中

242
00:06:06,680 --> 00:06:07,180
好了

243
00:06:07,180 --> 00:06:08,260
那么此时我们就看到

244
00:06:08,260 --> 00:06:10,360
我们的CSS和js这两资源

245
00:06:10,360 --> 00:06:11,920
就都加载成功了

246
00:06:11,920 --> 00:06:12,620
加载成功以后

247
00:06:12,620 --> 00:06:13,660
我们看一下慢点js

248
00:06:13,660 --> 00:06:14,620
找到Response

249
00:06:14,620 --> 00:06:14,980
我们看到

250
00:06:14,980 --> 00:06:16,100
此时确实收到了

251
00:06:16,100 --> 00:06:17,820
这个文件正确的下降结果

252
00:06:17,820 --> 00:06:18,560
那么打开console

253
00:06:18,560 --> 00:06:19,980
我们就看到这里输出了

254
00:06:19,980 --> 00:06:21,220
那么就证明

255
00:06:21,220 --> 00:06:22,200
我们此时在这里呢

256
00:06:22,200 --> 00:06:23,700
就把这个静态资源

257
00:06:23,700 --> 00:06:25,740
在这里去分别给它处理好了

258
00:06:25,740 --> 00:06:27,960
那么这就是什么呢

259
00:06:27,960 --> 00:06:29,600
这就是我们如何来处理

260
00:06:29,600 --> 00:06:31,120
页面中的静态资源

261
00:06:31,120 --> 00:06:32,780
那我们最简单的方式呢

262
00:06:32,780 --> 00:06:36,220
就是一个一个的来进行单独处理

263
00:06:36,220 --> 00:06:37,400
那我们就在这里呢

264
00:06:37,400 --> 00:06:38,580
去判断它的请求路径

265
00:06:38,580 --> 00:06:40,700
如果说匹配到了

266
00:06:40,700 --> 00:06:41,940
那我们就把它对应的

267
00:06:41,940 --> 00:06:43,280
这个物理磁盘上的这个文件

268
00:06:43,280 --> 00:06:43,800
去给它读出来

269
00:06:43,800 --> 00:06:44,480
然后想给它

270
00:06:44,480 --> 00:06:45,640
好 当然最后

271
00:06:45,640 --> 00:06:47,900
如果说对于其他不存在的资源

272
00:06:47,900 --> 00:06:48,920
那这个时候呢

273
00:06:48,920 --> 00:06:49,700
我们可以这样

274
00:06:49,700 --> 00:06:50,580
我们再来一个else

275
00:06:50,580 --> 00:06:51,860
那么else的话

276
00:06:51,860 --> 00:06:53,140
那么它就理所当然的

277
00:06:53,140 --> 00:06:54,760
应该是应该是什么呢

278
00:06:54,760 --> 00:06:56,200
就是一个404

279
00:06:56,200 --> 00:06:56,580
对吧

280
00:06:56,580 --> 00:06:58,240
好 我们再来个404

281
00:06:58,240 --> 00:06:59,680
好 那这个content type呢

282
00:06:59,680 --> 00:07:00,320
就是0

283
00:07:00,320 --> 00:07:01,480
就是普通内容

284
00:07:01,480 --> 00:07:02,960
好了 然后我们给它想一个结果

285
00:07:02,960 --> 00:07:03,720
用404

286
00:07:03,720 --> 00:07:05,380
not fun

287
00:07:05,380 --> 00:07:07,120
好了 那么我们这样写好以后

288
00:07:07,120 --> 00:07:07,700
那么此时

289
00:07:07,700 --> 00:07:08,660
例如假如说

290
00:07:08,660 --> 00:07:09,660
你来到这个业命当中

291
00:07:09,660 --> 00:07:12,000
假设你来请求一个不存在的资源

292
00:07:12,000 --> 00:07:13,400
另一边来一个

293
00:07:13,400 --> 00:07:13,760
假如说

294
00:07:13,760 --> 00:07:14,920
有个a.js

295
00:07:14,920 --> 00:07:16,260
那么这个资源呢

296
00:07:16,260 --> 00:07:17,860
在我们这里当前根本就没有

297
00:07:17,860 --> 00:07:18,860
那么没有的话

298
00:07:18,860 --> 00:07:20,240
那么它对于这个资源的请求

299
00:07:20,240 --> 00:07:21,600
那么收到的就是一个404

300
00:07:21,600 --> 00:07:23,000
我们来看一下刷新一下

301
00:07:23,000 --> 00:07:24,140
那么此时大家就看到

302
00:07:24,140 --> 00:07:26,540
我们家里是不是确实收到了这个404

303
00:07:26,540 --> 00:07:28,540
没有找到对应的资源

304
00:07:28,540 --> 00:07:29,540
就是这样的

305
00:07:29,540 --> 00:07:30,060
那么这样的话

306
00:07:30,060 --> 00:07:31,640
我们就完成了对这个静态

307
00:07:31,640 --> 00:07:33,340
就是页面中静态资源的一个

308
00:07:33,340 --> 00:07:34,920
比较完整的一个处理

309
00:07:34,920 --> 00:07:36,880
虽然这个代码比较繁琐

310
00:07:36,880 --> 00:07:39,880
但是这个才是最基本的方式

311
00:07:39,880 --> 00:07:41,380
那么如何在这个

312
00:07:41,380 --> 00:07:42,720
如何让它就是

313
00:07:42,720 --> 00:07:44,140
其实这个大拿手语问题

314
00:07:44,140 --> 00:07:45,300
那么这个就是另一个问题

315
00:07:45,300 --> 00:07:47,440
我们放到后面再来单独来讲

