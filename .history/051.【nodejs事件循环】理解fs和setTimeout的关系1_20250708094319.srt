1
00:00:00,000 --> 00:00:05,240
好 那么我们这几个开始呢 要通过代码 去学习 乐阶是它里面的一个世界循环

2
00:00:05,240 --> 00:00:10,840
 到底是怎么样的 这几个我们主要讲解大码片段 一在讲解之前呢 有三个一篇老师需要带同学们去简单的回顾一下

3
00:00:10,840 --> 00:00:16,440
首先 帕塞里说这个方法 它会把路径 它解析为一个决定路径 比如说你点斜杠

4
00:00:16,440 --> 00:00:20,780
 或者点点斜杠 这样的路径呢 是相对的路径 这样会有什么问题啊 是不是我们解析问中

5
00:00:21,180 --> 00:00:22,900
我们在移植文件的时候是不是会有问题

6
00:00:22,900 --> 00:00:24,960
但是如果说你把它解决成绝对路径

7
00:00:24,960 --> 00:00:26,960
你这样一段件事不管贴到哪里它都能够去运行

8
00:00:26,960 --> 00:00:29,100
是非常稳定的一个方法

9
00:00:29,100 --> 00:00:30,600
提升咱们项目的一个稳定性

10
00:00:30,600 --> 00:00:33,100
Fs.Refail它是异不得去读取文件的全部内容

11
00:00:33,100 --> 00:00:34,740
之前同学们应该学过Fs模块

12
00:00:34,740 --> 00:00:37,940
怎么样去同步的读起了是不是Fs.RefailSync

13
00:00:37,940 --> 00:00:40,440
那么DIRNAME是做什么的呢

14
00:00:40,440 --> 00:00:43,260
它是不是NodeGNS里面相当于它提供了一个全局的变量

15
00:00:43,260 --> 00:00:45,900
它是不是总是指向你当前文件的

16
00:00:45,900 --> 00:00:46,900
文件夹的绝对路径呢

17
00:00:46,900 --> 00:00:48,260
同学们怎么样理解

18
00:00:50,140 --> 00:00:53,980
比如说我们现在有这样一个chapter如果说我们去打印

19
00:00:53,980 --> 00:00:57,820
.rlim他打印出来的是不是就是咱们

20
00:00:57,820 --> 00:01:00,640
系统上面的chapter

21
00:01:00,640 --> 00:01:04,480
这样一个文件讲不如对吧那么如果说我们去打印

22
00:01:04,480 --> 00:01:09,080
filing

23
00:01:09,080 --> 00:01:14,200
是不是就会打印出来咱们的chapter和什么

24
00:01:14,200 --> 00:01:19,580
api.js其实咱们通过API的名称就可以看出来他的作用是什么

25
00:01:19,840 --> 00:01:21,640
dir name 就是打印出你当前文件

26
00:01:21,640 --> 00:01:23,300
他所在的文件夹的绝对入境

27
00:01:23,300 --> 00:01:25,220
fail name 就是他这个文件

28
00:01:25,220 --> 00:01:26,240
他的绝对入境是什么

29
00:01:26,240 --> 00:01:27,260
这就是他们之间的区别

30
00:01:27,260 --> 00:01:29,560
好 那么我们继续

31
00:01:29,560 --> 00:01:31,360
这里呢 我们分析这样一段代码

32
00:01:31,360 --> 00:01:33,660
首先呢 引入 fs 模块和 pass

33
00:01:33,660 --> 00:01:35,460
这里呢 主要执行两个方法

34
00:01:35,460 --> 00:01:38,020
一个是 some async operation

35
00:01:38,020 --> 00:01:40,840
它是做什么呢 里面其实就是一个fs.readfail

36
00:01:40,840 --> 00:01:43,140
就是咱们去读取一段文件

37
00:01:43,140 --> 00:01:44,920
然后呢 去执行一个callback方法

38
00:01:44,920 --> 00:01:46,460
那么呢readfail他是义步的

39
00:01:46,460 --> 00:01:48,000
下面呢 会有一段定时器

40
00:01:48,000 --> 00:01:49,280
10毫秒之后去执行

41
00:01:49,840 --> 00:01:51,880
其实我们去分析事情循环

42
00:01:51,880 --> 00:01:52,640
是不是就是分析

43
00:01:52,640 --> 00:01:54,180
我们异部代码之间的关系

44
00:01:54,180 --> 00:01:56,080
事情循环是做什么的

45
00:01:56,080 --> 00:01:58,260
老师之前在讲解浏览器的事情循环里面

46
00:01:58,260 --> 00:01:58,580
是不是讲过

47
00:01:58,580 --> 00:02:00,120
它的核心是不是就是对异部

48
00:02:00,120 --> 00:02:01,180
毁掉的一些调度

49
00:02:01,180 --> 00:02:03,160
我们搞清楚了异部之间它是怎么调度的

50
00:02:03,160 --> 00:02:03,920
我们也就搞清楚了

51
00:02:03,920 --> 00:02:04,980
漏的JS里面事情循环的

52
00:02:04,980 --> 00:02:05,920
它们之间的一个关系

53
00:02:05,920 --> 00:02:07,580
因为如果说全部都是同步的代码

54
00:02:07,580 --> 00:02:08,380
那么我们还分析什么

55
00:02:08,380 --> 00:02:09,860
JS它复杂就复杂了

56
00:02:09,860 --> 00:02:11,920
是不是就复杂到全部都是异部的

57
00:02:11,920 --> 00:02:13,160
我们去搞不清楚它们之间关系

58
00:02:13,160 --> 00:02:14,500
所以我们才需要去使用世界循环

59
00:02:14,500 --> 00:02:14,840
对吧

60
00:02:14,840 --> 00:02:15,120
好

61
00:02:15,120 --> 00:02:15,480
那么呢

62
00:02:15,480 --> 00:02:15,880
这里呢

63
00:02:15,880 --> 00:02:17,960
老师就通过一幅图来带大家去了解一下

64
00:02:17,960 --> 00:02:19,480
我们去分析一下这样一段代码

65
00:02:19,480 --> 00:02:21,380
好

66
00:02:21,380 --> 00:02:21,740
首先

67
00:02:21,740 --> 00:02:22,920
这里呢

68
00:02:22,920 --> 00:02:24,460
是我们的代码

69
00:02:24,460 --> 00:02:24,900
这里呢

70
00:02:24,900 --> 00:02:26,140
是咱们对Pool的一个描述

71
00:02:26,140 --> 00:02:27,240
然后呢

72
00:02:27,240 --> 00:02:28,680
这里的它是三个核心阶段

73
00:02:28,680 --> 00:02:30,640
我们刚才是不是看到了

74
00:02:30,640 --> 00:02:32,540
我们只需要关注

75
00:02:32,540 --> 00:02:32,900
Timer

76
00:02:32,900 --> 00:02:34,080
IO和Pool

77
00:02:34,080 --> 00:02:34,600
其余的呢

78
00:02:34,600 --> 00:02:35,420
暂时不用去关注

79
00:02:35,420 --> 00:02:36,420
所以老师就没有去画出来

80
00:02:36,420 --> 00:02:37,300
好

81
00:02:37,300 --> 00:02:37,780
那咱们继续

82
00:02:37,780 --> 00:02:38,520
IO

83
00:02:38,520 --> 00:02:38,960
IO是什么

84
00:02:38,960 --> 00:02:40,560
IO是不是就是浏览器的一个线程

85
00:02:40,560 --> 00:02:41,300
去调度一些异步

86
00:02:41,300 --> 00:02:41,880
回调的

87
00:02:43,160 --> 00:02:46,720
这里呢

88
00:02:46,720 --> 00:02:49,060
这块区域就代表咱们的执行顺序

89
00:02:49,060 --> 00:02:49,600
老师等一下

90
00:02:49,600 --> 00:02:51,220
谁先执行就把谁先拖进来

91
00:02:51,220 --> 00:02:52,220
同学们可以一目两然看到

92
00:02:52,220 --> 00:02:53,440
然后这个黄色的块呢

93
00:02:53,440 --> 00:02:55,540
就代表我们当前执行的时间是什么

94
00:02:55,540 --> 00:02:56,560
比如说我在0毫秒

95
00:02:56,560 --> 00:02:57,480
咱们最开始执行的时候

96
00:02:57,480 --> 00:02:58,180
时间是不是0呢

97
00:02:58,180 --> 00:02:58,400
对吧

98
00:02:58,400 --> 00:02:58,620
好

99
00:02:58,620 --> 00:02:59,720
那我们就来执行看一下

100
00:02:59,720 --> 00:03:01,240
首先

101
00:03:01,240 --> 00:03:03,960
这样一些对变量的生命

102
00:03:03,960 --> 00:03:04,780
我们就不用去关注

103
00:03:04,780 --> 00:03:06,140
因为我们不用管他的执行顺序

104
00:03:06,140 --> 00:03:06,880
这是一目两然的

105
00:03:06,880 --> 00:03:07,280
同步的代码

106
00:03:07,280 --> 00:03:07,520
对吧

107
00:03:07,520 --> 00:03:07,680
好

108
00:03:07,680 --> 00:03:08,220
我们先来看一下

109
00:03:08,220 --> 00:03:10,180
首先第一行执行的是什么

110
00:03:10,180 --> 00:03:11,220
是不是有一个

111
00:03:11,220 --> 00:03:12,860
定时器啊

112
00:03:12,860 --> 00:03:13,360
10毫秒

113
00:03:13,360 --> 00:03:13,800
那么

114
00:03:13,800 --> 00:03:16,500
此时我们是不是需要把它丢掉咱们的IO里面去

115
00:03:16,500 --> 00:03:17,140
之前是不是讲过

116
00:03:17,140 --> 00:03:18,980
setmout它有个10毫秒的毁掉

117
00:03:18,980 --> 00:03:19,920
咱们就把它的毁掉

118
00:03:19,920 --> 00:03:22,020
丢掉咱们的IO里面去

119
00:03:22,020 --> 00:03:22,820
它需要等多久

120
00:03:22,820 --> 00:03:23,340
10毫秒

121
00:03:23,340 --> 00:03:23,540
对吧

122
00:03:23,540 --> 00:03:23,840
同学们

123
00:03:23,840 --> 00:03:24,040
好

124
00:03:24,040 --> 00:03:26,000
那咱们继续

125
00:03:26,000 --> 00:03:27,280
那么此时呢

126
00:03:27,280 --> 00:03:27,740
我们来看一下

127
00:03:27,740 --> 00:03:28,440
timer这里

128
00:03:28,440 --> 00:03:28,980
它有没有

129
00:03:28,980 --> 00:03:30,140
有没有

130
00:03:30,140 --> 00:03:32,400
callback会添加进去

131
00:03:32,400 --> 00:03:33,320
当前的实际室吗

132
00:03:33,320 --> 00:03:33,660
是0

133
00:03:33,660 --> 00:03:34,100
对吧

134
00:03:34,100 --> 00:03:34,940
那么咱们的timer

135
00:03:34,940 --> 00:03:36,780
timer它是去接收定时器的一些毁掉

136
00:03:36,780 --> 00:03:37,760
我们来看一下概念

137
00:03:37,760 --> 00:03:41,720
此阶段执行那些有timeout和setmout

138
00:03:41,720 --> 00:03:42,440
调度的回调函数

139
00:03:42,440 --> 00:03:44,920
那么咱们在当前时间为0毫秒的时候

140
00:03:44,920 --> 00:03:46,260
是不是

141
00:03:46,260 --> 00:03:48,440
timers 此时还没有任何callback加入啊

142
00:03:48,440 --> 00:03:49,260
所以咱们继续到IO

143
00:03:49,260 --> 00:03:49,760
IO也没有

144
00:03:49,760 --> 00:03:50,080
是吧

145
00:03:50,080 --> 00:03:50,880
IO是什么呀

146
00:03:50,880 --> 00:03:54,500
IO是不是以除了定时器和set immediate之外的回调

147
00:03:54,500 --> 00:03:55,640
没有回调加入

148
00:03:55,640 --> 00:03:55,980
对吧

149
00:03:55,980 --> 00:03:57,020
那么Paul呢

150
00:03:57,020 --> 00:03:57,720
Paul也没有

151
00:03:57,720 --> 00:03:58,580
对吧

152
00:03:58,580 --> 00:03:58,800
好

153
00:03:58,800 --> 00:03:59,900
那我们继续

154
00:03:59,900 --> 00:04:00,900
此时他会走到

155
00:04:00,900 --> 00:04:02,500
some think option

156
00:04:02,500 --> 00:04:03,820
那么some think option

157
00:04:03,820 --> 00:04:04,380
做了什么事情

158
00:04:04,380 --> 00:04:06,220
是不是他其实就是执行的FS.readfail

159
00:04:06,220 --> 00:04:07,720
他去读取一段文件

160
00:04:07,720 --> 00:04:09,180
读取一段文件

161
00:04:09,180 --> 00:04:10,660
然后他会有一个callback

162
00:04:10,660 --> 00:04:11,540
这个文件呢

163
00:04:11,540 --> 00:04:12,300
咱们先不用去滚

164
00:04:12,300 --> 00:04:14,620
这个文件是不是就是咱们

165
00:04:14,620 --> 00:04:17,440
咱们代码中的

166
00:04:17,440 --> 00:04:18,560
read.txt

167
00:04:18,560 --> 00:04:19,800
对吧

168
00:04:19,800 --> 00:04:20,560
这里呢

169
00:04:20,560 --> 00:04:22,360
咱们其实不用去关注它到底是什么

170
00:04:22,360 --> 00:04:23,800
因为这里不是重点

171
00:04:23,800 --> 00:04:25,480
好

172
00:04:25,480 --> 00:04:27,340
这个路径老是改一下

173
00:04:27,340 --> 00:04:30,680
行

174
00:04:30,680 --> 00:04:33,100
我们去读取read.txt

175
00:04:33,100 --> 00:04:34,160
会花费两毫秒

176
00:04:34,160 --> 00:04:35,820
也就是说两毫秒之后会执行个callback

177
00:04:35,820 --> 00:04:36,640
callback是谁呀

178
00:04:36,640 --> 00:04:37,660
callback是不是他呀

179
00:04:37,660 --> 00:04:39,860
也就是说咱们读取文件两毫秒之后会执行他

180
00:04:39,860 --> 00:04:40,580
所以呢

181
00:04:40,580 --> 00:04:43,580
fs的radfire回调 是不是你要加入咱们的Io里面去

182
00:04:43,580 --> 00:04:46,580
他的时间是多久啊 同学们 是不是2毫秒

183
00:04:46,580 --> 00:04:50,580
好 咱们刚才是不是已经走了Io callback了 再走了破破

184
00:04:50,580 --> 00:04:52,580
破 我们通过分析

185
00:04:52,580 --> 00:04:55,580
我们通过分析破

186
00:04:55,580 --> 00:04:58,580
此时在恰当的时候 load 它会阻塞在这个阶段

187
00:04:58,580 --> 00:05:00,580
那么此时呢

188
00:05:00,580 --> 00:05:03,580
我们是不是会阻塞在这个阶段呢破了这个阶段 对吧

189
00:05:03,580 --> 00:05:04,580
为什么呀

190
00:05:04,580 --> 00:05:08,580
因为我们是不是既有一个timer回调 还有一个fs的radfire 它那个回调

191
00:05:08,580 --> 00:05:09,520
他都没有返回结果

192
00:05:09,520 --> 00:05:10,240
所以说他

193
00:05:10,240 --> 00:05:11,860
所以说load的event

194
00:05:11,860 --> 00:05:13,880
一直会在pore这个阶段去等待

195
00:05:13,880 --> 00:05:15,180
谁先回来

196
00:05:15,180 --> 00:05:16,240
他就把谁先加入对面

197
00:05:16,240 --> 00:05:17,500
也就是说

198
00:05:17,500 --> 00:05:19,480
当前时间是0毫秒

199
00:05:19,480 --> 00:05:21,440
那么呢

200
00:05:21,440 --> 00:05:22,900
在0毫秒的时候

201
00:05:22,900 --> 00:05:23,700
咱们的eventNop

202
00:05:23,700 --> 00:05:24,500
已经走到pore这里

203
00:05:24,500 --> 00:05:25,420
这是第一轮时间循环

204
00:05:25,420 --> 00:05:26,320
注意听

205
00:05:26,320 --> 00:05:27,600
这是第一轮时间循环

206
00:05:27,600 --> 00:05:29,300
走到pore这里被阻塞了

207
00:05:29,300 --> 00:05:30,200
那么咱们的时间

208
00:05:30,200 --> 00:05:32,160
等他当他到2毫秒的时候

209
00:05:32,160 --> 00:05:33,720
我们的fs.readfail

210
00:05:33,720 --> 00:05:34,980
是不是已经读取完毕了

211
00:05:34,980 --> 00:05:36,480
那么在2毫秒的时候

212
00:05:36,480 --> 00:05:37,700
他是不是要加入了

213
00:05:37,700 --> 00:05:38,640
咱们的POR对面里面去

214
00:05:38,640 --> 00:05:39,240
同学们

215
00:05:39,240 --> 00:05:41,340
加入到POR对面里面去

216
00:05:41,340 --> 00:05:42,140
好

217
00:05:42,140 --> 00:05:43,880
那么加入到POR对面的时候

218
00:05:43,880 --> 00:05:44,780
会发生件什么事情

219
00:05:44,780 --> 00:05:48,880
如果代码没有设定

220
00:05:48,880 --> 00:05:50,200
SET IMMEDIATE EVEN NOBE

221
00:05:50,200 --> 00:05:51,140
将阻塞在该阶段

222
00:05:51,140 --> 00:05:52,280
等待callback加入

223
00:05:52,280 --> 00:05:53,060
POR CLEAN

224
00:05:53,060 --> 00:05:54,060
一旦到达一级执行

225
00:05:54,060 --> 00:05:54,860
什么意思

226
00:05:54,860 --> 00:05:57,200
咱们现在没有SET IMMEDIATE吧

227
00:05:57,200 --> 00:05:58,820
那么EVEN NOBE将阻塞在该阶段

228
00:05:58,820 --> 00:06:00,160
是不是等待FSREALFIRE回掉

229
00:06:00,160 --> 00:06:01,100
那么它已经执行了

230
00:06:01,100 --> 00:06:02,300
所以咱们加入POR去执行

231
00:06:02,300 --> 00:06:03,520
那么咱们来看一下

232
00:06:03,520 --> 00:06:04,320
REALFIRE它的回掉

233
00:06:04,320 --> 00:06:05,180
需要执行多长时间

234
00:06:05,180 --> 00:06:06,380
这里是不是有个Wire循环

235
00:06:06,380 --> 00:06:07,860
会去阻射20毫秒

236
00:06:07,860 --> 00:06:08,360
也就是说

237
00:06:08,360 --> 00:06:09,340
这样一个方法

238
00:06:09,340 --> 00:06:10,240
一旦到达立即执行

239
00:06:10,240 --> 00:06:10,860
它会执行多久

240
00:06:10,860 --> 00:06:13,160
是不是执行20毫秒

241
00:06:13,160 --> 00:06:14,720
一直到22毫秒

242
00:06:14,720 --> 00:06:15,100
对吧

243
00:06:15,100 --> 00:06:15,820
好

244
00:06:15,820 --> 00:06:17,900
那么到10毫秒的时候

245
00:06:17,900 --> 00:06:18,940
咱们的SETIMOUT能不能执行

246
00:06:18,940 --> 00:06:19,660
同学们

247
00:06:19,660 --> 00:06:21,380
不能执行吧

248
00:06:21,380 --> 00:06:21,540
为什么

249
00:06:21,540 --> 00:06:22,980
因为js它是单线程的

250
00:06:22,980 --> 00:06:24,120
你此时通过一个Wire循环

251
00:06:24,120 --> 00:06:24,960
把它给阻射住了

252
00:06:24,960 --> 00:06:26,200
一旦到达立即执行

253
00:06:26,200 --> 00:06:27,280
实际上SETIMOUT

254
00:06:27,280 --> 00:06:28,860
它这个时候是没有执行时间的

255
00:06:28,860 --> 00:06:30,460
所以说SETIMOUT一直被阻射

256
00:06:30,460 --> 00:06:31,520
那么也就是说

257
00:06:31,520 --> 00:06:34,180
当时间到达22毫秒之后

258
00:06:34,180 --> 00:06:35,240
咱们的fs real file

259
00:06:35,240 --> 00:06:36,880
它是不是已经执行完了

260
00:06:36,880 --> 00:06:37,480
对吧

261
00:06:37,480 --> 00:06:38,940
fs real file已经执行完毕

262
00:06:38,940 --> 00:06:40,720
那么咱们的set timeout回掉

263
00:06:40,720 --> 00:06:41,940
会在哪个阶段去执行呢

264
00:06:41,940 --> 00:06:42,660
同学们

265
00:06:42,660 --> 00:06:43,900
咱们来看这句话

266
00:06:43,900 --> 00:06:46,940
如果poorquia进入空

267
00:06:46,940 --> 00:06:48,060
进入空闲状态时

268
00:06:48,060 --> 00:06:49,500
咱们刚才

269
00:06:49,500 --> 00:06:51,980
咱们刚才执行fs real file的时候

270
00:06:51,980 --> 00:06:53,520
poorquia是不是不是空闲状态

271
00:06:53,520 --> 00:06:53,940
对吧

272
00:06:53,940 --> 00:06:54,360
好

273
00:06:54,360 --> 00:06:55,200
当它执行完成之后

274
00:06:55,200 --> 00:06:56,500
22号秒22号秒的时候

275
00:06:56,500 --> 00:06:58,480
它是不是poorquia进入了空闲状态

276
00:06:58,480 --> 00:06:58,840
对吧

277
00:06:58,840 --> 00:07:00,000
进入空闲状态时

278
00:07:00,000 --> 00:07:01,560
even knob将检查timers

279
00:07:01,560 --> 00:07:03,280
如果有一个或者多个timer的时间

280
00:07:03,280 --> 00:07:03,880
已经到达

281
00:07:04,180 --> 00:07:06,880
event nope将按顺序进入timer阶段并执行timer queen

282
00:07:06,880 --> 00:07:07,680
好

283
00:07:07,680 --> 00:07:08,800
那么呢

284
00:07:08,800 --> 00:07:11,760
我们的set timeout它的回调

285
00:07:11,760 --> 00:07:13,040
是不是早就已经到时间了

286
00:07:13,040 --> 00:07:14,100
10毫秒时间已经到时间了

287
00:07:14,100 --> 00:07:14,400
但是

288
00:07:14,400 --> 00:07:17,000
但是它是不是被fs.red fail

289
00:07:17,000 --> 00:07:17,980
都回到主射来

290
00:07:17,980 --> 00:07:19,940
所以说要等到22毫秒之后

291
00:07:19,940 --> 00:07:21,720
它才会加入到

292
00:07:21,720 --> 00:07:23,000
pore还是timer啊

293
00:07:23,000 --> 00:07:23,240
同学们

294
00:07:23,240 --> 00:07:23,920
这里是不是有一句话

295
00:07:23,920 --> 00:07:26,300
如果有一个或者多个timer程已经到达

296
00:07:26,300 --> 00:07:28,080
event nope将按顺序进入timer阶段

297
00:07:28,080 --> 00:07:28,460
什么意思

298
00:07:28,460 --> 00:07:29,000
也就是说

299
00:07:29,000 --> 00:07:30,040
它将会跳过pore

300
00:07:30,040 --> 00:07:31,400
此时呢进入第二轮世界循环

301
00:07:31,400 --> 00:07:33,100
然后开始从timer走

302
00:07:33,100 --> 00:07:34,220
好

303
00:07:34,220 --> 00:07:34,880
走

304
00:07:34,880 --> 00:07:35,540
走到Time这里

305
00:07:35,540 --> 00:07:36,500
对吧

306
00:07:36,500 --> 00:07:38,000
那么咱们的Timeout就已经开始执行了

307
00:07:38,000 --> 00:07:39,120
它的回调是什么时候

308
00:07:39,120 --> 00:07:39,880
22毫秒

309
00:07:39,880 --> 00:07:40,380
执行完毕

310
00:07:40,380 --> 00:07:41,420
咱们丢进去

311
00:07:41,420 --> 00:07:42,700
那么它的执行顺序是什么

312
00:07:42,700 --> 00:07:45,380
IFS Redfail它的callback会在这里去执行

313
00:07:45,380 --> 00:07:47,360
执行到22毫秒

314
00:07:47,360 --> 00:07:48,560
22毫秒之后

315
00:07:48,560 --> 00:07:50,280
Timeout它才会去执行

316
00:07:50,280 --> 00:07:52,280
那么这就是咱们刚才执行的一个过程

317
00:07:52,280 --> 00:07:55,540
那么我们可以把它打赢出来

318
00:07:55,540 --> 00:07:56,860
看一下到底是不这么回事

319
00:07:56,860 --> 00:07:57,440
好

320
00:07:57,440 --> 00:07:58,380
老师这里就把它打赢出来

321
00:07:58,380 --> 00:07:59,800
load

322
00:07:59,800 --> 00:08:07,000
demo1.ts 我们来看一下

323
00:08:07,000 --> 00:08:11,000
settimeout 22毫秒 has passed since I was statute 什么意思

324
00:08:11,000 --> 00:08:13,500
就是说settimeout在22毫秒之后才会被调度

325
00:08:13,500 --> 00:08:17,100
那么fairread他的callback 其实两毫秒之后就已经执行了

326
00:08:17,100 --> 00:08:21,500
好 我们一起来回顾一下刚才执行的过程

327
00:08:21,500 --> 00:08:26,600
首先settimeout 10毫秒之后执行是不是丢入咱们的i欧里面去

328
00:08:26,600 --> 00:08:27,100
对吧 同学们

329
00:08:27,100 --> 00:08:28,100
那么呢

330
00:08:28,800 --> 00:08:29,500
同时呢

331
00:08:29,500 --> 00:08:31,800
非要的他也开始去读取异步的去读取文件

332
00:08:31,800 --> 00:08:32,640
那么在两毫秒之内

333
00:08:32,640 --> 00:08:33,960
他是不是也在我里面去等待

334
00:08:33,960 --> 00:08:35,400
当两毫秒之后

335
00:08:35,400 --> 00:08:36,740
咱们的fs 热的发现回家

336
00:08:36,740 --> 00:08:37,560
是不是需要执行呢

337
00:08:37,560 --> 00:08:38,640
普通困为空时

338
00:08:38,640 --> 00:08:39,940
他将被阻塞在这里对吧

339
00:08:39,940 --> 00:08:41,200
被阻塞在这里

340
00:08:41,200 --> 00:08:41,900
将被阻塞

341
00:08:41,900 --> 00:08:44,160
那么呢就等待fs.readfail加入

342
00:08:44,160 --> 00:08:45,060
好

343
00:08:45,060 --> 00:08:46,500
那么呢当他执行完了之后

344
00:08:46,500 --> 00:08:47,000
咱们的破坏

345
00:08:47,000 --> 00:08:47,940
是不是进入了空闲状态啊

346
00:08:47,940 --> 00:08:49,140
同学们进入空闲状态时

347
00:08:49,140 --> 00:08:50,360
一般的我将检查timer

348
00:08:50,360 --> 00:08:51,000
检查谁是不检查

349
00:08:51,000 --> 00:08:51,840
咱们是他们out

350
00:08:51,940 --> 00:08:54,020
所以呢咱们的f162file执行完

351
00:08:54,020 --> 00:08:54,960
time 进去

352
00:08:54,960 --> 00:08:56,640
此时呢已经到了二扫好面

353
00:08:56,640 --> 00:08:59,480
这里呢就是咱们刚才这段代表的整个执行过程

354
00:08:59,480 --> 00:09:00,640
同学们可以去理解一下

355
00:09:00,640 --> 00:09:02,020
这里呢就是本节课的内容

