1
00:00:00,000 --> 00:00:03,240
好 这里呢 我们就来看一下 NextTig 它的应用场景是什么

2
00:00:03,240 --> 00:00:04,880
刚才我们是不是说了

3
00:00:04,880 --> 00:00:07,800
Processed NextTig 它不在EventLoop的任何阶段去执行

4
00:00:07,800 --> 00:00:09,480
那么我们为什么要去设计它

5
00:00:09,480 --> 00:00:13,200
它有什么目的 或者说有什么意义 那我们就来看一下

6
00:00:13,200 --> 00:00:17,400
首先 在多个事件里 交叉执行CPU密集型的计算任务

7
00:00:17,400 --> 00:00:19,760
首先咱们来看这样一段代码

8
00:00:19,760 --> 00:00:22,680
那么在接下来的讲解过程中呢 老师会封为

9
00:00:22,680 --> 00:00:24,680
会讲它三个应用场景

10
00:00:24,680 --> 00:00:26,880
但是呢 这里呢 我就不会去写代码了

11
00:00:26,880 --> 00:00:29,600
老师会带同学去看 去感受它的应用场景是什么

12
00:00:29,600 --> 00:00:30,880
因为这些代码它的

13
00:00:30,880 --> 00:00:33,440
它的demo不是很好写用场景不是很好实现

14
00:00:33,440 --> 00:00:33,960
所以说呢

15
00:00:33,960 --> 00:00:35,480
同学们只需要知道概念就可以了

16
00:00:35,480 --> 00:00:38,300
剩下的内容可以在实际开放中去自己去理解去领悟

17
00:00:38,300 --> 00:00:40,100
好 那咱们来看一下

18
00:00:40,100 --> 00:00:41,120
首先

19
00:00:41,120 --> 00:00:43,680
在多个世界里交叉执行CPU运行

20
00:00:43,680 --> 00:00:45,720
运算密集的任务

21
00:00:45,720 --> 00:00:47,000
什么是运算密集任务啊

22
00:00:47,000 --> 00:00:47,780
是不是就是它

23
00:00:47,780 --> 00:00:49,560
比如说咱们有一个计算的函数一直会执行

24
00:00:49,560 --> 00:00:50,600
一直卡在那里

25
00:00:50,600 --> 00:00:52,900
实际上它就是CPU密集型的一些任务

26
00:00:52,900 --> 00:00:55,460
什么意思

27
00:00:55,460 --> 00:00:56,220
比如说

28
00:00:56,220 --> 00:00:59,560
咱们在http去create一个server的时候

29
00:00:59,600 --> 00:01:01,520
就咱们去提一个http服务

30
00:01:01,520 --> 00:01:02,420
会去监听

31
00:01:02,420 --> 00:01:04,580
http的一些服务

32
00:01:04,580 --> 00:01:05,540
咱们监听5000端口

33
00:01:05,540 --> 00:01:08,120
比如说用户有一些发起一些request的请求

34
00:01:08,120 --> 00:01:09,200
那么服务器就会去响应

35
00:01:09,200 --> 00:01:09,960
发出了response

36
00:01:09,960 --> 00:01:11,640
那么发出response的时候

37
00:01:11,640 --> 00:01:14,220
可能你这个同时还需要去做什么呢

38
00:01:14,220 --> 00:01:16,320
你需要去做一些计算的工作

39
00:01:16,320 --> 00:01:19,020
比如说你需要去处理一段很复杂的数据

40
00:01:19,020 --> 00:01:21,520
你就可以通过process the next tick的方式

41
00:01:21,520 --> 00:01:24,740
去在试镜循环的间隙去计算它们

42
00:01:24,740 --> 00:01:25,800
如果说有了http

43
00:01:25,800 --> 00:01:26,900
请求过来

44
00:01:26,900 --> 00:01:29,140
然后去优先请求用户的响应

45
00:01:29,140 --> 00:01:30,800
如果说HTTP现在是空闲的

46
00:01:30,800 --> 00:01:31,780
那么他们就会去计算

47
00:01:31,780 --> 00:01:32,600
那么假如说

48
00:01:32,600 --> 00:01:35,240
假如说没有NestTik会怎么样啊

49
00:01:35,240 --> 00:01:35,800
同学们

50
00:01:35,800 --> 00:01:38,220
假如说我们把这段代码给注视掉

51
00:01:38,220 --> 00:01:40,300
假如说把这段代码给注视掉

52
00:01:40,300 --> 00:01:43,140
直接这么去执行

53
00:01:43,140 --> 00:01:44,800
这样会造成一个什么结果

54
00:01:44,800 --> 00:01:46,500
是不是我们的代码会一直卡在这里

55
00:01:46,500 --> 00:01:48,400
CPU的时间一直耗在computer这里

56
00:01:48,400 --> 00:01:50,660
那么HTTP它就不能够想用户的行为了

57
00:01:50,660 --> 00:01:51,600
不能想用户的请求

58
00:01:51,600 --> 00:01:53,140
所以说呢才需要computer

59
00:01:53,140 --> 00:01:56,840
咱们呢可以通过NestTik去解决这个问题

60
00:01:56,840 --> 00:01:58,740
那么这里呢

61
00:01:58,740 --> 00:02:00,400
也是刚才老师所讲解的一个内容

62
00:02:00,400 --> 00:02:01,560
朋友们可以自己去看一下

63
00:02:01,560 --> 00:02:01,860
好

64
00:02:01,860 --> 00:02:03,380
那么咱们来看一下第二个应用场景是什么

65
00:02:03,380 --> 00:02:05,760
它可以去保持毁掉函数

66
00:02:05,760 --> 00:02:06,620
逆步执行的原则

67
00:02:06,620 --> 00:02:07,280
什么意思

68
00:02:07,280 --> 00:02:10,020
比如说我们有这样的一段代码

69
00:02:10,020 --> 00:02:12,920
我们自己写了一个callback

70
00:02:12,920 --> 00:02:14,420
if data等于什么什么

71
00:02:14,420 --> 00:02:15,120
咱们叫callback

72
00:02:15,120 --> 00:02:16,180
is callback什么

73
00:02:16,180 --> 00:02:18,060
同学们来看一下

74
00:02:18,060 --> 00:02:19,020
asyncfuck

75
00:02:19,020 --> 00:02:20,840
它是一段同步还是eep的代码呀

76
00:02:20,840 --> 00:02:23,220
这是不是一段同步的代码呀

77
00:02:23,220 --> 00:02:23,800
同学们

78
00:02:23,800 --> 00:02:25,820
对吧

79
00:02:25,820 --> 00:02:28,380
比如说我执行到eve 然后直接callback他

80
00:02:28,380 --> 00:02:30,680
也就是说把执行权交给了

81
00:02:30,680 --> 00:02:31,960
在一个方形result

82
00:02:31,960 --> 00:02:35,040
他是一个同步的 那么会有什么问题 我们来看一下他造成的问题是什么

83
00:02:35,040 --> 00:02:38,360
比如说我们net.collect.net是什么 是不是load里面的网络模块

84
00:02:38,360 --> 00:02:40,660
比如说我们要去写一个socket 或者怎么样就用net模块

85
00:02:40,660 --> 00:02:43,740
好 咱们去打印一个什么的时候client.write

86
00:02:43,740 --> 00:02:48,100
那假如说在一个方形它的callback是同步的 会发生会引起什么问题啊

87
00:02:48,100 --> 00:02:52,440
是不是咱们collect在一个变量还没有出花 咱们就在client上面去write

88
00:02:52,960 --> 00:02:54,080
这样是不是会造成一个问题

89
00:02:54,080 --> 00:02:56,720
会爆出client is not defined

90
00:02:56,720 --> 00:02:57,320
对吧

91
00:02:57,320 --> 00:02:59,020
为什么因为方形它是同步的

92
00:02:59,020 --> 00:03:00,160
你去给它复制的时候

93
00:03:00,160 --> 00:03:01,700
你复制的函数都没有执行完

94
00:03:01,700 --> 00:03:04,900
你在里面又去掉下方法是不是不可以啊

95
00:03:04,900 --> 00:03:05,400
需要怎么做

96
00:03:05,400 --> 00:03:07,160
是不是我们需要把方形载个毁掉

97
00:03:07,160 --> 00:03:07,900
变成异步了

98
00:03:07,900 --> 00:03:08,520
怎么去做呢

99
00:03:08,520 --> 00:03:09,700
通过 next tick

100
00:03:09,700 --> 00:03:10,560
咱们把这个callback

101
00:03:10,560 --> 00:03:12,820
通过 process and next tick包装一次就可以了

102
00:03:12,820 --> 00:03:15,620
这样就完美的解决这个问题

103
00:03:15,620 --> 00:03:16,720
这是第二种场景

104
00:03:16,720 --> 00:03:18,520
那咱们看一下第三个场景

105
00:03:18,520 --> 00:03:19,960
使用在世间的触发过程中

106
00:03:19,960 --> 00:03:21,900
其实和第二种情况有点类似

107
00:03:21,900 --> 00:03:22,800
那么它的区别在哪里

108
00:03:22,800 --> 00:03:28,740
比如说,比如说我们在使用nodegns里面event emit方法的时候,它呢,event模块它是nodegns内置的一个模块

109
00:03:28,740 --> 00:03:31,200
不知道同学们之前在学习node的时候里面使用过这样一个方法

110
00:03:31,200 --> 00:03:34,560
这里老师来给同学们来温习一下,没有学过也没关系,它很简单

111
00:03:34,560 --> 00:03:37,180
它主要有两个核心的方法,get on,也是 emit

112
00:03:37,180 --> 00:03:41,160
它是node自带的订阅发布模式,同学们之前在学习gns内容学习

113
00:03:41,160 --> 00:03:44,900
设计模式的时候应该已经学习过,什么是发布,什么是订阅,那我们来看一下

114
00:03:44,900 --> 00:03:49,140
这里老师已经把代码写好了

115
00:03:49,140 --> 00:03:51,640
比如说我们先把event emit给requare进来

116
00:03:51,640 --> 00:03:53,560
首先呢咱们去定一个app的类去继承它

117
00:03:53,560 --> 00:03:54,500
咱们lue它

118
00:03:54,500 --> 00:03:56,660
那么呢app上面就有了两个方法

119
00:03:56,660 --> 00:03:57,560
一个是on 一个是emit

120
00:03:57,560 --> 00:03:58,240
on是什么呀

121
00:03:58,240 --> 00:03:58,620
同学们

122
00:03:58,620 --> 00:03:59,580
on是不是订阅

123
00:03:59,580 --> 00:04:01,020
咱们订阅了一个start

124
00:04:01,020 --> 00:04:01,860
这样的事件

125
00:04:01,860 --> 00:04:03,560
然后呢通过emit去触发它

126
00:04:03,560 --> 00:04:04,780
on

127
00:04:04,780 --> 00:04:06,020
订阅

128
00:04:06,020 --> 00:04:08,160
emit

129
00:04:08,160 --> 00:04:08,980
触发

130
00:04:08,980 --> 00:04:10,340
咱们的订阅了start事件

131
00:04:10,340 --> 00:04:10,800
然后呢

132
00:04:10,800 --> 00:04:12,520
通过emit去执行它

133
00:04:12,520 --> 00:04:13,220
打印一个start

134
00:04:13,220 --> 00:04:14,140
咱们来看一下

135
00:04:14,140 --> 00:04:15,540
效果

136
00:04:15,540 --> 00:04:18,500
清楚一下

137
00:04:18,500 --> 00:04:19,960
好

138
00:04:19,960 --> 00:04:20,800
start执行

139
00:04:20,800 --> 00:04:25,600
那么他阿密的在一个方法是同步的还是异步的呢

140
00:04:25,600 --> 00:04:32,300
康舟点咱们只需要关注康舟点到个111他实在是大的前面后面执行就知道他是一个同步的是异部的的密的对吧

141
00:04:32,300 --> 00:04:36,020
咱们来看一下结果康舟点到个111在后面对吧说明什么问题啊

142
00:04:36,020 --> 00:04:38,820
说明什么问题啊是不是说明咱们的

143
00:04:38,820 --> 00:04:42,160
阿密他他是一个同步的方法呀

144
00:04:42,160 --> 00:04:44,780
阿密是同步的方法

145
00:04:44,780 --> 00:04:49,420
同学们怕不怕他是一个异步的方法是不是很怕因为如果说

146
00:04:49,960 --> 00:04:55,780
nodegs 里面他的event 里面的emmit 也就是发布他是一个eep的 咱们是不是又要去通过试验去分析他好显他是同步的

147
00:04:55,780 --> 00:04:57,720
那么具体这里为什么是同步的

148
00:04:57,720 --> 00:04:59,780
其实老师可以跟同学们简单的去讲解一下

149
00:04:59,780 --> 00:05:08,140
为什么呢 因为咱们的emmit 如果是eep的 其实是很不好控制的 因为有时候我们就是需要去同步的去触发

150
00:05:08,140 --> 00:05:12,160
去发布一个事件 但是如果说你默认是eep的会造成一些很严重的问题 而且呢

151
00:05:12,160 --> 00:05:13,360
 如果说我们真要去emmit

152
00:05:13,360 --> 00:05:18,880
一个事件 我们是不是可以通过很多方式去eep的emmit 比如说 刚才咱们是不是讲了三种eep的方式

153
00:05:19,300 --> 00:05:21,180
sad time out

154
00:05:21,180 --> 00:05:22,440
sad immediate

155
00:05:22,440 --> 00:05:23,140
还有呢

156
00:05:23,140 --> 00:05:24,300
some next tick

157
00:05:24,300 --> 00:05:28,460
对吧

158
00:05:28,460 --> 00:05:29,660
所以说咱们的emit

159
00:05:29,660 --> 00:05:31,260
它不需要是eep同步的就够了

160
00:05:31,260 --> 00:05:32,560
如果说要eep你可以通过去

161
00:05:32,560 --> 00:05:34,260
通过eep的API去包装它

162
00:05:34,260 --> 00:05:37,260
好 这里呢就是eep的emit一个简单的介绍

163
00:05:37,260 --> 00:05:39,140
那咱们继续回来刚才的话题

164
00:05:39,140 --> 00:05:41,640
比如说我们再去

165
00:05:41,640 --> 00:05:44,260
咱们比如说现在咱们有一个方形是gene library

166
00:05:44,260 --> 00:05:47,260
它在一个方法里面直接去emit的一个start这样一个事件

167
00:05:47,260 --> 00:05:49,140
咱们在lue它的时候去执行on

168
00:05:49,560 --> 00:05:51,100
这样会不会造成什么问题啊同学们

169
00:05:51,100 --> 00:05:52,620
有什么问题呢

170
00:05:52,620 --> 00:05:55,440
咱们在lue它的时候是不是直接就执行了meat呀

171
00:05:55,440 --> 00:05:57,740
咱们在lue它的时候就已经了meat了

172
00:05:57,740 --> 00:06:00,300
也就是说咱们直接发布了但是呢此时还订阅了没有

173
00:06:00,300 --> 00:06:02,100
是不是还没有订阅啊那么造成一个什么问题

174
00:06:02,100 --> 00:06:03,640
是不是concel.log它不会执行了

175
00:06:03,640 --> 00:06:04,400
同学们

176
00:06:04,400 --> 00:06:04,920
对吧

177
00:06:04,920 --> 00:06:06,200
那么怎么去解决呢

178
00:06:06,200 --> 00:06:08,760
通过process the next tick咱们去一步的

179
00:06:08,760 --> 00:06:11,580
咱们去一步的

180
00:06:11,580 --> 00:06:12,340
去发布

181
00:06:12,340 --> 00:06:13,620
咱们在lue它的时候

182
00:06:13,620 --> 00:06:14,900
去监听它的start事件

183
00:06:14,900 --> 00:06:16,180
一步的去

184
00:06:16,180 --> 00:06:17,980
一步的去发布是不是就解决了这个问题

185
00:06:18,480 --> 00:06:24,350
这里呢 同学们可以好好地去琢磨 理由一下 好 这里呢 就是nestek它的三种应用场景

186
00:06:24,350 --> 00:06:26,020
 这里呢 老师带同学们来回顾一下

187
00:06:26,020 --> 00:06:31,220
首先呢 在多个事件交叉执行cpu密集型任务 比如说 你在

188
00:06:31,220 --> 00:06:36,200
写个http服务器的时候 你想利用空闲时间去做一些运算工作 你就可以去使用nestek

189
00:06:36,200 --> 00:06:40,720
第二个呢 保持回调函数 逆步执行的原则 什么意思

190
00:06:40,720 --> 00:06:45,800
也就是说 咱们去写些callback的时候 但是呢 又需要在他的callback里面去使用

191
00:06:46,300 --> 00:06:50,000
在一个变量 比如说client 如果是同步的情况下 咱们client 只是安定发应的

192
00:06:50,000 --> 00:06:51,100
 对吧 但是如果说我们

193
00:06:51,100 --> 00:06:56,200
把connect callback通过 next tick去包装一下 是不是解决这个问题 也就是说把咱们callback永远变成一部

194
00:06:56,200 --> 00:06:59,700
这就解决这个问题 这也是 next tick的其中作用之一 咱们来看一下第三种情况

195
00:06:59,700 --> 00:07:06,940
meat 咱们是不是在发布一个事件之前 是不是先去订阅他 对吧 那么这种情况就是咱们

196
00:07:06,940 --> 00:07:09,400
都还没订阅就已经发布了 是不是就会导致

197
00:07:09,400 --> 00:07:14,400
是不是就导致咱们的concelnog不会去执行 怎么去解决呢 是不是咱们

198
00:07:15,000 --> 00:07:17,800
在一个异步的方法里面去包装一下

199
00:07:17,800 --> 00:07:18,660
再去发布他

200
00:07:18,660 --> 00:07:20,760
也就说让订阅在发布之前

201
00:07:20,760 --> 00:07:24,640
让保证

202
00:07:24,640 --> 00:07:28,340
订阅永远在发布之前

203
00:07:28,340 --> 00:07:30,740
好这里呢就是这节课内容

