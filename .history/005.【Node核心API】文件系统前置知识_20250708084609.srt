1
00:00:00,000 --> 00:00:02,040
好 接下来我们来看一下FS模块

2
00:00:02,040 --> 00:00:05,120
平时有写过路的JS的同学肯定对它不陌生

3
00:00:05,120 --> 00:00:07,940
因为我们也会有非常多的场景都需要去使用到

4
00:00:07,940 --> 00:00:11,260
那么FS的文件系统顾名思义就是对我们的文件进行一些操作

5
00:00:11,260 --> 00:00:14,340
比如说写入 读取 删除 创建目录等等

6
00:00:14,340 --> 00:00:17,920
凡是涉及对咱们文件的操作都可以去使用这样的一个模块

7
00:00:17,920 --> 00:00:20,480
我们来看一下它有多少个方法

8
00:00:20,480 --> 00:00:24,320
是不是非常多呀 看起来也非常的可怕

9
00:00:24,320 --> 00:00:26,620
不过没关系

10
00:00:26,620 --> 00:00:29,180
那么对于FS模块来学习呢 我们就

11
00:00:29,180 --> 00:00:33,020
咱们就不去使用logs的一个文档了 因为它呢 也非常的多 也没有重点

12
00:00:33,020 --> 00:00:35,320
所以这里呢 我们直接来看咱们一个讲义

13
00:00:35,320 --> 00:00:37,380
好 在logs中

14
00:00:37,380 --> 00:00:41,220
所有的文件操作都是通过咱们在一个模块去实现的 然后它可以去创建

15
00:00:41,220 --> 00:00:41,460
 删除

16
00:00:41,460 --> 00:00:45,300
以及查询我们文件的一些状态 以及对咱们文件进行一些操作

17
00:00:45,300 --> 00:00:49,140
好 那么我们在学习FS模块之前呢 我们需要去了解一些前置知识

18
00:00:49,140 --> 00:00:50,680
比如说文件的权限位

19
00:00:50,680 --> 00:00:53,500
包括了标志位 以及文件的描述符FD

20
00:00:53,500 --> 00:00:56,320
好 首先我们来看一下文件的一个权限位MOD

21
00:00:56,620 --> 00:01:00,620
因为FS模块需要对文件进行操作 会涉及到操作权限的问题

22
00:01:00,620 --> 00:01:03,620
所以我们先要搞清楚什么是文件权限

23
00:01:03,620 --> 00:01:06,620
而且它都有哪些权限 我们一起来看一下

24
00:01:06,620 --> 00:01:08,620
我们的文件一共有三种权限

25
00:01:08,620 --> 00:01:11,620
一个是读 一个是写 一个是执行

26
00:01:11,620 --> 00:01:14,620
那么读很简单就是我们去读取我们的文件内容

27
00:01:14,620 --> 00:01:16,620
写了就是去修改 执行了就是咱们去运行它

28
00:01:16,620 --> 00:01:17,620
比如说我们一个GS脚本

29
00:01:17,620 --> 00:01:20,620
是不是需要有执行的权限才可以去使用load去执行

30
00:01:20,620 --> 00:01:22,620
那么读写执行的分别由

31
00:01:22,620 --> 00:01:26,460
2 W X 代表他们这里是他们英文缩写的手字母

32
00:01:26,460 --> 00:01:28,260
数字表示分别是421

33
00:01:28,260 --> 00:01:30,300
那么这里的421其实设计的非常巧妙

34
00:01:30,300 --> 00:01:32,600
为什么说它非常的巧妙吗

35
00:01:32,600 --> 00:01:34,140
比如说我这里随便给出一个数字3

36
00:01:34,140 --> 00:01:35,680
我们是不是可以代表这一个文字呢

37
00:01:35,680 --> 00:01:37,460
这一个文件只能够写和执行

38
00:01:37,460 --> 00:01:38,500
对吧 它也没其他组合

39
00:01:38,500 --> 00:01:39,000
没有吧

40
00:01:39,000 --> 00:01:40,020
3是不是只能有2+1组合

41
00:01:40,020 --> 00:01:41,060
那么如果我给个5呢

42
00:01:41,060 --> 00:01:42,080
是不是只能有4和1

43
00:01:42,080 --> 00:01:42,580
我给个6呢

44
00:01:42,580 --> 00:01:43,620
是不是只能有4和2

45
00:01:43,620 --> 00:01:45,920
所以说这里它非常的巧妙

46
00:01:45,920 --> 00:01:47,460
那么这里用到一个小技巧就叫做

47
00:01:47,460 --> 00:01:48,480
咱们的一个未操作

48
00:01:48,480 --> 00:01:51,040
那么对未操作有兴趣的也可以去了解一下

49
00:01:51,040 --> 00:01:53,340
我们凡是涉及到文件权限、用户权限等等

50
00:01:53,340 --> 00:01:56,680
咱们平时的业务开发中都可以使用到V操作

51
00:01:56,680 --> 00:02:00,000
那么我们这里就通过命令的形式

52
00:02:00,000 --> 00:02:01,020
我们来演示一下

53
00:02:01,020 --> 00:02:03,840
咱们权限不同会对我们的文件造成哪些影响

54
00:02:03,840 --> 00:02:05,880
首先我们这里可以看到

55
00:02:05,880 --> 00:02:09,220
咱们的文件权限的读代表它是一个数字4

56
00:02:09,220 --> 00:02:13,560
这里我们先进入咱们的一个命令窗口给打开

57
00:02:13,560 --> 00:02:17,660
大家可以看到这里是我们的一个

58
00:02:19,960 --> 00:02:22,780
这里是咱们的命行 对吧 我们来看一下

59
00:02:22,780 --> 00:02:24,820
这里呢 我们呢有

60
00:02:24,820 --> 00:02:28,400
Buffer Dgram Event这样的几个文件夹

61
00:02:28,400 --> 00:02:33,020
我们来看一下他能的在一些文件夹包括MD在那些文件他们所对应的权限分别是什么

62
00:02:33,020 --> 00:02:35,060
我们呢只需要去执行一条命令

63
00:02:35,060 --> 00:02:40,180
我们执行LL就可以看到我们这样的一些文件他所对应的一些权限

64
00:02:40,180 --> 00:02:42,240
好 那么我们一起来观察一下

65
00:02:45,820 --> 00:02:51,200
首先我们可以看到这样的一大串的英文单词 d2wxr

66
00:02:51,200 --> 00:02:55,460
那么这里的rwx是不是就可以代表eta g可以读又可以写又可以去执行

67
00:02:55,460 --> 00:02:56,620
那么这里的d代表什么呢

68
00:02:56,620 --> 00:02:58,100
d是不是我们文件夹的一个意思对吧

69
00:02:58,100 --> 00:03:01,440
那么这里第一个rwx就代表我们的文件拥有者

70
00:03:01,440 --> 00:03:03,720
那么xr分别代表所在组合其他用户

71
00:03:03,720 --> 00:03:05,380
我们一般可以不去关注他们

72
00:03:05,380 --> 00:03:06,720
一般不需要去关注很少

73
00:03:06,720 --> 00:03:09,980
那么后面就分别对应咱们文件的一些名称是吧

74
00:03:09,980 --> 00:03:13,920
那么这里redme.md它所对应的一个权限稍微有点不一样

75
00:03:13,920 --> 00:03:15,500
是不是对应的是rw

76
00:03:15,500 --> 00:03:18,660
没有X 说明了他无法去进行一个什么样 是不是无法去执行了

77
00:03:18,660 --> 00:03:22,220
好 那么这里呢 假如说我们首先去创建一个文件夹

78
00:03:22,220 --> 00:03:26,060
make.dr 我们去创建一个FS 好 然后我们进入

79
00:03:26,060 --> 00:03:29,260
我们的FS 好 然后呢我们创建一个GS文件

80
00:03:29,260 --> 00:03:35,460
比如说我创建一个test.js 好 我们来看一下咱们这样一个GS它的权限是什么

81
00:03:35,460 --> 00:03:38,380
我们执行LL 好 大家可以看到了

82
00:03:38,380 --> 00:03:41,180
我们GS 此时它是不是只有

83
00:03:41,180 --> 00:03:44,660
读写的权限了 对吧 好 那么我们就把它给修改一下

84
00:03:45,140 --> 00:03:47,120
我们把它改为只能够读 不能够写

85
00:03:47,120 --> 00:03:51,380
那么只能读不能写 我们刚才也看过它所代表的一个数字是不是4啊

86
00:03:51,380 --> 00:03:54,920
说说我们此时需要去执行一个命名叫做chmod

87
00:03:54,920 --> 00:04:01,260
我们把咱们的这样一个gs的权限呢 我们把它改为只能够读 不能够写

88
00:04:01,260 --> 00:04:02,340
也就是4 4 4

89
00:04:02,340 --> 00:04:05,140
好 然后呢 第三个参数就是我们的test.gs

90
00:04:05,140 --> 00:04:08,260
好 此时呢 我们已经把它的权限呢 已经给修改成功了 我们再来看一下

91
00:04:08,260 --> 00:04:11,840
大家可以看到了 我们此时的test.gs是不是只能够读啊 对吧

92
00:04:11,840 --> 00:04:14,260
好 我们来对它进行修改来看一下能不能成功

93
00:04:15,140 --> 00:04:19,560
比如说我们在里面去编写concel.log

94
00:04:19,560 --> 00:04:22,240
然后我们对它进行保存

95
00:04:22,240 --> 00:04:25,280
好大家可以看到read only option is set

96
00:04:25,280 --> 00:04:28,860
说明我们的这样的一个test.gs是不是已经对它的权限受到生效了

97
00:04:28,860 --> 00:04:30,700
它只能够去读但是不能够去写

98
00:04:30,700 --> 00:04:33,380
这里就是我们修改权限的一个操作

99
00:04:33,380 --> 00:04:35,200
好接下来回到我们的一个讲义

100
00:04:35,200 --> 00:04:37,180
那么其他的情况也同理

101
00:04:37,180 --> 00:04:39,860
如果说你通过chmode在一个命令去修改它的权限之后

102
00:04:39,860 --> 00:04:42,940
就会对我们的文件读写执行的产生一些影响

103
00:04:42,940 --> 00:04:45,020
好 接下来我们来看一下标志位flag

104
00:04:45,020 --> 00:04:49,460
在loads中标志位代表着对文件的操作方式

105
00:04:49,460 --> 00:04:52,220
比如说读写 包括可读可写等等

106
00:04:52,220 --> 00:04:54,560
那么我们就使用下面一张表来表示

107
00:04:54,560 --> 00:04:56,680
文件操作的标志位以及对应的含义

108
00:04:56,680 --> 00:05:00,620
好 首先呢 符号r就代表读写文件

109
00:05:00,620 --> 00:05:02,320
对吧 那么r加是什么呢

110
00:05:02,320 --> 00:05:05,120
r加就代表读取并写入文件

111
00:05:05,120 --> 00:05:06,840
那么如果文件不存在则抛出异常

112
00:05:06,840 --> 00:05:08,120
那么如果说是rs呢

113
00:05:08,120 --> 00:05:10,740
就是也是读取并写入文件

114
00:05:10,740 --> 00:05:11,940
但是它和上面不同的就是

115
00:05:11,940 --> 00:05:14,820
只是我们操作系统 让我开本地缓存的进行

116
00:05:14,820 --> 00:05:16,960
让我开咱们的本地文件系统进行缓存

117
00:05:16,960 --> 00:05:18,360
好 那么下面还有很多

118
00:05:18,360 --> 00:05:21,940
我们同学们可以自己去看一下它的标志位所分别对应的一些操作

119
00:05:21,940 --> 00:05:24,500
好 接下来我们来看一下文件描述符

120
00:05:24,500 --> 00:05:26,140
那么文件描述符是什么呢

121
00:05:26,140 --> 00:05:30,940
操作系统会为我们每个打开的文件分配一个名为文件描述符的一个数字标志

122
00:05:30,940 --> 00:05:35,980
那么文件操作使用这些文件描述符来识别与追踪每一个特定的文件

123
00:05:35,980 --> 00:05:39,200
那么大家就可以把它理解为咱们文件的一个特定的ID

124
00:05:39,200 --> 00:05:41,380
那么Windows系统使用了一个不同

125
00:05:41,380 --> 00:05:44,220
但概念类似的机制来追踪我们的一个支援

126
00:05:44,220 --> 00:05:47,840
那为了方便用户NoteGS抽象的不同操作系统之间的差异

127
00:05:47,840 --> 00:05:52,060
为我们所有打开的文件分配了咱们的一些文件描述符

128
00:05:52,060 --> 00:05:54,380
那么在NoteGS中每操作一个文件

129
00:05:54,380 --> 00:05:55,960
文件描述符都是递增的

130
00:05:55,960 --> 00:05:57,380
那么文件描述符一般从三开始

131
00:05:57,380 --> 00:05:58,300
为什么呢

132
00:05:58,300 --> 00:06:01,780
因为前面的012分别代表我们的一个process

133
00:06:01,780 --> 00:06:03,100
咱们的一个std

134
00:06:03,100 --> 00:06:04,580
就是我们的一个标准的输入

135
00:06:04,580 --> 00:06:05,780
输出和错误的输出

136
00:06:05,780 --> 00:06:06,940
这里是咱们的一个相当于是

137
00:06:06,940 --> 00:06:08,700
它们012相当于是一个关键字

138
00:06:08,700 --> 00:06:14,110
所以说呢 描述符一般是从我们的3开始 好 这里呢 就是我们学习fs模块的一些前置知识

139
00:06:14,110 --> 00:06:14,980
 我们来简单的回顾一下

140
00:06:14,980 --> 00:06:21,180
我们呢 学习了咱们的一个权限位 对吧 通过sh mode 可以去修改他们的咱们文件的一些权限

141
00:06:21,180 --> 00:06:23,100
 那么文件的权限的分为读写和执行

142
00:06:23,100 --> 00:06:27,300
好 那么什么是标志位呢 那么标志位呢 其实就是标志着啊 我们对文件的一个操作方式

143
00:06:27,300 --> 00:06:31,800
 那么下面这些表格呢 都列在里面 后面我们呢 使用到的时候 我们就来查在那一张表

144
00:06:32,220 --> 00:06:36,790
那么文件描述符呢 我们刚才也看了 它其实就是我们 每操作一个文件

145
00:06:36,790 --> 00:06:38,300
 我们它都会去生成一个文件

146
00:06:38,300 --> 00:06:44,460
描述符 而且它一般会从三开始 需要去绕过我们一些关键字 好 这里就是我们这节课的内容

