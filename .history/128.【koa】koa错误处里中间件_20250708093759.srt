1
00:00:00,260 --> 00:00:02,820
好 这一节课我们就来看一下怎么样去处理错误

2
00:00:02,820 --> 00:00:05,380
那么处理错误的话其实我们最好的办法是什么呀

3
00:00:05,380 --> 00:00:06,920
是不是用中间键去写

4
00:00:06,920 --> 00:00:08,200
那么这里呢我们就来

5
00:00:08,200 --> 00:00:10,500
写一个做错误处理的中间键

6
00:00:10,500 --> 00:00:21,000
首先我们来定义一个中间键 比如说叫做

7
00:00:21,000 --> 00:00:21,760
 arrow

8
00:00:21,760 --> 00:00:22,780
tunder

9
00:00:22,780 --> 00:00:24,320
等于方形

10
00:00:24,320 --> 00:00:27,140
等于箭头函数吧 咱们用新一点的语法

11
00:00:27,140 --> 00:00:28,680
不能带着同学们去再用什么

12
00:00:28,920 --> 00:00:30,960
也是5了咱们要陪养成使用1.16的一个习惯

13
00:00:30,960 --> 00:00:32,000
为什么

14
00:00:32,000 --> 00:00:35,840
逼格高一点就是这么简单

15
00:00:35,840 --> 00:00:38,900
好那么其实我们之前讲过我们为什么要去实现一个

16
00:00:38,900 --> 00:00:39,920
凑处理的中间键

17
00:00:39,920 --> 00:00:41,200
因为我不可能

18
00:00:41,200 --> 00:00:43,260
在每一个方法里面去比如说

19
00:00:43,260 --> 00:00:45,300
比如说咱们现在是不是有一个路由对吧

20
00:00:45,300 --> 00:00:47,100
你比如说访问咱们的首页是命

21
00:00:47,100 --> 00:00:49,400
然后呢访问home呢你可能会定义一个

22
00:00:49,400 --> 00:00:50,160
home方法

23
00:00:50,160 --> 00:00:51,440
好const

24
00:00:51,440 --> 00:00:52,480
home等于

25
00:00:52,480 --> 00:00:54,260
比如说这是对home的一些

26
00:00:54,260 --> 00:00:56,560
处理那么我们不可能在每一段

27
00:00:56,560 --> 00:00:58,360
比如说我们在每一段路由里面去处理

28
00:00:58,620 --> 00:01:00,160
我们常见的错误处理是不是try-catch

29
00:01:00,160 --> 00:01:01,940
是不是try-catch

30
00:01:01,940 --> 00:01:03,740
那么我们

31
00:01:03,740 --> 00:01:05,020
可不可能

32
00:01:05,020 --> 00:01:06,560
每一个都去try-catch

33
00:01:06,560 --> 00:01:07,840
这好像

34
00:01:07,840 --> 00:01:09,620
效率不是很高对吧

35
00:01:09,620 --> 00:01:10,900
其实最好的办法是什么

36
00:01:10,900 --> 00:01:11,940
我们

37
00:01:11,940 --> 00:01:13,220
是不是利用咱们

38
00:01:13,220 --> 00:01:15,000
cowa它的一个中间键这样一个特性

39
00:01:15,000 --> 00:01:15,780
一次性处理

40
00:01:15,780 --> 00:01:16,540
怎么样去做呢

41
00:01:16,540 --> 00:01:19,620
首先我们的error-hander它是一个什么函数

42
00:01:19,620 --> 00:01:21,400
它是不是一个一步的方法为什么

43
00:01:21,400 --> 00:01:23,200
因为可能我们中间键执行过程中

44
00:01:23,200 --> 00:01:25,500
你的next里面它的函数可能是一步的

45
00:01:25,500 --> 00:01:26,520
你需要去await它

46
00:01:26,520 --> 00:01:27,540
我们之前是不是讲过

47
00:01:28,060 --> 00:01:28,960
讲过咱们扬出模型

48
00:01:28,960 --> 00:01:29,960
执行顺序的时候

49
00:01:29,960 --> 00:01:32,360
是不是咱们其实最好是每一个方法里面都加入一个什么

50
00:01:32,360 --> 00:01:33,260
是不是as yinc

51
00:01:33,260 --> 00:01:34,660
sync函数对吧

52
00:01:34,660 --> 00:01:35,800
好

53
00:01:35,800 --> 00:01:36,760
那么他呢

54
00:01:36,760 --> 00:01:39,660
我们的中间键是不是接受一个ctx一个是next

55
00:01:39,660 --> 00:01:40,860
首先我们去try

56
00:01:40,860 --> 00:01:42,160
try什么

57
00:01:42,160 --> 00:01:42,860
try

58
00:01:42,860 --> 00:01:44,860
我们去做错误处理

59
00:01:44,860 --> 00:01:46,860
我们是不是去捕捉next

60
00:01:46,860 --> 00:01:48,160
next这样一个方法

61
00:01:48,160 --> 00:01:48,560
他说

62
00:01:48,560 --> 00:01:50,860
他说产生了错误对吧

63
00:01:50,860 --> 00:01:52,860
所以我们去await一下next

64
00:01:52,860 --> 00:01:54,460
next代表什么

65
00:01:54,460 --> 00:01:56,760
是不是代表咱们扬出模型后面函数啊

66
00:01:56,760 --> 00:01:57,660
比如说我们去

67
00:01:58,060 --> 00:01:59,580
比如说我们去捕捉到错误之后

68
00:01:59,580 --> 00:02:01,380
咱们去catch一下它

69
00:02:01,380 --> 00:02:03,700
我们去catch一个error

70
00:02:03,700 --> 00:02:05,740
然后呢context的点

71
00:02:05,740 --> 00:02:08,300
readBounds的点

72
00:02:08,300 --> 00:02:10,100
status等于什么呢

73
00:02:10,100 --> 00:02:12,400
比如说我们error里面它可能会有一些

74
00:02:12,400 --> 00:02:14,180
status code

75
00:02:14,180 --> 00:02:16,740
或者说

76
00:02:16,740 --> 00:02:19,060
error的status

77
00:02:19,060 --> 00:02:21,100
可能跟咱们的一个实用版本有关系

78
00:02:21,100 --> 00:02:23,140
如果说都没有 直接返回500

79
00:02:23,140 --> 00:02:26,220
这是咱们默认的一个情况 这件事对

80
00:02:28,060 --> 00:02:35,060
的处理 因为你要需要让用户知道你错了 你必须返回一个500 或者说是404

81
00:02:35,060 --> 00:02:36,000
 或者说是其他一些状态嘛

82
00:02:36,000 --> 00:02:40,600
好 然后呢 咱们是不是还需要去咱们刚才去

83
00:02:40,600 --> 00:02:44,700
处理咱们错误的时候是不是两种方式 是不是一种是直接是罗一种事了

84
00:02:44,700 --> 00:02:46,740
 修改stay status之后还需要修改什么

85
00:02:46,740 --> 00:02:49,060
是不是body response.body等于

86
00:02:49,060 --> 00:02:51,860
比如说咱们去返回一个对象

87
00:02:53,920 --> 00:03:00,760
message, 其实 error对象里面它其实会默认的会带上message这样一个属性

88
00:03:00,760 --> 00:03:09,280
那么我们就来看一下它到底有没有用

89
00:03:09,280 --> 00:03:13,720
首先呢我们是不是在APP的哪个地方去use它同学们

90
00:03:13,720 --> 00:03:20,160
我们试图有个问题,我们是在咱们最前面去使用我们的ErrorHunt这样一个中间键

91
00:03:20,160 --> 00:03:22,080
还是在最后面呢

92
00:03:23,920 --> 00:03:26,060
比如说我们是在这里

93
00:03:26,060 --> 00:03:27,700
还是在这里

94
00:03:27,700 --> 00:03:28,560
这两个地方

95
00:03:28,560 --> 00:03:29,480
大家思考一下

96
00:03:29,480 --> 00:03:30,760
是不是在最前面了

97
00:03:30,760 --> 00:03:31,520
为什么呀

98
00:03:31,520 --> 00:03:33,100
因为我们是不是try的是next

99
00:03:33,100 --> 00:03:34,220
那么next代表谁

100
00:03:34,220 --> 00:03:34,880
是代表后面的

101
00:03:34,880 --> 00:03:35,780
你如果说加到最后

102
00:03:35,780 --> 00:03:36,600
后面是不是什么都没有

103
00:03:36,600 --> 00:03:37,280
那么你是不是

104
00:03:37,280 --> 00:03:38,220
那么你是不是白

105
00:03:38,220 --> 00:03:39,340
白去看起来

106
00:03:39,340 --> 00:03:40,440
白去处理错误了

107
00:03:40,440 --> 00:03:40,700
好

108
00:03:40,700 --> 00:03:41,560
那么我们就来看一下

109
00:03:41,560 --> 00:03:42,940
比如说我们在may里面

110
00:03:42,940 --> 00:03:44,580
我们在may里面去

111
00:03:44,580 --> 00:03:46,860
比如说我们去瞎书

112
00:03:46,860 --> 00:03:49,420
比如说我们去console.log一个

113
00:03:49,420 --> 00:03:51,900
AA.bbb

114
00:03:51,900 --> 00:03:53,100
其实AA它是什么

115
00:03:53,100 --> 00:03:54,120
是不是undefined BBB呢

116
00:03:54,120 --> 00:03:55,920
undefined上面一个属性

117
00:03:55,920 --> 00:03:58,080
那么undefined上面是不是没有BBB这样一个属性了

118
00:03:58,080 --> 00:03:58,860
所以它一定会报错

119
00:03:58,860 --> 00:03:59,880
那么我们就来看一下

120
00:03:59,880 --> 00:04:02,740
这样一个错误它能不能够捕捉到

121
00:04:02,740 --> 00:04:05,840
好 其实这里呢 报错了

122
00:04:05,840 --> 00:04:06,360
说明什么

123
00:04:06,360 --> 00:04:08,460
AA is not defined status呢 500

124
00:04:08,460 --> 00:04:11,380
其实这里我们就可以看到

125
00:04:11,380 --> 00:04:12,980
其实我们错误是已经捕捉到了

126
00:04:12,980 --> 00:04:15,280
为了方便同学们去看到

127
00:04:15,280 --> 00:04:19,140
效果我们在这里打印一下

128
00:04:19,140 --> 00:04:21,380
已经捕捉到了错误

129
00:04:21,380 --> 00:04:22,900
好 我们来重启一下

130
00:04:22,900 --> 00:04:25,320
刷新

131
00:04:25,320 --> 00:04:27,580
还是刚才的错误

132
00:04:27,580 --> 00:04:28,940
但是呢我们在控制台

133
00:04:28,940 --> 00:04:30,560
控制台为什么没有打印

134
00:04:30,560 --> 00:04:32,200
可能刚才没有重启

135
00:04:32,200 --> 00:04:33,480
我们重启一下服务

136
00:04:33,480 --> 00:04:37,300
好大家可以看到

137
00:04:37,300 --> 00:04:38,660
我们控制台是不是已经打印了

138
00:04:38,660 --> 00:04:40,180
已经捕捉到了错误

139
00:04:40,180 --> 00:04:40,720
对吧

140
00:04:40,720 --> 00:04:42,380
说明了在咱们的next里面

141
00:04:42,380 --> 00:04:43,720
已经报错了

142
00:04:43,720 --> 00:04:46,100
next的执行报错

143
00:04:46,100 --> 00:04:49,160
就会进入catch

144
00:04:49,160 --> 00:04:50,000
好

145
00:04:50,000 --> 00:04:51,420
那么我们刚才是不是还讲到了

146
00:04:51,420 --> 00:04:52,880
咱们的中间键要加在最前面

147
00:04:52,880 --> 00:04:53,920
那么我们来测试一下

148
00:04:53,920 --> 00:04:55,020
假如说加入到最后面

149
00:04:55,020 --> 00:04:57,520
我们能不能够捕捉到这样一个错误

150
00:04:57,520 --> 00:04:58,820
好

151
00:04:58,820 --> 00:05:00,560
那么我们再启动一下服务

152
00:05:00,560 --> 00:05:01,580
刷新

153
00:05:01,580 --> 00:05:02,920
大家可以看到

154
00:05:02,920 --> 00:05:04,000
英特朗西部Aerror

155
00:05:04,000 --> 00:05:05,320
这里也返回一个500

156
00:05:05,320 --> 00:05:06,360
那么我们来看一下

157
00:05:06,360 --> 00:05:07,560
这里的错误

158
00:05:07,560 --> 00:05:08,360
是不是我们nodejs

159
00:05:08,360 --> 00:05:09,840
它去自动的去捕捉的

160
00:05:09,840 --> 00:05:11,120
reference error

161
00:05:11,120 --> 00:05:12,320
AA is not defined

162
00:05:12,320 --> 00:05:12,940
说明什么问题

163
00:05:12,940 --> 00:05:13,820
是不是说明我们nodejs

164
00:05:13,820 --> 00:05:15,020
它的执行其实是报错的

165
00:05:15,020 --> 00:05:16,600
其实这里是没有捕捉到的

166
00:05:16,600 --> 00:05:17,260
它是nodejs

167
00:05:17,260 --> 00:05:18,840
它的自己的一个兜体方案

168
00:05:18,840 --> 00:05:20,000
去帮我们去捕捉的

169
00:05:20,000 --> 00:05:22,840
如果说没有

170
00:05:22,840 --> 00:05:23,940
错误处理

171
00:05:23,940 --> 00:05:26,500
中间键

172
00:05:26,500 --> 00:05:29,080
nodejs会帮我们去

173
00:05:29,080 --> 00:05:30,580
但是这样

174
00:05:30,580 --> 00:05:32,740
很不好

175
00:05:32,740 --> 00:05:34,340
为什么不好

176
00:05:34,340 --> 00:05:37,420
是不是有时候你去发生一些错误的时候

177
00:05:37,420 --> 00:05:38,140
你比如说

178
00:05:38,140 --> 00:05:39,620
比如说我们在Cash里面

179
00:05:39,620 --> 00:05:40,920
比如说我们在Cash如果说捕捉到了

180
00:05:40,920 --> 00:05:41,660
你是不是在这里

181
00:05:41,660 --> 00:05:44,980
是不是可以做一些错误的日子上传呢

182
00:05:44,980 --> 00:05:46,600
而且你可以定制化的去上传你的一些日子

183
00:05:46,600 --> 00:05:48,580
但是如果说nodejs它自动的去捕捉

184
00:05:48,580 --> 00:05:50,200
那么你是不是没办法去操作它

185
00:05:50,200 --> 00:05:53,840
你没有办法去控制

186
00:05:53,840 --> 00:05:57,880
这里呢就是咱们需要使用咱们错误处理中间键它的一个原因

187
00:05:57,880 --> 00:05:59,860
好那么其实呢我们的

188
00:05:59,860 --> 00:06:03,680
其实呢我们的AerrorHand是不是要在最前面呢

189
00:06:03,680 --> 00:06:06,500
这里呢需要希望同学们可以去思考一下我们的洋出模型

190
00:06:06,500 --> 00:06:07,620
为什么要在最前面

191
00:06:07,620 --> 00:06:09,320
好我们这里呢来总结一下

192
00:06:09,320 --> 00:06:14,320
错误处理中间键

193
00:06:14,320 --> 00:06:16,020
好那么我们的一个思路是什么

194
00:06:16,020 --> 00:06:18,820
是不是使用什么

195
00:06:18,820 --> 00:06:19,940
使用

196
00:06:19,940 --> 00:06:23,040
try catch

197
00:06:23,040 --> 00:06:24,640
那么在try中执行什么

198
00:06:24,640 --> 00:06:28,360
是不是在try中执行next的函数

199
00:06:28,360 --> 00:06:29,720
然后呢

200
00:06:29,720 --> 00:06:30,240
catch

201
00:06:30,240 --> 00:06:33,200
next发生的错误

202
00:06:33,200 --> 00:06:35,680
那么next是不是就是咱们后面所执行的函数

203
00:06:35,680 --> 00:06:37,280
那么如果说你后面的一系列

204
00:06:37,280 --> 00:06:39,500
你的整个洋葱模型有某一个环节发生的错误

205
00:06:39,500 --> 00:06:40,900
那么都呢会被catch到

206
00:06:49,940 --> 00:06:51,680
好

207
00:06:51,680 --> 00:06:53,260
那么为什么我们的洋出模型

208
00:06:53,260 --> 00:06:54,000
有一处发生的错误

209
00:06:54,000 --> 00:06:54,780
它都会被卡起到

210
00:06:54,780 --> 00:06:55,900
同学们还记不记得

211
00:06:55,900 --> 00:06:56,900
我们之前画的这样一个

212
00:06:56,900 --> 00:06:58,120
堆战的一个模型

213
00:06:58,120 --> 00:06:59,800
你比如说我们的

214
00:06:59,800 --> 00:07:01,820
我们的error handler是不是在第一个

215
00:07:01,820 --> 00:07:03,120
那么它的next只是谁

216
00:07:03,120 --> 00:07:03,760
是不是one呢

217
00:07:03,760 --> 00:07:06,040
后面呢执行的是two和three

218
00:07:06,040 --> 00:07:08,360
那么假如说你的two和three发生了错误

219
00:07:08,360 --> 00:07:09,900
那么咱们的next这样一个执行站

220
00:07:09,900 --> 00:07:11,920
是不是最晚才能够退出

221
00:07:11,920 --> 00:07:15,280
所以说咱们one里面

222
00:07:15,280 --> 00:07:16,160
所发生的错误

223
00:07:16,160 --> 00:07:17,580
它一定会被我们的错误处理中间间

224
00:07:17,580 --> 00:07:18,340
所以卡起到

225
00:07:18,340 --> 00:07:19,380
反过来说

226
00:07:19,380 --> 00:07:21,300
你1 2 3它这样三个函数

227
00:07:21,300 --> 00:07:22,900
它里面只要发生一些错误

228
00:07:22,900 --> 00:07:24,400
咱们都可以通过1去看起

229
00:07:24,400 --> 00:07:26,180
同时呢咱们的错误处理中间键

230
00:07:26,180 --> 00:07:28,700
就可以去捕捉到我们所有的错误

231
00:07:28,700 --> 00:07:30,120
希望同学们可以好好的去领悟一下

232
00:07:30,120 --> 00:07:31,340
好这里呢就是我们这节课的内容

