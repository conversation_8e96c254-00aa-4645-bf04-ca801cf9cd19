1
00:00:00,000 --> 00:00:02,580
实现群聊功能以后

2
00:00:02,580 --> 00:00:05,680
然后接下来我们来看一下这个私聊怎么来处理

3
00:00:05,680 --> 00:00:06,920
什么是私聊呢

4
00:00:06,920 --> 00:00:08,640
就是说我们现在可以看到就是说

5
00:00:08,640 --> 00:00:10,380
所有人都在进行群聊

6
00:00:10,380 --> 00:00:11,260
只要你登录进来

7
00:00:11,260 --> 00:00:12,540
你发的这个消息都是群聊

8
00:00:12,540 --> 00:00:13,740
私聊就是说

9
00:00:13,740 --> 00:00:16,320
我们在这可以给他制定一个数据的格式

10
00:00:16,320 --> 00:00:17,520
例如说我们举个例子

11
00:00:17,520 --> 00:00:18,440
比如说我们这个时候

12
00:00:18,440 --> 00:00:21,200
我们让他再来增加一个用户

13
00:00:21,200 --> 00:00:22,520
我们再让他增加一个用户

14
00:00:22,520 --> 00:00:24,440
例如这个nodeclient

15
00:00:24,440 --> 00:00:26,440
例如这个他在输入这个rose

16
00:00:26,440 --> 00:00:28,220
那这个时候

17
00:00:28,220 --> 00:00:29,240
rose也在这 hello

18
00:00:29,240 --> 00:00:30,460
好我们就可以看到

19
00:00:30,460 --> 00:00:31,780
就是大家都看到这个Rose说的话

20
00:00:31,780 --> 00:00:33,140
好那假如说这个Rose呢

21
00:00:33,140 --> 00:00:34,260
想单独跟Jack说

22
00:00:34,260 --> 00:00:35,640
我们给他设计一下

23
00:00:35,640 --> 00:00:37,100
他可以atJack

24
00:00:37,100 --> 00:00:37,760
然后空格

25
00:00:37,760 --> 00:00:38,900
然后输入Hello

26
00:00:38,900 --> 00:00:40,020
好那么这样的话

27
00:00:40,020 --> 00:00:40,560
我们就希望

28
00:00:40,560 --> 00:00:42,920
这个消息是发给Jack的

29
00:00:42,920 --> 00:00:44,200
只有Jack能收到

30
00:00:44,200 --> 00:00:45,700
而Mac不应该收到

31
00:00:45,700 --> 00:00:46,860
所以就是这个意思

32
00:00:46,860 --> 00:00:48,000
好那么这样的话

33
00:00:48,000 --> 00:00:50,680
当然这个也是当前这个用户

34
00:00:50,680 --> 00:00:51,460
所输入的消息

35
00:00:51,460 --> 00:00:54,380
所以说我们要对这个消息的格式

36
00:00:54,380 --> 00:00:55,420
进行一个探定了

37
00:00:55,420 --> 00:00:57,340
就是说如果当前用户输入的消息

38
00:00:57,340 --> 00:00:58,400
符合这种数据格式

39
00:00:58,400 --> 00:00:59,400
那么我们就认为

40
00:00:59,400 --> 00:01:00,440
它在进行私聊

41
00:01:00,440 --> 00:01:01,740
如果说当前用户

42
00:01:01,740 --> 00:01:02,560
输入的这个消息呢

43
00:01:02,560 --> 00:01:03,860
不是这样一个格式

44
00:01:03,860 --> 00:01:04,700
那么我们就认为

45
00:01:04,700 --> 00:01:05,240
它在群聊

46
00:01:05,240 --> 00:01:06,740
如果输入了这样一个消息

47
00:01:06,740 --> 00:01:07,600
那么我们就认为

48
00:01:07,600 --> 00:01:08,700
它是私聊群聊

49
00:01:08,700 --> 00:01:09,260
我们一辉车

50
00:01:09,260 --> 00:01:11,040
所有人就能看见了

51
00:01:11,040 --> 00:01:12,500
所以说关键就在于什么呢

52
00:01:12,500 --> 00:01:13,340
关键就在于我们希望

53
00:01:13,340 --> 00:01:14,280
有这么一个东西

54
00:01:14,280 --> 00:01:14,760
i的价格

55
00:01:14,760 --> 00:01:15,100
叉叉叉

56
00:01:15,100 --> 00:01:15,880
一辉车

57
00:01:15,880 --> 00:01:16,900
我们希望把这个消息

58
00:01:16,900 --> 00:01:17,800
作为私聊的消息

59
00:01:17,800 --> 00:01:19,060
发给这个价格

60
00:01:19,060 --> 00:01:20,040
当然现在的话

61
00:01:20,040 --> 00:01:20,640
我们就可以看到

62
00:01:20,640 --> 00:01:22,140
如果说我们输入一个消息

63
00:01:22,140 --> 00:01:22,660
这样一辉车

64
00:01:22,660 --> 00:01:24,640
然后是不是把这个消息

65
00:01:24,640 --> 00:01:26,100
当成了我们当前用户

66
00:01:26,100 --> 00:01:28,000
要发送的群聊消息内容

67
00:01:28,000 --> 00:01:29,000
所以你看到

68
00:01:29,000 --> 00:01:30,780
它发出的是这样一个数据

69
00:01:30,780 --> 00:01:31,900
所以说

70
00:01:31,900 --> 00:01:32,840
接下来我们待会就可以

71
00:01:32,840 --> 00:01:33,940
就需要处理一下

72
00:01:33,940 --> 00:01:35,220
这个赋料的一个实现

73
00:01:35,220 --> 00:01:36,720
首先

74
00:01:36,720 --> 00:01:38,820
到我们这个编辑器当中

75
00:01:38,820 --> 00:01:39,860
找到谁呢

76
00:01:39,860 --> 00:01:40,760
找到我们扣端

77
00:01:40,760 --> 00:01:41,740
在这里的一个输入

78
00:01:41,740 --> 00:01:42,940
我们待会都知道

79
00:01:42,940 --> 00:01:43,800
扣端输入的消息

80
00:01:43,800 --> 00:01:45,320
都会触发这个data事件

81
00:01:45,320 --> 00:01:45,880
也就是说

82
00:01:45,880 --> 00:01:46,940
从这个nikname

83
00:01:46,940 --> 00:01:47,700
只要它有了昵称

84
00:01:47,700 --> 00:01:49,100
后面的都是聊天的消息

85
00:01:49,100 --> 00:01:50,240
那这样的话

86
00:01:50,240 --> 00:01:50,700
我们在这里

87
00:01:50,700 --> 00:01:52,080
来给它制定一个简单的规范

88
00:01:52,080 --> 00:01:53,720
这个规范是什么呢

89
00:01:53,720 --> 00:01:54,400
非常简单

90
00:01:54,400 --> 00:01:58,340
我们在这来用一个证则来判定一下

91
00:01:58,340 --> 00:02:00,440
等于什么呢

92
00:02:00,440 --> 00:02:01,620
这里有个证则表达式

93
00:02:01,620 --> 00:02:02,320
test

94
00:02:02,320 --> 00:02:02,960
test什么呢

95
00:02:02,960 --> 00:02:03,560
我们这个data

96
00:02:03,560 --> 00:02:05,420
这个证则表达式长这样

97
00:02:05,420 --> 00:02:06,060
长这样

98
00:02:06,060 --> 00:02:06,640
以什么呢

99
00:02:06,640 --> 00:02:07,440
以at开头

100
00:02:07,440 --> 00:02:10,180
然后是这个答备

101
00:02:10,180 --> 00:02:10,900
加

102
00:02:10,900 --> 00:02:13,320
也就是说这个中间这个是什么呢

103
00:02:13,320 --> 00:02:14,140
就是对方的捏称

104
00:02:14,140 --> 00:02:16,540
然后-s一个空格

105
00:02:16,540 --> 00:02:17,200
一个空格

106
00:02:17,200 --> 00:02:18,720
然后接下来就是这个

107
00:02:18,720 --> 00:02:19,620
加

108
00:02:19,620 --> 00:02:21,180
加就是他要发这个消息

109
00:02:21,180 --> 00:02:21,740
有任意一位

110
00:02:21,740 --> 00:02:22,640
好

111
00:02:22,640 --> 00:02:24,020
然后最后结尾

112
00:02:24,020 --> 00:02:25,860
也就是接下来我们在这里

113
00:02:25,860 --> 00:02:28,260
我们把这个w加

114
00:02:28,260 --> 00:02:30,460
应该是-w

115
00:02:30,460 --> 00:02:31,900
这个就是字符

116
00:02:31,900 --> 00:02:33,580
匹配这个0到9

117
00:02:33,580 --> 00:02:34,660
就是小写A到Z

118
00:02:34,660 --> 00:02:36,320
就是用户的昵称

119
00:02:36,320 --> 00:02:38,260
好了加的话就是一次或者多次

120
00:02:38,260 --> 00:02:39,960
我们在这呢

121
00:02:39,960 --> 00:02:41,540
把它我们要正则分组

122
00:02:41,540 --> 00:02:42,320
把它给分出来

123
00:02:42,320 --> 00:02:44,940
所以说我们在这要把它给括起来

124
00:02:44,940 --> 00:02:45,460
好了

125
00:02:45,460 --> 00:02:47,740
不对应该是把连这个加号

126
00:02:47,740 --> 00:02:48,880
也给它括起来

127
00:02:48,880 --> 00:02:50,220
好了然后这个表加是表

128
00:02:50,220 --> 00:02:51,140
它要发的那个消息

129
00:02:51,140 --> 00:02:51,980
再次进行分组

130
00:02:51,980 --> 00:02:52,860
好

131
00:02:52,860 --> 00:02:54,640
有时接下来我们在这就要判断一下

132
00:02:54,640 --> 00:02:55,280
如果

133
00:02:55,280 --> 00:02:56,660
Metis

134
00:02:56,660 --> 00:02:58,680
如果Metis匹配成功

135
00:02:58,680 --> 00:02:59,640
那么这个就表示

136
00:02:59,640 --> 00:03:00,580
也就是说

137
00:03:00,580 --> 00:03:04,100
如果本次的消息符合

138
00:03:04,100 --> 00:03:04,880
符合什么呢

139
00:03:04,880 --> 00:03:07,800
符合就是at格式

140
00:03:07,800 --> 00:03:09,740
那么这个就证明

141
00:03:09,740 --> 00:03:11,560
用户要发送的是一个私聊的消息

142
00:03:11,560 --> 00:03:13,800
那否则

143
00:03:13,800 --> 00:03:15,120
那么这个就表示什么呢

144
00:03:15,120 --> 00:03:16,560
这个表示它是群聊的消息

145
00:03:16,560 --> 00:03:19,680
当然了我们也可以不用这个else

146
00:03:19,680 --> 00:03:21,540
就是这样的话减少这个代表的天套

147
00:03:21,540 --> 00:03:22,920
那么我们在这里

148
00:03:22,920 --> 00:03:23,380
私聊的话

149
00:03:23,380 --> 00:03:24,340
我们家就是变成了这样

150
00:03:24,340 --> 00:03:25,340
Message呢

151
00:03:25,340 --> 00:03:26,980
会提取到这个数据的分组

152
00:03:26,980 --> 00:03:28,100
那这个时候

153
00:03:28,100 --> 00:03:28,640
我们家就是

154
00:03:28,640 --> 00:03:31,200
returnclient.write

155
00:03:31,200 --> 00:03:33,840
json.streamfile

156
00:03:33,840 --> 00:03:36,640
那么这里的话

157
00:03:36,640 --> 00:03:38,380
好

158
00:03:38,380 --> 00:03:38,980
小细的内容

159
00:03:38,980 --> 00:03:40,520
首先这个type

160
00:03:40,520 --> 00:03:41,300
就变成了

161
00:03:41,300 --> 00:03:42,960
type里面的P2P

162
00:03:42,960 --> 00:03:43,820
好

163
00:03:43,820 --> 00:03:44,760
然后接下来就是

164
00:03:44,760 --> 00:03:46,780
这个是这个内容的类型

165
00:03:46,780 --> 00:03:47,720
然后接下来这个私聊

166
00:03:47,720 --> 00:03:48,600
你要私聊给谁呢

167
00:03:48,600 --> 00:03:49,080
对吧

168
00:03:49,080 --> 00:03:50,000
所以说有个to

169
00:03:50,000 --> 00:03:51,200
要发给谁

170
00:03:51,200 --> 00:03:53,120
我们要把它发给这个

171
00:03:53,120 --> 00:03:55,580
我们要把它发给这个

172
00:03:55,580 --> 00:03:57,280
或者说叫做nickname

173
00:03:57,280 --> 00:03:58,420
就是要发给谁

174
00:03:58,420 --> 00:03:59,540
发给谁呢

175
00:03:59,540 --> 00:04:01,720
也就是这个叫matches1

176
00:04:01,720 --> 00:04:04,220
0的话是这个字符传本身

177
00:04:04,220 --> 00:04:05,120
1的话是分组

178
00:04:05,120 --> 00:04:05,980
数括号

179
00:04:05,980 --> 00:04:07,080
第一个括号就是第一组

180
00:04:07,080 --> 00:04:08,360
第二括号就是第二组

181
00:04:08,360 --> 00:04:09,180
好了

182
00:04:09,180 --> 00:04:10,260
然后是这个matches

183
00:04:10,260 --> 00:04:11,300
matches就是消息

184
00:04:11,300 --> 00:04:12,120
好了

185
00:04:12,120 --> 00:04:12,760
那这个消息呢

186
00:04:12,760 --> 00:04:15,500
就是这个matches2

187
00:04:15,500 --> 00:04:16,200
好了

188
00:04:16,200 --> 00:04:16,720
那这个时候

189
00:04:16,720 --> 00:04:18,680
这个就是我们这个资料

190
00:04:18,680 --> 00:04:19,220
p2p

191
00:04:19,220 --> 00:04:20,400
就是说我要发给

192
00:04:20,400 --> 00:04:22,360
捏称叫他的这个消息

193
00:04:22,360 --> 00:04:23,240
所以就这个意思

194
00:04:23,240 --> 00:04:24,060
好了

195
00:04:24,060 --> 00:04:26,120
那如果说这个might is不匹配

196
00:04:26,120 --> 00:04:26,780
那不匹配的话

197
00:04:26,780 --> 00:04:27,280
那就是群聊

198
00:04:27,280 --> 00:04:28,300
群聊的话就直接这样

199
00:04:28,300 --> 00:04:29,680
所以就是这个意思

200
00:04:29,680 --> 00:04:31,500
这个时候对于我们这个

201
00:04:31,500 --> 00:04:32,320
服务端来讲

202
00:04:32,320 --> 00:04:34,220
那么这里的话就匹配到了

203
00:04:34,220 --> 00:04:35,520
匹配到以后

204
00:04:35,520 --> 00:04:36,020
那么这里的话

205
00:04:36,020 --> 00:04:37,260
他就要去进行处理

206
00:04:37,260 --> 00:04:38,420
那么处理的话

207
00:04:38,420 --> 00:04:39,420
也非常简单

208
00:04:39,420 --> 00:04:40,960
我们直接叫找到

209
00:04:40,960 --> 00:04:42,380
你要撕掉的那个用户

210
00:04:42,380 --> 00:04:43,180
所以说就是users

211
00:04:43,180 --> 00:04:44,240
我们在这里可以用find

212
00:04:44,240 --> 00:04:45,940
finditem

213
00:04:45,940 --> 00:04:48,740
然后是item.nickname

214
00:04:48,740 --> 00:04:49,460
等于什么呢

215
00:04:49,460 --> 00:04:50,940
用这个data里面的NicNicNic

216
00:04:50,940 --> 00:04:52,080
我们要找到这个对方

217
00:04:52,080 --> 00:04:54,420
我们定一个来接收一下

218
00:04:54,420 --> 00:04:55,000
接收一下

219
00:04:55,000 --> 00:04:57,140
那么这里的话就是这个User

220
00:04:57,140 --> 00:04:58,300
好了

221
00:04:58,300 --> 00:04:59,380
然后接下来我们判断一下

222
00:04:59,380 --> 00:05:00,960
如果废一下这个User

223
00:05:00,960 --> 00:05:02,120
这个表就是说

224
00:05:02,120 --> 00:05:03,180
没有找到私标的用户

225
00:05:03,180 --> 00:05:03,960
那证明这个用户

226
00:05:03,960 --> 00:05:05,000
它不存在

227
00:05:05,000 --> 00:05:06,300
或者是已经下线了

228
00:05:06,300 --> 00:05:07,140
就是该用户不存在

229
00:05:07,140 --> 00:05:08,100
所以说这个时候

230
00:05:08,100 --> 00:05:09,420
我们在这要return

231
00:05:09,420 --> 00:05:12,180
然后clientSocket.Write

232
00:05:12,180 --> 00:05:13,940
然后通知当前发消息的这个用户

233
00:05:13,940 --> 00:05:16,260
然后让这个

234
00:05:16,260 --> 00:05:18,800
json.screensive

235
00:05:18,800 --> 00:05:21,220
好了我们给他一个type

236
00:05:21,220 --> 00:05:24,060
type.t2p

237
00:05:24,060 --> 00:05:26,660
好了然后还有一个就是success

238
00:05:26,660 --> 00:05:29,540
表示这个私聊消息发送失败了

239
00:05:29,540 --> 00:05:33,100
好该用户不存在

240
00:05:33,100 --> 00:05:36,660
好然后接下来这个时候

241
00:05:36,660 --> 00:05:38,020
表示没有这个用户

242
00:05:38,020 --> 00:05:39,360
那如果说有这个用户

243
00:05:39,360 --> 00:05:40,980
那接下来我们就要给这个用户发消息

244
00:05:40,980 --> 00:05:43,680
那么fire的话我们在这就变成了user.write

245
00:05:43,680 --> 00:05:44,420
user是谁

246
00:05:44,420 --> 00:05:46,840
就是你要私聊的对方那个用户

247
00:05:46,840 --> 00:05:47,900
他的那个socket的客户端

248
00:05:47,900 --> 00:05:50,600
好 那么这里的话我们就是jason.screen file

249
00:05:50,600 --> 00:05:52,560
screen file

250
00:05:52,560 --> 00:05:54,860
好了 那么这里的话就是这个tap

251
00:05:54,860 --> 00:05:57,700
就是taps.p2p

252
00:05:57,700 --> 00:05:59,000
好了 然后success

253
00:05:59,000 --> 00:06:00,700
那么此时就是true

254
00:06:00,700 --> 00:06:02,920
然后这个message

255
00:06:02,920 --> 00:06:04,980
message就是这个data里面的message

256
00:06:04,980 --> 00:06:06,220
好 那么这样的话

257
00:06:06,220 --> 00:06:08,160
我们就真正的发给了那个单独的用户

258
00:06:08,160 --> 00:06:09,680
好 这个是服务端

259
00:06:09,680 --> 00:06:11,320
把数据呢去发过来了

260
00:06:11,320 --> 00:06:12,720
然后客户端呢

261
00:06:12,720 --> 00:06:14,820
咱们就得去接收 去判处理

262
00:06:14,820 --> 00:06:16,620
所以说这个是客户端的这里

263
00:06:16,620 --> 00:06:17,900
那么括弹到这里呢

264
00:06:17,900 --> 00:06:18,560
就要判断一下

265
00:06:18,560 --> 00:06:19,700
如果data.success

266
00:06:19,700 --> 00:06:21,480
那么这个就表示啊

267
00:06:21,480 --> 00:06:22,760
如果它成功了

268
00:06:22,760 --> 00:06:23,760
当然我们待会先飞一下

269
00:06:23,760 --> 00:06:24,840
如果说失败了

270
00:06:24,840 --> 00:06:26,180
那么这个时候呢

271
00:06:26,180 --> 00:06:26,920
我们家里就要去

272
00:06:26,920 --> 00:06:28,100
我们处理一下

273
00:06:28,100 --> 00:06:28,620
非常简单

274
00:06:28,620 --> 00:06:29,460
直接入return

275
00:06:29,460 --> 00:06:30,380
console.log

276
00:06:30,380 --> 00:06:33,100
那么这个就是发送失败

277
00:06:33,100 --> 00:06:34,900
发送失败

278
00:06:34,900 --> 00:06:37,260
失败的原因是什么呢

279
00:06:37,260 --> 00:06:39,080
那么这个就是data里面的

280
00:06:39,080 --> 00:06:40,060
这个叫message

281
00:06:40,060 --> 00:06:41,340
好了如果没有问题

282
00:06:41,340 --> 00:06:42,220
慢慢往后执行

283
00:06:42,220 --> 00:06:42,900
那就证明就是说

284
00:06:42,900 --> 00:06:43,840
success成功了

285
00:06:43,840 --> 00:06:44,980
那么成功的话

286
00:06:44,980 --> 00:06:45,860
我们待会就给它

287
00:06:45,860 --> 00:06:47,060
输出一条消息啊

288
00:06:47,060 --> 00:06:47,800
输出一条消息

289
00:06:47,800 --> 00:06:49,620
就是我们也可以提示用户

290
00:06:49,620 --> 00:06:51,980
就是说这个发送成功了

291
00:06:51,980 --> 00:06:53,380
发送成功也可以这样

292
00:06:53,380 --> 00:06:54,940
当然如果说成功

293
00:06:54,940 --> 00:06:56,240
成功的话我们叫输出啊

294
00:06:56,240 --> 00:06:58,020
就是谁谁对你说啊

295
00:06:58,020 --> 00:06:58,520
所以说这个时候

296
00:06:58,520 --> 00:07:01,140
我们家里就是Date.

297
00:07:01,140 --> 00:07:03,140
就是这个时候我们家里就得有一个

298
00:07:03,140 --> 00:07:04,040
这个用户是谁啊

299
00:07:04,040 --> 00:07:05,120
所以说他有个拟称

300
00:07:05,120 --> 00:07:06,680
那么就是Date里面这个拟称

301
00:07:06,680 --> 00:07:07,980
Date.Nicname

302
00:07:07,980 --> 00:07:09,120
好了

303
00:07:09,120 --> 00:07:10,280
Date.Nicname

304
00:07:10,280 --> 00:07:12,780
然后他对你说啊

305
00:07:12,780 --> 00:07:13,620
对你说的什么呢

306
00:07:13,620 --> 00:07:14,420
大乎过起来

307
00:07:14,420 --> 00:07:15,560
然后是data.message

308
00:07:15,560 --> 00:07:18,040
就是说哪个用户对你说

309
00:07:18,040 --> 00:07:19,100
说的什么消息

310
00:07:19,100 --> 00:07:22,080
然后这个用户在这就收到了这个消息

311
00:07:22,080 --> 00:07:25,220
然后这样完了以后

312
00:07:25,220 --> 00:07:26,040
然后加拿来这个时候

313
00:07:26,040 --> 00:07:27,340
我们就可以来处理

314
00:07:27,340 --> 00:07:28,480
我们就可以来测试了

315
00:07:28,480 --> 00:07:29,180
我们来测试一下

316
00:07:29,180 --> 00:07:30,220
打个控制台

317
00:07:30,220 --> 00:07:32,140
然后这个时候同样的

318
00:07:32,140 --> 00:07:33,620
我们把这个扣端全部给它关掉

319
00:07:33,620 --> 00:07:36,440
全部关闭一下

320
00:07:36,440 --> 00:07:39,620
然后这个时候

321
00:07:39,620 --> 00:07:40,560
node.client

322
00:07:40,560 --> 00:07:43,180
我们可以看到这里有一个报错

323
00:07:43,180 --> 00:07:45,020
在我们client.js第27行

324
00:07:45,020 --> 00:07:46,260
我们看一下具体原因

325
00:07:46,260 --> 00:07:48,140
第27行

326
00:07:48,140 --> 00:07:48,980
我们来找一找

327
00:07:48,980 --> 00:07:50,700
它说在这里有一个报错

328
00:07:50,700 --> 00:07:53,740
这是一个正则表达式

329
00:07:53,740 --> 00:07:54,720
这个data

330
00:07:54,720 --> 00:07:57,460
如果match in client.write

331
00:07:57,460 --> 00:07:58,960
json.screen file

332
00:07:58,960 --> 00:07:59,800
是这个对象

333
00:07:59,800 --> 00:08:02,620
我们可以看到这里是

334
00:08:02,620 --> 00:08:04,100
用的是中文的括号

335
00:08:04,100 --> 00:08:05,800
应该是英文的括号

336
00:08:05,800 --> 00:08:08,640
然后再回来

337
00:08:08,640 --> 00:08:09,140
然后

338
00:08:09,140 --> 00:08:11,720
node.client

339
00:08:11,720 --> 00:08:13,500
还有个错误

340
00:08:13,500 --> 00:08:15,080
这里丢失了一个货号

341
00:08:15,080 --> 00:08:17,000
在这个我们看下第31行

342
00:08:17,000 --> 00:08:17,660
client.js

343
00:08:17,660 --> 00:08:19,700
我们来看一下第31行

344
00:08:19,700 --> 00:08:20,280
它说在这里

345
00:08:20,280 --> 00:08:21,800
我们这里丢失了一个货号

346
00:08:21,800 --> 00:08:23,260
再保存一下

347
00:08:23,260 --> 00:08:25,180
然后再回来

348
00:08:25,180 --> 00:08:26,500
NodeClient

349
00:08:26,500 --> 00:08:27,720
好了这回就对了

350
00:08:27,720 --> 00:08:29,000
我们再来输入这个捏称

351
00:08:29,000 --> 00:08:29,740
Ludec

352
00:08:29,740 --> 00:08:31,940
好了这里同样的

353
00:08:31,940 --> 00:08:33,240
NodeClient

354
00:08:33,240 --> 00:08:34,860
那这里也让输入捏称

355
00:08:34,860 --> 00:08:35,320
Mac

356
00:08:35,320 --> 00:08:36,720
好这个用户

357
00:08:36,720 --> 00:08:38,580
同样的NodeClient

358
00:08:38,580 --> 00:08:39,540
Rose

359
00:08:39,540 --> 00:08:41,140
好这个时候我们来测试一下

360
00:08:41,140 --> 00:08:42,020
Rose在这 hello

361
00:08:42,020 --> 00:08:44,240
Mac也在这 hello

362
00:08:44,240 --> 00:08:45,780
Jack也在这 hello

363
00:08:45,780 --> 00:08:47,200
我们可以确信

364
00:08:47,200 --> 00:08:48,500
就是现在有三个用户

365
00:08:48,500 --> 00:08:49,480
这是Jack

366
00:08:49,480 --> 00:08:50,220
这个是Mac

367
00:08:50,220 --> 00:08:50,820
这个是Rose

368
00:08:50,820 --> 00:08:51,860
然后接下来

369
00:08:51,860 --> 00:08:53,380
现在他们发的消息的默认都是群聊

370
00:08:53,380 --> 00:08:55,320
然后接下来我们让他来私聊

371
00:08:55,320 --> 00:08:56,660
例如我们让这个Jack

372
00:08:56,660 --> 00:08:57,800
对这个Rose说一个hello

373
00:08:57,800 --> 00:08:58,680
我们来看一下

374
00:08:58,680 --> 00:09:00,880
这个时候你只需要按照我们制定的这个数据格式

375
00:09:00,880 --> 00:09:01,260
app

376
00:09:01,260 --> 00:09:03,200
然后是这个叫Rose

377
00:09:03,200 --> 00:09:04,120
碰格

378
00:09:04,120 --> 00:09:04,700
hello

379
00:09:04,700 --> 00:09:07,880
那么这个就表示我们要跟Rose说一个hello

380
00:09:07,880 --> 00:09:08,540
我们回车

381
00:09:08,540 --> 00:09:11,040
我们大家可以看到这里说发送失败

382
00:09:11,040 --> 00:09:12,520
该用户不存在

383
00:09:12,520 --> 00:09:14,180
那这个是什么原因呢

384
00:09:14,180 --> 00:09:15,220
接下来我们来检查一下

385
00:09:15,220 --> 00:09:17,800
首先我们来看一下这个客户端

386
00:09:17,800 --> 00:09:19,200
在这这个私聊

387
00:09:19,200 --> 00:09:20,400
Tabs P2P

388
00:09:20,400 --> 00:09:21,220
Matches 1

389
00:09:21,220 --> 00:09:23,140
这个Matches 1呢

390
00:09:23,140 --> 00:09:24,620
应该是这个用户的昵称

391
00:09:24,620 --> 00:09:27,100
然后当然我们这里就要去验证了

392
00:09:27,100 --> 00:09:28,520
我们看一下这个NickName到底是什么

393
00:09:28,520 --> 00:09:29,340
到底是什么

394
00:09:29,340 --> 00:09:31,720
所以说我们这样

395
00:09:31,720 --> 00:09:34,860
复制一下

396
00:09:34,860 --> 00:09:38,760
我们打印一下这个对象

397
00:09:38,760 --> 00:09:39,900
这个是发送的这个

398
00:09:39,900 --> 00:09:43,140
或者说我们在这里接收的时候答应一下就可以

399
00:09:43,140 --> 00:09:44,380
也是一样的

400
00:09:44,380 --> 00:09:46,540
好我们输出这个

401
00:09:46,540 --> 00:09:48,780
来看一下啊

402
00:09:48,780 --> 00:09:50,280
输出这个叫data near

403
00:09:50,280 --> 00:09:52,700
当然这个时候我们得

404
00:09:52,700 --> 00:09:54,720
再重新来一下啊

405
00:09:54,720 --> 00:09:55,440
输入昵称

406
00:09:55,440 --> 00:09:57,980
Jack at rose

407
00:09:57,980 --> 00:09:59,140
当然这个时候还没有这个rose

408
00:09:59,140 --> 00:10:01,120
我们主要看一下这个发送数据

409
00:10:01,120 --> 00:10:04,580
我们看一下服务端这里收到一个type是2

410
00:10:04,580 --> 00:10:06,680
然后没有别的消息了

411
00:10:06,680 --> 00:10:07,800
那这就说明有问题了

412
00:10:07,800 --> 00:10:08,500
好了

413
00:10:08,500 --> 00:10:10,700
让我们回过来来检查一下怎么回事

414
00:10:10,700 --> 00:10:12,840
我们这个test

415
00:10:12,840 --> 00:10:15,320
我们这里应该用exec这个方法

416
00:10:15,320 --> 00:10:17,820
因为test这个测试方法

417
00:10:17,820 --> 00:10:18,720
就是这个匹配方法

418
00:10:18,720 --> 00:10:19,900
它只测试

419
00:10:19,900 --> 00:10:21,160
只测试就是说是否匹配

420
00:10:21,160 --> 00:10:22,160
但是不会分组

421
00:10:22,160 --> 00:10:24,540
这种表达是exec这个方法才会进行分组

422
00:10:24,540 --> 00:10:25,360
所以是这样

423
00:10:25,360 --> 00:10:27,420
好 然后这个时候我们再来

424
00:10:27,420 --> 00:10:28,840
我们再来

425
00:10:28,840 --> 00:10:32,300
ad rose

426
00:10:32,300 --> 00:10:34,980
好 这个时候我们再来就可以看到

427
00:10:34,980 --> 00:10:36,140
type12 nickname message

428
00:10:36,140 --> 00:10:36,820
这时候都有了

429
00:10:36,820 --> 00:10:38,540
好了这个时候就对了

430
00:10:38,540 --> 00:10:39,860
好了没有问题以后

431
00:10:39,860 --> 00:10:40,800
然后这个时候我们再来错

432
00:10:40,800 --> 00:10:42,380
这个时候三个客户端

433
00:10:42,380 --> 00:10:45,480
我们一个一个来

434
00:10:45,480 --> 00:10:46,140
Rose

435
00:10:46,140 --> 00:10:49,340
Mac

436
00:10:49,340 --> 00:10:52,220
Jack

437
00:10:52,220 --> 00:10:53,560
三个用户都进来了

438
00:10:53,560 --> 00:10:54,300
请输入名称

439
00:10:54,300 --> 00:10:58,040
这个失败的原因是什么

440
00:10:58,040 --> 00:10:58,720
我再来说一下

441
00:10:58,720 --> 00:11:00,140
就是说刚才你这个客户端

442
00:11:00,140 --> 00:11:01,420
就是关了又重开了一下

443
00:11:01,420 --> 00:11:02,660
实际上就是说客户端

444
00:11:02,660 --> 00:11:04,020
应该把刚才掉线的用户

445
00:11:04,020 --> 00:11:04,780
去把它清除掉

446
00:11:04,780 --> 00:11:05,640
是这个原因

447
00:11:05,640 --> 00:11:06,880
这个做一个细节优化

448
00:11:06,880 --> 00:11:07,600
我们待会再来说

449
00:11:07,600 --> 00:11:09,500
现在呢我们先再输入一个其他的名字

450
00:11:09,500 --> 00:11:10,440
这个叫做Jack1

451
00:11:10,440 --> 00:11:11,460
当中成功

452
00:11:11,460 --> 00:11:12,780
然后接下来这个时候

453
00:11:12,780 --> 00:11:14,220
我们再来打个招呼

454
00:11:14,220 --> 00:11:16,200
当然这个确实也不行

455
00:11:16,200 --> 00:11:18,020
还是因为就是那个用户没有了

456
00:11:18,020 --> 00:11:19,300
所以说也没法去给它发消息了

457
00:11:19,300 --> 00:11:21,180
我们待会再处理这个掉线的问题

458
00:11:21,180 --> 00:11:23,320
掉线之后就不应该再给它发了

459
00:11:23,320 --> 00:11:25,980
我们这个目前应该是这样的

460
00:11:25,980 --> 00:11:29,100
我们的ClientJack

461
00:11:29,100 --> 00:11:32,280
Mac

462
00:11:32,280 --> 00:11:36,440
好 我们待会来看一下

463
00:11:36,440 --> 00:11:37,140
这个Jack在这

464
00:11:37,140 --> 00:11:38,780
Hello 大家都看到了

465
00:11:38,780 --> 00:11:40,760
Mac Hello 没有问题

466
00:11:40,760 --> 00:11:41,320
然后Rose

467
00:11:41,320 --> 00:11:42,260
Hello OK

468
00:11:42,260 --> 00:11:44,080
好 然后接下来让Rose对这个Jack说

469
00:11:44,080 --> 00:11:45,660
那就atJack Hello

470
00:11:45,660 --> 00:11:46,460
我们回车

471
00:11:46,460 --> 00:11:48,560
我们在这此时就可以看到

472
00:11:48,560 --> 00:11:49,560
此时就可以看到

473
00:11:49,560 --> 00:11:51,520
这个Jack在这是不是就收到了

474
00:11:51,520 --> 00:11:52,880
Jack对你说 Hello

475
00:11:52,880 --> 00:11:55,060
那就证明我们这个是不是

476
00:11:55,060 --> 00:11:56,380
私聊就已经OK了呀

477
00:11:56,380 --> 00:11:57,060
Jack对你说

478
00:11:57,060 --> 00:11:59,620
好了 那接下来我们Jack既然

479
00:11:59,620 --> 00:12:01,520
这个是Jack对你说

480
00:12:01,520 --> 00:12:02,580
啊这个不对应该是

481
00:12:02,580 --> 00:12:04,380
我看一下应该是Rose对你说啊

482
00:12:04,380 --> 00:12:05,660
我们看这个昵称应该写错了啊

483
00:12:05,660 --> 00:12:07,980
我们来看一下这个数据

484
00:12:07,980 --> 00:12:09,820
nickname啊

485
00:12:09,820 --> 00:12:10,280
nickname

486
00:12:10,280 --> 00:12:12,920
看一下我们刚才at的是

487
00:12:12,920 --> 00:12:13,860
Jack

488
00:12:13,860 --> 00:12:15,680
对啊表示我们要

489
00:12:15,680 --> 00:12:17,480
呃告诉这个Jack

490
00:12:17,480 --> 00:12:19,860
啊所以这个nickname呢

491
00:12:19,860 --> 00:12:21,820
应该是当前这个用户啊

492
00:12:21,820 --> 00:12:23,520
所以说这里还不应该是这个data nickname

493
00:12:23,520 --> 00:12:26,180
应该是个叫client stock的nickname啊

494
00:12:26,180 --> 00:12:27,980
当前发消息的这个用户的昵称啊

495
00:12:27,980 --> 00:12:28,520
应该是他

496
00:12:28,520 --> 00:12:31,280
好了啊这个时候我们再来测试啊

497
00:12:31,520 --> 00:12:32,680
当然由于服务端啊

498
00:12:32,680 --> 00:12:34,360
在这儿我们到重新连接

499
00:12:34,360 --> 00:12:36,360
重新连接

500
00:12:36,360 --> 00:12:37,420
Rose

501
00:12:37,420 --> 00:12:38,840
Mac

502
00:12:38,840 --> 00:12:40,160
Jack

503
00:12:40,160 --> 00:12:41,460
好了我们再来看一下

504
00:12:41,460 --> 00:12:42,640
让这个Rose对Jack说

505
00:12:42,640 --> 00:12:43,180
hello

506
00:12:43,180 --> 00:12:44,840
你看此时我们再来收到

507
00:12:44,840 --> 00:12:45,460
就是Rose对你说

508
00:12:45,460 --> 00:12:45,980
hello

509
00:12:45,980 --> 00:12:48,420
而这个Jack想回忆一个啊

510
00:12:48,420 --> 00:12:49,700
那就atRose

511
00:12:49,700 --> 00:12:52,040
我们大家就可以看到

512
00:12:52,040 --> 00:12:53,140
Rose是不是就收到

513
00:12:53,140 --> 00:12:53,880
就是Jack对你说

514
00:12:53,880 --> 00:12:55,260
而Mac在中间

515
00:12:55,260 --> 00:12:56,120
他是不是谁的

516
00:12:56,120 --> 00:12:56,960
就是私了消息

517
00:12:56,960 --> 00:12:58,000
Mac是不是都看不见

518
00:12:58,000 --> 00:12:59,960
如果说他在这儿

519
00:12:59,960 --> 00:13:01,080
哈哈

520
00:13:01,080 --> 00:13:02,140
那这个时候是群聊

521
00:13:02,140 --> 00:13:02,820
那群聊的话

522
00:13:02,820 --> 00:13:03,620
所有人都能看见

523
00:13:03,620 --> 00:13:04,500
如果他在这

524
00:13:04,500 --> 00:13:05,300
atmac

525
00:13:05,300 --> 00:13:05,940
哈哈

526
00:13:05,940 --> 00:13:06,560
又回车

527
00:13:06,560 --> 00:13:08,180
我们是不是只有mac能看见

528
00:13:08,180 --> 00:13:08,900
肉思运说哈哈

529
00:13:08,900 --> 00:13:09,540
对吧

530
00:13:09,540 --> 00:13:11,060
而Jack什么也看不见

531
00:13:11,060 --> 00:13:12,540
所以说这样的话呢

532
00:13:12,540 --> 00:13:13,800
我们就实现了我们这个

533
00:13:13,800 --> 00:13:15,400
就是私聊的功能

534
00:13:15,400 --> 00:13:16,640
私聊的功能

535
00:13:16,640 --> 00:13:18,120
接下来我们简单的

536
00:13:18,120 --> 00:13:18,820
就是总结一下

537
00:13:18,820 --> 00:13:19,560
简单回顾一下

538
00:13:19,560 --> 00:13:21,180
我们这个功能的一个实现的过程

539
00:13:21,180 --> 00:13:23,020
我们待会可以来看一下

540
00:13:23,020 --> 00:13:25,060
首先我们对这个数据的格式

541
00:13:25,060 --> 00:13:25,760
做了一个规定

542
00:13:25,760 --> 00:13:27,140
就是说你在这

543
00:13:27,140 --> 00:13:28,040
说出这样的消息

544
00:13:28,040 --> 00:13:29,100
我们就认为是群聊

545
00:13:29,100 --> 00:13:30,740
如果说你@一个用户

546
00:13:30,740 --> 00:13:31,580
然后空格XXX

547
00:13:31,580 --> 00:13:33,100
那我们就认为是私了

548
00:13:33,100 --> 00:13:35,900
所以说我们对数据的用户的输入

549
00:13:35,900 --> 00:13:38,060
做了一个就是这个聊天消息的区分

550
00:13:38,060 --> 00:13:40,020
所以我们在这里

551
00:13:40,020 --> 00:13:41,360
首先在这定义了一个证则表达式

552
00:13:41,360 --> 00:13:42,680
这个证则表达式呢

553
00:13:42,680 --> 00:13:44,120
就能匹配这样的一种数据格式

554
00:13:44,120 --> 00:13:46,240
如果说能验证这个数据

555
00:13:46,240 --> 00:13:47,040
验证成功

556
00:13:47,040 --> 00:13:48,460
这个matches应该是一个数组

557
00:13:48,460 --> 00:13:49,340
如果验证不成功

558
00:13:49,340 --> 00:13:50,220
matches就是no

559
00:13:50,220 --> 00:13:50,900
就什么都没有

560
00:13:50,900 --> 00:13:52,240
所以说我们在这判断一下

561
00:13:52,240 --> 00:13:53,660
如果matches表示验证成功

562
00:13:53,660 --> 00:13:55,660
成功的话证明用户要发的是一个

563
00:13:55,660 --> 00:13:56,780
就是聊天的消息

564
00:13:56,780 --> 00:13:57,960
所以说这个时候

565
00:13:57,960 --> 00:13:58,580
我们在这里就是

566
00:13:58,580 --> 00:13:59,360
client.write

567
00:13:59,360 --> 00:14:00,980
就是类型的就是P2P

568
00:14:00,980 --> 00:14:02,020
nickname就是说

569
00:14:02,020 --> 00:14:02,720
你要发给谁

570
00:14:02,720 --> 00:14:03,460
message就是

571
00:14:03,460 --> 00:14:04,540
你要发送的消息

572
00:14:04,540 --> 00:14:05,340
好了

573
00:14:05,340 --> 00:14:06,680
那服务端收到以后

574
00:14:06,680 --> 00:14:07,540
服务端收到以后

575
00:14:07,540 --> 00:14:09,300
那么服务端在这里呢

576
00:14:09,300 --> 00:14:10,420
就是要去便利

577
00:14:10,420 --> 00:14:12,360
查找你这个用户

578
00:14:12,360 --> 00:14:12,920
所以说在这

579
00:14:12,920 --> 00:14:13,740
users.find

580
00:14:13,740 --> 00:14:14,560
来找

581
00:14:14,560 --> 00:14:15,580
找有没有

582
00:14:15,580 --> 00:14:17,120
你要私聊的这个用户

583
00:14:17,120 --> 00:14:17,900
所以说我们在这盘谈一下

584
00:14:17,900 --> 00:14:18,420
如果没有

585
00:14:18,420 --> 00:14:19,520
就是说你私聊的这个用户

586
00:14:19,520 --> 00:14:20,280
不存在

587
00:14:20,280 --> 00:14:21,640
那么我们就告诉你

588
00:14:21,640 --> 00:14:22,740
该用户不存在

589
00:14:22,740 --> 00:14:23,940
如果说存在的话

590
00:14:23,940 --> 00:14:24,700
我们就给这个用户

591
00:14:24,700 --> 00:14:25,740
发一条消息

592
00:14:25,740 --> 00:14:26,420
这个用户

593
00:14:26,420 --> 00:14:28,660
我们当然想的消息就是这个P2P类型

594
00:14:28,660 --> 00:14:29,960
此时此之类处

595
00:14:29,960 --> 00:14:32,360
Nikname就是说这个消息来自于谁

596
00:14:32,360 --> 00:14:34,360
Message就是这个私聊的消息的内容

597
00:14:34,360 --> 00:14:35,060
那这样的话

598
00:14:35,060 --> 00:14:37,860
我们在这里就实现了这个私聊的功能

599
00:14:37,860 --> 00:14:39,460
所以说这个时候你当然就可以看到

600
00:14:39,460 --> 00:14:41,460
理由Mac对Jack说hello

601
00:14:41,460 --> 00:14:43,480
是不是只有Jack能收到Mac的消息

602
00:14:43,480 --> 00:14:44,880
而Rose在这毫不知情了

603
00:14:44,880 --> 00:14:46,480
因为这是私聊

604
00:14:46,480 --> 00:14:48,880
这个就是我们这个聊天室当中

605
00:14:48,880 --> 00:14:52,820
私聊功能的一个具体的实现过程

