1
00:00:00,000 --> 00:00:03,880
好 这节课我们来看一下集合中一些命令的补充

2
00:00:03,880 --> 00:00:07,140
首先第一个 获取集合中的元素个数

3
00:00:07,140 --> 00:00:13,120
我们首先来看一下我们的s numbers 也就是看一下我们的letters

4
00:00:13,120 --> 00:00:14,620
它总共有哪些元素 比如说c和b

5
00:00:14,620 --> 00:00:19,180
在letters里面它有c和b 那么我们就来看一下它的长度是多少

6
00:00:19,180 --> 00:00:20,400
它的命令是什么呢

7
00:00:20,400 --> 00:00:26,860
s card letters 长度为2 也就是我们集合的它的长度

8
00:00:26,860 --> 00:00:29,100
好 第二个命令呢

9
00:00:29,100 --> 00:00:31,120
进行结合运算并将结果存储

10
00:00:31,120 --> 00:00:31,520
什么意思

11
00:00:31,520 --> 00:00:33,020
什么意思

12
00:00:33,020 --> 00:00:35,220
比如说我们去定义一个元素

13
00:00:35,220 --> 00:00:36,940
s add a

14
00:00:36,940 --> 00:00:38,740
我们定为1 2 3 4

15
00:00:38,740 --> 00:00:43,440
我们再来s add b

16
00:00:43,440 --> 00:00:45,580
3 4

17
00:00:45,580 --> 00:00:47,640
好 那么我们求一下

18
00:00:47,640 --> 00:00:48,580
我们来求一下

19
00:00:48,580 --> 00:00:49,840
比如说我们去s

20
00:00:49,840 --> 00:00:51,580
d-a和b

21
00:00:51,580 --> 00:00:52,620
他们的差级是什么

22
00:00:52,620 --> 00:00:53,220
是不是

23
00:00:53,220 --> 00:00:54,800
他们的差级是不是

24
00:00:54,800 --> 00:00:58,180
A和B他们差几是不是1R啊

25
00:00:58,180 --> 00:00:59,380
S div AB

26
00:00:59,380 --> 00:01:01,900
这里为什么是1呢

27
00:01:01,900 --> 00:01:03,240
我们来看一下

28
00:01:03,240 --> 00:01:04,100
我们来看一下B是什么

29
00:01:04,100 --> 00:01:04,740
我们来看一下

30
00:01:04,740 --> 00:01:08,300
S members B

31
00:01:08,300 --> 00:01:09,460
因为呢在B里面

32
00:01:09,460 --> 00:01:10,580
它呢是234

33
00:01:10,580 --> 00:01:11,700
所有的AB它的一个差几

34
00:01:11,700 --> 00:01:13,340
就是1

35
00:01:13,340 --> 00:01:15,400
那么S div store

36
00:01:15,400 --> 00:01:16,220
它这个命运是什么

37
00:01:16,220 --> 00:01:18,260
它的区别就是前者呢

38
00:01:18,260 --> 00:01:18,460
会

39
00:01:18,460 --> 00:01:20,680
就是说S div store

40
00:01:20,680 --> 00:01:21,740
它不会直接返回结果

41
00:01:21,740 --> 00:01:22,760
而是呢将结果存储在

42
00:01:22,760 --> 00:01:24,520
也就是说把我们的结果

43
00:01:24,520 --> 00:01:27,660
给存起来 用他后面将命叫做store store的仓库存储的意思

44
00:01:27,660 --> 00:01:31,860
那我们就来看一下 你比如说 我们来看一下怎么去使用 比如说我们s.diff

45
00:01:31,860 --> 00:01:32,640
 store

46
00:01:32,640 --> 00:01:34,280
a b

47
00:01:34,280 --> 00:01:38,640
第二个就是我们需要存储的 一个k叫做什么 比如说我们去存储一个叫做abdiff

48
00:01:38,640 --> 00:01:44,320
我们来对比一下ab 得到了我们diff的结果是1 这里我们来看一下abdiff它是什么

49
00:01:44,320 --> 00:01:45,440
s

50
00:01:45,440 --> 00:01:48,680
membersabdiff

51
00:01:48,680 --> 00:01:51,940
大家看到没有 abdiff就已经被存储了 它的值为1

52
00:01:52,280 --> 00:01:56,280
同样的sinter store和sunit store其实和我们的deep store是不是一样的

53
00:01:56,280 --> 00:01:58,680
比如说你去求一个差级或者求一个并级把他的结果给

54
00:01:58,680 --> 00:02:03,420
通过一个k给存起来这里呢就是这样一个命令虽然说很长但是也不难很简单

55
00:02:03,420 --> 00:02:05,980
我们来看一下第三个随机获得集合中的元素

56
00:02:05,980 --> 00:02:08,780
比如说我们的

57
00:02:08,780 --> 00:02:12,980
A那他是1234我们来执行一下随机

58
00:02:12,980 --> 00:02:15,280
srund member

59
00:02:20,280 --> 00:02:22,840
with arcs begin with 哦 它还需要去接收一个

60
00:02:22,840 --> 00:02:24,380
接收一个

61
00:02:24,380 --> 00:02:25,400
count

62
00:02:25,400 --> 00:02:27,700
count是什么意思呢 当它为正数时

63
00:02:27,700 --> 00:02:30,260
会随机从集合中获取count个不重复的元素

64
00:02:30,260 --> 00:02:33,340
如果count的值大于集合中元素的个数 则

65
00:02:33,340 --> 00:02:37,940
它会返回集合中全部的元素 什么意思 count这个参数呢 就是说你需要去随机获得集合中的几个元素

66
00:02:37,940 --> 00:02:40,760
你比如说我们去s run ae

67
00:02:40,760 --> 00:02:46,400
听错了 s run ae

68
00:02:50,240 --> 00:02:53,580
S,打错了,没有1,Srandombo A1

69
00:02:53,580 --> 00:02:57,700
好,大家看到没有,我们获取了一个值为3,再执行次1

70
00:02:57,700 --> 00:03:00,340
再执行次4,再执行次1

71
00:03:00,340 --> 00:03:04,180
4,它是随机获取一个值,那么我们来看一下

72
00:03:04,180 --> 00:03:06,480
L2,此时就是随机获取两个值

73
00:03:06,480 --> 00:03:08,780
3,2,2,4,3,1

74
00:03:08,780 --> 00:03:12,740
好,那么这里就是这样一个命令,那么当Count为复数时

75
00:03:12,740 --> 00:03:14,780
它会随机从集合中获取

76
00:03:14,780 --> 00:03:17,000
Count跟元素,这些元素有可能相同

77
00:03:17,000 --> 00:03:17,680
什么意思

78
00:03:17,680 --> 00:03:19,260
当你看它为正数的时候

79
00:03:19,260 --> 00:03:20,300
它能随机获取几个元素

80
00:03:20,300 --> 00:03:20,900
当你为负数

81
00:03:20,900 --> 00:03:23,020
它也就是说

82
00:03:23,020 --> 00:03:23,980
这些元素可能相同

83
00:03:23,980 --> 00:03:25,040
怎么样理解

84
00:03:25,040 --> 00:03:27,120
如果说为正数

85
00:03:27,120 --> 00:03:27,880
就比如说你去

86
00:03:27,880 --> 00:03:30,020
在箱子里面

87
00:03:30,020 --> 00:03:31,000
是不是摸乒乓球啊

88
00:03:31,000 --> 00:03:32,500
摸出来球就不会拿回去了

89
00:03:32,500 --> 00:03:33,700
球的号码永远不会重复

90
00:03:33,700 --> 00:03:34,860
那么当抗它为负数的时候

91
00:03:34,860 --> 00:03:36,120
那你每摸一次

92
00:03:36,120 --> 00:03:36,740
咱们乒乓球

93
00:03:36,740 --> 00:03:37,980
你都要去把它给放回去重摸

94
00:03:37,980 --> 00:03:39,540
这就是抗它为负数的一个意义

95
00:03:39,540 --> 00:03:40,460
我们来测试一下

96
00:03:40,460 --> 00:03:42,060
比如说我们去负3

97
00:03:42,060 --> 00:03:43,020
大家看到没有

98
00:03:43,020 --> 00:03:44,300
我们2就已经被摸出来两次

99
00:03:44,300 --> 00:03:44,620
对吧

100
00:03:44,620 --> 00:03:46,220
324

101
00:03:46,220 --> 00:03:47,280
413

102
00:03:47,280 --> 00:03:48,420
334

103
00:03:48,420 --> 00:03:49,400
我们的3又重复了

104
00:03:49,400 --> 00:03:49,860
所以说呢

105
00:03:49,860 --> 00:03:50,820
cant为正数和负数

106
00:03:50,820 --> 00:03:51,740
它们的区别就是说

107
00:03:51,740 --> 00:03:53,320
可以重复和不重复

108
00:03:53,320 --> 00:03:54,580
大家看一下第四个命令

109
00:03:54,580 --> 00:03:55,720
从集合中弹出一个元素

110
00:03:55,720 --> 00:03:57,200
这个就简单了

111
00:03:57,200 --> 00:03:57,660
从集合中

112
00:03:57,660 --> 00:03:58,640
就是说我们随机的

113
00:03:58,640 --> 00:03:59,660
去酸出一个元素

114
00:03:59,660 --> 00:04:01,780
比如说我们去S

115
00:04:01,780 --> 00:04:02,640
Spop

116
00:04:02,640 --> 00:04:03,980
我们为什么是随机的酸出

117
00:04:03,980 --> 00:04:05,160
因为集合它是没有顺序的

118
00:04:05,160 --> 00:04:06,300
所以说你去酸出的时候

119
00:04:06,300 --> 00:04:07,800
它肯定是随机的去酸出

120
00:04:07,800 --> 00:04:09,580
比如说我们去SpopA

121
00:04:09,580 --> 00:04:10,440
大家看到没有

122
00:04:10,440 --> 00:04:11,580
我们就把4给酸掉了

123
00:04:11,580 --> 00:04:13,160
比如说我们去看一下SmembersA

124
00:04:13,160 --> 00:04:14,760
它只剩下123

125
00:04:14,760 --> 00:04:15,180
好

126
00:04:15,180 --> 00:04:16,540
这里就是破口这样一个命令

127
00:04:16,540 --> 00:04:19,580
那么我们就来总结一下

128
00:04:19,580 --> 00:04:21,540
这几个一个内容

129
00:04:21,540 --> 00:04:24,940
首先我们刚才

130
00:04:24,940 --> 00:04:27,000
是不是看到我们集合里面的一个

131
00:04:27,000 --> 00:04:31,680
集合里面补充的一个命令

132
00:04:31,680 --> 00:04:33,200
咱们补充了哪些命令呢

133
00:04:33,200 --> 00:04:34,680
第一个是不是获取

134
00:04:34,680 --> 00:04:38,120
获取集合的个数

135
00:04:38,120 --> 00:04:38,920
怎么获取

136
00:04:38,920 --> 00:04:39,780
是不是嫩事

137
00:04:39,780 --> 00:04:40,540
不是吧

138
00:04:40,540 --> 00:04:41,080
Card

139
00:04:41,080 --> 00:04:42,180
Scard

140
00:04:42,180 --> 00:04:43,860
第二个呢

141
00:04:43,860 --> 00:04:44,300
将

142
00:04:44,300 --> 00:04:46,020
结果存储

143
00:04:46,020 --> 00:04:46,780
什么意思

144
00:04:46,780 --> 00:04:47,300
你比如说

145
00:04:47,300 --> 00:04:48,500
我们去算一个交集

146
00:04:48,500 --> 00:04:49,120
sdiff

147
00:04:49,120 --> 00:04:49,900
怎么去存取呢

148
00:04:49,900 --> 00:04:51,160
给它后面加入一个store

149
00:04:51,160 --> 00:04:54,080
同样的我们的交集和并级也是一样的

150
00:04:54,080 --> 00:04:55,060
这里就不去写

151
00:04:55,060 --> 00:04:57,820
咱们又写了一个随机

152
00:04:57,820 --> 00:04:58,900
怎么去随机呢

153
00:04:58,900 --> 00:04:59,440
srunt

154
00:04:59,440 --> 00:05:01,020
runt是什么意思啊

155
00:05:01,020 --> 00:05:01,280
随机

156
00:05:01,280 --> 00:05:01,920
runt什么呢

157
00:05:01,920 --> 00:05:02,500
runt member

158
00:05:02,500 --> 00:05:03,960
大家发现没有

159
00:05:03,960 --> 00:05:06,700
我们这命令是不是和我们的生活中语法的使用

160
00:05:06,700 --> 00:05:07,740
我们人正常的思维

161
00:05:07,740 --> 00:05:08,320
可能也是啊

162
00:05:08,320 --> 00:05:08,480
为什么

163
00:05:08,480 --> 00:05:09,460
因为我们的计算机

164
00:05:09,460 --> 00:05:10,560
它也是人去发明的

165
00:05:10,560 --> 00:05:11,760
所以说这些api在设计的时候

166
00:05:11,760 --> 00:05:13,160
也是按人的思维去设计

167
00:05:13,160 --> 00:05:13,860
比如说你随机

168
00:05:13,860 --> 00:05:15,000
就是我们去run的一个member

169
00:05:15,000 --> 00:05:15,960
随机出来一个member

170
00:05:15,960 --> 00:05:17,580
大家也可以发现我们一些框架里面

171
00:05:17,580 --> 00:05:19,400
它的一些API也是非常的讲究

172
00:05:19,400 --> 00:05:20,080
非常的易度

173
00:05:20,080 --> 00:05:22,480
这里是随机这样一个命令

174
00:05:22,480 --> 00:05:23,020
那么我们

175
00:05:23,020 --> 00:05:25,420
其实在随机里面是不是需要去注意一个问题

176
00:05:25,420 --> 00:05:27,520
那么count是不是可以正负

177
00:05:27,520 --> 00:05:28,880
有正负值

178
00:05:28,880 --> 00:05:31,840
那么正负值它的一个区别是什么呢

179
00:05:31,840 --> 00:05:33,780
正和负它的一个区别是什么

180
00:05:33,780 --> 00:05:37,980
正是不是不能重复

181
00:05:37,980 --> 00:05:39,200
随机的不能重复

182
00:05:39,200 --> 00:05:39,560
对吧

183
00:05:39,560 --> 00:05:39,980
负呢

184
00:05:39,980 --> 00:05:40,520
可以

185
00:05:40,520 --> 00:05:41,000
重复

186
00:05:41,000 --> 00:05:43,080
还记得我刚才觉得乒乓球那一个例子吗

187
00:05:43,080 --> 00:05:44,240
一个可以拿回去

188
00:05:44,240 --> 00:05:44,980
一个不能拿回去

189
00:05:44,980 --> 00:05:46,380
这里就是随机获取元素

190
00:05:46,380 --> 00:05:47,920
还有一个呢

191
00:05:47,920 --> 00:05:49,500
是不是随机弹出

192
00:05:49,500 --> 00:05:50,660
弹出什么意思

193
00:05:50,660 --> 00:05:51,380
是不是就是双出

194
00:05:51,380 --> 00:05:52,560
什么呢

195
00:05:52,560 --> 00:05:53,280
S泡泡

196
00:05:53,280 --> 00:05:53,760
好

197
00:05:53,760 --> 00:05:56,880
这里就是咱们这节课的一个内容

