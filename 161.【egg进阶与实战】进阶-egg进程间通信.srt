1
00:00:00,000 --> 00:00:03,520
好,刚才我们是不是已经简单的介绍了一下agent以及它的一个使用场景

2
00:00:03,520 --> 00:00:06,080
那么我们来看一下进程之间是如何通信的

3
00:00:06,080 --> 00:00:07,300
那么有的同学可能会问了

4
00:00:07,300 --> 00:00:10,320
老师你之前不是讲过漏的街是它的一个进程之间的通信吗

5
00:00:10,320 --> 00:00:10,960
那么为什么还讲

6
00:00:10,960 --> 00:00:14,020
那么我们之前是什么样的通信呢

7
00:00:14,020 --> 00:00:15,800
是不是我们通过work on和send

8
00:00:15,800 --> 00:00:18,340
以及咱们的worker进程了通过process去监听message事件

9
00:00:18,340 --> 00:00:21,600
咱们是不是进行master和worker之间的一个通信

10
00:00:21,600 --> 00:00:24,100
那么这里呢,我们的模型是不是变了呀,同学们

11
00:00:24,100 --> 00:00:26,200
对吧,大家可以看到我们以前

12
00:00:26,200 --> 00:00:27,920
我们以前是不是只有master和worker呀

13
00:00:27,920 --> 00:00:29,480
但是呢,我们现在是不是多了一个agent

14
00:00:29,480 --> 00:00:32,100
那么它那个通信模式会不会有所变化呢

15
00:00:32,100 --> 00:00:35,020
那么以往我们的worker之间的通信是怎么去通信的

16
00:00:35,020 --> 00:00:36,800
大家还记得

17
00:00:36,800 --> 00:00:40,060
因为我们刚才的例子是不是讲了master worker之间的通信

18
00:00:40,060 --> 00:00:42,060
大家还记得worker与worker之间的通信吗

19
00:00:42,060 --> 00:00:43,520
是不是通过master去转发呀

20
00:00:43,520 --> 00:00:44,760
好

21
00:00:44,760 --> 00:00:47,480
那么其实在咱们的edg里面

22
00:00:47,480 --> 00:00:51,240
我们app里面它去封装了一个message这样一个对象

23
00:00:51,240 --> 00:00:53,340
可以去帮助我们进行进程之间的通信

24
00:00:53,340 --> 00:00:54,780
不仅可以用于我们的agent

25
00:00:54,780 --> 00:00:55,960
没有agent的情况呢

26
00:00:55,960 --> 00:00:57,620
我们也可以去使用它来进行通信

27
00:00:57,620 --> 00:00:59,400
我们在message下面

28
00:00:59,400 --> 00:01:00,560
比如说有广播

29
00:01:00,560 --> 00:01:01,380
broadcast

30
00:01:01,380 --> 00:01:02,580
比如说有broadcast

31
00:01:02,580 --> 00:01:04,240
好 我们来看一下

32
00:01:04,240 --> 00:01:06,520
比如说有broadcast

33
00:01:06,520 --> 00:01:10,160
它会发送给所有的agint和app的一个进程

34
00:01:10,160 --> 00:01:11,180
什么意思

35
00:01:11,180 --> 00:01:12,960
也就是说你不管在哪里发起广播

36
00:01:12,960 --> 00:01:13,900
你不管在agint

37
00:01:13,900 --> 00:01:15,320
还是咱们一个app里面

38
00:01:15,320 --> 00:01:15,840
app是谁啊

39
00:01:15,840 --> 00:01:16,580
app是不是只walk

40
00:01:16,580 --> 00:01:19,860
也就是说你不仅仅在秘书那里发消息

41
00:01:19,860 --> 00:01:20,940
咱们的工人那里发消息

42
00:01:20,940 --> 00:01:22,100
大家都可以收到

43
00:01:22,100 --> 00:01:23,740
这里有一个方法叫做send to app

44
00:01:23,740 --> 00:01:24,620
那么很简单

45
00:01:24,620 --> 00:01:25,640
是不是我们把消息发给app

46
00:01:25,640 --> 00:01:26,560
也就是发给工人

47
00:01:26,560 --> 00:01:27,480
那么谁发给工人

48
00:01:27,480 --> 00:01:28,700
工人是不是也可以发给工人

49
00:01:28,700 --> 00:01:29,280
那么秘书呢

50
00:01:29,280 --> 00:01:29,620
也可以

51
00:01:29,620 --> 00:01:30,180
但是呢

52
00:01:30,180 --> 00:01:30,880
他的目标是谁

53
00:01:30,880 --> 00:01:32,600
是工人吧

54
00:01:32,600 --> 00:01:34,300
那么Broadcast广播呢

55
00:01:34,300 --> 00:01:34,940
就是所有人嘛

56
00:01:34,940 --> 00:01:35,200
对吧

57
00:01:35,200 --> 00:01:37,260
那么Sender2的进程

58
00:01:37,260 --> 00:01:38,000
共鸣思义呢

59
00:01:38,000 --> 00:01:38,880
是把消息呢

60
00:01:38,880 --> 00:01:39,340
发给秘书

61
00:01:39,340 --> 00:01:40,900
那么SenderRandom呢

62
00:01:40,900 --> 00:01:41,300
随机发

63
00:01:41,300 --> 00:01:42,960
那么随机发是发给谁

64
00:01:42,960 --> 00:01:44,940
随机发其实发给一个APP进程

65
00:01:44,940 --> 00:01:46,420
为什么呀

66
00:01:46,420 --> 00:01:47,500
为什么不能随机发给秘书

67
00:01:47,500 --> 00:01:48,960
因为秘书不需要随机发

68
00:01:48,960 --> 00:01:50,000
因为我们只有唯一的一个秘书

69
00:01:50,000 --> 00:01:50,860
但是我们的Worker有很多

70
00:01:50,860 --> 00:01:52,380
那么这里呢

71
00:01:52,380 --> 00:01:53,320
有一个Sender2的方法

72
00:01:53,320 --> 00:01:53,920
做什么用呢

73
00:01:53,920 --> 00:01:55,700
可以发送给子弟的进程

74
00:01:55,700 --> 00:01:58,760
也就是说你如果说知道一个公认的名字

75
00:01:58,760 --> 00:01:59,800
你可以直接给他打电话

76
00:01:59,800 --> 00:02:00,880
就是这么一个意思

77
00:02:00,880 --> 00:02:01,640
好 这里呢

78
00:02:01,640 --> 00:02:03,460
我就来给同学们来简单的去演示一下

79
00:02:03,460 --> 00:02:05,700
我们去发送消息的一个方法

80
00:02:05,700 --> 00:02:07,460
我们来通过

81
00:02:07,460 --> 00:02:08,120
不过这里呢

82
00:02:08,120 --> 00:02:09,220
需要同学们去稍微注意一下

83
00:02:09,220 --> 00:02:12,520
我们的APP上面是不是有Message这样一个对象

84
00:02:12,520 --> 00:02:14,320
其实在咱们的进展上面也有这样一个对象

85
00:02:14,320 --> 00:02:16,460
他们两个是一模一样的

86
00:02:16,460 --> 00:02:16,920
不分彼此

87
00:02:16,920 --> 00:02:18,360
好 这里呢

88
00:02:18,360 --> 00:02:19,020
我们就来演示一下

89
00:02:19,020 --> 00:02:19,520
首先呢

90
00:02:19,520 --> 00:02:20,180
我们来演示一下

91
00:02:20,180 --> 00:02:21,700
Broadcast广播

92
00:02:21,700 --> 00:02:23,320
发送给所有人这样一个方法

93
00:02:23,320 --> 00:02:24,580
首先这里呢

94
00:02:24,580 --> 00:02:25,460
需要大家去注意一点

95
00:02:25,460 --> 00:02:28,980
在 agint 里面发消息

96
00:02:28,980 --> 00:02:34,060
必须 必须在 1gg ready 在你的生命周期里面

97
00:02:34,060 --> 00:02:42,780
也就说 咱们在 agint 里面发消息 你必须在 1gg ready 这个事件发生之后

98
00:02:42,780 --> 00:02:43,840
 你才能够去发送 为什么

99
00:02:43,840 --> 00:02:47,500
因为 1gg ready 是不代表我们的应用程序准备好了 出售化成功了 你才能去发

100
00:02:47,500 --> 00:02:49,140
 如果说你的应用程序没有出售化成功

101
00:02:49,140 --> 00:02:54,100
那么你是无法发送 对吧 所以呢 这里我们需要重点 去注意一下 好 那么我们就在这一个事件里面

102
00:02:54,500 --> 00:02:56,540
啊也就是咱们的是不是咱们触发成功之后

103
00:02:56,540 --> 00:02:57,580
我们来发起一个

104
00:02:57,580 --> 00:02:59,100
消息比如说

105
00:02:59,100 --> 00:03:00,640
在镜头上面是不是有一个

106
00:03:00,640 --> 00:03:01,660
Message在那个

107
00:03:01,660 --> 00:03:02,180
对象

108
00:03:02,180 --> 00:03:02,940
我们

109
00:03:02,940 --> 00:03:05,760
调用他一个什么方法比如说我们要把消息发给所有人是不是

110
00:03:05,760 --> 00:03:06,540
Broad

111
00:03:06,540 --> 00:03:08,060
Broadcast

112
00:03:08,060 --> 00:03:09,100
对吧

113
00:03:09,100 --> 00:03:11,900
那么怎么样使用呢比如说我们先去定义一个广播的名称

114
00:03:11,900 --> 00:03:14,720
广播的名称比如说我们去发送给所有人叫做Wall

115
00:03:14,720 --> 00:03:16,780
那么我发送的数据是什么呢

116
00:03:16,780 --> 00:03:18,300
数据就是

117
00:03:18,300 --> 00:03:20,620
啊

118
00:03:20,620 --> 00:03:21,380
大家

119
00:03:23,180 --> 00:03:27,740
打工资很开心的一件事情是吧 好 那么我们来看一下 我们的我可能不能够给收到

120
00:03:27,740 --> 00:03:34,020
我们的我可是不是就是我们的app.js啊 是不是我们的我可搬转的好 那么我们在哪里去监听这一个事件呢

121
00:03:34,020 --> 00:03:34,280
 当然来

122
00:03:34,280 --> 00:03:39,540
大家思考一下 那思考一下我们的app在哪里去监听事件 是不是在我们的contract里面对吧

123
00:03:39,540 --> 00:03:45,300
 因为contract里面是不是app的一个实力 所以我们调用app.怎么样是不是messenger.

124
00:03:45,300 --> 00:03:50,020
嗯吧 对吧 因为我们监听事件是我们监听谁呢 是不是监听我

125
00:03:51,080 --> 00:03:51,840
好 我们来看一下

126
00:03:51,840 --> 00:03:54,400
这里呢 它呢 接受一个回调

127
00:03:54,400 --> 00:04:00,040
我们来看一下 我们把数据打印出来 看看咱们有没有接收到涨工资的这样一个消息

128
00:04:00,040 --> 00:04:00,300
 对吧

129
00:04:00,300 --> 00:04:07,200
好 我们来重启一下我们的应用

130
00:04:07,200 --> 00:04:11,560
好 大家可以看到

131
00:04:11,560 --> 00:04:13,600
大家可以看到

132
00:04:13,600 --> 00:04:16,940
是不是咱们的工人是不是也收到消息 大家涨工资了 对吧

133
00:04:16,940 --> 00:04:19,760
而且呢 是不是在咱们的agint运行之后啊

134
00:04:20,000 --> 00:04:22,560
所以说咱们的进的运行之后发送一个消息

135
00:04:22,560 --> 00:04:25,340
哈喽大家哎你们长工资啊很开心对吧好

136
00:04:25,340 --> 00:04:29,600
这里呢就是啊给同学去演示一个啊咱们进程之间通信的一个模型

137
00:04:29,600 --> 00:04:31,640
所以说我们如果说想在进程之间进行通信

138
00:04:31,640 --> 00:04:33,840
大家一定要使用啊买神买自己的对象

139
00:04:33,840 --> 00:04:36,200
是不是比我们之前通过啊这样一个什么啊

140
00:04:36,200 --> 00:04:38,040
percent on 这样是不是方便很多

141
00:04:38,040 --> 00:04:41,040
好那么这里呢就是我们啊这节课的内容

142
00:04:41,040 --> 00:04:42,040
后面呢还有一些api

143
00:04:42,040 --> 00:04:43,600
你比如说啊剩的去我app

144
00:04:43,600 --> 00:04:45,600
那么剩的去我庭的剩的什么软的

145
00:04:45,600 --> 00:04:47,640
其实这里呢和咱们的broadcast使用是差不多的

146
00:04:47,640 --> 00:04:49,340
这里呢就不给我们去一个一个去演示

147
00:04:49,340 --> 00:04:51,280
同学们可以下去之后自己去领问一下

148
00:04:51,280 --> 00:04:53,320
那么我们来总结一下这期课的内容

149
00:04:53,320 --> 00:04:56,660
其实我们这期课是不是主要讲解了什么

150
00:04:56,660 --> 00:04:58,300
是不是讲解了我们的通信过程

151
00:04:58,300 --> 00:04:59,920
进程通信

152
00:04:59,920 --> 00:05:00,840
那么进程通信

153
00:05:00,840 --> 00:05:01,840
我们用了一个核心

154
00:05:01,840 --> 00:05:02,860
核心的对象是什么

155
00:05:02,860 --> 00:05:05,600
是不是咱们用了一个叫做Message

156
00:05:05,600 --> 00:05:06,620
这样一个对象

157
00:05:06,620 --> 00:05:07,700
那么它有一个什么特点

158
00:05:07,700 --> 00:05:12,840
特点是不是挂载在APP和进台下面

159
00:05:12,840 --> 00:05:14,640
那么我们怎么样去发送数据

160
00:05:14,640 --> 00:05:17,000
我们是不是可以通过什么呀

161
00:05:17,000 --> 00:05:17,540
它呢

162
00:05:17,540 --> 00:05:19,100
怎么是不是支持哪些API

163
00:05:19,100 --> 00:05:21,220
比如说我们可以通过罗的

164
00:05:21,220 --> 00:05:23,260
罗的cast

165
00:05:23,260 --> 00:05:24,400
是不是可以广播给所有人

166
00:05:24,400 --> 00:05:24,700
对吧

167
00:05:24,700 --> 00:05:26,080
所有人

168
00:05:26,080 --> 00:05:27,820
而且我们还是不是还可以去

169
00:05:27,820 --> 00:05:29,120
还可以去圣的

170
00:05:29,120 --> 00:05:30,620
圣得到我们某一个app啊

171
00:05:30,620 --> 00:05:32,040
比如说我们圣得到app

172
00:05:32,040 --> 00:05:33,300
传给我们的工人

173
00:05:33,300 --> 00:05:34,100
还可以呢

174
00:05:34,100 --> 00:05:37,440
是不是还可以去发送给我们的秘书

175
00:05:37,440 --> 00:05:38,500
而且呢

176
00:05:38,500 --> 00:05:40,760
是不是还可以随机的发送给我们某一个工人呢

177
00:05:40,760 --> 00:05:41,820
也就是说圣的

178
00:05:41,820 --> 00:05:43,320
乱等随机

179
00:05:43,320 --> 00:05:44,260
而且呢还可以

180
00:05:44,260 --> 00:05:45,440
比如说你知道一个工人的名字

181
00:05:45,440 --> 00:05:46,280
你还可以啊

182
00:05:46,280 --> 00:05:46,720
生的

183
00:05:46,720 --> 00:05:47,940
好

184
00:05:47,940 --> 00:05:48,920
这里呢就是我们

185
00:05:48,920 --> 00:05:50,640
进程间通讯这几个内容

