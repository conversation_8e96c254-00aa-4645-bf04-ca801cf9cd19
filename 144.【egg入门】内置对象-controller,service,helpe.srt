1
00:00:00,000 --> 00:00:01,880
好 我们接下来来看一下controller

2
00:00:01,880 --> 00:00:04,180
那么框架呢 它提供了一个controller这样的一个机内

3
00:00:04,180 --> 00:00:06,780
并且它推荐所有的咱们的一个控制器都继承

4
00:00:06,780 --> 00:00:08,180
都继承于该机内去实现

5
00:00:08,180 --> 00:00:10,240
那么这个类呢 它下面有这样一些属性

6
00:00:10,240 --> 00:00:13,280
比如说contact app config service和logger

7
00:00:13,280 --> 00:00:16,360
那么在咱们的一个controller文件里面

8
00:00:16,360 --> 00:00:18,720
可以通过两种方式来引用咱们的一个controller一个机内

9
00:00:18,720 --> 00:00:19,660
比如说第一种

10
00:00:19,660 --> 00:00:21,740
第一种呢 咱们去require

11
00:00:21,740 --> 00:00:25,580
咱们去引用1GG上面是不是有一个controller这样的一个属性呢

12
00:00:25,580 --> 00:00:28,280
那么它那个值呢 其实就是它的就是controller这样的一个类

13
00:00:28,280 --> 00:00:29,920
那么我们通过你的一个

14
00:00:29,920 --> 00:00:31,920
你要实现的一个控制器

15
00:00:31,920 --> 00:00:32,800
然后去继承它

16
00:00:32,800 --> 00:00:34,080
那么我们刚才所举的例子

17
00:00:34,080 --> 00:00:35,100
是不是都是通过这样一种方式

18
00:00:35,100 --> 00:00:36,540
你比如说我们有个home这样的逻辑

19
00:00:36,540 --> 00:00:36,980
咱们去

20
00:00:36,980 --> 00:00:38,160
咱们是不是去继承了

21
00:00:38,160 --> 00:00:40,360
是不是继承了1G上面的一个控制器

22
00:00:40,360 --> 00:00:42,380
那么这是第一种方式

23
00:00:42,380 --> 00:00:43,800
那么其实还有另外一种方式是什么呢

24
00:00:43,800 --> 00:00:45,140
你比如说我们module.export

25
00:00:45,140 --> 00:00:46,440
其实也可以接受一个函数

26
00:00:46,440 --> 00:00:48,100
那么contrl刚才讲到了

27
00:00:48,100 --> 00:00:50,500
是不是它也在APP的实力上面

28
00:00:50,500 --> 00:00:51,440
是不是也有contrl了这样一个

29
00:00:51,440 --> 00:00:52,020
那样

30
00:00:52,020 --> 00:00:53,500
那么contrl它的作用

31
00:00:53,500 --> 00:00:54,620
我们之前是不是已经介绍很多了

32
00:00:54,620 --> 00:00:55,620
这里老师就不再去啰嗦

33
00:00:55,620 --> 00:00:56,620
好

34
00:00:56,620 --> 00:00:57,640
那么我们接下来来看一下service

35
00:00:57,640 --> 00:00:58,680
那么框架呢

36
00:00:58,680 --> 00:00:59,860
它同样呢

37
00:00:59,860 --> 00:01:00,800
也提供了一个service的积累

38
00:01:00,800 --> 00:01:02,280
它其实和controller是非常类似的

39
00:01:02,280 --> 00:01:02,780
你可以呢

40
00:01:02,780 --> 00:01:04,000
通过两种方式去创建它

41
00:01:04,000 --> 00:01:04,720
你比如说

42
00:01:04,720 --> 00:01:05,780
是不是也是可以通过

43
00:01:05,780 --> 00:01:07,600
你比如说1G上面有一个service这样的内

44
00:01:07,600 --> 00:01:08,580
那么同样的app上面

45
00:01:08,580 --> 00:01:10,580
也有和咱们的controller完全是一样的

46
00:01:10,580 --> 00:01:11,540
那么具体的service怎么写

47
00:01:11,540 --> 00:01:12,700
我们后面内容会去具体介绍

48
00:01:12,700 --> 00:01:13,900
因为我们这几个是不是主要就是讲

49
00:01:13,900 --> 00:01:15,020
去介绍1G里面

50
00:01:15,020 --> 00:01:16,000
它有哪些的内置对象

51
00:01:16,000 --> 00:01:16,580
好

52
00:01:16,580 --> 00:01:17,580
那么接下来看一下黑暗

53
00:01:17,580 --> 00:01:18,900
黑暗是不是一个

54
00:01:18,900 --> 00:01:20,500
非常有用的一个工具函数

55
00:01:20,500 --> 00:01:22,260
一些工具方法

56
00:01:22,260 --> 00:01:24,400
那么我们之前也举过一个例子

57
00:01:24,400 --> 00:01:25,060
比如说在模板里面

58
00:01:25,060 --> 00:01:25,900
我们去调用一些方法

59
00:01:25,900 --> 00:01:26,320
对吧

60
00:01:26,320 --> 00:01:28,000
大家还记不记得我们的

61
00:01:28,000 --> 00:01:28,620
我们怎么使用的

62
00:01:28,620 --> 00:01:29,580
是不是咱们在extend里面

63
00:01:29,580 --> 00:01:30,700
去写个help.js

64
00:01:30,700 --> 00:01:32,640
比如说我们去get一个data

65
00:01:32,640 --> 00:01:34,300
然后咱们在模板里面

66
00:01:34,300 --> 00:01:35,400
是不是就可以直接去使用它

67
00:01:35,400 --> 00:01:36,120
对吧

68
00:01:36,120 --> 00:01:37,720
help.get data

69
00:01:37,720 --> 00:01:39,660
这里是一个模板里面

70
00:01:39,660 --> 00:01:40,380
可以去直接使用

71
00:01:40,380 --> 00:01:41,000
而且呢

72
00:01:41,000 --> 00:01:42,440
而且呢

73
00:01:42,440 --> 00:01:43,840
help的实力也可以了

74
00:01:43,840 --> 00:01:44,540
在咱们的康

75
00:01:44,540 --> 00:01:45,560
在咱们的康泰上面

76
00:01:45,560 --> 00:01:47,060
其实也可以防止我们的对象

77
00:01:47,060 --> 00:01:47,660
这里呢

78
00:01:47,660 --> 00:01:49,580
我老师就不给同学们去演示了

79
00:01:49,580 --> 00:01:50,300
大家可以自己下去

80
00:01:50,300 --> 00:01:51,120
验证一下

81
00:01:51,120 --> 00:01:51,960
是不是怎么回事

82
00:01:51,960 --> 00:01:54,040
包括还会提供一个config对象

83
00:01:54,040 --> 00:01:55,280
因为刚才讲到我们目录里面

84
00:01:55,280 --> 00:01:56,980
是不是有配置这样一个目录

85
00:01:56,980 --> 00:01:57,460
所以说呢

86
00:01:57,460 --> 00:01:59,720
在APP下面其实也有

87
00:01:59,720 --> 00:02:01,580
会有config这样的一个

88
00:02:01,580 --> 00:02:03,780
这样的一个配置文件的一个对象

89
00:02:03,780 --> 00:02:05,900
那么config其实它的内容还比较多

90
00:02:05,900 --> 00:02:07,760
那么我们会在后面的内容呢

91
00:02:07,760 --> 00:02:09,360
会专门设立一节课来讲解

92
00:02:09,360 --> 00:02:10,060
什么是config

93
00:02:10,060 --> 00:02:11,220
好这里呢就是我们

94
00:02:11,220 --> 00:02:12,500
所有的类似

95
00:02:12,500 --> 00:02:14,120
所有的类似对象的一个内容

96
00:02:14,120 --> 00:02:17,180
我们来回归一下刚才所讲解的内容

97
00:02:17,180 --> 00:02:19,640
好我们刚才是不是讲到了一个

98
00:02:19,640 --> 00:02:23,100
controller 它是什么 它是不是控制器

99
00:02:23,100 --> 00:02:25,140
那么我们怎么去用呢 怎么使用

100
00:02:25,140 --> 00:02:28,420
是不是直接去通过class继承

101
00:02:28,420 --> 00:02:32,280
对吧 好 那么呢 我们是不是可以通过两种方式去继承呢

102
00:02:32,280 --> 00:02:34,660
拿两种 第一种呢 是不是咱们直接去

103
00:02:34,660 --> 00:02:37,720
require一个一GG 然后去调用它的一个

104
00:02:37,720 --> 00:02:39,640
controller 在那一个实力

105
00:02:39,640 --> 00:02:42,460
另外一种呢 是不是直接可以通过方形的方式 对吧

106
00:02:42,460 --> 00:02:44,980
它呢 是不是挂在咱们的app的

107
00:02:44,980 --> 00:02:48,280
app的controller 在一个属性下面

108
00:02:48,280 --> 00:02:50,080
那么接下来我们是不是还介绍了

109
00:02:50,080 --> 00:02:52,640
service 那么service他是不是和咱们的一个

110
00:02:52,640 --> 00:02:57,240
是不是和我们的一个controller是一模一样的呀 它一个使用方式对吧

111
00:02:57,240 --> 00:03:02,880
好 那么呢

112
00:03:02,880 --> 00:03:09,280
service呢 他同样的是不是可以通过require一几几的什么是不是service 然后呢app点service去

113
00:03:09,280 --> 00:03:13,880
使用他好 那么呢 除了讲的service 我们是不是还讲了一个

114
00:03:14,400 --> 00:03:15,160
黑暗部呀

115
00:03:15,160 --> 00:03:16,840
那么黑暗部其实我们之前也演示过了

116
00:03:16,840 --> 00:03:18,140
那么黑暗部呢可以在哪里去使用

117
00:03:18,140 --> 00:03:19,140
是不是一个是模板

118
00:03:19,140 --> 00:03:20,760
还有一个是不是context的点

119
00:03:20,760 --> 00:03:22,960
context下面是不是有一个

120
00:03:22,960 --> 00:03:24,460
黑暗部属性

121
00:03:24,460 --> 00:03:26,260
好那么呢

122
00:03:26,260 --> 00:03:27,960
最后呢是不是还介绍了我们一个config对象

123
00:03:27,960 --> 00:03:30,800
他呢其实是不是也在咱们app的config

124
00:03:30,800 --> 00:03:32,340
在一个属性下面

125
00:03:32,340 --> 00:03:34,140
我们呢就可以去获取我们的一个config对象

126
00:03:34,140 --> 00:03:35,660
好这里呢就是我们这节套的内容

