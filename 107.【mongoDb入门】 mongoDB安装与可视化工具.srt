1
00:00:00,000 --> 00:00:03,320
好 这节课我们就来看一下mongodb它的一些环境安装

2
00:00:03,320 --> 00:00:05,640
首先我们是不是需要去安装咱们的mongodb啊

3
00:00:05,640 --> 00:00:07,680
那么同样的在mac里面一行命令就可以解决

4
00:00:07,680 --> 00:00:08,700
breed install mongodb

5
00:00:08,700 --> 00:00:12,540
如果说同学们需要去在windows下面去安装的同学们可以去官网去看一下怎么去

6
00:00:12,540 --> 00:00:13,320
安装

7
00:00:13,320 --> 00:00:14,600
比如说我们去

8
00:00:14,600 --> 00:00:17,660
搜索mongo

9
00:00:17,660 --> 00:00:23,300
同样的你如果说懒得看英文你也可以这样你直接搜mongodb安装

10
00:00:23,300 --> 00:00:24,060
对吧

11
00:00:24,060 --> 00:00:29,180
可以说有很多的教程所以说我这里就不去详细讲了怎么样去安装咱们的mongodb

12
00:00:30,000 --> 00:00:33,440
这里不是我们的一个重点 因为你都到了N+12的课程了

13
00:00:33,440 --> 00:00:36,800
这里呢 如果说不会安装可能就不太好了 对吧

14
00:00:36,800 --> 00:00:39,040
好 我们再来看一下 可视化工具安装

15
00:00:39,040 --> 00:00:41,880
对吧 我们只要是数据库 我们都可能需要去安装一个可视化工具

16
00:00:41,880 --> 00:00:45,000
这样看起来会爽一点 比如说你去写些买收口 写些word口

17
00:00:45,000 --> 00:00:48,640
你去使用一些非关系 然后使用一些关系数据库的时候你也需要去用

18
00:00:48,640 --> 00:00:52,800
那么我们这里其实选择的是什么呢 叫做studio 3T这样的一个可视化工具

19
00:00:52,800 --> 00:00:55,920
其实有很多 同学们爱用什么用什么 网上百率搜很多

20
00:00:55,920 --> 00:00:57,920
好 我们来看一下它那个

21
00:01:00,000 --> 00:01:01,020
看一下他那些

22
00:01:01,020 --> 00:01:02,300
介绍

23
00:01:02,300 --> 00:01:05,640
全是英文算了不看

24
00:01:05,640 --> 00:01:07,940
其实很简单 我们只需要关注一个download

25
00:01:07,940 --> 00:01:08,960
对吧

26
00:01:08,960 --> 00:01:11,520
我只需要看怎么样下载OK了 其他的不看

27
00:01:11,520 --> 00:01:12,280
没什么好看

28
00:01:12,280 --> 00:01:14,080
大家可以看到他也支持三个平台

29
00:01:14,080 --> 00:01:16,120
Windows、Mac、Ninux 所以统计们自己去装

30
00:01:16,120 --> 00:01:19,200
地址我已经贴在咱们的讲义里面去了

31
00:01:19,200 --> 00:01:21,000
好 那我们继续

32
00:01:21,000 --> 00:01:23,040
这里呢 其实我已经给大家安装好了

33
00:01:23,040 --> 00:01:24,840
可以给大家去看一下他长什么样子

34
00:01:24,840 --> 00:01:27,400
我们来看一下他长什么样子

35
00:01:27,900 --> 00:01:29,900
拖错了 不好意思 其实它就是

36
00:01:29,900 --> 00:01:31,900
其实就长这样子

37
00:01:31,900 --> 00:01:35,900
长这样子 那我们再来看一下我们的realis长什么样

38
00:01:35,900 --> 00:01:41,900
我们的realis长这样 但是呢我们的mongodb它那个数据固长这样

39
00:01:41,900 --> 00:01:47,900
对吧 其实看下功能是不是我们的mongodb它的一个可操工具是不是会复杂很多呀

40
00:01:47,900 --> 00:01:47,900
 对吧

41
00:01:47,900 --> 00:01:52,900
其实这里都没关系 同学们可以继续看一下它到底有哪些功能

42
00:01:52,900 --> 00:01:54,900
其实我是比较喜欢这样一种傻瓜式

43
00:01:54,900 --> 00:01:58,740
这种傻瓜是 其实像mongodb这样一个比较复杂的看的头晕 说实话头晕好

44
00:01:58,740 --> 00:02:02,320
那我们再来看一下

45
00:02:02,320 --> 00:02:08,210
看一下我们可刷工具是不是已经安装好了 那么安装好之后呢 我们看一下

46
00:02:08,210 --> 00:02:11,020
 我们是不是还需要一个nodejs去操作mongodb的一个库啊

47
00:02:11,020 --> 00:02:15,150
为什么呀 因为我不可能再去命令行一行一行一行的去敲吧 所以说到这里呢

48
00:02:15,150 --> 00:02:18,700
 我们有了之前到loadds去操作reddit经验 我们这里直接去安装一下

49
00:02:18,700 --> 00:02:23,180
我们mongodb去操 咱们loadds操作mongodb的酷叫什么呢 叫做mongodb native

50
00:02:23,180 --> 00:02:24,420
 可以带同学们去看一下

51
00:02:24,900 --> 00:02:29,000
这几个呢其实主要就是说是一些介绍

52
00:02:29,000 --> 00:02:30,360
没什么好说的

53
00:02:30,360 --> 00:02:32,200
好那我们可以看到

54
00:02:32,200 --> 00:02:33,700
我们所使用的一个库叫做什么呢

55
00:02:33,700 --> 00:02:35,160
叫做node mongodb native

56
00:02:35,160 --> 00:02:37,420
原生的node操作mongodb的库

57
00:02:37,420 --> 00:02:38,560
它就介绍什么呢

58
00:02:38,560 --> 00:02:40,240
mongodb native nodejs driver

59
00:02:40,240 --> 00:02:41,340
它其实就是一个

60
00:02:41,340 --> 00:02:43,640
也就是mongodb它唯一的

61
00:02:43,640 --> 00:02:44,760
它唯一操作咱们

62
00:02:44,760 --> 00:02:46,320
咱们nodejs唯一操作mongodb的库

63
00:02:46,320 --> 00:02:48,220
所以这里就不涉及到什么技术选型的问题

64
00:02:48,220 --> 00:02:49,280
我们直接用就好了

65
00:02:49,280 --> 00:02:50,920
那么安装呢其实也很简单

66
00:02:50,920 --> 00:02:51,720
最好命令

67
00:02:51,720 --> 00:02:53,760
npr名书到mongodb-save就可以了

68
00:02:53,760 --> 00:02:56,840
所以这里呢 我这里就不给同学们去展示怎么去安装了

69
00:02:56,840 --> 00:03:05,280
这里呢就是我们这几个内容 我们主要就是说去安装一个mogdb 然后呢去安装一个可视化的工具

70
00:03:05,280 --> 00:03:06,300
 然后呢去

71
00:03:06,300 --> 00:03:11,420
安装咱们一个loads 去操作mogdb库 对吧 好 这里呢就是我们这几个的内容

