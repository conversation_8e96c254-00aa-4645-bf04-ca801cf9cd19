1
00:00:00,000 --> 00:00:02,240
好这节课呢我们就来看一下更新操作

2
00:00:02,240 --> 00:00:04,880
那么更新操作呢我们首先来看一下表

3
00:00:04,880 --> 00:00:07,320
put吧一般更新都是put

4
00:00:07,320 --> 00:00:09,340
那么put呢对应的方法呢就是update

5
00:00:09,340 --> 00:00:12,000
所以呢我们来定一个update这样一个方法

6
00:00:12,000 --> 00:00:15,300
asyncupdate

7
00:00:15,300 --> 00:00:18,400
好那么开始之前呢我们同样的来分析一下思路

8
00:00:18,400 --> 00:00:21,840
首先第一步是不是获取id啊

9
00:00:21,840 --> 00:00:24,840
对吧因为你得知道你需要更新哪一条数据

10
00:00:24,840 --> 00:00:27,340
那么你知道了更新哪条数据你还需要知道什么呀

11
00:00:27,340 --> 00:00:28,460
是不是怎么更新呢对吧

12
00:00:28,460 --> 00:00:29,980
那么怎么更新来就拿你啊

13
00:00:29,980 --> 00:00:31,980
是不来就有requist的body

14
00:00:31,980 --> 00:00:33,020
那么第三步才是

15
00:00:33,020 --> 00:00:35,340
咱们数据库的一个更新操作吧

16
00:00:35,340 --> 00:00:36,900
好 那么获取id呢

17
00:00:36,900 --> 00:00:38,500
是不是和咱们刚才的一样啊

18
00:00:38,500 --> 00:00:40,820
通过post id 咱们可以获取到

19
00:00:40,820 --> 00:00:42,260
那么

20
00:00:42,260 --> 00:00:44,100
requist body呢

21
00:00:44,100 --> 00:00:45,100
其实requist body呢

22
00:00:45,100 --> 00:00:46,140
其实我们之前在做

23
00:00:46,140 --> 00:00:47,220
create操作的时候

24
00:00:47,220 --> 00:00:48,220
是不是也已经获取过啊

25
00:00:48,220 --> 00:00:49,140
我们直接把它给括过来

26
00:00:49,140 --> 00:00:51,740
好 这里呢

27
00:00:51,740 --> 00:00:52,660
其实就可以获取到我们

28
00:00:52,660 --> 00:00:54,100
需要更新的一个数据

29
00:00:54,100 --> 00:00:55,620
那我们都获取到之后

30
00:00:55,620 --> 00:00:56,460
接下来做什么

31
00:00:56,460 --> 00:00:57,500
是不是进行更新操作

32
00:00:57,500 --> 00:00:58,940
所以我们再来继续copy

33
00:00:59,460 --> 00:01:03,300
很爽 比如说我们去抗执一个rent 然后呢咱们去调用一个什么方法呢

34
00:01:03,300 --> 00:01:07,300
我们需要去更新数据 所以说呢我们model上面其实也对应了一个data这样一个方法

35
00:01:07,300 --> 00:01:08,580
 但是他需要接受两参数

36
00:01:08,580 --> 00:01:11,100
第一个呢是查询条件

37
00:01:11,100 --> 00:01:20,700
条件 那么第二个呢 就是更新的数据 好 首先呢 我们的查询条件是不是通过我们的下方向id去对吧

38
00:01:20,700 --> 00:01:22,380
 去查到我们这条数据 然后第二个呢

39
00:01:22,380 --> 00:01:27,460
咱们是不是就是如何去更新 那么如何去更新呢 这里其实它有一个特殊的质量叫做dollar

40
00:01:27,460 --> 00:01:27,620
 start

41
00:01:27,980 --> 00:01:29,660
其实就是代表我们此时要去进行更新

42
00:01:29,660 --> 00:01:31,180
这里呢就是咱们的一个

43
00:01:31,180 --> 00:01:34,140
相当于一个约束

44
00:01:34,140 --> 00:01:35,340
你按这么去写就行了

45
00:01:35,340 --> 00:01:36,540
你每次只要去转update

46
00:01:36,540 --> 00:01:38,820
那么他的数据一定是包在一个多的set下面的

47
00:01:38,820 --> 00:01:39,700
这是咱们文档规定的

48
00:01:39,700 --> 00:01:41,180
所以说我们这么去写就可以了

49
00:01:41,180 --> 00:01:43,220
那么这里呢我们可以利用一个小技巧是什么

50
00:01:43,220 --> 00:01:44,540
咱们用一个扩展运算服务

51
00:01:44,540 --> 00:01:46,340
咱们直接去把它给

52
00:01:46,340 --> 00:01:47,900
request body扩展开来就可以了

53
00:01:47,900 --> 00:01:51,300
好那么这里呢其实我们就已经实现了一个更新操作

54
00:01:51,300 --> 00:01:52,380
那么完成之后是不是同样的

55
00:01:52,380 --> 00:01:53,100
调用什么呀

56
00:01:53,100 --> 00:01:55,780
调用this.success.read

57
00:01:55,780 --> 00:01:57,060
好那么为了保险期间呢

58
00:01:57,060 --> 00:01:59,420
我们是不是还是一样的 进行一个try

59
00:01:59,420 --> 00:02:01,660
进行一个try catch

60
00:02:01,660 --> 00:02:07,820
我们把代码给贴进去

61
00:02:07,820 --> 00:02:12,420
这里呢 少了一个

62
00:02:12,420 --> 00:02:13,440
await

63
00:02:13,440 --> 00:02:16,520
await for

64
00:02:16,520 --> 00:02:18,560
error传去

65
00:02:18,560 --> 00:02:23,940
好 这里呢 我们的更新操作其实就已经完成了 我们来测试一下

66
00:02:25,220 --> 00:02:26,940
首先呢 我们来看一下数据库里面的一个数据

67
00:02:26,940 --> 00:02:29,220
我们就来看一下咱们的

68
00:02:29,220 --> 00:02:33,180
这一条吧 我们的title是不是一串数字啊 对吧

69
00:02:33,180 --> 00:02:35,220
那么我们来把它的title给修改一下

70
00:02:35,220 --> 00:02:36,860
比如说我们先把id给copy一下

71
00:02:36,860 --> 00:02:39,320
那么更新操作能不能在浏览器里面去做

72
00:02:39,320 --> 00:02:40,100
是不是也不行呢

73
00:02:40,100 --> 00:02:42,020
我们必须通过postman去模拟

74
00:02:42,020 --> 00:02:44,200
好 我们来找put

75
00:02:44,200 --> 00:02:45,600
然后来把id换成他的id

76
00:02:45,600 --> 00:02:48,060
然后我们是不是需要去修改什么呀

77
00:02:48,060 --> 00:02:49,880
我们是不是要去修改title呀

78
00:02:49,880 --> 00:02:51,620
比如说我们来给一个标记

79
00:02:51,620 --> 00:02:54,280
我是修改过的

80
00:02:55,220 --> 00:02:55,980
标题

81
00:02:55,980 --> 00:02:57,780
咱们来圣得一下

82
00:02:57,780 --> 00:02:59,820
大家可以看到我们这里报了一个错

83
00:02:59,820 --> 00:03:02,900
for is not defined

84
00:03:02,900 --> 00:03:04,440
我们来看一下代码是怎么回事

85
00:03:04,440 --> 00:03:07,000
大家可以看到我们这里是不是忘了调用this

86
00:03:07,000 --> 00:03:08,540
好 那么我们来重新

87
00:03:08,540 --> 00:03:09,560
圣得一下

88
00:03:09,560 --> 00:03:12,620
大家可以看到我们此时其实还是失败了

89
00:03:12,620 --> 00:03:14,160
我们来看一下失败的原因是什么

90
00:03:14,160 --> 00:03:17,500
其实大家可以看到

91
00:03:17,500 --> 00:03:18,260
这样一句话

92
00:03:18,260 --> 00:03:20,300
contest.module.post.update

93
00:03:20,300 --> 00:03:22,860
其实我们这里一个名字写错了

94
00:03:22,860 --> 00:03:23,900
其实叫做update

95
00:03:23,900 --> 00:03:25,180
好 我们再来试一下

96
00:03:25,220 --> 00:03:29,720
好 大家可以看到此时是不是反复了一个OK

97
00:03:29,720 --> 00:03:30,880
它的提示是什么

98
00:03:30,880 --> 00:03:33,560
Modify的1是不是和我们的双处是一样的呀

99
00:03:33,560 --> 00:03:35,280
你双的一条数据它会告诉你双的一条

100
00:03:35,280 --> 00:03:36,980
那么此时它会告诉你Modify的一条

101
00:03:36,980 --> 00:03:39,160
好 那我们来看一下数据库到底有没有改

102
00:03:39,160 --> 00:03:41,840
大家可以看到我们的title是不是已经被修改了

103
00:03:41,840 --> 00:03:45,160
好 那么这里其实就是我们的一个更新操作

104
00:03:45,160 --> 00:03:47,140
我们来总结一下

105
00:03:47,140 --> 00:03:50,080
其实我们的更新操作呢其实也不能分为三步

106
00:03:50,080 --> 00:03:51,100
第一个呢首先呢获取ID

107
00:03:51,100 --> 00:03:53,760
然后呢我们获取ID是不是可以找到

108
00:03:53,760 --> 00:03:54,660
我们需要修改哪一条

109
00:03:54,660 --> 00:03:56,160
此时你是不是还需要知道怎么改

110
00:03:56,160 --> 00:03:57,640
所以通过咱们的一个Request的玻璃

111
00:03:57,640 --> 00:03:59,300
然后再来执行咱们的一个更新操作

112
00:03:59,300 --> 00:04:00,100
那么更新操作的话

113
00:04:00,100 --> 00:04:01,640
是不是使用咱们Model的一个Update

114
00:04:01,640 --> 00:04:03,400
那么Update它就是两个参数

115
00:04:03,400 --> 00:04:04,160
第一个查询条件

116
00:04:04,160 --> 00:04:06,260
第二个也就是我们需要更新的一个数据

117
00:04:06,260 --> 00:04:07,460
好

118
00:04:07,460 --> 00:04:11,360
那么这里其实我们是不是已经实现了咱们数据库的一个增山改查

119
00:04:11,360 --> 00:04:11,860
对吧

120
00:04:11,860 --> 00:04:13,240
增山改查其实都已经实现了

121
00:04:13,240 --> 00:04:14,900
那么接下来我们还有没有什么需要去做的呢

122
00:04:14,900 --> 00:04:15,860
其实还有

123
00:04:15,860 --> 00:04:16,480
什么呢

124
00:04:16,480 --> 00:04:19,120
大家记不记得我们之前是不是提过一个service

125
00:04:19,120 --> 00:04:20,740
那么service它的作用是什么

126
00:04:20,740 --> 00:04:24,780
是不是进行咱们一个数据库的增长改查

127
00:04:24,780 --> 00:04:25,560
那么我们现在是不是没有

128
00:04:25,560 --> 00:04:26,500
都写到控制器里面了

129
00:04:26,500 --> 00:04:29,420
所以说我们需要通过一个service把它给提取出来吧

130
00:04:29,420 --> 00:04:29,920
对吧

131
00:04:29,920 --> 00:04:31,040
好

132
00:04:31,040 --> 00:04:32,620
那么下一节课我们就来讲它

