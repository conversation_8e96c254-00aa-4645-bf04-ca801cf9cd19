1
00:00:00,000 --> 00:00:02,240
我们刚才是不是已经学习了

2
00:00:02,240 --> 00:00:04,100
在Reddit里面它的过期时间

3
00:00:04,100 --> 00:00:05,640
那么这里我们就来看一下

4
00:00:05,640 --> 00:00:06,740
它的应用场景是什么

5
00:00:06,740 --> 00:00:07,520
我们去实现一个diamond

6
00:00:07,520 --> 00:00:07,800
看一下

7
00:00:07,800 --> 00:00:09,580
比如说我们去实现一个访问

8
00:00:09,580 --> 00:00:10,740
频率限制

9
00:00:10,740 --> 00:00:11,920
我们的需求是什么呢

10
00:00:11,920 --> 00:00:13,100
为了减轻服务的压力

11
00:00:13,100 --> 00:00:14,640
需要限制每个用户

12
00:00:14,640 --> 00:00:16,040
一段时间的最大访问量

13
00:00:16,040 --> 00:00:16,740
什么意思

14
00:00:16,740 --> 00:00:18,380
你比如说你去做一个网站

15
00:00:18,380 --> 00:00:19,460
然后我天天

16
00:00:19,460 --> 00:00:21,640
比如说我用logs写一段负旋款

17
00:00:21,640 --> 00:00:22,800
天天去负旋款你的网站

18
00:00:22,800 --> 00:00:24,040
我一秒钟访问一千次

19
00:00:24,040 --> 00:00:26,820
是不是一瞬间就把你的服务器给打挂了

20
00:00:26,820 --> 00:00:27,940
这样你是不是会蒙受损失

21
00:00:27,940 --> 00:00:30,200
比如说你去有一个邮件服务或者短信服务

22
00:00:30,200 --> 00:00:31,020
我去写一段脚本

23
00:00:31,020 --> 00:00:33,080
去无线的给你的服务器去发邮件

24
00:00:33,080 --> 00:00:34,840
或者说让你的服务器给我发邮件

25
00:00:34,840 --> 00:00:35,500
给我发短信

26
00:00:35,500 --> 00:00:36,540
那么我们都知道

27
00:00:36,540 --> 00:00:38,120
发邮件发短信是需要收费的

28
00:00:38,120 --> 00:00:39,420
那么如果说谁都这样去搞

29
00:00:39,420 --> 00:00:41,720
那么你的网站可能几天之内就要倒闭了

30
00:00:41,720 --> 00:00:43,480
所以说我们需要去实现

31
00:00:43,480 --> 00:00:46,280
对某一个IP地址的一个访问频率的限制

32
00:00:46,280 --> 00:00:47,480
它可以怎么样去实现呢

33
00:00:47,480 --> 00:00:48,720
就可以通过Expare

34
00:00:48,720 --> 00:00:49,820
好

35
00:00:49,820 --> 00:00:51,940
那么我们在用Expare去实现之前

36
00:00:51,940 --> 00:00:53,300
我们来先分析一下

37
00:00:53,300 --> 00:00:54,580
怎么去实现

38
00:00:54,580 --> 00:00:56,100
你比如说我们通过Expare

39
00:00:56,100 --> 00:00:57,900
去实现

40
00:00:57,900 --> 00:01:00,380
访问频率的一个限制

41
00:01:00,380 --> 00:01:02,620
首先第一步

42
00:01:02,620 --> 00:01:03,660
我们是不是要通过

43
00:01:03,660 --> 00:01:05,000
用户ID去存储某一个东西

44
00:01:05,000 --> 00:01:05,620
对吧

45
00:01:05,620 --> 00:01:07,720
我们通过用户ID

46
00:01:07,720 --> 00:01:08,580
去新建一个字段

47
00:01:08,580 --> 00:01:09,800
字段是什么呢

48
00:01:09,800 --> 00:01:10,360
你比如说

49
00:01:10,360 --> 00:01:11,440
我们去新建一个

50
00:01:11,440 --> 00:01:12,840
right limit

51
00:01:12,840 --> 00:01:14,700
然后后面跟上你的用户ID

52
00:01:14,700 --> 00:01:15,700
然后每一个用户

53
00:01:15,700 --> 00:01:16,660
都会有这样一个字段

54
00:01:16,660 --> 00:01:17,520
去专门去存储

55
00:01:17,520 --> 00:01:18,860
它的一个访问量是什么

56
00:01:18,860 --> 00:01:20,780
那么我们存储的时候

57
00:01:20,780 --> 00:01:22,660
它会存储什么呀

58
00:01:22,660 --> 00:01:23,460
这样一个字段

59
00:01:23,460 --> 00:01:24,120
存储什么类型

60
00:01:24,120 --> 00:01:26,540
它是不是一个闪电

61
00:01:26,540 --> 00:01:28,420
然后存储的结果

62
00:01:28,420 --> 00:01:30,320
存储的结果是什么

63
00:01:30,320 --> 00:01:33,300
存储的结果

64
00:01:33,300 --> 00:01:34,320
访问量吧

65
00:01:34,320 --> 00:01:35,000
就是数字

66
00:01:35,000 --> 00:01:37,860
那么我们既然有了这样一个字段

67
00:01:37,860 --> 00:01:39,120
比如说right limit

68
00:01:39,120 --> 00:01:40,200
比如说我

69
00:01:40,200 --> 00:01:40,920
代表我

70
00:01:40,920 --> 00:01:41,760
这样一个字段代表我

71
00:01:41,760 --> 00:01:42,400
存储的访问量

72
00:01:42,400 --> 00:01:43,260
访问量就是数字吧

73
00:01:43,260 --> 00:01:43,880
比如0123

74
00:01:43,880 --> 00:01:45,820
那么每次用户访问的时候

75
00:01:45,820 --> 00:01:46,580
是不是会加1

76
00:01:46,580 --> 00:01:47,500
而且呢

77
00:01:47,500 --> 00:01:48,620
我们是不是最重要的

78
00:01:48,620 --> 00:01:49,400
需要通过Expare

79
00:01:49,400 --> 00:01:50,640
去给它设置一个过期时间

80
00:01:50,640 --> 00:01:53,300
那么设置过期时间

81
00:01:53,300 --> 00:01:55,520
我们是不是需要一个判断

82
00:01:55,520 --> 00:01:57,580
比如说我们去访问的时候

83
00:01:57,580 --> 00:01:58,760
通过这样一个字段去读取

84
00:01:58,760 --> 00:01:59,540
如果说没有过期

85
00:01:59,540 --> 00:02:00,140
我们就加一

86
00:02:00,140 --> 00:02:02,300
那么如果说过期了

87
00:02:02,300 --> 00:02:03,940
是不是需要去新建字段

88
00:02:03,940 --> 00:02:04,920
然后呢初始化访问

89
00:02:04,920 --> 00:02:05,600
我们的次数为零

90
00:02:05,600 --> 00:02:06,320
什么意思

91
00:02:06,320 --> 00:02:08,000
可能我们光说没什么意思

92
00:02:08,000 --> 00:02:08,960
我们直接来看一下

93
00:02:08,960 --> 00:02:10,480
到底怎么去写这样一段代码

94
00:02:10,480 --> 00:02:18,140
比如说我们有一个Expile.js

95
00:02:18,140 --> 00:02:21,380
首先

96
00:02:21,380 --> 00:02:24,700
我们先把操作

97
00:02:24,700 --> 00:02:26,180
数据库的代码粘贴过来

98
00:02:26,180 --> 00:02:28,100
然后我们刚才讲到了是不是要新建一个字段

99
00:02:28,100 --> 00:02:28,940
你比如说

100
00:02:28,940 --> 00:02:32,300
比如说我们

101
00:02:32,300 --> 00:02:37,200
比如说我们去

102
00:02:37,200 --> 00:02:39,900
比如说我们首先需要去判断

103
00:02:39,900 --> 00:02:41,360
首先需要去判断

104
00:02:41,360 --> 00:02:42,940
is k

105
00:02:42,940 --> 00:02:44,180
exists

106
00:02:44,180 --> 00:02:45,060
什么意思

107
00:02:45,060 --> 00:02:46,940
比如说你第一次访问

108
00:02:46,940 --> 00:02:47,980
你第一次访问的时候

109
00:02:47,980 --> 00:02:49,620
咱们的radis数据库里面有没有你的字段

110
00:02:49,620 --> 00:02:57,500
第1次访问redis里面有你的字段吗 你比如说我们去ex

111
00:02:57,500 --> 00:02:59,500
says

112
00:02:59,500 --> 00:03:08,700
right limit 后面呢 需要跟上你的ip地址 对吧 因为你在访问网站之前 我知道你的ip吗

113
00:03:08,700 --> 00:03:13,660
不知道吧 所以说第一次访问redis是没有你的字段的 所以我们需要通过用一个变量

114
00:03:13,660 --> 00:03:18,260
用一个变量去判断一下 首先我们的redis数据库里面是不是有没有你的ip

115
00:03:18,260 --> 00:03:19,340
 也就是说你有没有访问过

116
00:03:19,340 --> 00:03:21,280
这里呢又奉有两种情况

117
00:03:21,280 --> 00:03:23,900
如果说你访问过和没有访问过的情况

118
00:03:23,900 --> 00:03:25,720
那么你第一次访问是肯定可以访问的

119
00:03:25,720 --> 00:03:26,260
对吧

120
00:03:26,260 --> 00:03:27,680
那么我们主要是看你后面

121
00:03:27,680 --> 00:03:28,860
我们怎么去限制你的频率

122
00:03:28,860 --> 00:03:30,980
如果存在

123
00:03:30,980 --> 00:03:32,760
也就是说如果说你已经访问过了

124
00:03:32,760 --> 00:03:33,200
还有一种情况

125
00:03:33,200 --> 00:03:34,080
你如果没有访问

126
00:03:34,080 --> 00:03:35,700
没有访问的情况是不是最简单

127
00:03:35,700 --> 00:03:36,940
我们先写没有访问过

128
00:03:36,940 --> 00:03:41,500
我们先写没有访问过的情况

129
00:03:41,500 --> 00:03:43,140
比如说client点

130
00:03:43,140 --> 00:03:44,800
第一次访问需要调用什么命令

131
00:03:44,800 --> 00:03:45,560
同学们

132
00:03:45,560 --> 00:03:46,960
是不是incr进行加一啊

133
00:03:46,960 --> 00:03:47,780
对你当前这样一个字吧

134
00:03:47,780 --> 00:03:48,160
加一

135
00:03:48,160 --> 00:03:50,160
对谁啊

136
00:03:50,160 --> 00:03:50,700
就是他吧

137
00:03:50,700 --> 00:03:52,240
我们直接对他进行加1就OK了

138
00:03:52,240 --> 00:03:53,320
好

139
00:03:53,320 --> 00:03:54,200
那么加1之后

140
00:03:54,200 --> 00:03:56,000
加1之后需要做什么

141
00:03:56,000 --> 00:03:57,660
是不是要给他一个超时时间

142
00:03:57,660 --> 00:04:00,120
比如说client.xpile

143
00:04:00,120 --> 00:04:02,840
好

144
00:04:02,840 --> 00:04:03,780
假设我们超时时间

145
00:04:03,780 --> 00:04:05,080
给短一点60秒

146
00:04:05,080 --> 00:04:05,760
好

147
00:04:05,760 --> 00:04:06,340
这里呢

148
00:04:06,340 --> 00:04:07,320
是我们第一次访问的情况

149
00:04:07,320 --> 00:04:08,300
因为你第一次访问

150
00:04:08,300 --> 00:04:09,140
我肯定不会拦截你吧

151
00:04:09,140 --> 00:04:09,800
你是新用户

152
00:04:09,800 --> 00:04:10,080
好

153
00:04:10,080 --> 00:04:10,600
那么

154
00:04:10,600 --> 00:04:12,380
我们重点在于这一段逻辑

155
00:04:12,380 --> 00:04:13,640
你访问过

156
00:04:13,640 --> 00:04:14,480
我们怎么样去判断

157
00:04:14,480 --> 00:04:15,140
你一分钟之内

158
00:04:15,140 --> 00:04:16,380
你是不是访问了十次以上

159
00:04:16,380 --> 00:04:16,980
假设

160
00:04:16,980 --> 00:04:24,480
假设我们一分钟之内最多让你访问十次

161
00:04:24,480 --> 00:04:26,200
好

162
00:04:26,200 --> 00:04:26,980
此时呢

163
00:04:26,980 --> 00:04:28,900
我们首先是不是需要去咱们的

164
00:04:28,900 --> 00:04:30,140
在一个字段里面去读取

165
00:04:30,140 --> 00:04:31,500
你到底访问过多少次吧

166
00:04:31,500 --> 00:04:32,460
比如说我们的

167
00:04:32,460 --> 00:04:33,420
what time等于什么

168
00:04:33,420 --> 00:04:35,080
client点

169
00:04:35,080 --> 00:04:36,460
incr

170
00:04:36,460 --> 00:04:40,780
incr是不是既能加一又能够去得到

171
00:04:40,780 --> 00:04:42,060
你访问了多少次了

172
00:04:42,060 --> 00:04:42,300
对吧

173
00:04:42,300 --> 00:04:44,020
这里呢就可以得到你访问了一个次数

174
00:04:44,020 --> 00:04:44,380
times

175
00:04:44,380 --> 00:04:45,220
此时呢

176
00:04:45,220 --> 00:04:46,140
我们可以来做一个判断

177
00:04:46,140 --> 00:04:47,420
if什么呢

178
00:04:47,420 --> 00:04:48,380
if times

179
00:04:48,380 --> 00:04:50,340
大于十

180
00:04:50,340 --> 00:04:52,500
如果大于十

181
00:04:52,500 --> 00:04:53,940
此时我是不是要告诉你

182
00:04:53,940 --> 00:04:55,220
client.exit

183
00:04:55,220 --> 00:04:57,480
你不能访问吧

184
00:04:57,480 --> 00:05:01,280
好

185
00:05:01,280 --> 00:05:02,200
到这里呢

186
00:05:02,200 --> 00:05:03,360
我们是不是已经就实现了

187
00:05:03,360 --> 00:05:04,460
咱们一个基本的访问限制

188
00:05:04,460 --> 00:05:05,660
你比如说

189
00:05:05,660 --> 00:05:06,480
首先我们去判断一下

190
00:05:06,480 --> 00:05:07,260
你是不是第一次访问

191
00:05:07,260 --> 00:05:08,800
你如果说至第一次访问

192
00:05:08,800 --> 00:05:09,740
那我们直接对你

193
00:05:09,740 --> 00:05:10,900
在一个字段进行加一

194
00:05:10,900 --> 00:05:11,980
也就是那你访问了一次

195
00:05:11,980 --> 00:05:13,100
然后给他一个过期时间

196
00:05:13,100 --> 00:05:14,800
当你后面再次访问的时候

197
00:05:14,800 --> 00:05:16,240
我们是不是会每次都会进行加一

198
00:05:16,240 --> 00:05:17,960
如果说当你超过十

199
00:05:17,960 --> 00:05:19,060
你就不能访问了

200
00:05:19,060 --> 00:05:19,820
对吧

201
00:05:19,820 --> 00:05:20,120
好

202
00:05:20,120 --> 00:05:22,400
那么我们再来看一下

203
00:05:22,400 --> 00:05:24,540
那么我们再来看一下

204
00:05:24,540 --> 00:05:25,940
这一段代码其实呢

205
00:05:25,940 --> 00:05:27,580
它存在一个问题

206
00:05:27,580 --> 00:05:28,840
什么问题啊

207
00:05:28,840 --> 00:05:30,320
假如说

208
00:05:30,320 --> 00:05:32,720
假如说你程序执行完倒数第二行后

209
00:05:32,720 --> 00:05:34,040
突然某种原因退出了

210
00:05:34,040 --> 00:05:35,980
没能够该为它设置过期时间

211
00:05:35,980 --> 00:05:37,140
那么它就会永久存在

212
00:05:37,140 --> 00:05:38,400
这是一个很严重的问题

213
00:05:38,400 --> 00:05:40,700
什么意思啊同学们

214
00:05:40,700 --> 00:05:41,960
什么意思

215
00:05:41,960 --> 00:05:44,300
其实问题出在这里

216
00:05:44,300 --> 00:05:46,260
就是如果说

217
00:05:46,260 --> 00:05:48,240
咱们的client.incr

218
00:05:48,240 --> 00:05:49,880
假如

219
00:05:49,880 --> 00:05:52,460
出现意外

220
00:05:52,460 --> 00:05:53,280
则

221
00:05:53,280 --> 00:05:57,720
永远不会设置过期时间

222
00:05:57,720 --> 00:05:58,360
什么意思

223
00:05:58,360 --> 00:06:00,880
也就是说假如我们的client.incr这里

224
00:06:00,880 --> 00:06:02,000
咱们的执行发生错误了

225
00:06:02,000 --> 00:06:03,640
那么我们的spire是不是不会去执行

226
00:06:03,640 --> 00:06:05,480
但是呢

227
00:06:05,480 --> 00:06:08,360
你的incr又对它进行了加一

228
00:06:08,360 --> 00:06:10,300
也就是说它会造成一个什么问题

229
00:06:10,300 --> 00:06:11,520
你一个用户访问你

230
00:06:11,520 --> 00:06:13,280
在一个网站十次之后就永远不能访问了

231
00:06:13,280 --> 00:06:14,240
这样问题是不是很严重

232
00:06:14,240 --> 00:06:15,180
你把客人都赶走了

233
00:06:15,180 --> 00:06:15,880
对吧

234
00:06:15,880 --> 00:06:16,580
那你的网站就

235
00:06:16,580 --> 00:06:17,640
可能就不太好了

236
00:06:17,640 --> 00:06:19,560
所以说我们怎么样去解决

237
00:06:19,560 --> 00:06:20,820
同学们思考一下

238
00:06:20,820 --> 00:06:21,860
我们怎么样去解决这个问题

239
00:06:21,860 --> 00:06:23,800
比如说我们NCR发生错误了

240
00:06:23,800 --> 00:06:24,420
但是呢

241
00:06:24,420 --> 00:06:25,720
我们Expire就执行不了

242
00:06:25,720 --> 00:06:26,220
怎么办

243
00:06:26,220 --> 00:06:28,280
我们之前是不是讲过

244
00:06:28,280 --> 00:06:29,120
可以用什么

245
00:06:29,120 --> 00:06:30,020
Client点

246
00:06:30,020 --> 00:06:31,740
MULTI

247
00:06:31,740 --> 00:06:32,160
它是什么

248
00:06:32,160 --> 00:06:33,120
是不是咱们的事物啊

249
00:06:33,120 --> 00:06:33,980
咱们是不是可以用事物

250
00:06:33,980 --> 00:06:34,660
去解决这个问题

251
00:06:34,660 --> 00:06:35,600
Client点

252
00:06:35,600 --> 00:06:37,280
Exec

253
00:06:37,280 --> 00:06:37,940
对吧

254
00:06:37,940 --> 00:06:38,660
也就是说

255
00:06:38,660 --> 00:06:40,400
如果说我们的NCR报错

256
00:06:40,400 --> 00:06:42,320
咱们一系列都执行不了

257
00:06:42,320 --> 00:06:43,340
我们事物是不是就可以解决

258
00:06:43,340 --> 00:06:43,820
这样一个问题

259
00:06:43,820 --> 00:06:45,460
好

260
00:06:45,460 --> 00:06:47,580
这里呢

261
00:06:47,580 --> 00:06:49,140
我们就来回顾一下

262
00:06:49,140 --> 00:06:50,160
我们刚才的这样一个例子

263
00:06:50,160 --> 00:06:53,740
我们需要去实现的需求是什么

264
00:06:53,740 --> 00:06:55,460
是不是对访问进行限制

265
00:06:55,460 --> 00:06:56,260
我们怎么去做

266
00:06:56,260 --> 00:06:57,960
是不是直接去建一个字段

267
00:06:57,960 --> 00:06:59,060
然后每次访问对它加1

268
00:06:59,060 --> 00:06:59,680
对吧

269
00:06:59,680 --> 00:07:01,180
通过INCR对它进行加1

270
00:07:01,180 --> 00:07:02,460
那么如果说超过限制

271
00:07:02,460 --> 00:07:03,120
我们就直接退出

272
00:07:03,120 --> 00:07:03,840
如果说没有了

273
00:07:03,840 --> 00:07:04,600
就继续加1

274
00:07:04,600 --> 00:07:05,820
然后给它一个过期时间

275
00:07:05,820 --> 00:07:07,340
然后这里需要注意的是

276
00:07:07,340 --> 00:07:08,140
我们是不是需要去

277
00:07:08,140 --> 00:07:09,120
用事物去处理

278
00:07:09,120 --> 00:07:09,960
加1在一个过程

279
00:07:09,960 --> 00:07:10,560
为什么

280
00:07:10,560 --> 00:07:11,660
因为你如果报错

281
00:07:11,660 --> 00:07:12,720
你如果INCR报错

282
00:07:12,720 --> 00:07:13,740
那么过期时间就永远

283
00:07:13,740 --> 00:07:14,540
设置不上了

284
00:07:14,540 --> 00:07:16,500
好这里就是我们实现

285
00:07:16,500 --> 00:07:18,980
这样一个过期时间的一个小的单

