1
00:00:00,260 --> 00:00:01,800
好 这节目我们就来看一下

2
00:00:01,800 --> 00:00:04,100
怎么样通过集合来去存储文章的

3
00:00:04,100 --> 00:00:05,120
标签

4
00:00:05,120 --> 00:00:07,420
那么我们一个文章标签是不是

5
00:00:07,420 --> 00:00:08,960
会很多而且不相同

6
00:00:08,960 --> 00:00:11,520
而且展示的时候这些标签它也没有一个顺序的要求

7
00:00:11,520 --> 00:00:14,080
所以呢我们可以用集合类型来去存储文章的标签

8
00:00:14,080 --> 00:00:14,840
非常完美

9
00:00:14,840 --> 00:00:17,160
好 那我们直接进入代码的一个编写

10
00:00:17,160 --> 00:00:26,360
好 首先老过去引入一个redis

11
00:00:26,360 --> 00:00:27,900
等于require

12
00:00:29,180 --> 00:00:32,880
然后呢去lue一个咱们的抠端

13
00:00:32,880 --> 00:00:33,880
lue redis

14
00:00:33,880 --> 00:00:36,320
好对象是一些配置不管

15
00:00:36,320 --> 00:00:38,740
首先我们的需求是什么呀

16
00:00:38,740 --> 00:00:42,940
需求是不是存储标签

17
00:00:42,940 --> 00:00:44,920
那么存储标签

18
00:00:44,920 --> 00:00:46,320
我们应该怎么去存

19
00:00:46,320 --> 00:00:48,300
刚才我们是不是分析过

20
00:00:48,300 --> 00:00:50,880
怎么去存储评论

21
00:00:50,880 --> 00:00:52,300
存储评论怎么去存

22
00:00:52,300 --> 00:00:54,440
我们的评论是不是每一篇文章

23
00:00:54,440 --> 00:00:55,540
它都会有一个列表

24
00:00:55,540 --> 00:00:57,900
我们的评论是不是叫做post id

25
00:00:59,180 --> 00:00:59,680
comments

26
00:00:59,680 --> 00:01:03,420
按我的标签是不是也可以叫做post id

27
00:01:03,420 --> 00:01:07,940
tax呢 为什么 因为我们在一个id下面

28
00:01:07,940 --> 00:01:11,020
也会有通过我们集合的形式去存储我们的标签

29
00:01:11,020 --> 00:01:13,840
然后每个id呢 都会有一个集合

30
00:01:13,840 --> 00:01:16,220
这里我们就来看一下 比如说client

31
00:01:16,220 --> 00:01:19,420
.said

32
00:01:19,420 --> 00:01:23,080
名称是什么呢 比如说post 我们给42号这篇文章

33
00:01:23,080 --> 00:01:24,580
id为42号的文章去存储tax

34
00:01:24,580 --> 00:01:27,220
我们去存储标签

35
00:01:27,620 --> 00:01:30,520
比如说 它叫 杂稳技术

36
00:01:30,520 --> 00:01:32,620
加ma

37
00:01:32,620 --> 00:01:34,120
6JS

38
00:01:34,120 --> 00:01:36,620
这里呢 我们就给 ID为42的文章

39
00:01:36,620 --> 00:01:40,620
存入了一些Tax 技术 杂稳 什么加ma 6JS 它呢 都是一些

40
00:01:40,620 --> 00:01:42,620
集合

41
00:01:42,620 --> 00:01:44,620
那么如果说我们想 这里是存储

42
00:01:44,620 --> 00:01:47,620
那么我们如果想删除呢 比如说我们

43
00:01:47,620 --> 00:01:48,620
技术不要了 怎么去删

44
00:01:48,620 --> 00:01:50,620
client.srm

45
00:01:50,620 --> 00:01:51,620
传出什么呢

46
00:01:51,620 --> 00:01:52,620
post呢

47
00:01:52,620 --> 00:01:55,620
咱们的首先文章ID 然后咱们删除技术

48
00:01:55,620 --> 00:01:58,700
这里可以很简单的就完成我们文章tag的一个存储和

49
00:01:58,700 --> 00:01:59,980
算储

50
00:01:59,980 --> 00:02:01,500
我们怎么样去获取

51
00:02:01,500 --> 00:02:03,560
获取我们所有文章的一些tags

52
00:02:03,560 --> 00:02:04,840
很简单比如说咱们去

53
00:02:04,840 --> 00:02:06,380
定一个tags等于

54
00:02:06,380 --> 00:02:09,180
plaint.s

55
00:02:09,180 --> 00:02:10,220
members

56
00:02:10,220 --> 00:02:12,000
什么呀是不是传入我们的post

57
00:02:12,000 --> 00:02:12,780
42

58
00:02:12,780 --> 00:02:14,560
这样就可以了吧

59
00:02:14,560 --> 00:02:15,840
所以集合的操作还是

60
00:02:15,840 --> 00:02:16,860
比较简单的

61
00:02:16,860 --> 00:02:19,420
这里我们就已经完成了我们存储文章标签的一个

62
00:02:19,420 --> 00:02:20,460
需求

63
00:02:20,460 --> 00:02:23,780
这里其实老师还给大家去提一下

64
00:02:24,300 --> 00:02:26,200
比如说我们有两种这样的场景

65
00:02:26,200 --> 00:02:29,220
一般来讲

66
00:02:29,220 --> 00:02:30,660
我们有一个add件

67
00:02:30,660 --> 00:02:32,020
我们都会有个add件

68
00:02:32,020 --> 00:02:32,820
去让我们去

69
00:02:32,820 --> 00:02:33,620
比如说天真爱好

70
00:02:33,620 --> 00:02:34,160
你爱打篮球

71
00:02:34,160 --> 00:02:34,640
爱游泳

72
00:02:34,640 --> 00:02:35,220
咱们去add

73
00:02:35,220 --> 00:02:36,180
再次还有一种

74
00:02:36,180 --> 00:02:37,700
还有一种让你去田里来好

75
00:02:37,700 --> 00:02:39,160
它存储的是什么类型呢

76
00:02:39,160 --> 00:02:40,040
这样一种是不是存储自传

77
00:02:40,040 --> 00:02:40,840
那么自传

78
00:02:40,840 --> 00:02:41,900
如果说你需要去存储

79
00:02:41,900 --> 00:02:43,420
其实还是去使用

80
00:02:43,420 --> 00:02:44,740
咱们自传类型比较好

81
00:02:44,740 --> 00:02:46,560
前面这种呢

82
00:02:46,560 --> 00:02:49,620
它就比较适合去使用我们的即可去存储

83
00:02:49,620 --> 00:02:51,140
所以说这里为了说明一个什么问题

84
00:02:51,140 --> 00:02:53,440
我们ralis它的存储方式

85
00:02:53,440 --> 00:02:54,800
其实没有绝对的规则

86
00:02:54,800 --> 00:02:57,640
比如说你可以看情况去利用字数创去存储

87
00:02:57,640 --> 00:02:58,620
也可以用集合去存储

88
00:02:58,620 --> 00:03:00,020
什么情况下适合用集合存储呢

89
00:03:00,020 --> 00:03:01,500
你比如说你有些需求

90
00:03:01,500 --> 00:03:03,580
需要用tags去搜索文章

91
00:03:03,580 --> 00:03:04,100
那么这里呢

92
00:03:04,100 --> 00:03:05,840
你就适合去用集合去处理

93
00:03:05,840 --> 00:03:06,520
那么下一节课

94
00:03:06,520 --> 00:03:07,440
我们就会去讲解

95
00:03:07,440 --> 00:03:08,460
怎么样去进行搜索

96
00:03:08,460 --> 00:03:10,440
我们现在总结一下这节课的内容

97
00:03:10,440 --> 00:03:11,480
这节课我们是不是讲解了

98
00:03:11,480 --> 00:03:12,420
怎么样通过集合去存储

99
00:03:12,420 --> 00:03:12,980
其实很简单

100
00:03:12,980 --> 00:03:13,780
通过add

101
00:03:13,780 --> 00:03:14,740
add什么呢

102
00:03:14,740 --> 00:03:16,400
是不是我们的属性的命名

103
00:03:16,400 --> 00:03:17,280
也就是咱们k的命名

104
00:03:17,280 --> 00:03:18,900
和评论的命名是很类似的

105
00:03:18,900 --> 00:03:20,760
post 代表我们的文章类型传入ID

106
00:03:20,760 --> 00:03:22,720
tax代表我们存入了一个

107
00:03:22,720 --> 00:03:24,320
是它的一些标签

108
00:03:24,320 --> 00:03:25,580
post id tax

109
00:03:25,580 --> 00:03:27,660
然后呢后面跟上我们需要去存储的标签

110
00:03:27,660 --> 00:03:29,760
然后呢你可以通过srim去双处它

111
00:03:29,760 --> 00:03:32,520
也可以通过smembers去获取这篇文章所有的tax

112
00:03:32,520 --> 00:03:34,780
这里呢就是存储文章标签这一节课的内容

