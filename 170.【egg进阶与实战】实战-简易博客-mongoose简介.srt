1
00:00:00,520 --> 00:00:02,820
好 那么这节课我们就来简单的学一下Mongoose

2
00:00:02,820 --> 00:00:04,100
那么什么是Mongoose呢

3
00:00:04,100 --> 00:00:06,400
其实它是对MongoDB的一层抽象和封装

4
00:00:06,400 --> 00:00:08,960
方便了我们在Lowdx中去操作MongoDB水库

5
00:00:08,960 --> 00:00:11,520
好 我们首先来看一下它的一个Github

6
00:00:11,520 --> 00:00:15,100
好 这里呢 其实就是它的一个Github

7
00:00:15,100 --> 00:00:17,920
那么它有多少颗星呢 其实是有一万九千多颗的

8
00:00:17,920 --> 00:00:21,760
我们来看一下它的一个介绍是什么 其实就是它的一个最显眼的

9
00:00:21,760 --> 00:00:25,860
Mongoose is a mongoDB object modern tool designed to work in an

10
00:00:25,860 --> 00:00:27,400
什么什么的environment

11
00:00:27,400 --> 00:00:29,180
那么什么意思呢 其实我也不知道

12
00:00:29,440 --> 00:00:30,180
怎么办呢

13
00:00:30,180 --> 00:00:31,300
把它翻译一下

14
00:00:31,300 --> 00:00:33,340
mongos是一个mongodb的对象建模工具

15
00:00:33,340 --> 00:00:34,840
只在在异步环境中工作

16
00:00:34,840 --> 00:00:35,780
好像还是没有看懂

17
00:00:35,780 --> 00:00:36,680
其实重点关注什么

18
00:00:36,680 --> 00:00:37,340
异步环境

19
00:00:37,340 --> 00:00:38,940
所以我们可以猜测

20
00:00:38,940 --> 00:00:40,180
mongos它的API是不是

21
00:00:40,180 --> 00:00:41,180
是异步的呀

22
00:00:41,180 --> 00:00:42,580
那么其实怎么理解它

23
00:00:42,580 --> 00:00:44,380
大家其实就可以把mongos理解为

24
00:00:44,380 --> 00:00:45,380
解quary

25
00:00:45,380 --> 00:00:46,680
其实它就是对mongodb

26
00:00:46,680 --> 00:00:47,880
它的操作的一个简化

27
00:00:47,880 --> 00:00:49,700
但是爱用js还是爱用解quary

28
00:00:49,700 --> 00:00:50,640
当然是解quary对吧

29
00:00:50,640 --> 00:00:51,240
它非常方便

30
00:00:51,240 --> 00:00:52,440
其实mongos也是这样的

31
00:00:52,440 --> 00:00:53,340
既方便又好用

32
00:00:53,340 --> 00:00:54,740
那么我们来简单看一下

33
00:00:54,740 --> 00:00:55,980
mongos它一个中文文档

34
00:00:55,980 --> 00:00:57,440
其实文档写得非常好

35
00:00:57,440 --> 00:00:58,280
也非常清晰

36
00:00:58,280 --> 00:00:58,880
而且呢

37
00:00:58,880 --> 00:00:59,540
有很多的内容

38
00:00:59,540 --> 00:01:00,560
比如说呢

39
00:01:00,560 --> 00:01:01,860
讲了它里面很多的一些概念

40
00:01:01,860 --> 00:01:03,300
schemas模式的一些类型

41
00:01:03,300 --> 00:01:04,040
包括呢

42
00:01:04,040 --> 00:01:04,540
文档

43
00:01:04,540 --> 00:01:04,900
Document

44
00:01:04,900 --> 00:01:06,220
是不是在mongodb里面

45
00:01:06,220 --> 00:01:06,900
很重要的一个概念

46
00:01:06,900 --> 00:01:08,080
查询中间件

47
00:01:08,080 --> 00:01:08,760
其实在mongo

48
00:01:08,760 --> 00:01:09,820
其实在mongos里面呢

49
00:01:09,820 --> 00:01:10,860
也可以去写一些中间件

50
00:01:10,860 --> 00:01:12,400
以及一些插件

51
00:01:12,400 --> 00:01:13,800
包括了还有它的API文档

52
00:01:13,800 --> 00:01:15,520
那么希望同学们能够下去之后

53
00:01:15,520 --> 00:01:16,620
好好的去看一下mongodb的文档

54
00:01:16,620 --> 00:01:17,560
因为我们主要是实战

55
00:01:17,560 --> 00:01:18,360
不可能说带着同学

56
00:01:18,360 --> 00:01:19,700
一个一个去学习

57
00:01:19,700 --> 00:01:19,980
好

58
00:01:19,980 --> 00:01:22,100
那么我们来进入我们mongos

59
00:01:22,100 --> 00:01:22,920
它的简单的学习

60
00:01:22,920 --> 00:01:24,080
那么这里呢

61
00:01:24,080 --> 00:01:25,500
我们来看一下蒙古斯它的一个简介

62
00:01:25,500 --> 00:01:27,420
就是咱们一个简单的使用

63
00:01:27,420 --> 00:01:30,060
只在目的是让同学去了解它是什么东西

64
00:01:30,060 --> 00:01:31,520
好 我们先来看一下

65
00:01:31,520 --> 00:01:33,200
在蒙古斯里面一切始于schema

66
00:01:33,200 --> 00:01:34,380
什么是schema

67
00:01:34,380 --> 00:01:37,360
schema是对数据模型的描述

68
00:01:37,360 --> 00:01:39,920
是对应数据库存储的字段

69
00:01:39,920 --> 00:01:41,080
什么意思

70
00:01:41,080 --> 00:01:44,260
比如说我们现在有一个文章的schema

71
00:01:44,260 --> 00:01:45,980
因为我们的项目是要去实现一个博客

72
00:01:45,980 --> 00:01:47,500
那么我们核心是围绕什么去

73
00:01:47,500 --> 00:01:49,380
对 是不是围绕我们的一个文章

74
00:01:49,380 --> 00:01:51,660
那么如果说我们要创建一篇文章

75
00:01:51,660 --> 00:01:53,040
太有哪些属性了

76
00:01:53,040 --> 00:01:54,160
是不是title和content

77
00:01:54,160 --> 00:01:54,860
title代表标题

78
00:01:54,860 --> 00:01:55,780
content代表我们的证文

79
00:01:55,780 --> 00:01:57,900
那么schema调用mongus.schema

80
00:01:57,900 --> 00:01:58,660
其实就对我们文章

81
00:01:58,660 --> 00:01:59,180
它的描述

82
00:01:59,180 --> 00:01:59,700
比如说title呢

83
00:01:59,700 --> 00:02:00,900
我希望它是字不串类型

84
00:02:00,900 --> 00:02:01,700
content呢

85
00:02:01,700 --> 00:02:02,920
我也希望它是字不串类型

86
00:02:02,920 --> 00:02:03,720
然后呢

87
00:02:03,720 --> 00:02:04,400
返回了一个值

88
00:02:04,400 --> 00:02:05,360
我们给它命名为post

89
00:02:05,360 --> 00:02:06,440
postschema

90
00:02:06,440 --> 00:02:07,600
代表我们文章的一个schema

91
00:02:07,600 --> 00:02:08,500
那么我们来看一下

92
00:02:08,500 --> 00:02:09,660
到底什么是它

93
00:02:09,660 --> 00:02:10,720
那么其实刚才

94
00:02:10,720 --> 00:02:11,500
是不是给同学们演示过

95
00:02:11,500 --> 00:02:12,700
我们文章的存储的时候

96
00:02:12,700 --> 00:02:13,640
是不是主要有四个字吧

97
00:02:13,640 --> 00:02:15,080
最核心的就是title和content

98
00:02:15,080 --> 00:02:15,860
其实呢

99
00:02:15,860 --> 00:02:17,500
也就是对应我们schema的一个

100
00:02:17,500 --> 00:02:22,540
是不是对于我们schema的一个content和我们的抬头

101
00:02:22,540 --> 00:02:24,240
这里就是schema它的作用

102
00:02:24,240 --> 00:02:27,020
也就是对我们数据库存储自断的一个描述

103
00:02:27,020 --> 00:02:29,020
那么我们既然有了描述

104
00:02:29,020 --> 00:02:31,680
是不是此时需要去创建一个什么

105
00:02:31,680 --> 00:02:33,460
创建一个model

106
00:02:33,460 --> 00:02:35,860
那么在萌故事里面其实也有model的这样的一个概念

107
00:02:35,860 --> 00:02:37,720
比如说我们通过posts之schema

108
00:02:37,720 --> 00:02:40,680
也就是通过我们文章的描述去创建一个posts这样的model

109
00:02:40,680 --> 00:02:41,720
然后反复一个posts

110
00:02:41,720 --> 00:02:45,460
其实咱们核心就是通过它的schema去创建了文章的一个model

111
00:02:45,460 --> 00:02:46,400
那么model是什么呢

112
00:02:46,400 --> 00:02:49,880
model是我们创造document的class说明它是一个类

113
00:02:49,880 --> 00:02:53,900
那么每一个document都是一篇文章

114
00:02:53,900 --> 00:02:55,360
那么既然它是一类

115
00:02:55,360 --> 00:02:57,280
所以我们接下来一定需要做一个什么操作

116
00:02:57,280 --> 00:02:58,140
是不是一定要new它

117
00:02:58,140 --> 00:02:59,240
那么我们new的时候

118
00:02:59,240 --> 00:03:02,560
是不是就需要去传入咱们一个具体的标题是什么

119
00:03:02,560 --> 00:03:03,580
和咱们证文是什么

120
00:03:03,580 --> 00:03:05,640
其实和我们介绍面向对象的用法

121
00:03:05,640 --> 00:03:07,540
是不是思想稍微有一点类似

122
00:03:07,540 --> 00:03:08,160
非常相似

123
00:03:08,160 --> 00:03:10,380
好我们这里去创建了一个文章的实力

124
00:03:10,380 --> 00:03:12,020
我们去new post这样的一个model

125
00:03:12,020 --> 00:03:14,020
得到了一个比如说我们得到了一个真正的实力

126
00:03:14,020 --> 00:03:15,240
叫做news

127
00:03:15,240 --> 00:03:16,340
对吧一篇真正的文章

128
00:03:16,340 --> 00:03:18,360
那么我们既然得到文章之后

129
00:03:18,360 --> 00:03:19,140
接下来需要做什么呀

130
00:03:19,140 --> 00:03:20,260
得到文章之后

131
00:03:20,260 --> 00:03:21,600
比如说我们去new了之后

132
00:03:21,600 --> 00:03:22,540
它有没有存储到数据库

133
00:03:22,540 --> 00:03:24,020
其实是没有的

134
00:03:24,020 --> 00:03:24,380
为什么呀

135
00:03:24,380 --> 00:03:25,780
因为我们的js里面去new了之后

136
00:03:25,780 --> 00:03:26,500
是不是创建了一个对象

137
00:03:26,500 --> 00:03:29,460
我们是不是需要去手动的去保存

138
00:03:29,460 --> 00:03:31,460
所以其实在news上面

139
00:03:31,460 --> 00:03:32,300
其实有这样一个方法

140
00:03:32,300 --> 00:03:32,980
叫做save

141
00:03:32,980 --> 00:03:34,540
那么save是谁题目呢

142
00:03:34,540 --> 00:03:35,080
大家想一下

143
00:03:35,080 --> 00:03:37,060
是不是post这样的一个lay

144
00:03:37,060 --> 00:03:37,680
它是一个class

145
00:03:37,680 --> 00:03:38,840
所以当我们save之后

146
00:03:38,840 --> 00:03:40,800
就会存储到我们的数据库里面

147
00:03:40,800 --> 00:03:42,020
那么我们save之后

148
00:03:42,020 --> 00:03:43,260
我们其实每save一次

149
00:03:43,260 --> 00:03:45,020
每save一次就代表我们数据库的

150
00:03:45,020 --> 00:03:46,020
数据库里面的一条

151
00:03:46,020 --> 00:03:47,700
是不是代表我们在一张表帕里面的一条

152
00:03:47,700 --> 00:03:48,520
数据

153
00:03:48,520 --> 00:03:49,060
好

154
00:03:49,060 --> 00:03:49,940
那么这里呢

155
00:03:49,940 --> 00:03:50,820
既然讲到的save

156
00:03:50,820 --> 00:03:51,220
save是什么

157
00:03:51,220 --> 00:03:52,240
save是不是存储

158
00:03:52,240 --> 00:03:54,160
存储就是咱们真商改查中的对应哪一个环节

159
00:03:54,160 --> 00:03:54,540
是不是真

160
00:03:54,540 --> 00:03:56,060
那么既然有真

161
00:03:56,060 --> 00:03:58,000
那肯定相应的有啥呀

162
00:03:58,000 --> 00:03:59,300
是不是有查呀

163
00:03:59,300 --> 00:04:00,340
那么怎么样去查呢

164
00:04:00,340 --> 00:04:01,740
查其实就很简单

165
00:04:01,740 --> 00:04:03,120
其实我们调用post指的find

166
00:04:03,120 --> 00:04:03,680
post是什么

167
00:04:03,680 --> 00:04:04,880
post是不是我们的model一个类

168
00:04:04,880 --> 00:04:06,080
它上面有一个find的方法

169
00:04:06,080 --> 00:04:08,260
我们就可以通过去查找我们的某些文章

170
00:04:08,260 --> 00:04:10,500
比如说你想去查找我们的开头

171
00:04:10,500 --> 00:04:11,920
名字为书的一些数据

172
00:04:11,920 --> 00:04:36,400
你可以

173
00:04:36,400 --> 00:04:39,560
那么schema其实就是对我们自端的一个描述

174
00:04:39,560 --> 00:04:41,560
那么是不是就对应我们数据库里面的一个

175
00:04:41,560 --> 00:04:43,500
自端的title和content

176
00:04:43,500 --> 00:04:45,900
那么model呢model是不是就代表我们的什么呀

177
00:04:45,900 --> 00:04:47,300
model就代表我们的表

178
00:04:47,300 --> 00:04:48,200
代表我们的表

179
00:04:48,200 --> 00:04:49,060
比如说我们

180
00:04:49,060 --> 00:04:50,400
比如说我们去存储文章

181
00:04:50,400 --> 00:04:52,060
它的表的名称叫什么叫post

182
00:04:52,060 --> 00:04:55,100
那么当然了我们项目里面是不是还会有一个cool的名称呢

183
00:04:55,100 --> 00:04:56,200
cool的名称叫做example

184
00:04:56,200 --> 00:04:57,600
那么这样一个example从哪里来呢

185
00:04:57,600 --> 00:04:59,200
等一下呢老师会给同学去演示

186
00:04:59,200 --> 00:05:01,960
好这里呢就是我们这节课的内容

