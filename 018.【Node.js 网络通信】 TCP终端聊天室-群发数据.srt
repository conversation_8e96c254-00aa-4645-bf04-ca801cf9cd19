1
00:00:00,000 --> 00:00:02,900
有了这样一个代码基础之后

2
00:00:02,900 --> 00:00:03,940
然后接下来呢

3
00:00:03,940 --> 00:00:05,200
我们就可以利用这个

4
00:00:05,200 --> 00:00:07,160
结合这几个小功能特性

5
00:00:07,160 --> 00:00:09,240
然后去做一个非常有意思的东西

6
00:00:09,240 --> 00:00:12,140
就是基于终端的一个聊天室

7
00:00:12,140 --> 00:00:13,720
基于终端的一个聊天室

8
00:00:13,720 --> 00:00:14,640
什么意思呢

9
00:00:14,640 --> 00:00:15,280
我们这个

10
00:00:15,280 --> 00:00:16,980
我们把这个业务功能设计成这样

11
00:00:16,980 --> 00:00:18,720
就是说我们第一次上来

12
00:00:18,720 --> 00:00:19,820
第一次上来以后

13
00:00:19,820 --> 00:00:21,380
我们就提示提示用户

14
00:00:21,380 --> 00:00:22,360
就是说请

15
00:00:22,360 --> 00:00:25,000
请输入昵称

16
00:00:25,000 --> 00:00:26,980
我们让这个用户来输入自己的昵称

17
00:00:26,980 --> 00:00:28,000
说完以后

18
00:00:28,000 --> 00:00:29,000
然后他回车

19
00:00:29,000 --> 00:00:30,640
然后进入我们的聊天室

20
00:00:30,640 --> 00:00:31,520
然后进来以后

21
00:00:31,520 --> 00:00:32,580
当然我们服务端

22
00:00:32,580 --> 00:00:33,500
要对这个昵称

23
00:00:33,500 --> 00:00:35,100
是否重复进行一个教验

24
00:00:35,100 --> 00:00:36,340
如果说重复了

25
00:00:36,340 --> 00:00:37,720
那我们就不让他进来

26
00:00:37,720 --> 00:00:38,860
如果说没有重复

27
00:00:38,860 --> 00:00:40,480
那我们就让他允许进来

28
00:00:40,480 --> 00:00:41,600
所以说就是这样一个

29
00:00:41,600 --> 00:00:42,800
简单的小功能

30
00:00:42,800 --> 00:00:43,940
那么进来以后

31
00:00:43,940 --> 00:00:44,720
然后再接下来

32
00:00:44,720 --> 00:00:45,540
客户端就可以在这

33
00:00:45,540 --> 00:00:46,700
去进行发送消息了

34
00:00:46,700 --> 00:00:47,820
有他说一个Hello

35
00:00:47,820 --> 00:00:48,540
那么这里

36
00:00:48,540 --> 00:00:49,680
我们就把他答案出来说

37
00:00:49,680 --> 00:00:51,080
例如这个李四

38
00:00:51,080 --> 00:00:53,400
例如李四说

39
00:00:53,400 --> 00:00:54,220
说什么呢

40
00:00:54,220 --> 00:00:55,740
说他刚才发的这个消息

41
00:00:55,740 --> 00:00:56,760
去答案出来

42
00:00:56,760 --> 00:00:58,680
然后我们是有多个客户端的

43
00:00:58,680 --> 00:01:00,720
然后其他客户就都会收到这个消息

44
00:01:00,720 --> 00:01:01,740
看到这个李斯说

45
00:01:01,740 --> 00:01:04,240
所以说就是这样一个功能

46
00:01:04,240 --> 00:01:06,080
那了解了这个基本需求之后

47
00:01:06,080 --> 00:01:07,320
然后接下来

48
00:01:07,320 --> 00:01:09,880
我们再来就把它来给简单的实现一下

49
00:01:09,880 --> 00:01:10,460
实现一下

50
00:01:10,460 --> 00:01:11,800
好

51
00:01:11,800 --> 00:01:13,140
为了方便

52
00:01:13,140 --> 00:01:16,840
我就不再去对这两个文件去进行修改了

53
00:01:16,840 --> 00:01:18,900
所以我们在当前这个单板当中

54
00:01:18,900 --> 00:01:20,780
再来给他创建一个目录

55
00:01:20,780 --> 00:01:22,920
这个目录呢

56
00:01:22,920 --> 00:01:24,540
我们给他起名为Chat

57
00:01:24,540 --> 00:01:27,440
叫Chatroom

58
00:01:27,440 --> 00:01:30,640
就是聊天室

59
00:01:30,640 --> 00:01:31,120
好了

60
00:01:31,120 --> 00:01:32,240
然后接下来

61
00:01:32,240 --> 00:01:34,380
当然我们再来换个名字

62
00:01:34,380 --> 00:01:36,140
叫TCP

63
00:01:36,140 --> 00:01:37,520
TCP聊天室

64
00:01:37,520 --> 00:01:40,240
我们在里面同样的创建两个文件

65
00:01:40,240 --> 00:01:41,400
一个叫server.js

66
00:01:41,400 --> 00:01:43,120
一个叫client.js

67
00:01:43,120 --> 00:01:46,100
然后接下来我们把这两个

68
00:01:46,100 --> 00:01:48,000
客户端和服务器快速来给它实现出来

69
00:01:48,000 --> 00:01:49,480
还是我们刚才这个代码

70
00:01:49,480 --> 00:01:50,660
然后net

71
00:01:50,660 --> 00:01:52,540
然后net.create

72
00:01:52,540 --> 00:01:53,720
server

73
00:01:53,720 --> 00:01:56,300
然后在这里去接收一下这个server

74
00:01:56,300 --> 00:01:57,780
然后监听一下

75
00:01:57,780 --> 00:01:58,740
这个Server的叫做

76
00:01:58,740 --> 00:01:59,900
Connection事件

77
00:01:59,900 --> 00:02:01,400
然后拿到这个叫

78
00:02:01,400 --> 00:02:02,720
Client Socket

79
00:02:02,720 --> 00:02:05,060
然后上来以后

80
00:02:05,060 --> 00:02:05,940
我们去盯听

81
00:02:05,940 --> 00:02:07,960
这个客户端的这个数据

82
00:02:07,960 --> 00:02:11,240
盯听这个客户端的数据

83
00:02:11,240 --> 00:02:12,780
我们直接去图词证一下

84
00:02:12,780 --> 00:02:13,440
好了

85
00:02:13,440 --> 00:02:14,700
然后我们去给他

86
00:02:14,700 --> 00:02:16,200
发送一个消息过去

87
00:02:16,200 --> 00:02:17,000
Right

88
00:02:17,000 --> 00:02:18,220
确保这个没有问题

89
00:02:18,220 --> 00:02:18,840
好了

90
00:02:18,840 --> 00:02:20,860
最后去Server.Listen

91
00:02:20,860 --> 00:02:22,300
给我给个3000

92
00:02:22,300 --> 00:02:23,640
好

93
00:02:23,640 --> 00:02:24,200
做一个提示

94
00:02:24,200 --> 00:02:24,920
然后

95
00:02:24,920 --> 00:02:26,580
server

96
00:02:26,580 --> 00:02:28,220
软利

97
00:02:28,220 --> 00:02:29,340
不要太运行起来了

98
00:02:29,340 --> 00:02:29,480
好了

99
00:02:29,480 --> 00:02:30,740
这个最基本的服务脚本

100
00:02:30,740 --> 00:02:31,760
我们待会就串联好

101
00:02:31,760 --> 00:02:32,140
好

102
00:02:32,140 --> 00:02:32,540
完了以后

103
00:02:32,540 --> 00:02:33,260
然后接下来客户端

104
00:02:33,260 --> 00:02:35,540
这个叫net引进来

105
00:02:35,540 --> 00:02:38,220
好了

106
00:02:38,220 --> 00:02:39,160
引进来以后

107
00:02:39,160 --> 00:02:41,520
然后我们继续写后续功能

108
00:02:41,520 --> 00:02:43,860
那么这里的话

109
00:02:43,860 --> 00:02:45,420
就是net.create

110
00:02:45,420 --> 00:02:46,840
connection

111
00:02:46,840 --> 00:02:47,600
好

112
00:02:47,600 --> 00:02:48,200
我们要告诉他

113
00:02:48,200 --> 00:02:49,480
连接到本地

114
00:02:49,480 --> 00:02:52,100
127.0.0.1

115
00:02:52,100 --> 00:02:52,740
然后

116
00:02:52,740 --> 00:02:53,920
part端口号

117
00:02:53,920 --> 00:02:54,840
3000

118
00:02:54,840 --> 00:02:57,100
我们得到这个client

119
00:02:57,100 --> 00:03:00,420
得到它以后

120
00:03:00,420 --> 00:03:02,180
然后将来我们再去client

121
00:03:02,180 --> 00:03:05,380
监听这个服务端想过来的data

122
00:03:05,380 --> 00:03:09,240
我们去tostream一下

123
00:03:09,240 --> 00:03:10,720
输出这个内容结果

124
00:03:10,720 --> 00:03:12,160
当然client还有一个实践

125
00:03:12,160 --> 00:03:13,720
叫做connect

126
00:03:13,720 --> 00:03:14,740
连接实践

127
00:03:14,740 --> 00:03:16,820
这个就表示

128
00:03:16,820 --> 00:03:20,120
客户端与服务器建立连接

129
00:03:20,120 --> 00:03:21,580
成功

130
00:03:21,580 --> 00:03:24,120
然后这个时候我们可以让客户端

131
00:03:24,120 --> 00:03:25,840
去给服务端发一个消息

132
00:03:25,840 --> 00:03:27,040
又发一个woad

133
00:03:27,040 --> 00:03:29,680
好 那我们就把这个聊天室

134
00:03:29,680 --> 00:03:31,380
这个客户端和服务器

135
00:03:31,380 --> 00:03:32,740
最基本的一个节奏

136
00:03:32,740 --> 00:03:33,560
就给大家写出来

137
00:03:33,560 --> 00:03:34,660
好 写出来以后

138
00:03:34,660 --> 00:03:35,900
我们大家启动来测试一下

139
00:03:35,900 --> 00:03:38,240
然后打开我们的命令行

140
00:03:38,240 --> 00:03:39,500
把他们的关键

141
00:03:39,500 --> 00:03:41,780
然后cd到我们这个叫

142
00:03:41,780 --> 00:03:42,800
chadroom当中

143
00:03:42,800 --> 00:03:45,520
chadroom当中

144
00:03:45,520 --> 00:03:47,180
好 那这里的话

145
00:03:47,180 --> 00:03:48,980
首先我们再去启动我们的服务端

146
00:03:48,980 --> 00:03:49,880
server

147
00:03:49,880 --> 00:03:51,600
然后再来我们的客户端

148
00:03:51,600 --> 00:03:52,100
client

149
00:03:52,100 --> 00:03:53,320
好 我们这样就可以看到

150
00:03:53,320 --> 00:03:55,220
来 服务单给客户端发这个hello

151
00:03:55,220 --> 00:03:56,860
客户端给服务单发这个wo的

152
00:03:56,860 --> 00:03:57,460
没有问题

153
00:03:57,460 --> 00:03:59,680
基本结构准备好以后

154
00:03:59,680 --> 00:04:00,940
然后接下来

155
00:04:00,940 --> 00:04:03,680
接下来我们就把我们这个功能

156
00:04:03,680 --> 00:04:04,960
来给大家简单实现一下

157
00:04:04,960 --> 00:04:05,640
简单实现一下

158
00:04:05,640 --> 00:04:07,820
我们先来实现一个最简单的功能

159
00:04:07,820 --> 00:04:08,400
最简单的功能

160
00:04:08,400 --> 00:04:11,360
就是说关于这个叫消息的群发

161
00:04:11,360 --> 00:04:12,160
消息的群发

162
00:04:12,160 --> 00:04:13,400
就是说我们的客户端

163
00:04:13,400 --> 00:04:14,900
是可以有多个的

164
00:04:14,900 --> 00:04:15,680
可以有多个

165
00:04:15,680 --> 00:04:18,380
所以我们在这就可以看到

166
00:04:18,380 --> 00:04:20,500
我再次新建一个就是命令和窗口

167
00:04:20,500 --> 00:04:21,560
我在这里的话

168
00:04:21,560 --> 00:04:22,440
我再来

169
00:04:22,440 --> 00:04:24,020
来连接啊

170
00:04:24,020 --> 00:04:24,520
我这个sir

171
00:04:24,520 --> 00:04:25,320
连接client

172
00:04:25,320 --> 00:04:26,120
就是client

173
00:04:26,120 --> 00:04:26,960
连接sir

174
00:04:26,960 --> 00:04:27,940
这时候我们看到

175
00:04:27,940 --> 00:04:29,320
你看又有一个客户端

176
00:04:29,320 --> 00:04:30,560
是不是加入我们的服务端链

177
00:04:30,560 --> 00:04:32,240
那接下来我们先实现一个

178
00:04:32,240 --> 00:04:32,980
最简单的功能

179
00:04:32,980 --> 00:04:34,680
就是说任何客户端发的消息

180
00:04:34,680 --> 00:04:36,600
然后让所有客户端都能看见

181
00:04:36,600 --> 00:04:38,460
因为我带着写个11一回车

182
00:04:38,460 --> 00:04:39,960
不仅服务端而输出

183
00:04:39,960 --> 00:04:41,680
其他的客户端也都应该被看到

184
00:04:41,680 --> 00:04:44,080
那这个东西应该怎么来做呢

185
00:04:44,080 --> 00:04:45,040
其实方式呢

186
00:04:45,040 --> 00:04:45,920
也非常的简单

187
00:04:45,920 --> 00:04:47,380
也就是说说白了

188
00:04:47,380 --> 00:04:48,660
我们把客户端发的消息

189
00:04:48,660 --> 00:04:50,680
再回传给其他客户端

190
00:04:50,680 --> 00:04:52,100
那怎么来做呢

191
00:04:52,100 --> 00:04:52,480
来看一下

192
00:04:52,480 --> 00:04:54,660
我们每一个客户端与服务端

193
00:04:54,660 --> 00:04:55,860
建议连接成功以后

194
00:04:55,860 --> 00:04:57,600
服务端就能通过这个回调函数

195
00:04:57,600 --> 00:04:58,300
这个client socket

196
00:04:58,300 --> 00:05:00,960
就是得到当前这个客户端的通信接口

197
00:05:00,960 --> 00:05:02,720
我们要说白了

198
00:05:02,720 --> 00:05:04,360
那这个可能是有多个的

199
00:05:04,360 --> 00:05:06,580
所以说我们在这创建一个client

200
00:05:06,580 --> 00:05:07,680
就是数组

201
00:05:07,680 --> 00:05:08,740
用它来干嘛呢

202
00:05:08,740 --> 00:05:11,080
用来存储这些客户端的通信socket

203
00:05:11,080 --> 00:05:13,920
所以说只要客户端与服务端连接成功

204
00:05:13,920 --> 00:05:15,520
那我们就再连接上来

205
00:05:15,520 --> 00:05:17,560
我们就client.push

206
00:05:17,560 --> 00:05:19,020
我们这个叫client socket

207
00:05:19,020 --> 00:05:20,700
就说说白了

208
00:05:20,700 --> 00:05:22,440
把这个客户端的

209
00:05:22,440 --> 00:05:26,540
把当前连接的客户端

210
00:05:26,540 --> 00:05:29,320
这个通信中通信

211
00:05:29,320 --> 00:05:30,620
通信接口

212
00:05:30,620 --> 00:05:32,480
存储到这个数据中

213
00:05:32,480 --> 00:05:33,560
存储起来

214
00:05:33,560 --> 00:05:35,100
我们马上就要去用到它了

215
00:05:35,100 --> 00:05:36,000
马上就要用到它

216
00:05:36,000 --> 00:05:37,100
我们接下来怎么去用呢

217
00:05:37,100 --> 00:05:38,960
那么每当我们带着去收到

218
00:05:38,960 --> 00:05:40,340
收到这个客户端数据的时候

219
00:05:40,340 --> 00:05:42,720
我们一个是服务单带着去

220
00:05:42,720 --> 00:05:43,400
打印了一下

221
00:05:43,400 --> 00:05:45,820
就是说我们先随便来打印一个

222
00:05:45,820 --> 00:05:46,400
这个叫

223
00:05:46,400 --> 00:05:48,300
有人说

224
00:05:48,300 --> 00:05:49,220
有人说什么呢

225
00:05:49,220 --> 00:05:50,340
说这么这样一个消息

226
00:05:50,340 --> 00:05:50,860
好

227
00:05:50,860 --> 00:05:51,580
我们打印完之后

228
00:05:51,580 --> 00:05:52,400
然后接下来怎么着呢

229
00:05:52,400 --> 00:05:53,480
把这个数据

230
00:05:53,480 --> 00:05:54,560
发给

231
00:05:54,560 --> 00:05:55,760
发给这个

232
00:05:55,760 --> 00:05:57,220
所有的客户团

233
00:05:57,220 --> 00:05:57,980
怎么去发

234
00:05:57,980 --> 00:05:58,460
非常简单

235
00:05:58,460 --> 00:06:00,980
我们在直接client.forEach

236
00:06:00,980 --> 00:06:02,600
然后大致呢

237
00:06:02,600 --> 00:06:03,100
就是有这个叫

238
00:06:03,100 --> 00:06:04,260
其实就是每一个socket

239
00:06:04,260 --> 00:06:05,380
就是每一个client的socket

240
00:06:05,380 --> 00:06:06,480
好

241
00:06:06,480 --> 00:06:08,580
我们在这里让它去socket.write

242
00:06:08,580 --> 00:06:09,400
write什么呢

243
00:06:09,400 --> 00:06:10,960
write我们某客户端

244
00:06:10,960 --> 00:06:11,840
说的这个data

245
00:06:11,840 --> 00:06:13,220
这个消息数据

246
00:06:13,220 --> 00:06:13,920
说这个data

247
00:06:13,920 --> 00:06:14,440
这个消息数据

248
00:06:14,440 --> 00:06:15,500
所以就是这个意思

249
00:06:15,500 --> 00:06:16,300
好

250
00:06:16,300 --> 00:06:17,280
当然我们在这其实

251
00:06:17,280 --> 00:06:18,700
我们应该把它自身

252
00:06:18,700 --> 00:06:20,140
是不是给它排除在外

253
00:06:20,140 --> 00:06:22,060
我们给它自身去排除在外

254
00:06:22,060 --> 00:06:23,500
所以说我们在这可以

255
00:06:23,500 --> 00:06:24,360
这样的判断

256
00:06:24,360 --> 00:06:27,880
如果这个client socket

257
00:06:27,880 --> 00:06:29,180
就是如果它

258
00:06:29,180 --> 00:06:30,320
就说完了

259
00:06:30,320 --> 00:06:31,740
如果当前便利的这个socket

260
00:06:31,740 --> 00:06:34,140
它不是当前说话的这个人

261
00:06:34,140 --> 00:06:35,620
例如这个就是

262
00:06:35,620 --> 00:06:37,320
就是这个raw socket

263
00:06:37,320 --> 00:06:38,660
不同意这个叫client socket

264
00:06:38,660 --> 00:06:39,600
好了

265
00:06:39,600 --> 00:06:40,460
那么我们在这里

266
00:06:40,460 --> 00:06:42,000
就给它把这个消息

267
00:06:42,000 --> 00:06:42,700
发过去

268
00:06:42,700 --> 00:06:43,400
然后这样的话

269
00:06:43,400 --> 00:06:44,680
就发给了所有人

270
00:06:44,680 --> 00:06:45,920
就socket.write这个data

271
00:06:45,920 --> 00:06:46,560
就这样

272
00:06:46,560 --> 00:06:47,460
好

273
00:06:47,460 --> 00:06:48,660
那么这样写完以后

274
00:06:48,660 --> 00:06:49,460
注意我们这个

275
00:06:49,460 --> 00:06:51,000
其实简单的群聊功能

276
00:06:51,000 --> 00:06:52,200
就已经实现了

277
00:06:52,200 --> 00:06:53,740
接下来我们可以来验证一下

278
00:06:53,740 --> 00:06:54,860
回到控制台

279
00:06:54,860 --> 00:06:56,120
我们为了方便

280
00:06:56,120 --> 00:06:56,860
我们把所有中单

281
00:06:56,860 --> 00:06:57,780
都给它给关闭一下

282
00:06:57,780 --> 00:06:58,540
关闭一下

283
00:06:58,540 --> 00:06:59,300
首先

284
00:06:59,300 --> 00:07:00,780
来启动我们的server

285
00:07:00,780 --> 00:07:02,140
然后

286
00:07:02,140 --> 00:07:03,300
来

287
00:07:03,300 --> 00:07:04,920
启动我们的client

288
00:07:04,920 --> 00:07:08,560
同样的

289
00:07:08,560 --> 00:07:09,540
node more

290
00:07:09,540 --> 00:07:10,120
我们的client

291
00:07:10,120 --> 00:07:11,180
好了

292
00:07:11,180 --> 00:07:12,060
我们这来看到

293
00:07:12,060 --> 00:07:12,600
服务单这

294
00:07:12,600 --> 00:07:13,300
是不是说出来

295
00:07:13,300 --> 00:07:14,040
有人说呀

296
00:07:14,040 --> 00:07:15,180
接下来我给他发个消息

297
00:07:15,180 --> 00:07:15,660
来看一下

298
00:07:15,660 --> 00:07:17,120
111回车

299
00:07:17,120 --> 00:07:18,940
222回车

300
00:07:18,940 --> 00:07:21,020
我们可以看到我们这的数据

301
00:07:21,020 --> 00:07:22,080
为什么没有发出去呢

302
00:07:22,080 --> 00:07:23,140
原因很简单

303
00:07:23,140 --> 00:07:24,560
我们并没有把客户端的数据

304
00:07:24,560 --> 00:07:25,460
就是发到服务端

305
00:07:25,460 --> 00:07:27,900
然后让服务端去帮我们去群发这个消息吧

306
00:07:27,900 --> 00:07:29,180
所以说

307
00:07:29,180 --> 00:07:30,960
所以说我们在这里别忘了一件事

308
00:07:30,960 --> 00:07:32,760
我们还得去在这里这样来做

309
00:07:32,760 --> 00:07:34,280
就是去clad

310
00:07:34,280 --> 00:07:40,280
这个叫process.std.ondate

311
00:07:40,280 --> 00:07:42,920
拿到控制台的数据

312
00:07:42,920 --> 00:07:43,940
我们接受到以后

313
00:07:43,940 --> 00:07:46,620
我们直接让这个data去tostream

314
00:07:46,620 --> 00:07:47,860
然后trim一下

315
00:07:47,860 --> 00:07:49,340
把这个空格

316
00:07:49,340 --> 00:07:50,760
还有什么回程换行都给它去掉

317
00:07:50,760 --> 00:07:51,780
去掉以后

318
00:07:51,780 --> 00:07:55,020
接下来我们再去这个client.write

319
00:07:55,020 --> 00:07:56,700
write这个data

320
00:07:56,700 --> 00:07:57,380
然后这样的话

321
00:07:57,380 --> 00:07:58,300
是不是就把这个data

322
00:07:58,300 --> 00:08:00,900
去发到我们这个服务端链

323
00:08:00,900 --> 00:08:02,200
发到这个服务端了

324
00:08:02,200 --> 00:08:03,020
发到服务端

325
00:08:03,020 --> 00:08:04,480
服务端就监听到当前客户端

326
00:08:04,480 --> 00:08:05,460
发这个消息数据了

327
00:08:05,460 --> 00:08:06,420
拿到以后

328
00:08:06,420 --> 00:08:07,000
然后这打印一下

329
00:08:07,000 --> 00:08:07,420
有人说

330
00:08:07,420 --> 00:08:08,520
然后接下来便利

331
00:08:08,520 --> 00:08:09,900
便利所有的client

332
00:08:09,900 --> 00:08:11,120
也就是所有的客户端

333
00:08:11,120 --> 00:08:13,160
当然把它自己排除在外

334
00:08:13,160 --> 00:08:14,480
如果这个Sorted

335
00:08:14,480 --> 00:08:16,620
就是不等于是这个

336
00:08:16,620 --> 00:08:18,340
当前的这个ClientSorted

337
00:08:18,340 --> 00:08:20,380
那么我们在这就让他把这个消息

338
00:08:20,380 --> 00:08:22,580
发给就是说话这个人之外的

339
00:08:22,580 --> 00:08:23,240
其他的用户

340
00:08:23,240 --> 00:08:24,540
所以就是这个意思

341
00:08:24,540 --> 00:08:27,220
那接下来我们在这就可以再来进行测试

342
00:08:27,220 --> 00:08:29,620
来我们在这把它先关闭一下

343
00:08:29,620 --> 00:08:30,300
关闭一下

344
00:08:30,300 --> 00:08:31,740
我们在这启动

345
00:08:31,740 --> 00:08:33,220
启动

346
00:08:33,220 --> 00:08:36,220
好我们在这看一下我们在这输入111

347
00:08:36,220 --> 00:08:38,160
来大家可以看到我们在服务端

348
00:08:38,160 --> 00:08:39,680
是不是输出了有人说111

349
00:08:39,680 --> 00:08:42,100
我们在输入222

350
00:08:42,100 --> 00:08:43,960
你看不仅这里输出1122

351
00:08:43,960 --> 00:08:45,760
其他客户端是不是也输出了

352
00:08:45,760 --> 00:08:46,560
这个1122

353
00:08:46,560 --> 00:08:49,420
大家好啊回车

354
00:08:49,420 --> 00:08:50,980
然后你看到我们这个客户端

355
00:08:50,980 --> 00:08:51,980
是不是也打印出来了

356
00:08:51,980 --> 00:08:53,020
大家好啊

357
00:08:53,020 --> 00:08:55,160
所以在这呢就是这个意思

358
00:08:55,160 --> 00:08:55,900
就这个意思

359
00:08:55,900 --> 00:08:57,520
好那这样的话

360
00:08:57,520 --> 00:08:59,000
我们这个聊天室

361
00:08:59,000 --> 00:09:01,120
就实现了一个非常简单的这种群聊功能

362
00:09:01,120 --> 00:09:03,440
那这个你好回车

363
00:09:03,440 --> 00:09:05,840
你看这个端的这个用户

364
00:09:05,840 --> 00:09:07,120
是不是也就收到了你好的消息

365
00:09:07,120 --> 00:09:08,960
他在这把哈哈回车

366
00:09:08,960 --> 00:09:10,300
你看这里也能收到

367
00:09:10,300 --> 00:09:11,220
111回车

368
00:09:11,220 --> 00:09:12,300
你看是不是已经收到

369
00:09:12,300 --> 00:09:14,700
当然我们服务端肯定是能收到所有人的消息的

370
00:09:14,700 --> 00:09:17,200
就是服务端做一个代理转发的一个过程

371
00:09:17,200 --> 00:09:18,700
那这个就是这个

372
00:09:18,700 --> 00:09:22,840
我们这个聊天室最基本的这个群聊功能的一个实现

