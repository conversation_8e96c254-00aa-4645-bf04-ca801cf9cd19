1
00:00:00,000 --> 00:00:03,160
好刚才我们是不是使用咱们的Core去架设一个htb的服务

2
00:00:03,160 --> 00:00:05,560
那么接下来我们来看一下咱们基本用法中剩下的内容

3
00:00:05,560 --> 00:00:12,140
首先我们来看一下咱们起了服务之后怎么样去返回一个东西

4
00:00:12,140 --> 00:00:13,640
因为刚才咱们返回是多放的

5
00:00:13,640 --> 00:00:15,240
那么我们来看一下怎么去返回一个东西

6
00:00:15,240 --> 00:00:19,400
这里就要用到在Core里面它有一个context对象

7
00:00:19,400 --> 00:00:23,440
像Core它提供一个context对象表示咱们对话的上下文

8
00:00:23,440 --> 00:00:27,860
那么你通过去加工这样一个对象就可以去控制返回给用户的内容

9
00:00:27,860 --> 00:00:30,240
你比如说像contest.response.body

10
00:00:30,240 --> 00:00:31,460
它就是发送给用户的那种

11
00:00:31,460 --> 00:00:32,340
好

12
00:00:32,340 --> 00:00:33,780
这里呢可能说的同学们有点迷

13
00:00:33,780 --> 00:00:34,200
没关系

14
00:00:34,200 --> 00:00:35,300
我们直接进入咱们代码的编写

15
00:00:35,300 --> 00:00:35,820
我们来看一下

16
00:00:35,820 --> 00:00:42,400
怎么去写咱们这行代码

17
00:00:42,400 --> 00:00:45,400
好

18
00:00:45,400 --> 00:00:48,640
我们刚才是不是已经app.listen起了一个3000端口

19
00:00:48,640 --> 00:00:49,380
对吧

20
00:00:49,380 --> 00:00:51,400
也就是起服务

21
00:00:51,400 --> 00:00:53,860
监听3000端口

22
00:00:53,860 --> 00:00:54,140
好

23
00:00:54,140 --> 00:00:56,060
那么我们监听了3000端口之后

24
00:00:56,060 --> 00:00:56,680
其实呢

25
00:00:56,680 --> 00:00:57,480
在call里面

26
00:00:57,480 --> 00:00:58,680
他有一个方法

27
00:00:58,680 --> 00:01:01,120
叫做app.use

28
00:01:01,120 --> 00:01:02,800
在use里面呢

29
00:01:02,800 --> 00:01:05,360
他接受一个参数

30
00:01:05,360 --> 00:01:08,900
接受一个参数是什么呢

31
00:01:08,900 --> 00:01:11,400
接受一个函数

32
00:01:11,400 --> 00:01:12,700
接受一个函数

33
00:01:12,700 --> 00:01:14,120
当然来

34
00:01:14,120 --> 00:01:16,660
比如说我们去定一个

35
00:01:16,660 --> 00:01:18,800
tons的main等于

36
00:01:18,800 --> 00:01:20,480
方形

37
00:01:20,480 --> 00:01:22,040
好

38
00:01:22,040 --> 00:01:23,000
咱们先把这个函数

39
00:01:23,000 --> 00:01:24,460
传入app.use里面

40
00:01:24,460 --> 00:01:24,860
好

41
00:01:24,860 --> 00:01:26,180
那么我们app.use里面

42
00:01:26,180 --> 00:01:26,860
传入一个方法

43
00:01:26,860 --> 00:01:28,420
那么这个方法它的第一个参数

44
00:01:28,420 --> 00:01:29,080
也就是Cover

45
00:01:29,080 --> 00:01:30,560
它提供给我们的context的对象

46
00:01:30,560 --> 00:01:32,940
比如说它呢叫做ctx

47
00:01:32,940 --> 00:01:36,060
这呢它就是Cover提供的

48
00:01:36,060 --> 00:01:40,620
这个context对象可以做什么

49
00:01:40,620 --> 00:01:41,840
可以做什么

50
00:01:41,840 --> 00:01:42,780
刚才我们是不是讲到了

51
00:01:42,780 --> 00:01:44,200
它提供了context的对象

52
00:01:44,200 --> 00:01:45,880
是不是表示我们这一次对话的上下文

53
00:01:45,880 --> 00:01:47,920
对话对话是不是咱们前后端的一个沟通

54
00:01:47,920 --> 00:01:49,860
怎么样对话是不是request和response

55
00:01:49,860 --> 00:01:51,100
通过加工这样一个对话

56
00:01:51,100 --> 00:01:52,420
我们就可以控制返回的用户内容

57
00:01:52,420 --> 00:01:53,560
好比如说

58
00:01:53,560 --> 00:01:56,000
比如说

59
00:01:56,000 --> 00:02:01,760
比如说我们去ctx刚才是不讲的他可以去控制我们的

60
00:02:01,760 --> 00:02:07,980
是不控制咱们对话的上下文咱们的对话咱们whcp对话有哪些上下文是不是有requise和response他是必须有的

61
00:02:07,980 --> 00:02:14,100
所以说我们去修改把它比如说我们去修改ctx.response.body等于什么呢

62
00:02:14,100 --> 00:02:15,800
hello

63
00:02:15,800 --> 00:02:17,540
word

64
00:02:17,540 --> 00:02:20,700
好这里了我们

65
00:02:20,700 --> 00:02:23,860
比如说我们访问3000端口访问3000端口的时候呢

66
00:02:23,860 --> 00:02:27,620
那么CTX它的Response就会返回个玻璃叫做Hello World

67
00:02:27,620 --> 00:02:29,380
也就是返回改造给咱们客户端一个字路创

68
00:02:29,380 --> 00:02:29,960
Hello World

69
00:02:29,960 --> 00:02:31,320
那么到底是不是怎么回事呢

70
00:02:31,320 --> 00:02:31,840
我们来看一下

71
00:02:31,840 --> 00:02:36,600
好 走 大家可以看到

72
00:02:36,600 --> 00:02:38,460
我们已经接受到一个Hello World

73
00:02:38,460 --> 00:02:39,960
那么我们再来看一下

74
00:02:39,960 --> 00:02:42,400
这个Hello World它到底是个什么东西

75
00:02:42,400 --> 00:02:42,940
它是一个字路创

76
00:02:42,940 --> 00:02:44,840
还是一个HTML片端的同学们

77
00:02:44,840 --> 00:02:45,520
大家思考一下

78
00:02:45,520 --> 00:02:47,360
Hello World它是HTML还是字路创

79
00:02:47,360 --> 00:02:53,480
好

80
00:02:53,480 --> 00:02:54,780
我们来看一下我们这样一个请求

81
00:02:54,780 --> 00:02:56,300
hello word

82
00:02:56,300 --> 00:03:00,920
context type text plane

83
00:03:00,920 --> 00:03:02,580
reponse header

84
00:03:02,580 --> 00:03:03,700
requise header

85
00:03:03,700 --> 00:03:04,740
其实大家可以看到

86
00:03:04,740 --> 00:03:05,760
我们的hello word

87
00:03:05,760 --> 00:03:07,540
其实是不是返回了

88
00:03:07,540 --> 00:03:09,160
返回了这个html的片段

89
00:03:09,160 --> 00:03:10,980
大家可以看到

90
00:03:10,980 --> 00:03:12,560
是不是返回了咱们一个html

91
00:03:12,560 --> 00:03:14,420
对吧

92
00:03:14,420 --> 00:03:17,000
好

93
00:03:17,000 --> 00:03:18,140
那么我们就来看一下

94
00:03:18,140 --> 00:03:19,840
context里面它到底有哪些对象

95
00:03:19,840 --> 00:03:20,960
咱们这个context

96
00:03:20,960 --> 00:03:21,920
可能同学们比较疑惑

97
00:03:21,920 --> 00:03:22,900
那么这个ctx

98
00:03:22,900 --> 00:03:23,820
他里面到底有哪些东西

99
00:03:23,820 --> 00:03:24,640
这里呢

100
00:03:24,640 --> 00:03:25,420
有一个办法

101
00:03:25,420 --> 00:03:26,460
我们可以看一下它是什么

102
00:03:26,460 --> 00:03:27,980
是不是可以通过打断点呢

103
00:03:27,980 --> 00:03:28,400
之前

104
00:03:28,400 --> 00:03:30,940
之前我是不是带同学们去讲过

105
00:03:30,940 --> 00:03:32,300
学习咱们nodejs的时候

106
00:03:32,300 --> 00:03:33,020
咱们是不是教过

107
00:03:33,020 --> 00:03:33,820
同学们怎么样去调试

108
00:03:33,820 --> 00:03:34,800
是不是通过这样一个小虫子

109
00:03:34,800 --> 00:03:36,280
首先我们来配置一下

110
00:03:36,280 --> 00:03:37,440
咱们小虫子里面入口文件

111
00:03:37,440 --> 00:03:37,700
在哪里

112
00:03:37,700 --> 00:03:38,440
是不是在app.js

113
00:03:38,440 --> 00:03:38,740
好

114
00:03:38,740 --> 00:03:39,640
那么呢

115
00:03:39,640 --> 00:03:40,620
我们点在一个播放按钮

116
00:03:40,620 --> 00:03:41,300
好

117
00:03:41,300 --> 00:03:41,760
这里呢

118
00:03:41,760 --> 00:03:43,080
我们在哪里打一个断点呢

119
00:03:43,080 --> 00:03:44,240
我们是不是在cts

120
00:03:44,240 --> 00:03:45,080
这里打一个断点

121
00:03:45,080 --> 00:03:45,680
我们就可以看到

122
00:03:45,680 --> 00:03:46,740
它到底是什么呀

123
00:03:46,740 --> 00:03:46,940
好

124
00:03:46,940 --> 00:03:47,920
那么我们来

125
00:03:47,920 --> 00:03:49,720
刷新一下

126
00:03:49,720 --> 00:03:49,980
走

127
00:03:49,980 --> 00:03:50,360
好

128
00:03:50,360 --> 00:03:52,240
大家可以看到是不是咱们的断点已经定在这里了

129
00:03:52,240 --> 00:03:53,180
是不是已经定在这里了

130
00:03:53,180 --> 00:03:54,440
说明了他的请求已经过来了

131
00:03:54,440 --> 00:03:55,000
我们来看一下

132
00:03:55,000 --> 00:03:56,580
cts他到底是什么

133
00:03:56,580 --> 00:04:01,280
大家可以看到咱们的context

134
00:04:01,280 --> 00:04:02,520
context是不是一个对象啊

135
00:04:02,520 --> 00:04:03,160
object里面呢

136
00:04:03,160 --> 00:04:03,660
有一些属性

137
00:04:03,660 --> 00:04:04,580
比如说

138
00:04:04,580 --> 00:04:06,000
比如说有什么呢

139
00:04:06,000 --> 00:04:06,960
accept app

140
00:04:06,960 --> 00:04:08,160
app是不是就是他呀

141
00:04:08,160 --> 00:04:09,540
是不是就是口网提供的app啊

142
00:04:09,540 --> 00:04:09,960
body

143
00:04:09,960 --> 00:04:10,880
然后呢

144
00:04:10,880 --> 00:04:11,340
headers

145
00:04:11,340 --> 00:04:12,560
ip

146
00:04:12,560 --> 00:04:13,840
have hostname

147
00:04:13,840 --> 00:04:14,260
对吧

148
00:04:14,260 --> 00:04:16,320
包括最重要的request和response

149
00:04:16,320 --> 00:04:17,300
request和response

150
00:04:17,300 --> 00:04:18,280
是不是就是request

151
00:04:18,280 --> 00:04:19,780
是不是就是咱们客户端发起的请求啊

152
00:04:19,780 --> 00:04:20,640
也就是咱们浏览器

153
00:04:20,640 --> 00:04:22,140
发过来的请求

154
00:04:22,140 --> 00:04:23,080
request response

155
00:04:23,080 --> 00:04:24,960
是不是咱们需要返回

156
00:04:24,960 --> 00:04:26,360
需要给咱们客户端去返回的内容

157
00:04:26,360 --> 00:04:28,460
那么我们这里来看一下

158
00:04:28,460 --> 00:04:29,980
我们context

159
00:04:29,980 --> 00:04:31,560
context

160
00:04:31,560 --> 00:04:34,120
它的response最开始

161
00:04:34,120 --> 00:04:36,240
大家可以看到message是not found的

162
00:04:36,240 --> 00:04:37,220
message是not found的

163
00:04:37,220 --> 00:04:37,480
对吧

164
00:04:37,480 --> 00:04:38,640
然后呢

165
00:04:38,640 --> 00:04:39,900
还有一个status

166
00:04:39,900 --> 00:04:40,560
status是什么

167
00:04:40,560 --> 00:04:42,640
是不是咱们的http状态码

168
00:04:42,640 --> 00:04:43,480
对吧

169
00:04:43,480 --> 00:04:44,700
返回的是势力室

170
00:04:44,700 --> 00:04:46,000
那么这里同学们注意

171
00:04:46,000 --> 00:04:46,740
这里同学们注意

172
00:04:46,740 --> 00:04:48,080
我们试团一个问题

173
00:04:48,080 --> 00:04:49,860
是个问题

174
00:04:49,860 --> 00:04:51,280
此时我们打断点的时候

175
00:04:51,280 --> 00:04:52,580
此时我们断点的时候

176
00:04:52,580 --> 00:04:53,020
咱们其实

177
00:04:53,020 --> 00:04:54,380
Response它的Message

178
00:04:54,380 --> 00:04:54,880
是Not Found

179
00:04:54,880 --> 00:04:55,720
Status 404

180
00:04:55,720 --> 00:04:56,940
那么为什么我们

181
00:04:56,940 --> 00:04:57,700
我们能够

182
00:04:57,700 --> 00:04:58,700
仿佛到这一个页面

183
00:04:58,700 --> 00:05:00,520
大家可以看到

184
00:05:00,520 --> 00:05:05,280
大家可以看到

185
00:05:05,280 --> 00:05:06,320
我们此时

186
00:05:06,320 --> 00:05:07,280
是不是咱们

187
00:05:07,280 --> 00:05:09,940
我们此时是不是

188
00:05:09,940 --> 00:05:12,540
Status是404

189
00:05:12,540 --> 00:05:13,820
而且的Message是Not Found

190
00:05:13,820 --> 00:05:14,760
那么我们为什么

191
00:05:14,760 --> 00:05:16,600
修把它的Response.Board

192
00:05:16,600 --> 00:05:17,920
修改成了Hello World之后

193
00:05:17,920 --> 00:05:19,440
他的status就变成了200

194
00:05:19,440 --> 00:05:20,340
同学思考一下

195
00:05:20,340 --> 00:05:21,580
为什么

196
00:05:21,580 --> 00:05:23,060
修改

197
00:05:23,060 --> 00:05:24,720
body之后

198
00:05:24,720 --> 00:05:28,720
status就变为了200

199
00:05:28,720 --> 00:05:31,080
好

200
00:05:31,080 --> 00:05:33,080
为了方便同学们理解了

201
00:05:33,080 --> 00:05:33,980
我们这里先把它给断掉

202
00:05:33,980 --> 00:05:35,120
我们重新来启动一下

203
00:05:35,120 --> 00:05:36,880
我们重新来启动一下

204
00:05:36,880 --> 00:05:37,880
大家可以看到

205
00:05:37,880 --> 00:05:39,240
我们现在status是不是200

206
00:05:39,240 --> 00:05:41,760
而且它返回的结果是一个html片段

207
00:05:41,760 --> 00:05:42,240
好了word

208
00:05:42,240 --> 00:05:43,560
那么刚才我们的status

209
00:05:43,560 --> 00:05:44,280
明明是事件事

210
00:05:44,280 --> 00:05:45,880
而且我们没有修改contest

211
00:05:45,880 --> 00:05:46,820
他的status属性

212
00:05:46,820 --> 00:05:48,340
那么它为什么就变成了200

213
00:05:48,340 --> 00:05:49,000
对吧

214
00:05:49,000 --> 00:05:50,800
怎么样从404变成200

215
00:05:50,800 --> 00:05:51,740
是不是很奇怪

216
00:05:51,740 --> 00:05:53,240
那么Core他里面到底做了什么

217
00:05:53,240 --> 00:05:55,540
同学们是不是非常感到非常疑惑

218
00:05:55,540 --> 00:05:56,140
对吧

219
00:05:56,140 --> 00:05:56,920
那么这里呢

220
00:05:56,920 --> 00:05:57,920
其实很简单去分析

221
00:05:57,920 --> 00:05:59,420
我们还是打一个断点

222
00:05:59,420 --> 00:06:00,340
这里呢

223
00:06:00,340 --> 00:06:02,120
我就带同学们来理解一下

224
00:06:02,120 --> 00:06:04,000
其实很简单啊

225
00:06:04,000 --> 00:06:05,980
好 走 刷新

226
00:06:05,980 --> 00:06:08,080
好 大家可以看到

227
00:06:08,080 --> 00:06:09,560
我们现在Stadius是200

228
00:06:09,560 --> 00:06:10,900
Stadius是404

229
00:06:10,900 --> 00:06:11,100
不好意思

230
00:06:11,100 --> 00:06:12,120
Stadius是404

231
00:06:12,120 --> 00:06:12,760
然后呢

232
00:06:12,760 --> 00:06:14,000
Message是多放的

233
00:06:14,000 --> 00:06:14,860
因为此时我们断点

234
00:06:14,860 --> 00:06:15,560
在这里是不是说明

235
00:06:15,560 --> 00:06:17,060
contest.response.body

236
00:06:17,060 --> 00:06:18,000
他这行还没执行呢

237
00:06:18,000 --> 00:06:19,240
好我们来看一下

238
00:06:19,240 --> 00:06:20,180
我们往下走一行

239
00:06:20,180 --> 00:06:20,980
看下面发生了什么

240
00:06:20,980 --> 00:06:21,980
咱们走一步

241
00:06:21,980 --> 00:06:22,640
大家可以看到

242
00:06:22,640 --> 00:06:24,200
我们有一个setbody

243
00:06:24,200 --> 00:06:26,300
在response.js里面

244
00:06:26,300 --> 00:06:28,580
有一个setbody

245
00:06:28,580 --> 00:06:30,200
那么set是什么意思

246
00:06:30,200 --> 00:06:31,720
set是什么意思

247
00:06:31,720 --> 00:06:33,840
set

248
00:06:33,840 --> 00:06:37,300
是不是咱们es5里面的一个数据解词

249
00:06:37,300 --> 00:06:38,320
和咱们的view里面一样

250
00:06:38,320 --> 00:06:39,440
比如说我们去get

251
00:06:39,440 --> 00:06:40,660
比如说我们去setbody的时候

252
00:06:40,660 --> 00:06:43,740
刚才我们是不是修改了咱们contest.response.body

253
00:06:43,740 --> 00:06:44,360
这一个属性

254
00:06:44,360 --> 00:06:45,320
也就是咱们改了body属性

255
00:06:45,320 --> 00:06:46,720
那么咱们修改玻璃属性的时候

256
00:06:46,720 --> 00:06:48,060
是不是会触发set的这个方法

257
00:06:48,060 --> 00:06:49,160
大家可以看到

258
00:06:49,160 --> 00:06:51,000
我们往下走

259
00:06:51,000 --> 00:06:51,720
大家可以看到这样一行

260
00:06:51,720 --> 00:06:54,760
大家注意这样一行代码

261
00:06:54,760 --> 00:06:58,520
if dcx

262
00:06:58,520 --> 00:07:00,060
什么什么

263
00:07:00,060 --> 00:07:01,720
如果说它一个什么状态

264
00:07:01,720 --> 00:07:03,600
它的status就等于200

265
00:07:03,600 --> 00:07:06,000
说明什么问题

266
00:07:06,000 --> 00:07:07,680
说明我们去修改玻璃的时候

267
00:07:07,680 --> 00:07:09,040
其实是不是咱们的cora

268
00:07:09,040 --> 00:07:10,700
它自动的会把咱们的status改为200

269
00:07:10,700 --> 00:07:12,180
所以说

270
00:07:12,180 --> 00:07:14,120
所以说在这里

271
00:07:14,120 --> 00:07:17,300
我们呢只需要把玻璃给了他一个合理的值

272
00:07:17,300 --> 00:07:18,400
也是给了他一个合法的值

273
00:07:18,400 --> 00:07:19,080
这里不会报错

274
00:07:19,080 --> 00:07:21,540
他就会自动的把咱们的status改为200

275
00:07:21,540 --> 00:07:28,000
通过set节词

276
00:07:28,000 --> 00:07:31,720
只要修改了玻璃

277
00:07:31,720 --> 00:07:35,820
就会自动把status改为200

278
00:07:35,820 --> 00:07:37,360
其实咱们代码分析到这里

279
00:07:37,360 --> 00:07:39,620
大家不要觉得core这框架它非常简单

280
00:07:39,620 --> 00:07:41,420
大家也不要觉得我们平时使用的时候

281
00:07:41,420 --> 00:07:42,760
大家可以看到

282
00:07:42,760 --> 00:07:44,560
我们的Core去返回一个

283
00:07:44,560 --> 00:07:45,900
是不是返回咱们一个字幕传

284
00:07:45,900 --> 00:07:46,980
非常简单就几行代码

285
00:07:46,980 --> 00:07:48,380
但是其实它里面

286
00:07:48,380 --> 00:07:50,180
是不是隐藏了非常多的一些思想

287
00:07:50,180 --> 00:07:51,460
而且我们代码分析到这里

288
00:07:51,460 --> 00:07:54,220
其实可以去学习了很多的一些东西

289
00:07:54,220 --> 00:07:55,860
所以同学们不要认为Core它简单

290
00:07:55,860 --> 00:07:57,620
也不要认为咱们平时就写了几行代码

291
00:07:57,620 --> 00:07:58,560
也就是为什么说

292
00:07:58,560 --> 00:08:00,400
我们的前端工程是在工作几年之后

293
00:08:00,400 --> 00:08:01,860
为什么大家有这么大差距

294
00:08:01,860 --> 00:08:04,000
为什么同样工作了3.5年的同学

295
00:08:04,000 --> 00:08:04,860
为什么水平不一样

296
00:08:04,860 --> 00:08:05,980
比如说有人就会思考

297
00:08:05,980 --> 00:08:07,500
我为什么修改它一个玻璃

298
00:08:07,500 --> 00:08:08,020
对吧

299
00:08:08,020 --> 00:08:08,880
咱们Hello World

300
00:08:08,880 --> 00:08:13,120
他明明是逝离室 为什么state就是改为了200 因为如果说我们想要去把他的stay

301
00:08:13,120 --> 00:08:16,980
 如果说按照预想 我们是不是需要手动的去把state就是

302
00:08:16,980 --> 00:08:18,960
改为200才可以啊 对吧

303
00:08:18,960 --> 00:08:23,640
大家都学过 http 状态吧 但是为什么这里我们不需要去设置他呢 因为是不是他对body

304
00:08:23,640 --> 00:08:25,120
 进行了一个set

305
00:08:25,120 --> 00:08:29,970
所以说大家在平时做开发的时候一定要去多思考 多思考咱们技术的本质是什么

306
00:08:29,970 --> 00:08:34,080
 他到底做了什么事情 这样的同学们的技术才可以去提升 好 这里呢就是我们

307
00:08:38,880 --> 00:08:40,160
这里呢

308
00:08:40,160 --> 00:08:40,640
是不是我们

309
00:08:40,640 --> 00:08:44,080
咱们通过Core去返回一个具体的值

310
00:08:44,080 --> 00:08:44,960
去给客户端

311
00:08:44,960 --> 00:08:46,080
玻璃去修改一些值

312
00:08:46,080 --> 00:08:47,760
咱们的通过context的对象去

313
00:08:47,760 --> 00:08:50,480
给客户端返回值的一个过程呢

314
00:08:50,480 --> 00:08:52,120
希望同学们能够去思考一下

315
00:08:52,120 --> 00:08:53,240
学习一些东西

316
00:08:53,240 --> 00:08:53,680
刚才呢

317
00:08:53,680 --> 00:08:54,160
其实我已经

318
00:08:54,160 --> 00:08:55,460
是不是带同学们去总结了一遍呢

319
00:08:55,460 --> 00:08:55,840
而且呢

320
00:08:55,840 --> 00:08:56,680
我们也告诉同学们

321
00:08:56,680 --> 00:08:58,060
咱们去思考非常的重要

322
00:08:58,060 --> 00:08:58,840
所以说呢

323
00:08:58,840 --> 00:09:00,480
可以了

324
00:09:00,480 --> 00:09:02,180
同学们可以去再回顾一下

325
00:09:02,180 --> 00:09:02,500
好

326
00:09:02,500 --> 00:09:02,800
这里呢

327
00:09:02,800 --> 00:09:03,560
就是我们这节课的内容

