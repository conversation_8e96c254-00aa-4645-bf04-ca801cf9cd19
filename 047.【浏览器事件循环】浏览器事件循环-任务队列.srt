1
00:00:00,000 --> 00:00:02,640
好 我们刚才是不是已经讲解了世界循环

2
00:00:02,640 --> 00:00:03,840
它的一个概念

3
00:00:03,840 --> 00:00:04,960
那么从这里开始呢

4
00:00:04,960 --> 00:00:06,400
老师就来带大家去学习浏览器

5
00:00:06,400 --> 00:00:08,000
它里面的一个世界循环

6
00:00:08,000 --> 00:00:09,400
这里呢会分为三部分去讲

7
00:00:09,400 --> 00:00:11,440
既然是它为什么是单线程的

8
00:00:11,440 --> 00:00:12,700
然后呢会讲解任务对面

9
00:00:12,700 --> 00:00:13,480
任务对面是什么

10
00:00:13,480 --> 00:00:14,400
任务对面是不是就是

11
00:00:14,400 --> 00:00:16,200
咱们刚才所介绍到的浏览器

12
00:00:16,200 --> 00:00:17,200
它的一个核心是协调

13
00:00:17,200 --> 00:00:18,040
怎么去协调

14
00:00:18,040 --> 00:00:19,360
是不是通过对面的形式去协调

15
00:00:19,360 --> 00:00:21,080
然后呢还会去讲解

16
00:00:21,080 --> 00:00:23,060
任务对面里面的红任务与微任务

17
00:00:23,060 --> 00:00:25,000
大家在学习这三块内容之后呢

18
00:00:25,000 --> 00:00:27,560
相信就会对浏览器的身体循环有一个大致的了解

19
00:00:27,560 --> 00:00:28,840
以及可以掌握

20
00:00:28,840 --> 00:00:30,880
那么这节课的主要内容

21
00:00:30,880 --> 00:00:32,680
我会讲两部分的内容

22
00:00:32,680 --> 00:00:33,960
第一个js它为什么是单线程的

23
00:00:33,960 --> 00:00:34,720
第二个任务对立

24
00:00:34,720 --> 00:00:36,000
首先我们来看一下

25
00:00:36,000 --> 00:00:37,800
GS它为什么是单线程的

26
00:00:37,800 --> 00:00:41,900
我们平时使用js的时候一般会用来做什么

27
00:00:41,900 --> 00:00:42,400
是不是操作Dome

28
00:00:42,400 --> 00:00:44,460
那么操作Dome

29
00:00:44,460 --> 00:00:47,280
如果说我们假设js它是多线程的

30
00:00:47,280 --> 00:00:48,300
一个线程

31
00:00:48,300 --> 00:00:50,860
给某一个节点添加内容

32
00:00:50,860 --> 00:00:52,640
那么另一个线程拴出来这个节点

33
00:00:52,640 --> 00:00:54,440
只是浏览器应该以哪个线程为主

34
00:00:55,000 --> 00:00:56,120
是不是流了一些他也会懵逼

35
00:00:56,120 --> 00:00:57,280
他不知道怎么样去做

36
00:00:57,280 --> 00:00:59,160
所以这样会造成很严重的问题

37
00:00:59,160 --> 00:01:00,660
于是呢

38
00:01:00,660 --> 00:01:02,280
GS的作者就去

39
00:01:02,280 --> 00:01:05,680
定义了js它一定要是单线程的

40
00:01:05,680 --> 00:01:07,660
那么既然js它是单线程的

41
00:01:07,660 --> 00:01:09,060
所以呢就会带来一些问题

42
00:01:09,060 --> 00:01:10,720
因为呀

43
00:01:10,720 --> 00:01:12,880
我们任务

44
00:01:12,880 --> 00:01:14,120
它是不是需要一个一个的去执行

45
00:01:14,120 --> 00:01:16,660
但是如果说遇到一些IO操作

46
00:01:16,660 --> 00:01:17,480
一些ebo的操作

47
00:01:17,480 --> 00:01:19,440
就会一直闲置挂起

48
00:01:19,440 --> 00:01:20,380
就类似加把那样

49
00:01:20,380 --> 00:01:22,100
它碰到IO操作就会在那里等待

50
00:01:22,100 --> 00:01:22,900
那么js行不行

51
00:01:22,900 --> 00:01:23,520
是不是不可以

52
00:01:23,520 --> 00:01:24,880
如果说一直在那里等待

53
00:01:24,880 --> 00:01:25,640
那么我们浏览器

54
00:01:25,640 --> 00:01:26,440
是不是就往往那样

55
00:01:26,440 --> 00:01:28,420
浏览器全部都是Io操作

56
00:01:28,420 --> 00:01:30,980
所以说我们js就设计成了一门Io的语言

57
00:01:30,980 --> 00:01:32,420
它不会在那里做无谓的等待

58
00:01:32,420 --> 00:01:33,880
怎么样去做呢

59
00:01:33,880 --> 00:01:36,420
通过任务对立的方式去调度

60
00:01:36,420 --> 00:01:37,800
那么在js里面

61
00:01:37,800 --> 00:01:39,780
它分为同步的任务和Io的任务

62
00:01:39,780 --> 00:01:42,280
所有的任务都会在主线层上面去执行

63
00:01:42,280 --> 00:01:44,700
所有同步的任务

64
00:01:44,700 --> 00:01:45,880
都会在主线层上面去执行

65
00:01:45,880 --> 00:01:46,440
形成一个调用杖

66
00:01:46,440 --> 00:01:47,160
执行杖

67
00:01:47,160 --> 00:01:48,560
那么在主线层之外

68
00:01:48,560 --> 00:01:49,220
还会有个任务对立

69
00:01:49,220 --> 00:01:50,160
怎么去理解

70
00:01:50,160 --> 00:01:53,920
就是我们比如说遇到一些Io的操作

71
00:01:53,920 --> 00:01:54,960
定时器

72
00:01:54,960 --> 00:01:55,860
我们警觉等等

73
00:01:55,860 --> 00:01:56,700
只要它有了

74
00:01:56,700 --> 00:01:57,640
反复的结果

75
00:01:57,640 --> 00:01:59,100
就会加入任务对链里面去

76
00:01:59,100 --> 00:02:01,340
当我们的主线层

77
00:02:01,340 --> 00:02:02,200
执行完毕之后

78
00:02:02,200 --> 00:02:03,140
就会去任务对链里面去找

79
00:02:03,140 --> 00:02:04,920
如果说任务对链里面有任务

80
00:02:04,920 --> 00:02:05,560
我们就去执行它

81
00:02:05,560 --> 00:02:06,940
然后主线层不断的重复

82
00:02:06,940 --> 00:02:07,520
以上的三步

83
00:02:07,520 --> 00:02:09,040
这就是我们js里面的任务对链

84
00:02:09,040 --> 00:02:10,540
可能不是很好理解

85
00:02:10,540 --> 00:02:11,800
老师这里通过

86
00:02:11,800 --> 00:02:12,980
代码来带同学们去理解一下

87
00:02:12,980 --> 00:02:14,340
到底什么是任务对链

88
00:02:14,340 --> 00:02:15,840
好

89
00:02:15,840 --> 00:02:17,640
我们先来

90
00:02:17,640 --> 00:02:18,900
写一段

91
00:02:18,900 --> 00:02:19,480
比如说

92
00:02:19,480 --> 00:02:21,200
方形一步

93
00:02:21,200 --> 00:02:23,820
concel.log

94
00:02:23,820 --> 00:02:25,020
一步

95
00:02:25,020 --> 00:02:27,340
好 这里有个定时器

96
00:02:27,340 --> 00:02:28,540
setTimeout

97
00:02:28,540 --> 00:02:30,800
回掉了就是一步方法

98
00:02:30,800 --> 00:02:33,300
一步就是它 对吧

99
00:02:33,300 --> 00:02:34,220
时间一千

100
00:02:34,220 --> 00:02:37,480
最后console.log一个may

101
00:02:37,480 --> 00:02:38,940
好

102
00:02:38,940 --> 00:02:42,160
我们刚才讲到了

103
00:02:42,160 --> 00:02:43,440
任务对面里面是不是奉为

104
00:02:43,440 --> 00:02:45,760
主线程和一个任务对面

105
00:02:45,760 --> 00:02:46,740
那我们就来看一下

106
00:02:46,740 --> 00:02:48,200
刚才再一段代码

107
00:02:48,200 --> 00:02:49,080
什么是主线程

108
00:02:49,080 --> 00:02:49,760
什么是任务对面

109
00:02:49,760 --> 00:02:50,480
他们是怎么样去工作

110
00:02:50,480 --> 00:02:53,720
首先我们是不是会

111
00:02:53,720 --> 00:02:55,480
会有一个主线层

112
00:02:55,480 --> 00:02:59,160
然后还会有一个任务对列

113
00:02:59,160 --> 00:03:01,460
任务对列

114
00:03:01,460 --> 00:03:03,240
这里呢在浏览器里面

115
00:03:03,240 --> 00:03:05,620
它还会有一个叫做易不等待

116
00:03:05,620 --> 00:03:06,220
也就是

117
00:03:06,220 --> 00:03:09,760
IO

118
00:03:09,760 --> 00:03:11,380
这里呢同学们不必关心

119
00:03:11,380 --> 00:03:12,940
IO是什么样的形式存在

120
00:03:12,940 --> 00:03:14,800
只是一个老师呢只是帮不单理解

121
00:03:14,800 --> 00:03:16,940
咱们核心在于主线层和任务对列之间的关系

122
00:03:16,940 --> 00:03:19,180
首先主线层上面会去执行什么

123
00:03:19,180 --> 00:03:20,660
是不是settimeout

124
00:03:20,660 --> 00:03:23,600
首先它会进入咱们的主线层

125
00:03:23,600 --> 00:03:25,360
那么settimeout它执行的时候

126
00:03:25,360 --> 00:03:26,900
是不是会把一个叫做逆步的方法

127
00:03:26,900 --> 00:03:28,740
是不是丢入咱们的IO里面去

128
00:03:28,740 --> 00:03:32,000
逆步丢到咱们的IO里面去

129
00:03:32,000 --> 00:03:32,880
对吧

130
00:03:32,880 --> 00:03:35,180
那么此时

131
00:03:35,180 --> 00:03:36,020
逆步这个方法

132
00:03:36,020 --> 00:03:37,120
是不是需要等到一秒钟才能执行

133
00:03:37,120 --> 00:03:38,680
所以settimeout它先出站

134
00:03:38,680 --> 00:03:40,260
我们的主线程继续执行

135
00:03:40,260 --> 00:03:41,380
这个时候

136
00:03:41,380 --> 00:03:43,060
是不是会去

137
00:03:43,060 --> 00:03:44,280
下一步

138
00:03:44,280 --> 00:03:45,700
康斯点log命

139
00:03:45,700 --> 00:03:46,960
老师简写log命

140
00:03:46,960 --> 00:03:49,000
它会进入咱们的主线程

141
00:03:49,000 --> 00:03:50,340
然后执行完之后出站

142
00:03:50,340 --> 00:03:52,040
此时我们的逆步这样一个方法

143
00:03:52,040 --> 00:03:53,400
是不是还在等待之中呢

144
00:03:53,400 --> 00:03:54,920
因为需要等到一秒钟

145
00:03:54,920 --> 00:03:56,920
实际上一秒钟之内我们的命早就执行完了

146
00:03:56,920 --> 00:03:58,700
那么我们等到一秒钟之后

147
00:03:58,700 --> 00:04:00,960
是不是义步它就要进入我们的任务对列

148
00:04:00,960 --> 00:04:03,500
那么进入任务对列之后

149
00:04:03,500 --> 00:04:04,220
我们的主线层

150
00:04:04,220 --> 00:04:05,340
是不是会去

151
00:04:05,340 --> 00:04:07,400
主线层现在执行完之后就会去检查

152
00:04:07,400 --> 00:04:08,020
任务对列没任务

153
00:04:08,020 --> 00:04:09,060
好 只是有一个义步

154
00:04:09,060 --> 00:04:09,820
它就进入主线层

155
00:04:09,820 --> 00:04:11,900
主线层执行完之后就出战

156
00:04:11,900 --> 00:04:13,860
好 那么这里呢就是我们义步

157
00:04:13,860 --> 00:04:14,640
它的执行过程

158
00:04:14,640 --> 00:04:21,360
那么我们再来看一下

159
00:04:21,360 --> 00:04:22,720
通过刚才这个例子

160
00:04:22,720 --> 00:04:25,100
主线程从任务队列中读取事件

161
00:04:25,100 --> 00:04:26,720
这个过程是循环不断的

162
00:04:26,720 --> 00:04:28,660
所以整个这种机制又称为event

163
00:04:28,660 --> 00:04:29,940
那么到这里

164
00:04:29,940 --> 00:04:32,600
我们就已经掌握了有那些事情循环吗

165
00:04:32,600 --> 00:04:33,180
其实还没有

166
00:04:33,180 --> 00:04:35,760
大家还记不记得老师刚才所提到的这样一个例子

167
00:04:35,760 --> 00:04:37,660
是Timeout和promise在一起

168
00:04:37,660 --> 00:04:39,860
那么我们再来看一下

169
00:04:39,860 --> 00:04:42,020
它那个事情循环是什么样

170
00:04:42,020 --> 00:04:45,720
这里老师还是画幅图

171
00:04:45,720 --> 00:04:51,260
首先由主线程和任务队列

172
00:04:51,260 --> 00:04:54,100
还会有一个IO对吧

173
00:04:54,100 --> 00:04:58,060
主线层

174
00:04:58,060 --> 00:04:59,960
用对立

175
00:04:59,960 --> 00:05:05,060
IO好

176
00:05:05,060 --> 00:05:08,260
此时我们的主线层里面

177
00:05:08,260 --> 00:05:10,120
是不是首先settimeout会进入主线层

178
00:05:10,120 --> 00:05:13,280
settimeout

179
00:05:13,280 --> 00:05:15,300
进入主线层对吧

180
00:05:15,300 --> 00:05:16,420
那么呢

181
00:05:16,420 --> 00:05:17,120
谁进入IO

182
00:05:17,120 --> 00:05:18,120
是不是console.log

183
00:05:18,120 --> 00:05:19,660
settimeout会进入IO对吧

184
00:05:20,420 --> 00:05:21,460
简写log

185
00:05:21,460 --> 00:05:26,320
sets time out

186
00:05:26,320 --> 00:05:27,340
他进入IO

187
00:05:27,340 --> 00:05:28,100
那么

188
00:05:28,100 --> 00:05:29,380
log进入IO之后

189
00:05:29,380 --> 00:05:30,920
是不是他没有出战

190
00:05:30,920 --> 00:05:31,700
此时

191
00:05:31,700 --> 00:05:33,740
promise是不是入战

192
00:05:33,740 --> 00:05:35,540
对吧 promise.resolve这样一个方法入战

193
00:05:35,540 --> 00:05:39,880
promise.resolve

194
00:05:39,880 --> 00:05:41,680
好

195
00:05:41,680 --> 00:05:43,460
当他入战之后

196
00:05:43,460 --> 00:05:44,500
执行

197
00:05:44,500 --> 00:05:47,300
此时是不是会把那个promise也丢在那里面

198
00:05:47,300 --> 00:05:49,620
因为promise的点证方法是一步的对吧

199
00:05:49,660 --> 00:05:52,600
好 那么呢 此时我们就把LogPromise丢到IO里面去

200
00:05:52,600 --> 00:05:56,940
LogPromise

201
00:05:56,940 --> 00:06:00,240
好 此时Promise.resolve是不是可以出战了

202
00:06:00,240 --> 00:06:01,440
然后接下来

203
00:06:01,440 --> 00:06:03,240
Log

204
00:06:03,240 --> 00:06:07,680
没 执行完之后出战

205
00:06:07,680 --> 00:06:09,840
好 那么我们的主线程已经执行完了

206
00:06:09,840 --> 00:06:11,880
我们呢 此时是不是需要去IO里面去看

207
00:06:11,880 --> 00:06:16,560
谁Sertimeout和Promise随进入任务队列

208
00:06:17,880 --> 00:06:19,420
实际上我们是不是很难去判断呢

209
00:06:19,420 --> 00:06:19,920
为什么

210
00:06:19,920 --> 00:06:21,980
因为settimeout他的延迟是0

211
00:06:21,980 --> 00:06:23,260
promise的证呢

212
00:06:23,260 --> 00:06:24,280
他也是一个

213
00:06:24,280 --> 00:06:25,820
立即执行的一个一步回掉

214
00:06:25,820 --> 00:06:27,360
那么我们的settimeout和promise

215
00:06:27,360 --> 00:06:28,640
到底随先进入论断链

216
00:06:28,640 --> 00:06:29,660
这是不是一个问题

217
00:06:29,660 --> 00:06:30,940
对吧

218
00:06:30,940 --> 00:06:31,440
所以呢

219
00:06:31,440 --> 00:06:33,240
我们需要去学习下节内容

220
00:06:33,240 --> 00:06:35,040
这里呢涉及到一个概念

221
00:06:35,040 --> 00:06:35,540
叫做什么呢

222
00:06:35,540 --> 00:06:36,320
叫做

223
00:06:36,320 --> 00:06:38,360
红任务与微任务

224
00:06:38,360 --> 00:06:39,640
这是下节课老师会去

225
00:06:39,640 --> 00:06:41,180
讲解我们的promise和setmout

226
00:06:41,180 --> 00:06:42,720
他的执行顺序到底是什么样的

227
00:06:42,720 --> 00:06:45,020
那么我们现在回顾一下刚才说讲解内容

228
00:06:45,020 --> 00:06:46,800
既然是他为什么是单线程的

229
00:06:47,320 --> 00:06:49,760
是因为多线程会引起多我们操作冲突

230
00:06:49,760 --> 00:06:51,040
那么什么是任务对链呢

231
00:06:51,040 --> 00:06:52,040
任务对链

232
00:06:52,040 --> 00:06:54,960
是不是就是我们的同步任务都在主线程上面去执行

233
00:06:54,960 --> 00:06:56,480
一步的代码它会形成一个

234
00:06:56,480 --> 00:06:58,240
任务对链当它需要去执行

235
00:06:58,240 --> 00:07:00,640
一步的时候就会把它推入到我们的任务对链里面去

236
00:07:00,640 --> 00:07:03,360
然后主线程执行完成之后就会去任务对链里面去找

237
00:07:03,360 --> 00:07:04,360
找出来之后再执行它

238
00:07:04,360 --> 00:07:06,000
然后不断的去重复上面的散步

239
00:07:06,000 --> 00:07:08,720
这就是这样的一种运行机制就称为event loop

240
00:07:08,720 --> 00:07:09,760
但是event loop

241
00:07:09,760 --> 00:07:10,840
我们还需要去了解

242
00:07:10,840 --> 00:07:18,480
这就是咱们这节课的内容

