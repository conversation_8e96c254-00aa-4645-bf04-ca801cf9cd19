1
00:00:00,000 --> 00:00:04,220
我们来看一下那FS他有哪些方法 首先第一个RaphaelSync

2
00:00:04,220 --> 00:00:08,200
那么他就是去同步的去读取我们的一个文件

3
00:00:08,200 --> 00:00:13,320
我们来看一下他介绍两个参数 第一个参数就是我们读取的文件的路径或者

4
00:00:13,320 --> 00:00:18,180
文件描述符 因为刚才我们说到了 那么文件描述符其实就是我们文件相当于是他的ID

5
00:00:18,180 --> 00:00:21,000
所以你传入路径和文件描述符都可以 但是一般情况下

6
00:00:21,000 --> 00:00:23,560
我们都是去传入咱们这样一个文件的路径

7
00:00:23,560 --> 00:00:24,840
那么第二个参数就是

8
00:00:24,840 --> 00:00:26,620
选项options

9
00:00:27,140 --> 00:00:30,980
你呢可以去传入他的一些编码 包括一些flag标准位对吧

10
00:00:30,980 --> 00:00:33,800
好 那么我们就一起来看一下他是如何去运行的

11
00:00:33,800 --> 00:00:35,840
我们呢把这样一段代码的把给粘贴一下

12
00:00:35,840 --> 00:00:39,940
好 我们来新建一个

13
00:00:39,940 --> 00:00:45,580
我们来建一个

14
00:00:45,580 --> 00:00:53,000
dreadfailsync.js

15
00:00:53,000 --> 00:00:55,300
好 我们把test那给刷掉

16
00:00:57,900 --> 00:01:00,460
我们把刚才咱们读取文件的一个代码把它给张贴过来

17
00:01:00,460 --> 00:01:02,520
好 这里呢 它呢首先

18
00:01:02,520 --> 00:01:05,580
通过一个buff去接收我们的一个

19
00:01:05,580 --> 00:01:06,860
咱们的一个文件

20
00:01:06,860 --> 00:01:09,420
然后呢再通过一个data传入一个utf-8这样一个编码

21
00:01:09,420 --> 00:01:12,760
它依然呢也是去读取我们的1.txt

22
00:01:12,760 --> 00:01:15,060
所以说我们这里呢需要去创建一个1.txt

23
00:01:15,060 --> 00:01:19,160
因为你不能让它去读取一个空吧 我们给它写入一个

24
00:01:19,160 --> 00:01:20,440
好了我的

25
00:01:20,440 --> 00:01:23,260
好 那么我们呢来执行一下这样一个代码

26
00:01:23,260 --> 00:01:24,020
node

27
00:01:24,020 --> 00:01:25,560
fs

28
00:01:27,140 --> 00:01:28,300
readfilesync

29
00:01:28,300 --> 00:01:29,220
好 我们来看一下

30
00:01:29,220 --> 00:01:32,040
好 大家可以看到了

31
00:01:32,040 --> 00:01:33,060
这里呢 报了一个错

32
00:01:33,060 --> 00:01:35,240
losaxfilewall directory open

33
00:01:35,240 --> 00:01:36,260
好

34
00:01:36,260 --> 00:01:38,960
这里呢 它说没有找到1.txt

35
00:01:38,960 --> 00:01:40,040
我们来看一下问题是在哪里

36
00:01:40,040 --> 00:01:42,040
我们是不是没有加上点斜杠呢

37
00:01:42,040 --> 00:01:42,780
它的一个相对路径

38
00:01:42,780 --> 00:01:43,720
好 我们再来试一下

39
00:01:43,720 --> 00:01:53,440
好 这里呢 依然报了一个错

40
00:01:53,440 --> 00:01:55,300
我们找不到咱们的文件的一个路径

41
00:01:55,300 --> 00:01:56,400
好 这里呢

42
00:01:56,400 --> 00:01:58,200
我们可能需要使用到我们的一个PASS模块

43
00:01:58,200 --> 00:02:02,040
我们来引入我们的PASS

44
00:02:02,040 --> 00:02:12,020
我们来PASS.resolve

45
00:02:12,020 --> 00:02:18,680
我们的1.txt

46
00:02:23,020 --> 00:02:26,560
好 我们再来看一下 此时能不能读到我们的TST文件

47
00:02:26,560 --> 00:02:33,000
这里呢 把我们修改一下 改回了pass.jom 需要传入我们的dl-link

48
00:02:33,000 --> 00:02:35,560
然后呢 以相对路径形式来把我们的路径给改写一下

49
00:02:35,560 --> 00:02:37,860
好 这里呢 此时就可以读取到我们的一个文件了

50
00:02:37,860 --> 00:02:40,180
好 我们再来执行一下load

51
00:02:40,180 --> 00:02:43,760
好 大家可以看到了 我们第一个是不是打印出来是一个二性字的一个buffer

52
00:02:43,760 --> 00:02:45,800
那么第二个呢 就是我们好了我的0字不唱

53
00:02:45,800 --> 00:02:47,080
所以说呢 分点说明一个问题

54
00:02:47,080 --> 00:02:50,420
当我们去调用reffile sync的时候 如果说我们不去传入它的编码

55
00:02:50,420 --> 00:02:52,200
默认呢 给我们输出的就是二性字

56
00:02:52,460 --> 00:02:55,540
那么如果说你传入了编码 就会给我们输出 utf-f-8的一个编码

57
00:02:55,540 --> 00:03:00,900
好 这里是我们的同步读取 咱们的一个文件的一个过程 接下来我们再来看一下

58
00:03:00,900 --> 00:03:06,800
当然了 我们Redfair Sync是不是会去对应的一个什么 是不是一步读取我们的文件的方法

59
00:03:06,800 --> 00:03:10,900
好 那么如何去一步读取呢 我们呢 直接把我们的代码给粘贴过来

60
00:03:18,820 --> 00:03:22,920
我们这里呢 还是把咱们前面对路径的一个处理 咱们把它给改一下

61
00:03:22,920 --> 00:03:30,860
好 首先呢 我们把error给注视掉 因为我们的回调函数里面呢 一般情况下

62
00:03:30,860 --> 00:03:33,920
咱们呢 一般不会 咱们这里演示呢 肯定是没有error的 对吧

63
00:03:33,920 --> 00:03:38,020
当然如果说你在正常开发中 肯定需要去处理我们的错误 我们来执行一下看一下

64
00:03:38,020 --> 00:03:41,600
好 大家可以看到呢 这里报了一个错 报了什么错呢

65
00:03:41,600 --> 00:03:45,180
pass一直 因为我们前面的require把它给注视掉 好 我们再来执行一下

66
00:03:48,820 --> 00:03:50,360
好 还是有问题 好我们

67
00:03:50,360 --> 00:03:52,400
再来

68
00:03:52,400 --> 00:03:54,960
好 大家可以看到我们好了 word 这是不是已经打印出来了

69
00:03:54,960 --> 00:03:57,520
那么我们如何看出它是一个同步还是ebo的呢

70
00:03:57,520 --> 00:03:58,300
我们在这里

71
00:03:58,300 --> 00:03:59,580
咱们来打印一个

72
00:03:59,580 --> 00:04:01,880
咱们来打印

73
00:04:01,880 --> 00:04:04,440
打印一段东西是不是可以去验证咱们ebo的这样一个过程了对吧

74
00:04:04,440 --> 00:04:08,540
大家可以看到

75
00:04:08,540 --> 00:04:11,340
我们是不是咱们先打印这样一个等于号然后再去

76
00:04:11,340 --> 00:04:12,120
打印我们了

77
00:04:12,120 --> 00:04:12,620
好了 word

78
00:04:12,620 --> 00:04:13,520
好 那么文件

79
00:04:13,520 --> 00:04:16,220
同步和ebo的一个读取呢其实他们差不多了只是

80
00:04:16,220 --> 00:04:17,500
核心的区别就是他们

81
00:04:17,740 --> 00:04:21,580
一个是同步 一个是异步 那么异步就是基于回来函数

82
00:04:21,580 --> 00:04:23,620
好 那么我们接着呢 咱们继续往下看

83
00:04:23,620 --> 00:04:27,980
既然我们了解了同步和异步的去读取我们的一个文件 接下来我们是不是需要去了解

84
00:04:27,980 --> 00:04:29,520
如何对我们的文件进行一个

85
00:04:29,520 --> 00:04:30,800
写入 对吧

86
00:04:30,800 --> 00:04:37,200
所以呢 我这里呢 把我们的写入给复制一下 它的一些参数呢 我们再去看demon的时候咱们再来分析

87
00:04:37,200 --> 00:04:39,500
因为它参数也会很简单

88
00:04:39,500 --> 00:04:40,520
我们来看一下

89
00:04:40,520 --> 00:04:43,860
我们来粘贴一下

90
00:04:43,860 --> 00:04:46,920
然后把pass呢 也给粘贴过来

91
00:04:47,440 --> 00:04:52,140
因为以防呢我们待会出一些问题 对吧 那pass确实都是gf里面呢 大家需要特别注意我们对路径的一个处理

92
00:04:52,140 --> 00:04:58,690
好 这里呢fs.refail对我们的一个2.tst进行了一个write 也就是对它进行写入

93
00:04:58,690 --> 00:05:01,260
 写入了一个好了word 我们这里呢把它呢

94
00:05:01,260 --> 00:05:05,360
还是改为我们刚才说 使用的一个1.tst

95
00:05:05,360 --> 00:05:10,740
我们对我们的1.tst去进行一个写入

96
00:05:13,040 --> 00:05:18,420
好,我们写入的内容是什么,我们此时就不去写好了,我们写入一个

97
00:05:18,420 --> 00:05:21,480
are you ok

98
00:05:21,480 --> 00:05:23,540
are you ok

99
00:05:23,540 --> 00:05:27,880
好,那么我此时呢我们来测试一下

100
00:05:27,880 --> 00:05:31,980
那么WriteFairSync,也就是同步去写入,它的效果是什么样的,我们来执行一下

101
00:05:31,980 --> 00:05:36,080
好,我们应该执行WriteFair这个GIS

102
00:05:36,080 --> 00:05:42,740
不好意思,我刚才这里其实应该去创建的不是WriteFair,而是WriteFair,对吧

103
00:05:43,040 --> 00:05:45,340
因为我们只是去演示我们的一个write

104
00:05:45,340 --> 00:05:47,640
我们再来重新去运行一下

105
00:05:47,640 --> 00:05:52,260
什么都没有发生 说明我们的write应该已经成功了 我们来看一下我们的1.tst

106
00:05:52,260 --> 00:05:53,020
大家可以看到

107
00:05:53,020 --> 00:05:58,400
我们的咱们的这样的一个文件是不是已经改为了RUOK 对吧 那么我们之前的好了word是不是已经消失了呀

108
00:05:58,400 --> 00:06:01,980
所以呢 说明我们的写入它是一个覆盖的

109
00:06:01,980 --> 00:06:06,340
既然我们的写入有同步的

110
00:06:06,340 --> 00:06:09,160
那么我们就可以猜测它是不是一定就会有异步的写入方法

111
00:06:09,160 --> 00:06:11,200
我们就来看一下异步的写入方法

112
00:06:11,720 --> 00:06:16,680
那么一部的写入方法 其实和咱们的一个readfile其实是非常类似的

113
00:06:16,680 --> 00:06:18,580
我们来看一下它的一个demo

114
00:06:18,580 --> 00:06:26,060
咱们把它给粘连过来

115
00:06:26,060 --> 00:06:30,920
好 我们把它给注释掉

116
00:06:30,920 --> 00:06:40,140
好 大家可以看到了 我们去writefile的时候 第一个算数依然传的是咱们需要去写入的文件

117
00:06:40,140 --> 00:06:42,140
那么第二个呢就是咱们需要去写入的内容

118
00:06:42,140 --> 00:06:43,920
然后呢第三个呢就是它的一个回应函数

119
00:06:43,920 --> 00:06:44,240
对吧

120
00:06:44,240 --> 00:06:46,000
然后它写入完成之后呢

121
00:06:46,000 --> 00:06:47,860
是不是会给我们返回一些信息啊

122
00:06:47,860 --> 00:06:48,200
对吧

123
00:06:48,200 --> 00:06:51,280
那么这里呢就是它去一步写入的一个方法

124
00:06:51,280 --> 00:06:53,740
这里呢我就不去给同学们再进行重复的写入了

125
00:06:53,740 --> 00:06:54,780
其实呢都是差不多的

126
00:06:54,780 --> 00:06:57,240
好我们接下来来看一下

127
00:06:57,240 --> 00:06:58,940
因为我们刚才也介绍过了

128
00:06:58,940 --> 00:07:02,340
我们的writefail和writefail sync的都是咱们的一个覆盖操作

129
00:07:02,340 --> 00:07:05,140
那么如果说我希望在一个文件后面去进行一个追加怎么办呢

130
00:07:05,140 --> 00:07:07,620
其实这里呢就有一个appendfail

131
00:07:07,620 --> 00:07:09,760
好那么我们呢就来尝试一下在那个

132
00:07:09,760 --> 00:07:17,900
那么它的调用呢 其实也非常简单 直接调用append file sync

133
00:07:17,900 --> 00:07:22,020
然后呢 第一个参数呢 当然也是传入我们文件的一个位置 对吧 文件也就是咱们文件的路径

134
00:07:22,020 --> 00:07:27,400
那么第二个参数呢 也就是我们需要去写入写入的一个内容 比如说我们叫做append

135
00:07:27,400 --> 00:07:28,820
 dog

136
00:07:28,820 --> 00:07:29,800
我们插入一条狗

137
00:07:29,800 --> 00:07:37,320
好 我们来执行一下write file.js 我们再来看一下咱们的tst 大家可以看到我们是不是append的一个dog

138
00:07:38,160 --> 00:07:42,000
好 那么这里就是我们对文件的一个append的一个操作

139
00:07:42,000 --> 00:07:47,380
那么同样的我们的append操作它所对应的其实也有一个咱们的一个异步的方法叫做appendfair

140
00:07:47,380 --> 00:07:51,220
好 那么appendfair是如何去进行一个执行的 那么我们也

141
00:07:51,220 --> 00:07:56,840
不去演示 大家可以去猜到如何去使用 对吧

142
00:07:56,840 --> 00:07:58,900
包括后面还会有一些方法

143
00:07:58,900 --> 00:08:03,500
比如说copyfairsync 也就是同步的去拷贝文件 包括异步的去拷贝文件

144
00:08:03,500 --> 00:08:07,340
接下来的内容我们就不再去对他们纷纷进行演示了

145
00:08:07,600 --> 00:08:08,800
API的使用都非常的简单

146
00:08:08,800 --> 00:08:10,480
好 我们来回顾一下咱们这节课的内容

147
00:08:10,480 --> 00:08:16,100
我们这节课是不是有带同同学们去对我们的文件进行了同步一步的一个读写操作

148
00:08:16,100 --> 00:08:18,540
包括还可以去进行一些追加的写入

149
00:08:18,540 --> 00:08:20,520
以及包括我们的文件还可以进行拷贝

150
00:08:20,520 --> 00:08:23,720
好 那么下节课我们就来看一下我们FS它的一些高级方法

