1
00:00:00,000 --> 00:00:02,660
接下来我们来总结一下

2
00:00:02,660 --> 00:00:04,440
我们这个第二章节

3
00:00:04,440 --> 00:00:06,720
构建TCP服务这门课程

4
00:00:06,720 --> 00:00:10,640
我们在这个构建TCP服务这门课程当中

5
00:00:10,640 --> 00:00:12,520
我们主要是学习到了以下几点

6
00:00:12,520 --> 00:00:13,540
那么第一呢

7
00:00:13,540 --> 00:00:16,780
我们就了解到TCP必须呢

8
00:00:16,780 --> 00:00:17,780
建立连接

9
00:00:17,780 --> 00:00:19,320
然后才能去进行通信

10
00:00:19,320 --> 00:00:21,680
这是它的一个非常重要的一点

11
00:00:21,680 --> 00:00:23,400
就是必须首先建立连接

12
00:00:23,400 --> 00:00:25,340
然后再才能去收发数据

13
00:00:25,340 --> 00:00:27,600
TCP连接的建立呢

14
00:00:27,600 --> 00:00:29,420
是通过这种三次握手的方式

15
00:00:29,420 --> 00:00:30,280
去接着连接的

16
00:00:30,280 --> 00:00:30,980
就是说

17
00:00:30,980 --> 00:00:32,200
首先的客户端去

18
00:00:32,200 --> 00:00:33,200
请求服务端

19
00:00:33,200 --> 00:00:33,980
给他发一个信号

20
00:00:33,980 --> 00:00:35,660
然后服务端再响应一个信号

21
00:00:35,660 --> 00:00:36,540
然后接下来

22
00:00:36,540 --> 00:00:37,920
再进行这个数据的传输

23
00:00:37,920 --> 00:00:39,980
所以说一共的经过这三次

24
00:00:39,980 --> 00:00:41,360
才能就是确保

25
00:00:41,360 --> 00:00:43,840
双方都能收到双方的这个信息

26
00:00:43,840 --> 00:00:44,480
或者是数据

27
00:00:44,480 --> 00:00:46,240
然后才能去开始进行数据的传输

28
00:00:46,240 --> 00:00:48,180
然后第二点的这个

29
00:00:48,180 --> 00:00:48,960
TCP呢

30
00:00:48,960 --> 00:00:50,820
本身的是只负责这个数据的传输

31
00:00:50,820 --> 00:00:53,760
他不关心我们这个数据的格式问题

32
00:00:53,760 --> 00:00:55,240
就是说你给他一

33
00:00:55,240 --> 00:00:55,880
他就给你发一

34
00:00:55,880 --> 00:00:56,400
你给他二

35
00:00:56,400 --> 00:00:57,040
他就给你发二

36
00:00:57,040 --> 00:00:58,520
他不关心你这个是数字

37
00:00:58,520 --> 00:00:59,200
还是字幅串

38
00:00:59,200 --> 00:01:01,760
所以说如果说我们需要使用这个TCP

39
00:01:01,760 --> 00:01:03,920
去完成一些业务功能的时候

40
00:01:03,920 --> 00:01:06,360
那这个时候我们就要对我们的数据

41
00:01:06,360 --> 00:01:08,600
去进行这个业务数据格式的一个制定

42
00:01:08,600 --> 00:01:11,040
例如我们在这个聊天室的案例当中

43
00:01:11,040 --> 00:01:12,100
我们去使用了这个

44
00:01:12,100 --> 00:01:14,540
我们把数据去把它包装成了这种JSON格式

45
00:01:14,540 --> 00:01:15,900
那么只有这样的话呢

46
00:01:15,900 --> 00:01:18,220
我们才能在这个通信的两端

47
00:01:18,220 --> 00:01:19,980
就是通信的这个端中

48
00:01:19,980 --> 00:01:23,380
去使用去解析理解这个数据的含义

49
00:01:23,380 --> 00:01:25,980
当然我们现在使用的是

50
00:01:25,980 --> 00:01:27,260
自己制定的这种协议格式

51
00:01:27,260 --> 00:01:28,600
到后面的话呢

52
00:01:28,600 --> 00:01:31,820
例如我们学到这个浏览器和服务器通信的时候呢

53
00:01:31,820 --> 00:01:34,500
我们会接触到一些第三方的这个协议

54
00:01:34,500 --> 00:01:36,980
例如HTTP或者是这个WebSocket的

55
00:01:36,980 --> 00:01:40,540
那么Socket在我们这个课程当中呢

56
00:01:40,540 --> 00:01:42,000
我们还一直在使用到这个Socket

57
00:01:42,000 --> 00:01:43,400
Socket是什么呢

58
00:01:43,400 --> 00:01:45,340
Socket其实就是与之通信的另一端

59
00:01:45,340 --> 00:01:47,500
我们可以通过这个Socket呢

60
00:01:47,500 --> 00:01:50,280
去接收或者是发送这个数据

61
00:01:50,280 --> 00:01:51,020
也就是Socket

62
00:01:51,020 --> 00:01:52,680
这个就是Socket的它一个含义

63
00:01:52,680 --> 00:01:55,680
然后最下面就是关于我们整个

64
00:01:55,680 --> 00:01:58,380
这个GTCP的这种Socket通信模型

65
00:01:58,380 --> 00:02:00,980
也就是说我们这个GTCP

66
00:02:00,980 --> 00:02:02,820
Socket通信这个端一端之间

67
00:02:02,820 --> 00:02:03,800
他们要想完成通信

68
00:02:03,800 --> 00:02:04,940
他们的一个过程

69
00:02:04,940 --> 00:02:06,480
对于服务端来讲呢

70
00:02:06,480 --> 00:02:08,420
他首先要来建立这个服务端

71
00:02:08,420 --> 00:02:09,180
针听Socket

72
00:02:09,180 --> 00:02:11,180
也就是创建服务器

73
00:02:11,180 --> 00:02:12,200
然后针听一个端口号

74
00:02:12,200 --> 00:02:14,480
然后等待这个客户端的连接

75
00:02:14,480 --> 00:02:17,760
然后客户端要干嘛呢

76
00:02:17,760 --> 00:02:19,660
客户端就是要创建连接Socket

77
00:02:19,660 --> 00:02:21,120
然后向服务端发送请求

78
00:02:21,120 --> 00:02:23,400
当然这里是通过这个三次握手

79
00:02:23,400 --> 00:02:24,040
完成连接

80
00:02:24,040 --> 00:02:26,320
连接成功以后

81
00:02:26,320 --> 00:02:26,820
那么服务端

82
00:02:26,820 --> 00:02:28,240
清除到客户端的请求呢

83
00:02:28,240 --> 00:02:29,600
会创建这个连接Socket

84
00:02:29,600 --> 00:02:30,440
也就是说

85
00:02:30,440 --> 00:02:32,720
我们在服务端就可以拿到

86
00:02:32,720 --> 00:02:34,340
当前与我连接的这个客户端的

87
00:02:34,340 --> 00:02:35,280
Socket通信对象

88
00:02:35,280 --> 00:02:37,000
有了它以后

89
00:02:37,000 --> 00:02:37,840
然后双方呢

90
00:02:37,840 --> 00:02:39,680
就可以去进行这个双向通信了

91
00:02:39,680 --> 00:02:42,120
也就是说服务端可以通过这个

92
00:02:42,120 --> 00:02:44,400
建议连接成功的客户端Socket

93
00:02:44,400 --> 00:02:46,780
去对客户端进行这个消息的收发

94
00:02:46,780 --> 00:02:47,800
然后客户端

95
00:02:47,800 --> 00:02:49,320
也可以通过创建连接的这个Socket

96
00:02:49,320 --> 00:02:51,620
对服务端去进行消息的一个收发

97
00:02:51,620 --> 00:02:53,160
当然在最后

98
00:02:53,160 --> 00:02:55,600
如果说他们不需要再去进行通信了

99
00:02:55,600 --> 00:02:58,360
那么双方可以去关闭这个Socket的相关资源

100
00:02:58,360 --> 00:03:00,780
那么这样的话也就结束了这个连接

101
00:03:00,780 --> 00:03:06,580
所以说这个就是我们基于这个TCP的这个叫Socket的通信模型

102
00:03:06,580 --> 00:03:12,740
那我们基于TCP或者说构建TCP服务

103
00:03:12,740 --> 00:03:14,580
这一章节我们就先说到这里

