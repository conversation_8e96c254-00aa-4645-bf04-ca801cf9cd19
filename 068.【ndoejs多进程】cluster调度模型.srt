1
00:00:00,260 --> 00:00:01,800
好刚才我们是不是已经讲过了

2
00:00:01,800 --> 00:00:02,300
金群

3
00:00:02,300 --> 00:00:04,600
和enches他的一个多进程模型

4
00:00:04,600 --> 00:00:05,880
这节目我们就来看一下

5
00:00:05,880 --> 00:00:07,940
Cluster他的一个多进程

6
00:00:07,940 --> 00:00:10,500
调度模型

7
00:00:10,500 --> 00:00:13,060
Cluster他是怎么一回事呢

8
00:00:13,060 --> 00:00:14,080
其实我们之前

9
00:00:14,080 --> 00:00:15,100
写了一些cluster的代码

10
00:00:15,100 --> 00:00:15,880
应该有一些感觉

11
00:00:15,880 --> 00:00:18,440
其实cluster他也是由master去监听请求

12
00:00:18,440 --> 00:00:21,240
那么master监听请求在金群现象里面

13
00:00:21,240 --> 00:00:22,780
是不是由walker去抢

14
00:00:22,780 --> 00:00:24,840
但是cluster他一种模型

15
00:00:24,840 --> 00:00:25,860
就类似于enches

16
00:00:25,860 --> 00:00:27,400
是由咱们的

17
00:00:27,640 --> 00:00:30,140
监听的也就是由咱们的Master去分发

18
00:00:30,140 --> 00:00:34,300
但是呢他会通过一个叫做RundRobin的算法去分发给各个Worker

19
00:00:34,300 --> 00:00:37,240
那么我们来看一下什么是RundRobin

20
00:00:37,240 --> 00:00:40,760
这里呢大家看到这种词建议大家直接去百度

21
00:00:40,760 --> 00:00:42,520
这里老师来带同学们来搜索一下

22
00:00:42,520 --> 00:00:46,160
RundRobin中文翻译为轮询调度

23
00:00:46,160 --> 00:00:47,280
是一种以轮询的方式

24
00:00:47,280 --> 00:00:49,200
依次将一个域名解析到多个IP地址

25
00:00:49,200 --> 00:00:50,560
调度不同服务器的计算方法

26
00:00:50,560 --> 00:00:52,580
什么意思其实很好理解

27
00:00:52,580 --> 00:00:53,760
什么叫轮询呢

28
00:00:53,760 --> 00:00:56,640
比如说我们就以这幅图为例子

29
00:00:57,640 --> 00:01:00,000
比如说

30
00:01:00,000 --> 00:01:01,860
那么master监听到一个请求了

31
00:01:01,860 --> 00:01:03,280
监造一个请求

32
00:01:03,280 --> 00:01:04,720
首先它会fork四个walker出来

33
00:01:04,720 --> 00:01:07,160
这里是咱们的四个walker

34
00:01:07,160 --> 00:01:08,240
fork出来

35
00:01:08,240 --> 00:01:10,500
那么由master去监听

36
00:01:10,500 --> 00:01:11,720
再由master去dispatch

37
00:01:11,720 --> 00:01:12,940
dispatch是什么意思

38
00:01:12,940 --> 00:01:13,500
分发

39
00:01:13,500 --> 00:01:14,880
就类似于redux里面的

40
00:01:14,880 --> 00:01:15,580
dispatch action

41
00:01:15,580 --> 00:01:16,140
分发action

42
00:01:16,140 --> 00:01:16,460
对吧

43
00:01:16,460 --> 00:01:16,780
好

44
00:01:16,780 --> 00:01:18,700
那么master收到请求之后

45
00:01:18,700 --> 00:01:19,620
收到哪里的请求呢

46
00:01:19,620 --> 00:01:20,260
是不是3000了

47
00:01:20,260 --> 00:01:20,880
大家看到没有

48
00:01:20,880 --> 00:01:23,120
这里呢

49
00:01:23,120 --> 00:01:25,000
是不是有一个箭头过来

50
00:01:25,000 --> 00:01:25,880
也就是说master

51
00:01:25,880 --> 00:01:27,260
他收到3000多个的请求之后

52
00:01:27,260 --> 00:01:29,200
就会通过Dispatch发到Worker

53
00:01:29,200 --> 00:01:30,400
然后利用呢

54
00:01:30,400 --> 00:01:32,000
Rod Robin这样一种算法

55
00:01:32,000 --> 00:01:35,120
算法叫做什么呢

56
00:01:35,120 --> 00:01:36,660
Round

57
00:01:36,660 --> 00:01:39,180
Round Robin

58
00:01:39,180 --> 00:01:39,660
好

59
00:01:39,660 --> 00:01:41,260
那么Round Robin是什么一回事呢

60
00:01:41,260 --> 00:01:42,440
其实刚才咱们已经查过了

61
00:01:42,440 --> 00:01:43,500
它是叫做轮循调度算法

62
00:01:43,500 --> 00:01:44,040
什么是轮循呢

63
00:01:44,040 --> 00:01:45,220
比如说来一个请求

64
00:01:45,220 --> 00:01:46,120
Work1走

65
00:01:46,120 --> 00:01:46,780
好

66
00:01:46,780 --> 00:01:47,560
再来一个第二个

67
00:01:47,560 --> 00:01:48,260
Work2

68
00:01:48,260 --> 00:01:49,340
走

69
00:01:49,340 --> 00:01:50,500
再来一个Work3

70
00:01:50,500 --> 00:01:51,240
走

71
00:01:51,240 --> 00:01:52,120
再来一个Work4

72
00:01:52,120 --> 00:01:52,960
走

73
00:01:52,960 --> 00:01:54,680
也就是说他们是不是每一个接受一个请求

74
00:01:54,680 --> 00:01:54,860
好

75
00:01:54,860 --> 00:01:55,620
假如说再来第五个

76
00:01:55,620 --> 00:01:57,400
5是不是在这里啊

77
00:01:57,400 --> 00:01:59,360
6呢

78
00:01:59,360 --> 00:01:59,920
这里

79
00:01:59,920 --> 00:02:00,920
其实呢

80
00:02:00,920 --> 00:02:02,600
这里就是轮循调度算法的一个模型

81
00:02:02,600 --> 00:02:03,400
轮循是什么意思

82
00:02:03,400 --> 00:02:04,400
就是一个一个来

83
00:02:04,400 --> 00:02:05,260
也就是呢

84
00:02:05,260 --> 00:02:06,540
咱们master会把这些请求

85
00:02:06,540 --> 00:02:08,020
一个一个的派发给每一个

86
00:02:08,020 --> 00:02:09,180
它们呢

87
00:02:09,180 --> 00:02:10,500
是非常平均的

88
00:02:10,500 --> 00:02:11,420
其实这里就是

89
00:02:11,420 --> 00:02:13,520
咱们cluster的一个roll lobby算法

90
00:02:13,520 --> 00:02:14,460
好

91
00:02:14,460 --> 00:02:14,760
这里呢

92
00:02:14,760 --> 00:02:16,640
我们就来通过实际的代码

93
00:02:16,640 --> 00:02:17,460
去实现一段

94
00:02:17,460 --> 00:02:20,540
咱们的cluster调度的一个简易的代码

95
00:02:20,540 --> 00:02:22,200
因为咱们光说不练

96
00:02:22,200 --> 00:02:22,600
没用

97
00:02:22,600 --> 00:02:23,120
咱们来练一练

98
00:02:23,120 --> 00:02:24,100
看一下到底cluster

99
00:02:24,100 --> 00:02:25,000
它是怎么样去调度的

100
00:02:25,000 --> 00:02:26,160
有什么好处好

101
00:02:26,160 --> 00:02:32,040
首先我们会有一个

102
00:02:32,040 --> 00:02:32,960
must

103
00:02:32,960 --> 00:02:35,600
同样的我们是不是需要引入一个net模块啊

104
00:02:35,600 --> 00:02:41,120
那的模块是不涨进行咱们的说可以通信的他比较底层好然后呢

105
00:02:41,120 --> 00:02:43,600
这里fork

106
00:02:43,600 --> 00:02:47,460
require就的

107
00:02:47,460 --> 00:02:49,460
process

108
00:02:49,460 --> 00:02:53,640
写到这里同学们可能有疑问了

109
00:02:54,060 --> 00:02:55,340
老师你不是讲cluster模型吗

110
00:02:55,340 --> 00:02:57,140
你讲了半天怎么还在讲这个chill process

111
00:02:57,140 --> 00:02:59,940
实际上老师这里是通过chill process

112
00:02:59,940 --> 00:03:01,480
去实现咱们的一个

113
00:03:01,480 --> 00:03:03,540
类似于cluster模型的简易的版本

114
00:03:03,540 --> 00:03:09,160
目的是什么呢

115
00:03:09,160 --> 00:03:10,960
目的是为了帮助大家去

116
00:03:10,960 --> 00:03:13,000
真正的去领悟cluster所做的事情

117
00:03:13,000 --> 00:03:14,280
我们为什么要用chill process

118
00:03:14,280 --> 00:03:17,360
因为咱们的cluster其实就是基于chill process去封装的

119
00:03:17,360 --> 00:03:23,760
cluster就是基于chill process

120
00:03:24,060 --> 00:03:27,380
冲装的好那我们继续

121
00:03:27,380 --> 00:03:33,280
他们是不是还是需要去看的一下对吧那特点

122
00:03:33,280 --> 00:03:39,160
好这里呢我们找个地方扣一下

123
00:03:49,400 --> 00:03:53,610
好 let.create server handle 也就说咱们继续去创建一个 server 兼听了三千端口

124
00:03:53,610 --> 00:03:55,760
 然后写一个负群环

125
00:03:55,760 --> 00:03:58,720
好 又是负群环 负群环里面咱们干嘛呀

126
00:03:58,720 --> 00:04:05,000
同学们 是不是看到负群环 我们是不是就要创建进程了呀 哇 i 等于0 i

127
00:04:05,000 --> 00:04:06,060
 小于4

128
00:04:06,060 --> 00:04:11,020
哎 加加 干嘛 这里

129
00:04:19,400 --> 00:04:21,980
for循环

130
00:04:21,980 --> 00:04:23,160
好

131
00:04:23,160 --> 00:04:23,940
这里呢

132
00:04:23,940 --> 00:04:26,200
其实我们的for循环是什么呢

133
00:04:26,200 --> 00:04:31,000
首先我呢

134
00:04:31,000 --> 00:04:33,680
会挖一个walkers等于一个空数

135
00:04:33,680 --> 00:04:34,440
也就是说咱们呢

136
00:04:34,440 --> 00:04:35,240
会先定一个walker

137
00:04:35,240 --> 00:04:36,660
把这四个进程

138
00:04:36,660 --> 00:04:38,140
以一个对立的形式给存起来

139
00:04:38,140 --> 00:04:40,760
然后再在for循环里面

140
00:04:40,760 --> 00:04:43,080
walkers.push

141
00:04:43,080 --> 00:04:45,020
咱们的cluster里面

142
00:04:45,020 --> 00:04:45,940
cluster里面

143
00:04:45,940 --> 00:04:47,380
是不是也有walkers啊

144
00:04:47,380 --> 00:04:47,700
同学们

145
00:04:47,700 --> 00:04:49,040
是不是也有walkers这样一个属性

146
00:04:49,040 --> 00:04:50,580
这里呢我们就是去

147
00:04:50,580 --> 00:04:52,660
简单的去实现一个cluster里面的walkers

148
00:04:52,660 --> 00:04:55,600
好

149
00:04:55,600 --> 00:04:56,260
那么呢

150
00:04:56,260 --> 00:04:57,280
我们去push

151
00:04:57,280 --> 00:04:58,200
push什么呢

152
00:04:58,200 --> 00:04:59,000
push什么呀

153
00:04:59,000 --> 00:05:00,220
是不是我们会fork呀

154
00:05:00,220 --> 00:05:01,280
fork

155
00:05:01,280 --> 00:05:02,060
fork

156
00:05:02,060 --> 00:05:02,740
fork什么

157
00:05:02,740 --> 00:05:03,560
咱们的walker

158
00:05:03,560 --> 00:05:04,460
对吧

159
00:05:04,460 --> 00:05:04,960
咱们的walker

160
00:05:04,960 --> 00:05:08,660
这里是chill process里面使用的方法

161
00:05:08,660 --> 00:05:11,000
大家不要觉得反俗

162
00:05:11,000 --> 00:05:12,820
因为咱们真正的技术水平的提高

163
00:05:12,820 --> 00:05:16,320
就是通过不断的去实现这样一些原理性的东西

164
00:05:16,320 --> 00:05:18,180
才能真正的提升咱们的水平

165
00:05:18,180 --> 00:05:18,460
好

166
00:05:18,460 --> 00:05:22,680
那么hander咱们呢 咱们呢 已经把worker给创建出来 并且呢 谱习到一个数字里面去

167
00:05:22,680 --> 00:05:24,420
那么hander创建了一个服务 怎么办

168
00:05:24,420 --> 00:05:27,160
此时呢 我们直接去listen 让他自己去监听

169
00:05:27,160 --> 00:05:30,740
咱们让3000端口 此时呢 类似于什么master

170
00:05:30,740 --> 00:05:37,660
让hander去监听listen 直接调用listen 然后listen之后 是不是咱们需要去connection呢

171
00:05:37,660 --> 00:05:39,700
 也就是处理咱们的请求connection

172
00:05:39,700 --> 00:05:42,520
好 他呢connection等于一个方形

173
00:05:44,320 --> 00:05:48,920
第一个参数是error 第二个是hand 大家注意这个hand 不是这个hand 两个是不一样的东西

174
00:05:48,920 --> 00:05:53,800
那么咱们继续 on collection 包括listen 大家可以去看下net的文档

175
00:05:53,800 --> 00:05:55,320
好

176
00:05:55,320 --> 00:05:57,380
那么咱们在collection 里面首先

177
00:05:57,380 --> 00:05:59,680
是不是需要去walker去处理请求啊

178
00:05:59,680 --> 00:06:02,240
那么咱们把walker

179
00:06:02,240 --> 00:06:04,040
pop出来

180
00:06:04,040 --> 00:06:09,920
好 pop完之后

181
00:06:09,920 --> 00:06:10,680
怎么办

182
00:06:10,680 --> 00:06:11,720
是不是

183
00:06:11,720 --> 00:06:12,480
在

184
00:06:13,760 --> 00:06:14,520
剩的呀

185
00:06:14,520 --> 00:06:18,880
剩的是什么是不是给咱们的咱们的紫禁存咱们去通信呢

186
00:06:18,880 --> 00:06:20,420
walker.send

187
00:06:20,420 --> 00:06:23,240
然后把hunter传进去hunter上collection他在一个回调函数

188
00:06:23,240 --> 00:06:24,000
好

189
00:06:24,000 --> 00:06:24,760
walkers

190
00:06:24,760 --> 00:06:27,580
咱们的pop之后

191
00:06:27,580 --> 00:06:32,440
存到walker但是我们的进程是不是不能够酸掉啊pop是不是酸掉了所以说我们要

192
00:06:32,440 --> 00:06:33,720
把它在

193
00:06:33,720 --> 00:06:37,320
把它在插到walker

194
00:06:37,320 --> 00:06:43,200
咱们要把它插到walkers的前面去什么意思

195
00:06:43,760 --> 00:06:44,900
什么意思

196
00:06:44,900 --> 00:06:45,300
来

197
00:06:45,300 --> 00:06:46,540
咱们来看一下

198
00:06:46,540 --> 00:06:48,780
首先我们是不是有四个walker

199
00:06:48,780 --> 00:06:51,240
大家注意是不是有四个walker

200
00:06:51,240 --> 00:06:52,920
那么你首先把它pop

201
00:06:52,920 --> 00:06:53,620
pop

202
00:06:53,620 --> 00:06:54,180
pop是不是

203
00:06:54,180 --> 00:06:55,960
从对位酸出一个

204
00:06:55,960 --> 00:06:56,540
对吧

205
00:06:56,540 --> 00:06:56,900
好

206
00:06:56,900 --> 00:06:57,740
那你酸出之后

207
00:06:57,740 --> 00:06:58,920
咱们不能直接酸吧

208
00:06:58,920 --> 00:06:59,980
直接酸是不是没了呀

209
00:06:59,980 --> 00:07:00,280
同学们

210
00:07:00,280 --> 00:07:01,100
所以呢

211
00:07:01,100 --> 00:07:01,860
我们不能直接酸

212
00:07:01,860 --> 00:07:02,260
我们呢

213
00:07:02,260 --> 00:07:03,240
把它处理完之后

214
00:07:03,240 --> 00:07:04,920
处理完请求要把它再丢到前面去

215
00:07:04,920 --> 00:07:06,100
丢到前面去

216
00:07:06,100 --> 00:07:06,760
那么继续

217
00:07:06,760 --> 00:07:08,040
它处理完

218
00:07:08,040 --> 00:07:09,040
是不是咱们再要丢到前面去

219
00:07:09,040 --> 00:07:10,280
也就是形成了一个简易的轮循

220
00:07:10,280 --> 00:07:10,880
这里呢

221
00:07:10,880 --> 00:07:12,060
就是处理一个简易的轮循

222
00:07:12,060 --> 00:07:19,320
通过pop和unshift

223
00:07:19,320 --> 00:07:22,660
实现一个简易的允训

224
00:07:22,660 --> 00:07:24,520
好

225
00:07:24,520 --> 00:07:25,740
那么我们回咱们来

226
00:07:25,740 --> 00:07:27,300
回顾一下这样一段代码

227
00:07:27,300 --> 00:07:29,300
首先我们去require.net

228
00:07:29,300 --> 00:07:30,740
然后去fork的chooseprocess

229
00:07:30,740 --> 00:07:31,680
好

230
00:07:31,680 --> 00:07:32,460
那么这里呢

231
00:07:32,460 --> 00:07:33,460
首先通过一个workers

232
00:07:33,460 --> 00:07:34,260
咱们一个数组

233
00:07:34,260 --> 00:07:36,220
给它创建四个worker

234
00:07:36,220 --> 00:07:37,140
然后存到数组里面去

235
00:07:37,140 --> 00:07:37,960
存进去之后

236
00:07:37,960 --> 00:07:39,140
咱们去创建一个服务

237
00:07:39,140 --> 00:07:39,680
监听

238
00:07:39,680 --> 00:07:40,520
然后去连接

239
00:07:40,520 --> 00:07:41,940
oncollection的回调里面

240
00:07:41,940 --> 00:07:46,300
我们把Walker 泡泡说来也就是咱们从对位取出一个

241
00:07:46,300 --> 00:07:49,880
然后进行圣的也就是和他的进程之间去通信

242
00:07:49,880 --> 00:07:52,940
传入回流函数也就是说和Walker去通信

243
00:07:52,940 --> 00:07:54,480
那么Walker是在

244
00:07:54,480 --> 00:07:58,580
首先咱们把泡泡是不是对位处理完之后再插到咱们的队的前面去

245
00:07:58,580 --> 00:08:01,900
是不是插到咱们数据的前面去这样咱们就形成一个简易的轮循

246
00:08:01,900 --> 00:08:03,700
好 这里是Master的逻辑

247
00:08:03,700 --> 00:08:05,740
那么Master逻辑完成之后我们就来看一下

248
00:08:05,740 --> 00:08:08,300
Walker这边去编写

249
00:08:10,620 --> 00:08:13,180
这里首先是不是还是需要net模块

250
00:08:13,180 --> 00:08:22,400
然后呢

251
00:08:22,400 --> 00:08:25,220
咱们怎么样去监听

252
00:08:25,220 --> 00:08:26,240
Worker伸得过来的消息

253
00:08:26,240 --> 00:08:28,020
是Process.on

254
00:08:28,020 --> 00:08:31,360
Process.on

255
00:08:31,360 --> 00:08:34,420
File.on

256
00:08:34,420 --> 00:08:35,960
怎么样Message

257
00:08:35,960 --> 00:08:38,780
好callback

258
00:08:40,060 --> 00:08:44,220
第一个参数是一个空对象 第二个是Hunder 也就是咱们的Hunder

259
00:08:44,220 --> 00:08:47,740
好 准办 start 和之前一样

260
00:08:47,740 --> 00:08:59,780
好 那么后面的内容其实和之前的一样 老师直接把它粘过来

261
00:08:59,780 --> 00:09:01,480
这里首先去定义一个

262
00:09:01,480 --> 00:09:02,680
制度创

263
00:09:02,680 --> 00:09:05,140
hello load.js 然后 readbox.http 200

264
00:09:05,140 --> 00:09:07,380
这里是大的函数 它是做什么呢 留一个socket

265
00:09:07,380 --> 00:09:14,660
然后readable和writeable都为去然后去把再一个结果给返回咱们之前是不是已经写过类似代码其实咱们核心就是mastermaster的实现

266
00:09:14,660 --> 00:09:16,220
通过轮行对面

267
00:09:16,220 --> 00:09:18,120
然后通过hunter去进行

268
00:09:18,120 --> 00:09:18,820
前停

269
00:09:18,820 --> 00:09:24,240
然后再给walker send 给walker去处理把这一个回调给walker一个个处理我可干什么了我可是不是去

270
00:09:24,240 --> 00:09:28,940
直接去end对吧好那我们就要看一下效果是怎么样

271
00:09:28,940 --> 00:09:35,200
好我们来看一下效果loadmaster.js好我们的服务已经起来了

272
00:09:35,660 --> 00:09:37,060
再通过是不通过AB啊

273
00:09:37,060 --> 00:09:37,700
咱们去看一下

274
00:09:37,700 --> 00:09:37,960
走

275
00:09:37,960 --> 00:09:42,060
Goalt connection walker PID 74715

276
00:09:42,060 --> 00:09:45,900
254

277
00:09:45,900 --> 00:09:48,200
74717254

278
00:09:48,200 --> 00:09:48,980
大家看到没有

279
00:09:48,980 --> 00:09:50,000
咱们是不是有四个walker

280
00:09:50,000 --> 00:09:52,820
1516171818251次

281
00:09:52,820 --> 00:09:56,140
172541624915250

282
00:09:56,140 --> 00:09:57,420
他们是不是非常接近了

283
00:09:57,420 --> 00:09:58,440
这里

284
00:09:58,440 --> 00:10:00,740
是不是和咱们金群现象不一样

285
00:10:00,740 --> 00:10:03,440
当发生金群的时候

286
00:10:03,440 --> 00:10:05,100
咱们之前是不是有个截图

287
00:10:05,100 --> 00:10:05,620
看一下

288
00:10:05,660 --> 00:10:07,960
它们是不是差别非常大呀

289
00:10:07,960 --> 00:10:09,280
多的呢有228次

290
00:10:09,280 --> 00:10:11,480
少的呢有228次

291
00:10:11,480 --> 00:10:12,420
多的呢有292次

292
00:10:12,420 --> 00:10:13,480
它们是不是相隔70次

293
00:10:13,480 --> 00:10:14,660
那么但是我们

294
00:10:14,660 --> 00:10:16,860
我们在一种cluster的简易调度模型通过

295
00:10:16,860 --> 00:10:18,980
咱们的rod robin也就是轮循算法

296
00:10:18,980 --> 00:10:21,540
是不是会让咱们的worker非常的均衡

297
00:10:21,540 --> 00:10:22,220
而且呢通过

298
00:10:22,220 --> 00:10:28,200
通过了咱们的master去监听我们的请求

299
00:10:28,200 --> 00:10:29,400
好

300
00:10:29,400 --> 00:10:31,320
这里呢我们就来回归一下cluster

301
00:10:31,320 --> 00:10:32,780
这样一种简易的调度模型

302
00:10:32,780 --> 00:10:34,960
首先是不是会在

303
00:10:34,960 --> 00:10:36,860
master去监听三千端口的

304
00:10:36,860 --> 00:10:40,120
master去监听他

305
00:10:40,120 --> 00:10:41,400
然后他创建四个worker

306
00:10:41,400 --> 00:10:43,160
核心是什么

307
00:10:43,160 --> 00:10:44,020
是不是dispatch

308
00:10:44,020 --> 00:10:45,520
然后呢基于什么算法

309
00:10:45,520 --> 00:10:47,340
是不是基于轮循算法

310
00:10:47,340 --> 00:10:48,860
好

311
00:10:48,860 --> 00:10:49,940
那么这里呢就是cluster

312
00:10:49,940 --> 00:10:51,280
他的一个简易模型

313
00:10:51,280 --> 00:10:52,440
那么代码层面的话

314
00:10:52,440 --> 00:10:54,260
老师呢简单给大家再总结一遍

315
00:10:54,260 --> 00:10:54,760
首先

316
00:10:54,760 --> 00:10:56,620
咱们去引入chill process的fork

317
00:10:56,620 --> 00:10:59,800
然后呢去fork四次

318
00:10:59,800 --> 00:11:00,980
然后把它给存起来

319
00:11:00,980 --> 00:11:01,560
也就是workers

320
00:11:01,560 --> 00:11:03,180
那么呢通过hander

321
00:11:03,180 --> 00:11:04,080
hander去监听三千

322
00:11:04,080 --> 00:11:04,660
此时呢

323
00:11:04,660 --> 00:11:06,960
咱们呢 此时这里它是不是master呀 同学们

324
00:11:06,960 --> 00:11:09,260
这里是不是master呀

325
00:11:09,260 --> 00:11:14,900
也就是 也就是说咱们的hunter是不是master去处理了 也就是hunter他去监听我们的请求

326
00:11:14,900 --> 00:11:18,480
监听之后呢 通过clutching的回调

327
00:11:18,480 --> 00:11:25,020
通过walker去圣的 圣的给紫禁城 紫禁城再去处理.end rest 好 这里呢就是这节课的内容

