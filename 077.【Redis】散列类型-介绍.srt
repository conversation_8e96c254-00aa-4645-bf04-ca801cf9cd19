1
00:00:00,000 --> 00:00:01,960
好,这节课我们来看一下闪电类型

2
00:00:01,960 --> 00:00:04,080
那么什么是闪电类型呢

3
00:00:04,080 --> 00:00:05,400
那么闪电类型

4
00:00:05,400 --> 00:00:07,940
其实这里我们要谈到刚才

5
00:00:07,940 --> 00:00:10,280
我们所举的文章的那一个例子

6
00:00:10,280 --> 00:00:12,700
比如说我们去读取文章的一些信息的时候

7
00:00:12,700 --> 00:00:15,320
我们是不是需要在存储和读取的时候

8
00:00:15,320 --> 00:00:17,800
都需要把文章的它这样一个字物串

9
00:00:17,800 --> 00:00:19,300
进行序列化和反序列化

10
00:00:19,300 --> 00:00:20,940
比如说我们刚才写的这样一段代码

11
00:00:20,940 --> 00:00:24,800
我们在存储的时候

12
00:00:24,800 --> 00:00:25,800
是不是需要把一个对象

13
00:00:25,800 --> 00:00:27,880
通过json.sg.fi把它转成字物串

14
00:00:27,880 --> 00:00:29,120
然后去set存储

15
00:00:29,120 --> 00:00:30,100
那么读取的时候呢

16
00:00:30,100 --> 00:00:31,100
你把它get出来之后

17
00:00:31,100 --> 00:00:32,680
是不是咱们还需要通过节省点pass

18
00:00:32,680 --> 00:00:33,920
把它转为真正的对象

19
00:00:33,920 --> 00:00:34,740
然后再去读取

20
00:00:34,740 --> 00:00:37,180
那么这样好不好呢

21
00:00:37,180 --> 00:00:37,840
其实不好

22
00:00:37,840 --> 00:00:38,400
为什么呀

23
00:00:38,400 --> 00:00:39,780
因为我们每次存储或者读取

24
00:00:39,780 --> 00:00:42,360
都需要去进行序量化和反序量化

25
00:00:42,360 --> 00:00:43,540
它是比较消耗性能的

26
00:00:43,540 --> 00:00:44,680
会造成资源的浪费

27
00:00:44,680 --> 00:00:46,280
所以说

28
00:00:46,280 --> 00:00:48,380
通过自传去做这一类的需求

29
00:00:48,380 --> 00:00:50,040
是不是很优雅的

30
00:00:50,040 --> 00:00:50,380
所以

31
00:00:50,380 --> 00:00:52,320
闪电它解决了这样一个问题

32
00:00:52,320 --> 00:00:55,600
那么我们来看一下闪电

33
00:00:55,600 --> 00:00:58,040
闪电它是否存储对象

34
00:00:58,040 --> 00:01:01,860
其实它是使用对象类别和ID构成件名

35
00:01:01,860 --> 00:01:02,260
什么意思

36
00:01:02,260 --> 00:01:03,560
比如说

37
00:01:03,560 --> 00:01:05,840
比如说我们现在有这样一个汽车模型

38
00:01:05,840 --> 00:01:06,820
那么闪电的话

39
00:01:06,820 --> 00:01:09,980
闪电它其实就是一种二维的数据结构

40
00:01:09,980 --> 00:01:11,240
二维的数据结构

41
00:01:11,240 --> 00:01:13,200
比如说我们的data有一个k

42
00:01:13,200 --> 00:01:14,160
k有一个value

43
00:01:14,160 --> 00:01:15,780
那么我们的value它又是一个对象

44
00:01:15,780 --> 00:01:17,160
其实这就是一种二维数据结构

45
00:01:17,160 --> 00:01:22,280
比如说我们对象的类别和ID构成件名

46
00:01:22,280 --> 00:01:24,240
比如说我们去存储汽车的时候

47
00:01:24,240 --> 00:01:25,340
类别是car

48
00:01:25,340 --> 00:01:26,320
ID是2

49
00:01:26,320 --> 00:01:27,620
这里就构成了它的件名

50
00:01:27,620 --> 00:01:29,380
那么我们的自断是什么呢

51
00:01:29,380 --> 00:01:31,400
比如说自断咱们的汽车是不是有一些

52
00:01:31,400 --> 00:01:33,420
比如说color name价格

53
00:01:33,420 --> 00:01:35,940
接下来就是它自断的值

54
00:01:35,940 --> 00:01:38,180
其实这里就是一个二维的对象

55
00:01:38,180 --> 00:01:39,940
这是我们的汽车模型

56
00:01:39,940 --> 00:01:41,860
好

57
00:01:41,860 --> 00:01:44,140
那么我们再来看一下咱们的关系型数据库

58
00:01:44,140 --> 00:01:45,880
比如说想实现咱们这样一个汽车模型

59
00:01:45,880 --> 00:01:48,400
我们是不是需要去存储

60
00:01:48,400 --> 00:01:50,500
首先id color name price

61
00:01:50,500 --> 00:01:51,740
是不是你有哪些属性

62
00:01:51,740 --> 00:01:53,880
咱们在关系型数据库里面就需要去存储

63
00:01:53,880 --> 00:01:55,060
哪些自断

64
00:01:55,060 --> 00:01:56,740
好这些自断呢

65
00:01:56,740 --> 00:02:05,700
他又需要,假如说,假如我们现在遇到一种场景,遇到一种情况,我们的宝马,我们的宝马车,他需要新增一个资料,叫做data。

66
00:02:05,700 --> 00:02:12,780
你比如说我们的车子坏了去维修,所以会有一个维修日期,但是奥迪和宾利,他的质量比较好,不需要这样一个资料。

67
00:02:12,780 --> 00:02:24,460
那么在咱们的关系系数据库里面,这个多余的data资料会造成一些问题,你比如说咱们只有宝马需要,那么奥迪和宾利不需要,是不是造成了我们内存的浪费,而且我们在新建资端的时候也会有一些会影响性能。

68
00:02:24,940 --> 00:02:27,240
但是Redis却不存在这样一个问题

69
00:02:27,240 --> 00:02:28,260
我们可以为任何

70
00:02:28,260 --> 00:02:31,980
键新增或者双处这段而不影响其他

71
00:02:31,980 --> 00:02:34,160
这里就是咱们闪链的他一个特点

72
00:02:34,160 --> 00:02:36,980
好 那么我们来总结一下

73
00:02:36,980 --> 00:02:39,180
我们的闪链它是不是就是一种二维数据结构

74
00:02:39,180 --> 00:02:41,160
它比较灵活 你不管怎么干都不会影响

75
00:02:41,160 --> 00:02:43,260
其他就有点类似于我们建议室里面的对象

76
00:02:43,260 --> 00:02:44,400
那么闪链呢 其实

77
00:02:44,400 --> 00:02:47,420
闪链 其实它就是咱们的字顶

78
00:02:47,420 --> 00:02:49,780
dist 大家可以理解为它就是咱们的字顶

79
00:02:49,780 --> 00:02:51,940
好 这里就是这节课的内容

