1
00:00:00,000 --> 00:00:04,360
好 这节课我们来看一下 字幕创类型它的一些扩展

2
00:00:04,360 --> 00:00:08,840
首先第一个 我们怎么样去增加指定的整数

3
00:00:08,840 --> 00:00:16,380
比如说我们调用incr的时候是不是每次只能地震一样 那么现在呢 再有一个这样的命令叫做incrby

4
00:00:16,380 --> 00:00:23,040
比如说我们去输入一个 好 首先我们来看一下 我们所有的case case

5
00:00:23,040 --> 00:00:26,620
好 它有这么多的一些属性

6
00:00:26,620 --> 00:00:28,680
比如说我们来看一下

7
00:00:29,700 --> 00:00:33,480
我记得abcd好像是一个数字getabcd 好它是3

8
00:00:33,480 --> 00:00:38,940
假如我们想将abcd进行加10怎么办 我们就可以用incrby

9
00:00:38,940 --> 00:00:41,280
然后输入kabcd

10
00:00:41,280 --> 00:00:45,360
好 我们来看一下 那么此时我们abcd就变成了13

11
00:00:45,360 --> 00:00:50,940
其实它就是在咱们的incr后面加了一个by 好

12
00:00:50,940 --> 00:00:54,580
第二个 减少指定的整数

13
00:00:54,580 --> 00:00:59,300
首先我们还是abcd 我们刚才incr它是增加 我们来看一下怎么减少

14
00:00:59,700 --> 00:01:01,080
通过D1CR

15
00:01:01,080 --> 00:01:03,460
D1CR

16
00:01:03,460 --> 00:01:07,720
比如说我们D1CR,A,B,C,D,是不是变为12

17
00:01:07,720 --> 00:01:09,640
只要去执行它就-1

18
00:01:09,640 --> 00:01:11,980
怎么去-去指定的整数呢

19
00:01:11,980 --> 00:01:12,720
比如说我要去-10

20
00:01:12,720 --> 00:01:15,240
是不是还是交流一个by和增加是一样的

21
00:01:15,240 --> 00:01:15,840
它们是对称的

22
00:01:15,840 --> 00:01:16,560
D1CR

23
00:01:16,560 --> 00:01:20,940
A,B,C,D,10

24
00:01:20,940 --> 00:01:23,960
好,R,Run,哦

25
00:01:23,960 --> 00:01:25,960
咱们是不是少一个by啊

26
00:01:25,960 --> 00:01:27,020
好,大家看到没有

27
00:01:27,020 --> 00:01:29,380
咱们的12就已经-10变为2

28
00:01:29,700 --> 00:01:33,540
好 看一下第三个 增加指定的伏点数 伏点数是不是就是我们的小数啊

29
00:01:33,540 --> 00:01:35,840
 怎么操作呢 叫做incrbyflot

30
00:01:35,840 --> 00:01:44,300
我们来看一下incrbyflot

31
00:01:44,300 --> 00:01:50,440
比如说我们的abcd 我们给他加多少呢 我们给他加一个0.5 他是不是就会变成

32
00:01:50,440 --> 00:01:51,720
2.5

33
00:01:51,720 --> 00:01:55,040
大家看到没有 咱们的abcd就变成了2.5

34
00:01:55,820 --> 00:01:59,660
而且呢 同时呢 它还可以去添加一些 比如说我们一些很大的数

35
00:01:59,660 --> 00:02:02,740
好 这里我们来看一下

36
00:02:02,740 --> 00:02:05,040
很大的伏点数是怎么回事

37
00:02:05,040 --> 00:02:08,100
incr byflots

38
00:02:08,100 --> 00:02:12,200
5d+4

39
00:02:12,200 --> 00:02:16,040
什么呢 那么abcd吧 是吧 abcd

40
00:02:16,040 --> 00:02:17,840
大家看到没有 它变成了

41
00:02:17,840 --> 00:02:22,700
50002.5 这里呢 是它对伏点数的一个操作

42
00:02:23,220 --> 00:02:27,820
第四个向违顾追降值append 咱们在浏览器里面是不是经常去append一个Door吗

43
00:02:27,820 --> 00:02:30,140
那么Redis他怎么去append呢 比如说

44
00:02:30,140 --> 00:02:35,760
比如说我们去set一个key set一个test吧 它就是一个

45
00:02:35,760 --> 00:02:37,300
hello

46
00:02:37,300 --> 00:02:42,160
咱们来get一下gettest 它的值呢是hello 那么我们来操作一下append

47
00:02:42,160 --> 00:02:47,020
test 怎么了 word 咱们来看一下结果

48
00:02:47,020 --> 00:02:52,660
它的返回值是我们插入之后它的长度是多少 那么我们就来get一下

49
00:02:53,220 --> 00:02:55,340
gettest

50
00:02:55,340 --> 00:02:57,260
大家看到没有

51
00:02:57,260 --> 00:02:58,240
变成了好多word

52
00:02:58,240 --> 00:02:59,300
好

53
00:02:59,300 --> 00:02:59,940
我们再来看一下

54
00:02:59,940 --> 00:03:01,160
怎么去获取字不串的长度

55
00:03:01,160 --> 00:03:04,640
命令很简单

56
00:03:04,640 --> 00:03:06,660
strln

57
00:03:06,660 --> 00:03:08,240
strln

58
00:03:08,240 --> 00:03:10,140
也就是student的一个length

59
00:03:10,140 --> 00:03:10,620
对吧

60
00:03:10,620 --> 00:03:11,540
好

61
00:03:11,540 --> 00:03:12,120
我们再来看一下

62
00:03:12,120 --> 00:03:13,280
我们来看一下test

63
00:03:13,280 --> 00:03:14,160
它的长度是10

64
00:03:14,160 --> 00:03:15,820
好

65
00:03:15,820 --> 00:03:17,040
那么这里有一个提示

66
00:03:17,040 --> 00:03:17,760
就是说

67
00:03:17,760 --> 00:03:20,140
我们使用utf-8编码的中文

68
00:03:20,140 --> 00:03:21,580
由于理和好两个字的

69
00:03:21,580 --> 00:03:22,800
它的编码的长度都是3

70
00:03:22,800 --> 00:03:27,560
所以磁力的会返回6 什么意思啊 实际上在咱们的编码中 我们的中文

71
00:03:27,560 --> 00:03:30,260
它的utf长度不是1吧

72
00:03:30,260 --> 00:03:35,240
怎么理解 比如说 我们去set一个k set一个

73
00:03:35,240 --> 00:03:38,960
a吧 比如我们set一个

74
00:03:38,960 --> 00:03:42,720
汉字 叫做你好 好 我们来get一下

75
00:03:42,720 --> 00:03:45,000
get汉字

76
00:03:45,000 --> 00:03:48,120
大家看到没有 它是一串编码

77
00:03:49,000 --> 00:03:51,300
UTF-8那个编码,那么我们来看一下它的长度

78
00:03:51,300 --> 00:03:52,840
STR Lens

79
00:03:52,840 --> 00:03:55,400
K汉字

80
00:03:55,400 --> 00:03:57,440
它的长度是6,说明一个什么问题

81
00:03:57,440 --> 00:03:58,980
说明我们一个你好

82
00:03:58,980 --> 00:04:02,320
你好它一个汉字占我们三个字节

83
00:04:02,320 --> 00:04:03,840
UTF-8

84
00:04:03,840 --> 00:04:06,160
如果说对UTF-8有兴趣的同学可以去了解一下

85
00:04:06,160 --> 00:04:07,940
咱们计算去编码的一个发展的过程

86
00:04:07,940 --> 00:04:10,000
这里就不在本节课去介绍了

87
00:04:10,000 --> 00:04:12,040
网上有很多的一些资料可以自己去查一下

88
00:04:12,040 --> 00:04:14,340
好,这里了

89
00:04:14,340 --> 00:04:18,440
或许和设置多个建制,我们来看一下怎么去做

90
00:04:19,000 --> 00:04:24,120
比如说 是不是我们通过在get和set下面去加一个m

91
00:04:24,120 --> 00:04:31,720
mset 比如说我们的mset一个k1k1的值为1k2的值为2

92
00:04:31,720 --> 00:04:34,240
好 我们已经设置成功了 那我们来获取一下mget

93
00:04:34,240 --> 00:04:39,960
k1k2大家看到没有 1和2其实已经都已经读到了

94
00:04:39,960 --> 00:04:41,240
那么接下来我们来看一下

95
00:04:41,240 --> 00:04:45,040
一个比较烧气的一个操作叫做位操作

96
00:04:45,040 --> 00:04:46,160
那么什么是位操作呢

97
00:04:46,160 --> 00:04:50,260
大家都知道我们的计算机它是由二极管二进制所组成的

98
00:04:50,260 --> 00:04:52,300
那么它是什么意思呢

99
00:04:52,300 --> 00:04:54,480
比如说我们去set的一个-bar

100
00:04:54,480 --> 00:04:57,040
我们去set的一个-bar

101
00:04:57,040 --> 00:05:00,180
也就是咱们给的它一个-这样一个属性

102
00:05:00,180 --> 00:05:00,760
它只为bar

103
00:05:00,760 --> 00:05:04,240
那么我们来看一下它的一个ascl码分别是什么

104
00:05:04,240 --> 00:05:06,120
分别是9897和114

105
00:05:06,120 --> 00:05:08,500
那么它的转化成二进制的分别是

106
00:05:08,500 --> 00:05:12,820
110010 1100001和110010

107
00:05:12,820 --> 00:05:14,200
什么意思

108
00:05:14,200 --> 00:05:15,500
二进制这里的话

109
00:05:15,500 --> 00:05:17,680
老师呢可以给同学们简单的去讲解一下

110
00:05:17,680 --> 00:05:19,020
你比如说

111
00:05:19,020 --> 00:05:20,180
比如说98

112
00:05:20,180 --> 00:05:24,100
那么首先这里呢我们可能需要去讲到什么是ASCL

113
00:05:24,100 --> 00:05:27,420
这里呢我们简单的给大家去介绍一下我们计算机发展的一个历程

114
00:05:27,420 --> 00:05:28,680
你比如说我们

115
00:05:28,680 --> 00:05:30,000
所有的

116
00:05:30,000 --> 00:05:32,420
比如说我们在计算机的发展过程中

117
00:05:32,420 --> 00:05:36,100
所有的文字

118
00:05:36,100 --> 00:05:37,860
比如说文字包括什么呀

119
00:05:37,860 --> 00:05:38,740
同学们比如说我们的

120
00:05:38,740 --> 00:05:40,060
就是我们的英文单词

121
00:05:40,060 --> 00:05:42,100
汉字包括韩语

122
00:05:42,100 --> 00:05:43,740
日语

123
00:05:43,740 --> 00:05:45,320
符号等等

124
00:05:45,320 --> 00:05:46,960
咱们怎么样去表示它呢

125
00:05:46,960 --> 00:05:47,900
是不是都是通过一个

126
00:05:47,900 --> 00:05:49,060
其实都是通过一个

127
00:05:49,060 --> 00:05:51,340
数字去表示

128
00:05:51,340 --> 00:05:53,060
但是呢这个数字很大

129
00:05:53,060 --> 00:05:53,740
你比如说

130
00:05:53,740 --> 00:05:56,740
BAR是几个英文字母

131
00:05:56,740 --> 00:06:00,320
它所对应的AASCLR码分别是9897和114

132
00:06:00,320 --> 00:06:01,780
说明了它们

133
00:06:01,780 --> 00:06:04,680
属于比较前面的位置

134
00:06:04,680 --> 00:06:05,720
你比如说像汉语

135
00:06:05,720 --> 00:06:09,180
可能在咱们的AASCLR码里面在第几万个位置

136
00:06:09,180 --> 00:06:11,120
包括像日语和韩语可能就更远了

137
00:06:11,120 --> 00:06:13,340
也就是说咱们世界上存在多少个语言多少个字符

138
00:06:13,340 --> 00:06:15,200
它呢就会分别有唯一的码所

139
00:06:15,200 --> 00:06:20,180
对应 这就是asclr嘛 它的一个意义 那么咱们怎么样通过二进制去

140
00:06:20,180 --> 00:06:23,840
去表示它 那么二进制 同学们

141
00:06:23,840 --> 00:06:27,720
不少同学们了解我 这里老师来简单的介绍一下 比如说我们现在有个二进制

142
00:06:27,720 --> 00:06:28,800
它的一个值

143
00:06:28,800 --> 00:06:31,720
为1和1 那么我们

144
00:06:31,720 --> 00:06:38,260
怎么样去计算它实际的一个asclr的编码到底是多少 我们是不是可以通过2的

145
00:06:38,260 --> 00:06:40,500
三次方

146
00:06:40,500 --> 00:06:44,840
加上2的平方 再加上什么 加上2

147
00:06:45,200 --> 00:06:46,860
为什么呀 比如说我们这里改成0

148
00:06:46,860 --> 00:06:49,040
是不是我们就要把2的三次方给去掉

149
00:06:49,040 --> 00:06:54,490
假如说我们把第二位改成0呢 我们是不是就需要把2的平方给去掉啊 这里呢

150
00:06:54,490 --> 00:06:55,700
 我们只简单的介绍一下

151
00:06:55,700 --> 00:06:57,480
咱们二进制的一个原理

152
00:06:57,480 --> 00:07:01,840
如果说有不了解的同学可以去百度一下 这里呢 网上也有很多的一些资料

153
00:07:01,840 --> 00:07:04,140
这里是二进制简单的一个介绍

154
00:07:04,140 --> 00:07:05,680
这里我们就来看一下

155
00:07:05,680 --> 00:07:07,980
比AR这三个字母分别对应的

156
00:07:07,980 --> 00:07:12,600
在计算机里面是怎么去存储的 它通过二进制会这么样去存储 你比如说

157
00:07:12,840 --> 00:07:15,240
我们通过getbyte去获取一下

158
00:07:15,240 --> 00:07:18,520
getbyte也就是说获取咱们的

159
00:07:18,520 --> 00:07:20,280
在计算机里面他说

160
00:07:20,280 --> 00:07:24,280
比如说k是咱们的比如说我们来选择负二

161
00:07:24,280 --> 00:07:25,560
他的指挥二进手是这样的

162
00:07:25,560 --> 00:07:26,940
我们来看一下第0位他是什么

163
00:07:26,940 --> 00:07:28,080
第0位

164
00:07:28,080 --> 00:07:29,820
第0位是不是0呢

165
00:07:29,820 --> 00:07:30,060
同学们

166
00:07:30,060 --> 00:07:31,400
对吧

167
00:07:31,400 --> 00:07:32,600
那么我们来看一下第6位是什么

168
00:07:32,600 --> 00:07:36,660
123456也是0吧

169
00:07:36,660 --> 00:07:37,920
1

170
00:07:37,920 --> 00:07:40,480
重新数一遍

171
00:07:40,480 --> 00:07:42,600
0123456是1

172
00:07:42,600 --> 00:07:43,660
对 没有问题

173
00:07:43,660 --> 00:07:46,880
那么可能有的同学可能会问的

174
00:07:46,880 --> 00:07:48,400
老师你讲个这么恶心的东西

175
00:07:48,400 --> 00:07:49,460
讲个二进制它有什么用

176
00:07:49,460 --> 00:07:50,460
在咱们的实际性场景中

177
00:07:50,460 --> 00:07:51,640
其实它非常有用

178
00:07:51,640 --> 00:07:52,940
你比如说我举个例子

179
00:07:52,940 --> 00:07:53,720
同学们

180
00:07:53,720 --> 00:07:54,600
你比如说我们

181
00:07:54,600 --> 00:07:57,180
平时在Redis存储数据的时候

182
00:07:57,180 --> 00:07:59,020
我们是不是会经常遇到这样的场景

183
00:07:59,020 --> 00:08:00,700
比如说我们去统计一个学校的学生

184
00:08:00,700 --> 00:08:01,980
他的性别为男和女

185
00:08:01,980 --> 00:08:04,660
那么如果说我们去用数字零和一去存储的时候

186
00:08:04,660 --> 00:08:06,960
可能需要100万个字节

187
00:08:06,960 --> 00:08:07,600
约等于一兆

188
00:08:07,600 --> 00:08:09,200
为什么呀

189
00:08:09,200 --> 00:08:11,340
因为刚才老师是不是讲解了ascl

190
00:08:11,340 --> 00:08:16,350
他那个吗 他的启示通过二进次 他可能是通过一般来讲 我们的一般来讲

191
00:08:16,350 --> 00:08:18,660
 我们的一个字符都是通过一个八位

192
00:08:18,660 --> 00:08:19,860
八位的一个

193
00:08:19,860 --> 00:08:26,160
计算机的八位来代表一个数字0 虽然说我们数字是0 但是他会

194
00:08:26,160 --> 00:08:29,120
在计算机里面所占用八个位

195
00:08:29,120 --> 00:08:36,580
那么但是你如果说用二进次去存储的话 你只需要100k 为什么呀 因为我们可以只占用计算机的一个位

196
00:08:41,340 --> 00:08:44,220
这里呢就是未操作的一个

197
00:08:44,220 --> 00:08:45,780
它的一个使用场景

198
00:08:45,780 --> 00:08:48,820
它呢可以有效的减少我们redis存储的一个空间

199
00:08:48,820 --> 00:08:51,320
更加的能够增加它的一个查询的速度

200
00:08:51,320 --> 00:08:52,500
比如说我们再举一个例子

201
00:08:52,500 --> 00:08:54,120
我们id是不是从

202
00:08:54,120 --> 00:08:56,600
假设我们id从100万开始递增

203
00:08:56,600 --> 00:08:57,680
那么呢我们可以

204
00:08:57,680 --> 00:08:59,020
是不是比如说我们id

205
00:08:59,020 --> 00:09:00,300
第一个同学它就是100万

206
00:09:00,300 --> 00:09:01,800
第二个同学呢是101万

207
00:09:01,800 --> 00:09:02,940
那么我们在存储的时候

208
00:09:02,940 --> 00:09:05,100
其实依然可以先把它减去100万

209
00:09:05,100 --> 00:09:08,580
依然按照0和01234567890这样递增去存储

210
00:09:08,580 --> 00:09:09,700
那么在实际读取的时候

211
00:09:09,700 --> 00:09:10,820
我们是不是再去进行

212
00:09:10,820 --> 00:09:11,860
去进行

213
00:09:11,860 --> 00:09:14,300
和100万进行相加一个操作

214
00:09:14,300 --> 00:09:15,420
这样可以去节约空间

215
00:09:15,420 --> 00:09:18,340
那么其实我们的位操作也是利用了这样一个思想

216
00:09:18,340 --> 00:09:20,900
比如我们男女就有两个状态

217
00:09:20,900 --> 00:09:23,180
我们通过计算机的一个位就可以去表示它

218
00:09:23,180 --> 00:09:24,800
也就是零和一就完全可以去表示它

219
00:09:24,800 --> 00:09:26,720
我们没有必要去用ascl的

220
00:09:26,720 --> 00:09:28,220
ascll的嘛

221
00:09:28,220 --> 00:09:29,540
咱们的支付串是不是都是基于它的

222
00:09:29,540 --> 00:09:30,480
所以说呢

223
00:09:30,480 --> 00:09:33,220
咱们去操作位操作咱们计算机更底层

224
00:09:33,220 --> 00:09:34,520
更有助于咱们的性能

225
00:09:34,520 --> 00:09:34,900
好

226
00:09:34,900 --> 00:09:36,700
这里呢我们就来

227
00:09:36,700 --> 00:09:39,320
总结一下刚才所讲解的

228
00:09:39,320 --> 00:09:40,320
内容

229
00:09:40,320 --> 00:09:44,220
刚才我们是不是讲解了一些扩展的命令呢

230
00:09:44,220 --> 00:09:46,220
比如说什么呀

231
00:09:46,220 --> 00:09:48,900
是不是我们的新增操作呀

232
00:09:48,900 --> 00:09:49,660
Increate by

233
00:09:49,660 --> 00:09:51,100
比如说我们可以指定

234
00:09:51,100 --> 00:09:53,540
指定对吧

235
00:09:53,540 --> 00:09:56,060
指定增加

236
00:09:56,060 --> 00:09:57,280
删除

237
00:09:57,280 --> 00:09:59,200
那么删除的命令是什么呢

238
00:09:59,200 --> 00:10:00,100
删除是不是

239
00:10:00,100 --> 00:10:02,140
减少啊

240
00:10:02,140 --> 00:10:03,940
不好意思说错了

241
00:10:03,940 --> 00:10:04,740
不是增加是咱们的

242
00:10:04,740 --> 00:10:05,140
减少

243
00:10:05,140 --> 00:10:06,220
是不是咱们的

244
00:10:06,220 --> 00:10:08,560
第一CR

245
00:10:08,560 --> 00:10:10,280
你如果说想要指定的去减少了

246
00:10:10,280 --> 00:10:12,680
是不是的cr bye呀

247
00:10:12,680 --> 00:10:14,040
好

248
00:10:14,040 --> 00:10:15,960
辅典操作呢

249
00:10:15,960 --> 00:10:17,000
咱们辅典操作

250
00:10:17,000 --> 00:10:18,840
incre

251
00:10:18,840 --> 00:10:22,120
incr bye

252
00:10:22,120 --> 00:10:24,000
flot对吧

253
00:10:24,000 --> 00:10:26,760
然后呢还刚才还学的什么append

254
00:10:26,760 --> 00:10:29,640
append是不是可以从我们的尾部啊

255
00:10:29,640 --> 00:10:31,800
尾部添加

256
00:10:31,800 --> 00:10:36,160
这里是辅典运算

257
00:10:38,560 --> 00:10:39,560
减少

258
00:10:39,560 --> 00:10:44,420
好 那除了碰的了 我们是不是还讲了 怎么去获取长度啊

259
00:10:44,420 --> 00:10:49,000
获取长度 怎么去记 strs 准 ielens

260
00:10:49,000 --> 00:10:54,320
然后呢 还有一个mget和mset

261
00:10:54,320 --> 00:10:57,120
他们是不是可以进行

262
00:10:57,120 --> 00:11:01,060
他们是不是可以进行 多个字操作呀

263
00:11:01,060 --> 00:11:04,860
然后还有一个比较重要的 未操作

264
00:11:05,860 --> 00:11:07,400
他命令是什么

265
00:11:07,400 --> 00:11:09,660
是不getbyte

266
00:11:09,660 --> 00:11:11,300
然后他在一个使用场景呢

267
00:11:11,300 --> 00:11:14,820
你比如说二进制

268
00:11:14,820 --> 00:11:17,800
表示哪里关系对吧

269
00:11:17,800 --> 00:11:18,400
性别

270
00:11:18,400 --> 00:11:21,160
这样是不是可以节省我们的内存空间

271
00:11:21,160 --> 00:11:23,820
好这里呢就是我们这节课的内容

