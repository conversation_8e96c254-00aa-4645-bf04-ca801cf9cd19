1
00:00:00,000 --> 00:00:03,560
好 接下来我们可以再来加入一个非常小的功能

2
00:00:03,560 --> 00:00:05,340
这个小功能就是说

3
00:00:05,340 --> 00:00:07,120
当有人进入聊天室的时候

4
00:00:07,120 --> 00:00:08,400
我们就给他广播一下

5
00:00:08,400 --> 00:00:10,820
就是说谁谁谁进入聊天室了

6
00:00:10,820 --> 00:00:12,400
然后当前在线用户没多少

7
00:00:12,400 --> 00:00:14,680
然后当有人离开聊天室的时候

8
00:00:14,680 --> 00:00:15,680
我们再广播一下

9
00:00:15,680 --> 00:00:16,720
告诉其他人就是说

10
00:00:16,720 --> 00:00:18,240
谁谁谁离开了聊天室

11
00:00:18,240 --> 00:00:19,800
然后当前剩余多少用户在线

12
00:00:19,800 --> 00:00:21,360
就是这么一个小功能

13
00:00:21,360 --> 00:00:23,240
好 那么这个时候相当于就是说

14
00:00:23,240 --> 00:00:24,720
我们又增加了一个需求量

15
00:00:24,720 --> 00:00:26,680
所以接下来首先呢

16
00:00:26,680 --> 00:00:28,080
我们对于这样这种

17
00:00:28,080 --> 00:00:30,220
就是说提示谁上线了

18
00:00:30,220 --> 00:00:30,960
谁离线了

19
00:00:30,960 --> 00:00:31,800
这样一个功能呢

20
00:00:31,800 --> 00:00:33,200
我们又有了这个消息啊

21
00:00:33,200 --> 00:00:36,900
所以说我们再来增加一个这个消息类型啊

22
00:00:36,900 --> 00:00:37,720
那么这个类型呢

23
00:00:37,720 --> 00:00:38,900
我们简单一点就叫log

24
00:00:38,900 --> 00:00:40,300
就是日志啊

25
00:00:40,300 --> 00:00:40,580
日志

26
00:00:40,580 --> 00:00:41,320
好了

27
00:00:41,320 --> 00:00:42,480
然后接下来啊

28
00:00:42,480 --> 00:00:43,900
我们来到这个server这里啊

29
00:00:43,900 --> 00:00:46,220
首先我们来看一下上线提示啊

30
00:00:46,220 --> 00:00:47,440
就是说有新用户提示

31
00:00:47,440 --> 00:00:48,900
那么新用户提示的话呢

32
00:00:48,900 --> 00:00:49,900
应该在哪提示呢

33
00:00:49,900 --> 00:00:52,040
是不是应该当有新的用户登录成功的时候

34
00:00:52,040 --> 00:00:53,900
是不是再来去给它进行提示啊

35
00:00:53,900 --> 00:00:55,120
所以说这个时候呢

36
00:00:55,120 --> 00:00:56,100
我们应该在这里啊

37
00:00:56,100 --> 00:00:57,100
去提示这个用户

38
00:00:57,100 --> 00:00:59,540
那么这个是有用户登陆成功

39
00:00:59,540 --> 00:01:02,560
然后将来我们再来发一条消息

40
00:01:02,560 --> 00:01:03,860
再发一条消息

41
00:01:03,860 --> 00:01:07,600
我们这个就是users.for each

42
00:01:07,600 --> 00:01:09,100
users.for each

43
00:01:09,100 --> 00:01:10,720
然后是这个叫user

44
00:01:10,720 --> 00:01:13,320
好了

45
00:01:13,320 --> 00:01:14,840
那我们这条消息发给谁呢

46
00:01:14,840 --> 00:01:17,180
我们要发给这个非当前登陆用户

47
00:01:17,180 --> 00:01:19,380
就是告诉其他人非当前登陆用户

48
00:01:19,380 --> 00:01:21,080
所以说我们家里要做一个判断

49
00:01:21,080 --> 00:01:22,940
就是说如果这个user

50
00:01:22,940 --> 00:01:24,660
如果便利的这个user

51
00:01:24,660 --> 00:01:26,120
它等于谁呢

52
00:01:26,120 --> 00:01:27,280
等于当前这个登录用户

53
00:01:27,280 --> 00:01:28,060
也就是client circuit

54
00:01:28,060 --> 00:01:30,480
所以说我们在这飞一下

55
00:01:30,480 --> 00:01:32,720
如果它这个user不是当前登录用户

56
00:01:32,720 --> 00:01:35,660
那么这个时候我们在这就是user.write

57
00:01:35,660 --> 00:01:36,520
user.write

58
00:01:36,520 --> 00:01:37,660
write什么呢

59
00:01:37,660 --> 00:01:39,720
我们在这就是json.screen file

60
00:01:39,720 --> 00:01:41,240
好了那这里的话

61
00:01:41,240 --> 00:01:44,420
这个type就是taps.log

62
00:01:44,420 --> 00:01:45,200
属于日志

63
00:01:45,200 --> 00:01:47,480
好了然后接下来这有个message

64
00:01:47,480 --> 00:01:49,420
这个message呢

65
00:01:49,420 --> 00:01:50,380
它里面就是说

66
00:01:50,380 --> 00:01:52,060
就是说就有这个用户

67
00:01:52,060 --> 00:01:53,540
message我们就简单提示一下

68
00:01:53,540 --> 00:01:55,440
就是说谁谁进来了

69
00:01:55,440 --> 00:01:58,160
然后就是dollar

70
00:01:58,160 --> 00:01:59,800
然后说这个叫nic name

71
00:01:59,800 --> 00:02:02,980
进入了

72
00:02:02,980 --> 00:02:06,980
然后当前

73
00:02:06,980 --> 00:02:08,480
在线用户

74
00:02:08,480 --> 00:02:09,680
然后当前这个在线用户

75
00:02:09,680 --> 00:02:13,000
然后就是它的一个数量

76
00:02:13,000 --> 00:02:13,820
就是它的一个数量

77
00:02:13,820 --> 00:02:16,780
我们看一下这里是这样提示的

78
00:02:16,780 --> 00:02:18,940
打扰扣起来

79
00:02:18,940 --> 00:02:20,100
然后就是我们这个叫

80
00:02:20,100 --> 00:02:21,320
users

81
00:02:21,320 --> 00:02:23,200
users点

82
00:02:23,200 --> 00:02:24,000
Length

83
00:02:24,000 --> 00:02:24,660
好了

84
00:02:24,660 --> 00:02:25,180
那这样的话

85
00:02:25,180 --> 00:02:27,100
我们在这就给他去进行了一个广播

86
00:02:27,100 --> 00:02:28,540
就是给飞当前登陆用户

87
00:02:28,540 --> 00:02:29,700
就是进行了广播

88
00:02:29,700 --> 00:02:30,760
就是说有新的用户进来了

89
00:02:30,760 --> 00:02:31,700
就这个意思

90
00:02:31,700 --> 00:02:32,420
好

91
00:02:32,420 --> 00:02:33,560
那这样你广播出来以后

92
00:02:33,560 --> 00:02:34,560
然后其他的客户端

93
00:02:34,560 --> 00:02:35,760
就得来接收一下

94
00:02:35,760 --> 00:02:37,440
所以说这里的话

95
00:02:37,440 --> 00:02:38,560
又得来加一个

96
00:02:38,560 --> 00:02:40,440
CaseType.log

97
00:02:40,440 --> 00:02:42,220
对于这个log来讲

98
00:02:42,220 --> 00:02:43,060
我们再简单一点

99
00:02:43,060 --> 00:02:44,260
我们直接去Console.log

100
00:02:44,260 --> 00:02:45,740
这个Data里面的Message

101
00:02:45,740 --> 00:02:46,860
我们直接去提示出来

102
00:02:46,860 --> 00:02:47,680
好了

103
00:02:47,680 --> 00:02:48,560
那接下来这个时候

104
00:02:48,560 --> 00:02:49,460
我们就可以来测试

105
00:02:49,460 --> 00:02:50,000
来看一下

106
00:02:50,000 --> 00:02:51,840
我们打开后手台

107
00:02:51,840 --> 00:02:53,260
然后这个时候呢

108
00:02:53,260 --> 00:02:55,380
我们来NodeClient

109
00:02:55,380 --> 00:02:56,740
好了

110
00:02:56,740 --> 00:02:57,540
这里是一样的

111
00:02:57,540 --> 00:02:58,260
NodeClient

112
00:02:58,260 --> 00:03:02,020
我们来测试一下

113
00:03:02,020 --> 00:03:03,120
例如这里输入Jack

114
00:03:03,120 --> 00:03:04,440
Jack已经进来了

115
00:03:04,440 --> 00:03:05,480
例如Mac要进来

116
00:03:05,480 --> 00:03:06,340
Mac进来

117
00:03:06,340 --> 00:03:07,600
当然Mac进来的话

118
00:03:07,600 --> 00:03:08,660
是不是应该通知当前

119
00:03:08,660 --> 00:03:09,760
就是说其他的在线用户

120
00:03:09,760 --> 00:03:11,340
告诉别人Mac进来了呀

121
00:03:11,340 --> 00:03:12,080
我们在这回车

122
00:03:12,080 --> 00:03:13,320
我们在这是不是看到

123
00:03:13,320 --> 00:03:14,400
Mac进入聊天室

124
00:03:14,400 --> 00:03:15,400
当前在线用户可以2

125
00:03:15,400 --> 00:03:16,040
是不是

126
00:03:16,040 --> 00:03:17,580
当然Mac就没有必要来收了

127
00:03:17,580 --> 00:03:18,980
Mac只需要知道自己登入成功

128
00:03:18,980 --> 00:03:19,760
当前在线用户

129
00:03:19,760 --> 00:03:20,580
每2就可以了

130
00:03:20,580 --> 00:03:21,300
好

131
00:03:21,300 --> 00:03:22,560
如果说我们再来进入一个用户

132
00:03:22,560 --> 00:03:23,920
NodeClient

133
00:03:23,920 --> 00:03:25,300
我们再来输入一个Rose

134
00:03:25,300 --> 00:03:25,940
我们再来回车

135
00:03:25,940 --> 00:03:28,440
那Jack和Mac是不是都看到了

136
00:03:28,440 --> 00:03:29,420
Rose进入了聊天室

137
00:03:29,420 --> 00:03:30,360
当成在用户为3

138
00:03:30,360 --> 00:03:32,260
这就是我们这个进入

139
00:03:32,260 --> 00:03:33,240
就是通知其他用户

140
00:03:33,240 --> 00:03:34,020
我上线了

141
00:03:34,020 --> 00:03:35,100
这个就是日志

142
00:03:35,100 --> 00:03:37,300
那么如果说它离线

143
00:03:37,300 --> 00:03:38,180
就是说它要离开

144
00:03:38,180 --> 00:03:39,480
那么离开的话

145
00:03:39,480 --> 00:03:40,860
那这里的话也很简单

146
00:03:40,860 --> 00:03:41,860
我们大家就可以快速来

147
00:03:41,860 --> 00:03:42,480
给它实现一下

148
00:03:42,480 --> 00:03:43,820
离开的话

149
00:03:43,820 --> 00:03:44,700
就是说它在这要触发

150
00:03:44,700 --> 00:03:45,740
这个end的事件

151
00:03:45,740 --> 00:03:46,480
好了

152
00:03:46,480 --> 00:03:48,020
也就是说在这里

153
00:03:48,020 --> 00:03:48,920
在这里

154
00:03:48,920 --> 00:03:50,300
我们在这里就要干一件事

155
00:03:50,300 --> 00:03:50,640
就是

156
00:03:50,640 --> 00:03:53,500
广播通知

157
00:03:53,500 --> 00:03:56,880
我们直接在这里去写就行了

158
00:03:56,880 --> 00:03:59,900
广播通知其他用户

159
00:03:59,900 --> 00:04:02,640
该就是某个用户

160
00:04:02,640 --> 00:04:04,880
已离开

161
00:04:04,880 --> 00:04:08,060
然后当前这个剩余人数

162
00:04:08,060 --> 00:04:10,660
就是这样

163
00:04:10,660 --> 00:04:11,660
好了那这个时候

164
00:04:11,660 --> 00:04:12,780
我们在这个就比较简单了

165
00:04:12,780 --> 00:04:14,100
Splice如果一执行

166
00:04:14,100 --> 00:04:14,840
那是不是就证明

167
00:04:14,840 --> 00:04:15,600
那个离线的用户

168
00:04:15,600 --> 00:04:16,740
已经给大家就是移除了

169
00:04:16,740 --> 00:04:17,860
那剩下的是不是

170
00:04:17,860 --> 00:04:18,900
就是所有的在线的用户

171
00:04:18,900 --> 00:04:19,920
所以说我们大家

172
00:04:19,920 --> 00:04:21,920
就可以直接users for each

173
00:04:21,920 --> 00:04:23,920
好我们在这直接user

174
00:04:23,920 --> 00:04:25,920
这里就也不用判断了

175
00:04:25,920 --> 00:04:27,920
因为删完的剩下的就是在线的

176
00:04:27,920 --> 00:04:29,920
点write

177
00:04:29,920 --> 00:04:32,920
write这个叫json.strinside

178
00:04:32,920 --> 00:04:37,920
然后这个type就是types.log

179
00:04:37,920 --> 00:04:38,920
日志级别

180
00:04:38,920 --> 00:04:41,920
message就是某个用户离线了

181
00:04:41,920 --> 00:04:43,920
某个用户离线了

182
00:04:43,920 --> 00:04:45,920
当然了我们在这里的话

183
00:04:45,920 --> 00:04:47,920
这个用户离线

184
00:04:47,920 --> 00:04:49,520
我们给他提示一下

185
00:04:49,520 --> 00:04:52,460
然后这里提示的是进入了聊天室

186
00:04:52,460 --> 00:04:53,580
我们还用这个消息

187
00:04:53,580 --> 00:04:54,480
这个格式

188
00:04:54,480 --> 00:04:57,180
还用这个消息格式

189
00:04:57,180 --> 00:05:00,340
那这里问题就是说

190
00:05:00,340 --> 00:05:01,560
这个nikname是谁呢

191
00:05:01,560 --> 00:05:02,840
这个nikname是谁

192
00:05:02,840 --> 00:05:04,320
实际上其实就是对应的

193
00:05:04,320 --> 00:05:05,220
那个所以的那个用户

194
00:05:05,220 --> 00:05:06,580
所以说我们带这

195
00:05:06,580 --> 00:05:07,260
const

196
00:05:07,260 --> 00:05:08,640
offline

197
00:05:08,640 --> 00:05:09,700
offline user

198
00:05:09,700 --> 00:05:11,660
我们得先删之前先拿出来

199
00:05:11,660 --> 00:05:15,220
users.offline user

200
00:05:15,220 --> 00:05:16,820
所以说这里的话

201
00:05:16,820 --> 00:05:18,080
就是offline user啊

202
00:05:18,080 --> 00:05:19,920
离线用户的这个nickname啊

203
00:05:19,920 --> 00:05:22,820
这应该是是离开了聊天室

204
00:05:22,820 --> 00:05:23,920
然后当前在线用户

205
00:05:23,920 --> 00:05:25,120
User.lens

206
00:05:25,120 --> 00:05:26,660
所以说这样的话就可以了

207
00:05:26,660 --> 00:05:27,580
好当你把这个消息呢

208
00:05:27,580 --> 00:05:28,760
去给大家公布出去以后

209
00:05:28,760 --> 00:05:30,960
然后这里的话就把这个上线离线日志

210
00:05:30,960 --> 00:05:32,980
在这呢去进行了一个统一的打印啊

211
00:05:32,980 --> 00:05:34,420
统一的打印

212
00:05:34,420 --> 00:05:36,320
好然后将来我们在这就可以来测试一下啊

213
00:05:36,320 --> 00:05:37,560
测试一下

214
00:05:37,560 --> 00:05:39,120
我们不再换手台

215
00:05:39,120 --> 00:05:42,020
啊我们打断啊都得重新来

216
00:05:42,020 --> 00:05:43,280
然后我们在这

217
00:05:45,220 --> 00:05:46,660
啊我们这儿都得了啊

218
00:05:46,660 --> 00:05:46,880
好了

219
00:05:46,880 --> 00:05:48,680
里我们家里输入Jack

220
00:05:48,680 --> 00:05:49,780
哎等不成功

221
00:05:49,780 --> 00:05:50,660
啊这里

222
00:05:50,660 --> 00:05:51,880
Rose

223
00:05:51,880 --> 00:05:52,520
啊

224
00:05:52,520 --> 00:05:53,120
Mac

225
00:05:53,120 --> 00:05:54,480
Mac上线也通知了啊

226
00:05:54,480 --> 00:05:55,180
好然后再来我们让

227
00:05:55,180 --> 00:05:56,980
里我们让这个Mac来离线啊

228
00:05:56,980 --> 00:05:58,480
来我们这在Ctrl+C强制离开啊

229
00:05:58,480 --> 00:05:59,580
离开

230
00:05:59,580 --> 00:06:02,260
呃我们可以看到这里出了一个小问题啊

231
00:06:02,260 --> 00:06:03,160
就是说

232
00:06:03,160 --> 00:06:06,460
Cannot read property nickname of undefined

233
00:06:06,460 --> 00:06:09,220
呃他是不是说这里拿不到这个昵称是吧

234
00:06:09,220 --> 00:06:10,220
我们看具体原因啊

235
00:06:10,220 --> 00:06:12,160
sorting.js第81行

236
00:06:12,160 --> 00:06:13,180
第81行啊

237
00:06:13,180 --> 00:06:15,080
他说这个offline user

238
00:06:15,080 --> 00:06:17,820
应该是 users in dax

239
00:06:17,820 --> 00:06:18,640
应该是这样的

240
00:06:18,640 --> 00:06:21,040
in dax是那个我们找到那个用户的索引下标

241
00:06:21,040 --> 00:06:21,660
应该是它

242
00:06:21,660 --> 00:06:24,060
好了这个时候我们还得就是

243
00:06:24,060 --> 00:06:25,940
全部都调开

244
00:06:25,940 --> 00:06:28,620
好了

245
00:06:28,620 --> 00:06:29,920
nodeClient

246
00:06:29,920 --> 00:06:33,080
我们先让Jack进来

247
00:06:33,080 --> 00:06:34,780
nodeClient

248
00:06:34,780 --> 00:06:35,740
Mac

249
00:06:35,740 --> 00:06:38,680
我们再来一个Rose

250
00:06:38,680 --> 00:06:39,700
好了我们举个例子

251
00:06:39,700 --> 00:06:40,840
例如这个时候我们让这个Rose

252
00:06:40,840 --> 00:06:42,600
我们让这个Mac离线

253
00:06:42,600 --> 00:06:43,200
Ctrl-C

254
00:06:43,200 --> 00:06:43,940
好了

255
00:06:43,940 --> 00:06:45,080
Mac一强制离开

256
00:06:45,080 --> 00:06:45,760
我们大家就可以看到

257
00:06:45,760 --> 00:06:47,360
Mac离开了聊天室

258
00:06:47,360 --> 00:06:48,280
当前在用户为2

259
00:06:48,280 --> 00:06:51,400
其他人就收到这个Mac离线的这个日之链

260
00:06:51,400 --> 00:06:52,880
如果说Mac又想进来

261
00:06:52,880 --> 00:06:53,500
那么在这

262
00:06:53,500 --> 00:06:56,320
当然了我们这不保存这个用户的登录状态

263
00:06:56,320 --> 00:06:58,020
就是说我们每一次离开进来的时候

264
00:06:58,020 --> 00:06:59,220
都让他重新来登录注册

265
00:06:59,220 --> 00:07:00,360
好再说Mac

266
00:07:00,360 --> 00:07:01,740
你看Mac进入聊天室

267
00:07:01,740 --> 00:07:02,720
当前在用户为3

268
00:07:02,720 --> 00:07:03,580
对吧

269
00:07:03,580 --> 00:07:04,480
好那这样的话

270
00:07:04,480 --> 00:07:06,360
我们就实现了这个

271
00:07:06,360 --> 00:07:09,620
就是用户上线离线的一个通知的功能

272
00:07:09,620 --> 00:07:10,340
通知的功能

273
00:07:10,340 --> 00:07:11,440
好

274
00:07:11,440 --> 00:07:13,140
然后接下来我们在这里来简单总结一下

275
00:07:13,140 --> 00:07:14,460
用户上线

276
00:07:14,460 --> 00:07:15,780
就是说用户注册成功了

277
00:07:15,780 --> 00:07:16,540
登录成功

278
00:07:16,540 --> 00:07:17,700
所以说我们待着呢

279
00:07:17,700 --> 00:07:19,140
去发了一个广播通知

280
00:07:19,140 --> 00:07:20,560
当然我们待着呢

281
00:07:20,560 --> 00:07:22,240
把当前用户给排除在外

282
00:07:22,240 --> 00:07:23,560
就是说上线的这个用户呢

283
00:07:23,560 --> 00:07:24,420
就没有必要来看了

284
00:07:24,420 --> 00:07:25,980
就是给其他用户来看

285
00:07:25,980 --> 00:07:28,640
如果是用户离线

286
00:07:28,640 --> 00:07:29,880
离线的话就是在这里

287
00:07:29,880 --> 00:07:31,440
我们在这里去给他

288
00:07:31,440 --> 00:07:32,460
就是进行了通知

289
00:07:32,460 --> 00:07:33,720
我们这里不用去判断

290
00:07:33,720 --> 00:07:35,820
原因是当你执行了这个Splice之后呢

291
00:07:35,820 --> 00:07:36,720
那个离线的用户呢

292
00:07:36,720 --> 00:07:37,560
已经被移除了

293
00:07:37,560 --> 00:07:39,060
所以剩下的就是所有的在线用户

294
00:07:39,060 --> 00:07:40,220
所以说在这里呢

295
00:07:40,220 --> 00:07:41,780
去通知了其他所有的加线用户

296
00:07:41,780 --> 00:07:44,640
就是说谁是谁离开了当线聊天室

297
00:07:44,640 --> 00:07:45,620
当线聊天室呢

298
00:07:45,620 --> 00:07:47,200
还剩有多少个用户

299
00:07:47,200 --> 00:07:49,920
所以说这个就是我们这个用户

300
00:07:49,920 --> 00:07:52,140
就是上线和里线的一个

301
00:07:52,140 --> 00:07:53,860
广播通知的一个优化实现

