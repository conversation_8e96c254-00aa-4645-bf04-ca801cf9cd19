1
00:00:00,000 --> 00:00:02,820
好 这节课我们来看一下

2
00:00:02,820 --> 00:00:07,160
咱们列表了 命力补充 首先第一个 获取和设置指定所有的元数值

3
00:00:07,160 --> 00:00:09,480
首先

4
00:00:09,480 --> 00:00:14,600
我们来看一下怎么样去获取和设置 我们首先来比如说rpush咱们一个list

5
00:00:14,600 --> 00:00:15,880
list

6
00:00:15,880 --> 00:00:21,240
list1吧 我们现在已经有一个list了 我们来看一下 比如说l run

7
00:00:21,240 --> 00:00:23,800
l run list1

8
00:00:23,800 --> 00:00:27,140
0 -1 好 我们是不是有这样一个列表啊

9
00:00:27,140 --> 00:00:29,960
23 345 好 那我们来看一下 首先

10
00:00:30,000 --> 00:00:33,580
Lindex是不是获取指令所影的元素

11
00:00:33,580 --> 00:00:34,600
然后我们就来获取一下13

12
00:00:34,600 --> 00:00:35,780
它的所影是1吧

13
00:00:35,780 --> 00:00:38,160
比如说Lindex

14
00:00:38,160 --> 00:00:39,060
第二个

15
00:00:39,060 --> 00:00:39,940
K

16
00:00:39,940 --> 00:00:41,720
例子1

17
00:00:41,720 --> 00:00:43,520
再插入咱们的index1

18
00:00:43,520 --> 00:00:44,260
13

19
00:00:44,260 --> 00:00:45,060
没毛病

20
00:00:45,060 --> 00:00:46,100
那么我们再来设置一下

21
00:00:46,100 --> 00:00:49,320
Lset例子1

22
00:00:49,320 --> 00:00:50,100
设置谁呢

23
00:00:50,100 --> 00:00:51,440
我们来设置index为1

24
00:00:51,440 --> 00:00:52,060
把它改为什么呢

25
00:00:52,060 --> 00:00:52,480
改为30

26
00:00:52,480 --> 00:00:53,240
好OK

27
00:00:53,240 --> 00:00:53,840
我们再来看一下

28
00:00:53,840 --> 00:00:55,760
Lrand例子10-1

29
00:00:55,760 --> 00:00:56,480
2313

30
00:00:56,480 --> 00:00:57,240
是5没毛病吗

31
00:00:57,240 --> 00:00:57,800
好

32
00:00:57,800 --> 00:00:58,220
那我们继续

33
00:00:58,220 --> 00:00:59,380
我们来看一下第二个命令

34
00:00:59,380 --> 00:01:01,380
只保留列表中指定的片段

35
00:01:01,380 --> 00:01:03,380
L trim 什么意思就是可以双处指定

36
00:01:03,380 --> 00:01:05,380
所有范围之外的所有元素

37
00:01:05,380 --> 00:01:07,380
和介绍里面哪一个命令比较像

38
00:01:07,380 --> 00:01:09,380
是不是Splite和Splice

39
00:01:09,380 --> 00:01:13,380
好 那么我们来看一下这个命令怎么去用

40
00:01:13,380 --> 00:01:15,380
它其实说白了你传谁它就保留谁吧

41
00:01:15,380 --> 00:01:17,380
比如说我们再来

42
00:01:17,380 --> 00:01:19,380
L run 击一下

43
00:01:19,380 --> 00:01:21,380
二三十三四五 那我们就来看一下

44
00:01:21,380 --> 00:01:23,380
我们只保留三四五怎么去做

45
00:01:23,380 --> 00:01:25,380
比如说L trim 例子的1

46
00:01:25,380 --> 00:01:27,380
Start 是什么

47
00:01:27,380 --> 00:01:30,380
好了,是,好,我们再来执行一下lRange

48
00:01:30,380 --> 00:01:33,380
345,可以吧,好,我们来看一下第三个命理

49
00:01:33,380 --> 00:01:37,380
像列表中插入,inset k,before或者after

50
00:01:37,380 --> 00:01:39,380
pvert,pvert是什么,也就是你要在谁

51
00:01:39,380 --> 00:01:42,380
在前面或者后面去插入一个value,value就是你要插入的值

52
00:01:42,380 --> 00:01:45,380
什么意思,我们来看一下linset

53
00:01:45,380 --> 00:01:48,380
意思,比如说我们在3后面插一个30

54
00:01:48,380 --> 00:01:50,380
after

55
00:01:50,380 --> 00:01:52,380
after谁呢,3

56
00:01:52,380 --> 00:01:55,380
哦,inset拼错了

57
00:01:55,380 --> 00:02:02,380
l insert least after3,我们少了一个value,30

58
00:02:02,380 --> 00:02:06,380
好,我们再来看一下,l range,大家看到没有,30,45

59
00:02:06,380 --> 00:02:09,380
好,那假设我们需要在5前面去添一个50怎么去做

60
00:02:09,380 --> 00:02:15,380
l insert least 1 before,before谁,是before5,添加什么,50

61
00:02:15,380 --> 00:02:18,380
好,我们再来l range,3,34,55,对吧

62
00:02:18,380 --> 00:02:21,380
好,这里就是插入这样一个命令,我们再来看一下

63
00:02:21,380 --> 00:02:23,380
将元素从一个列表转到另一个列表

64
00:02:23,380 --> 00:02:25,380
rpop push,什么意思

65
00:02:25,380 --> 00:02:28,380
咱们光看名字是不是就可以知道它的功能了

66
00:02:28,380 --> 00:02:32,380
rpop,从右边pop,pop是什么,拿走,push呢,推入

67
00:02:32,380 --> 00:02:35,380
有点像咱们推陈出新的一个意思

68
00:02:35,380 --> 00:02:39,380
那么rpop和lpush是不是和咱们js里面的pop

69
00:02:39,380 --> 00:02:41,380
i-shaped有点像,先从右边弹出

70
00:02:41,380 --> 00:02:45,380
然后再从左边加入,这个过程是原子化的

71
00:02:45,380 --> 00:02:47,380
那么它的应用场景是什么

72
00:02:47,380 --> 00:02:51,380
其实咱们rpop和lpush的应用场景用于咱们的网站监控系统

73
00:02:51,380 --> 00:02:54,380
你比如说你要使用一个对列去存储需要监控的网站

74
00:02:54,380 --> 00:02:59,380
然后你的监控程序不断的通过rpop和lpush去循环的提出一个网址来测试可用性

75
00:02:59,380 --> 00:03:02,380
那么你在使用这样一个命令的时候

76
00:03:02,380 --> 00:03:06,380
程序在执行过程中仍然可以不断的向网址列表中去加入新的网址

77
00:03:06,380 --> 00:03:09,380
而整个系统也容易扩展 允许多个客户同时去处理对列

78
00:03:09,380 --> 00:03:13,380
这里就是rpop和lpush它的一个使用场景

79
00:03:13,380 --> 00:03:17,380
我们就来演示一下rpop和lpush的一个命令

80
00:03:17,380 --> 00:03:21,680
首先,我们通过lrang来看一下我们的list1还有剩哪些元素

81
00:03:21,680 --> 00:03:23,980
0-1,它还剩3,34,50

82
00:03:23,980 --> 00:03:26,280
那么我们再来看一下lrang和list

83
00:03:26,280 --> 00:03:27,780
比如说我们假设list11

84
00:03:27,780 --> 00:03:29,980
lrang command

85
00:03:29,980 --> 00:03:33,180
我们没有输入index,emptylist or set

86
00:03:33,180 --> 00:03:35,380
大家看到没有,我们的list1它有4个元素

87
00:03:35,380 --> 00:03:38,180
那么我们的list11它是空的emptylist

88
00:03:38,180 --> 00:03:41,180
那么我们就来看一下rpop,l push

89
00:03:41,180 --> 00:03:44,380
第一个传入list1,第二个传入list11

90
00:03:44,380 --> 00:03:46,780
那么list1是什么,是不是rpop呀

91
00:03:46,780 --> 00:03:49,860
也就是说从least1的右边最右边去拿出一个值也就是50

92
00:03:49,860 --> 00:03:51,640
把它给lpush到谁

93
00:03:51,640 --> 00:03:52,920
是不是lpush到least11

94
00:03:52,920 --> 00:03:54,980
那么我们来看一下结果50

95
00:03:54,980 --> 00:03:57,780
咱们来执行一下lrungeleast1

96
00:03:57,780 --> 00:03:58,560
0

97
00:03:58,560 --> 00:03:59,840
-1

98
00:03:59,840 --> 00:04:02,140
334是把我们的50拿走了50去哪了

99
00:04:02,140 --> 00:04:04,700
是不是lpush到了咱们的least11

100
00:04:04,700 --> 00:04:07,780
lrungeleast110-1

101
00:04:07,780 --> 00:04:08,540
大家看到没有50

102
00:04:08,540 --> 00:04:11,620
这里就是rpop和lpush它的一个

103
00:04:11,620 --> 00:04:13,140
应用那我们来

104
00:04:13,140 --> 00:04:16,740
总结一下刚才我们是不是学习了

105
00:04:16,780 --> 00:04:18,980
咱们链表中的获取和设置链表

106
00:04:18,980 --> 00:04:20,100
怎么去

107
00:04:20,100 --> 00:04:21,780
是不是通过Lindex和Lset

108
00:04:21,780 --> 00:04:23,860
然后还学习了一个Ltrim

109
00:04:23,860 --> 00:04:27,320
它是不是有点类似DS里面的Splice和Splice

110
00:04:27,320 --> 00:04:29,940
也就是它会把咱们的速度给切出一部分来

111
00:04:29,940 --> 00:04:31,780
也就是说你比如说你长度维持的速度

112
00:04:31,780 --> 00:04:33,200
你把它保留五个

113
00:04:33,200 --> 00:04:34,200
三个或者四个

114
00:04:34,200 --> 00:04:35,320
那么Instant也就是插入

115
00:04:35,320 --> 00:04:36,640
它可以从前或者从后插入

116
00:04:36,640 --> 00:04:37,960
通过传入before和after

117
00:04:37,960 --> 00:04:40,000
那么Airpop和Airpop和Airpush

118
00:04:40,000 --> 00:04:43,000
首先左边的pop右边的push

119
00:04:43,000 --> 00:04:45,020
它的一个应用场景

120
00:04:45,020 --> 00:04:46,560
可以用到咱们网站的监控一线

121
00:04:46,560 --> 00:04:48,540
你需要去进行咱们一些速度轮巡的

122
00:04:48,540 --> 00:04:50,280
一些用场景你都可以去考虑用它

123
00:04:50,280 --> 00:04:52,360
好 这里呢就是我们这一节课的内容

