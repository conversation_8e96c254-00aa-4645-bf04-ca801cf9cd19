1
00:00:00,000 --> 00:00:02,580
好 这节课我们就来看一下service的一个提取

2
00:00:02,580 --> 00:00:04,360
那么我们为什么要去提取service呢

3
00:00:04,360 --> 00:00:06,000
这里呢可能我们需要去看一下文档

4
00:00:06,000 --> 00:00:07,540
为什么要看文档呢

5
00:00:07,540 --> 00:00:10,120
因为我们得有理有据 实处有名

6
00:00:10,120 --> 00:00:13,540
好 这里呢其实我们首先来看一下contruler它的一个介绍

7
00:00:13,540 --> 00:00:16,440
那么其实呢在官网里面它推荐我们contruler里面

8
00:00:16,440 --> 00:00:17,200
主要去做四件事情

9
00:00:17,200 --> 00:00:19,920
第一个 获取用户通过http传递过来的请求参数

10
00:00:19,920 --> 00:00:22,760
那么我们在做创建数据和更新数据的时候

11
00:00:22,760 --> 00:00:24,020
是不是也获取过咱们的一个请求参数

12
00:00:24,020 --> 00:00:25,220
所以说我们使用正确

13
00:00:25,220 --> 00:00:26,880
包括呢 校验和组装参数

14
00:00:26,880 --> 00:00:27,900
我们是不是也进行了一个校验

15
00:00:27,900 --> 00:00:29,620
第三个的调用service进行业务处理

16
00:00:29,620 --> 00:00:31,880
必要是转换service的一个返回结果

17
00:00:31,880 --> 00:00:32,860
让它去适应用户的需求

18
00:00:32,860 --> 00:00:34,080
我们是不是没有去调用service

19
00:00:34,080 --> 00:00:36,920
第四个通过http将结果响应给用户

20
00:00:36,920 --> 00:00:37,880
是不是就是咱们一个

21
00:00:37,880 --> 00:00:40,000
d16.6和d16.4我们去抽象的一层

22
00:00:40,000 --> 00:00:42,320
那么其实我们唯独是不是缺service

23
00:00:42,320 --> 00:00:44,620
那么service它到底是做什么用的呢

24
00:00:44,620 --> 00:00:45,820
我们再来看一下service

25
00:00:45,820 --> 00:00:48,100
那么简单来说

26
00:00:48,100 --> 00:00:50,280
service就是在复杂的业务场景下

27
00:00:50,280 --> 00:00:52,100
用于做业务逻辑封装的一个抽象层

28
00:00:52,100 --> 00:00:53,940
那么这么有以下几个好处

29
00:00:53,940 --> 00:00:54,880
好好处就不看了

30
00:00:55,640 --> 00:00:57,180
我们再来看一下它的一个使用场景

31
00:00:57,180 --> 00:01:00,760
比如说展现的信息需要从数据库获取

32
00:01:00,760 --> 00:01:02,300
好其实我们看到这里够了

33
00:01:02,300 --> 00:01:05,120
核心是什么service是不是进行咱们数据库的一个操作

34
00:01:05,120 --> 00:01:06,660
包括了去组装一些数据

35
00:01:06,660 --> 00:01:07,940
所以说我们到这里就OK了

36
00:01:07,940 --> 00:01:10,500
那我们就来进行service的一个编写

37
00:01:10,500 --> 00:01:14,080
好那么在进入service的编写之前

38
00:01:14,080 --> 00:01:15,620
我们是不是要创建

39
00:01:15,620 --> 00:01:17,400
创建一个service像一个文件夹呀

40
00:01:17,400 --> 00:01:18,940
service

41
00:01:18,940 --> 00:01:21,240
好那么里面呢同样的咱们创建一个js

42
00:01:21,240 --> 00:01:22,280
是不是叫做post字典

43
00:01:23,560 --> 00:01:24,800
那么service怎么去写呢

44
00:01:24,800 --> 00:01:29,000
其实service和咱们控制器啊

45
00:01:29,000 --> 00:01:32,460
包括呢 其他的一些 egg的类似对象非常的相似

46
00:01:32,460 --> 00:01:34,800
比如说我们去抗执的一个service等于什么呢

47
00:01:34,800 --> 00:01:38,800
require egg.service

48
00:01:38,800 --> 00:01:41,260
这里咱们是直接获取了egg里面的一个service这样一个类

49
00:01:41,260 --> 00:01:42,660
那么我们通过一个class

50
00:01:42,660 --> 00:01:44,620
比如说我们要定一个post service

51
00:01:44,620 --> 00:01:46,920
咱们是不是通过去extend它

52
00:01:46,920 --> 00:01:47,920
对吧

53
00:01:47,920 --> 00:01:49,600
extend

54
00:01:49,600 --> 00:01:51,220
然后去继承这样的一个service

55
00:01:51,220 --> 00:01:53,460
咱们就可以去实现了我们这样一个

56
00:01:53,460 --> 00:01:57,300
逻辑 然后刚才我们是不是讲到在service里面我们主要去做什么呀

57
00:01:57,300 --> 00:01:59,860
就是增3改查呀

58
00:01:59,860 --> 00:02:02,160
好 那么呢我们

59
00:02:02,160 --> 00:02:05,240
我们呢就从我们呢 首先呢把咱们的一个

60
00:02:05,240 --> 00:02:09,080
controller给拿过来 我们来看一下有哪些地方咱们需要去改造

61
00:02:09,080 --> 00:02:12,400
首先我们来看一下我们的create逻辑 也就是我们的创建

62
00:02:12,400 --> 00:02:14,460
那么呢刚才我们是不是可以看到

63
00:02:14,460 --> 00:02:17,780
刚才我们是不是可以看到咱们的controller一般使用

64
00:02:17,780 --> 00:02:20,340
怎么去写 是不是这四个步骤 controller里面

65
00:02:20,340 --> 00:02:21,880
好 我们把它给粘过来

66
00:02:23,160 --> 00:02:24,220
咱们把它给张过来

67
00:02:24,220 --> 00:02:28,420
好 我们来看一下

68
00:02:28,420 --> 00:02:30,440
首先呢

69
00:02:30,440 --> 00:02:32,160
这一行去获取我们的

70
00:02:32,160 --> 00:02:33,260
是不是获取我们的一个

71
00:02:33,260 --> 00:02:34,640
request的一些请求信息啊

72
00:02:34,640 --> 00:02:35,920
是不是符合我们controller的逻辑

73
00:02:35,920 --> 00:02:37,440
包括了validate去教育数据

74
00:02:37,440 --> 00:02:38,740
也属于我们controller的逻辑

75
00:02:38,740 --> 00:02:39,520
那么唯一

76
00:02:39,520 --> 00:02:41,840
我们需要去抽象到service的是哪一部分呢

77
00:02:41,840 --> 00:02:43,120
是不是我们操作model啊

78
00:02:43,120 --> 00:02:44,480
也就是咱们操作数据库的内容

79
00:02:44,480 --> 00:02:45,360
咱们需要把它呢

80
00:02:45,360 --> 00:02:48,340
是不是要把它给抽象到我们的一个service里面去啊

81
00:02:48,340 --> 00:02:48,860
所以说呢

82
00:02:48,860 --> 00:02:50,280
咱们首先来完成我们的一个

83
00:02:50,280 --> 00:02:51,240
什么操作呢

84
00:02:51,240 --> 00:02:53,000
完成我们的一个新增操作

85
00:02:53,000 --> 00:02:57,360
那么新增操作同样的 我们去给他命名叫做什么呢

86
00:02:57,360 --> 00:03:03,240
那么create需要接受参数呢 肯定需要吧 为什么呀

87
00:03:03,240 --> 00:03:08,100
我们因为这里呢 我们service是不是自己相对去封占一个函数 所以说我们去给他设计一个参数

88
00:03:08,100 --> 00:03:09,120
 他需要接收一个

89
00:03:09,120 --> 00:03:11,680
什么呢 是不是接受一个data 他的作用是什么

90
00:03:11,680 --> 00:03:15,520
因为你要去存储数据嘛 你肯定有一个数据员 对吧

91
00:03:15,520 --> 00:03:22,440
好 我们create呢 假设他去接受一个date 这个date是谁呢 这个date是不是就是咱们客户端所传过来的title和content

92
00:03:22,440 --> 00:03:24,440
所以说呢 我们去封装的时候呢

93
00:03:24,440 --> 00:03:28,440
你比如说 我们在这里是不是去抗死一个

94
00:03:28,440 --> 00:03:31,440
比如说呢 我们在这里直接把它给copy回来

95
00:03:31,440 --> 00:03:35,440
因为我们所有数据过的操作是不是都要抽象到我们service里面去啊 对吧

96
00:03:35,440 --> 00:03:39,440
好 所以说呢 我们首先咱们在service里面去

97
00:03:39,440 --> 00:03:41,440
留一个咱们的model 这样一个实力

98
00:03:41,440 --> 00:03:44,440
那么我们唯一需要修改的 是不是咱们title和content它从哪里来呀

99
00:03:44,440 --> 00:03:46,440
是不是咱们从外面所传递过来

100
00:03:46,440 --> 00:03:48,440
所以说呢 把它改为data

101
00:03:48,440 --> 00:03:50,440
data.title和data.content

102
00:03:50,440 --> 00:03:52,800
content 然后呢咱们再去执行什么操作

103
00:03:52,800 --> 00:03:54,440
就是执行咱们的一个save操作

104
00:03:54,440 --> 00:03:58,680
那么save操作呢是不是也需要在我们的一个service里面去完成了

105
00:03:58,680 --> 00:04:01,640
对吧 因为它呢是不是也属于我们数据库的一个操作

106
00:04:01,640 --> 00:04:03,640
好 那么我们把注释也张过来

107
00:04:03,640 --> 00:04:06,440
好 注释也给同学们给张过来

108
00:04:06,440 --> 00:04:09,640
好 那么我们完成的save操作是不是就结束了我们create这样的一个

109
00:04:09,640 --> 00:04:14,040
它的一个指指 咱们把它的一个readbounce把它给retend一下

110
00:04:14,040 --> 00:04:17,440
好 那么这里呢其实我们就已经完成了咱们第一个方法的编写

111
00:04:17,440 --> 00:04:20,140
那么咱们来先把我们的Service给暴露一下

112
00:04:20,140 --> 00:04:23,580
module.export等于咱们的post service

113
00:04:23,580 --> 00:04:26,560
我们把它给暴露一下

114
00:04:26,560 --> 00:04:28,060
好

115
00:04:28,060 --> 00:04:30,540
那么这里呢就完成了我们一个create它的编写

116
00:04:30,540 --> 00:04:34,860
那么我们这里是不是需要把这些代码全部都给拴掉

117
00:04:34,860 --> 00:04:35,700
那么怎么改呢

118
00:04:35,700 --> 00:04:38,420
我们这里呢教演参数通过之后

119
00:04:38,420 --> 00:04:39,980
咱们是不是要通过去调用我们的一个Service

120
00:04:39,980 --> 00:04:42,420
比如说咱们的avate context的点

121
00:04:42,420 --> 00:04:45,320
那么Service在咱们的context下面其实也有这样一个实力

122
00:04:45,320 --> 00:04:47,260
我们去调用Service的什么方法呢

123
00:04:47,260 --> 00:04:48,540
是不是post对吧

124
00:04:48,540 --> 00:04:49,020
为什么呀

125
00:04:49,020 --> 00:04:50,060
因为我们是不是在service里面

126
00:04:50,060 --> 00:04:50,960
去创建一个post字点

127
00:04:50,960 --> 00:04:52,300
gs啊

128
00:04:52,300 --> 00:04:53,220
所以我们去创建

129
00:04:53,220 --> 00:04:54,700
所以我们去调语post字点

130
00:04:54,700 --> 00:04:55,080
什么方法

131
00:04:55,080 --> 00:04:55,460
create

132
00:04:55,460 --> 00:04:56,740
然后传入

133
00:04:56,740 --> 00:04:58,620
传入我们一个request

134
00:04:58,620 --> 00:04:59,640
request body

135
00:04:59,640 --> 00:05:01,780
这样就可以完成我们一个创建操作

136
00:05:01,780 --> 00:05:02,680
这里呢

137
00:05:02,680 --> 00:05:04,000
其实就实现了我们的第一个抽象

138
00:05:04,000 --> 00:05:05,080
那么我就来试一下

139
00:05:05,080 --> 00:05:05,860
有没有问题

140
00:05:05,860 --> 00:05:06,960
能不能行

141
00:05:06,960 --> 00:05:09,060
有毛病没毛病

142
00:05:09,060 --> 00:05:11,580
比如说我们post

143
00:05:11,580 --> 00:05:15,080
然后来给它一个content

144
00:05:15,080 --> 00:05:18,080
好

145
00:05:18,080 --> 00:05:19,420
咱们把它给改的显眼一点

146
00:05:19,420 --> 00:05:20,720
剩的

147
00:05:20,720 --> 00:05:21,040
整理

148
00:05:21,040 --> 00:05:21,680
好

149
00:05:21,680 --> 00:05:22,140
大家可以看到

150
00:05:22,140 --> 00:05:22,740
报错了

151
00:05:22,740 --> 00:05:24,360
那么我们来看一下哪里报错了

152
00:05:24,360 --> 00:05:25,740
context is not defined

153
00:05:25,740 --> 00:05:26,620
好

154
00:05:26,620 --> 00:05:27,120
我们来看一下

155
00:05:27,120 --> 00:05:27,820
到底哪行处错

156
00:05:27,820 --> 00:05:28,700
我们直接来看调文章

157
00:05:28,700 --> 00:05:29,400
好

158
00:05:29,400 --> 00:05:29,900
大家可以看到

159
00:05:29,900 --> 00:05:31,220
我们留了一个context

160
00:05:31,220 --> 00:05:31,440
那么

161
00:05:31,440 --> 00:05:33,500
只是这个context从哪里来呀

162
00:05:33,500 --> 00:05:34,200
是不是没有

163
00:05:34,200 --> 00:05:34,680
所以说呢

164
00:05:34,680 --> 00:05:35,380
我们需要去

165
00:05:35,380 --> 00:05:36,420
是不是调一个this

166
00:05:36,420 --> 00:05:36,880
对吧

167
00:05:36,880 --> 00:05:40,020
好

168
00:05:40,020 --> 00:05:40,880
那么我们再来看一下

169
00:05:40,880 --> 00:05:41,580
行不行

170
00:05:41,580 --> 00:05:43,480
整理

171
00:05:43,480 --> 00:05:44,160
好

172
00:05:44,160 --> 00:05:44,740
还是错误

173
00:05:44,740 --> 00:05:48,740
好 大家可以看到 res is not defined

174
00:05:48,740 --> 00:05:53,740
其实我们这里可以看到 res is not defined

175
00:05:53,740 --> 00:05:54,740
我们来看叫调用战

176
00:05:54,740 --> 00:05:58,740
大家其实可以看到我们的success里面是不是调用一个res方法

177
00:05:58,740 --> 00:05:59,740
那么以前有个res 现在没有了

178
00:05:59,740 --> 00:06:01,740
其实我们的service里面是不是返回了一个res

179
00:06:01,740 --> 00:06:03,740
所以说我们需要呢

180
00:06:03,740 --> 00:06:04,740
去在这里去接收一下

181
00:06:04,740 --> 00:06:06,740
比如说我们cons的res等于他

182
00:06:06,740 --> 00:06:07,740
好 这样其实就可以了

183
00:06:07,740 --> 00:06:08,740
那我们再来试一下

184
00:06:08,740 --> 00:06:10,740
得理

185
00:06:10,740 --> 00:06:13,740
好 其实我们现在还是发生了错误

186
00:06:14,740 --> 00:06:17,920
其实我们现在报了一个错误是什么呀

187
00:06:17,920 --> 00:06:19,920
看了一个这样一个错误嘛

188
00:06:19,920 --> 00:06:20,860
其实我就可以知道

189
00:06:20,860 --> 00:06:22,220
它呢有一个k重复了

190
00:06:22,220 --> 00:06:23,540
说明了我们添加了一个重复的title

191
00:06:23,540 --> 00:06:24,500
那么为什么呢

192
00:06:24,500 --> 00:06:25,300
我们来看一下原因

193
00:06:25,300 --> 00:06:27,620
我们刚才发生错误的是哪一行代码

194
00:06:27,620 --> 00:06:29,520
是不是success这里res我忘了去写

195
00:06:29,520 --> 00:06:31,520
但是前面它是不是有成功的执行呢

196
00:06:31,520 --> 00:06:32,100
说明了什么问题

197
00:06:32,100 --> 00:06:34,380
是不是说明我们的数据其实已经存进去了

198
00:06:34,380 --> 00:06:36,700
只是说我们返回给用户的一个response出现一些问题

199
00:06:36,700 --> 00:06:39,100
所以说我们的数据实际上是存储进去了

200
00:06:39,100 --> 00:06:40,360
我们不信不信同意来看一下税库

201
00:06:40,360 --> 00:06:41,180
是不是已经存进去了

202
00:06:41,180 --> 00:06:43,600
所以说呢我们这里来再来修改一下咱们一个title

203
00:06:43,600 --> 00:06:46,560
比如说我们去改一个x

204
00:06:46,560 --> 00:06:47,800
好 大家可以看到

205
00:06:47,800 --> 00:06:50,640
我们现在是不是已经完成了我们的一个创建操作

206
00:06:50,640 --> 00:06:53,560
好 那么我们继续来继续改造

207
00:06:53,560 --> 00:06:57,200
我们刚才是不是已经改造完成了我们的一个形成操作

208
00:06:57,200 --> 00:06:58,080
那么接下来呢

209
00:06:58,080 --> 00:06:59,400
我们就来改造我们的一个什么呢

210
00:06:59,400 --> 00:07:01,600
比如说我们去改造我们的一个查询

211
00:07:01,600 --> 00:07:03,200
那么查询的话

212
00:07:03,200 --> 00:07:07,120
是不是咱们比如说我们去定一个方法

213
00:07:07,120 --> 00:07:08,000
叫做find

214
00:07:08,000 --> 00:07:09,620
但其实也是非常的好理解

215
00:07:09,620 --> 00:07:10,580
那么find的话

216
00:07:10,580 --> 00:07:12,140
它是不是需要接收一个参数

217
00:07:12,140 --> 00:07:16,740
这个是什么参数了是不是查询的条件了对吧

218
00:07:16,740 --> 00:07:18,020
你比如说你可以传一个对象

219
00:07:18,020 --> 00:07:20,080
那什么是查询条件呢是不是咱们传一个对象

220
00:07:20,080 --> 00:07:22,120
要么是一个空的要么是带有一个id对吧

221
00:07:22,120 --> 00:07:25,700
所以呢这里呢咱们还是去给他一个比如说咱们给他一个参数叫做data

222
00:07:25,700 --> 00:07:26,980
好那么我们呢

223
00:07:26,980 --> 00:07:30,580
find呢其实这里呢就非常简单了比如说我们去看这个res等于什么呢

224
00:07:30,580 --> 00:07:31,600
我们去await

225
00:07:31,600 --> 00:07:35,440
this.context.model.post

226
00:07:35,440 --> 00:07:40,300
调用什么方法呢fine是不是调用他的一个find的方法然后呢我们把查询条件给传入

227
00:07:41,580 --> 00:07:43,240
然后直接去把它给return

228
00:07:43,240 --> 00:07:48,780
好 那么我们再来改造一下我们的一个find它的操作

229
00:07:48,780 --> 00:07:51,740
那么index里面是不是有使用到一个find

230
00:07:51,740 --> 00:07:57,560
所以说呢 我们这里直接把它改为avatecontext点service

231
00:07:57,560 --> 00:07:59,820
把它给个小写的post上 对吧

232
00:07:59,820 --> 00:08:04,860
好 那么这里呢 其实就已经完成了我们查询的一个改造

233
00:08:04,860 --> 00:08:05,900
那么是不是还剩下一个单个

234
00:08:05,900 --> 00:08:07,420
那么单个呢 也是一样的

235
00:08:07,420 --> 00:08:11,560
我们把这里呢 改为咱们的一个service点post

236
00:08:11,560 --> 00:08:12,200
就可以了

237
00:08:12,200 --> 00:08:13,100
好

238
00:08:13,100 --> 00:08:15,160
那么我们就来试一下效果

239
00:08:15,160 --> 00:08:22,280
好

240
00:08:22,280 --> 00:08:24,080
因为我们的id刚才不存在

241
00:08:24,080 --> 00:08:25,800
我们首先来获取一下全部的数据

242
00:08:25,800 --> 00:08:27,420
大家可以看到我们是不是获取了全部的数据

243
00:08:27,420 --> 00:08:28,720
那么获取完成之后

244
00:08:28,720 --> 00:08:29,900
我们来试一下单个数据行不行

245
00:08:29,900 --> 00:08:32,220
比如说我们来查一下title为xrx的这条

246
00:08:32,220 --> 00:08:34,320
好

247
00:08:34,320 --> 00:08:36,260
大家可以看到我们的查询是不是也已经改造成功了

248
00:08:36,260 --> 00:08:36,440
好

249
00:08:36,440 --> 00:08:36,940
那我们继续

250
00:08:36,940 --> 00:08:38,880
那么我们既然改造我们的find

251
00:08:38,880 --> 00:08:42,160
那么我们就来看一下删除

252
00:08:42,160 --> 00:08:43,740
那么删除的话

253
00:08:43,740 --> 00:08:46,120
我们取一个名字叫做async remove

254
00:08:46,120 --> 00:08:48,580
那么删除它需要接受什么参数呢

255
00:08:48,580 --> 00:08:50,160
是不是需要去接受一个

256
00:08:50,160 --> 00:08:51,720
也是接受一个查询条件了

257
00:08:51,720 --> 00:08:52,860
所以说我们也叫它一个 data

258
00:08:52,860 --> 00:08:54,420
它其实和我们的find是一样的

259
00:08:54,420 --> 00:08:55,720
我们去给它接受一个查询条件

260
00:08:55,720 --> 00:08:56,960
那么我们remove

261
00:08:56,960 --> 00:08:58,880
是不是和我们的find是非常像的

262
00:08:58,880 --> 00:09:00,520
我们照样可以去直接copy

263
00:09:00,520 --> 00:09:01,800
比如说我们去有一个res

264
00:09:01,800 --> 00:09:03,920
去接受一个例行contest的model.pose

265
00:09:03,920 --> 00:09:04,820
什么样是不是

266
00:09:04,820 --> 00:09:07,000
我们去调用model的remove方法就可以了

267
00:09:07,000 --> 00:09:09,820
那么这里其实就是我们的一个酸处操作

268
00:09:09,820 --> 00:09:10,800
那么我们service

269
00:09:10,800 --> 00:09:12,160
我们的service封装之后

270
00:09:12,160 --> 00:09:14,220
是不是在咱们的controller里面也需要去修改一次

271
00:09:14,220 --> 00:09:15,320
咱们找到什么呀

272
00:09:15,320 --> 00:09:16,000
是不是找到disjoy

273
00:09:16,000 --> 00:09:17,800
那么disjoy呢

274
00:09:17,800 --> 00:09:19,740
这里咱们同样的把它改为contest

275
00:09:19,740 --> 00:09:22,400
是不是contest.service.post

276
00:09:22,400 --> 00:09:23,560
然后呢

277
00:09:23,560 --> 00:09:24,560
调用它的一个remove方法

278
00:09:24,560 --> 00:09:26,300
咱们就可以实现我们的一个酸处操作呀

279
00:09:26,300 --> 00:09:26,660
好

280
00:09:26,660 --> 00:09:27,080
那么这里呢

281
00:09:27,080 --> 00:09:28,260
其实也已经改造完成了

282
00:09:28,260 --> 00:09:29,680
那我们就来试一下效果

283
00:09:29,680 --> 00:09:31,800
首先呢

284
00:09:31,800 --> 00:09:32,520
我们来找一条数据

285
00:09:32,520 --> 00:09:33,700
那么我们就来

286
00:09:33,700 --> 00:09:36,920
比如说我们就来双xx这一条吧

287
00:09:36,920 --> 00:09:37,680
我们来把它给拴掉

288
00:09:37,680 --> 00:09:41,020
那么呢我们就来调用一个

289
00:09:41,020 --> 00:09:42,560
delete的方法

290
00:09:42,560 --> 00:09:45,120
好大家可以看到delete的抗扫唯一说明我们已经给拴掉了

291
00:09:45,120 --> 00:09:47,680
那么我们再来验证一下大家可以看到是不是已经没了

292
00:09:47,680 --> 00:09:48,180
好

293
00:09:48,180 --> 00:09:49,980
那么我们呢拴除操作呢也已经给

294
00:09:49,980 --> 00:09:52,540
改造成功了那么最后剩下的就是什么呢update

295
00:09:52,540 --> 00:09:53,560
好

296
00:09:53,560 --> 00:09:54,840
那么我们就来看一下update

297
00:09:54,840 --> 00:09:57,920
我们同样的去给他定一个方法叫做update

298
00:09:57,920 --> 00:09:59,200
update他接受几个参数啊

299
00:09:59,200 --> 00:10:01,500
第一个就是查询条件

300
00:10:01,500 --> 00:10:04,560
第二个呢就是我们的一个

301
00:10:04,560 --> 00:10:06,100
修改的数据啊

302
00:10:06,920 --> 00:10:11,800
那么update怎么去写呢

303
00:10:11,800 --> 00:10:13,740
update其实也非常的简单

304
00:10:13,740 --> 00:10:15,800
比如说它和remove其实也非常的相似

305
00:10:15,800 --> 00:10:17,920
你比如说我们去调用model的一个

306
00:10:17,920 --> 00:10:19,300
是不是调用它的update方法

307
00:10:19,300 --> 00:10:20,880
然后它是不是需要去交参两个参数

308
00:10:20,880 --> 00:10:22,920
首先第一个是不是查询条件

309
00:10:22,920 --> 00:10:26,160
比如说叫做find data

310
00:10:26,160 --> 00:10:27,280
第二个就是我们的一个

311
00:10:27,280 --> 00:10:28,680
是不是咱们修改的一个数据源

312
00:10:28,680 --> 00:10:29,840
假如我们就叫data

313
00:10:29,840 --> 00:10:34,140
那么我们就去给它去传入两条参数

314
00:10:34,140 --> 00:10:35,800
第二个就是我们的一个

315
00:10:36,920 --> 00:10:37,340
data

316
00:10:37,340 --> 00:10:43,740
好 那么我们其实也已经完成了serviceupdate的一个修改

317
00:10:43,740 --> 00:10:46,980
那么这里呢我们就可以进入我们的controller去看一下我们的update怎么去改

318
00:10:46,980 --> 00:10:53,180
其实这里呢也是一样的 我们去调用context.service.post

319
00:10:53,180 --> 00:10:54,040
然后调用它的update

320
00:10:54,040 --> 00:10:56,700
那么这样呢其实我们也已经给它改造完成了

321
00:10:56,700 --> 00:10:58,660
那么我们就来试一下效果 是不是怎么回事

322
00:10:58,660 --> 00:11:03,840
好 那比如说啊

323
00:11:03,840 --> 00:11:06,240
比如说呢我们来修改title为等于号的这条数据

324
00:11:06,240 --> 00:11:08,420
我们把它title改为别的名称

325
00:11:08,420 --> 00:11:11,540
好 我们来执行一个put操作

326
00:11:11,540 --> 00:11:13,180
创作ID

327
00:11:13,180 --> 00:11:14,960
好 我们来修改它的一个title

328
00:11:14,960 --> 00:11:17,340
比如说我们给它加入一些中华线

329
00:11:17,340 --> 00:11:17,960
圣等

330
00:11:17,960 --> 00:11:20,720
好 大家可以看到我们这里呢 出错了

331
00:11:20,720 --> 00:11:23,500
好 我们来看一下问题出在哪里

332
00:11:23,500 --> 00:11:25,140
大家说得很清楚

333
00:11:25,140 --> 00:11:26,500
Problems passing 节省

334
00:11:26,500 --> 00:11:27,760
说明我们的节省数据结构有问题

335
00:11:27,760 --> 00:11:29,000
大家可以看到这里是不是多了一个逗号

336
00:11:29,000 --> 00:11:30,380
好 那么我们来重新圣等一下

337
00:11:30,380 --> 00:11:31,660
好 还是失败了

338
00:11:31,660 --> 00:11:32,820
我们来看一下错误出在哪里

339
00:11:36,240 --> 00:11:40,600
这里可以看到contact.service.post.update is not function

340
00:11:40,600 --> 00:11:42,900
那我们来看一下是不是update给拼错了

341
00:11:42,900 --> 00:11:47,760
我们来找到我们的一个

342
00:11:47,760 --> 00:11:49,560
update

343
00:11:49,560 --> 00:11:52,620
好像没拼错我们再来试一下

344
00:11:52,620 --> 00:11:53,900
剩的

345
00:11:53,900 --> 00:11:59,020
是出在我们刚才没有Ctrl+S去保存我们的代码

346
00:11:59,020 --> 00:12:00,560
所以说导致更新没有生效

347
00:12:00,560 --> 00:12:01,840
所以我们来重新试的一下

348
00:12:01,840 --> 00:12:03,880
大家可以看到现在其实我们代码就已经生效了

349
00:12:04,400 --> 00:12:06,100
那么我们来看一下数据

350
00:12:06,100 --> 00:12:09,460
大家可以看到我们是不是已经修改成功了

351
00:12:09,460 --> 00:12:11,760
那么这里就是我们service的一个封装

352
00:12:11,760 --> 00:12:13,740
那么我们就来总结一下这几个内容

353
00:12:13,740 --> 00:12:15,520
那么首先我们什么情况下

354
00:12:15,520 --> 00:12:16,580
需要去抽象我们service呢

355
00:12:16,580 --> 00:12:18,500
是不是我们的controller需要去做什么呀

356
00:12:18,500 --> 00:12:20,480
是不是我们的controller需要去做一些业务处理

357
00:12:20,480 --> 00:12:21,460
那么业务处理是什么呢

358
00:12:21,460 --> 00:12:22,620
是不是就是操作我们的数据库

359
00:12:22,620 --> 00:12:23,960
那么怎么样去抽象呢

360
00:12:23,960 --> 00:12:25,640
其实我们的service核心就是四方块

361
00:12:25,640 --> 00:12:26,760
也就是一个增商

362
00:12:26,760 --> 00:12:28,640
包括我们数据的一些组装

363
00:12:28,640 --> 00:12:30,100
那么有的同学可能会问了

364
00:12:30,100 --> 00:12:31,680
老师你这样不是多此一局吗

365
00:12:31,680 --> 00:12:32,160
对吧

366
00:12:32,160 --> 00:12:33,200
因为我们service的逻辑

367
00:12:33,200 --> 00:12:34,720
好像并没有帮助我们去减少多少袋

368
00:12:34,720 --> 00:12:36,700
那么现在来看是这样的

369
00:12:36,700 --> 00:12:38,460
但是如果我们项目的一个长期发展

370
00:12:38,460 --> 00:12:39,940
我们的数据会越来越复杂

371
00:12:39,940 --> 00:12:42,120
因为我们现在还只涉及文章

372
00:12:42,120 --> 00:12:43,120
那么如果说我们

373
00:12:43,120 --> 00:12:44,400
比如说到时候加入一些用户

374
00:12:44,400 --> 00:12:45,240
加入一些权限

375
00:12:45,240 --> 00:12:46,660
那么用户权限和文章之间

376
00:12:46,660 --> 00:12:48,020
有一些错综复杂的关系的时候

377
00:12:48,020 --> 00:12:50,040
那么此时的service就会非常的有用了

