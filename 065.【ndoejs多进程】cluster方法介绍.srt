1
00:00:00,520 --> 00:00:01,800
好 接下来我们来看一下

2
00:00:01,800 --> 00:00:03,840
cluster里面的一些事件是怎么一回事

3
00:00:03,840 --> 00:00:05,640
首先我们来看第一个fork

4
00:00:05,640 --> 00:00:06,920
那么我们直接来看文档

5
00:00:06,920 --> 00:00:09,720
fork事件

6
00:00:09,720 --> 00:00:12,040
当新的工作进程被fork时

7
00:00:12,040 --> 00:00:14,340
cluster模块将触发fork事件可以用来记录

8
00:00:14,340 --> 00:00:17,160
工作进程的活动产生一个自定义的timeout

9
00:00:17,160 --> 00:00:18,440
什么意思

10
00:00:18,440 --> 00:00:24,580
我们直接来看一下fork事件是怎么触发的

11
00:00:24,580 --> 00:00:29,960
首先fork的定义咱把它粘贴一下

12
00:00:30,000 --> 00:00:36,680
当新的工作进程被forks cluster模块将束发fork事件

13
00:00:36,680 --> 00:00:37,940
可以用来记录工作进程的活动

14
00:00:37,940 --> 00:00:44,160
好 那么咱们就是cluster.fork.on

15
00:00:44,160 --> 00:00:46,060
对吧 咱们监听的是fork事件

16
00:00:46,060 --> 00:00:49,160
看一下他接受哪两个参数

17
00:00:49,160 --> 00:00:54,620
他接受一个walker

18
00:00:54,620 --> 00:00:59,040
walker是不是就是咱们的整形成的process对象

19
00:00:59,040 --> 00:01:01,040
我可

20
00:01:01,040 --> 00:01:03,040
counsel.log

21
00:01:03,040 --> 00:01:07,040
主进程

22
00:01:07,040 --> 00:01:09,040
fork了一个

23
00:01:09,040 --> 00:01:11,040
walker

24
00:01:11,040 --> 00:01:13,040
pid 咱们把它的 pid 打印出来看一下

25
00:01:13,040 --> 00:01:17,040
walker.pid

26
00:01:17,040 --> 00:01:21,040
好 大家看到没有

27
00:01:21,040 --> 00:01:23,040
pid 为 undefined

28
00:01:23,040 --> 00:01:27,040
好 那我们来打个断点

29
00:01:27,040 --> 00:01:28,160
打个段点看一下

30
00:01:28,160 --> 00:01:30,460
看一下Walker它到底是什么

31
00:01:30,460 --> 00:01:34,360
小虫子

32
00:01:34,360 --> 00:01:35,020
走

33
00:01:35,020 --> 00:01:38,680
咱们是不是没有调lunc.js呢

34
00:01:38,680 --> 00:01:40,100
chapter

35
00:01:40,100 --> 00:01:42,160
cluster

36
00:01:42,160 --> 00:01:46,000
main.js

37
00:01:46,000 --> 00:01:47,740
走

38
00:01:47,740 --> 00:01:49,540
咱们来看一下Walker是什么

39
00:01:49,540 --> 00:01:51,340
Walker是一个对象

40
00:01:51,340 --> 00:01:53,620
Walker下面是不是有个process属性

41
00:01:53,620 --> 00:01:54,880
那咱们扫写了

42
00:01:54,880 --> 00:01:55,680
扫写了一个属性

43
00:01:55,680 --> 00:01:57,080
walker.process

44
00:01:57,080 --> 00:01:58,000
停掉

45
00:01:58,000 --> 00:02:00,600
点process.pld

46
00:02:00,600 --> 00:02:01,820
好 咱们再来看一下

47
00:02:01,820 --> 00:02:04,900
主线程fork的一个walker

48
00:02:04,900 --> 00:02:05,340
pld为

49
00:02:05,340 --> 00:02:06,080
这个

50
00:02:06,080 --> 00:02:08,040
好 这里呢就是cluster

51
00:02:08,040 --> 00:02:09,380
walk方法

52
00:02:09,380 --> 00:02:11,340
好 咱们再来看一下

53
00:02:11,340 --> 00:02:11,960
listening

54
00:02:11,960 --> 00:02:13,540
当一个工作进程

55
00:02:13,540 --> 00:02:14,800
调用listen后

56
00:02:14,800 --> 00:02:16,200
触发事件处理

57
00:02:16,200 --> 00:02:17,220
两个参数

58
00:02:17,220 --> 00:02:18,820
它呢接收两个参数

59
00:02:18,820 --> 00:02:20,580
walker和工作进程

60
00:02:20,580 --> 00:02:21,180
这一个对象

61
00:02:21,180 --> 00:02:23,280
什么呢就是walker和process

62
00:02:23,280 --> 00:02:24,180
那咱们来看一下

63
00:02:24,180 --> 00:02:26,760
listen的这样一个方法

64
00:02:26,760 --> 00:02:27,580
咱们直接来看我们当

65
00:02:27,580 --> 00:02:29,540
listen的事件

66
00:02:29,540 --> 00:02:31,760
好

67
00:02:31,760 --> 00:02:34,560
粘贴过来

68
00:02:34,560 --> 00:02:40,980
当一个工作进程调用listen后

69
00:02:40,980 --> 00:02:44,060
工作进程上的server会出发listen事件

70
00:02:44,060 --> 00:02:45,700
同时cluster也会出发listen事件

71
00:02:45,700 --> 00:02:48,320
其中work包含了工作进程对象address

72
00:02:48,320 --> 00:02:50,360
那么这些参数

73
00:02:50,360 --> 00:02:51,620
我们先不管这些参数有没有用

74
00:02:51,620 --> 00:02:52,300
咱们来看一下

75
00:02:52,300 --> 00:02:53,980
cluster.on

76
00:02:53,980 --> 00:02:54,580
先听

77
00:02:54,580 --> 00:02:57,260
Listening

78
00:02:57,260 --> 00:03:01,840
Walker

79
00:03:01,840 --> 00:03:03,100
咱们先不用管第二个参数

80
00:03:03,100 --> 00:03:03,880
第二个参数是address

81
00:03:03,880 --> 00:03:04,580
咱们暂时用不上

82
00:03:04,580 --> 00:03:05,700
我们同样的

83
00:03:05,700 --> 00:03:08,080
把它的PID给打印出来

84
00:03:08,080 --> 00:03:12,060
主线程Fork的一个Walker

85
00:03:12,060 --> 00:03:14,480
进行http服务

86
00:03:14,480 --> 00:03:14,920
好

87
00:03:14,920 --> 00:03:17,220
那么咱们是不是要进行http服务

88
00:03:17,220 --> 00:03:18,400
那么我们来copy一段代码

89
00:03:18,400 --> 00:03:20,320
这里就不要再去写了

90
00:03:20,320 --> 00:03:22,920
找一下我们之前创建的服务

91
00:03:22,920 --> 00:03:25,240
好 8001

92
00:03:25,240 --> 00:03:29,760
这里呢在子建成创建的时候

93
00:03:29,760 --> 00:03:30,660
咱们是不是create一个sever

94
00:03:30,660 --> 00:03:32,640
这里是不是还需要去引入一下http

95
00:03:32,640 --> 00:03:35,760
等于requirehttp

96
00:03:35,760 --> 00:03:43,740
好那我们就来看一下

97
00:03:43,740 --> 00:03:45,980
先把fork在一个事件注释免得干扰

98
00:03:45,980 --> 00:03:49,100
走大家看到没有

99
00:03:49,100 --> 00:03:51,420
主进程fork的一个worker进行http服务

100
00:03:51,420 --> 00:03:52,820
pid为5457

101
00:03:52,820 --> 00:03:55,020
那咱们把这一段注释

102
00:03:55,020 --> 00:03:57,580
是不是只有http的服务

103
00:03:57,580 --> 00:03:58,980
才能触发他的listening事件呢

104
00:03:58,980 --> 00:03:59,840
咱们随便打印一下

105
00:03:59,840 --> 00:04:00,800
看能不能触发他的listening

106
00:04:00,800 --> 00:04:02,540
触发不了吧

107
00:04:02,540 --> 00:04:03,080
这里呢

108
00:04:03,080 --> 00:04:03,840
咱们的应用场景

109
00:04:03,840 --> 00:04:05,500
就是比如说你想去检测哪些worker

110
00:04:05,500 --> 00:04:06,780
他在进行http服务的时候

111
00:04:06,780 --> 00:04:08,180
你就可以用listening这样一个方法

112
00:04:08,180 --> 00:04:08,680
好

113
00:04:08,680 --> 00:04:11,300
那我们就来看下一个message事件

114
00:04:11,300 --> 00:04:13,000
咱们怎么样去触发message

115
00:04:13,000 --> 00:04:17,280
好

116
00:04:17,280 --> 00:04:18,140
我们直接去看文档

117
00:04:18,140 --> 00:04:20,520
我们来看一下message事件

118
00:04:20,520 --> 00:04:25,380
当cluster主进程接受任意工作进程发生的消息后出发

119
00:04:25,380 --> 00:04:35,120
那么他是不是和咱们的process里面去chill的main通信是不是一样的呀

120
00:04:35,120 --> 00:04:40,320
咱们在chill的里面去send然后呢在main里面去onmessage这样一个方法去接收

121
00:04:40,320 --> 00:04:42,200
那么到底是不是这样的呢咱们就来看一下

122
00:04:42,200 --> 00:04:47,660
cluster main好咱们来监听一个cluster点on

123
00:04:47,660 --> 00:04:49,740
什么message

124
00:04:49,740 --> 00:04:59,120
data 咱们把它打印出来 看一下 console.log

125
00:04:59,120 --> 00:05:05,960
收到data 好 怎么要去发送呢 是不是process点剩的

126
00:05:05,960 --> 00:05:09,460
你好 好 我们来看一下 看咱们的cluster能不能收到

127
00:05:09,460 --> 00:05:13,720
走 收到

128
00:05:13,720 --> 00:05:17,460
大家看到没有 咱们是不是收到了

129
00:05:17,460 --> 00:05:19,240
咱们收到对吧

130
00:05:19,240 --> 00:05:20,540
那这样会不会有什么问题

131
00:05:20,540 --> 00:05:21,340
同学们

132
00:05:21,340 --> 00:05:22,160
比如说啊

133
00:05:22,160 --> 00:05:25,040
比如说咱们发送了一个

134
00:05:25,040 --> 00:05:26,120
你好

135
00:05:26,120 --> 00:05:29,080
咱们发送一个

136
00:05:29,080 --> 00:05:31,820
process.pid吧

137
00:05:31,820 --> 00:05:32,600
咱们来看一下

138
00:05:32,600 --> 00:05:33,840
看收到的data是什么

139
00:05:33,840 --> 00:05:35,040
data

140
00:05:35,040 --> 00:05:41,000
process.pid

141
00:05:41,000 --> 00:05:50,320
send process.pid

142
00:05:50,320 --> 00:05:53,900
好

143
00:05:53,900 --> 00:05:55,240
我们来伸的一个999吧

144
00:05:55,240 --> 00:05:55,800
好

145
00:05:55,800 --> 00:05:56,260
再来

146
00:05:56,260 --> 00:06:00,340
点message data

147
00:06:00,340 --> 00:06:08,160
data.process

148
00:06:11,000 --> 00:06:13,480
点pid好这样我们来看一下看对不对

149
00:06:13,480 --> 00:06:24,900
我们来看一下message

150
00:06:24,900 --> 00:06:29,400
message当class的主线程接收到工作进程发生的消息后触发

151
00:06:29,400 --> 00:06:33,380
那么他是不是和咱们刚才讲的chilled process里面的fork

152
00:06:33,380 --> 00:06:37,340
通过onmessage和send去接收负责之间的消息

153
00:06:37,340 --> 00:06:38,820
他们是这么一回事吗

154
00:06:38,820 --> 00:06:40,740
那么我们就来做一下试验

155
00:06:40,740 --> 00:06:43,080
cluster里面的他的message是不是这样去使用

156
00:06:43,080 --> 00:06:48,040
首先这里我们cluster去on一个message

157
00:06:48,040 --> 00:06:49,120
然后呢process去圣的

158
00:06:49,120 --> 00:06:50,460
咱们来看一下收到的data是什么

159
00:06:50,460 --> 00:06:51,220
是不是9999

160
00:06:51,220 --> 00:06:54,340
大家可以看一下

161
00:06:54,340 --> 00:06:56,360
他发送过来的其实是一个对象

162
00:06:56,360 --> 00:06:57,480
为什么呀

163
00:06:57,480 --> 00:06:58,860
因为我们监听消息

164
00:06:58,860 --> 00:07:02,100
需要在每一个work里面去进行message的收发

165
00:07:02,100 --> 00:07:06,500
但是此时cluster他不知道是哪一个process传过来信息

166
00:07:06,500 --> 00:07:07,680
所以说这样会有问题

167
00:07:07,680 --> 00:07:09,240
那么我们怎么去修改呢

168
00:07:09,240 --> 00:07:13,080
其实在其实咱们可拉的

169
00:07:13,080 --> 00:07:20,500
其实在卡拉斯的方法下面他有一个我可属性咱们可以看一下咱们呢比如说咱们去

170
00:07:20,500 --> 00:07:28,960
咱们去挖一个咱们去挖一个log等于可拉斯的咱们把卡拉斯打印出来看一下他到底是什么

171
00:07:28,960 --> 00:07:31,260
走

172
00:07:33,820 --> 00:07:34,840
好 大家注意到没有

173
00:07:34,840 --> 00:07:37,660
cluster下面他有一个workers

174
00:07:37,660 --> 00:07:41,240
workers呢 他是一个宿主 其实workers就是咱们每一个

175
00:07:41,240 --> 00:07:44,580
紫禁城所付出来的对象 所以说呢 我们

176
00:07:44,580 --> 00:07:45,860
需要

177
00:07:45,860 --> 00:07:48,660
去在每一个紫work上面去

178
00:07:48,660 --> 00:07:52,260
通过message去监听 这里也是需要一个咱们需要注意的一个问题

179
00:07:52,260 --> 00:07:54,560
这里object.case

180
00:07:54,560 --> 00:07:56,340
是什么 是不是便利咱们对象的k

181
00:07:56,340 --> 00:07:57,880
这之前咱们学过吧

182
00:07:57,880 --> 00:07:58,900
es6里面的内容

183
00:07:58,900 --> 00:08:00,700
object.case

184
00:08:00,700 --> 00:08:01,980
cluster

185
00:08:01,980 --> 00:08:02,740
注释掉

186
00:08:03,820 --> 00:08:28,460
.workers.forEach.cluster.workers.on.message.function.data

187
00:08:28,460 --> 00:08:32,300
好咱们再来打用data console.nogdata

188
00:08:32,300 --> 00:08:36,140
哎按错了

189
00:08:36,140 --> 00:08:39,980
好再来咱们再来

190
00:08:39,980 --> 00:08:42,020
大家看到了现在就传过来了

191
00:08:42,020 --> 00:08:43,300
如果说我们去传

192
00:08:43,300 --> 00:08:46,640
process.pid

193
00:08:46,640 --> 00:08:48,680
其实也可以传递过来

194
00:08:48,680 --> 00:08:52,020
好那么这里呢就是onmessage的使用

195
00:08:52,020 --> 00:08:55,860
那么onmessage呢这里呢需要注意比较特殊

196
00:08:56,360 --> 00:08:58,660
需要去在单独的

197
00:08:58,660 --> 00:09:00,460
我可上

198
00:09:00,460 --> 00:09:01,480
监听

199
00:09:01,480 --> 00:09:04,040
好那我再来看一下

200
00:09:04,040 --> 00:09:05,580
this connect 事件

201
00:09:05,580 --> 00:09:11,720
他是什么呢

202
00:09:11,720 --> 00:09:14,800
在cluster workers的每个工作进程中调用

203
00:09:14,800 --> 00:09:16,080
this connect

204
00:09:16,080 --> 00:09:18,880
也就说当有你有工作进程断开以后那么cluster

205
00:09:18,880 --> 00:09:20,160
将会

206
00:09:20,160 --> 00:09:21,200
去触发

207
00:09:21,200 --> 00:09:22,220
他说了这么一大堆

208
00:09:22,220 --> 00:09:24,000
其实很简单你只要有worker

209
00:09:24,000 --> 00:09:26,320
退出了那么就会触发cluster的disconnect事件

210
00:09:26,360 --> 00:09:28,540
到底是这样的呢 我们来看一下

211
00:09:28,540 --> 00:09:34,560
cluster.on 什么呀?disconnect

212
00:09:34,560 --> 00:09:40,960
打印出来

213
00:09:40,960 --> 00:09:45,560
有工作进程退出了

214
00:09:45,560 --> 00:09:47,600
这里应该是walker

215
00:09:47,600 --> 00:09:48,880
咱们来打印一下

216
00:09:48,880 --> 00:09:54,260
walker.process.prd

217
00:09:55,540 --> 00:09:57,060
那么我们先来看一下

218
00:09:57,060 --> 00:10:00,360
好 这里呢稍微有点干扰

219
00:10:00,360 --> 00:10:01,240
注释一下

220
00:10:01,240 --> 00:10:03,140
好 重新再来

221
00:10:03,140 --> 00:10:07,320
好 这里呢其实什么都没有发生

222
00:10:07,320 --> 00:10:08,520
那么我们怎么样去模拟

223
00:10:08,520 --> 00:10:10,340
有工作进程退出了这件事情呢

224
00:10:10,340 --> 00:10:10,860
其实很简单

225
00:10:10,860 --> 00:10:12,240
在麦克里面是很简单的

226
00:10:12,240 --> 00:10:13,100
活动监视器

227
00:10:13,100 --> 00:10:14,300
大家看到没有

228
00:10:14,300 --> 00:10:14,940
咱们是不是有

229
00:10:14,940 --> 00:10:16,660
有八个 八个进程

230
00:10:16,660 --> 00:10:17,980
我们退一个 退出一个

231
00:10:17,980 --> 00:10:18,880
想退出

232
00:10:18,880 --> 00:10:19,440
大家看到没有

233
00:10:19,440 --> 00:10:21,620
有工作进程退出了55135

234
00:10:21,620 --> 00:10:23,920
好 我们再来看一下

235
00:10:23,920 --> 00:10:24,780
退出

236
00:10:24,780 --> 00:10:27,660
有工作进程退出了55137

237
00:10:27,660 --> 00:10:28,800
那么我们再来做一个实验

238
00:10:28,800 --> 00:10:29,940
假如说我们把master给干掉

239
00:10:29,940 --> 00:10:31,000
第一个是master退出

240
00:10:31,000 --> 00:10:32,100
好

241
00:10:32,100 --> 00:10:33,420
那我们整个进程就挂掉了

242
00:10:33,420 --> 00:10:34,480
所以说咱们的class里面

243
00:10:34,480 --> 00:10:36,000
主线程是非常重要的

244
00:10:36,000 --> 00:10:37,140
那么接下来呢

245
00:10:37,140 --> 00:10:37,700
是exit

246
00:10:37,700 --> 00:10:39,020
其实exit事件呢

247
00:10:39,020 --> 00:10:40,480
它和disconnect很类似

248
00:10:40,480 --> 00:10:41,580
你有工作进程退出

249
00:10:41,580 --> 00:10:42,900
也会触发exit事件

250
00:10:42,900 --> 00:10:43,840
这里呢

251
00:10:43,840 --> 00:10:44,780
老师呢就不去

252
00:10:44,780 --> 00:10:46,800
再对他去做一个代码的讲解了

253
00:10:46,800 --> 00:10:47,900
同学们可以自己去看文档

254
00:10:47,900 --> 00:10:50,780
这里呢就是咱们class里面

255
00:10:50,780 --> 00:10:52,280
事件的一个使用

256
00:10:52,280 --> 00:10:53,300
我们来回顾一下

257
00:10:53,300 --> 00:10:54,580
我们刚才是不是讲解了

258
00:10:54,580 --> 00:10:57,320
fork是不是就是咱们去创建一个新的进程

259
00:10:57,320 --> 00:10:59,720
listening它就是

260
00:10:59,720 --> 00:11:02,820
咱们有一个工作进程去创建那个http服务

261
00:11:02,820 --> 00:11:03,900
就会触发它message

262
00:11:03,900 --> 00:11:06,300
会监听子进程所传递过来的消息

263
00:11:06,300 --> 00:11:08,180
但是它必须要在单独的walk上面去监听

264
00:11:08,180 --> 00:11:08,360
为什么

265
00:11:08,360 --> 00:11:10,340
因为咱们的cluster.walker它是不是一个宿主

266
00:11:10,340 --> 00:11:12,200
我们要把它单独拿出去监听

267
00:11:12,200 --> 00:11:13,140
disconnect

268
00:11:13,140 --> 00:11:15,920
只要有我们的子进程退出就会触发

269
00:11:15,920 --> 00:11:17,460
包括exit是一样的

270
00:11:17,460 --> 00:11:17,660
好

271
00:11:17,660 --> 00:11:19,420
那么如果说有其他问题的同学

272
00:11:19,420 --> 00:11:20,880
可以下去之后自己去看一下文档

273
00:11:20,880 --> 00:11:23,120
文档的链接老师也贴在这里了

274
00:11:23,120 --> 00:11:23,320
好

275
00:11:23,320 --> 00:11:24,480
这里就是这节课的内容

