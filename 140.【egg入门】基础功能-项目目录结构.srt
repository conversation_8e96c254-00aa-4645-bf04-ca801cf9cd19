1
00:00:00,000 --> 00:00:04,820
好 这节课开始了 我们就要正式进入1GG 它那个学习了 我们接下来会系统去学习

2
00:00:04,820 --> 00:00:06,140
 1GG 里面它到底有哪些

3
00:00:06,140 --> 00:00:07,420
模块以及功能

4
00:00:07,420 --> 00:00:08,700
首先呢 我们来看一下它的

5
00:00:08,700 --> 00:00:10,500
目录结构 因为我们刚才讲到了

6
00:00:10,500 --> 00:00:12,040
1GG 它的一个原则是什么

7
00:00:12,040 --> 00:00:14,340
是不是约定

8
00:00:14,340 --> 00:00:15,100
大于

9
00:00:15,100 --> 00:00:18,440
配置 那么我们来看一下它那些目录结构的

10
00:00:18,440 --> 00:00:18,940
约定

11
00:00:18,940 --> 00:00:19,720
是什么

12
00:00:19,720 --> 00:00:25,660
首先呢 比如说我们起了一个1GG Project 这是不是咱们的项目录 那么里面会有px.json

13
00:00:25,660 --> 00:00:26,360
 是咱们那些依赖项

14
00:00:26,360 --> 00:00:29,180
还会有呢 app.js app.js 它是做什么的呢

15
00:00:29,440 --> 00:00:35,580
他其实呢可以我们去通过 app 比如说我们去监听 error 时间去监听一些礼服启动之后他的一些生命周期

16
00:00:35,580 --> 00:00:39,160
这里是app.js做的事情 具体的内容呢 我们后面都会去介绍

17
00:00:39,160 --> 00:00:43,260
那么什么是agent呢 这里有个agent.js 他其实是和咱们的多进程相关的

18
00:00:43,260 --> 00:00:45,320
我们之前是不是学习过 nodejs多进程

19
00:00:45,320 --> 00:00:49,400
那么 nodejs多进程 他可以分为什么呢 是不是一个master 然后一些worker 去给master

20
00:00:49,400 --> 00:00:49,920
 去工作

21
00:00:49,920 --> 00:00:51,200
那么在1GG里面呢

22
00:00:51,200 --> 00:00:53,000
agent.js他呢 实际上是

23
00:00:53,000 --> 00:00:55,300
他呢 其实是比较独特的

24
00:00:56,580 --> 00:00:57,780
这呢是一机里面独有的

25
00:00:57,780 --> 00:00:59,520
那么他到底是做什么用的

26
00:00:59,520 --> 00:01:01,220
我们后面会去介绍他

27
00:01:01,220 --> 00:01:02,480
接下来是app这样一个文件夹

28
00:01:02,480 --> 00:01:04,320
app是不代表咱们的一个具体业务逻辑

29
00:01:04,320 --> 00:01:06,920
root不用说路由controller的控制器

30
00:01:06,920 --> 00:01:08,420
那么什么是service呢

31
00:01:08,420 --> 00:01:11,080
那么我们来看一下

32
00:01:11,080 --> 00:01:13,340
想了解service是什么

33
00:01:13,340 --> 00:01:16,380
你比如说我们现在有一个home页面

34
00:01:16,380 --> 00:01:19,120
当我们去访问home这样一个路由的时候

35
00:01:19,120 --> 00:01:21,480
是不是会访问到咱们home的这样的controller

36
00:01:21,480 --> 00:01:23,080
比如说在root里面

37
00:01:23,080 --> 00:01:23,980
我们get的目录

38
00:01:23,980 --> 00:01:25,840
然后呢是不是调用了咱们controller里面的

39
00:01:25,840 --> 00:01:27,120
Home里面的index方法

40
00:01:27,120 --> 00:01:29,160
但是呢在咱们

41
00:01:29,160 --> 00:01:31,980
index里面是不是会给context.body

42
00:01:31,980 --> 00:01:33,520
比如说我们去返回一段什么

43
00:01:33,520 --> 00:01:34,040
数据

44
00:01:34,040 --> 00:01:36,080
那么controller呢他只是负责

45
00:01:36,080 --> 00:01:36,840
你比如说

46
00:01:36,840 --> 00:01:38,120
我访问到了一个

47
00:01:38,120 --> 00:01:38,900
首页

48
00:01:38,900 --> 00:01:40,440
那么可能你这个时候呢需要去

49
00:01:40,440 --> 00:01:41,460
读取一些数据

50
00:01:41,460 --> 00:01:43,240
但是呢controller他不负责处理

51
00:01:43,240 --> 00:01:44,020
数据逻辑

52
00:01:44,020 --> 00:01:45,800
那么这一块工作呢就会交给

53
00:01:45,800 --> 00:01:48,360
service去处理因为为什么我们现在没有去写service

54
00:01:48,360 --> 00:01:50,420
是不是因为我们目前的逻辑非常的简单了

55
00:01:50,420 --> 00:01:51,180
那么在

56
00:01:51,180 --> 00:01:52,460
后面我们的项目

57
00:01:52,460 --> 00:01:55,280
变得复杂变得更大的之后那么我们就会用到service

58
00:01:55,280 --> 00:01:57,640
他呢是专门去处理我们的一个数据逻辑的

59
00:01:57,640 --> 00:02:00,040
好 那我们接着往下走

60
00:02:00,040 --> 00:02:01,280
 middle wall 中间间

61
00:02:01,280 --> 00:02:02,560
那么是 106 他是什么呢

62
00:02:02,560 --> 00:02:04,080
他实际上是定时任务

63
00:02:04,080 --> 00:02:04,880
那么定时任务

64
00:02:04,880 --> 00:02:05,600
他有什么作用

65
00:02:05,600 --> 00:02:06,720
后面课程会去介绍

66
00:02:06,720 --> 00:02:08,520
public 是不是我们的经常资源文件目录

67
00:02:08,520 --> 00:02:09,560
view 模板

68
00:02:09,560 --> 00:02:11,520
extend 是不是可以去扩展我们的一些

69
00:02:11,520 --> 00:02:12,640
方法呀

70
00:02:12,640 --> 00:02:13,840
你比如说 help contests

71
00:02:13,840 --> 00:02:14,840
咱们刚才是不是都讲过

72
00:02:14,840 --> 00:02:15,800
其实呢还可以去扩展

73
00:02:15,800 --> 00:02:16,800
你比如说你想扩展一下

74
00:02:16,800 --> 00:02:18,280
request 或者 response

75
00:02:18,280 --> 00:02:20,320
或者app agent 都是可以的

76
00:02:20,320 --> 00:02:21,600
我们后面都会去介绍

77
00:02:21,600 --> 00:02:23,640
包括咱们项目是不是一定会有一些配置

78
00:02:23,640 --> 00:02:25,380
那么它全部都在config里面

79
00:02:25,380 --> 00:02:27,180
包括还有一个test目录是做什么的

80
00:02:27,180 --> 00:02:28,100
那么我们猜都可以猜了

81
00:02:28,100 --> 00:02:29,480
是专门去做测试的

82
00:02:29,480 --> 00:02:29,900
好

83
00:02:29,900 --> 00:02:33,400
那么这里呢就是我们这节课的一个内容

