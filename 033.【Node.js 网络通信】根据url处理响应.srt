1
00:00:00,000 --> 00:00:03,840
对于我们现在这个创建的这个简单的HTTP服务来说

2
00:00:03,840 --> 00:00:05,320
它目前呢非常简单

3
00:00:05,320 --> 00:00:07,900
简单到我们发送任何请求

4
00:00:07,900 --> 00:00:09,800
我们在这里收到的响应呢都是Hello World

5
00:00:09,800 --> 00:00:11,540
因为我们可以看到在单码当中

6
00:00:11,540 --> 00:00:12,620
请求进来以后

7
00:00:12,620 --> 00:00:14,280
这个是我们对应的请求助理函数

8
00:00:14,280 --> 00:00:17,420
也就是说不管你请求的是-A还是-B还是-C

9
00:00:17,420 --> 00:00:18,860
那么你收到的都是Hello World

10
00:00:18,860 --> 00:00:20,000
所以说你可以看到

11
00:00:20,000 --> 00:00:21,380
例如我们在这里我们输入-A

12
00:00:21,380 --> 00:00:23,700
当然这个路径它现在也是不存在的

13
00:00:23,700 --> 00:00:24,640
-B -C

14
00:00:24,640 --> 00:00:26,960
我们可以看到我们自己收到的响应呢都是Hello World

15
00:00:26,960 --> 00:00:28,680
也就是说我们现在这个服务器呢

16
00:00:28,680 --> 00:00:30,180
它的能力还非常的简单

17
00:00:30,180 --> 00:00:30,920
或者说非常的弱

18
00:00:30,920 --> 00:00:32,700
那么接下来我们要给它

19
00:00:32,700 --> 00:00:34,720
让它的能力稍微的来升级一步

20
00:00:34,720 --> 00:00:36,760
就是说我们要让它能根据

21
00:00:36,760 --> 00:00:39,200
能根据我们在浏览器当中

22
00:00:39,200 --> 00:00:40,780
所输入的不同的请求地址

23
00:00:40,780 --> 00:00:42,780
来返回不同的数据

24
00:00:42,780 --> 00:00:44,540
例如我让它请求杠A的时候

25
00:00:44,540 --> 00:00:45,740
我让它响应回来

26
00:00:45,740 --> 00:00:46,420
Hello A

27
00:00:46,420 --> 00:00:47,460
请求杠B的时候

28
00:00:47,460 --> 00:00:48,560
我们让它响应Hello B

29
00:00:48,560 --> 00:00:50,020
那么这个时候

30
00:00:50,020 --> 00:00:51,240
我们就需要在这里的

31
00:00:51,240 --> 00:00:52,260
针对不同的请求路径

32
00:00:52,260 --> 00:00:53,480
来处理不同的响应

33
00:00:53,480 --> 00:00:54,880
那我们在这里

34
00:00:54,880 --> 00:00:56,660
如何得到当前的请求路径呢

35
00:00:56,660 --> 00:00:57,620
使用方式的

36
00:00:57,620 --> 00:00:58,920
实际方式也非常的简单

37
00:00:58,920 --> 00:01:00,120
我们只需要接下来

38
00:01:00,120 --> 00:01:01,820
所以我们把这个例子呢

39
00:01:01,820 --> 00:01:02,820
写到一个单独的文件当中

40
00:01:02,820 --> 00:01:04,820
那么这个就是http

41
00:01:04,820 --> 00:01:09,120
或者说叫做url

42
00:01:09,120 --> 00:01:11,920
那这个时候我们还是把刚才这个代码

43
00:01:11,920 --> 00:01:12,920
势力代码我们给它拿过来

44
00:01:12,920 --> 00:01:13,920
拿过来以后

45
00:01:13,920 --> 00:01:15,720
然后这个时候我们首先把这种代码

46
00:01:15,720 --> 00:01:16,720
我们先给它去掉

47
00:01:16,720 --> 00:01:17,660
好了去掉以后

48
00:01:17,660 --> 00:01:18,560
接下来我们就可以看到

49
00:01:18,560 --> 00:01:21,420
我们可以在这里拿到通过请求对象

50
00:01:21,420 --> 00:01:23,620
拿到我们的这个url

51
00:01:23,620 --> 00:01:24,520
也就是当前的请求路径

52
00:01:24,520 --> 00:01:25,820
拿到它以后

53
00:01:25,820 --> 00:01:27,360
然后接下来我们就可以来进行一个判断

54
00:01:27,360 --> 00:01:29,040
也就是说如果例如

55
00:01:29,040 --> 00:01:30,340
如果它等于干

56
00:01:30,340 --> 00:01:32,220
那什么时候为干呢

57
00:01:32,220 --> 00:01:33,880
例如当我们在这个地址栏当中

58
00:01:33,880 --> 00:01:35,040
你什么都没有说的时候

59
00:01:35,040 --> 00:01:36,620
那么这个时候实际上默认就是干

60
00:01:36,620 --> 00:01:39,000
所以那我们请就为干的时候

61
00:01:39,000 --> 00:01:40,820
我们在这里可以直接去response

62
00:01:40,820 --> 00:01:41,660
我们简单一点

63
00:01:41,660 --> 00:01:43,040
可以直接来end一个消息

64
00:01:43,040 --> 00:01:44,240
我们来一个hello

65
00:01:44,240 --> 00:01:47,600
那接下来我们再来else if

66
00:01:47,600 --> 00:01:49,460
如果url是干A

67
00:01:49,460 --> 00:01:51,000
那么在这我们就可以看到

68
00:01:51,000 --> 00:01:53,500
其实所有的url都已干Cator

69
00:01:53,500 --> 00:01:55,180
那如果是干A的话

70
00:01:55,180 --> 00:01:56,040
那么这个时候

71
00:01:56,040 --> 00:01:57,640
我们去到这里让它helloA

72
00:01:57,640 --> 00:02:00,200
同样的我们再来简单来一个

73
00:02:00,200 --> 00:02:01,480
如果是B

74
00:02:01,480 --> 00:02:04,720
那么这个时候我们在这里让它来一个helloB

75
00:02:04,720 --> 00:02:07,720
那么最后对于其他未知的

76
00:02:07,720 --> 00:02:09,320
那么未知的话

77
00:02:09,320 --> 00:02:11,380
我们通常情况下都会想什么呢

78
00:02:11,380 --> 00:02:12,540
一般就是想一个404

79
00:02:12,540 --> 00:02:14,000
那么对于404的话

80
00:02:14,000 --> 00:02:16,160
我们在这里可以直接response.state

81
00:02:16,160 --> 00:02:18,860
code等于404

82
00:02:18,860 --> 00:02:20,140
好我们首先设置一个状态码

83
00:02:20,140 --> 00:02:21,040
那么这样的话

84
00:02:21,040 --> 00:02:23,800
我们就是这个是最正确的一种方式

85
00:02:23,800 --> 00:02:25,640
因为其他的正确的都是200

86
00:02:25,640 --> 00:02:27,200
就是未知的没有的

87
00:02:27,200 --> 00:02:28,200
就是我们一般想要404

88
00:02:28,200 --> 00:02:29,480
好了设置完以后

89
00:02:29,480 --> 00:02:30,920
然后接下来我们可以给他想一个消息

90
00:02:30,920 --> 00:02:33,440
我们带来一个404not fund

91
00:02:33,440 --> 00:02:34,820
那么这样的话

92
00:02:34,820 --> 00:02:36,060
我们这个最基本的

93
00:02:36,060 --> 00:02:38,700
这个就是根据URL来返回不同响应

94
00:02:38,700 --> 00:02:39,900
我们当然这个写好了

95
00:02:39,900 --> 00:02:40,400
写好以后

96
00:02:40,400 --> 00:02:41,580
接下来我们可以注意

97
00:02:41,580 --> 00:02:43,840
首先来到我们的命令行当中

98
00:02:43,840 --> 00:02:44,660
我们在命令行当中

99
00:02:44,660 --> 00:02:45,480
我们要把这个

100
00:02:45,480 --> 00:02:46,660
我们刚才这个Node00

101
00:02:46,660 --> 00:02:48,180
启动这个服务先给它关掉

102
00:02:48,180 --> 00:02:49,620
然后接下来此时我们再来

103
00:02:49,620 --> 00:02:51,560
Node01URL.js

104
00:02:51,560 --> 00:02:53,800
那么把这个服务启动成功以后

105
00:02:53,800 --> 00:02:55,380
然后接下来回到你的浏览器当中

106
00:02:55,380 --> 00:02:57,420
那么首先我们来先请求默认

107
00:02:57,420 --> 00:02:59,480
我们看到此时就是Hello World

108
00:02:59,480 --> 00:03:00,200
然后一个碳号

109
00:03:00,200 --> 00:03:02,380
接下来我们在这里-A回车

110
00:03:02,380 --> 00:03:03,440
我们就看到Hello A

111
00:03:03,440 --> 00:03:05,360
我们再来-B就是Hello B

112
00:03:05,360 --> 00:03:06,240
接下来说-C

113
00:03:06,240 --> 00:03:09,220
那-C我们发现此时反馈的就是404

114
00:03:09,220 --> 00:03:11,780
当然我们写的那个状态码

115
00:03:11,780 --> 00:03:12,820
它的一个主要作用是什么

116
00:03:12,820 --> 00:03:15,380
我们写的那个状态码主要的一个作用

117
00:03:15,380 --> 00:03:19,440
就是因为我们可以在这个就是控制台当中

118
00:03:19,440 --> 00:03:22,020
在控制台当中来看到这个404的

119
00:03:22,020 --> 00:03:24,240
很明显的一个就是错误提示消息

120
00:03:24,240 --> 00:03:25,080
能看到这个404

121
00:03:25,080 --> 00:03:26,240
主要就是这个原因

122
00:03:26,240 --> 00:03:27,320
那假如说你在这里

123
00:03:27,320 --> 00:03:28,420
你不写这个404的代码

124
00:03:28,420 --> 00:03:29,680
那么这个时候

125
00:03:29,680 --> 00:03:31,140
你在这个浏览器当中

126
00:03:31,140 --> 00:03:32,040
就不太好区分

127
00:03:32,040 --> 00:03:33,740
我们在这里可以看到这里

128
00:03:33,740 --> 00:03:36,380
为什么这里没有生效呢

129
00:03:36,380 --> 00:03:37,280
注意原因是

130
00:03:37,280 --> 00:03:38,500
如果说你服务单代码

131
00:03:38,500 --> 00:03:39,460
一旦发生改变

132
00:03:39,460 --> 00:03:40,620
那么一定要记得

133
00:03:40,620 --> 00:03:42,360
把你的服务重启下

134
00:03:42,360 --> 00:03:43,320
否则它运行的

135
00:03:43,320 --> 00:03:45,360
还是你改变之前的交换代码

136
00:03:45,360 --> 00:03:46,340
所以这个时候Ctrl C

137
00:03:46,340 --> 00:03:47,080
把它打断

138
00:03:47,080 --> 00:03:47,760
把它挂掉

139
00:03:47,760 --> 00:03:48,520
然后这个时候

140
00:03:48,520 --> 00:03:49,860
我们再来弄个01

141
00:03:49,860 --> 00:03:51,060
我们再把它启动起来

142
00:03:51,060 --> 00:03:52,000
那么此时我们来看一下

143
00:03:52,000 --> 00:03:53,360
就是说当你不写这个404的时候

144
00:03:53,360 --> 00:03:54,560
注意你发的消息

145
00:03:54,560 --> 00:03:55,160
肯定是发回来了

146
00:03:55,160 --> 00:03:56,900
只不过浏览器并不知道

147
00:03:56,900 --> 00:03:57,580
这个东西呢

148
00:03:57,580 --> 00:03:58,780
它是未知的一个资源

149
00:03:58,780 --> 00:04:00,480
或者说没有找到这个资源

150
00:04:00,480 --> 00:04:01,140
那么这个时候

151
00:04:01,140 --> 00:04:01,900
回到这个浏览器当中

152
00:04:01,900 --> 00:04:02,660
你再来刷新

153
00:04:02,660 --> 00:04:04,240
你会发现此时赚单码是凉带

154
00:04:04,240 --> 00:04:05,120
但是实际上

155
00:04:05,120 --> 00:04:05,860
我们想的消息呢

156
00:04:05,860 --> 00:04:06,760
是404没有找到

157
00:04:06,760 --> 00:04:08,820
所以说我们尽量让什么呢

158
00:04:08,820 --> 00:04:10,000
让我们想的这个赚单码

159
00:04:10,000 --> 00:04:11,900
和你真实的这个内容体

160
00:04:11,900 --> 00:04:13,280
去有一个对应关系

161
00:04:13,280 --> 00:04:15,160
所以说正确的方式呢

162
00:04:15,160 --> 00:04:15,920
我们在这里应该

163
00:04:15,920 --> 00:04:16,740
来给他写一个404

164
00:04:16,740 --> 00:04:18,380
当然一定要记得

165
00:04:18,380 --> 00:04:19,260
就是改完单码以后

166
00:04:19,260 --> 00:04:20,940
你要想重新看到这个效果

167
00:04:20,940 --> 00:04:21,660
就来到这里

168
00:04:21,660 --> 00:04:24,040
去把你的服务来重新启动一下

169
00:04:24,040 --> 00:04:24,540
好

170
00:04:24,540 --> 00:04:25,460
那么这就是再回到浏览器当中

171
00:04:25,460 --> 00:04:26,360
我们再来刷新一下

172
00:04:26,360 --> 00:04:27,100
然后你就能看到

173
00:04:27,100 --> 00:04:29,180
这个带有红色提示的这种404

174
00:04:29,180 --> 00:04:30,620
我们的内容也是404

175
00:04:30,620 --> 00:04:31,960
那这样的话

176
00:04:31,960 --> 00:04:32,560
就是看起来呢

177
00:04:32,560 --> 00:04:34,040
要更就是合理一些

178
00:04:34,040 --> 00:04:34,860
当然说

179
00:04:34,860 --> 00:04:36,380
如果你请求我们的正常的

180
00:04:36,380 --> 00:04:37,360
理由请求这个杠的时候

181
00:04:37,360 --> 00:04:38,260
那么想回来消息

182
00:04:38,260 --> 00:04:38,620
就好了

183
00:04:38,620 --> 00:04:40,200
当然我们如果说

184
00:04:40,200 --> 00:04:41,100
你没有写这个专大码

185
00:04:41,100 --> 00:04:42,260
那么它默认就是两版

186
00:04:42,260 --> 00:04:43,300
好

187
00:04:43,300 --> 00:04:45,460
所以这个就是在我们这个Note中

188
00:04:45,460 --> 00:04:46,860
使用HTTP模块

189
00:04:46,860 --> 00:04:49,040
来根据不同的请求路径

190
00:04:49,040 --> 00:04:51,040
去发送不同的想象消息

191
00:04:51,040 --> 00:04:52,440
感谢观看

