1
00:00:00,000 --> 00:00:05,200
在我们当前这个案例完成这个最基本的功能之后

2
00:00:05,200 --> 00:00:07,560
然后接下来我们来看一下我们这个案例

3
00:00:07,560 --> 00:00:10,000
它的一个核心的功能需求

4
00:00:10,000 --> 00:00:11,580
也就是说剩余哪些功能

5
00:00:11,580 --> 00:00:14,680
好我们这里可以看到就是说

6
00:00:14,680 --> 00:00:17,640
我们除了需要实现这个基本的广播消息之外

7
00:00:17,640 --> 00:00:20,660
我们希望呢还能实现这个用户的登录注册

8
00:00:20,660 --> 00:00:23,220
也就是说我们用户第一次进来的时候呢

9
00:00:23,220 --> 00:00:26,100
我们提示用户让他输入昵称来进行一个登录注册

10
00:00:26,100 --> 00:00:28,460
如果说这个昵称呢已经重复了

11
00:00:28,460 --> 00:00:30,700
那我们服务端要告诉这个用户

12
00:00:30,700 --> 00:00:31,800
如果说可以使用

13
00:00:31,800 --> 00:00:34,000
那这个时候我们才允许他进来

14
00:00:34,000 --> 00:00:34,700
他进来以后

15
00:00:34,700 --> 00:00:37,680
然后用户再去进行这个消息发送的时候

16
00:00:37,680 --> 00:00:39,560
那么这个时候他就有了昵称了

17
00:00:39,560 --> 00:00:41,680
用户有了昵称之后

18
00:00:41,680 --> 00:00:43,060
他既可以进行群发

19
00:00:43,060 --> 00:00:44,580
也可以进行点对点

20
00:00:44,580 --> 00:00:45,580
也就是说私聊

21
00:00:45,580 --> 00:00:47,940
就是说我们现在的这个聊天室里面

22
00:00:47,940 --> 00:00:48,880
有很多的用户

23
00:00:48,880 --> 00:00:51,020
例如我想张三想跟李四说

24
00:00:51,020 --> 00:00:52,440
李四想跟王五说

25
00:00:52,440 --> 00:00:54,260
那么对于这样的一个需求功能

26
00:00:54,260 --> 00:00:56,400
接下来我们就通过这个代码

27
00:00:56,400 --> 00:00:58,280
去把它完善的实现一下

28
00:00:58,280 --> 00:00:59,160
好

29
00:00:59,160 --> 00:01:00,260
那说到这里呢

30
00:01:00,260 --> 00:01:01,440
大家就已经看到

31
00:01:01,440 --> 00:01:03,720
我们这里增加了一些功能

32
00:01:03,720 --> 00:01:05,100
增加了一些功能之后呢

33
00:01:05,100 --> 00:01:05,820
那么这个时候

34
00:01:05,820 --> 00:01:07,800
无论是我们的客户端也好

35
00:01:07,800 --> 00:01:09,820
还是说我们这个服务端也好

36
00:01:09,820 --> 00:01:10,400
那么这个时候

37
00:01:10,400 --> 00:01:11,840
我们发送的数据呢

38
00:01:11,840 --> 00:01:13,100
客户端要给服务端发

39
00:01:13,100 --> 00:01:14,400
服务端要给客户端发

40
00:01:14,400 --> 00:01:15,440
那么这个时候

41
00:01:15,440 --> 00:01:17,100
这个不同的数据

42
00:01:17,100 --> 00:01:18,200
是代表不同的含义

43
00:01:18,200 --> 00:01:19,460
而我们都知道

44
00:01:19,460 --> 00:01:19,960
我们在这里

45
00:01:19,960 --> 00:01:21,960
例如我们给服务端发一个1

46
00:01:21,960 --> 00:01:23,540
那么服务端收到的就是1

47
00:01:23,540 --> 00:01:24,840
但这个时候有一个问题

48
00:01:24,840 --> 00:01:26,580
就是说对于双方

49
00:01:26,580 --> 00:01:27,660
对于双方来讲

50
00:01:27,660 --> 00:01:31,300
如何知道对方发送的这个消息的一个含义

51
00:01:31,300 --> 00:01:32,340
就是它的一个含义

52
00:01:32,340 --> 00:01:33,780
因为我们现在的话

53
00:01:33,780 --> 00:01:36,600
两个端在进行这个消息收发的时候

54
00:01:36,600 --> 00:01:39,480
他们并不知道对面发过来的数据到底是什么

55
00:01:39,480 --> 00:01:40,820
就是说我们在这

56
00:01:40,820 --> 00:01:43,280
像我们在这里列出来的这些功能

57
00:01:43,280 --> 00:01:43,500
好

58
00:01:43,500 --> 00:01:44,960
那发过来的消息

59
00:01:44,960 --> 00:01:46,780
那哪个是这个登录注册的

60
00:01:46,780 --> 00:01:47,780
那哪个消息

61
00:01:47,780 --> 00:01:50,720
怎么知道这个消息是这个群聊群发

62
00:01:50,720 --> 00:01:52,120
怎么知道这个消息是私聊呢

63
00:01:52,120 --> 00:01:53,420
所以说接下来呢

64
00:01:53,420 --> 00:01:55,480
我们就要对这个复杂需求的数据

65
00:01:55,480 --> 00:01:57,960
我们要进行一个数据格式的设计

66
00:01:57,960 --> 00:01:58,740
好

67
00:01:58,740 --> 00:01:59,620
什么是数据格式呢

68
00:01:59,620 --> 00:02:01,020
其实非常简单

69
00:02:01,020 --> 00:02:02,260
就是说数据格式

70
00:02:02,260 --> 00:02:03,700
这里有一个专业一点的解释

71
00:02:03,700 --> 00:02:05,400
就是说描述这个数据

72
00:02:05,400 --> 00:02:07,300
这有它的一个基本的规则

73
00:02:07,300 --> 00:02:10,120
因为只有当我们这个数据有了格式

74
00:02:10,120 --> 00:02:13,000
我们才能更方便的去解析使用它

75
00:02:13,000 --> 00:02:14,160
那这样的话就是说

76
00:02:14,160 --> 00:02:15,640
我们大家就是你能看懂

77
00:02:15,640 --> 00:02:16,540
我也能看懂

78
00:02:16,540 --> 00:02:17,860
就好比我们现在

79
00:02:17,860 --> 00:02:20,220
我们中国人说的话一样

80
00:02:20,220 --> 00:02:21,880
例如大家都用汉语来进行交流

81
00:02:21,880 --> 00:02:22,620
那这样的话

82
00:02:22,620 --> 00:02:24,100
我说什么大家就懂

83
00:02:24,100 --> 00:02:25,340
大家说什么我也理解

84
00:02:25,340 --> 00:02:27,400
所以说那这样的一个东西

85
00:02:27,400 --> 00:02:29,160
就是说制定数据格式

86
00:02:29,160 --> 00:02:31,920
说白了也就是在设计一个语言协议

87
00:02:31,920 --> 00:02:33,660
设计一个语言协议

88
00:02:33,660 --> 00:02:33,900
好

89
00:02:33,900 --> 00:02:35,280
那我们常见的

90
00:02:35,280 --> 00:02:36,100
就是在我们这个

91
00:02:36,100 --> 00:02:38,440
就是数据交互的这个数据格式当中

92
00:02:38,440 --> 00:02:40,060
我们比较常用的就是这种

93
00:02:40,060 --> 00:02:40,960
例如JSON呀

94
00:02:40,960 --> 00:02:42,280
XML呀

95
00:02:42,280 --> 00:02:43,140
YML呀

96
00:02:43,140 --> 00:02:43,600
等等等等

97
00:02:43,600 --> 00:02:44,400
有很多

98
00:02:44,400 --> 00:02:45,080
好

99
00:02:45,080 --> 00:02:46,540
对于这里我们这样一个需求呢

100
00:02:46,540 --> 00:02:48,900
我们这里选用我们比较熟悉的JSON

101
00:02:48,900 --> 00:02:50,060
好

102
00:02:50,060 --> 00:02:51,400
所以说接下来大家就可以来看一下

103
00:02:51,400 --> 00:02:53,920
我们用JSON作为我们这种数据格式

104
00:02:53,920 --> 00:02:55,240
然后接下来呢

105
00:02:55,240 --> 00:02:57,740
我这里呢就用Json的方式呢

106
00:02:57,740 --> 00:02:58,900
去简单的制定了一下

107
00:02:58,900 --> 00:03:01,060
我们这几个需求功能

108
00:03:01,060 --> 00:03:02,700
它的一个就是格式

109
00:03:02,700 --> 00:03:04,260
到底就是长什么样

110
00:03:04,260 --> 00:03:06,040
例如当我们进行用户登录的时候

111
00:03:06,040 --> 00:03:07,460
客户端在这里呢

112
00:03:07,460 --> 00:03:09,700
输入它的这个这个昵称

113
00:03:09,700 --> 00:03:11,600
好那给服务端怎么发呢

114
00:03:11,600 --> 00:03:13,000
我们不是说直接发

115
00:03:13,000 --> 00:03:14,740
例如发这样一个消息过去

116
00:03:14,740 --> 00:03:15,820
那服务端他不知道

117
00:03:15,820 --> 00:03:16,840
就是说你是登录

118
00:03:16,840 --> 00:03:18,700
还是说你是这个群聊的消息

119
00:03:18,700 --> 00:03:19,540
所以说分不清

120
00:03:19,540 --> 00:03:20,620
所以说这个时候呢

121
00:03:20,620 --> 00:03:21,280
大家就可以看一下

122
00:03:21,280 --> 00:03:23,380
首先呢我把用户啊

123
00:03:23,380 --> 00:03:25,100
这一部分其实是用户输入的消息

124
00:03:25,100 --> 00:03:27,260
我把它用json做了一个包装

125
00:03:27,260 --> 00:03:28,900
里面有一个type

126
00:03:28,900 --> 00:03:31,940
type就是当前的这个数据的类型

127
00:03:31,940 --> 00:03:33,320
那么这个就表示说

128
00:03:33,320 --> 00:03:35,240
它属于是登录的消息

129
00:03:35,240 --> 00:03:36,740
那对于登录消息来讲

130
00:03:36,740 --> 00:03:38,220
它就写带了一个nickname

131
00:03:38,220 --> 00:03:39,120
到了这个服务端

132
00:03:39,120 --> 00:03:41,460
那这个时候我们这个对方

133
00:03:41,460 --> 00:03:42,800
他收到这个消息以后

134
00:03:42,800 --> 00:03:44,400
他一看你的type是login

135
00:03:44,400 --> 00:03:46,020
然后将来拿到这个nickname

136
00:03:46,020 --> 00:03:48,000
例如我去服务端去教验一下

137
00:03:48,000 --> 00:03:50,260
这个昵称是否已经被使用了

138
00:03:50,260 --> 00:03:52,080
做一下这样的逻辑处理

139
00:03:52,080 --> 00:03:53,860
所以服务端教验完毕之后

140
00:03:53,860 --> 00:03:55,060
同样的

141
00:03:55,060 --> 00:03:57,460
那服务端也要给当前这个登录的请求

142
00:03:57,460 --> 00:03:58,540
响应一个消息

143
00:03:58,540 --> 00:03:59,640
所以说你可以看到

144
00:03:59,640 --> 00:04:01,180
这里服务端响应回来

145
00:04:01,180 --> 00:04:02,300
那类型是什么呢

146
00:04:02,300 --> 00:04:04,620
这是针对于login的这个响应消息

147
00:04:04,620 --> 00:04:06,180
success就是告诉这个客户端

148
00:04:06,180 --> 00:04:09,660
你这次的登录请求是成功还是失败了

149
00:04:09,660 --> 00:04:10,820
message就是告诉客户端

150
00:04:10,820 --> 00:04:12,120
一个简单的消息提示

151
00:04:12,120 --> 00:04:13,640
就是成功了或者说失败

152
00:04:13,640 --> 00:04:15,460
包括什么失败的原因

153
00:04:15,460 --> 00:04:16,440
这个sum users

154
00:04:16,440 --> 00:04:18,860
就是说如果登录成功的时候

155
00:04:18,860 --> 00:04:20,660
那我们把当前这个

156
00:04:20,660 --> 00:04:23,020
在线的用户总数量告诉客户端

157
00:04:23,020 --> 00:04:24,060
那这样的话

158
00:04:24,060 --> 00:04:25,380
例如我们这个用户呢

159
00:04:25,380 --> 00:04:26,300
输入名称登录成功

160
00:04:26,300 --> 00:04:27,720
那我们马上就能看到

161
00:04:27,720 --> 00:04:29,620
就是例如欢迎进入聊天室

162
00:04:29,620 --> 00:04:31,100
当前有多少个用户在线

163
00:04:31,100 --> 00:04:32,960
我们做一个这样的交互

164
00:04:32,960 --> 00:04:34,000
这个是用户登录

165
00:04:34,000 --> 00:04:35,160
好再结束往下来

166
00:04:35,160 --> 00:04:36,920
那用户登录成功以后

167
00:04:36,920 --> 00:04:37,560
然后用户呢

168
00:04:37,560 --> 00:04:39,140
就可以去进行这个群聊

169
00:04:39,140 --> 00:04:40,900
也就是广播消息

170
00:04:40,900 --> 00:04:41,980
那么这里的话呢

171
00:04:41,980 --> 00:04:43,020
它输入它的这个

172
00:04:43,020 --> 00:04:44,340
这个数据的类型

173
00:04:44,340 --> 00:04:45,860
那么这个时候就是 broadcast

174
00:04:45,860 --> 00:04:47,100
就是广播的意思

175
00:04:47,100 --> 00:04:47,820
Message呢

176
00:04:47,820 --> 00:04:48,880
就是当前这个用户呢

177
00:04:48,880 --> 00:04:50,320
所要广播发送的消息

178
00:04:50,320 --> 00:04:52,080
好然后服务端呢

179
00:04:52,080 --> 00:04:55,420
收到这个广播消息的这个数据过来以后

180
00:04:55,420 --> 00:04:56,460
服务端经过探断

181
00:04:56,460 --> 00:04:58,260
看到你是要进行消息的广播

182
00:04:58,260 --> 00:05:00,140
然后将来这个时候服务端呢

183
00:05:00,140 --> 00:05:01,760
去找到所有的客户端

184
00:05:01,760 --> 00:05:02,680
去把这个消息呢

185
00:05:02,680 --> 00:05:03,300
去给它发回去

186
00:05:03,300 --> 00:05:04,320
当然这个时候

187
00:05:04,320 --> 00:05:05,900
服务端要告诉那些客户端

188
00:05:05,900 --> 00:05:07,260
当前这条广播的消息

189
00:05:07,260 --> 00:05:09,640
是哪个客户端发过来的

190
00:05:09,640 --> 00:05:11,520
那客户端收到这个消息以后呢

191
00:05:11,520 --> 00:05:13,960
客户端就根据这个对应的type类型

192
00:05:13,960 --> 00:05:15,240
去进行后续的助理

193
00:05:15,240 --> 00:05:16,820
好 这是广播消息

194
00:05:16,820 --> 00:05:18,900
好 然后再接下来最后一个呢

195
00:05:18,900 --> 00:05:20,300
就是我们这个点对点消息

196
00:05:20,300 --> 00:05:21,080
也就是说

197
00:05:21,080 --> 00:05:22,820
例如我章三要给李四发

198
00:05:22,820 --> 00:05:23,800
李四要给王五发

199
00:05:23,800 --> 00:05:25,060
所以说是这样的

200
00:05:25,060 --> 00:05:25,980
好那在这里的话呢

201
00:05:25,980 --> 00:05:27,920
同样的我们把这个type设计为P2P

202
00:05:27,920 --> 00:05:29,600
就是point to point

203
00:05:29,600 --> 00:05:30,480
就是端到端

204
00:05:30,480 --> 00:05:32,560
好那to就表示说

205
00:05:32,560 --> 00:05:34,180
这个客户端要发给谁

206
00:05:34,180 --> 00:05:35,700
就是to就是发给谁

207
00:05:35,700 --> 00:05:36,960
好message就是说

208
00:05:36,960 --> 00:05:38,080
你要发送的这个消息

209
00:05:38,080 --> 00:05:39,340
好那么服务端

210
00:05:39,340 --> 00:05:41,000
如果说处理完毕以后

211
00:05:41,000 --> 00:05:42,460
那么他要给这个客户端

212
00:05:42,460 --> 00:05:44,160
去回应一个消息

213
00:05:44,160 --> 00:05:45,280
那么回应什么呢

214
00:05:45,280 --> 00:05:47,080
然后回应给指定的这个客户端

215
00:05:47,080 --> 00:05:49,160
那么type P2P的

216
00:05:49,160 --> 00:05:50,600
from就是说来自于谁

217
00:05:50,600 --> 00:05:52,520
to就是说要发给谁

218
00:05:52,520 --> 00:05:55,680
message就是说这个要发送的这个P2P消息

219
00:05:55,680 --> 00:05:57,440
那这样的话

220
00:05:57,440 --> 00:05:58,540
到此为止

221
00:05:58,540 --> 00:06:02,100
就是说我们刚才列集的这几个功能需求点

222
00:06:02,100 --> 00:06:05,340
然后我们就把他的这个具体的消息的格式

223
00:06:05,340 --> 00:06:07,900
再给他去规范化的制定了一下

224
00:06:07,900 --> 00:06:09,080
那下一步呢

225
00:06:09,080 --> 00:06:11,880
我们就要根据我们制定的这个消息的数据格式

226
00:06:11,880 --> 00:06:14,140
来进行这个功能的一个实现

