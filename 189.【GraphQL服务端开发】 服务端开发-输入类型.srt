1
00:00:00,000 --> 00:00:03,840
接下来我们继续来看

2
00:00:03,840 --> 00:00:05,080
关于内置类型相关的内容

3
00:00:05,080 --> 00:00:06,120
关于内置类型的话

4
00:00:06,120 --> 00:00:07,020
我们还剩最后一个知识点

5
00:00:07,020 --> 00:00:08,140
就是所谓的输入类型

6
00:00:08,140 --> 00:00:09,100
输入类型的话

7
00:00:09,100 --> 00:00:09,600
稍微复杂一点

8
00:00:09,600 --> 00:00:11,220
因为它会涉及到一个新的场景

9
00:00:11,220 --> 00:00:12,740
我们叫数据的变更操作

10
00:00:12,740 --> 00:00:15,200
对于之前我们所学的相关的内容

11
00:00:15,200 --> 00:00:16,140
主要是用于查询

12
00:00:16,140 --> 00:00:17,840
对于一个复杂的外部应用来说

13
00:00:17,840 --> 00:00:19,100
肯定已经离不开数据的变更

14
00:00:19,100 --> 00:00:20,600
比如说我们要注册一个用户

15
00:00:20,600 --> 00:00:22,020
或者说要修改用户的信息

16
00:00:22,020 --> 00:00:23,460
这里边就涉及到了数据的变更

17
00:00:23,460 --> 00:00:24,780
对GraphQ2来说

18
00:00:24,780 --> 00:00:26,560
其实它提供了一个专门的类型

19
00:00:26,560 --> 00:00:27,140
叫mutation

20
00:00:27,140 --> 00:00:28,640
来用于做这个数据变更操作

21
00:00:28,640 --> 00:00:30,040
那这个数据变更的时候

22
00:00:30,040 --> 00:00:31,400
就涉及到了这个数据的传递

23
00:00:31,400 --> 00:00:32,560
那这里边我们就可以借助

24
00:00:32,560 --> 00:00:33,600
这个输入类型的方式

25
00:00:33,600 --> 00:00:35,660
来传递一点的这个数据格式

26
00:00:35,660 --> 00:00:37,580
那具体从使用的角度来说

27
00:00:37,580 --> 00:00:39,120
我们需要先定义一个输入类型

28
00:00:39,120 --> 00:00:41,140
然后还需要定义一个mutation类型

29
00:00:41,140 --> 00:00:42,520
那mutation这个类型的话

30
00:00:42,520 --> 00:00:44,580
我们也需要做这个数据的解析

31
00:00:44,580 --> 00:00:45,800
也就是需要提供resolver

32
00:00:45,800 --> 00:00:47,140
从使用的角度来说

33
00:00:47,140 --> 00:00:48,120
其实mutation和query

34
00:00:48,120 --> 00:00:49,520
这个类型是类似的

35
00:00:49,520 --> 00:00:50,860
它都属于GraphQ2

36
00:00:54,780 --> 00:00:55,840
一个用于变更操作

37
00:00:55,840 --> 00:00:57,600
那接下来我们就重点来看一下

38
00:00:57,600 --> 00:00:58,960
这个变更的数据逻辑

39
00:00:58,960 --> 00:01:00,920
这是这三个步骤

40
00:01:00,920 --> 00:01:02,260
就是定义输入类型

41
00:01:02,260 --> 00:01:03,100
定义Mutation类型

42
00:01:03,100 --> 00:01:05,380
然后去解析Mutation类型的数据

43
00:01:05,380 --> 00:01:06,700
然后Mutation大中

44
00:01:06,700 --> 00:01:07,800
可以通过参数的方式

45
00:01:07,800 --> 00:01:09,180
来接收这个input类型

46
00:01:09,180 --> 00:01:11,020
那最后一步就是客户端

47
00:01:11,020 --> 00:01:12,380
通过这种方式

48
00:01:12,380 --> 00:01:14,440
把数据传递到这个服务端

49
00:01:14,440 --> 00:01:15,940
这里边用到的这个输入类型

50
00:01:15,940 --> 00:01:17,960
就是以这种方式传递数据的

51
00:01:17,960 --> 00:01:19,340
那接下来的话

52
00:01:19,340 --> 00:01:20,160
我们就按照这个流程

53
00:01:20,160 --> 00:01:20,980
来演示一下

54
00:01:20,980 --> 00:01:22,280
这个输入类型的具体用法

55
00:01:22,280 --> 00:01:25,380
首先我们要提供一个输入类型的定义

56
00:01:25,380 --> 00:01:26,460
这我们备注一下

57
00:01:26,460 --> 00:01:28,300
这是输入类型

58
00:01:28,300 --> 00:01:31,100
这里边要通过一个关键字叫input来定义

59
00:01:31,100 --> 00:01:33,480
后来几个名我们就叫userinfer

60
00:01:33,480 --> 00:01:35,780
这里边我们注册用户的话

61
00:01:35,780 --> 00:01:37,080
我们就简单的提供两个字段

62
00:01:37,080 --> 00:01:38,140
一个是unim

63
00:01:38,140 --> 00:01:39,740
类型的话我们用sping

64
00:01:39,740 --> 00:01:41,140
另外一个的话是pw

65
00:01:41,140 --> 00:01:42,740
我们也是使用sping的方式

66
00:01:42,740 --> 00:01:43,960
这是输入类型

67
00:01:43,960 --> 00:01:46,000
除了输入类型之外

68
00:01:46,000 --> 00:01:47,780
我们还应该提供一个叫bink

69
00:01:47,780 --> 00:01:50,960
类型通过type的方式来定义nutation

70
00:01:50,960 --> 00:01:53,260
那这个mutation类型的话

71
00:01:53,260 --> 00:01:53,900
它这个字段

72
00:01:53,900 --> 00:01:55,640
一般会起一个有特殊含义的名字

73
00:01:55,640 --> 00:01:57,760
比如说我们加Uder

74
00:01:57,760 --> 00:01:58,500
AdUder

75
00:01:58,500 --> 00:02:01,480
然后按照input的方式来进行添加

76
00:02:01,480 --> 00:02:03,680
但是这个名字实际上也是自定义的

77
00:02:03,680 --> 00:02:04,620
叫APT也是可以的

78
00:02:04,620 --> 00:02:07,040
但是我们一般还是起一个有含义的名字

79
00:02:07,040 --> 00:02:07,660
表示一个动作

80
00:02:07,660 --> 00:02:09,160
然后这里边会接收

81
00:02:09,160 --> 00:02:11,000
客户端传过来的参数

82
00:02:11,000 --> 00:02:12,120
在这我们就起个名字

83
00:02:12,120 --> 00:02:13,460
就叫UderInput

84
00:02:13,460 --> 00:02:14,460
后边这个类型的话

85
00:02:14,460 --> 00:02:16,040
就是我们上面定义的UderInput

86
00:02:16,040 --> 00:02:18,200
这是接收数据的方式

87
00:02:18,200 --> 00:02:19,980
接收完数据之后会有一个响应

88
00:02:19,980 --> 00:02:21,540
显应的话也需要提供一个类型

89
00:02:21,540 --> 00:02:23,220
这我们就提供一个Uler类型

90
00:02:23,220 --> 00:02:25,420
这个Uler就是我们创建好的用户

91
00:02:25,420 --> 00:02:27,640
所以说我们这再添加一个Uler类型

92
00:02:27,640 --> 00:02:30,480
用户类型通过Test

93
00:02:30,480 --> 00:02:31,220
再进行

94
00:02:31,220 --> 00:02:32,520
加上Uler名称

95
00:02:32,520 --> 00:02:34,400
返回到这个用户的类型的话

96
00:02:34,400 --> 00:02:35,260
我们会生成一个ID

97
00:02:35,260 --> 00:02:36,680
所以说这我们要提供一个ID

98
00:02:36,680 --> 00:02:39,080
然后把注册好的用户名称

99
00:02:39,080 --> 00:02:39,740
也给它返回去

100
00:02:39,740 --> 00:02:41,060
同时把密码

101
00:02:41,060 --> 00:02:43,120
我们也提供到显应的数据当中

102
00:02:43,120 --> 00:02:45,360
这是关于Uler类型

103
00:02:45,360 --> 00:02:47,980
到此为止我们需要的类型都进行好了

104
00:02:47,980 --> 00:02:49,480
然后还有一步

105
00:02:49,480 --> 00:02:50,920
就是mutation要做这个数据的解析

106
00:02:50,920 --> 00:02:52,500
这里边要提供resolver

107
00:02:52,500 --> 00:02:54,920
它的resolver我们需要单独提供一个类型

108
00:02:54,920 --> 00:02:55,720
就叫mutation

109
00:02:55,720 --> 00:02:59,540
后面通过函数的方式来处理

110
00:02:59,540 --> 00:03:01,840
这其实也是要添加自己的字段

111
00:03:01,840 --> 00:03:03,960
这个字段的名称要和上面要对得上

112
00:03:03,960 --> 00:03:05,800
后面是一个函数

113
00:03:05,800 --> 00:03:06,860
注意这里边有个细节

114
00:03:06,860 --> 00:03:07,960
就是这里边其实不是函数

115
00:03:07,960 --> 00:03:09,120
这应该是一个对象

116
00:03:09,120 --> 00:03:10,000
里边的这个字段

117
00:03:10,000 --> 00:03:12,120
这个字段才是函数

118
00:03:12,120 --> 00:03:13,780
好 这个细节要注意

119
00:03:13,780 --> 00:03:16,720
其实这个规则和上面的query的规则是一样的

120
00:03:16,720 --> 00:03:18,260
所以说我们这里边来返回

121
00:03:18,260 --> 00:03:20,100
简易的有点的数据

122
00:03:20,100 --> 00:03:20,740
ID的话

123
00:03:20,740 --> 00:03:22,340
我们就给它一个摸案值

124
00:03:22,340 --> 00:03:22,900
就是123

125
00:03:22,900 --> 00:03:24,120
然后这个unim

126
00:03:24,120 --> 00:03:24,940
这个值

127
00:03:24,940 --> 00:03:25,700
其实我们应该是

128
00:03:25,700 --> 00:03:27,060
客户端传过来的那个用户名

129
00:03:27,060 --> 00:03:27,760
我们直接带给它原

130
00:03:27,760 --> 00:03:28,860
原后不动的给它返回

131
00:03:28,860 --> 00:03:30,140
这样我们就能够测试出来

132
00:03:30,140 --> 00:03:30,900
注册的这个用户

133
00:03:30,900 --> 00:03:32,140
有没有传递到服务端

134
00:03:32,140 --> 00:03:34,320
那这个客户端的

135
00:03:34,320 --> 00:03:35,060
这个传过来的数据

136
00:03:35,060 --> 00:03:35,840
如何获取呢

137
00:03:35,840 --> 00:03:37,000
实际上我们之前是学习过的

138
00:03:37,000 --> 00:03:37,840
就是参数的传递

139
00:03:37,840 --> 00:03:38,800
我们要通过

140
00:03:38,800 --> 00:03:40,160
这个resolver函数当中的

141
00:03:40,160 --> 00:03:41,460
第二个参数来进行接收

142
00:03:41,460 --> 00:03:42,560
就是args

143
00:03:42,560 --> 00:03:44,020
所以说我们在X当中

144
00:03:44,020 --> 00:03:44,960
应该能够得到谁呢

145
00:03:44,960 --> 00:03:46,320
应该能够得到这个U.input

146
00:03:46,320 --> 00:03:47,960
就是客户端传过来的

147
00:03:47,960 --> 00:03:48,720
input类型的数据

148
00:03:48,720 --> 00:03:49,860
然后它里边应该有一个

149
00:03:49,860 --> 00:03:50,560
UNEM属性

150
00:03:50,560 --> 00:03:52,560
那从里还有一个就是PWB

151
00:03:52,560 --> 00:03:53,860
我们也是通过参数

152
00:03:53,860 --> 00:03:54,560
给它获取到

153
00:03:54,560 --> 00:03:55,760
然后在原封不动的

154
00:03:55,760 --> 00:03:57,500
返回到这个客户端

155
00:03:57,500 --> 00:04:00,940
这是关于后台的业务逻辑

156
00:04:00,940 --> 00:04:01,580
主要就这么多

157
00:04:01,580 --> 00:04:03,080
然后的话我们把它启动

158
00:04:03,080 --> 00:04:04,960
打开这个命令盒

159
00:04:04,960 --> 00:04:06,780
然后执行Node07

160
00:04:06,780 --> 00:04:09,080
好 启动成功之后

161
00:04:09,080 --> 00:04:10,080
我们怎么做测试呢

162
00:04:10,080 --> 00:04:11,400
这里边看最后一段

163
00:04:11,400 --> 00:04:12,160
这个代码

164
00:04:12,160 --> 00:04:12,860
就是这个

165
00:04:12,860 --> 00:04:15,020
这个和之前有一点特殊

166
00:04:15,020 --> 00:04:16,880
就是这个最外层的

167
00:04:16,880 --> 00:04:17,980
对象的前面有一个mutation

168
00:04:17,980 --> 00:04:19,740
其实这个格式是类似的

169
00:04:19,740 --> 00:04:20,460
为什么这样说呢

170
00:04:20,460 --> 00:04:21,820
你看我们之前在做查询的时候

171
00:04:21,820 --> 00:04:22,680
是这样做的

172
00:04:22,680 --> 00:04:23,340
好了

173
00:04:23,340 --> 00:04:24,420
然后点查询

174
00:04:24,420 --> 00:04:25,400
这里边能得到数据

175
00:04:25,400 --> 00:04:27,100
其实这个前边有一个默认的名字

176
00:04:27,100 --> 00:04:27,640
叫什么呢

177
00:04:27,640 --> 00:04:28,220
这个叫query

178
00:04:28,220 --> 00:04:29,540
你这样写也没有问题

179
00:04:29,540 --> 00:04:31,020
只不过之前我们默认

180
00:04:31,020 --> 00:04:32,180
是省略掉这个名字的

181
00:04:32,180 --> 00:04:33,720
所以说这个季节要注意

182
00:04:33,720 --> 00:04:35,360
但是对于mutation来说

183
00:04:35,360 --> 00:04:36,360
这个名字不可以省略掉

184
00:04:36,360 --> 00:04:37,340
你要做这个变更操作

185
00:04:37,340 --> 00:04:38,100
前边必须得显示

186
00:04:38,100 --> 00:04:39,200
加一个名称叫mutation

187
00:04:39,200 --> 00:04:40,640
在里边我们有一座

188
00:04:40,640 --> 00:04:42,640
实际的查询的这个业务逻辑

189
00:04:42,640 --> 00:04:43,960
这个就不叫查询了

190
00:04:43,960 --> 00:04:44,680
这个应该叫定更

191
00:04:44,680 --> 00:04:47,420
那这个里边的字段应该怎么写呢

192
00:04:47,420 --> 00:04:48,800
就是我们定义的那个add

193
00:04:48,800 --> 00:04:50,200
ylder

194
00:04:50,200 --> 00:04:51,040
然后by

195
00:04:51,040 --> 00:04:52,380
那么input

196
00:04:52,380 --> 00:04:54,840
然后这个里边我们要传餐

197
00:04:54,840 --> 00:04:56,960
传餐的这个名字要和服务端要对应

198
00:04:56,960 --> 00:04:58,000
叫ylder

199
00:04:58,000 --> 00:04:58,860
input

200
00:04:58,860 --> 00:05:01,500
后面是我们具体的传记的数据

201
00:05:01,500 --> 00:05:03,520
我们要传的一个是unim

202
00:05:03,520 --> 00:05:06,280
unim我们这儿给它一个值

203
00:05:06,280 --> 00:05:07,120
比如说要注册理次

204
00:05:07,120 --> 00:05:08,900
然后还有一个pwd

205
00:05:08,900 --> 00:05:10,520
这给它一个值

206
00:05:10,520 --> 00:05:10,940
ABC

207
00:05:10,940 --> 00:05:12,940
这是我们传递的input数据

208
00:05:12,940 --> 00:05:14,360
那响应什么数据呢

209
00:05:14,360 --> 00:05:15,740
后边通过花花的方式

210
00:05:15,740 --> 00:05:18,060
来获取响应的这个数据

211
00:05:18,060 --> 00:05:18,880
响应的话我们有ID

212
00:05:18,880 --> 00:05:19,900
还有Eunheim

213
00:05:19,900 --> 00:05:21,180
还有PWD

214
00:05:21,180 --> 00:05:22,980
就是我们把传过去的这两个数据

215
00:05:22,980 --> 00:05:24,500
原本不动的再取回来

216
00:05:24,500 --> 00:05:25,540
并且再加上一个ID

217
00:05:25,540 --> 00:05:26,820
然后我们点查询

218
00:05:26,820 --> 00:05:28,020
这里边就有数据了

219
00:05:28,020 --> 00:05:30,180
那如果说你把这个李四

220
00:05:30,180 --> 00:05:31,100
它的名字给它改了

221
00:05:31,100 --> 00:05:31,740
改成章三

222
00:05:31,740 --> 00:05:33,800
然后呢

223
00:05:33,800 --> 00:05:35,100
下面给它改成123

224
00:05:35,100 --> 00:05:36,940
然后再点查询

225
00:05:36,940 --> 00:05:37,980
这就变了

226
00:05:37,980 --> 00:05:38,880
这就证明什么呢

227
00:05:38,880 --> 00:05:41,140
我们这里边发送到服务端的数据

228
00:05:41,140 --> 00:05:43,580
被服务端的AX接收到了

229
00:05:43,580 --> 00:05:44,380
接收到之后

230
00:05:44,380 --> 00:05:46,480
它再复制给对应的字段

231
00:05:46,480 --> 00:05:47,740
然后再返回到客户端

232
00:05:47,740 --> 00:05:51,160
这样就建立起了客户端和服务器的数据交互

233
00:05:51,160 --> 00:05:53,840
这就是关于input类型

234
00:05:53,840 --> 00:05:54,660
它的应用场景

235
00:05:54,660 --> 00:05:58,100
主要是用于变更的操作

236
00:05:58,100 --> 00:06:00,400
最后的结论我们看一下

237
00:06:00,400 --> 00:06:03,280
input类型主要用于变更操作的数据传递

238
00:06:03,280 --> 00:06:04,680
关于具体的固执

239
00:06:04,680 --> 00:06:06,220
我们刚才就演示了一遍

240
00:06:06,220 --> 00:06:08,640
其实mutation操作是非常重要的一个操作

241
00:06:08,640 --> 00:06:10,300
我们后边来讲客户端查询的时候

242
00:06:10,300 --> 00:06:10,960
还会重点

243
00:06:10,960 --> 00:06:12,760
再来分析一下Mutation的应用场景

244
00:06:12,760 --> 00:06:14,520
这里边我们重点先得知道

245
00:06:14,520 --> 00:06:15,940
这个Input类型它怎么用

246
00:06:15,940 --> 00:06:17,060
这就可以了

247
00:06:17,060 --> 00:06:19,040
好 关于数字类型我们就说到这里

