1
00:00:00,000 --> 00:00:02,100
好

2
00:00:02,100 --> 00:00:03,100
那么这里呢

3
00:00:03,100 --> 00:00:03,740
我们再来

4
00:00:03,740 --> 00:00:05,880
去访问一下我们的

5
00:00:05,880 --> 00:00:06,460
通过Postment

6
00:00:06,460 --> 00:00:08,300
我们去存一条数据来看一下

7
00:00:08,300 --> 00:00:09,120
怎么回事

8
00:00:09,120 --> 00:00:10,180
走里

9
00:00:10,180 --> 00:00:11,000
好

10
00:00:11,000 --> 00:00:11,520
大家可以看到

11
00:00:11,520 --> 00:00:12,260
是不是失败了

12
00:00:12,260 --> 00:00:12,640
扣了1

13
00:00:12,640 --> 00:00:13,740
那么data为什么没有值

14
00:00:13,740 --> 00:00:14,500
那我们来看一下

15
00:00:14,500 --> 00:00:15,500
为什么data没有值

16
00:00:15,500 --> 00:00:20,060
其实这里呢

17
00:00:20,060 --> 00:00:20,780
大家可以看到

18
00:00:20,780 --> 00:00:21,460
我们呢

19
00:00:21,460 --> 00:00:23,040
requestboard is not defined

20
00:00:23,040 --> 00:00:24,740
说明了我们代码

21
00:00:24,740 --> 00:00:25,400
是不是报错了

22
00:00:25,400 --> 00:00:26,060
我们来看一下

23
00:00:26,060 --> 00:00:26,680
是什么问题

24
00:00:26,680 --> 00:00:29,060
好

25
00:00:29,060 --> 00:00:29,980
其实刚才的错误呢

26
00:00:29,980 --> 00:00:30,920
是我们的request body

27
00:00:30,920 --> 00:00:32,120
这里的他拼写错误

28
00:00:32,120 --> 00:00:32,680
小问题

29
00:00:32,680 --> 00:00:33,700
那么我们再来看一下

30
00:00:33,700 --> 00:00:35,040
我们通过postment来访问一下

31
00:00:35,040 --> 00:00:35,720
剩的

32
00:00:35,720 --> 00:00:36,000
好

33
00:00:36,000 --> 00:00:36,800
其实大家可以看到

34
00:00:36,800 --> 00:00:38,440
我们现在是不是得到一个结果

35
00:00:38,440 --> 00:00:38,860
是什么呀

36
00:00:38,860 --> 00:00:40,260
得到一个结果是什么

37
00:00:40,260 --> 00:00:42,240
message for code呢

38
00:00:42,240 --> 00:00:43,240
1万

39
00:00:43,240 --> 00:00:44,100
1万1

40
00:00:44,100 --> 00:00:45,100
然后data是什么呀

41
00:00:45,100 --> 00:00:45,980
它那里面有一句话

42
00:00:45,980 --> 00:00:46,720
叫做呢

43
00:00:46,720 --> 00:00:49,420
duplicate key

44
00:00:49,420 --> 00:00:50,380
那么我们看到这里

45
00:00:50,380 --> 00:00:50,960
是不是就知道了

46
00:00:50,960 --> 00:00:51,900
我们问题出在哪里

47
00:00:51,900 --> 00:00:53,580
是不是传入了重复的title啊

48
00:00:53,580 --> 00:00:54,660
那么当我们去传入

49
00:00:54,660 --> 00:00:55,740
传入一个不一样的title

50
00:00:55,740 --> 00:00:56,420
是不是就解决这个问题

51
00:00:56,420 --> 00:00:57,280
我们去剩的

52
00:00:57,280 --> 00:00:57,840
大家可以看到

53
00:00:57,840 --> 00:00:59,320
我们此时访问的结果是什么

54
00:00:59,320 --> 00:01:01,580
是不是code 0

55
00:01:01,580 --> 00:01:02,380
说明我们成功了吧

56
00:01:02,380 --> 00:01:02,920
Message OK

57
00:01:02,920 --> 00:01:04,880
后面呢就是我们传入了一个data

58
00:01:04,880 --> 00:01:05,480
好

59
00:01:05,480 --> 00:01:07,520
那么这里呢就是我们去进行的一个

60
00:01:07,520 --> 00:01:09,720
咱们进行的一个错误处理

61
00:01:09,720 --> 00:01:11,120
以及我们对返回值的一个封装

62
00:01:11,120 --> 00:01:13,500
好

63
00:01:13,500 --> 00:01:14,280
那么这里呢

64
00:01:14,280 --> 00:01:17,420
其实我们是不是已经完成了错误处理和一个返回值

65
00:01:17,420 --> 00:01:18,640
那么教育参数的话

66
00:01:18,640 --> 00:01:20,720
这一节课的词时间好像有点长

67
00:01:20,720 --> 00:01:21,860
那我们放在下几个去讲

68
00:01:21,860 --> 00:01:23,980
首先呢我们来总结一下这一节课的内容

69
00:01:23,980 --> 00:01:25,480
首先我们刚才是不是讲到了

70
00:01:25,480 --> 00:01:26,820
我们返回值的一个设计

71
00:01:26,820 --> 00:01:27,920
那么成功的话

72
00:01:27,920 --> 00:01:29,600
就是咱们的一个核心是不是扣的呀

73
00:01:29,600 --> 00:01:30,700
扣的零就是成功

74
00:01:30,700 --> 00:01:31,720
那么如果说不是零

75
00:01:31,720 --> 00:01:32,300
那么就是失败

76
00:01:32,300 --> 00:01:33,640
那么失败是不是有很多种情况

77
00:01:33,640 --> 00:01:35,900
那么我们可以根据我们的业务去定义很多的一些扣的

78
00:01:35,900 --> 00:01:36,600
反正你只要不是零

79
00:01:36,600 --> 00:01:37,380
那就一定是失败

80
00:01:37,380 --> 00:01:38,540
然后message这一个字段呢

81
00:01:38,540 --> 00:01:39,300
也是我们的一个约定

82
00:01:39,300 --> 00:01:40,880
message呢就代表我们的一些信息

83
00:01:40,880 --> 00:01:41,460
比如说你错误了

84
00:01:41,460 --> 00:01:42,480
就是错误信息你成功了

85
00:01:42,480 --> 00:01:43,320
一般就是OK

86
00:01:43,320 --> 00:01:44,540
那么成功了一般就是没有原因

87
00:01:44,540 --> 00:01:45,780
包括呢会带上一些data

88
00:01:45,780 --> 00:01:46,980
那么data呢就是比如说成功

89
00:01:46,980 --> 00:01:48,080
你需要请求一些宿主

90
00:01:48,080 --> 00:01:49,180
或者说请求一些列表啊

91
00:01:49,180 --> 00:01:49,780
包括节省

92
00:01:49,780 --> 00:01:50,700
那么如果说失败了

93
00:01:50,700 --> 00:01:52,420
data里面一般就是我们的错误信息

94
00:01:52,420 --> 00:01:53,480
刚才也给同学们去演示

95
00:01:53,480 --> 00:01:55,580
这里就是我们返回值的一个抽象

96
00:01:55,580 --> 00:01:57,640
那么我们通过httb的一个controller

97
00:01:57,640 --> 00:01:58,500
咱们抽象它之后

98
00:01:58,500 --> 00:01:59,300
是不是通过我们的

99
00:01:59,300 --> 00:02:01,220
是不是我们的homecontroller去继承它

100
00:02:01,220 --> 00:02:02,520
所以说我们就可以调用success

101
00:02:02,520 --> 00:02:03,900
和我们的一个失败的方法

102
00:02:03,900 --> 00:02:06,960
那么我们返回值设计完成之后

103
00:02:06,960 --> 00:02:08,000
咱们去编写代码的时候

104
00:02:08,000 --> 00:02:09,600
是不是一定要去进行错误处理

105
00:02:09,600 --> 00:02:11,080
这里一定要去进行错误处理

106
00:02:11,080 --> 00:02:12,340
因为这里我们前面讲过

107
00:02:12,340 --> 00:02:14,120
错误处理是体现咱们水平的事情

108
00:02:14,120 --> 00:02:14,820
特别是我们服务端

109
00:02:14,820 --> 00:02:15,800
你要保证它的一个稳定性

110
00:02:15,800 --> 00:02:17,600
所以说你一定要去进行错误准理

111
00:02:17,600 --> 00:02:18,740
因为如果说你哪个地方挂了

112
00:02:18,740 --> 00:02:20,580
是不是会导致我们的整个县城给崩掉

113
00:02:20,580 --> 00:02:21,680
这样不是很好

114
00:02:21,680 --> 00:02:23,300
所以说呢我们需要把咱们的代码

115
00:02:23,300 --> 00:02:24,720
是不是移入到咱们的一个try cache里面

116
00:02:24,720 --> 00:02:25,220
对吧

117
00:02:25,220 --> 00:02:26,640
比如说我们try里面去执行

118
00:02:26,640 --> 00:02:27,600
我们需要去执行的业务

119
00:02:27,600 --> 00:02:28,860
那么如果说发生错误了

120
00:02:28,860 --> 00:02:29,880
我们是不是直接调用for

121
00:02:29,880 --> 00:02:32,220
然后把错误信息给到我们的一个什么呀

122
00:02:32,220 --> 00:02:33,580
是不是给到我们的一个response里面呢

123
00:02:33,580 --> 00:02:34,460
希望用户可以看到

124
00:02:34,460 --> 00:02:35,260
好

125
00:02:35,260 --> 00:02:37,140
那么这里呢就是我们这节课的内容

