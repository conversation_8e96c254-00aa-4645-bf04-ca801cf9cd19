1
00:00:00,000 --> 00:00:03,000
互联网的核心就是一系列协议

2
00:00:03,000 --> 00:00:04,820
总称为互联网协议

3
00:00:04,820 --> 00:00:06,580
他们对电脑如何临接和组网

4
00:00:06,580 --> 00:00:07,920
做出了详尽的规定

5
00:00:07,920 --> 00:00:09,240
理解了这些协议

6
00:00:09,240 --> 00:00:11,580
那么就理解了互联网的原理

7
00:00:11,580 --> 00:00:13,060
那么我们当前看到的就是

8
00:00:13,060 --> 00:00:15,940
我们OSI提出的网络7层模型

9
00:00:15,940 --> 00:00:17,800
那么这里我们就可以看到

10
00:00:17,800 --> 00:00:19,340
当我们这个计算机

11
00:00:19,340 --> 00:00:21,220
与计算机要进行通信的时候

12
00:00:21,220 --> 00:00:23,520
那么他们要经过怎样的一个过程

13
00:00:23,520 --> 00:00:25,840
我们可以看到我们互联网协议当中

14
00:00:25,840 --> 00:00:27,360
就把这个数据的传输

15
00:00:27,360 --> 00:00:29,820
把它就是要想完成一个数据传输

16
00:00:29,820 --> 00:00:32,320
那么它就得经过这所谓的七层模型

17
00:00:32,320 --> 00:00:34,700
我们大家可以看到这七层模型

18
00:00:34,700 --> 00:00:38,760
那每一层都代表自己不同的含义

19
00:00:38,760 --> 00:00:39,320
那这里的话

20
00:00:39,320 --> 00:00:40,840
我们在这注意

21
00:00:40,840 --> 00:00:42,520
为了便于大家学习

22
00:00:42,520 --> 00:00:43,420
我们在晚上可以看到

23
00:00:43,420 --> 00:00:46,520
就是说很多人还把网络划分为五层模型

24
00:00:46,520 --> 00:00:48,500
还划分为五层模型

25
00:00:48,500 --> 00:00:51,280
所以大家可以看到一个减板的这种中心模型

26
00:00:51,280 --> 00:00:54,220
但是这个模型从上到下

27
00:00:54,220 --> 00:00:57,320
从最开最上面的应用层到最下面的物理层

28
00:00:57,320 --> 00:00:59,120
然后对这个数据的通信

29
00:00:59,120 --> 00:01:01,180
进行了这个详细的协议规定

30
00:01:01,180 --> 00:01:02,380
那么每一层呢

31
00:01:02,380 --> 00:01:04,360
都有各自这个不同的含义

32
00:01:04,360 --> 00:01:07,060
那么它的一个实现

33
00:01:07,060 --> 00:01:07,760
分成好几层

34
00:01:07,760 --> 00:01:09,600
每一层而且都有自己的功能

35
00:01:09,600 --> 00:01:10,540
就像建筑物一样

36
00:01:10,540 --> 00:01:12,920
每一层都是靠下一层的一个支持

37
00:01:12,920 --> 00:01:14,800
其实我们用户接触到的

38
00:01:14,800 --> 00:01:15,800
只是最上面的一层

39
00:01:15,800 --> 00:01:17,540
我们用户根本感觉不到

40
00:01:17,540 --> 00:01:18,460
下面这一层

41
00:01:18,460 --> 00:01:19,820
例如这个物理线

42
00:01:19,820 --> 00:01:20,720
怎么去进行传输

43
00:01:20,720 --> 00:01:23,420
那么理解这个互联网

44
00:01:23,420 --> 00:01:24,580
那我在这呢

45
00:01:24,580 --> 00:01:25,180
推荐大家呢

46
00:01:25,180 --> 00:01:26,320
从最下层来开始

47
00:01:26,320 --> 00:01:27,360
从下往上

48
00:01:27,360 --> 00:01:29,100
我们去理解它每一层的一个功能

49
00:01:29,100 --> 00:01:30,660
那这里的话

50
00:01:30,660 --> 00:01:33,040
我们首先我们来看一下

51
00:01:33,040 --> 00:01:35,740
我们这个关于层与协议

52
00:01:35,740 --> 00:01:37,140
什么是层与协议呢

53
00:01:37,140 --> 00:01:38,180
我们每一层呢

54
00:01:38,180 --> 00:01:39,900
是为了完成某一种功能

55
00:01:39,900 --> 00:01:41,140
那为了这些功能呢

56
00:01:41,140 --> 00:01:42,820
那么大家就要遵守这些共同的规则

57
00:01:42,820 --> 00:01:44,380
我们都遵守的这种规则

58
00:01:44,380 --> 00:01:45,360
就称之为协议

59
00:01:45,360 --> 00:01:47,620
我们在这个网络通信当中

60
00:01:47,620 --> 00:01:49,220
有定义了很多的协议

61
00:01:49,220 --> 00:01:50,580
那这些协议的总称呢

62
00:01:50,580 --> 00:01:51,940
就称之为互联网协议

63
00:01:51,940 --> 00:01:55,200
那么首先我们来了解一下

64
00:01:55,200 --> 00:01:58,560
我们这个网络通信模型当中的实体层

65
00:01:58,560 --> 00:02:00,820
那么设施实体层非常简单

66
00:02:00,820 --> 00:02:03,580
就是我们计算机和计算机要想完成通信

67
00:02:03,580 --> 00:02:05,260
首先我们在这儿

68
00:02:05,260 --> 00:02:07,040
以这个要通过什么呢

69
00:02:07,040 --> 00:02:09,200
通过这种物理的方式去连接起来

70
00:02:09,200 --> 00:02:11,460
当然我们在这儿暂时不讨论那些

71
00:02:11,460 --> 00:02:13,600
当然也包括我们这个无线电波

72
00:02:13,600 --> 00:02:15,200
也就是说白了我们常见的这种

73
00:02:15,200 --> 00:02:16,000
比如光纤呀

74
00:02:16,000 --> 00:02:16,820
电缆呀

75
00:02:16,820 --> 00:02:17,700
这个双脚线呀

76
00:02:17,700 --> 00:02:19,020
就是这样的东西

77
00:02:19,020 --> 00:02:20,600
那么这个先连接起来以后

78
00:02:20,600 --> 00:02:22,400
那么这一层就称之为实体层

79
00:02:22,400 --> 00:02:24,300
它是把电脑连起来的一种物理手段

80
00:02:24,300 --> 00:02:25,460
其实它说白了

81
00:02:25,460 --> 00:02:27,560
就是说利用它来传输数据

82
00:02:27,560 --> 00:02:28,820
就是我们这些电信号

83
00:02:28,820 --> 00:02:29,680
例如零或者一

84
00:02:29,680 --> 00:02:31,800
所以这是实体层的一个作用

85
00:02:31,800 --> 00:02:33,880
那有了这个实体层

86
00:02:33,880 --> 00:02:35,700
就是说单纯的实体层

87
00:02:35,700 --> 00:02:37,600
没有任何意义

88
00:02:37,600 --> 00:02:38,800
单纯的实体层没有任何意义

89
00:02:38,800 --> 00:02:39,780
所以我们就是说

90
00:02:39,780 --> 00:02:42,840
必须规定这个实体层的解读方式

91
00:02:42,840 --> 00:02:44,920
那么例如多少个电信号

92
00:02:44,920 --> 00:02:46,240
例如这个零刻一算一组

93
00:02:46,240 --> 00:02:48,660
所以说我们大家就可以看到

94
00:02:48,660 --> 00:02:50,860
我们就有了所谓的链接层

95
00:02:50,860 --> 00:02:52,400
那么链接层的作用非常简单

96
00:02:52,400 --> 00:02:54,400
它位于我们这个实际层的上方

97
00:02:54,400 --> 00:02:56,080
它的作用就是用来规定

98
00:02:56,080 --> 00:02:58,720
我们这个零和一的分组方式

99
00:02:58,720 --> 00:02:59,640
那怎么去分组

100
00:02:59,640 --> 00:03:01,280
我们在早期的话呢

101
00:03:01,280 --> 00:03:02,560
我们发现各大公司

102
00:03:02,560 --> 00:03:03,720
就是各大生产厂商

103
00:03:03,720 --> 00:03:05,740
他们这个通信的分组方式都不太一样

104
00:03:05,740 --> 00:03:06,800
所以后来呢

105
00:03:06,800 --> 00:03:07,800
我们就可以看到啊

106
00:03:07,800 --> 00:03:10,800
我们到后来就有了一个叫做以太网协议

107
00:03:10,800 --> 00:03:12,340
那么这个以太网协议呢

108
00:03:12,340 --> 00:03:13,740
那么它就提出了一个标准

109
00:03:13,740 --> 00:03:15,700
那么它就规定一组电信号

110
00:03:15,700 --> 00:03:16,760
就成一个数据班

111
00:03:16,760 --> 00:03:18,020
我们把它称之为帧

112
00:03:18,020 --> 00:03:19,600
那么每一帧

113
00:03:19,600 --> 00:03:20,660
也就是说每一个数据班

114
00:03:20,660 --> 00:03:22,460
这个当中有两部分

115
00:03:22,460 --> 00:03:24,040
一个叫做数据的标头

116
00:03:24,040 --> 00:03:25,280
一个叫做数据本身

117
00:03:25,280 --> 00:03:27,660
那么这个标头用来干嘛的

118
00:03:27,660 --> 00:03:30,320
主要是用来包含一些说明项

119
00:03:30,320 --> 00:03:33,280
例如我这个数据是要发给谁的

120
00:03:33,280 --> 00:03:35,360
也就是发送者通常发过来的

121
00:03:35,360 --> 00:03:35,940
谁来接

122
00:03:35,940 --> 00:03:37,440
包括数据的类型

123
00:03:37,440 --> 00:03:38,860
那么data数据班

124
00:03:38,860 --> 00:03:40,340
就是这个数据本身的具体内容

125
00:03:40,340 --> 00:03:42,420
那么这个标头的长度

126
00:03:42,420 --> 00:03:44,220
那这里我们再做一个简单了解

127
00:03:44,220 --> 00:03:46,340
就是说当你的这个数据非常多的时候

128
00:03:46,340 --> 00:03:49,000
那么这个数据就会分割成多个帧

129
00:03:49,000 --> 00:03:49,720
去进行发送

130
00:03:49,720 --> 00:03:52,160
也就是一针一针一针的去进行发送

131
00:03:52,160 --> 00:03:54,860
所以这个就是我们常见的这个以太网协议

132
00:03:54,860 --> 00:03:57,620
那么有了这个以太网协议之后

133
00:03:57,620 --> 00:03:59,140
然后接下来我们来了解一下

134
00:03:59,140 --> 00:04:01,940
在链接层当中的Mac协议

135
00:04:01,940 --> 00:04:04,280
好那这个东西呢是用来干嘛的呢

136
00:04:04,280 --> 00:04:05,360
我们大家可以看到

137
00:04:05,360 --> 00:04:07,420
就是说以太网数据包的标头

138
00:04:07,420 --> 00:04:10,880
它包含了这个发送者和接受者的信息

139
00:04:10,880 --> 00:04:12,640
那发送的接受者怎么去标识

140
00:04:12,640 --> 00:04:14,000
通过什么东西来进行标记

141
00:04:14,000 --> 00:04:15,360
所以我们大家可以看到

142
00:04:15,360 --> 00:04:16,880
以太网当中的就规定

143
00:04:16,880 --> 00:04:18,500
接入网络的所有设备呢

144
00:04:18,500 --> 00:04:20,140
都必须有网卡接口

145
00:04:20,140 --> 00:04:22,560
也就是我们这个物理设备网卡

146
00:04:22,560 --> 00:04:24,100
那么这个数据包

147
00:04:24,100 --> 00:04:25,420
它必须就是说

148
00:04:25,420 --> 00:04:27,900
从A网卡传到B网卡

149
00:04:27,900 --> 00:04:28,860
那么网卡

150
00:04:28,860 --> 00:04:29,920
网卡本身呢

151
00:04:29,920 --> 00:04:31,520
是有这个所谓的地址的

152
00:04:31,520 --> 00:04:32,300
那么这个地址呢

153
00:04:32,300 --> 00:04:33,640
我们把它称之为Mac地址

154
00:04:33,640 --> 00:04:36,320
那这个地址大概长什么样呢

155
00:04:36,320 --> 00:04:37,680
我们在这可以来看一下

156
00:04:37,680 --> 00:04:39,420
每块物理设备网卡

157
00:04:39,420 --> 00:04:40,420
在出厂的时候呢

158
00:04:40,420 --> 00:04:41,500
它都有一个全世界

159
00:04:41,500 --> 00:04:43,420
就是独一无二的一个Mac地址

160
00:04:43,420 --> 00:04:44,580
那么它大概呢

161
00:04:44,580 --> 00:04:46,060
就是长这个样子

162
00:04:46,060 --> 00:04:47,560
那我们在这就简单了解

163
00:04:47,560 --> 00:04:48,380
就不管怎么着

164
00:04:48,380 --> 00:04:49,340
我们在就知道

165
00:04:49,340 --> 00:04:51,280
以太网协议当中就规定了

166
00:04:51,280 --> 00:04:53,460
我们这个首先把数据划分为

167
00:04:53,460 --> 00:04:55,660
就是一帧一帧的去听发送

168
00:04:55,660 --> 00:04:56,940
然后同时规定

169
00:04:56,940 --> 00:04:58,680
我们得规定什么呢

170
00:04:58,680 --> 00:05:00,920
这个数据当中得包含这个Mark地址

171
00:05:00,920 --> 00:05:01,620
也就是说完了

172
00:05:01,620 --> 00:05:02,940
这个Mark地址用来标记什么呢

173
00:05:02,940 --> 00:05:04,640
标记网卡的地址了

174
00:05:04,640 --> 00:05:06,360
有了这个网卡地址就可以去发出去

175
00:05:06,360 --> 00:05:08,900
那光这样就可以了吗

176
00:05:08,900 --> 00:05:10,160
当然还是不行的

177
00:05:10,160 --> 00:05:12,660
所以我们在这可以再继续往下来

178
00:05:12,660 --> 00:05:13,800
往下来

179
00:05:13,800 --> 00:05:14,700
然后我们再来看一下

180
00:05:14,700 --> 00:05:17,300
那么以太网既然知道对方Mark地址了

181
00:05:17,300 --> 00:05:19,560
那么怎么才能把这个数据去发过去呢

182
00:05:19,560 --> 00:05:21,260
我们待会就可以看到

183
00:05:21,260 --> 00:05:22,780
那一个网卡怎么知道

184
00:05:22,780 --> 00:05:24,000
另一个网卡的麦克地址

185
00:05:24,000 --> 00:05:25,140
就算有这个麦克地址

186
00:05:25,140 --> 00:05:26,460
那怎么把这个数据发过去

187
00:05:26,460 --> 00:05:27,860
那么这里我们就可以看到

188
00:05:27,860 --> 00:05:29,200
我们以太网采用了一种

189
00:05:29,200 --> 00:05:30,460
就是非常原始的方式

190
00:05:30,460 --> 00:05:32,040
它不是说把这个数据包

191
00:05:32,040 --> 00:05:33,120
直接给到这个接收方

192
00:05:33,120 --> 00:05:35,700
这个其实是不太容易做到的

193
00:05:35,700 --> 00:05:36,680
而是像什么呢

194
00:05:36,680 --> 00:05:37,960
我们这个以太网在这就是说

195
00:05:37,960 --> 00:05:39,500
直接把数据给所有

196
00:05:39,500 --> 00:05:41,000
就是当前网络当中的计算机

197
00:05:41,000 --> 00:05:43,060
全部都去给它进行发送

198
00:05:43,060 --> 00:05:45,720
我们让每台计算机来自己判断

199
00:05:45,720 --> 00:05:46,660
是否为接收方

200
00:05:46,660 --> 00:05:48,580
我们待会可以来举个例子

201
00:05:48,580 --> 00:05:49,680
我们待会可以来看一下

202
00:05:49,680 --> 00:05:51,940
例如在下面这张图当中

203
00:05:51,940 --> 00:05:53,260
一号计算机

204
00:05:53,260 --> 00:05:56,540
要向二号计算机发送数据

205
00:05:56,540 --> 00:05:57,520
那么同时

206
00:05:57,520 --> 00:05:58,820
三四五号计算机

207
00:05:58,820 --> 00:06:00,260
都会收到这个数据包

208
00:06:00,260 --> 00:06:02,180
然后其实这些计算机

209
00:06:02,180 --> 00:06:02,900
都会这么着呢

210
00:06:02,900 --> 00:06:04,320
读取到这个数据包的标头

211
00:06:04,320 --> 00:06:06,360
然后找到接收方的mark地址

212
00:06:06,360 --> 00:06:07,600
然后这些计算机

213
00:06:07,600 --> 00:06:08,460
拿到这个数据

214
00:06:08,460 --> 00:06:09,400
然后拿到mark地址

215
00:06:09,400 --> 00:06:10,780
和自己的这个mark地址

216
00:06:10,780 --> 00:06:11,340
进行比较

217
00:06:11,340 --> 00:06:12,560
如果相同

218
00:06:12,560 --> 00:06:13,740
那么他就接受这个数据包

219
00:06:13,740 --> 00:06:14,720
如果不同

220
00:06:14,720 --> 00:06:16,000
那么他就把这个包给丢掉

221
00:06:16,000 --> 00:06:18,260
那么这种发送数据的方式呢

222
00:06:18,260 --> 00:06:19,360
就称之为广播

223
00:06:19,360 --> 00:06:20,800
也就是链接层的广播

224
00:06:20,800 --> 00:06:23,280
那么有了这个数据包的定义

225
00:06:23,280 --> 00:06:24,260
网卡的麦克地址

226
00:06:24,260 --> 00:06:25,740
以及广播的发送方式

227
00:06:25,740 --> 00:06:27,160
实际上我们这个链接层

228
00:06:27,160 --> 00:06:29,960
就已经可以在这个多台计算机之间

229
00:06:29,960 --> 00:06:31,300
去进行传送数据了

230
00:06:31,300 --> 00:06:32,360
就已经就可以了

231
00:06:32,360 --> 00:06:35,300
我们大家就可以继续往下来

232
00:06:35,300 --> 00:06:36,800
然后这样能发

233
00:06:36,800 --> 00:06:37,480
那就可以了吗

234
00:06:37,480 --> 00:06:39,400
当然还是有一些小问题的

235
00:06:39,400 --> 00:06:41,000
我们接下来就可以来看一下

236
00:06:41,000 --> 00:06:42,140
有哪些问题

237
00:06:42,140 --> 00:06:44,120
那接下来我们就可以看到

238
00:06:44,120 --> 00:06:48,020
我们这个分层模型当中的网络层

239
00:06:48,020 --> 00:06:48,920
我们在可以看到

240
00:06:48,920 --> 00:06:50,560
就是说我们刚才的这种方式

241
00:06:50,560 --> 00:06:51,580
有很大的问题

242
00:06:51,580 --> 00:06:53,220
问题就在于什么呢

243
00:06:53,220 --> 00:06:55,560
以太阳采用广播的方式

244
00:06:55,560 --> 00:06:56,420
去发送数据包

245
00:06:56,420 --> 00:06:57,500
那这样的话

246
00:06:57,500 --> 00:06:59,060
就是所有成员人手一包

247
00:06:59,060 --> 00:07:00,400
这个不仅效率低

248
00:07:00,400 --> 00:07:02,080
而且仅仅局限在

249
00:07:02,080 --> 00:07:03,280
我们这个发送者的子网络

250
00:07:03,280 --> 00:07:04,400
也就是说

251
00:07:04,400 --> 00:07:05,940
如果我们这个

252
00:07:05,940 --> 00:07:07,240
例如我在A城市

253
00:07:07,240 --> 00:07:08,200
那你在B城市

254
00:07:08,200 --> 00:07:09,480
那这个时候就不行了

255
00:07:09,480 --> 00:07:09,960
也就是说白了

256
00:07:09,960 --> 00:07:12,000
我们必须得同处于一个局域网

257
00:07:12,000 --> 00:07:13,000
你才能使用这个

258
00:07:13,000 --> 00:07:14,380
以太网加这个广播的方式

259
00:07:14,380 --> 00:07:15,700
去把这个数据取得发过去

260
00:07:15,700 --> 00:07:16,720
所以就比较麻烦

261
00:07:16,720 --> 00:07:17,780
有很大的问题

262
00:07:17,780 --> 00:07:19,780
而且广播

263
00:07:19,780 --> 00:07:20,540
就是说

264
00:07:20,540 --> 00:07:21,740
如果两个计算机

265
00:07:21,740 --> 00:07:22,420
不在一个子网络

266
00:07:22,420 --> 00:07:23,780
广播它传不过去

267
00:07:23,780 --> 00:07:25,120
那这样肯定是OK的

268
00:07:25,120 --> 00:07:26,460
肯定是就是这种设计

269
00:07:26,460 --> 00:07:27,720
从本身设计的角度来讲

270
00:07:27,720 --> 00:07:28,460
这是对的

271
00:07:28,460 --> 00:07:29,320
因为大家可以想象一下

272
00:07:29,320 --> 00:07:31,780
如果你让世界上

273
00:07:31,780 --> 00:07:32,860
每一台这个计算机

274
00:07:32,860 --> 00:07:34,020
都来接受这个所有数据包

275
00:07:34,020 --> 00:07:35,640
那大家就可以想象一下

276
00:07:35,640 --> 00:07:36,360
这是不是肯定就会

277
00:07:36,360 --> 00:07:37,740
不太可能的呀

278
00:07:37,740 --> 00:07:38,420
好

279
00:07:38,420 --> 00:07:40,000
然后我们在这儿可以看到

280
00:07:40,000 --> 00:07:41,220
就是说互联网

281
00:07:41,220 --> 00:07:43,280
其实是由无数个子网络

282
00:07:43,280 --> 00:07:44,680
共同组成一个巨型网络

283
00:07:44,680 --> 00:07:46,340
所以你很难想象得到

284
00:07:46,340 --> 00:07:48,620
我们在上海和洛杉矶的电脑

285
00:07:48,620 --> 00:07:49,520
同属于一个网络

286
00:07:49,520 --> 00:07:51,060
那这个肯定不是的

287
00:07:51,060 --> 00:07:53,800
所以说我们大家就必须找到一种方式

288
00:07:53,800 --> 00:07:55,800
能区分这个mark地址

289
00:07:55,800 --> 00:07:57,520
它属于是哪一个子网络

290
00:07:57,520 --> 00:07:58,820
然后再去进行广播

291
00:07:58,820 --> 00:08:00,040
那些不是

292
00:08:00,040 --> 00:08:01,880
那怎么去进行区分呢

293
00:08:01,880 --> 00:08:03,000
我们大家就可以看到

294
00:08:03,000 --> 00:08:05,280
那如果是子网络

295
00:08:05,280 --> 00:08:06,500
就用广播方式发送

296
00:08:06,500 --> 00:08:07,340
否则就用路由

297
00:08:07,340 --> 00:08:08,400
当然路由的话

298
00:08:08,400 --> 00:08:09,320
我们大家就不设计

299
00:08:09,320 --> 00:08:11,500
那大家一定要知道

300
00:08:11,500 --> 00:08:13,040
Mac地址本身是做不到的

301
00:08:13,040 --> 00:08:14,300
那所以说就有了什么呢

302
00:08:14,300 --> 00:08:16,000
有了我们这个所谓的网络层

303
00:08:16,000 --> 00:08:18,100
那么网络层

304
00:08:18,100 --> 00:08:19,560
它就怎么样呢

305
00:08:19,560 --> 00:08:21,020
就是为我们这个计算机

306
00:08:21,020 --> 00:08:22,620
规定了一个新的地址

307
00:08:22,620 --> 00:08:23,780
也就是除了网卡

308
00:08:23,780 --> 00:08:25,660
这个本身的这个物理的Mac地址之外

309
00:08:25,660 --> 00:08:27,400
还有就是网络地址

310
00:08:27,400 --> 00:08:28,640
那这两个地址之间呢

311
00:08:28,640 --> 00:08:29,820
本身是没有任何关系的

312
00:08:29,820 --> 00:08:31,660
Mac地址绑在网卡上

313
00:08:31,660 --> 00:08:32,960
网络地址是由管理员

314
00:08:32,960 --> 00:08:34,500
或者是这个系统自动分配的

315
00:08:34,500 --> 00:08:36,140
那么接下来

316
00:08:36,140 --> 00:08:38,160
我们来看一下这个地址

317
00:08:38,160 --> 00:08:38,740
它的一个应用

318
00:08:38,740 --> 00:08:40,180
那么首先

319
00:08:40,180 --> 00:08:41,820
就是我们在网络层当中

320
00:08:41,820 --> 00:08:43,160
大家所熟知的

321
00:08:43,160 --> 00:08:44,180
这个叫IP协议

322
00:08:44,180 --> 00:08:46,780
也就是这个协议所规定的IP地址

323
00:08:46,780 --> 00:08:48,300
那么规定网络地址的协议

324
00:08:48,300 --> 00:08:49,540
就是IP协议

325
00:08:49,540 --> 00:08:51,920
它所定义的地址就成为IP地址

326
00:08:51,920 --> 00:08:53,660
我们目前的比较常用的

327
00:08:53,660 --> 00:08:55,480
用的比较多的是IP协议的第四版

328
00:08:55,480 --> 00:08:56,680
也就是IPv4

329
00:08:56,680 --> 00:08:58,160
那么这个版本当中就规定

330
00:08:58,160 --> 00:09:00,500
网址由32个二斤之位来组成

331
00:09:00,500 --> 00:09:02,080
那么它大概就是长这样

332
00:09:02,080 --> 00:09:04,160
当然我们习惯上的会把这个

333
00:09:04,160 --> 00:09:06,240
就是32个二斤之位

334
00:09:06,240 --> 00:09:08,280
分成四段的实进制

335
00:09:08,280 --> 00:09:09,700
来表示这个二金之位

336
00:09:09,700 --> 00:09:11,580
方便我们人类去阅读

337
00:09:11,580 --> 00:09:12,440
所以就是这样

338
00:09:12,440 --> 00:09:14,380
那么IP地址的一个作用

339
00:09:14,380 --> 00:09:15,600
它非常简单

340
00:09:15,600 --> 00:09:17,060
就是IP协议本身的

341
00:09:17,060 --> 00:09:18,980
就是为计算机分配IP地址

342
00:09:18,980 --> 00:09:21,080
然后确定哪些地址在同一个子网络

343
00:09:21,080 --> 00:09:22,260
那么这样的话

344
00:09:22,260 --> 00:09:23,600
我们就可以通过什么呢

345
00:09:23,600 --> 00:09:27,040
通过IP地址去找到这个对应的这个子网络

346
00:09:27,040 --> 00:09:28,200
然后通过MAC地址

347
00:09:28,200 --> 00:09:29,140
去进行这个广播

348
00:09:29,140 --> 00:09:31,400
发送到具体的子网络里面的某个计算机

349
00:09:31,400 --> 00:09:33,420
所以就这样来完成这个数据的

350
00:09:33,420 --> 00:09:34,380
就是找到

351
00:09:34,380 --> 00:09:35,040
找到对方

352
00:09:35,040 --> 00:09:36,520
然后去把这个数据去给它发回去

353
00:09:36,520 --> 00:09:37,440
好

354
00:09:37,440 --> 00:09:39,720
那这就是我们的这个IP协议的IP地址

355
00:09:39,720 --> 00:09:41,600
那么大家接着往下来

356
00:09:41,600 --> 00:09:44,180
那有了IP地址之后

357
00:09:44,180 --> 00:09:45,580
我们待着可以看一下

358
00:09:45,580 --> 00:09:46,360
我们IP

359
00:09:46,360 --> 00:09:48,960
它是怎么发送这个IP地址的

360
00:09:48,960 --> 00:09:50,200
也就是我们这个IP的数据包

361
00:09:50,200 --> 00:09:52,500
我们待着就可以往下来一下

362
00:09:52,500 --> 00:09:54,760
那有了这个IP地址

363
00:09:54,760 --> 00:09:55,540
接下来就是说

364
00:09:55,540 --> 00:09:57,660
那这个数据到达我们这个计算机了

365
00:09:57,660 --> 00:09:58,800
到达计算机以后

366
00:09:58,800 --> 00:09:59,720
大家可以想象一下

367
00:09:59,720 --> 00:10:01,900
我们计算机接收的网络数据

368
00:10:01,900 --> 00:10:03,360
是不是有各种各样的网络数据

369
00:10:03,360 --> 00:10:05,680
你有浏览网页的网络数据

370
00:10:05,680 --> 00:10:08,740
你有和你朋友去聊天的网络数据

371
00:10:08,740 --> 00:10:10,540
例如还有你去进行这个

372
00:10:10,540 --> 00:10:12,140
去购物的这个什么网络数据

373
00:10:12,140 --> 00:10:12,920
等等等等

374
00:10:12,920 --> 00:10:14,260
就是各种各样的网络数据

375
00:10:14,260 --> 00:10:15,380
是都通过这个网卡

376
00:10:15,380 --> 00:10:16,560
把数据给接收过来了

377
00:10:16,560 --> 00:10:18,780
那计算机怎么去识别

378
00:10:18,780 --> 00:10:19,800
哪些数据

379
00:10:19,800 --> 00:10:21,400
例如是给你这个网页用的

380
00:10:21,400 --> 00:10:23,780
哪些数据是给你这个聊天程序用的

381
00:10:23,780 --> 00:10:24,680
所以说这里的话

382
00:10:24,680 --> 00:10:25,980
我们就要用到一个新的东西

383
00:10:25,980 --> 00:10:26,620
叫做什么呢

384
00:10:26,620 --> 00:10:27,580
叫做端口号

385
00:10:27,580 --> 00:10:29,600
也就是这里我们有了一个新的

386
00:10:29,600 --> 00:10:30,640
就是除了网络层之外

387
00:10:30,640 --> 00:10:32,060
我们这有个叫做传输层

388
00:10:32,060 --> 00:10:32,960
传输团层当中

389
00:10:32,960 --> 00:10:33,820
有个非常重要的东西呢

390
00:10:33,820 --> 00:10:34,360
就是端口号

391
00:10:34,360 --> 00:10:35,760
什么是端口号呢

392
00:10:35,760 --> 00:10:36,660
端口号

393
00:10:36,660 --> 00:10:37,160
注意

394
00:10:37,160 --> 00:10:38,560
它的作用就是用来什么呢

395
00:10:38,560 --> 00:10:41,340
用来标识我们不同的应用程序的

396
00:10:41,340 --> 00:10:42,020
好

397
00:10:42,020 --> 00:10:43,240
所以说这样子我们就可以看到

398
00:10:43,240 --> 00:10:45,460
然后端口的是一个

399
00:10:45,460 --> 00:10:47,160
0到65535之间的一个整数

400
00:10:47,160 --> 00:10:50,560
那么0到1023是被系统所占用的

401
00:10:50,560 --> 00:10:52,080
我们在实际开发的时候

402
00:10:52,080 --> 00:10:52,740
最好不要去用

403
00:10:52,740 --> 00:10:54,880
那么我们自己开发过程当中

404
00:10:54,880 --> 00:10:55,860
最好去选择这些

405
00:10:55,860 --> 00:10:57,560
大于1023的这些端口号

406
00:10:57,560 --> 00:10:59,960
那不管是我们流浪网页

407
00:10:59,960 --> 00:11:01,700
还是这个使用这个聊天程序

408
00:11:01,700 --> 00:11:03,480
那么实际上这些软件

409
00:11:03,480 --> 00:11:04,260
就这些软件

410
00:11:04,260 --> 00:11:05,680
他们在进行网络通信的时候

411
00:11:05,680 --> 00:11:08,000
他们实际上会随机的选一个端口

412
00:11:08,000 --> 00:11:09,420
拿个服务端去进行通信

413
00:11:09,420 --> 00:11:10,820
所以它就是这样的

414
00:11:10,820 --> 00:11:12,720
所以说我们大家就可以看到

415
00:11:12,720 --> 00:11:14,620
我们传输层的一个功能

416
00:11:14,620 --> 00:11:18,240
实际上就是建立我们这个端口到端口的一个通信

417
00:11:18,240 --> 00:11:21,020
所以说我们相比之下

418
00:11:21,020 --> 00:11:24,840
我们网络层的功能是建立这个主机到主机的通信

419
00:11:24,840 --> 00:11:26,220
而数据发过来以后

420
00:11:26,220 --> 00:11:27,360
怎么去找到这个应用程序

421
00:11:27,360 --> 00:11:29,500
那这就是我们传输层要做的

422
00:11:29,500 --> 00:11:32,260
传输层就能帮你找到这个具体的应用程序本身

423
00:11:32,260 --> 00:11:35,480
所以说只要我们确定了这个主机和端口号

424
00:11:35,480 --> 00:11:38,360
那么我们就能实现这个程序之间的一个交流了

425
00:11:38,360 --> 00:11:42,100
所以大家可以看到我们常见的这个所谓的网络编程

426
00:11:42,100 --> 00:11:43,620
例如我们这个传输层的这个通信

427
00:11:43,620 --> 00:11:45,820
我们也把它称之为套接字通信

428
00:11:45,820 --> 00:11:47,080
或者是socket通信

429
00:11:47,080 --> 00:11:49,400
也就是说我们确定了主机和端口号

430
00:11:49,400 --> 00:11:51,660
再简单来说就是IP地址和端口号

431
00:11:51,660 --> 00:11:53,560
我们就能找到我们就能找到对方

432
00:11:53,560 --> 00:11:54,440
然后就进行通信了

433
00:11:54,440 --> 00:11:55,480
所以就是这样的

434
00:11:55,480 --> 00:11:58,860
所以说我们有时候把这个网络通信

435
00:11:58,860 --> 00:12:00,400
也称之为这个Socket通信

436
00:12:00,400 --> 00:12:01,360
为什么叫Socket

437
00:12:01,360 --> 00:12:02,720
因为Socket在英文当中

438
00:12:02,720 --> 00:12:04,120
就表示这个插座

439
00:12:04,120 --> 00:12:05,560
就是把他这个插座

440
00:12:05,560 --> 00:12:06,820
就是连上一条线

441
00:12:06,820 --> 00:12:08,000
去进行数据交互

442
00:12:08,000 --> 00:12:09,140
所以他就是这个意思

443
00:12:09,140 --> 00:12:11,320
好 我们待会在节奏上下来

444
00:12:11,320 --> 00:12:12,820
在这个传输层当中

445
00:12:12,820 --> 00:12:14,220
那数据能发送了

446
00:12:14,220 --> 00:12:15,420
那我们怎么去发

447
00:12:15,420 --> 00:12:16,820
那接下来我们再来看一下

448
00:12:16,820 --> 00:12:18,020
那这个就是我们的

449
00:12:18,020 --> 00:12:18,940
首先要了解一下

450
00:12:18,940 --> 00:12:20,600
这个叫做UDP协议

451
00:12:20,600 --> 00:12:22,140
那什么是UDP协议呢

452
00:12:22,140 --> 00:12:22,860
我们再可以看到

453
00:12:22,860 --> 00:12:24,020
就是说我们

454
00:12:24,020 --> 00:12:26,020
现在就必须在这个数据包当中

455
00:12:26,020 --> 00:12:28,020
就加入了这个端口的信息

456
00:12:28,020 --> 00:12:29,260
那么这个

457
00:12:29,260 --> 00:12:30,440
你要想往这个数据里面

458
00:12:30,440 --> 00:12:31,340
加端口信息

459
00:12:31,340 --> 00:12:32,880
那么我们待着就有个新的协议

460
00:12:32,880 --> 00:12:34,240
叫做UDP协议

461
00:12:34,240 --> 00:12:35,240
它就是我们在我们这个

462
00:12:35,240 --> 00:12:36,140
原本的数据之上

463
00:12:36,140 --> 00:12:37,080
加入一个端口号

464
00:12:37,080 --> 00:12:38,780
所以UDP的数据包

465
00:12:38,780 --> 00:12:39,680
大概就长这样

466
00:12:39,680 --> 00:12:41,240
一个头一个数据

467
00:12:41,240 --> 00:12:42,420
那标头定义了

468
00:12:42,420 --> 00:12:44,020
发出的端口和接收的端口

469
00:12:44,020 --> 00:12:45,320
也就是发出去的应用程序

470
00:12:45,320 --> 00:12:47,020
和接收方的应用程序

471
00:12:47,020 --> 00:12:48,940
然后数据就有具体的内容

472
00:12:48,940 --> 00:12:50,220
然后把UDP这个数据包呢

473
00:12:50,220 --> 00:12:51,780
放入这个IP数据包的数据部分

474
00:12:51,780 --> 00:12:53,960
然后就组成这样的一个数据格式

475
00:12:53,960 --> 00:12:55,020
然后去给它进行发送

476
00:12:55,020 --> 00:12:57,480
那这就是我们的UDP协议

477
00:12:57,480 --> 00:12:59,980
那UDP协议本身

478
00:12:59,980 --> 00:13:00,700
注意啊

479
00:13:00,700 --> 00:13:01,640
它本身呢

480
00:13:01,640 --> 00:13:02,960
不太可靠

481
00:13:02,960 --> 00:13:03,960
不可靠

482
00:13:03,960 --> 00:13:05,640
就是说这个数据发出去了

483
00:13:05,640 --> 00:13:06,520
我怎么知道

484
00:13:06,520 --> 00:13:07,400
对方有没有收到

485
00:13:07,400 --> 00:13:09,040
那对方把数据发回来了

486
00:13:09,040 --> 00:13:10,700
那就是对方怎么知道

487
00:13:10,700 --> 00:13:11,840
我是否真的收到了呢

488
00:13:11,840 --> 00:13:13,280
所以说它保证不了

489
00:13:13,280 --> 00:13:14,760
我们这个数据传输的一个

490
00:13:14,760 --> 00:13:16,040
完整和一致性

491
00:13:16,040 --> 00:13:18,160
所以说我们在传输层当中呢

492
00:13:18,160 --> 00:13:20,240
还有一个我们非常著名的

493
00:13:20,240 --> 00:13:21,740
这个叫TCP协议

494
00:13:21,740 --> 00:13:23,560
那什么是TCP协议呢

495
00:13:23,560 --> 00:13:24,980
那TCP它的全名是

496
00:13:24,980 --> 00:13:26,800
叫传输控制协议

497
00:13:26,800 --> 00:13:29,280
那它的一个作用呢

498
00:13:29,280 --> 00:13:31,140
就是说它是一个面向临接的

499
00:13:31,140 --> 00:13:32,040
可靠的

500
00:13:32,040 --> 00:13:33,220
就是基于自己流的一种

501
00:13:33,220 --> 00:13:35,140
传输层的一种通信协议

502
00:13:35,140 --> 00:13:37,840
那在OSI这种网络模型当中

503
00:13:37,840 --> 00:13:39,660
那么它属于是传输层的

504
00:13:39,660 --> 00:13:40,540
我们在这里就看到

505
00:13:40,540 --> 00:13:42,560
它属于传输层的一个协议

506
00:13:42,560 --> 00:13:44,520
那由于UDP协议本身

507
00:13:44,520 --> 00:13:46,020
就是传输数据不可靠

508
00:13:46,020 --> 00:13:47,720
所以说有这个TCP协议

509
00:13:47,720 --> 00:13:48,780
那么它最重要的一件事情

510
00:13:48,780 --> 00:13:50,440
就是保证了我们这个数据

511
00:13:50,440 --> 00:13:51,500
传输的一个可靠性

512
00:13:51,500 --> 00:13:52,460
就是说白了

513
00:13:52,460 --> 00:13:53,920
利用这个数据发出去

514
00:13:53,920 --> 00:13:55,300
这个数据是否完整呢

515
00:13:55,300 --> 00:13:56,140
那如果不完整

516
00:13:56,140 --> 00:13:57,500
对方可能就会直接丢弃

517
00:13:57,500 --> 00:13:59,800
然后再进行重新发送

518
00:13:59,800 --> 00:14:02,060
所以它就是这样的一个东西

519
00:14:02,060 --> 00:14:03,200
那我们在课程当中呢

520
00:14:03,200 --> 00:14:03,960
就会使用到

521
00:14:03,960 --> 00:14:05,760
我们Node中提供的Net模块

522
00:14:05,760 --> 00:14:07,580
来构建这个TCP协议

523
00:14:07,580 --> 00:14:08,940
当然我们待着也可以看到

524
00:14:08,940 --> 00:14:10,120
就是说那传输层

525
00:14:10,120 --> 00:14:11,820
就是说我们真正能接触到的

526
00:14:11,820 --> 00:14:12,600
实际上是什么呢

527
00:14:12,600 --> 00:14:14,360
我们就是抛开那些物理层不说

528
00:14:14,360 --> 00:14:15,920
我们开发人实际上能接触到

529
00:14:15,920 --> 00:14:17,080
就是这个UDP协议

530
00:14:17,080 --> 00:14:18,000
TCP协议

531
00:14:18,000 --> 00:14:18,800
主要就是这些

532
00:14:18,800 --> 00:14:20,560
那我们待着也可以看到

533
00:14:20,560 --> 00:14:21,380
那UDP不可靠

534
00:14:21,380 --> 00:14:22,060
TCP可靠

535
00:14:22,060 --> 00:14:22,860
那我们到底有哪个

536
00:14:22,860 --> 00:14:23,800
那是用可靠的呀

537
00:14:23,800 --> 00:14:25,120
注意还真不一定

538
00:14:25,120 --> 00:14:26,420
我们得看场景

539
00:14:26,420 --> 00:14:27,580
得看场景

540
00:14:27,580 --> 00:14:29,860
我们就是什么数据需要可靠呢

541
00:14:29,860 --> 00:14:31,620
例如我们这个网页

542
00:14:31,620 --> 00:14:32,320
网页的浏览

543
00:14:32,320 --> 00:14:33,760
我们需要保证这个数据的可靠性

544
00:14:33,760 --> 00:14:35,740
例如你不能说数据多了少了

545
00:14:35,740 --> 00:14:36,580
导致我这个网页

546
00:14:36,580 --> 00:14:37,840
例如可能这个数据展示

547
00:14:37,840 --> 00:14:39,300
数据的传输就有问题

548
00:14:39,300 --> 00:14:40,560
那就坏了

549
00:14:40,560 --> 00:14:41,320
那哪些东西

550
00:14:41,320 --> 00:14:43,140
例如可能就适用于UDP协议呢

551
00:14:43,140 --> 00:14:45,080
例如我们经常看到的这个

552
00:14:45,080 --> 00:14:46,640
例如这个视频的直播呀

553
00:14:46,640 --> 00:14:47,900
就是视频的直播呀

554
00:14:47,900 --> 00:14:48,980
这个语音通话呀

555
00:14:48,980 --> 00:14:49,860
那这种东西的话

556
00:14:49,860 --> 00:14:51,140
它就适合UDP协议

557
00:14:51,140 --> 00:14:51,540
就是说

558
00:14:51,540 --> 00:14:53,760
它不会说因为这个数据的丢失

559
00:14:53,760 --> 00:14:55,560
而导致去需要去重新建立连接

560
00:14:55,560 --> 00:14:57,060
然后再去做其他额外处理

561
00:14:57,060 --> 00:14:58,320
它顶多就是卡顿一下

562
00:14:58,320 --> 00:14:59,960
它不会导致这个连接失败

563
00:14:59,960 --> 00:15:01,260
而我们这个TCP协议

564
00:15:01,260 --> 00:15:02,260
有个非常重要的特性

565
00:15:02,260 --> 00:15:02,600
就是说

566
00:15:02,600 --> 00:15:03,340
一旦这个

567
00:15:03,340 --> 00:15:04,740
例如这个数据发送失败了

568
00:15:04,740 --> 00:15:05,600
那么它会怎么着呢

569
00:15:05,600 --> 00:15:06,820
它会重新发送

570
00:15:06,820 --> 00:15:07,720
它会重新发送

571
00:15:07,720 --> 00:15:09,220
所以说在不同场景下

572
00:15:09,220 --> 00:15:11,000
这些TCP或者是UDP

573
00:15:11,000 --> 00:15:12,580
它们有不同的使用场景

574
00:15:12,580 --> 00:15:14,040
好

575
00:15:14,040 --> 00:15:15,080
那TCP也好

576
00:15:15,080 --> 00:15:15,660
UDP也好

577
00:15:15,660 --> 00:15:16,880
这个在传输层的

578
00:15:16,880 --> 00:15:17,500
相对来讲

579
00:15:17,500 --> 00:15:18,520
还是比较底层的

580
00:15:18,520 --> 00:15:20,660
然后我们一般来讲

581
00:15:20,660 --> 00:15:21,440
我们真正的

582
00:15:21,440 --> 00:15:23,240
就是最上层用户能接触到的

583
00:15:23,240 --> 00:15:25,120
其实是我们常见的应用层

584
00:15:25,120 --> 00:15:26,320
也就是应用层

585
00:15:26,320 --> 00:15:27,680
也就是说传输层

586
00:15:27,680 --> 00:15:29,780
能负责帮你把这个数据去收到了

587
00:15:29,780 --> 00:15:31,220
那收到以后

588
00:15:31,220 --> 00:15:32,220
我们实际上还需要去

589
00:15:32,220 --> 00:15:34,520
制定这个数据的格式

590
00:15:34,520 --> 00:15:35,100
例如我们常见的

591
00:15:35,100 --> 00:15:36,480
例如我们常见的

592
00:15:36,480 --> 00:15:37,180
这个收发邮件

593
00:15:37,180 --> 00:15:38,260
网络浏览

594
00:15:38,260 --> 00:15:39,840
例如包括我们在

595
00:15:39,840 --> 00:15:41,780
使用到的一些硬件设备通信

596
00:15:41,780 --> 00:15:42,960
那这些通信的话

597
00:15:42,960 --> 00:15:43,820
他们也得怎么着呢

598
00:15:43,820 --> 00:15:44,960
去制定数据协议格式

599
00:15:44,960 --> 00:15:45,920
所以说

600
00:15:45,920 --> 00:15:47,360
这就是我们最上层的应用层

601
00:15:47,360 --> 00:15:49,240
那应用层的话

602
00:15:49,240 --> 00:15:51,180
就是应用程序收到传输层的数据

603
00:15:51,180 --> 00:15:52,060
这样就进行解读

604
00:15:52,060 --> 00:15:53,660
那在不同情况下

605
00:15:53,660 --> 00:15:54,680
有不同的应用层协议

606
00:15:54,680 --> 00:15:55,860
例如什么呢

607
00:15:55,860 --> 00:15:57,360
我们的email 邮箱

608
00:15:57,360 --> 00:15:59,020
这个电子邮件相关的协议

609
00:15:59,020 --> 00:16:00,180
什么SMTP

610
00:16:00,180 --> 00:16:01,220
3W

611
00:16:01,220 --> 00:16:02,800
我们这个网络相关的协议

612
00:16:02,800 --> 00:16:04,360
FTP文件上传协议

613
00:16:04,360 --> 00:16:05,220
等等等等

614
00:16:05,220 --> 00:16:06,720
那么这些协议就规定了

615
00:16:06,720 --> 00:16:07,960
我们这个电子邮件网页

616
00:16:07,960 --> 00:16:09,100
包括这个文件

617
00:16:09,100 --> 00:16:11,700
这个交互FTP数据的这个格式

618
00:16:11,700 --> 00:16:13,880
那这就是我们所谓的应用层

619
00:16:13,880 --> 00:16:15,240
那么这些应用层协议呢

620
00:16:15,920 --> 00:16:17,400
面向我们的这个用户的

621
00:16:17,400 --> 00:16:18,680
当然我们普通用户

622
00:16:18,680 --> 00:16:19,480
其实不需要了解

623
00:16:19,480 --> 00:16:20,160
最主要的是

624
00:16:20,160 --> 00:16:21,540
我们这些开发人员

625
00:16:21,540 --> 00:16:22,580
例如我们经常做这个

626
00:16:22,580 --> 00:16:23,540
服务端开发

627
00:16:23,540 --> 00:16:24,600
或者是客户端

628
00:16:24,600 --> 00:16:25,380
这个浏览器

629
00:16:25,380 --> 00:16:26,540
这个一面开发的时候

630
00:16:26,540 --> 00:16:27,480
我们经常接触到

631
00:16:27,480 --> 00:16:28,700
这个叫HTTP协议

632
00:16:28,700 --> 00:16:29,920
也就是网页相关的

633
00:16:29,920 --> 00:16:30,820
这个HTTP协议

634
00:16:30,820 --> 00:16:31,680
那么实际上

635
00:16:31,680 --> 00:16:32,580
他们都是基于

636
00:16:32,580 --> 00:16:33,520
我们的传输层

637
00:16:33,520 --> 00:16:35,220
也就是这个TCP协议

638
00:16:35,220 --> 00:16:36,120
而构建而来的

639
00:16:36,120 --> 00:16:36,760
因为他们要

640
00:16:36,760 --> 00:16:37,660
通过这个传输层

641
00:16:37,660 --> 00:16:38,820
去进行这个数据的传输

642
00:16:38,820 --> 00:16:40,800
那么应用层的数据

643
00:16:40,800 --> 00:16:42,240
就通过这种方式

644
00:16:45,920 --> 00:16:48,580
它的一个数据传输的一个格式

645
00:16:48,580 --> 00:16:50,940
那这里的话

646
00:16:50,940 --> 00:16:53,040
我们讲到这

647
00:16:53,040 --> 00:16:54,880
我们就大家大概就知道

648
00:16:54,880 --> 00:16:58,440
我们整个网络通信的一个基本的过程

649
00:16:58,440 --> 00:16:59,260
一个基本的过程

650
00:16:59,260 --> 00:17:01,100
然后我们回到

651
00:17:01,100 --> 00:17:02,920
回到我们之前的这里

652
00:17:02,920 --> 00:17:06,000
回到我们刚开始看到的

653
00:17:06,000 --> 00:17:07,320
这个网络七层模型这里

654
00:17:07,320 --> 00:17:08,780
好 这是七层模型

655
00:17:08,780 --> 00:17:10,460
实际上我们一般情况下

656
00:17:10,460 --> 00:17:11,140
为了好理解

657
00:17:11,140 --> 00:17:12,420
我们可以看这个五层模型

658
00:17:12,420 --> 00:17:14,040
我们在这这回大家就知道

659
00:17:15,920 --> 00:17:17,800
模型当中物理层啊

660
00:17:17,800 --> 00:17:19,460
就是连接起来的这个物理线

661
00:17:19,460 --> 00:17:21,420
或者是无线电波等等啊

662
00:17:21,420 --> 00:17:22,560
用来传输这个电信号

663
00:17:22,560 --> 00:17:24,180
就是01010的数据啊

664
00:17:24,180 --> 00:17:26,140
但是数据的零和一没有意义啊

665
00:17:26,140 --> 00:17:26,760
那怎么办呢

666
00:17:26,760 --> 00:17:28,520
所以说我们就有了这个数据链路层

667
00:17:28,520 --> 00:17:30,180
那么数据链路层呢

668
00:17:30,180 --> 00:17:31,300
就对这些零和一

669
00:17:31,300 --> 00:17:33,300
就是制定了一个就是规范

670
00:17:33,300 --> 00:17:34,880
让他把这个零和一呢

671
00:17:34,880 --> 00:17:36,060
去分成一组一组的

672
00:17:36,060 --> 00:17:36,880
那每一组呢

673
00:17:36,880 --> 00:17:37,740
我们就称之为一个真

674
00:17:37,740 --> 00:17:39,440
那么在这的话呢

675
00:17:39,440 --> 00:17:40,660
数据链路层这里啊

676
00:17:40,660 --> 00:17:42,820
就包括里面就包括了这个

677
00:17:42,820 --> 00:17:43,820
有了这个也就是

678
00:17:43,820 --> 00:17:44,540
以太网协议当中

679
00:17:44,540 --> 00:17:45,760
就规定了这个NAC地址

680
00:17:45,760 --> 00:17:47,980
IP TCP等等等等

681
00:17:47,980 --> 00:17:49,820
当然这个是从自上而下的

682
00:17:49,820 --> 00:17:50,580
自上而下的

683
00:17:50,580 --> 00:17:51,620
就是说当这个

684
00:17:51,620 --> 00:17:52,840
从应用层网下走的时候

685
00:17:52,840 --> 00:17:53,440
走到这的时候

686
00:17:53,440 --> 00:17:54,560
实际上里面就已经带

687
00:17:54,560 --> 00:17:55,620
携带了这些数据

688
00:17:55,620 --> 00:17:57,120
然后网络层呢

689
00:17:57,120 --> 00:17:58,800
就主要就是提供这个IP地址

690
00:17:58,800 --> 00:18:01,060
就是IP协议相关的一些内容

691
00:18:01,060 --> 00:18:02,720
然后在我们传输层当中呢

692
00:18:02,720 --> 00:18:03,580
主要是提供我们这个

693
00:18:03,580 --> 00:18:06,080
就是传输层就制定了这个数据

694
00:18:06,080 --> 00:18:06,860
就是怎么去

695
00:18:06,860 --> 00:18:08,340
就是给了这个端口号

696
00:18:08,340 --> 00:18:09,120
相关的一些规定

697
00:18:09,120 --> 00:18:10,480
就是说从应用程序

698
00:18:10,480 --> 00:18:13,120
到应用程序当中的一个规范的制定

699
00:18:13,120 --> 00:18:13,760
好了

700
00:18:13,760 --> 00:18:14,900
然后最后在应用层

701
00:18:14,900 --> 00:18:16,520
这个就是我们最上层的一个协议

702
00:18:16,520 --> 00:18:19,520
就是这个邮件的发送和接收

703
00:18:19,520 --> 00:18:20,940
包括这个网页的浏览

704
00:18:20,940 --> 00:18:21,660
等等

705
00:18:21,660 --> 00:18:23,440
就是其他的一些应用层相关的东西

706
00:18:23,440 --> 00:18:25,380
那这个就是我们

707
00:18:25,380 --> 00:18:28,320
从这个网络分层模型

708
00:18:28,320 --> 00:18:29,560
来简单了解一下

709
00:18:29,560 --> 00:18:32,220
我们这个网络是如何进行通信的

