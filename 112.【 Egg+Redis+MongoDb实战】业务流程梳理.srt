1
00:00:00,000 --> 00:00:01,980
好 那么我们了解CookSession之后

2
00:00:01,980 --> 00:00:03,780
其实基本上就可以进入咱们代码的编写

3
00:00:03,780 --> 00:00:04,640
那么编写之前

4
00:00:04,640 --> 00:00:06,420
其实我们还需要去梳理一下业务流程

5
00:00:06,420 --> 00:00:07,980
因为同学们得清晰的知道

6
00:00:07,980 --> 00:00:09,860
我们前后端是如何进行交互的

7
00:00:09,860 --> 00:00:10,680
首先我们得有思路

8
00:00:10,680 --> 00:00:11,560
再来去编码

9
00:00:11,560 --> 00:00:12,460
这样是不是会更加快

10
00:00:12,460 --> 00:00:13,740
而且有一句话这么说的

11
00:00:13,740 --> 00:00:15,480
我们其实在平时的开发中

12
00:00:15,480 --> 00:00:17,480
一般都是七分思考 三分编码

13
00:00:17,480 --> 00:00:18,720
其实我们这节目的重点

14
00:00:18,720 --> 00:00:20,320
如果说你能够把业务流程给梳理清楚

15
00:00:20,320 --> 00:00:21,520
把CookSession理解清楚

16
00:00:21,520 --> 00:00:23,520
那么其实编码简直就是小科

17
00:00:23,520 --> 00:00:25,860
接下来呢我们呢来简单的梳理一下咱们的一个业务流程

18
00:00:25,860 --> 00:00:28,680
首先呢我们的业务流程的主要核心呢分为三大块

19
00:00:28,680 --> 00:00:29,900
我们来看一下

20
00:00:29,900 --> 00:00:33,300
第一大块呢也就是我们的一个注册

21
00:00:33,300 --> 00:00:35,520
因为咱们你要登录肯定要注册嘛对吧

22
00:00:35,520 --> 00:00:37,100
第二块呢就是我们的一个登录

23
00:00:37,100 --> 00:00:38,240
包括了去整个set cookie

24
00:00:38,240 --> 00:00:39,760
包括去存储session的一个过程

25
00:00:39,760 --> 00:00:41,660
第三步也就是我们用户再次访问页面

26
00:00:41,660 --> 00:00:42,480
如何去保持一个登录

27
00:00:42,480 --> 00:00:43,360
其实就是这样的三大块

28
00:00:43,360 --> 00:00:44,240
我们只要搞清楚了

29
00:00:44,240 --> 00:00:45,380
咱们后面的编写非常简单

30
00:00:45,380 --> 00:00:46,880
好我们首先来看一下咱们的第一步

31
00:00:46,880 --> 00:00:47,820
如何去进行注册

32
00:00:47,820 --> 00:00:49,480
首先呢 新用户第一次访问我们的首页

33
00:00:49,480 --> 00:00:51,280
跳转了SIGN在一个页面进行注册

34
00:00:51,280 --> 00:00:52,140
然后呢 注册完成之后

35
00:00:52,140 --> 00:00:53,800
将数据存储到我们的一个mongodb里面

36
00:00:53,800 --> 00:00:55,240
好 我们来演示一下这一个过程

37
00:00:55,240 --> 00:00:56,600
首先呢 我这里推出登录

38
00:00:56,600 --> 00:00:58,300
我们来演示一下注册这样一个过程

39
00:00:58,300 --> 00:01:00,300
我们来访问SIGN在注册

40
00:01:00,300 --> 00:01:01,640
咱们注册一个什么样的账号呢

41
00:01:01,640 --> 00:01:04,220
比如说 传字 密码123456

42
00:01:04,220 --> 00:01:05,040
我们来点击注册

43
00:01:05,040 --> 00:01:06,360
好 这里呢 注册成功

44
00:01:06,360 --> 00:01:07,600
接下来我们来数据库里面看一下

45
00:01:07,600 --> 00:01:09,060
咱们存储的数据是怎么样的

46
00:01:09,060 --> 00:01:09,940
来 我们刷新一下

47
00:01:09,940 --> 00:01:13,360
大家可以看到在mongodb里面

48
00:01:13,360 --> 00:01:15,940
username这一行是不是有一个传字

49
00:01:15,940 --> 00:01:18,300
那么password里面呢有一个12356

50
00:01:18,300 --> 00:01:20,020
那么实际上在我们真正的项目里面

51
00:01:20,020 --> 00:01:21,900
我们的密码不是以名文的显示去存在的

52
00:01:21,900 --> 00:01:23,780
可能它会以MD5的显示去加密

53
00:01:23,780 --> 00:01:25,040
但这里呢不是我们这几个的重点

54
00:01:25,040 --> 00:01:26,980
好 这里呢其实就是我们数据库所存储的数据

55
00:01:26,980 --> 00:01:28,160
非常简单 username和password

56
00:01:28,160 --> 00:01:30,300
好 我们呢此时注册流程已经完成了

57
00:01:30,300 --> 00:01:33,140
接下来我们来看下一步

58
00:01:33,140 --> 00:01:34,380
那么我们注册完成之后

59
00:01:34,380 --> 00:01:35,540
是不是要进行咱们一个登录啊

60
00:01:35,540 --> 00:01:36,460
那么登录如何登录

61
00:01:36,460 --> 00:01:37,800
咱们输入用户名和密码

62
00:01:37,800 --> 00:01:42,000
然后呢进行教验和谁教验和谁教验是不是和我们的mongodb所存储的一个用户名和密码教验

63
00:01:42,000 --> 00:01:44,300
好那么如果说教验不通过那不用想你重新登录或者注册

64
00:01:44,300 --> 00:01:45,400
那么如果说通过了呢

65
00:01:45,400 --> 00:01:46,400
会生成一个token

66
00:01:46,400 --> 00:01:48,900
并且呢对客户端进行一个cookie

67
00:01:48,900 --> 00:01:50,600
也就是呢在客户端去种下一个cookie

68
00:01:50,600 --> 00:01:51,300
好那么当

69
00:01:51,300 --> 00:01:53,500
当这不完成之后

70
00:01:53,500 --> 00:01:55,500
那么刚才讲三线的时候

71
00:01:55,500 --> 00:01:56,700
是不是会以token为k

72
00:01:56,700 --> 00:01:58,800
那么有的同学可能不是很理解这个token是什么

73
00:01:58,800 --> 00:01:59,300
什么是token

74
00:01:59,300 --> 00:02:02,500
其实token大家可以理解为我们之前在讲义里面是不是讲到了

75
00:02:02,600 --> 00:02:06,180
我们会以username为k就在数据库里面去存储一块单独的数据

76
00:02:06,180 --> 00:02:09,260
其实大家可以理解为token其实就是我们加密的

77
00:02:09,260 --> 00:02:14,640
加密的过的用户名因为我们去生成这样一个token 不希望可能会产生一些安全性问题

78
00:02:14,640 --> 00:02:20,260
所以说我们叫做token其实大家可以去完全可以把它理解为他其实就是对用户信息的一种加密

79
00:02:20,260 --> 00:02:22,560
你可以把它理解为username也可以

80
00:02:22,560 --> 00:02:26,920
我们以token为k存储用户信息到redis服务器

81
00:02:26,920 --> 00:02:28,720
好 我们来看一下这样一个过程

82
00:02:30,240 --> 00:02:32,760
好 我们刚才是不是已经注册了一个传字的账号 此时咱们来访问

83
00:02:32,760 --> 00:02:34,440
login 也就是登录

84
00:02:34,440 --> 00:02:37,120
传字 立马123456

85
00:02:37,120 --> 00:02:39,860
大家可以看到咱们页面中是不是已经出现了一个fook

86
00:02:39,860 --> 00:02:43,580
那么它是如何去种下这个fook的过程 其实咱们前面是不是已经演示过来了

87
00:02:43,580 --> 00:02:46,740
我们重点是不是需要去看一下redis里面存储的k为什么

88
00:02:46,740 --> 00:02:49,240
是不是redis存储的k为这样一个value啊 这样一串token

89
00:02:49,240 --> 00:02:51,120
好 我们来看一下redis里面存储的到底是什么数据

90
00:02:51,120 --> 00:02:55,860
16BB49 好 咱们来看一下redis的一个数据库

91
00:02:55,860 --> 00:02:59,120
好 刷新 大家可以看到16BB49 对吧

92
00:03:00,080 --> 00:03:01,620
16BB19我们来看一下数据存的什么

93
00:03:01,620 --> 00:03:04,180
这里呢在redis里面的存储一个对象

94
00:03:04,180 --> 00:03:04,940
里面的值

95
00:03:04,940 --> 00:03:06,480
为login

96
00:03:06,480 --> 00:03:08,520
Q也就是代表用户此时已经登录了

97
00:03:08,520 --> 00:03:12,620
Xpire这里其实是一个过期时间包括maxage都是去存储用户的一些过期时间

98
00:03:12,620 --> 00:03:14,420
当然因为我们的项目设计很简单

99
00:03:14,420 --> 00:03:17,740
你呢也可以在一个对象里面去添加一些购物车全线信息菜单信息等等

100
00:03:17,740 --> 00:03:20,040
咱们之前也讲过了session所解决的一些问题

101
00:03:20,040 --> 00:03:22,360
那么在redis里面你爱存多少内容存多少内容

102
00:03:22,360 --> 00:03:25,940
那么这样一个键其实就是代表咱们刚才所输入传字这样一个用户

103
00:03:25,940 --> 00:03:27,480
那么比如说我这里再举一个例子

104
00:03:28,240 --> 00:03:32,840
我们这里呢 登陆一个新账号 再来观察一下Redis里面所存储的数据 比如说我们登陆ABC

105
00:03:32,840 --> 00:03:35,400
一二三四五六 刚才咱们登陆的是船字吧 咱们登陆一个ABC

106
00:03:35,400 --> 00:03:37,200
Messing CSRF Token

107
00:03:37,200 --> 00:03:38,740
好 没关系 我们重新登陆一下

108
00:03:38,740 --> 00:03:43,080
ABC一二三四五六 登陆

109
00:03:43,080 --> 00:03:44,880
大家可以看到咱们是不是创建了一个

110
00:03:44,880 --> 00:03:48,720
新的Token叫做6C22B 咱们来看一下Redis里面的一个数据

111
00:03:48,720 --> 00:03:50,000
刷新一下

112
00:03:50,000 --> 00:03:51,800
大家可以发现咱们Redis里面是不是

113
00:03:51,800 --> 00:03:52,820
新增了一条

114
00:03:52,820 --> 00:03:55,640
Token其实它就代表咱们刚才说 登陆了一个

115
00:03:55,640 --> 00:03:56,400
ABC

116
00:03:57,160 --> 00:03:58,140
好,login也为Q

117
00:03:58,140 --> 00:04:00,700
所以说这里呢,大家可以得出一个结论

118
00:04:00,700 --> 00:04:04,940
我们每个用户登录的时候,那么在redis里面都会存一个token为K

119
00:04:04,940 --> 00:04:07,700
那么这样一个K其实就是代表咱们登录了当前的这样一个用户

120
00:04:07,700 --> 00:04:09,900
那么这样一个用户里面其实会存储一些咱们的用户信息

121
00:04:09,900 --> 00:04:12,260
好,那么这里呢,其实就是咱们redis的一个作用

122
00:04:12,260 --> 00:04:14,040
它去存储咱们的用户信息

123
00:04:14,040 --> 00:04:16,160
好,接下来我们再来看一下

124
00:04:16,160 --> 00:04:19,220
第三块,也就是咱们再次访问页面

125
00:04:19,220 --> 00:04:21,360
如何去保持咱们的登录状态

126
00:04:21,360 --> 00:04:23,440
好,当我们用户再次访问首页的时候

127
00:04:23,440 --> 00:04:25,220
也就是咱们在走页刷新

128
00:04:25,220 --> 00:04:27,520
咱们将内蒙为1GGC型的Value

129
00:04:27,520 --> 00:04:29,760
Value为Token的一个cookie带到服气

130
00:04:29,760 --> 00:04:31,240
也就是把什么内容呢

131
00:04:31,240 --> 00:04:32,340
也就是把这类一串

132
00:04:32,340 --> 00:04:33,540
是不是非常奇怪的一个Token

133
00:04:33,540 --> 00:04:34,760
带到咱们的服务端

134
00:04:34,760 --> 00:04:35,540
好

135
00:04:35,540 --> 00:04:36,860
那么带到服务端之后呢

136
00:04:36,860 --> 00:04:38,220
去查询Redis数据库

137
00:04:38,220 --> 00:04:39,220
K为Token的对象

138
00:04:39,220 --> 00:04:40,580
它的login值是否为Q

139
00:04:40,580 --> 00:04:41,940
那么如果说它的login值为Q

140
00:04:41,940 --> 00:04:42,580
说明呢

141
00:04:42,580 --> 00:04:44,000
此时这样一个用户已经登陆了

142
00:04:44,000 --> 00:04:45,860
那么如果说为Force或者说不存在

143
00:04:45,860 --> 00:04:47,020
说明你这样一个用户没有登陆

144
00:04:47,020 --> 00:04:48,520
那么呢你需要去进行重新的登陆

145
00:04:48,520 --> 00:04:49,100
那么这里呢

146
00:04:49,100 --> 00:04:50,300
其实就是咱们整个登陆流程

147
00:04:50,300 --> 00:04:50,980
比较核心的商块

148
00:04:50,980 --> 00:04:52,600
我们简单的来进行一个回顾

149
00:04:52,600 --> 00:04:54,040
首先我们第一块

150
00:04:54,040 --> 00:04:54,820
也就是进行注册

151
00:04:54,820 --> 00:04:55,580
注册完成之后

152
00:04:55,580 --> 00:04:56,120
数据存储了

153
00:04:56,120 --> 00:04:56,940
咱们的mongodb里面

154
00:04:56,940 --> 00:04:59,300
然后我们通过刚才所注册的账号

155
00:04:59,300 --> 00:04:59,960
来进行登录

156
00:04:59,960 --> 00:05:00,980
那么教育通过之后

157
00:05:00,980 --> 00:05:01,620
会生成一个token

158
00:05:01,620 --> 00:05:02,420
那么这样一个token

159
00:05:02,420 --> 00:05:03,840
其实就是代表我们的用户信息

160
00:05:03,840 --> 00:05:05,780
那么我们生成token之后

161
00:05:05,780 --> 00:05:06,780
会基于这样一个token

162
00:05:06,780 --> 00:05:08,320
在realish里面去存储一些信息

163
00:05:08,320 --> 00:05:09,720
比如说我们这个项目

164
00:05:09,720 --> 00:05:10,580
存储数据非常简单

165
00:05:10,580 --> 00:05:12,120
就是一个login为2

166
00:05:12,120 --> 00:05:13,520
然后还会有一些过期信息

167
00:05:13,520 --> 00:05:13,960
当然了

168
00:05:13,960 --> 00:05:14,720
如果说复杂的系统

169
00:05:20,300 --> 00:05:21,740
就可以去保持咱们登陆态

170
00:05:21,740 --> 00:05:22,680
比如说我们再次访问

171
00:05:22,680 --> 00:05:23,760
我们的一个首页刷新页面

172
00:05:23,760 --> 00:05:24,560
此时呢

173
00:05:24,560 --> 00:05:25,560
我们是不是会携带一个cookie

174
00:05:25,560 --> 00:05:26,440
传递到咱们呢

175
00:05:26,440 --> 00:05:27,480
传递到咱们的一个服务端

176
00:05:27,480 --> 00:05:27,760
好

177
00:05:27,760 --> 00:05:28,780
那么我们服务端拿到咱们

178
00:05:28,780 --> 00:05:30,520
客户端传递过来一个token之后

179
00:05:30,520 --> 00:05:32,620
此时就会去realish数据库里面去查询

180
00:05:32,620 --> 00:05:34,380
它里面有没有一个可以为token的对象

181
00:05:34,380 --> 00:05:35,420
里面它的token的值为q

182
00:05:35,420 --> 00:05:36,600
那么如果说为q说明呢

183
00:05:36,600 --> 00:05:38,160
这个用户已经登陆过了

184
00:05:38,160 --> 00:05:39,560
所以我们页面可以正常的访问

185
00:05:39,560 --> 00:05:39,860
这里呢

186
00:05:39,860 --> 00:05:41,760
就是我们整个项目的一个业务流程的一个疏离

187
00:05:41,760 --> 00:05:45,160
如果说大家把这样一块内容所理解了

188
00:05:45,160 --> 00:05:47,240
可以说咱们的这样一个项目实战

189
00:05:47,240 --> 00:05:48,760
大家已经学会了70%

190
00:05:48,760 --> 00:05:52,400
因为你理解的思想编码就非常容易了

191
00:05:52,400 --> 00:05:54,540
那么这里就是我们这节课的内容

