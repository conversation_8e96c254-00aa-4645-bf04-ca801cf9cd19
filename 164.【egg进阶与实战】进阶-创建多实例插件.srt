1
00:00:00,000 --> 00:00:02,000
好 这节课我们就来看一下多实力插件

2
00:00:02,000 --> 00:00:04,000
那么多实力插件是什么回事呢

3
00:00:04,000 --> 00:00:06,000
我们之前是不是写过一些插件呢

4
00:00:06,000 --> 00:00:08,000
比如说我们之前是不是写过A和B

5
00:00:08,000 --> 00:00:10,000
我们为了演示它们之间的依赖关系

6
00:00:10,000 --> 00:00:11,000
那么A和B它到底做的什么事情呢

7
00:00:11,000 --> 00:00:13,000
是不是就在我们的extend

8
00:00:13,000 --> 00:00:16,000
也就是给我们的context去扩展一些属性

9
00:00:16,000 --> 00:00:19,000
那么这里呢我们之前为了演示插件去做的这样一个操作

10
00:00:19,000 --> 00:00:22,000
好 那么接下来多实力插件它是解决一个什么问题呢

11
00:00:22,000 --> 00:00:25,000
那么许多的插件它的目的就是相依先已有的服务引用的框架中

12
00:00:25,000 --> 00:00:27,000
那么比如说我们的egg买soco

13
00:00:27,000 --> 00:00:28,000
是不是为了去操作数据库

14
00:00:28,000 --> 00:00:30,820
包括了我们之前是不是也使用过1GG-LongJunk

15
00:00:30,820 --> 00:00:32,360
去使用它的一个模板方法

16
00:00:32,360 --> 00:00:34,920
他们呢其实都需要在APP上面去创建对应的实例

17
00:00:34,920 --> 00:00:35,680
那么呢

18
00:00:35,680 --> 00:00:39,260
1GG在那个团队在开发这一类插件的时候呢发现一些问题

19
00:00:39,260 --> 00:00:42,340
比如说在一个应用中需要使用同一个服务不同的实例

20
00:00:42,340 --> 00:00:46,180
什么意思其实也就是说在我们的一个应用中需要去连接多个买收口数据

21
00:00:46,180 --> 00:00:48,220
那么因为我们的数据库呢他

22
00:00:48,220 --> 00:00:51,040
可能会存储海量的数据那么你一个库可能存不下

23
00:00:51,040 --> 00:00:53,340
可能需要去有多需要连接多个数据库

24
00:00:53,340 --> 00:00:54,360
这是第一种情况

25
00:00:54,360 --> 00:00:55,640
那么还有一种情况是什么呢

26
00:00:55,900 --> 00:01:21,900
要

27
00:01:21,900 --> 00:01:22,900
所以

28
00:01:22,900 --> 00:01:25,660
所以呢我们呢需要在插件里面去做这样的一个初始化

29
00:01:25,660 --> 00:01:29,020
但是你如果让各自的插件自己去实现

30
00:01:29,020 --> 00:01:30,180
是不是会造成一些问题啊

31
00:01:30,180 --> 00:01:32,500
你比如说我今天开发了一个1GG买收口

32
00:01:32,500 --> 00:01:34,500
你呢又开发了另外一个根据你自己的业务场景

33
00:01:34,500 --> 00:01:36,040
那么这样长此以往是不是会乱透

34
00:01:36,040 --> 00:01:37,820
所以框架提供了这样一个方法

35
00:01:37,820 --> 00:01:38,500
什么方法呢

36
00:01:38,500 --> 00:01:40,120
app.add

37
00:01:40,120 --> 00:01:43,040
app.add

38
00:01:43,040 --> 00:01:44,220
synctone

39
00:01:44,220 --> 00:01:45,780
那么add synctone是什么意思呢

40
00:01:45,780 --> 00:01:46,920
其实是添加单力的意思

41
00:01:46,920 --> 00:01:48,400
那么通过这样一个方法呢

42
00:01:48,400 --> 00:01:49,860
就可以来统一我们这一类服务的创建

43
00:01:49,860 --> 00:01:50,940
那么需要注意的是

44
00:01:50,940 --> 00:01:52,420
使用这样一个方法的时候呢

45
00:01:52,420 --> 00:01:57,220
你在配置文件中一定要有client或者client作为可以的配置作为传入咱们一个creator函数的config

46
00:01:57,220 --> 00:01:59,080
好 那么我们接下来就要看一下

47
00:01:59,080 --> 00:02:03,180
这样的一个方法它到底是怎么样去使用的 这里呢 我就来给同学们去演示一下

48
00:02:03,180 --> 00:02:07,260
假如说我们模拟去写一个我们买seql的一个插件

49
00:02:07,260 --> 00:02:09,320
首先刚才提到的它是不是在

50
00:02:09,320 --> 00:02:13,420
APP下面去进行一个什么方法 是不是add

51
00:02:13,420 --> 00:02:17,000
singlepone这样的方法 那么它这样一个方法的意思是什么呀

52
00:02:17,000 --> 00:02:18,540
其实就是在

53
00:02:18,540 --> 00:02:21,860
它接受两个参数 第一个是一个字符创 第二个是方法

54
00:02:22,420 --> 00:02:26,340
比如说我们去传入一个什么呢 因为我们现在 假说我们现在需要去制作一个什么呀

55
00:02:26,340 --> 00:02:28,820
 是不是制作一个MySQL的插件 比如说我们叫做MySQL

56
00:02:28,820 --> 00:02:31,900
然后呢 咱们呢 去定义一个create

57
00:02:31,900 --> 00:02:36,380
sql的一个方法 那么他呢 就是真正的去创建

58
00:02:36,380 --> 00:02:39,320
创建咱们MySQL实力的一个

59
00:02:39,320 --> 00:02:40,600
方法

60
00:02:40,600 --> 00:02:43,420
创建

61
00:02:43,420 --> 00:02:47,500
MySQL实力 好 那么这里呢 咱们第一个参数

62
00:02:48,020 --> 00:02:52,430
m my circle他做的他其实做了什么事情呢他其实就是在app上面挂展的my

63
00:02:52,430 --> 00:02:54,160
 circle这样一个属性让你可以通过my circle

64
00:02:54,160 --> 00:02:56,220
app.my circle去调用我们这样的一个

65
00:02:56,220 --> 00:02:57,240
实力好

66
00:02:57,240 --> 00:03:00,300
那么create my circle这里呢他其实接受两个参数一个是config

67
00:03:00,300 --> 00:03:05,680
config呢就是从我们配置文件所传过来一个对象我们可以去读取我们的读取我们的配置然后去做一些定制化

68
00:03:05,680 --> 00:03:07,220
第2个参数呢就是咱们的一个app

69
00:03:07,220 --> 00:03:10,800
那么这里我们先来retain一个空的对象咱们来模拟

70
00:03:10,800 --> 00:03:12,340
假装

71
00:03:12,340 --> 00:03:13,360
假装是

72
00:03:13,360 --> 00:03:14,900
my circle的

73
00:03:15,160 --> 00:03:18,470
实力 因为我们的重点其实是介绍什么 介绍 AD SINGLE LUTEN 这样的一个方法

74
00:03:18,470 --> 00:03:21,300
 我们并不是去实力化一个 MySQL 好 那么这里呢

75
00:03:21,300 --> 00:03:22,840
咱们就通过啊

76
00:03:22,840 --> 00:03:27,200
带了一个方法 咱们创建一个 MySQL 的实力 我们来打印一下 我们来打印一下看

77
00:03:27,200 --> 00:03:29,500
 带一个方法 他到底能不能够执行 那么如果说

78
00:03:29,500 --> 00:03:34,250
咱们的 CREATE SQL 他如果说能够去执行的话 就说明我们是不是已经创建成功了

79
00:03:34,250 --> 00:03:34,360
 好

80
00:03:34,360 --> 00:03:36,920
我们来尝试一下 看他们能不能

81
00:03:36,920 --> 00:03:37,680
打印出来

82
00:03:37,680 --> 00:03:39,740
好 那么我们来

83
00:03:39,740 --> 00:03:41,280
乱一下

84
00:03:42,800 --> 00:03:44,340
其实呢大家可以看到是不是没有找到

85
00:03:44,340 --> 00:03:47,160
没有找到我们这样一些遗传感叹号啊说明什么问题是不是我们create

86
00:03:47,160 --> 00:03:48,180
sql他没有执行

87
00:03:48,180 --> 00:03:50,220
那么难道我们是使用错了吗

88
00:03:50,220 --> 00:03:52,020
其实文档里面确实是介绍了什么呢

89
00:03:52,020 --> 00:03:52,780
咱们是不是在

90
00:03:52,780 --> 00:03:54,060
app.js里面

91
00:03:54,060 --> 00:03:54,580
对吧

92
00:03:54,580 --> 00:03:57,400
然后呢通过construct里面app调用add single letter

93
00:03:57,400 --> 00:03:59,180
然后呢按照我们的讲义里面去做的

94
00:03:59,180 --> 00:04:00,460
那么为什么没有执行

95
00:04:00,460 --> 00:04:02,000
其实大家可以注意看这样一句话

96
00:04:02,000 --> 00:04:05,840
需要注意的是你在使用这样一个方法时配置文件中一定要有

97
00:04:05,840 --> 00:04:08,400
client或者client作为k的配置作为传入

98
00:04:08,400 --> 00:04:09,940
咱们这样一个函数的config

99
00:04:09,940 --> 00:04:11,720
什么意思其实呢

100
00:04:11,980 --> 00:04:14,280
就是需要我们在config里面

101
00:04:14,280 --> 00:04:15,820
去做一层配置

102
00:04:15,820 --> 00:04:17,860
因为我们比如说你要去写个mysoco的插件

103
00:04:17,860 --> 00:04:19,400
你肯定需要去传入一些配置

104
00:04:19,400 --> 00:04:22,220
那么你没有配置的插件其实无法是无法去出使话的

105
00:04:22,220 --> 00:04:23,240
那么我们传入配置

106
00:04:23,240 --> 00:04:24,520
重新启动一下

107
00:04:24,520 --> 00:04:26,060
咱们

108
00:04:26,060 --> 00:04:27,340
重新启动一下

109
00:04:27,340 --> 00:04:33,740
其实这里大家是不是就可以看到了

110
00:04:33,740 --> 00:04:35,280
大家是不是可以看到

111
00:04:35,280 --> 00:04:38,100
我们的create mysoco的方法是不是已经执行了

112
00:04:38,100 --> 00:04:40,140
然后它的配置文件是不是就是我们

113
00:04:40,900 --> 00:04:43,080
从config里面去读取的

114
00:04:43,080 --> 00:04:43,420
对吧

115
00:04:43,420 --> 00:04:45,100
对吧

116
00:04:45,100 --> 00:04:45,800
它们两个是一样的

117
00:04:45,800 --> 00:04:47,360
那么那你就可以通过去读取配置

118
00:04:47,360 --> 00:04:47,660
然后呢

119
00:04:47,660 --> 00:04:50,040
你手动的去实力化一个咱们的mysoco的那个实力

120
00:04:50,040 --> 00:04:50,520
好

121
00:04:50,520 --> 00:04:50,860
这里呢

122
00:04:50,860 --> 00:04:51,720
其实就是这样的一个属性

123
00:04:51,720 --> 00:04:51,960
它

124
00:04:51,960 --> 00:04:53,220
AdSyncTone

125
00:04:53,220 --> 00:04:54,200
在那个方法所做的一个事情

126
00:04:54,200 --> 00:04:57,260
刚才我们是不是实际上是实现的一个什么呀

127
00:04:57,260 --> 00:04:59,840
是不是实际上实现的是一个单实力

128
00:04:59,840 --> 00:05:01,060
那么单实力呢

129
00:05:01,060 --> 00:05:03,260
咱们通过config去配置了咱们的一个client

130
00:05:03,260 --> 00:05:04,080
然后传出去参数

131
00:05:04,080 --> 00:05:04,540
此时呢

132
00:05:04,540 --> 00:05:05,580
就可以创建我们的一个什么呀

133
00:05:05,580 --> 00:05:08,940
是不是在app上面去挂扎的一个mysoco这样的一个对象

134
00:05:08,940 --> 00:05:10,720
他呢我们就可以通过APP.NET

135
00:05:10,720 --> 00:05:12,220
然后呢去操作我们数据库

136
00:05:12,220 --> 00:05:13,520
通过query对吧

137
00:05:13,520 --> 00:05:14,180
query

138
00:05:14,180 --> 00:05:16,680
query其实就是咱们去操作数据库一系列API

139
00:05:16,680 --> 00:05:18,160
那么这里呢就是通过

140
00:05:18,160 --> 00:05:20,300
啊这个方法就操作我们的单实力

141
00:05:20,300 --> 00:05:23,200
那么我们接下来来看一下多实力是怎么样去操作

142
00:05:23,200 --> 00:05:24,860
我们来看一下

143
00:05:24,860 --> 00:05:28,180
那么呢多实力我们是不是需要去

144
00:05:28,180 --> 00:05:29,160
配置一个什么呢

145
00:05:29,160 --> 00:05:30,140
首先我们要配这个client

146
00:05:30,140 --> 00:05:31,440
client代表复数

147
00:05:31,440 --> 00:05:32,780
那么呢你在里面去传入db1

148
00:05:32,780 --> 00:05:34,240
db2你有多少个实力啊

149
00:05:34,240 --> 00:05:35,500
你就去配置多少个

150
00:05:35,500 --> 00:05:36,680
比如说user是什么啊

151
00:05:36,680 --> 00:05:37,140
密码是什么

152
00:05:37,140 --> 00:05:38,340
然后你的数据库名称是什么

153
00:05:38,340 --> 00:05:39,480
那么咱们在创建的时候

154
00:05:39,480 --> 00:05:41,080
他呢就会自动的帮你去创建多个实力

155
00:05:41,080 --> 00:05:42,300
那么怎么样去使用呢

156
00:05:42,300 --> 00:05:44,360
比如说你app.mysoco

157
00:05:44,360 --> 00:05:45,580
此时mysoco他的

158
00:05:45,580 --> 00:05:47,440
此时app.mysoco是不是一个多实力

159
00:05:47,440 --> 00:05:49,380
那么你此时是不是在选择你要操作哪个数据库

160
00:05:49,380 --> 00:05:51,400
那么呢你就可以通过getdb

161
00:05:51,400 --> 00:05:54,280
哪个db然后再去进行后面的操作

162
00:05:54,280 --> 00:05:56,220
那么这里呢就是mysoco他

163
00:05:56,220 --> 00:05:58,300
我们去进行多实力一个操作

164
00:05:58,300 --> 00:06:00,860
好 接下来呢我们来看一下

165
00:06:00,860 --> 00:06:02,060
怎么去动态创建一个实力

166
00:06:02,060 --> 00:06:03,660
也就是解决我们之前提到的第二个问题

167
00:06:03,660 --> 00:06:06,560
那么首先呢我们在app.beforestart

168
00:06:06,560 --> 00:06:08,020
在一个生命周期里面去做什么呢

169
00:06:08,020 --> 00:06:11,450
首先第一步 我们要远程获取 是不是要读取配置啊 对吧 我们通过fetch

170
00:06:11,450 --> 00:06:14,060
 去获取了我们一个MySQL config 这里就是咱们远程的一个配置

171
00:06:14,060 --> 00:06:18,960
那么此时怎么样去实力化呢 其实APP的MySQL它这样一个对象提供了一个什么方法呢

172
00:06:18,960 --> 00:06:20,380
 提供了一个createinstance

173
00:06:20,380 --> 00:06:25,120
这样一个方法 你只要传入咱们一个config 它同样的可以去创建一个咱们的数据库的一个对象

174
00:06:25,120 --> 00:06:27,700
那么此时的我们挂载在APP的database下面

175
00:06:27,700 --> 00:06:32,800
然后你通过调用this.app.database 然后去操作你当前动态创建的一个数据库

176
00:06:32,800 --> 00:06:35,020
 那么这里呢就是动态创建实力的一个过程

177
00:06:35,240 --> 00:06:37,800
我们来总结一下咱们这一课所讲解的内容

178
00:06:37,800 --> 00:06:42,920
那么其实我们这节课是不是主要讲的什么呀是不是讲了我们

179
00:06:42,920 --> 00:06:43,940
多实力插件

180
00:06:43,940 --> 00:06:47,780
多实力插件

181
00:06:47,780 --> 00:06:49,580
那我们的重点是什么

182
00:06:49,580 --> 00:06:52,400
重点是不是咱们一个app.add

183
00:06:52,400 --> 00:06:55,200
三个tone在那个apia

184
00:06:55,200 --> 00:06:58,540
它其实中文翻译过来就是咱们去创建一个单例

185
00:06:58,540 --> 00:07:00,080
那我们决定例子是什么

186
00:07:00,080 --> 00:07:00,840
创建

187
00:07:00,840 --> 00:07:01,860
多实力

188
00:07:01,860 --> 00:07:04,420
数据库对吧那么创建多实力数据库

189
00:07:04,680 --> 00:07:06,720
我们是不是其实我们需要注意什么问题

190
00:07:06,720 --> 00:07:10,320
是不是app.js里面

191
00:07:10,320 --> 00:07:13,120
干嘛是不是调用

192
00:07:13,120 --> 00:07:15,680
调用他呀对吧

193
00:07:15,680 --> 00:07:17,740
然后传入一个

194
00:07:17,740 --> 00:07:20,040
配置那么我们是不是必须在config里面去

195
00:07:20,040 --> 00:07:22,600
配置买收口里咱们数据库的一些配置

196
00:07:22,600 --> 00:07:23,360
配置

197
00:07:23,360 --> 00:07:27,200
好那么其实呢我们同样的是不是还可以动态的去

198
00:07:27,200 --> 00:07:29,760
创建

199
00:07:29,760 --> 00:07:32,840
那么动态是如何去创建的首先第一步我们是不是要

200
00:07:32,840 --> 00:07:34,120
远程

201
00:07:34,640 --> 00:07:39,500
获取配置然后呢通过咱们app.mycircle

202
00:07:39,500 --> 00:07:40,780
是不是去创建

203
00:07:40,780 --> 00:07:48,200
实力然后呢你想挂在哪里就挂在哪里我们呢是挂在什么是不是挂在database上面好那么这里呢就是我们这节课的内容

