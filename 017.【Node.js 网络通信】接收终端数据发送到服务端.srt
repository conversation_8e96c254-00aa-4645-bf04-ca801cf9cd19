1
00:00:00,000 --> 00:00:01,840
那么接下来呢

2
00:00:01,840 --> 00:00:04,140
我们待会可以对当前这个小案例

3
00:00:04,140 --> 00:00:05,640
给他做一个简单的升级

4
00:00:05,640 --> 00:00:07,660
就是说我们现在我们可以

5
00:00:07,660 --> 00:00:08,720
就是实现一个小功能

6
00:00:08,720 --> 00:00:10,960
当客户端与服务端连接成功以后

7
00:00:10,960 --> 00:00:12,200
我们在客户端这里

8
00:00:12,200 --> 00:00:13,700
例如我们输入一个内容

9
00:00:13,700 --> 00:00:14,400
例如输入123

10
00:00:14,400 --> 00:00:15,200
我们抄回车

11
00:00:15,200 --> 00:00:17,200
然后把这个123发到这个服务端

12
00:00:17,200 --> 00:00:18,980
我们输入我们随便输

13
00:00:18,980 --> 00:00:19,600
例如输个Hello

14
00:00:19,600 --> 00:00:20,360
我们抄回车

15
00:00:20,360 --> 00:00:22,120
然后就把这个数据发送到服务端

16
00:00:22,120 --> 00:00:23,060
说白了就是说

17
00:00:23,060 --> 00:00:25,340
我们把在终端当中输入的数据

18
00:00:25,340 --> 00:00:26,380
发给服务器

19
00:00:26,380 --> 00:00:28,080
然后服务器去把这个消息

20
00:00:28,080 --> 00:00:28,820
去把它打印出来

21
00:00:28,820 --> 00:00:29,960
就这样一个小功能

22
00:00:29,960 --> 00:00:32,080
那么我们在这里的话呢

23
00:00:32,080 --> 00:00:33,080
怎么去做呢

24
00:00:33,080 --> 00:00:34,700
实现实现方式非常简单

25
00:00:34,700 --> 00:00:36,480
我们在这就是说

26
00:00:36,480 --> 00:00:37,840
当这个客户端

27
00:00:37,840 --> 00:00:42,840
与这个服务端建立连接成功以后

28
00:00:42,840 --> 00:00:44,040
建立连接成功以后

29
00:00:44,040 --> 00:00:44,740
那我们这个

30
00:00:44,740 --> 00:00:46,920
然后我们可以

31
00:00:46,920 --> 00:00:49,180
监听这个终端

32
00:00:49,180 --> 00:00:50,940
监听这个终端的输入

33
00:00:50,940 --> 00:00:54,860
然后获取这个终端的输入

34
00:00:54,860 --> 00:00:57,040
然后发送给我们这个服务端

35
00:00:57,040 --> 00:00:57,900
所以说非常简单

36
00:00:57,900 --> 00:00:59,480
那么这个做法在Node中

37
00:00:59,480 --> 00:01:00,640
就通过这个叫Process

38
00:01:00,640 --> 00:01:01,700
这个进程对象

39
00:01:01,700 --> 00:01:02,840
这个叫StDIn

40
00:01:02,840 --> 00:01:04,340
它有一个事件

41
00:01:04,340 --> 00:01:05,240
那么这个事件呢

42
00:01:05,240 --> 00:01:06,400
就是Data事件

43
00:01:06,400 --> 00:01:06,680
好

44
00:01:06,680 --> 00:01:08,620
我们把这个Data

45
00:01:08,620 --> 00:01:09,280
带出来

46
00:01:09,280 --> 00:01:10,760
那这个Data是什么呢

47
00:01:10,760 --> 00:01:12,720
就是用户在命令行当中

48
00:01:12,720 --> 00:01:13,820
就是终端环境下

49
00:01:13,820 --> 00:01:15,120
所输入的这个数据

50
00:01:15,120 --> 00:01:16,680
当然我们在这里可以去点

51
00:01:16,680 --> 00:01:18,000
因为都是二进制

52
00:01:18,000 --> 00:01:19,420
我们直接去图词证一下

53
00:01:19,420 --> 00:01:21,160
然后接下来

54
00:01:21,160 --> 00:01:21,760
回到这里

55
00:01:21,760 --> 00:01:23,300
我们接下来我们可以来看一下

56
00:01:23,300 --> 00:01:24,060
大家启动一下

57
00:01:24,060 --> 00:01:25,060
我们输入111

58
00:01:25,060 --> 00:01:25,680
然后注意啊

59
00:01:25,680 --> 00:01:26,080
一回车

60
00:01:26,080 --> 00:01:26,840
他就会来出发

61
00:01:26,840 --> 00:01:27,260
回车

62
00:01:27,260 --> 00:01:28,220
我们可以看到

63
00:01:28,220 --> 00:01:30,500
是不是答应这个11122回车

64
00:01:30,500 --> 00:01:32,140
那么这个输入的内容

65
00:01:32,140 --> 00:01:33,560
我们就可以在什么呢

66
00:01:33,560 --> 00:01:35,020
是就可以在这个程序当中

67
00:01:35,020 --> 00:01:35,740
是不是来获取到

68
00:01:35,740 --> 00:01:37,300
好有了它以后

69
00:01:37,300 --> 00:01:38,020
接下来我们就希望

70
00:01:38,020 --> 00:01:38,700
把这个数据

71
00:01:38,700 --> 00:01:40,680
发给我们的服务端呀

72
00:01:40,680 --> 00:01:41,960
所以我们再去直接去

73
00:01:41,960 --> 00:01:44,260
collect.write这个data

74
00:01:44,260 --> 00:01:45,640
当然我们在这里去

75
00:01:45,640 --> 00:01:47,640
是否去tostream就无所谓了

76
00:01:47,640 --> 00:01:49,000
因为即便你tostream了

77
00:01:49,000 --> 00:01:50,220
它也会转成二进制

78
00:01:50,220 --> 00:01:51,160
再发给这个服务端

79
00:01:51,160 --> 00:01:52,160
所以它就是这样

80
00:01:52,160 --> 00:01:53,300
好然后接下来

81
00:01:53,300 --> 00:01:54,740
我们再来就可以来做一个测试

82
00:01:54,740 --> 00:01:56,360
然后回到这个面线行当中

83
00:01:56,360 --> 00:01:57,740
我们大家就可以来看一下

84
00:01:57,740 --> 00:01:58,800
我们输入111

85
00:01:58,800 --> 00:01:59,740
然后回车

86
00:01:59,740 --> 00:02:00,840
然后大家可以看到

87
00:02:00,840 --> 00:02:02,400
服务端在这里

88
00:02:02,400 --> 00:02:04,100
就输出客户端说

89
00:02:04,100 --> 00:02:05,420
11222回车

90
00:02:05,420 --> 00:02:06,280
333回车

91
00:02:06,280 --> 00:02:07,480
当然大家可以看到

92
00:02:07,480 --> 00:02:08,160
我们每一次在这

93
00:02:08,160 --> 00:02:09,800
是不是都有一个换行

94
00:02:09,800 --> 00:02:12,380
原因是我们在这个终端当中

95
00:02:12,380 --> 00:02:13,360
输入的内容

96
00:02:13,360 --> 00:02:14,060
你抄回车

97
00:02:14,060 --> 00:02:16,280
回车本身也算一个字符

98
00:02:16,280 --> 00:02:18,120
回车本身也算一个字符

99
00:02:18,120 --> 00:02:19,420
所以说你要是想

100
00:02:19,420 --> 00:02:20,280
没有这个换行

101
00:02:20,280 --> 00:02:21,480
那你可以这样来做

102
00:02:21,480 --> 00:02:22,960
就直接在这个data这儿去

103
00:02:22,960 --> 00:02:24,300
我们还是得tostream一下

104
00:02:24,300 --> 00:02:25,760
然后点trim

105
00:02:25,760 --> 00:02:28,180
就去除左右两边的回车换行

106
00:02:28,180 --> 00:02:29,140
包括这个空白

107
00:02:29,140 --> 00:02:30,120
所以它就这个意思

108
00:02:30,120 --> 00:02:31,480
那这样完了以后

109
00:02:31,480 --> 00:02:32,920
然后这个时候我们就可以再来看

110
00:02:32,920 --> 00:02:34,640
我们再连接上来

111
00:02:34,640 --> 00:02:35,520
111回车

112
00:02:35,520 --> 00:02:36,560
222回车

113
00:02:36,560 --> 00:02:38,420
那这个时候我们就可以看到

114
00:02:38,420 --> 00:02:39,840
这是不是就没有这个问题了

115
00:02:39,840 --> 00:02:41,740
我们说一个你好

116
00:02:41,740 --> 00:02:45,540
那这个就是关于我们这个小功能的一个扩展

117
00:02:45,540 --> 00:02:48,660
就是说把终端当中用户的输入

118
00:02:48,660 --> 00:02:52,240
通过这个TCP连接去发到我们的服务端

119
00:02:52,240 --> 00:02:53,180
也是通过网络

120
00:02:53,180 --> 00:02:54,480
是把这个数据去给它发出去

121
00:02:54,480 --> 00:02:56,700
我们服务端价值就接收到了

122
00:02:56,700 --> 00:02:58,400
这个客户端发送的这个数据了

