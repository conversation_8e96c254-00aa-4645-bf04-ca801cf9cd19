1
00:00:00,000 --> 00:00:02,840
好 这节课我们就来看一下KOVA中的一个中间键的概念

2
00:00:02,840 --> 00:00:06,240
那么中间键在KOVA里面的一个地位是非常之高的 也是非常的核心

3
00:00:06,240 --> 00:00:09,460
我们刚才是不是讲过KOVA它是下一代 肉里介是英勇框键

4
00:00:09,460 --> 00:00:10,440
为什么它是下一代

5
00:00:10,440 --> 00:00:12,240
是不是说明它的思想很先进

6
00:00:12,240 --> 00:00:13,320
那么它先进在哪

7
00:00:13,320 --> 00:00:15,860
其实KOVA它就先进在一个中间键的思想

8
00:00:15,860 --> 00:00:18,860
可以说这个中间键非常之重要 同学们一定要好好地听

9
00:00:18,860 --> 00:00:20,700
好了 我们先来看一下

10
00:00:20,700 --> 00:00:22,900
为了帮助同学们去理解什么是中间键

11
00:00:22,900 --> 00:00:23,780
我们首先来看一个例子

12
00:00:23,780 --> 00:00:27,340
比如说我们要去实现一个打印日志的功能

13
00:00:27,340 --> 00:00:28,620
其实它很简单

14
00:00:28,620 --> 00:00:30,880
我们在每一个请求的时候呢

15
00:00:30,880 --> 00:00:32,360
需要去把我们请求的方法

16
00:00:32,360 --> 00:00:34,140
和我们请求的地址给打印出来

17
00:00:34,140 --> 00:00:35,620
那么假如说

18
00:00:35,620 --> 00:00:37,080
我们直接通过代码来看

19
00:00:37,080 --> 00:00:40,140
我们刚才是不是实现了一个root

20
00:00:40,140 --> 00:00:41,920
对吧

21
00:00:41,920 --> 00:00:42,920
它有什么作用

22
00:00:42,920 --> 00:00:44,520
是不是当我们访问根目录的时候

23
00:00:44,520 --> 00:00:45,520
就执行命这样一个方法

24
00:00:45,520 --> 00:00:47,560
去访问一个好的word

25
00:00:47,560 --> 00:00:48,800
当执行其他的时候呢

26
00:00:48,800 --> 00:00:50,540
就给咱们访问一个其他

27
00:00:50,540 --> 00:00:51,460
我们的需求是什么

28
00:00:51,460 --> 00:00:53,200
是不是在每一个请求里面

29
00:00:53,200 --> 00:00:55,260
都需要去打印出他的

30
00:00:55,260 --> 00:00:56,380
什么

31
00:00:56,380 --> 00:00:59,540
是不是都需要去打印出他的一个请求的方法和路径了

32
00:00:59,540 --> 00:01:01,780
那么假设我们要去实现在一个需求这边去搞

33
00:01:01,780 --> 00:01:04,320
首先比如说方法

34
00:01:04,320 --> 00:01:11,180
我们是不是需要去通过contact.request.方法

35
00:01:11,180 --> 00:01:11,800
是不是message

36
00:01:11,800 --> 00:01:14,960
然后呢路径

37
00:01:14,960 --> 00:01:21,440
同样的是不是要通过contact.request.pass里面去取啊

38
00:01:21,440 --> 00:01:24,960
那么我们写一个concel.log打印一次行不行

39
00:01:24,960 --> 00:01:25,860
不够吧

40
00:01:25,860 --> 00:01:27,680
因为只有其他执行的时候才会去打印

41
00:01:27,680 --> 00:01:30,680
那么我们同样的是不是也需要在main里面去打印一次

42
00:01:30,680 --> 00:01:32,080
那么假如说你有很多的路由

43
00:01:32,080 --> 00:01:33,620
我们一个项目往往有几百个

44
00:01:33,620 --> 00:01:35,680
甚至几十个甚至几百个路由

45
00:01:35,680 --> 00:01:37,960
那么难道我要去copy几十次几百次吗

46
00:01:37,960 --> 00:01:39,220
是不是很累啊

47
00:01:39,220 --> 00:01:41,360
所以这里呢我们就需要去运用cord里面

48
00:01:41,360 --> 00:01:42,220
它的一个中间键的概念

49
00:01:42,220 --> 00:01:45,600
中间键

50
00:01:45,600 --> 00:01:48,820
那么中间键它的核心实际上是什么呢

51
00:01:48,820 --> 00:01:50,700
其实我们注意到了

52
00:01:50,700 --> 00:01:52,840
当我们app.use的时候

53
00:01:52,840 --> 00:01:54,120
其实我们cord的api很简单

54
00:01:54,120 --> 00:01:55,660
我们前面可能忘了给同学们去介绍

55
00:01:55,660 --> 00:01:57,440
我们Corefer的核心API

56
00:01:57,440 --> 00:01:58,480
就是一个Listen方法

57
00:01:58,480 --> 00:01:59,500
然后还有一个用适方法

58
00:01:59,500 --> 00:02:01,800
可以说你看不到Core有其他的一些方法

59
00:02:01,800 --> 00:02:03,080
他就只有两个Use一个Listen

60
00:02:03,080 --> 00:02:04,360
那么我们首先来看一下中间

61
00:02:04,360 --> 00:02:05,120
它的核心是什么

62
00:02:05,120 --> 00:02:07,180
当我们Use的时候是不是会在我们的一个

63
00:02:07,180 --> 00:02:08,720
Main方法里面去注入一个什么

64
00:02:08,720 --> 00:02:10,240
是不是注入一个对象叫Context

65
00:02:10,240 --> 00:02:12,300
也就是Corefer所提供的上下文

66
00:02:12,300 --> 00:02:14,080
那么中间的其实呢

67
00:02:14,080 --> 00:02:17,680
其实咱们的Core他不仅会注入Context这样一个对象

68
00:02:17,680 --> 00:02:18,700
还会注入一个Next

69
00:02:18,700 --> 00:02:20,740
其实我们的每一个方法里面都有

70
00:02:20,740 --> 00:02:23,300
所以我们就可以去得出一个结论是什么呢

71
00:02:24,120 --> 00:02:27,360
中间键的核心其实是

72
00:02:27,360 --> 00:02:33,600
core在U10里面注入的next的方法

73
00:02:33,600 --> 00:02:37,180
什么意思 或者说我们怎么样去使用

74
00:02:37,180 --> 00:02:40,240
好 这里的next可能跟同学们一下子讲不清楚

75
00:02:40,240 --> 00:02:43,060
我们直接来通过代码 我们来领悟它到底是什么

76
00:02:43,060 --> 00:02:44,600
首先我们这里呢 在

77
00:02:44,600 --> 00:02:49,200
命和其他里面都需要去加入next的属性怎么样去使用

78
00:02:49,200 --> 00:02:50,480
我们等一下来看 首先呢

79
00:02:50,480 --> 00:02:52,800
既然提到了中间键 我们是不是

80
00:02:54,120 --> 00:03:00,000
我们是不是要去实现这样一个日志的中间键了

81
00:03:00,000 --> 00:03:00,520
所以

82
00:03:00,520 --> 00:03:03,880
所以我们需要去定义一个中间键

83
00:03:03,880 --> 00:03:05,700
比如说我们去挖一个logger

84
00:03:05,700 --> 00:03:06,380
等于什么呢

85
00:03:06,380 --> 00:03:07,080
是不是也是一个方法

86
00:03:07,080 --> 00:03:09,040
然后咱们的中间键呢

87
00:03:09,040 --> 00:03:11,540
它也接收了contact next这两个书信

88
00:03:11,540 --> 00:03:14,620
其实大家也可以这样去理解

89
00:03:14,620 --> 00:03:15,920
在call里面一切接中间键

90
00:03:15,920 --> 00:03:16,980
包括我们的路由处理

91
00:03:16,980 --> 00:03:18,040
main和其他

92
00:03:18,040 --> 00:03:20,100
其实它也是某一种程度上面的一个中间键

93
00:03:20,100 --> 00:03:20,680
好

94
00:03:20,680 --> 00:03:21,060
这里呢

95
00:03:21,060 --> 00:03:21,540
我们只是说

96
00:03:21,540 --> 00:03:23,200
给日志专门定义为

97
00:03:23,200 --> 00:03:24,060
它为call里面

98
00:03:24,060 --> 00:03:29,700
补一个咱们日子的中间间怎么去处理呢你比如比如说我们要去打印一个是不是叫打印咱们的一个

99
00:03:29,700 --> 00:03:32,000
方法我们直接过来

100
00:03:32,000 --> 00:03:35,840
然后呢我们去给他一个中间建执行

101
00:03:35,840 --> 00:03:41,460
中间

102
00:03:41,460 --> 00:03:43,520
中间建

103
00:03:43,520 --> 00:03:46,840
执行好

104
00:03:46,840 --> 00:03:49,140
这人为了方便我们去区分

105
00:03:49,140 --> 00:03:52,480
好那么我们既然有中间间中间间刚才我们要解决的问题是什么

106
00:03:52,980 --> 00:03:55,020
都在每一个请求里面都能去打印了

107
00:03:55,020 --> 00:03:57,340
所以呢我们就在命和其他里面就不需要了

108
00:03:57,340 --> 00:03:59,120
然后呢去执行什么呢

109
00:03:59,120 --> 00:04:01,180
 next 这样一个

110
00:04:01,180 --> 00:04:02,460
方法

111
00:04:02,460 --> 00:04:08,080
我们怎么去使用呢其实同样的还是通过app.use 什么呢use

112
00:04:08,080 --> 00:04:10,140
logger就可以了

113
00:04:10,140 --> 00:04:12,180
我们只需要中间怎么去写

114
00:04:12,180 --> 00:04:14,740
我们比如说我们去打印了之后然后呢去调用 next 方法

115
00:04:14,740 --> 00:04:17,040
然后再传递给咱们的

116
00:04:17,040 --> 00:04:21,900
好其实这里面呢我们也需要去调用 next 方法

117
00:04:22,420 --> 00:04:24,800
都要去掉那么我们来看一下

118
00:04:24,800 --> 00:04:26,560
我们来看一下

119
00:04:26,560 --> 00:04:29,880
我们执行一下

120
00:04:29,880 --> 00:04:31,320
看一下咱们的中间间有没有生效

121
00:04:31,320 --> 00:04:31,320
咱们的中间间有没有生效

122
00:04:31,320 --> 00:04:31,320
咱们的中间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间间

123
00:04:31,320 --> 00:04:59,480
这里需要同学们去注意一个问题

124
00:04:59,480 --> 00:05:01,500
什么问题呢

125
00:05:01,500 --> 00:05:04,000
我们的log

126
00:05:04,000 --> 00:05:05,600
比如说我们去访问根目录的时候

127
00:05:05,600 --> 00:05:08,460
比如说我们去访问根目录的时候

128
00:05:08,460 --> 00:05:09,500
是不是只执行main呢

129
00:05:09,500 --> 00:05:11,120
那么到底是我们的log先执行

130
00:05:11,120 --> 00:05:12,040
还是我们的main先执行呢

131
00:05:12,040 --> 00:05:12,500
我们来看一下

132
00:05:12,500 --> 00:05:14,100
比如说这里呢

133
00:05:14,100 --> 00:05:14,860
执行了我们的中间件

134
00:05:14,860 --> 00:05:16,460
那么在main里面我们也打印

135
00:05:16,460 --> 00:05:19,060
我们也打印一个东西

136
00:05:19,060 --> 00:05:20,440
concel.log

137
00:05:20,440 --> 00:05:23,100
那么同学们猜一下

138
00:05:23,100 --> 00:05:25,200
是中间是他先打印

139
00:05:25,200 --> 00:05:26,160
还是他先打印

140
00:05:26,160 --> 00:05:27,940
在我们猜一下

141
00:05:27,940 --> 00:05:30,760
好 我们来看一下结果

142
00:05:30,760 --> 00:05:33,060
好 这里

143
00:05:33,060 --> 00:05:34,860
大家可以看到是不是我们的

144
00:05:34,860 --> 00:05:37,660
中间键先走 然后呢 main 才执行

145
00:05:37,660 --> 00:05:41,500
对吧 为什么呀 是不是因为我们 app 是不是先use the log

146
00:05:41,500 --> 00:05:45,600
后use的咱们的 main 所以说我们的 log 它的

147
00:05:45,600 --> 00:05:50,460
执行是不是在前面 那么咱们的 main 执行在后面 那么我们再来看另外一种情况

148
00:05:50,460 --> 00:05:53,540
我们再来看另外一种情况

149
00:05:55,340 --> 00:05:58,160
比如说我们把把这些打印的信息我们写在Connect的后面

150
00:05:58,160 --> 00:06:00,200
会发生什么样件事情

151
00:06:00,200 --> 00:06:05,840
重新击一下

152
00:06:05,840 --> 00:06:07,380
好这里

153
00:06:07,380 --> 00:06:09,420
大家这里是不是可以看到

154
00:06:09,420 --> 00:06:12,740
好挡住了

155
00:06:12,740 --> 00:06:16,580
大家这里是不是可以看到我们的命一件执行

156
00:06:16,580 --> 00:06:18,380
再执行我们中间里面log的方法

157
00:06:18,380 --> 00:06:20,940
这里有的同学可能会疑问的

158
00:06:20,940 --> 00:06:22,220
老师你不是刚才说了吗

159
00:06:22,220 --> 00:06:24,020
APP他先用死了logger

160
00:06:24,020 --> 00:06:24,780
然后才用死了命

161
00:06:25,040 --> 00:06:26,580
那么为什么这个时候命会先执行

162
00:06:26,580 --> 00:06:31,580
其实这里呢就是Core里面它大名鼎鼎的一个中间剑领执行的一个洋葱模型

163
00:06:31,580 --> 00:06:34,520
这呢也是Core里面非常核心的

164
00:06:34,520 --> 00:06:38,240
那么我们下几个就会去介绍到底什么是Core里面的一个洋葱模型

165
00:06:38,240 --> 00:06:40,700
也是它为什么这么神奇的原因

166
00:06:40,700 --> 00:06:43,700
好那么我们现在回顾一下刚才所讲解的内容

167
00:06:43,700 --> 00:06:51,240
我们刚才是不是介绍了中间剑

168
00:06:51,240 --> 00:06:54,700
那么中间剑在Core里面是不是非常核心的

169
00:06:54,700 --> 00:06:56,500
内容啊同学们

170
00:06:56,500 --> 00:06:58,800
而且呢是不是可以解决

171
00:06:58,800 --> 00:07:01,100
通用的问题

172
00:07:01,100 --> 00:07:02,120
怎么使用啊

173
00:07:02,120 --> 00:07:03,140
中间键怎么使用

174
00:07:03,140 --> 00:07:04,380
是不是它的核心是什么

175
00:07:04,380 --> 00:07:06,220
核心是不是next这样一个

176
00:07:06,220 --> 00:07:08,020
属性呢你只要去调用next

177
00:07:08,020 --> 00:07:09,800
那么我们就可以实现一个中间键

178
00:07:09,800 --> 00:07:11,600
刚才我们是不是还提到了

179
00:07:11,600 --> 00:07:12,840
它那个执行模式是什么

180
00:07:12,840 --> 00:07:14,660
是不是cova里面非常著名的

181
00:07:14,660 --> 00:07:17,100
洋葱模型

182
00:07:17,100 --> 00:07:18,500
那么到底什么是洋葱模型呢

183
00:07:18,500 --> 00:07:20,540
我们下几课就去讲

184
00:07:20,540 --> 00:07:22,600
好这里呢就是我们这几课的内容

