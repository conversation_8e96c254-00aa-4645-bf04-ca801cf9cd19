1
00:00:00,520 --> 00:00:01,800
好 这节课我们就来看一下

2
00:00:01,800 --> 00:00:03,840
Cluster中的优雅退出和进程守护

3
00:00:03,840 --> 00:00:06,660
我们为什么需要了解优雅退出和进程守护呢

4
00:00:06,660 --> 00:00:08,200
其实他们是结合起来

5
00:00:08,200 --> 00:00:09,980
去提升我们应用的一个稳定性

6
00:00:09,980 --> 00:00:10,760
优雅退出

7
00:00:10,760 --> 00:00:12,280
其实就是解决咱们的

8
00:00:12,280 --> 00:00:13,560
进行一些错误处理

9
00:00:13,560 --> 00:00:14,840
那么错误处理之后

10
00:00:14,840 --> 00:00:16,120
是不是要把咱们的进程关闭

11
00:00:16,120 --> 00:00:17,660
那么关闭之后怎么办

12
00:00:17,660 --> 00:00:18,940
此事是不是要重启

13
00:00:18,940 --> 00:00:20,740
其实重启就属于进程守护这部分了

14
00:00:20,740 --> 00:00:21,760
内容其实很简单

15
00:00:21,760 --> 00:00:23,300
我们来看一下

16
00:00:23,300 --> 00:00:24,320
优雅退出

17
00:00:24,320 --> 00:00:24,840
首先

18
00:00:24,840 --> 00:00:27,140
我们关闭一场Worker所有的TCP Server

19
00:00:27,900 --> 00:00:30,720
断开和master的ipc通道不再接受新用户的请求

20
00:00:30,720 --> 00:00:31,480
什么意思

21
00:00:31,480 --> 00:00:32,760
是不是就是咱们比如说

22
00:00:32,760 --> 00:00:34,560
http发生错误的时候

23
00:00:34,560 --> 00:00:35,320
咱们是不是要

24
00:00:35,320 --> 00:00:37,380
要断开http链接

25
00:00:37,380 --> 00:00:39,420
因为你本身你一个请求已经发生错误了

26
00:00:39,420 --> 00:00:40,700
那么咱们不能让他继续错下去

27
00:00:40,700 --> 00:00:43,260
所以咱们直接把这条通道断开也就是把咱们的

28
00:00:43,260 --> 00:00:46,340
class当前fork出来的进程和master断掉

29
00:00:46,340 --> 00:00:48,900
不再接受信货请求也就是咱们直接把它给

30
00:00:48,900 --> 00:00:50,940
关闭把咱们这个进程都给退出

31
00:00:50,940 --> 00:00:54,520
然后呢master立刻fork一个新的work进程保持在线的功能种族不变

32
00:00:54,520 --> 00:00:56,060
也就是咱们刚才是不是

33
00:00:56,320 --> 00:01:00,420
Walker出错了,咱们把它关掉,然后需要开一个新的Walker去代替,因为如果说你有八核

34
00:01:00,420 --> 00:01:04,260
八核你就有八个Walker,但是如果你挂了一个只有七个

35
00:01:04,260 --> 00:01:08,360
所以这时候你需要去造一个新的,是不是就和咱们以前打红井的时候很类似

36
00:01:08,360 --> 00:01:10,660
你造了一个农民,但主攻只能造八个农民

37
00:01:10,660 --> 00:01:13,980
你挂了一个农民就不能起房子了,所以你要再去造一个农民

38
00:01:13,980 --> 00:01:17,320
咱们游戏里面很多的一些情节是很类似的,比如说魔兽

39
00:01:17,320 --> 00:01:19,880
好,那我们再来看一下,当一场Walker

40
00:01:19,880 --> 00:01:23,460
等待一段时间处理完已经接受了请求后退出,其实和第一条差不多

41
00:01:23,460 --> 00:01:25,240
也就是咱们

42
00:01:25,760 --> 00:01:27,340
咱们比如说遇到错误之后

43
00:01:27,340 --> 00:01:29,000
我们此时不能够马上退出

44
00:01:29,000 --> 00:01:31,260
因为你还有一些http就没有处理完

45
00:01:31,260 --> 00:01:34,420
我们可能需要对它去做一些处理之后才能够去退出

46
00:01:34,420 --> 00:01:37,180
我们先来看一下怎么样去解决我们的错误

47
00:01:37,180 --> 00:01:40,520
比如说我们来通过代码看一下怎么样优雅退出

48
00:01:40,520 --> 00:01:41,520
好

49
00:01:41,520 --> 00:01:43,640
这里让老师举个例子

50
00:01:43,640 --> 00:01:47,640
假如说我们http去create了一个server

51
00:01:47,640 --> 00:01:49,420
咱们那res.end

52
00:01:49,420 --> 00:01:50,880
假如说出错了 用户相信

53
00:01:50,880 --> 00:01:51,520
这样

54
00:01:51,520 --> 00:01:53,760
那么我们此时这里是不是会报错啊 同学们

55
00:01:53,760 --> 00:01:55,260
是不是会报错

56
00:01:55,260 --> 00:01:56,280
那报错之后怎么办

57
00:01:56,280 --> 00:01:59,620
报错之后是不是会导致 导致咱们整个线程挂掉

58
00:01:59,620 --> 00:02:08,580
导致咱们http整个线程挂掉 不能再提供服务了

59
00:02:08,580 --> 00:02:13,440
那么此时我们是不是需要去

60
00:02:13,440 --> 00:02:15,480
需要再重启啊

61
00:02:15,480 --> 00:02:18,560
或者说做一些其他的操作 那么重启之前我们是不是首先

62
00:02:18,560 --> 00:02:21,120
比如说我们怎么去处理 再一段错误

63
00:02:23,940 --> 00:02:26,060
其实我们可以写这样的代码 老师这里呢 只是伪代码

64
00:02:26,060 --> 00:02:29,620
如果说

65
00:02:29,620 --> 00:02:37,700
try catch 咱们如果说 catch 到了 error 那么咱们是不是要把咱们的process

66
00:02:37,700 --> 00:02:39,940
给

67
00:02:39,940 --> 00:02:42,420
disconnect 是什么意思 是不断开链接啊

68
00:02:42,420 --> 00:02:49,540
断开和谁的链接 是不是断开和master的链接 那么断开之后应该怎么办

69
00:02:49,540 --> 00:02:53,540
是不是咱们要重启啊 那么整个过程是怎么样去重启的呢 其实这里呢

70
00:02:53,540 --> 00:02:53,620
 就

71
00:02:53,940 --> 00:02:56,180
这里呢就讲到了咱们后面要讲到守护进程

72
00:02:56,180 --> 00:02:57,540
也是在这一节课

73
00:02:57,540 --> 00:02:59,680
那么我这里呢老师就来去介绍一下什么是守护进程

74
00:02:59,680 --> 00:03:02,240
那么你此时断开链接

75
00:03:02,240 --> 00:03:03,420
你是不是要重启

76
00:03:03,420 --> 00:03:04,140
对吧

77
00:03:04,140 --> 00:03:05,080
那么守护进程

78
00:03:05,080 --> 00:03:08,000
其实就是重启

79
00:03:08,000 --> 00:03:09,060
那么我们怎么重启了

80
00:03:09,060 --> 00:03:10,840
process.disconnect之后

81
00:03:10,840 --> 00:03:12,280
咱们在什么时期去重启了

82
00:03:12,280 --> 00:03:13,820
这里呢我给大家举个例子

83
00:03:13,820 --> 00:03:14,880
我们不用creditself去做

84
00:03:14,880 --> 00:03:15,480
因为这里呢

85
00:03:15,480 --> 00:03:16,760
比较麻烦

86
00:03:16,760 --> 00:03:20,160
老师直接手动演示给大家看

87
00:03:20,160 --> 00:03:21,760
我们直接起一个walker之后

88
00:03:21,760 --> 00:03:23,420
起一个walker之后

89
00:03:23,420 --> 00:03:24,760
咱们直接直接process

90
00:03:24,760 --> 00:03:25,600
咱们直接调用

91
00:03:25,600 --> 00:03:25,880
放开

92
00:03:25,880 --> 00:03:26,560
我们来看一下

93
00:03:26,560 --> 00:03:28,580
是怎么一回事

94
00:03:28,580 --> 00:03:30,400
放开

95
00:03:30,400 --> 00:03:31,320
设定

96
00:03:31,320 --> 00:03:32,300
load

97
00:03:32,300 --> 00:03:33,660
mate.js

98
00:03:33,660 --> 00:03:34,260
大家看到没有

99
00:03:34,260 --> 00:03:35,620
有工作进程退出了

100
00:03:35,620 --> 00:03:37,060
直接咱们八个进程全部退出

101
00:03:37,060 --> 00:03:38,180
就是因为调用的disconnect

102
00:03:38,180 --> 00:03:40,500
那么在哪里监听到的disconnect

103
00:03:40,500 --> 00:03:41,200
这样一个事件呢

104
00:03:41,200 --> 00:03:42,020
就是咱们的

105
00:03:42,020 --> 00:03:44,060
在主线程的disconnect事件里面

106
00:03:44,060 --> 00:03:45,000
这里咱们前面讲API

107
00:03:45,000 --> 00:03:46,300
是不是已经讲过了

108
00:03:46,300 --> 00:03:46,580
好

109
00:03:46,580 --> 00:03:49,360
那么当有工作进程退出了之后

110
00:03:49,360 --> 00:03:50,560
咱们是不是要重启它呀

111
00:03:50,560 --> 00:03:50,940
同学们

112
00:03:50,940 --> 00:03:53,700
怎么重启呢

113
00:03:53,700 --> 00:03:54,940
咱们直接

114
00:03:54,940 --> 00:03:57,060
glass.fork

115
00:03:57,060 --> 00:03:58,500
好我们再来看一下效果

116
00:03:58,500 --> 00:04:00,000
有工作进程退出了

117
00:04:00,000 --> 00:04:01,220
为什么会出现这种情况

118
00:04:01,220 --> 00:04:02,700
它是不是一直在关闭退出

119
00:04:02,700 --> 00:04:03,660
关闭退出

120
00:04:03,660 --> 00:04:04,120
为什么呀

121
00:04:04,120 --> 00:04:05,360
因为你一旦disconnect

122
00:04:05,360 --> 00:04:06,040
它就会去

123
00:04:06,040 --> 00:04:07,660
监听到咱们的新的worker

124
00:04:07,660 --> 00:04:08,420
然后去fork

125
00:04:08,420 --> 00:04:10,160
那么咱们一旦fork又会去disconnect

126
00:04:10,160 --> 00:04:12,240
所以它就会不断地去循环在一个过程

127
00:04:12,240 --> 00:04:15,880
但是它会保证你一直只有8个worker

128
00:04:20,940 --> 00:04:24,340
所以咱们processing disconnect

129
00:04:24,340 --> 00:04:25,940
咱们是不是写到http里面

130
00:04:25,940 --> 00:04:27,320
就不会有这样一种情况来

131
00:04:27,320 --> 00:04:30,560
这里呢就是我们的cluster中的优雅退出

132
00:04:30,560 --> 00:04:31,520
以及进程守护

133
00:04:31,520 --> 00:04:33,180
进程守护老师其实刚才已经讲了

134
00:04:33,180 --> 00:04:37,440
咱们在cluster里面的exit事件和disconnect事件里面去监听

135
00:04:37,440 --> 00:04:40,980
然后呢去调用它的一个fork去其中一个新的进程

136
00:04:40,980 --> 00:04:43,220
那么进程守护它一个概念

137
00:04:43,220 --> 00:04:44,820
就是像天使一样默默的守护

138
00:04:44,820 --> 00:04:46,660
这些walk的进程保障整个应用的稳定性

139
00:04:46,660 --> 00:04:49,200
那么这里呢其实老师讲的cluster的一个

140
00:04:49,200 --> 00:04:51,680
优雅退出以及进程守护呢

141
00:04:51,680 --> 00:04:52,820
都比较简单

142
00:04:52,820 --> 00:04:54,520
实际上咱们项目中会比它复杂很多

143
00:04:54,520 --> 00:04:57,040
后面呢老师去讲解loads实际项目的时候

144
00:04:57,040 --> 00:04:59,000
咱们会去深入的去讲解loads的cluster

145
00:04:59,000 --> 00:04:59,900
怎么样去做错误处理

146
00:04:59,900 --> 00:05:01,060
怎么样去做进程守护

147
00:05:01,060 --> 00:05:02,620
这里呢只是简单的介绍一下概念

148
00:05:02,620 --> 00:05:05,200
我们再来回归一下刚才所讲解的内容

149
00:05:05,200 --> 00:05:08,260
首先我们去处理错误

150
00:05:08,260 --> 00:05:09,060
怎么样去优雅退出

151
00:05:09,060 --> 00:05:11,260
是不是通过disconnect或者process

152
00:05:11,260 --> 00:05:12,540
下面是不是还有一个方法

153
00:05:12,540 --> 00:05:15,440
叫做exit

154
00:05:15,440 --> 00:05:17,480
那么当咱们退出之后

155
00:05:17,480 --> 00:05:18,960
退出之后是不是需要在咱们的主线程

156
00:05:18,960 --> 00:05:22,180
cluster上面去监听这样一些exit或者collect这样的事件

157
00:05:22,180 --> 00:05:23,400
然后去重新for给一个新的

158
00:05:23,400 --> 00:05:26,840
这里呢就会保持你的worker数量永远不变

159
00:05:26,840 --> 00:05:28,480
这里呢就是优雅退出和进程守护

160
00:05:28,480 --> 00:05:31,440
他们结合起来所使用的一个小的例子

161
00:05:31,440 --> 00:05:33,860
好这节课呢就先到这里

162
00:05:33,860 --> 00:05:34,900
老师先把视频听一下

