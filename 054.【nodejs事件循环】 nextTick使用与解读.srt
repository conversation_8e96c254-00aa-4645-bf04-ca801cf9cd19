1
00:00:00,000 --> 00:00:05,240
好 这里我们来看一个更有意思的东西 叫做process.nextic

2
00:00:05,240 --> 00:00:09,720
同学们在学习node的过程中 可能已经听错过它了 那么它是做什么了呢

3
00:00:09,720 --> 00:00:12,540
我们直接通过代码来看一下

4
00:00:12,540 --> 00:00:19,960
首先 咱们来写一个process.nextic 执行一个concil.log111concil.log222 咱们来看一下它是做什么了

5
00:00:19,960 --> 00:00:21,500
咱们直接通过代码来感受一下

6
00:00:21,500 --> 00:00:23,800
node.nextic

7
00:00:27,400 --> 00:00:31,300
说明什么问题是process the next tick 它也是一个逆步止行的方法

8
00:00:31,300 --> 00:00:36,000
那么我们能不能联想到process the next tick

9
00:00:36,000 --> 00:00:44,000
它和settimeout和和什么和setimmediate

10
00:00:44,000 --> 00:00:47,900
setimmediate区别是什么 他们有什么区别

11
00:00:47,900 --> 00:00:52,100
既然他们都是逆步了 我们刚才是不是理解了settimeout和setimmediate它的区别

12
00:00:52,100 --> 00:00:55,100
那么它和process the next tick 它们的区别又是什么

13
00:00:56,300 --> 00:00:57,580
是不是咱们要搞清楚吧

14
00:00:57,580 --> 00:00:59,120
其实说起来这样一个话题

15
00:00:59,120 --> 00:00:59,920
其实很有意思

16
00:00:59,920 --> 00:01:01,560
在Node里面

17
00:01:01,560 --> 00:01:02,640
他们三个之间的关系

18
00:01:02,640 --> 00:01:03,840
其实呢

19
00:01:03,840 --> 00:01:04,520
就是比较复杂

20
00:01:04,520 --> 00:01:05,280
都是一步的

21
00:01:05,280 --> 00:01:06,640
这是咱们面试中经常会遇到的

22
00:01:06,640 --> 00:01:07,560
所以说ProcessDynastic

23
00:01:07,560 --> 00:01:08,700
为什么说它有意思

24
00:01:08,700 --> 00:01:10,300
NodeGNS为什么说复杂

25
00:01:10,300 --> 00:01:11,360
咱们刚才呢

26
00:01:11,360 --> 00:01:12,520
SendTimeout和SendImmediate

27
00:01:12,520 --> 00:01:13,780
已经把同学们已经有点绕晕了

28
00:01:13,780 --> 00:01:14,600
接下来呢

29
00:01:14,600 --> 00:01:15,800
又来了一个ProcessDynastic

30
00:01:15,800 --> 00:01:16,660
是不是要崩溃

31
00:01:16,660 --> 00:01:17,620
不过不要紧

32
00:01:17,620 --> 00:01:18,880
这已经是最后一个EPU API了

33
00:01:18,880 --> 00:01:19,500
咱们把它搞清楚

34
00:01:19,500 --> 00:01:20,600
也算是彻底理解了

35
00:01:20,600 --> 00:01:23,600
我们露了今天式的词键循环机制 同学们不要怕 我们现在看一下它的介绍

36
00:01:23,600 --> 00:01:28,760
process the next tick 不在even loop的任何阶段执行 什么意思

37
00:01:28,760 --> 00:01:34,160
咱们even loop是不是封为六阶段 它不在任何一个阶段执行 而是在各个阶段的中间执行

38
00:01:34,160 --> 00:01:35,000
 切换的中间

39
00:01:35,000 --> 00:01:39,480
什么意思 也就是说从一个阶段切换到下一个阶段前去执行 那我们来看一下这一段代码

40
00:01:39,480 --> 00:01:40,680
 它是怎么样的

41
00:01:40,680 --> 00:01:49,020
首先会读取一段文件 fs.readfail 那么readfail这两个老师来改写一下 稍微改写一下

42
00:01:50,540 --> 00:01:52,580
nextic 引入 fs

43
00:01:52,580 --> 00:01:57,700
路径换一下 咱们还是去读取 red.txt

44
00:01:57,700 --> 00:02:00,520
好 路径换一下

45
00:02:00,520 --> 00:02:04,620
好 那么我们来执行一下 看一下到底是怎么回事

46
00:02:04,620 --> 00:02:08,720
这里呢 是 pass.resolve

47
00:02:08,720 --> 00:02:11,780
走

48
00:02:11,780 --> 00:02:14,860
load nextic 2.js

49
00:02:14,860 --> 00:02:19,460
nextic 1 2 immediate nextic 3 set them out

50
00:02:19,980 --> 00:02:21,980
是不是说明 他们两个先执行 然后呢

51
00:02:21,980 --> 00:02:27,180
再执行 他 然后再执行 他 然后再执行 塞他帽子

52
00:02:27,180 --> 00:02:30,480
对吧 好 那么我们一起来分析一下 为什么会出现这种情况

53
00:02:30,480 --> 00:02:33,380
新建

54
00:02:33,380 --> 00:02:34,780
copy一下

55
00:02:34,780 --> 00:02:37,380
好

56
00:02:37,380 --> 00:02:41,380
下个背景 颜色有点丑

57
00:02:41,380 --> 00:02:44,580
好 那么我们把代码先贴过来

58
00:02:44,580 --> 00:02:48,880
贴代码

59
00:02:49,980 --> 00:02:50,500
好

60
00:02:50,500 --> 00:02:53,060
首先我们刚才是不是讲了

61
00:02:53,060 --> 00:02:54,840
next tick他在哪个阶段执行

62
00:02:54,840 --> 00:02:56,380
是不是他不在任何阶段执行

63
00:02:56,380 --> 00:02:57,140
而是在各个阶段

64
00:02:57,140 --> 00:02:58,420
切换的中间执行

65
00:02:58,420 --> 00:02:59,700
怎么样理解

66
00:02:59,700 --> 00:03:00,980
好我们一起来看一下

67
00:03:00,980 --> 00:03:04,060
先丢先拿开

68
00:03:04,060 --> 00:03:04,820
首先

69
00:03:04,820 --> 00:03:07,640
咱们去运行fs.warefile的时候

70
00:03:07,640 --> 00:03:08,160
此时

71
00:03:08,160 --> 00:03:09,700
是不是没有timer

72
00:03:09,700 --> 00:03:10,980
说明什么问题

73
00:03:10,980 --> 00:03:12,760
咱们是不是fswarefile的cobot

74
00:03:12,760 --> 00:03:13,780
他会卡在

75
00:03:13,780 --> 00:03:15,580
pro这个阶段呢会阻塞对吧

76
00:03:15,580 --> 00:03:16,340
咱们看这一段

77
00:03:16,340 --> 00:03:17,620
对吧因为他没有timer

78
00:03:17,620 --> 00:03:18,400
走的就是这里

79
00:03:18,400 --> 00:03:19,680
好

80
00:03:19,680 --> 00:03:20,360
咱们来看一下

81
00:03:20,360 --> 00:03:22,440
实际上这里呢

82
00:03:22,440 --> 00:03:23,000
是什么呢

83
00:03:23,000 --> 00:03:23,520
它是

84
00:03:23,520 --> 00:03:28,240
fs.readfile

85
00:03:28,240 --> 00:03:29,640
回掉

86
00:03:29,640 --> 00:03:31,020
咱们来假设它两毫秒

87
00:03:31,020 --> 00:03:32,040
暂时先不管它多久

88
00:03:32,040 --> 00:03:32,820
这个时间不重要

89
00:03:32,820 --> 00:03:34,600
首先当前时间是多少

90
00:03:34,600 --> 00:03:35,680
0对吧

91
00:03:35,680 --> 00:03:36,180
好

92
00:03:36,180 --> 00:03:36,620
那么呢

93
00:03:36,620 --> 00:03:37,760
当0毫秒的时候

94
00:03:37,760 --> 00:03:39,740
是不是fs.readfile回掉

95
00:03:39,740 --> 00:03:40,680
会卡在这里啊

96
00:03:40,680 --> 00:03:40,960
对吧

97
00:03:40,960 --> 00:03:42,140
pull会卡在pull这里

98
00:03:42,140 --> 00:03:44,320
那么当时间变为2毫秒的时候

99
00:03:44,320 --> 00:03:45,440
回掉

100
00:03:45,440 --> 00:03:46,000
过来

101
00:03:46,000 --> 00:03:46,620
对吧

102
00:03:46,620 --> 00:03:46,980
执行

103
00:03:46,980 --> 00:03:48,080
是不是执行

104
00:03:48,080 --> 00:03:48,440
同学们

105
00:03:48,440 --> 00:03:48,980
好

106
00:03:48,980 --> 00:03:50,080
咱们执行

107
00:03:50,080 --> 00:03:54,920
首先执行到 process. next tick

108
00:03:54,920 --> 00:03:56,920
执行到 process. next tick

109
00:03:56,920 --> 00:03:58,840
首先我们看

110
00:03:58,840 --> 00:04:00,340
我们先从第一行看起

111
00:04:00,340 --> 00:04:01,240
首先有个settimeout

112
00:04:01,240 --> 00:04:03,140
咱们现在是不是有一个settimeout

113
00:04:03,140 --> 00:04:04,140
丢进io

114
00:04:04,140 --> 00:04:07,740
settimeout

115
00:04:07,740 --> 00:04:08,820
他的什么还回掉

116
00:04:08,820 --> 00:04:14,440
对吧

117
00:04:14,440 --> 00:04:14,980
第一行是他

118
00:04:14,980 --> 00:04:15,880
所以咱们先把它丢进io

119
00:04:15,880 --> 00:04:17,540
因为此时还不能确定即是执行顺序

120
00:04:17,540 --> 00:04:18,080
第二个

121
00:04:18,480 --> 00:04:22,440
是要的immediate 是不是也加入IO同学们 对吧 因为他也是异步的 我们不知道他执行时机

122
00:04:22,440 --> 00:04:27,600
然后呢 process.nestick 这里呢 我就不写了 不好写这里 我们先来看一下

123
00:04:27,600 --> 00:04:29,800
第一次执行是在timeout的时候

124
00:04:29,800 --> 00:04:34,480
当把他们丢进IO的时候 是不是咱们还在pull这个阶段 那咱们来看一下

125
00:04:34,480 --> 00:04:38,920
咱们先不管process 因为为什么呀 咱们为什么不管process.nestick 为什么不管

126
00:04:38,920 --> 00:04:40,120
看这里

127
00:04:40,120 --> 00:04:44,560
他不在even loop的任何阶段执行 而是在各个阶段的切换的中间执行

128
00:04:44,920 --> 00:04:47,060
所以呢,我们先不管proset.nestic

129
00:04:47,060 --> 00:04:48,360
我们是不是要看一下

130
00:04:48,360 --> 00:04:50,740
首先确定setTimeout和setImmediate随先执行

131
00:04:50,740 --> 00:04:52,140
刚才咱们是不是讲过了

132
00:04:52,140 --> 00:04:54,840
首先FS它的callback结束之后

133
00:04:54,840 --> 00:04:56,920
也就是它走掉

134
00:04:56,920 --> 00:04:57,700
先把这个双掉

135
00:04:57,700 --> 00:04:59,140
走掉,它执行完成

136
00:04:59,140 --> 00:05:00,580
执行完成,pro阶段结束

137
00:05:00,580 --> 00:05:01,260
是不是会到check

138
00:05:01,260 --> 00:05:02,520
check会做什么

139
00:05:02,520 --> 00:05:03,280
check

140
00:05:03,280 --> 00:05:04,980
check做什么

141
00:05:04,980 --> 00:05:07,060
check是不是执行咱们的immediate

142
00:05:07,060 --> 00:05:09,060
所以说呢,immediate的回料会到这里执行

143
00:05:09,060 --> 00:05:10,120
但是

144
00:05:10,120 --> 00:05:11,320
但是

145
00:05:11,320 --> 00:05:13,920
但是咱们刚才的pro

146
00:05:13,920 --> 00:05:15,200
和check

147
00:05:15,200 --> 00:05:16,600
咱们刚才的pro和check

148
00:05:16,600 --> 00:05:17,260
是不是已经执行了

149
00:05:17,260 --> 00:05:17,640
同学们

150
00:05:17,640 --> 00:05:19,520
procheck做了一个什么操作

151
00:05:19,520 --> 00:05:22,080
从pro到check

152
00:05:22,080 --> 00:05:23,200
是不是做了一个切换的操作

153
00:05:23,200 --> 00:05:24,220
同学们

154
00:05:24,220 --> 00:05:26,140
process the next tick

155
00:05:26,140 --> 00:05:28,080
在各个阶段切换的中间执行

156
00:05:28,080 --> 00:05:31,100
也就是说在pro和check的中间会执行谁

157
00:05:31,100 --> 00:05:32,920
是不是执行process the next tick

158
00:05:32,920 --> 00:05:33,700
concil.log

159
00:05:33,700 --> 00:05:35,200
 next tick 1和 next tick 2

160
00:05:35,200 --> 00:05:35,600
对吧

161
00:05:35,600 --> 00:05:39,180
因为在pro和check的切换的中间会执行谁

162
00:05:39,180 --> 00:05:43,100
next tick 1

163
00:05:43,100 --> 00:05:47,940
和nastic

164
00:05:47,940 --> 00:05:49,020
二

165
00:05:49,020 --> 00:05:50,620
在pro和check

166
00:05:50,620 --> 00:05:51,700
他们两个切换的中间

167
00:05:51,700 --> 00:05:53,080
首先他们会执行

168
00:05:53,080 --> 00:05:54,200
进去

169
00:05:54,200 --> 00:05:55,260
进去

170
00:05:55,260 --> 00:05:55,640
好

171
00:05:55,640 --> 00:05:56,720
切换到check之后

172
00:05:56,720 --> 00:05:57,680
是不是就到了immediate

173
00:05:57,680 --> 00:05:58,060
对吧

174
00:05:58,060 --> 00:05:58,400
好

175
00:05:58,400 --> 00:05:59,740
进去

176
00:05:59,740 --> 00:06:00,060
好

177
00:06:00,060 --> 00:06:01,200
那么切换

178
00:06:01,200 --> 00:06:03,840
切换到check之后

179
00:06:03,840 --> 00:06:04,860
是不是咱们要去

180
00:06:04,860 --> 00:06:05,960
再到第二轮世界

181
00:06:05,960 --> 00:06:06,460
循环的timer

182
00:06:06,460 --> 00:06:06,840
对吧

183
00:06:06,840 --> 00:06:08,040
再到timer里面去等待谁

184
00:06:08,040 --> 00:06:09,360
是不是等待settimeout的毁掉

185
00:06:09,360 --> 00:06:11,400
那么在从check到timer的过程中

186
00:06:11,400 --> 00:06:12,720
是不是又来一个nastic3

187
00:06:12,720 --> 00:06:13,240
同学们

188
00:06:13,240 --> 00:06:14,260
所以呢

189
00:06:14,260 --> 00:06:15,280
此时呢

190
00:06:15,280 --> 00:06:16,300
又来一个

191
00:06:16,300 --> 00:06:17,840
在check和timer

192
00:06:17,840 --> 00:06:19,380
他们的中间切换的时候来一个3

193
00:06:19,380 --> 00:06:20,140
所以呢

194
00:06:20,140 --> 00:06:21,160
咱们就去执行

195
00:06:21,160 --> 00:06:22,440
好

196
00:06:22,440 --> 00:06:24,500
当set immediate的执行完成之后

197
00:06:24,500 --> 00:06:25,780
是不是咱们切换到timer呀

198
00:06:25,780 --> 00:06:27,820
他们执行谁是不是执行settimeout

199
00:06:27,820 --> 00:06:29,360
他呀对吧

200
00:06:29,360 --> 00:06:31,160
好进去

201
00:06:31,160 --> 00:06:33,200
那咱们此时顺序是不是已经得到了

202
00:06:33,200 --> 00:06:40,880
好那咱们来看一下和咱们当初运行的是不是一样的

203
00:06:42,420 --> 00:06:42,940
纯音执行

204
00:06:42,940 --> 00:06:45,740
netic1,2,cellimediate,3,cellimouts

205
00:06:45,740 --> 00:06:47,040
对吧,同学们

206
00:06:47,040 --> 00:06:49,960
好,那我们来回顾一下刚才的执行过程

207
00:06:49,960 --> 00:06:50,720
咱们来回顾一下

208
00:06:50,720 --> 00:06:52,780
首先

209
00:06:52,780 --> 00:06:55,600
FS执行的时候,是不是在L里面去等待

210
00:06:55,600 --> 00:06:57,020
在Pol里面这里会阻塞,对吧

211
00:06:57,020 --> 00:06:57,600
然后

212
00:06:57,600 --> 00:07:01,040
等到FSRF要完成之后

213
00:07:01,040 --> 00:07:02,640
RF要完成之后

214
00:07:02,640 --> 00:07:04,320
是不是就会执行他里面的这个callback

215
00:07:04,320 --> 00:07:05,440
是不是就是这样一段了

216
00:07:05,440 --> 00:07:06,680
这样一段执行他

217
00:07:06,680 --> 00:07:09,040
好,执行他的时候

218
00:07:09,040 --> 00:07:11,740
是不是首先咱们会看cellimediate

219
00:07:11,740 --> 00:07:15,180
如果说如果说代码已经被send immediate 设定callback 那么呢

220
00:07:15,180 --> 00:07:19,740
event loop将结束破阶段进入check 对吧 咱们呢 就进入check immediate进来

221
00:07:19,740 --> 00:07:23,180
那么在inmediate进来的时候 是不是从pore切换到check了

222
00:07:23,180 --> 00:07:26,880
那么切换的过程中 是不是就会执行next tick的1和2啊

223
00:07:26,880 --> 00:07:32,800
他在pore和check切换问中 那么pore和check切换过程 是不是在check之前呢

224
00:07:32,800 --> 00:07:36,740
 所以说就会先执行next tick 1和2 然后再执行send immediate

225
00:07:36,740 --> 00:07:40,240
好 那么咱们的check阶段完成 是不是要到第二个视线循环的timer

226
00:07:40,940 --> 00:07:42,980
time 执行谁 是不是执行set timeout

227
00:07:42,980 --> 00:07:45,040
那么在check和time之间

228
00:07:45,040 --> 00:07:47,860
是不是又会有一个netic3

229
00:07:47,860 --> 00:07:51,180
所以说咱们呢set timeout就在netic3

230
00:07:51,180 --> 00:07:51,940
之后

231
00:07:51,940 --> 00:07:55,020
同学们好好的去领悟一下结合之前所学习的内容

232
00:07:55,020 --> 00:07:56,820
那么这里就是这一节内容

