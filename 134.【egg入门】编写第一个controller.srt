1
00:00:00,000 --> 00:00:05,120
好 这节课呢 我们来看一下快速入门这一部分内容 我们来会去安装一个1GG

2
00:00:05,120 --> 00:00:07,980
 然后呢去编写咱们的第一个controller 控制器

3
00:00:07,980 --> 00:00:12,880
好 那么我们先来安装一下 安装的命令呢其实非常简单 npm init egg

4
00:00:12,880 --> 00:00:17,660
然后呢 给我们一个参数 type simple 也就是咱们先去建一个简单一点的项目

5
00:00:17,660 --> 00:00:20,100
 然后通过npm install 去安装一下 eline

6
00:00:20,100 --> 00:00:21,840
好 那么我们先来

7
00:00:21,840 --> 00:00:23,680
引力一下

8
00:00:23,680 --> 00:00:29,420
那么还有更多的一些安装的方法 同学们可以去一一的它的一个官网自己去看

9
00:00:29,420 --> 00:00:31,940
我们这里呢只是去给同学们演示其中一种

10
00:00:31,940 --> 00:00:34,180
好 大家呢可以稍等一下

11
00:00:34,180 --> 00:00:40,500
其实呢

12
00:00:40,500 --> 00:00:42,160
这里呢在我们等待的时候呢

13
00:00:42,160 --> 00:00:43,440
可以给同学们去安立一波

14
00:00:43,440 --> 00:00:45,980
其实一GG这样一个框架实际上是非常好用的

15
00:00:45,980 --> 00:00:47,080
那么在安装的时候呢

16
00:00:47,080 --> 00:00:47,920
我们来给大家看一下

17
00:00:47,920 --> 00:00:51,380
它的一个咱们一个Github上面的一个社区情况是怎么样的

18
00:00:51,380 --> 00:00:52,700
其实大家可以看到

19
00:00:52,700 --> 00:00:55,180
一GG它虽然是一个国产框架

20
00:00:55,180 --> 00:00:57,180
但是它的一个子大其实非常多的

21
00:00:57,180 --> 00:00:57,960
一万三千多个星

22
00:00:57,960 --> 00:01:00,800
那么大家其实不要去排斥国产的框架

23
00:01:00,800 --> 00:01:02,800
1G这样1G这样的一个框架

24
00:01:02,800 --> 00:01:04,060
它确实是做得非常的不错

25
00:01:04,060 --> 00:01:04,500
非常好

26
00:01:04,500 --> 00:01:06,520
虽然说有很多的一些国产框架非常坑

27
00:01:06,520 --> 00:01:06,980
但是

28
00:01:06,980 --> 00:01:09,980
但是1GG它是绝对是OK的

29
00:01:09,980 --> 00:01:10,520
值得大学

30
00:01:10,520 --> 00:01:11,860
好好的去学习和研究

31
00:01:11,860 --> 00:01:13,520
好

32
00:01:13,520 --> 00:01:16,120
我们的依赖已经安装完成了

33
00:01:16,120 --> 00:01:18,000
那么我们来首先来看一下咱们的

34
00:01:18,000 --> 00:01:20,060
好

35
00:01:20,060 --> 00:01:21,260
这里了是不是给咱们已经

36
00:01:21,260 --> 00:01:23,500
在咱们第二章的在一个文件夹里面

37
00:01:23,500 --> 00:01:24,680
是不是给我们默认初始化了一些

38
00:01:24,680 --> 00:01:26,320
初始化了一些内容啊

39
00:01:26,320 --> 00:01:26,780
同学们

40
00:01:26,780 --> 00:01:28,980
也就是说我们刚才执行npm init1gg的时候

41
00:01:28,980 --> 00:01:30,740
它会自动的帮我们生成一些文件夹的目录

42
00:01:30,740 --> 00:01:32,120
也比如说我们刚才是不是讲到了

43
00:01:32,120 --> 00:01:33,220
是不是讲到了

44
00:01:33,220 --> 00:01:35,620
好 先停一下 我们先把依赖给安装一下

45
00:01:35,620 --> 00:01:38,540
npm install

46
00:01:38,540 --> 00:01:41,080
好 其实大家可以看到

47
00:01:41,080 --> 00:01:43,060
我们的目录是不是它已经自动生成了

48
00:01:43,060 --> 00:01:45,420
而且会按照什么样是不是约定又有配置

49
00:01:45,420 --> 00:01:48,340
它会约定出一个app和一个config的文件夹

50
00:01:48,340 --> 00:01:54,240
这里呢 control这里就是咱们去写控制器的router

51
00:01:54,240 --> 00:01:56,240
是不是顾名思业去写你的路由

52
00:01:56,240 --> 00:01:57,940
config就去写一些咱们的配置文件

53
00:01:57,940 --> 00:01:58,800
你比如说呢

54
00:01:58,800 --> 00:02:01,000
是不是可以config.default.js

55
00:02:01,000 --> 00:02:01,980
你能去一些某种的配置

56
00:02:01,980 --> 00:02:02,820
包括了你还可以根据

57
00:02:02,820 --> 00:02:04,120
根据咱们的生产环境

58
00:02:04,120 --> 00:02:06,080
和咱们的开发环境去写不同的配置

59
00:02:06,080 --> 00:02:07,080
那么test的文件呢

60
00:02:07,080 --> 00:02:08,200
就是去写单册

61
00:02:08,200 --> 00:02:09,900
包括里面会有package.json

62
00:02:09,900 --> 00:02:11,280
我们来看一下它的依赖像是什么

63
00:02:11,280 --> 00:02:12,560
是不是依赖了咱们的1GG

64
00:02:12,560 --> 00:02:13,400
1GG CLI

65
00:02:13,400 --> 00:02:14,360
包括了YesLint

66
00:02:14,360 --> 00:02:16,180
这一些很简单的一些依赖

67
00:02:16,180 --> 00:02:16,620
好

68
00:02:16,620 --> 00:02:18,540
现在它其实已经依赖安装完成了

69
00:02:18,540 --> 00:02:18,840
这里呢

70
00:02:18,840 --> 00:02:19,360
同学们注意

71
00:02:19,360 --> 00:02:20,940
这里呢

72
00:02:20,940 --> 00:02:21,260
报一个

73
00:02:21,260 --> 00:02:23,660
npm what is fix

74
00:02:23,660 --> 00:02:25,660
说明了你的npm依赖有一些问题

75
00:02:25,660 --> 00:02:27,660
我们来执行一下

76
00:02:27,660 --> 00:02:29,660
其实我们遇到问题一定要去看

77
00:02:29,660 --> 00:02:31,660
咱们是不是控制台他给你的一些提示

78
00:02:31,660 --> 00:02:33,660
你按照他提示去走一定不会错的

79
00:02:33,660 --> 00:02:35,660
那么我们来稍等一下

80
00:02:35,660 --> 00:02:39,660
在安装依赖的时候

81
00:02:39,660 --> 00:02:41,660
因为我们这些个是不是来写controller

82
00:02:41,660 --> 00:02:43,660
我们来看一下他其实在

83
00:02:43,660 --> 00:02:44,660
是不是在依赖里面

84
00:02:44,660 --> 00:02:46,660
他是不是在他自动安装文件里面

85
00:02:46,660 --> 00:02:48,660
其实已经写好了一个默认的router和一个controller

86
00:02:48,660 --> 00:02:49,660
叫做home

87
00:02:49,660 --> 00:02:51,660
比如说我们先来看一下router

88
00:02:51,660 --> 00:02:52,660
我们先来看一下router

89
00:02:52,660 --> 00:02:56,700
比如说它module.export是不是导出了一个是不是导出了一个方形

90
00:02:56,700 --> 00:02:59,960
appapp是不是它其实就是咱们core里面的app

91
00:02:59,960 --> 00:03:03,860
然后它去执行那个router.get也就是在根目录的时候

92
00:03:03,860 --> 00:03:07,260
当你访问根目录会执行controller.home.index

93
00:03:07,260 --> 00:03:08,260
其实大家可以注意

94
00:03:08,260 --> 00:03:09,820
其实大家可以注意什么呢

95
00:03:09,820 --> 00:03:12,140
这里是不是调用的不是一个方法

96
00:03:12,140 --> 00:03:14,480
它其实是一个命名空间

97
00:03:14,480 --> 00:03:17,480
其实大家也可以把它理解为理解为什么呢

98
00:03:17,480 --> 00:03:19,560
egg的约定

99
00:03:19,560 --> 00:03:21,760
大家多写几次这样的代码就会有一些感觉

100
00:03:21,760 --> 00:03:23,520
你比如说我们在controller里面

101
00:03:23,520 --> 00:03:25,560
是不是导出了一个homecontroller

102
00:03:25,560 --> 00:03:28,200
然后他里面有一个index这样的一个方法

103
00:03:28,200 --> 00:03:30,320
他呢把context的body改为high egg

104
00:03:30,320 --> 00:03:31,640
也就是说咱们访问页面的时候

105
00:03:31,640 --> 00:03:33,700
是不是会给我们访问一个自不穿呢

106
00:03:33,700 --> 00:03:35,760
那么这里呢controller.home

107
00:03:35,760 --> 00:03:37,200
home指的是谁啊

108
00:03:37,200 --> 00:03:38,020
home指的是谁啊

109
00:03:38,020 --> 00:03:38,360
同学们

110
00:03:38,360 --> 00:03:39,560
home

111
00:03:39,560 --> 00:03:41,000
home是不是指的他呀

112
00:03:41,000 --> 00:03:42,300
你controller里面

113
00:03:42,300 --> 00:03:43,100
你controller里面

114
00:03:43,100 --> 00:03:44,780
对于文件夹的名称是什么

115
00:03:44,780 --> 00:03:45,680
它的命名空间就是什么

116
00:03:45,680 --> 00:03:47,800
而不是去看你的module.export名称是什么

117
00:03:47,800 --> 00:03:49,100
这里呢同学们需要去注意一下

118
00:03:49,100 --> 00:03:51,640
然后index是不是在咱们home里呢

119
00:03:51,640 --> 00:03:52,420
Home里面的方法

120
00:03:52,420 --> 00:03:54,340
这里有个SyncIndex这样的一个方形

121
00:03:54,340 --> 00:03:55,480
这个Index呢

122
00:03:55,480 --> 00:03:56,780
其实指的就是

123
00:03:56,780 --> 00:03:57,660
它

124
00:03:57,660 --> 00:03:58,680
好

125
00:03:58,680 --> 00:04:00,860
那么我们就来访问咱们页面来看一下

126
00:04:00,860 --> 00:04:01,940
到底是怎么回事

127
00:04:01,940 --> 00:04:04,200
那么我们需要去执行什么命令呢

128
00:04:04,200 --> 00:04:05,040
其实很简单的

129
00:04:05,040 --> 00:04:06,380
我们来看一下package.json

130
00:04:06,380 --> 00:04:08,120
我们来看一下scribe的dv

131
00:04:08,120 --> 00:04:10,280
它会执行一个1GG并dv

132
00:04:10,280 --> 00:04:10,700
好

133
00:04:10,700 --> 00:04:11,760
我们来执行一下npm

134
00:04:11,760 --> 00:04:14,540
npm run dv

135
00:04:14,540 --> 00:04:16,960
好

136
00:04:16,960 --> 00:04:17,980
我们的服务已经启动了

137
00:04:17,980 --> 00:04:19,220
那么我们来访问浏览器来看一下

138
00:04:19,220 --> 00:04:22,920
那么在1GG里面其实它那个默认的端口是什么呢

139
00:04:22,920 --> 00:04:23,660
是7001

140
00:04:23,660 --> 00:04:26,800
好 大家可以看到我们是不是现在已经访问到了

141
00:04:26,800 --> 00:04:27,760
嗨 1GG

142
00:04:27,760 --> 00:04:31,300
那么这里呢我们就已经完成了咱们的第一个controller的编写

143
00:04:31,300 --> 00:04:35,680
好 那么我们就来总结一下我们刚才所讲解的内容

144
00:04:35,680 --> 00:04:39,500
首先我们刚才是不是讲解了咱们快速入门里面的内容

145
00:04:39,500 --> 00:04:41,300
首先安装 对吧 怎么安装

146
00:04:41,300 --> 00:04:43,660
是不是npm init 1GG

147
00:04:43,660 --> 00:04:45,660
-type等于什么 是不是sample

148
00:04:45,660 --> 00:04:47,900
它会自动给我们是不是自动创解模板

149
00:04:47,900 --> 00:04:50,700
然后呢

150
00:04:50,700 --> 00:04:52,100
我们刚才安装完成之后

151
00:04:52,100 --> 00:04:52,900
是不是编写了

152
00:04:52,900 --> 00:04:55,680
第一个什么呀

153
00:04:55,680 --> 00:04:57,300
是不是扛Q了呀

154
00:04:57,300 --> 00:04:58,120
那么扛Q了

155
00:04:58,120 --> 00:05:00,900
咱们在root里面去调用的时候

156
00:05:00,900 --> 00:05:02,300
是不是调用的命名空间呢

157
00:05:02,300 --> 00:05:04,700
也就是说root里面

158
00:05:04,700 --> 00:05:09,400
调用的是命名空间

159
00:05:09,400 --> 00:05:10,840
你比如说我们把代码张过来看一下

160
00:05:10,840 --> 00:05:12,780
我们是不是通过root点

161
00:05:12,780 --> 00:05:14,380
root点get

162
00:05:14,380 --> 00:05:15,000
对吧

163
00:05:15,000 --> 00:05:16,280
咱们通过root点get

164
00:05:16,280 --> 00:05:17,740
去访问到我们的控制器

165
00:05:17,740 --> 00:05:19,000
那么我们的控制器呢

166
00:05:19,000 --> 00:05:21,520
我们控制器怎么样去返回咱们的一个数据

167
00:05:21,520 --> 00:05:22,720
是不是通过context的玻璃

168
00:05:22,720 --> 00:05:24,340
那么这个context来自于哪里

169
00:05:24,340 --> 00:05:25,960
context是不是来源于

170
00:05:25,960 --> 00:05:28,920
是不是来自call里面的一个context

171
00:05:28,920 --> 00:05:29,620
对吧

172
00:05:29,620 --> 00:05:29,900
好

173
00:05:29,900 --> 00:05:32,120
这里呢就是我们这些课的内容

