1
00:00:00,000 --> 00:00:02,420
了解了UDP的基本概念之后

2
00:00:02,420 --> 00:00:04,160
然后在这一节我们来看一下

3
00:00:04,160 --> 00:00:06,220
TCP和UDP之间的一个区别

4
00:00:06,220 --> 00:00:09,460
它们两者都是用于数据传输的一种协议

5
00:00:09,460 --> 00:00:11,720
那么这两种协议最重要的一个区别

6
00:00:11,720 --> 00:00:12,760
其实非常简单

7
00:00:12,760 --> 00:00:15,100
我们大家可以看到在图示当中

8
00:00:15,100 --> 00:00:16,200
例如对于TCP来讲

9
00:00:16,200 --> 00:00:17,060
它们要传输数据

10
00:00:17,060 --> 00:00:19,660
那么首先你必须通过三次握手来建立连接

11
00:00:19,660 --> 00:00:21,760
然后才能进行这个数据的收发

12
00:00:21,760 --> 00:00:23,600
而UDP是不需要的

13
00:00:23,600 --> 00:00:24,280
所以我们可以看到

14
00:00:24,280 --> 00:00:26,240
它们可以直接向对方发送数据

15
00:00:26,240 --> 00:00:28,180
而不需要关心对方是否存在

16
00:00:28,180 --> 00:00:29,840
以及数据对方是否能收到

17
00:00:29,840 --> 00:00:30,800
对于UDP来讲

18
00:00:30,800 --> 00:00:31,560
什么都不关心

19
00:00:31,560 --> 00:00:32,560
它就是采用这种

20
00:00:32,560 --> 00:00:33,680
就是尽力而为的

21
00:00:33,680 --> 00:00:34,360
这种传输方式

22
00:00:34,360 --> 00:00:35,160
所以速度呢

23
00:00:35,160 --> 00:00:36,540
相比UDP来讲要非常快

24
00:00:36,540 --> 00:00:38,000
那么这里有一个

25
00:00:38,000 --> 00:00:38,940
非常形象的例子

26
00:00:38,940 --> 00:00:40,500
就是TCP和UDP

27
00:00:40,500 --> 00:00:42,100
都是数据传输方式的协议

28
00:00:42,100 --> 00:00:43,180
比如说我要给你钱

29
00:00:43,180 --> 00:00:45,340
那么对于这个TCP来讲

30
00:00:45,340 --> 00:00:45,920
就是什么呢

31
00:00:45,920 --> 00:00:47,320
就是手把手的方式给你

32
00:00:47,320 --> 00:00:48,260
那么对于UDP

33
00:00:48,260 --> 00:00:49,080
就是说

34
00:00:49,080 --> 00:00:50,720
我是手把手的方式给你的

35
00:00:50,720 --> 00:00:52,320
还是说以快递的方式来给你

36
00:00:52,320 --> 00:00:52,980
那么手把手

37
00:00:52,980 --> 00:00:54,160
就是这个TCP的方式

38
00:00:54,160 --> 00:00:55,780
快递的话就是这个UDP

39
00:00:55,780 --> 00:00:56,960
所以说这个主要体现在

40
00:00:56,960 --> 00:00:58,040
它这个连接方面

41
00:00:58,040 --> 00:00:58,960
就是连接方面

42
00:00:58,960 --> 00:01:00,620
那么接下来下面呢

43
00:01:00,620 --> 00:01:01,620
我们这里有一个图示

44
00:01:01,620 --> 00:01:03,020
在这个图表当中

45
00:01:03,020 --> 00:01:04,140
我们详细的列出了

46
00:01:04,140 --> 00:01:06,500
对于UDP和TCP之间的一个对比

47
00:01:06,500 --> 00:01:07,840
首先从连接角度来讲

48
00:01:07,840 --> 00:01:08,440
我们可以看到

49
00:01:08,440 --> 00:01:10,320
UDP首先是无连接的

50
00:01:10,320 --> 00:01:12,060
而TCP是面向连接的

51
00:01:12,060 --> 00:01:12,580
也就是说

52
00:01:12,580 --> 00:01:13,680
如果要建立数据通信

53
00:01:13,680 --> 00:01:15,740
那么TCP必须首先通过三次握手

54
00:01:15,740 --> 00:01:16,360
来建立连接

55
00:01:16,360 --> 00:01:17,400
而UDP不需要

56
00:01:17,400 --> 00:01:18,980
所以说正是由于这种

57
00:01:18,980 --> 00:01:19,740
无连接的特点

58
00:01:19,740 --> 00:01:20,560
所以我们可以看到

59
00:01:20,560 --> 00:01:21,500
从速度上来讲

60
00:01:21,500 --> 00:01:23,020
这个UDP它的传输速度

61
00:01:23,020 --> 00:01:23,660
要更快一些

62
00:01:23,660 --> 00:01:25,680
而我们TCP要稍慢一些

63
00:01:25,680 --> 00:01:28,160
那么UDP它所传播的方式

64
00:01:28,160 --> 00:01:28,920
它的目的主机

65
00:01:28,920 --> 00:01:30,280
它不仅支持一对一

66
00:01:30,280 --> 00:01:31,300
而且支持一对多

67
00:01:31,300 --> 00:01:33,320
我这里所说的一对多指的是

68
00:01:33,320 --> 00:01:35,620
例如A要给多个客户班发数据

69
00:01:35,620 --> 00:01:36,700
假如说同样的数据

70
00:01:36,700 --> 00:01:38,000
那么UDP角度

71
00:01:38,000 --> 00:01:39,500
你可以发一次就可以了

72
00:01:39,500 --> 00:01:40,360
而对于TCP来讲

73
00:01:40,360 --> 00:01:42,080
假如说你要去应对这种一对多的场景

74
00:01:42,080 --> 00:01:43,820
那么假如说有N个客户班

75
00:01:43,820 --> 00:01:45,520
那同样的数据你要发N次

76
00:01:45,520 --> 00:01:47,800
所以说这是他们从目的主机

77
00:01:47,800 --> 00:01:49,460
或者说发送方式的一个区别

78
00:01:49,460 --> 00:01:50,720
UDP支持一对多

79
00:01:50,720 --> 00:01:55,100
那么这里就设立到它的一个带宽

80
00:01:55,100 --> 00:01:56,780
从带宽角度

81
00:01:56,780 --> 00:01:57,640
UDP的暴徒

82
00:01:57,640 --> 00:02:00,760
也就是说通过UDP发送的数据比较小

83
00:02:00,760 --> 00:02:03,400
所以说它消耗的带宽要更少一些

84
00:02:03,400 --> 00:02:04,260
消耗的带宽

85
00:02:04,260 --> 00:02:07,760
那么TCP相比UDP而言

86
00:02:07,760 --> 00:02:09,020
要比它消耗更多的带宽

87
00:02:09,020 --> 00:02:11,500
因为TCP的报纹比较大

88
00:02:11,500 --> 00:02:14,420
那么其次都关于他们的消息边界

89
00:02:14,420 --> 00:02:16,140
UDP是具有消息边界的

90
00:02:16,140 --> 00:02:16,800
而TCP没有

91
00:02:16,800 --> 00:02:19,300
所以包括可靠性

92
00:02:19,300 --> 00:02:20,460
我们刚才看到了

93
00:02:20,460 --> 00:02:21,840
UDP是不可靠的

94
00:02:21,840 --> 00:02:24,680
它不能保证你数据是否被对方收到

95
00:02:24,680 --> 00:02:27,180
或者说数据的顺序也无法保证

96
00:02:27,180 --> 00:02:28,340
所以说你可以看到

97
00:02:56,340 --> 00:02:57,180
所以说你可以看到

98
00:02:57,180 --> 00:03:00,200
如果说你对这个速度要求比较高

99
00:03:00,200 --> 00:03:01,940
那么这个时候你可以藏UTP

100
00:03:01,940 --> 00:03:03,140
例如我们常见的这种

101
00:03:03,140 --> 00:03:04,440
实时的音视频聊天

102
00:03:04,440 --> 00:03:05,840
可以聊天的

103
00:03:05,840 --> 00:03:08,300
那么如果说你对这个数据安全要求比较高

104
00:03:08,300 --> 00:03:10,380
例如我们这个常见的这种数据传输

105
00:03:10,380 --> 00:03:11,200
文件下载

106
00:03:11,200 --> 00:03:12,780
包括我们的网页的HTTP

107
00:03:12,780 --> 00:03:15,000
他们的都是基于这个TCP的

108
00:03:15,000 --> 00:03:16,380
那么假如

109
00:03:16,380 --> 00:03:17,780
那在这两种产品下

110
00:03:17,780 --> 00:03:19,400
例如我们对于这个视频聊天

111
00:03:19,400 --> 00:03:21,200
如果你需要画质优先

112
00:03:21,200 --> 00:03:22,480
那么你就选用TCP

113
00:03:22,480 --> 00:03:23,700
如果是流畅度优先

114
00:03:23,700 --> 00:03:25,840
那么你可以选择UTP

115
00:03:25,840 --> 00:03:28,500
所以说这个就是关于我们TCP和UDP

116
00:03:28,500 --> 00:03:30,220
他们之间的一个简单的对比

