1
00:00:00,000 --> 00:00:04,360
好 我们来看一下闪链的一些命令 首先是复值与取值

2
00:00:04,360 --> 00:00:07,160
那么怎么样去复值呢

3
00:00:07,160 --> 00:00:08,440
SET吧

4
00:00:08,440 --> 00:00:10,500
那么闪链需要加一个H SET

5
00:00:10,500 --> 00:00:11,260
首先

6
00:00:11,260 --> 00:00:12,280
第1个

7
00:00:12,280 --> 00:00:13,560
是不是咱们的

8
00:00:13,560 --> 00:00:15,880
类型加上ID啊 比如说卡

9
00:00:15,880 --> 00:00:17,160
加上ID

10
00:00:17,160 --> 00:00:18,180
这是一种约定

11
00:00:18,180 --> 00:00:19,200
你叫卡也可以

12
00:00:19,200 --> 00:00:20,740
但是我们在实际项目中是不是

13
00:00:20,740 --> 00:00:23,040
我们的车它都会有一些ID啊 你不可能有一辆车吧

14
00:00:23,040 --> 00:00:24,580
咱们通过ID去区分它们

15
00:00:24,580 --> 00:00:25,340
比如说

16
00:00:25,340 --> 00:00:27,140
给它一个属性 price 价格

17
00:00:27,140 --> 00:00:27,900
500

18
00:00:29,180 --> 00:00:29,940
intgr0

19
00:00:29,940 --> 00:00:31,740
说明其实已经存在了

20
00:00:31,740 --> 00:00:32,500
这里

21
00:00:32,500 --> 00:00:34,820
我先把咱们的car给算掉

22
00:00:34,820 --> 00:00:35,840
car1

23
00:00:35,840 --> 00:00:37,120
咱们重新来set

24
00:00:37,120 --> 00:00:40,960
hsetcar1price

25
00:00:40,960 --> 00:00:42,740
500

26
00:00:42,740 --> 00:00:43,260
好

27
00:00:43,260 --> 00:00:44,800
这个时候其实已经设置成功了

28
00:00:44,800 --> 00:00:45,820
返回值是唯一

29
00:00:45,820 --> 00:00:48,640
咱们再来给属性hsetcar1

30
00:00:48,640 --> 00:00:50,420
0

31
00:00:50,420 --> 00:00:53,240
好 都设置成功了

32
00:00:53,240 --> 00:00:55,040
那么我们来怎么样去读取了

33
00:00:55,040 --> 00:00:56,060
很简单

34
00:00:56,060 --> 00:00:56,820
hget

35
00:00:59,140 --> 00:00:59,660
2

36
00:00:59,660 --> 00:01:02,220
1 name

37
00:01:02,220 --> 00:01:03,500
皇马

38
00:01:03,500 --> 00:01:04,260
好

39
00:01:04,260 --> 00:01:06,300
这里呢就是他的一个

40
00:01:06,300 --> 00:01:07,340
负质与取质

41
00:01:07,340 --> 00:01:08,620
接下来我们来看一下

42
00:01:08,620 --> 00:01:11,180
在hset命令

43
00:01:11,180 --> 00:01:16,540
它的方便之处在于不区分插入和更新操作这意味着修改数据时不用事先判断自断是否存在

44
00:01:16,540 --> 00:01:19,880
来决定要执行的是插入还是更新

45
00:01:19,880 --> 00:01:21,160
那么当执行插入时

46
00:01:21,160 --> 00:01:22,700
hset它会返回1

47
00:01:22,700 --> 00:01:23,980
当执行的是更新时

48
00:01:23,980 --> 00:01:24,740
它会返回0

49
00:01:24,740 --> 00:01:25,500
怎么意思

50
00:01:25,500 --> 00:01:26,280
比如说

51
00:01:26,280 --> 00:01:27,560
比如说我们刚才

52
00:01:27,820 --> 00:01:29,900
就是hsat的car1

53
00:01:29,900 --> 00:01:31,380
它的name是什么呀

54
00:01:31,380 --> 00:01:32,020
是宝马

55
00:01:32,020 --> 00:01:33,660
那么假如我们给它一个其他的名称

56
00:01:33,660 --> 00:01:34,280
奔驰

57
00:01:34,280 --> 00:01:36,540
这个时候我们是不是执行的是修改操作

58
00:01:36,540 --> 00:01:37,220
那么呢

59
00:01:37,220 --> 00:01:37,980
修改操作

60
00:01:37,980 --> 00:01:38,860
它的返回值是什么

61
00:01:38,860 --> 00:01:39,740
0

62
00:01:39,740 --> 00:01:41,660
这个时候你就能明确的知道

63
00:01:41,660 --> 00:01:43,980
好

64
00:01:43,980 --> 00:01:45,460
那接下来我们来看一下

65
00:01:45,460 --> 00:01:47,420
怎么样获取建中所有的资短合资

66
00:01:47,420 --> 00:01:49,480
其实很简单

67
00:01:49,480 --> 00:01:51,120
hget war

68
00:01:51,120 --> 00:01:53,240
比如说我们的car1

69
00:01:53,240 --> 00:01:55,980
price500 name奔驰

70
00:01:55,980 --> 00:01:58,340
那么实际上在我们的项目中

71
00:01:58,340 --> 00:02:00,180
如果说我们去HGateWall的时候

72
00:02:00,180 --> 00:02:01,520
因为闪电它的使用非常频繁

73
00:02:01,520 --> 00:02:03,500
如果这么输出肯定是不行的

74
00:02:03,500 --> 00:02:04,380
我们怎么去使用呢

75
00:02:04,380 --> 00:02:05,860
其实在NodeGNS里面

76
00:02:05,860 --> 00:02:07,620
比如说我们去引入一个Redis

77
00:02:07,620 --> 00:02:09,560
在NodeGNS里面它会有一个Redis的

78
00:02:09,560 --> 00:02:10,560
酷

79
00:02:10,560 --> 00:02:11,660
这个我们后面会讲到

80
00:02:11,660 --> 00:02:12,720
比如说你去HGateWall

81
00:02:12,720 --> 00:02:13,720
卡

82
00:02:13,720 --> 00:02:17,180
此时的卡会返回一个JavaScribe的对象

83
00:02:17,180 --> 00:02:17,800
也就是Object

84
00:02:17,800 --> 00:02:19,680
我们可以通过对象的形式去操作它

85
00:02:19,680 --> 00:02:22,440
而且它各个语言会有各个语言封装的方式

86
00:02:22,440 --> 00:02:24,040
这里是GNS它那种方式

87
00:02:24,040 --> 00:02:24,580
好

88
00:02:24,580 --> 00:02:26,280
再来看一下怎么样去判断

89
00:02:26,280 --> 00:02:27,440
自断是否存在

90
00:02:27,440 --> 00:02:29,740
通过hx6

91
00:02:29,740 --> 00:02:31,560
比如说我们去判断一下

92
00:02:31,560 --> 00:02:34,240
hx6

93
00:02:34,240 --> 00:02:36,080
car name

94
00:02:36,080 --> 00:02:37,740
0

95
00:02:37,740 --> 00:02:39,380
0说明什么

96
00:02:39,380 --> 00:02:40,880
0是不是说明他

97
00:02:40,880 --> 00:02:43,080
存在呀

98
00:02:43,080 --> 00:02:44,480
不存在啊

99
00:02:44,480 --> 00:02:45,120
0是不存在

100
00:02:45,120 --> 00:02:45,920
咱们来看一下

101
00:02:45,920 --> 00:02:47,040
x6

102
00:02:47,040 --> 00:02:50,420
car name

103
00:02:50,420 --> 00:02:51,000
1

104
00:02:51,000 --> 00:02:51,740
咱们返回1

105
00:02:51,740 --> 00:02:52,480
说明了存在

106
00:02:52,480 --> 00:02:53,640
这里也就是说

107
00:02:53,640 --> 00:02:56,640
和我们之前的exit是一样的

108
00:02:56,640 --> 00:02:58,640
0和1分别代表不存在和存在

109
00:02:58,640 --> 00:02:59,640
好 这里就不去过多介绍了

110
00:02:59,640 --> 00:03:00,640
来看一下

111
00:03:00,640 --> 00:03:04,640
在咱们的闪电里面怎么去增加数字

112
00:03:04,640 --> 00:03:05,640
比如说

113
00:03:05,640 --> 00:03:11,640
Hincr by price

114
00:03:11,640 --> 00:03:16,640
car1 price error

115
00:03:16,640 --> 00:03:19,640
run number of arguments for

116
00:03:19,640 --> 00:03:22,640
Hincr by car1 price

117
00:03:22,640 --> 00:03:26,420
哦,少了个参数

118
00:03:26,420 --> 00:03:27,720
那么by是不是后面需要加入

119
00:03:27,720 --> 00:03:28,800
你需要添加多少的

120
00:03:28,800 --> 00:03:30,000
数字上,比如说我们加5

121
00:03:30,000 --> 00:03:31,140
好,大家看到没有

122
00:03:31,140 --> 00:03:32,600
我们刚才的price是500吧

123
00:03:32,600 --> 00:03:34,640
现在给加了5,是505

124
00:03:34,640 --> 00:03:35,640
怎么加1呢

125
00:03:35,640 --> 00:03:37,740
是不是HINCR

126
00:03:37,740 --> 00:03:40,000
car1price

127
00:03:40,000 --> 00:03:41,400
就是加1啊

128
00:03:41,400 --> 00:03:43,400
HINCR

129
00:03:43,400 --> 00:03:46,460
这里来说明什么问题

130
00:03:46,460 --> 00:03:48,900
我们的INCR是不是可以给某一个属性加1

131
00:03:48,900 --> 00:03:49,860
但是这里

132
00:03:49,860 --> 00:03:51,000
但是这里

133
00:03:51,000 --> 00:03:54,420
但是这里呢说明一个问题

134
00:03:54,420 --> 00:03:56,460
咱们呢在闪电里面是不能够去使用

135
00:03:56,460 --> 00:03:58,140
啊啊啊啊啊这样去加一

136
00:03:58,140 --> 00:03:59,060
我们只能够去通过

137
00:03:59,060 --> 00:04:01,140
办法式去给他新增数字好

138
00:04:01,140 --> 00:04:02,320
再来看一下怎么样去双处

139
00:04:02,320 --> 00:04:03,740
怎么样去双处某一个属性

140
00:04:03,740 --> 00:04:04,960
比如说我们的啊

141
00:04:04,960 --> 00:04:05,800
去给我

142
00:04:05,800 --> 00:04:08,040
他有这样一些属性

143
00:04:08,040 --> 00:04:10,880
price和内我们怎么样去双处其中的内容呢

144
00:04:10,880 --> 00:04:12,900
可以挨取第一个

145
00:04:12,900 --> 00:04:14,320
卡一

146
00:04:14,320 --> 00:04:15,380
然后呢双随内

147
00:04:15,380 --> 00:04:17,100
好我们再来看一下

148
00:04:17,100 --> 00:04:18,800
这里是一顺便双处成功

149
00:04:18,800 --> 00:04:20,320
那么我们再来给他握一下

150
00:04:20,320 --> 00:04:21,660
get wall

151
00:04:21,660 --> 00:04:23,980
好 大家看到没有

152
00:04:23,980 --> 00:04:25,800
price 505 咱们的name已经拴掉了

153
00:04:25,800 --> 00:04:28,640
好 这里呢就是关于闪列的一些命令的使用

154
00:04:28,640 --> 00:04:31,800
这里呢 老师呢来 带同学们来总结一下

155
00:04:31,800 --> 00:04:39,740
好 刚才我们是不是讲到了闪列的一些

156
00:04:39,740 --> 00:04:42,820
闪列的一些命令呢 同学们

157
00:04:42,820 --> 00:04:44,020
闪列

158
00:04:44,020 --> 00:04:46,660
命令

159
00:04:46,660 --> 00:04:52,360
首先咱们刚才是不是讲到了

160
00:04:52,360 --> 00:04:55,220
第一个负值与取值对吧

161
00:04:55,220 --> 00:04:57,360
负值与取值

162
00:04:57,360 --> 00:05:01,520
是什么是不是hget和hset呀

163
00:05:01,520 --> 00:05:04,220
那么获取所有的值呢

164
00:05:04,220 --> 00:05:05,620
获取所有

165
00:05:05,620 --> 00:05:07,700
hget

166
00:05:07,700 --> 00:05:10,100
怎么去判断存不存在

167
00:05:10,100 --> 00:05:10,900
存在

168
00:05:10,900 --> 00:05:14,900
hxset

169
00:05:14,900 --> 00:05:17,200
第1次

170
00:05:17,200 --> 00:05:18,480
新增数字呢

171
00:05:18,480 --> 00:05:20,020
新增数字

172
00:05:20,020 --> 00:05:21,820
HINCRY

173
00:05:21,820 --> 00:05:23,340
不能使用什么

174
00:05:23,340 --> 00:05:25,140
是不是不能使用HINCR呀

175
00:05:25,140 --> 00:05:25,900
好

176
00:05:25,900 --> 00:05:27,960
接下来呢是不是还讲了一个双除

177
00:05:27,960 --> 00:05:30,260
HD

178
00:05:30,260 --> 00:05:32,060
其实说白了咱们的闪链

179
00:05:32,060 --> 00:05:34,100
它就是在我们的命名前面加那个H

180
00:05:34,100 --> 00:05:36,660
你看见H就知道它一定是闪链的一些操作

181
00:05:36,660 --> 00:05:39,340
好这里呢就是这节课的内容

