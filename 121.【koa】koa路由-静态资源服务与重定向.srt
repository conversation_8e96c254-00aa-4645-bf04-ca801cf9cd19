1
00:00:00,000 --> 00:00:01,500
好 这里我们就来看一下

2
00:00:01,500 --> 00:00:03,500
咱们的路由怎么样访问我们的静态资源

3
00:00:03,500 --> 00:00:06,000
你比如说你的网站提供一些图片字体等等

4
00:00:06,000 --> 00:00:08,380
那么呢 为他们一个个写路由就很麻烦

5
00:00:08,380 --> 00:00:12,400
Core Steadic模块封撞了这部分的星球

6
00:00:12,400 --> 00:00:13,500
好

7
00:00:13,500 --> 00:00:16,740
那么同学们还记得或者知道

8
00:00:16,740 --> 00:00:19,980
或者了解过咱们怎么样通过loadds去访问我们的静态资源吗

9
00:00:19,980 --> 00:00:21,240
比如说访问我们的图片

10
00:00:21,240 --> 00:00:22,340
怎么意思

11
00:00:22,340 --> 00:00:24,440
我们来看一下

12
00:00:24,440 --> 00:00:26,880
首先我们这里呢 项目中呢 有两个图片

13
00:00:26,880 --> 00:00:28,400
两个图片

14
00:00:28,400 --> 00:00:30,780
一个DN的8 一个是江苏瘾

15
00:00:30,780 --> 00:00:31,880
两个非常美的美女

16
00:00:31,880 --> 00:00:34,200
那么我们想通过路由去访问这张图片

17
00:00:34,200 --> 00:00:35,540
我们该怎么去访问

18
00:00:35,540 --> 00:00:37,900
比如说我们的Image在Chapter 1

19
00:00:37,900 --> 00:00:40,780
它的Image在一个目录下面

20
00:00:40,780 --> 00:00:44,040
那么这里呢我再去访问

21
00:00:44,040 --> 00:00:44,900
不知道能不能够访问到

22
00:00:44,900 --> 00:00:46,620
同学们可以思考一下

23
00:00:46,620 --> 00:00:51,720
我再去访问

24
00:00:51,720 --> 00:00:54,480
可以访问吗

25
00:00:54,480 --> 00:00:56,860
Image美女点GPG

26
00:00:56,860 --> 00:00:58,120
比如说

27
00:00:58,120 --> 00:00:59,600
我们的in-magic是不是在

28
00:00:59,600 --> 00:01:00,640
我们的图片是不是在

29
00:01:00,640 --> 00:01:01,520
我们的in-magic文件夹下面

30
00:01:01,520 --> 00:01:02,380
美女点GPG

31
00:01:02,380 --> 00:01:03,260
我们再去访问

32
00:01:03,260 --> 00:01:04,080
可不可以访问到啊

33
00:01:04,080 --> 00:01:05,440
不可以吧

34
00:01:05,440 --> 00:01:06,560
无法访问持绑的

35
00:01:06,560 --> 00:01:08,240
对吧

36
00:01:08,240 --> 00:01:10,240
那么我们应该怎么去访问图片呢

37
00:01:10,240 --> 00:01:13,760
是不是我们要为每一张图片去写一个路由啊

38
00:01:13,760 --> 00:01:14,600
比如说这里呢

39
00:01:14,600 --> 00:01:16,320
我建了一个redin-magic.js

40
00:01:16,320 --> 00:01:17,960
我们去访问一下

41
00:01:17,960 --> 00:01:18,220
看一下

42
00:01:18,220 --> 00:01:19,380
比如说这里呢

43
00:01:19,380 --> 00:01:22,340
我来复制一段我们刚才路由的代码

44
00:01:22,340 --> 00:01:22,960
我们来看一下

45
00:01:22,960 --> 00:01:25,720
我们怎样为一张图片去写一个路由

46
00:01:25,720 --> 00:01:27,360
好

47
00:01:27,360 --> 00:01:28,220
那我们不改

48
00:01:28,220 --> 00:01:30,060
我们在其他这样一个路径里面去访问美女

49
00:01:30,060 --> 00:01:32,440
我们把路径改为美女

50
00:01:32,440 --> 00:01:33,140
好

51
00:01:33,140 --> 00:01:34,440
这样一个方法也改为美女

52
00:01:34,440 --> 00:01:37,280
好

53
00:01:37,280 --> 00:01:40,420
假如说当我们访问美女在一个路径的时候

54
00:01:40,420 --> 00:01:42,040
我们希望去读到这张图片

55
00:01:42,040 --> 00:01:42,660
第一个了吧

56
00:01:42,660 --> 00:01:43,740
怎么去做

57
00:01:43,740 --> 00:01:44,340
同学们

58
00:01:44,340 --> 00:01:45,940
我们来分析一下思路

59
00:01:45,940 --> 00:01:47,200
我们现在要做什么

60
00:01:47,200 --> 00:01:47,720
目的是什么

61
00:01:47,720 --> 00:01:49,560
是不是读取图片

62
00:01:49,560 --> 00:01:51,640
我们思路是什么

63
00:01:51,640 --> 00:01:53,620
同学们

64
00:01:53,620 --> 00:01:54,840
思路首先第一个

65
00:01:54,840 --> 00:01:56,760
是不是通过

66
00:01:56,760 --> 00:01:58,400
fs 读取

67
00:01:58,400 --> 00:01:59,880
图片呢

68
00:01:59,880 --> 00:02:00,580
是不是和我们的访问

69
00:02:00,580 --> 00:02:01,280
iqml 类似

70
00:02:01,280 --> 00:02:03,000
我们去访问

71
00:02:03,000 --> 00:02:03,760
iqml 页面的时候

72
00:02:03,760 --> 00:02:04,460
是不是也通过fs

73
00:02:04,460 --> 00:02:05,560
先读取我们的iqml 片段

74
00:02:05,560 --> 00:02:06,340
然后呢

75
00:02:06,340 --> 00:02:07,520
是不是再通过

76
00:02:07,520 --> 00:02:08,960
raise bounce

77
00:02:08,960 --> 00:02:10,300
返回图片

78
00:02:10,300 --> 00:02:11,920
那么在返回的时候

79
00:02:11,920 --> 00:02:12,720
是不是大家要注意个问题

80
00:02:12,720 --> 00:02:13,340
是不是修改

81
00:02:13,340 --> 00:02:14,900
我们的一个type

82
00:02:14,900 --> 00:02:15,580
好

83
00:02:15,580 --> 00:02:16,580
那么此时我们来看一下

84
00:02:16,580 --> 00:02:17,280
怎么去做

85
00:02:17,280 --> 00:02:18,520
比如说这里呢

86
00:02:18,520 --> 00:02:20,100
我们先来去引入我们的fs模块

87
00:02:20,100 --> 00:02:21,920
等于 require

88
00:02:21,920 --> 00:02:23,320
fs

89
00:02:23,320 --> 00:02:26,420
然后呢

90
00:02:26,420 --> 00:02:27,220
是不是还需要pass啊

91
00:02:27,220 --> 00:02:29,980
为什么需要pass

92
00:02:29,980 --> 00:02:31,700
是不是刚才我们提到的咱们路径的一个问题啊

93
00:02:31,700 --> 00:02:33,700
好

94
00:02:33,700 --> 00:02:37,980
这里呢我们先把type改为什么呢

95
00:02:37,980 --> 00:02:39,900
我们的图片是什么类型是不是jp1g

96
00:02:39,900 --> 00:02:42,620
我们的type了改为jp1g好

97
00:02:42,620 --> 00:02:47,580
那么我们修改type之后是不是还要去contact的点什么

98
00:02:47,580 --> 00:02:49,700
是不是response.body要改啊

99
00:02:49,700 --> 00:02:50,460
等于什么呢

100
00:02:50,460 --> 00:02:51,900
ifis.red

101
00:02:51,900 --> 00:02:54,020
发是不是sink啊

102
00:02:54,020 --> 00:02:55,380
我们这里要同步的去读取图片

103
00:02:56,060 --> 00:02:59,060
比如说去读取咱们pass.result

104
00:02:59,060 --> 00:03:01,060
然后呢pass.join

105
00:03:01,060 --> 00:03:05,060
第二类是不是访问咱们的Image下面的什么

106
00:03:05,060 --> 00:03:07,060
是不是美女点GPG啊

107
00:03:07,060 --> 00:03:09,060
美女点GPG

108
00:03:09,060 --> 00:03:12,060
好 这里呢我们就来看一下到底能不能访问到

109
00:03:12,060 --> 00:03:15,060
load chapter1

110
00:03:15,060 --> 00:03:17,060
read Imagine 这是周理

111
00:03:17,060 --> 00:03:18,060
刷新

112
00:03:18,060 --> 00:03:20,060
好 首先我们的根本录是Hello World

113
00:03:20,060 --> 00:03:24,060
我们来访问美女在一个路径

114
00:03:24,060 --> 00:03:26,060
大家可以看到我们是不是把图片已经成功了给读取到了

115
00:03:26,060 --> 00:03:28,220
那我们怎么去获取

116
00:03:28,220 --> 00:03:29,140
比如说我们要获取梅女二

117
00:03:29,140 --> 00:03:29,500
江苏颖

118
00:03:29,500 --> 00:03:30,340
怎么要去得到它

119
00:03:30,340 --> 00:03:31,640
我们直接这样可不可以

120
00:03:31,640 --> 00:03:33,160
这样可不可以

121
00:03:33,160 --> 00:03:34,280
弄得放的

122
00:03:34,280 --> 00:03:34,760
不可以吧

123
00:03:34,760 --> 00:03:34,960
为什么

124
00:03:34,960 --> 00:03:36,580
为什么

125
00:03:36,580 --> 00:03:38,020
是不是因为我们的路由

126
00:03:38,020 --> 00:03:38,560
没有注册了

127
00:03:38,560 --> 00:03:39,320
梅女二这样一个路径了

128
00:03:39,320 --> 00:03:40,300
如果说我们要去做的话

129
00:03:40,300 --> 00:03:41,800
比如说我们要去访问江苏颖

130
00:03:41,800 --> 00:03:42,980
我们是不是还要去写一个

131
00:03:42,980 --> 00:03:43,580
梅女二了

132
00:03:43,580 --> 00:03:44,900
然后写一个梅女二的逻辑

133
00:03:44,900 --> 00:03:46,200
把咱们的江苏颖这张图片

134
00:03:46,200 --> 00:03:46,780
给读取到

135
00:03:46,780 --> 00:03:47,320
然后给访回

136
00:03:47,320 --> 00:03:47,900
Ribonks访回

137
00:03:47,900 --> 00:03:49,020
那么这样是不是很麻烦

138
00:03:49,020 --> 00:03:49,700
对吗

139
00:03:49,700 --> 00:03:50,780
所以呢

140
00:03:50,780 --> 00:03:52,040
在Qua里面

141
00:03:52,040 --> 00:03:55,600
他提供了一个比较好的一个库来帮我们去解决这个问题

142
00:03:55,600 --> 00:03:58,100
那么也是cora static这个模块

143
00:03:58,100 --> 00:04:02,400
这里就是cora static它的一个官方的文档

144
00:04:02,400 --> 00:04:04,440
我们来看一下它的API其实很简单

145
00:04:04,440 --> 00:04:06,680
首先你引入cora static之后

146
00:04:06,680 --> 00:04:08,140
它是否返回函数

147
00:04:08,140 --> 00:04:10,480
它介绍两个参数一个root一个options

148
00:04:10,480 --> 00:04:13,040
那么root是不是就是我们的一个

149
00:04:13,040 --> 00:04:15,500
也就是说你需要去访问咱们新台支援的根目录啊

150
00:04:15,500 --> 00:04:18,600
它也介绍了root directory stream

151
00:04:18,600 --> 00:04:19,380
它是一个字不串

152
00:04:19,380 --> 00:04:20,700
表示什么是否表示我们的根文件夹

153
00:04:20,700 --> 00:04:23,000
也就是说你需要在哪一个稳定夹下面去访问一些进来资源

154
00:04:23,000 --> 00:04:25,060
那么Options呢也就是他的一些配置

155
00:04:25,060 --> 00:04:28,380
你比如说你可以去配置一些maxage他的一些过期时间

156
00:04:28,380 --> 00:04:30,420
因为我们的进来资源是不是可以去缓存了

157
00:04:30,420 --> 00:04:32,480
包括你要不要去做一些技术的压述

158
00:04:32,480 --> 00:04:33,240
好

159
00:04:33,240 --> 00:04:34,780
这里是他的一些配置

160
00:04:34,780 --> 00:04:36,820
然后他包括了还有一个example

161
00:04:36,820 --> 00:04:38,100
怎么样去使用

162
00:04:38,100 --> 00:04:39,900
是不是和我们去写路由是一样的

163
00:04:39,900 --> 00:04:40,920
你app去use

164
00:04:40,920 --> 00:04:42,720
通过core的实力去use一下他

165
00:04:42,720 --> 00:04:43,480
然后去调用

166
00:04:43,480 --> 00:04:45,540
save是什么

167
00:04:45,540 --> 00:04:46,820
是不是咱们static反汇的一个

168
00:04:46,820 --> 00:04:47,580
方法呀

169
00:04:47,580 --> 00:04:49,120
咱们去use

170
00:04:49,120 --> 00:04:49,880
去调用他

171
00:04:50,140 --> 00:04:53,860
这个参数是不是接受我们 你需要把哪一个目录作为你的一个进台资源房目录

172
00:04:53,860 --> 00:04:56,160
 你就传入一个什么 那么我们就来看一下怎么去做

173
00:04:56,160 --> 00:05:03,660
比如说我们这里有一个

174
00:05:03,660 --> 00:05:05,560
static.js

175
00:05:05,560 --> 00:05:10,500
我们把刚才的read image 把它给copy过来

176
00:05:10,500 --> 00:05:13,940
static.js 好 咱们刚才use的这样一些代码呢 先把它给

177
00:05:13,940 --> 00:05:17,060
先把它给删掉 先删掉

178
00:05:17,060 --> 00:05:19,340
fis也删掉 好像不需要

179
00:05:20,140 --> 00:05:24,840
这里呢我们是不是需要去引入一个Static 这样一个方法

180
00:05:24,840 --> 00:05:29,640
有这样一个酷 require core-static

181
00:05:29,640 --> 00:05:33,140
我们使用方法是不是app.use

182
00:05:33,140 --> 00:05:35,140
其实非常简单 又是什么呢

183
00:05:35,140 --> 00:05:36,140
Static 它是不是一个方法

184
00:05:36,140 --> 00:05:37,140
你只需要传入什么

185
00:05:37,140 --> 00:05:38,140
传入第一个参数

186
00:05:38,140 --> 00:05:40,140
也就是咱们要把哪一个目录作为咱们的

187
00:05:40,140 --> 00:05:41,140
是不是咱们进来这些目录

188
00:05:41,140 --> 00:05:44,140
也比如说我们这里呢就是pass.join

189
00:05:44,140 --> 00:05:45,140
什么呢

190
00:05:45,140 --> 00:05:46,140
dir name

191
00:05:46,140 --> 00:05:48,140
传入咱们的目录 image

192
00:05:49,140 --> 00:05:51,260
很简单一行代码就已经完成了我们这个

193
00:05:51,260 --> 00:05:53,300
进来转换目录的一个服务的一个搭建

194
00:05:53,300 --> 00:05:55,500
好 那我们来启动看一下到底是什么回事

195
00:05:55,500 --> 00:05:58,060
好 load

196
00:05:58,060 --> 00:06:00,660
好 Static.js好 我们已经运行了

197
00:06:00,660 --> 00:06:04,540
访问了放的对吧 好 那么这里呢

198
00:06:04,540 --> 00:06:06,580
我们访问更目录肯定是没有的 为什么呀

199
00:06:06,580 --> 00:06:10,540
因为我们起了服务之后 可以直接访问到咱们的inmagic这样一个文件夹

200
00:06:10,540 --> 00:06:13,460
里面有两个图片 一个是美女.jpg 一个是美女2.jpg

201
00:06:13,460 --> 00:06:15,020
我们更目录是不是什么都没有啊

202
00:06:15,140 --> 00:06:21,480
所以呢我们需要访问图片的时候是不是需要去输入美女点GP1G啊对吧这样我们就可以访问到图片那么

203
00:06:21,480 --> 00:06:25,400
第二个呢输入个2就可以了这样是不是很方便

204
00:06:25,400 --> 00:06:30,980
我们只需要一行代码就可以完成我们的静态支援福气而不需要一行一行的每一个路由来去写

205
00:06:30,980 --> 00:06:36,320
好接下来我们来看一下怎么样去重定项

206
00:06:36,320 --> 00:06:39,000
你比如说有一些场合服务器需要去重定项

207
00:06:39,000 --> 00:06:42,240
比如说哪一场合呢我们用户登录之后如果说

208
00:06:42,240 --> 00:06:48,250
咱们 note js 发现你没有登录 是不是需要把你重定向到别的页面去啊 其实这样的需求是非常多的

209
00:06:48,250 --> 00:06:49,160
 那么怎么样去重定向呢

210
00:06:49,160 --> 00:06:54,560
重定向其实非常简单 只需要呢 在康在咱们的contest的response上面加一个redirect

211
00:06:54,560 --> 00:06:55,800
 也就是一个重定向这样一个方法

212
00:06:55,800 --> 00:06:59,400
然后呢 把它指引到你所需要跳转的目录就可以了 那我们来看一下

213
00:06:59,400 --> 00:07:00,920
怎么样去使用

214
00:07:00,920 --> 00:07:11,420
这里呢 我们来新建一个文件叫做 ready react.js

215
00:07:12,240 --> 00:07:16,720
然后呢 我们来copy一下咱们刚才所写的root 好 这里先给

216
00:07:16,720 --> 00:07:23,060
酸掉 好 那假如说 我们访问其他的时候 假如我们访问其他的时候 我们需要把它给重订向到什么呢

217
00:07:23,060 --> 00:07:25,380
 要重订向咱们的 到咱们的Hello World去 怎么办

218
00:07:25,380 --> 00:07:38,900
是不是我们直接context.response.ready.ret到我们的根目录 就可以来 让我们来试一下

219
00:07:38,900 --> 00:07:40,680
 比如说我们去执行load

220
00:07:42,240 --> 00:07:43,740
radirect.js

221
00:07:43,740 --> 00:07:47,020
好

222
00:07:47,020 --> 00:07:48,120
根本是不是好了word

223
00:07:48,120 --> 00:07:49,280
那么我访问其他来看一下

224
00:07:49,280 --> 00:07:50,260
其他

225
00:07:50,260 --> 00:07:52,340
好

226
00:07:52,340 --> 00:07:52,760
大家可以看到

227
00:07:52,760 --> 00:07:54,620
我们是不是还是跳到了好了word

228
00:07:54,620 --> 00:07:56,200
那么其实这里呢

229
00:07:56,200 --> 00:07:58,320
我们可以通过一种方法

230
00:07:58,320 --> 00:07:59,780
来看一下

231
00:07:59,780 --> 00:08:02,340
因为你去访问其他的一瞬间

232
00:08:02,340 --> 00:08:03,380
访问其他一瞬间

233
00:08:03,380 --> 00:08:05,160
我们是不是就从立项的到我们好了word

234
00:08:05,160 --> 00:08:06,960
其实我们可以通过network去看一下

235
00:08:06,960 --> 00:08:07,680
到底发生了什么

236
00:08:07,680 --> 00:08:09,020
比如说我们去访问

237
00:08:09,020 --> 00:08:13,720
其他大家可以看到这样一个请求

238
00:08:13,720 --> 00:08:16,180
http localhost 3000其他

239
00:08:16,180 --> 00:08:17,500
它是一个get请求对吧

240
00:08:17,500 --> 00:08:18,860
然后呢

241
00:08:18,860 --> 00:08:19,840
status是什么呀

242
00:08:19,840 --> 00:08:21,720
当咱们去redirect的时候

243
00:08:21,720 --> 00:08:24,240
它返回了status会默认的变为三年

244
00:08:24,240 --> 00:08:25,840
是不是和我们刚才的修改body

245
00:08:25,840 --> 00:08:27,240
自动改为200元你是一样的

246
00:08:27,240 --> 00:08:27,860
对吧

247
00:08:27,860 --> 00:08:28,360
这里呢

248
00:08:28,360 --> 00:08:30,140
我们只要去见了redirect这样一个方法

249
00:08:30,140 --> 00:08:31,180
我们的status core

250
00:08:31,180 --> 00:08:32,360
它就会自动的修改为三年

251
00:08:32,360 --> 00:08:32,840
好

252
00:08:32,840 --> 00:08:34,440
那么我们重新像之后

253
00:08:34,440 --> 00:08:35,340
重新像到哪里去了

254
00:08:35,340 --> 00:08:37,980
是不是重新像到我们呢

255
00:08:37,980 --> 00:08:38,740
根不录啊

256
00:08:38,740 --> 00:08:40,540
所以说我们如果说页面发生的重点像

257
00:08:40,540 --> 00:08:42,580
你就可以通过咱们浏览器的调试工具去

258
00:08:42,580 --> 00:08:44,640
看它的一个状态码你就知道

259
00:08:44,640 --> 00:08:46,180
它是发生的一个重点像

260
00:08:46,180 --> 00:08:47,200
好那么呢

261
00:08:47,200 --> 00:08:49,240
我们来回顾一下咱们刚才所讲解的一个

262
00:08:49,240 --> 00:08:50,520
内容

263
00:08:50,520 --> 00:08:54,100
刚才呢我们是不是讲解了我们路由中的

264
00:08:54,100 --> 00:08:55,640
静态资源

265
00:08:55,640 --> 00:08:56,920
服务对吧

266
00:08:56,920 --> 00:08:57,940
那么我们传统的方式

267
00:08:57,940 --> 00:09:00,260
是不是需要一个图片

268
00:09:00,260 --> 00:09:01,280
一个

269
00:09:01,280 --> 00:09:02,040
路由

270
00:09:02,040 --> 00:09:03,580
但是呢我们使用了

271
00:09:03,580 --> 00:09:06,400
使用了什么是不是使用了咱们的cova static

272
00:09:06,400 --> 00:09:07,940
之后呢就可以

273
00:09:07,980 --> 00:09:09,640
一行命令搞定

274
00:09:09,640 --> 00:09:14,320
静态资源服务的搭建了

275
00:09:14,320 --> 00:09:18,240
然后呢是不是咱们还是不是讲解了我们的一个页面的重立项

276
00:09:18,240 --> 00:09:20,440
那么重立项它的status是什么

277
00:09:20,440 --> 00:09:22,020
是不是302啊

278
00:09:22,020 --> 00:09:24,520
而且呢它是自动的去修改我们的status code吧

279
00:09:24,520 --> 00:09:26,780
那么重立项它的一个应用场景是什么

280
00:09:26,780 --> 00:09:29,940
朋友们我们学习技术一定要知道为什么要有重立项

281
00:09:29,940 --> 00:09:30,740
是不是登录啊

282
00:09:30,740 --> 00:09:32,220
你如果说去访问某个页面

283
00:09:32,220 --> 00:09:33,140
他发现你没有登录

284
00:09:33,140 --> 00:09:34,820
然后呢把你给重立项到登录页面去

285
00:09:34,820 --> 00:09:35,040
对吧

286
00:09:35,040 --> 00:09:36,140
这样的需求很常见

287
00:09:36,140 --> 00:09:36,480
好

288
00:09:36,480 --> 00:09:37,900
这里呢就是我们这几个的内容

